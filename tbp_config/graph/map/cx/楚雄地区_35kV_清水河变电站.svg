<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-28" aopId="786686" id="thSvg" viewBox="3091 -1309 2202 1490">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape95">
    <ellipse cx="20" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="34" x2="29" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="34" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="31" x2="29" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="21" x2="21" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="9" x2="0" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="6" x2="3" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="7" x2="2" y1="4" y2="4"/>
    <polyline fill="none" points="21,19 5,19 5,6 " stroke-width="1"/>
    <ellipse cx="31" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="20" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="31" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape87">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="29" x2="32" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="54" y1="26" y2="16"/>
    <circle cx="54" cy="33" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="44" cy="32" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="47" cy="42" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="27" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="31" x2="31" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="37" x2="25" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="53" y1="17" y2="17"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="84" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="47" y1="84" y2="84"/>
    <rect height="27" stroke-width="0.416667" width="14" x="41" y="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="42" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="84" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="84" y2="50"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape156">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.918888" x1="43" x2="9" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="46" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="42" x2="42" y1="59" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="44" x2="44" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="33" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="33" x2="9" y1="68" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="33" x2="33" y1="68" y2="59"/>
    <polyline arcFlag="1" fill="none" points="33,48 34,48 34,48 35,48 36,48 36,49 37,49 38,50 38,50 38,51 39,52 39,52 39,53 39,54 39,55 39,55 38,56 38,57 38,57 37,58 36,58 36,59 35,59 34,59 34,59 33,59 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" fill="none" points="33,37 34,37 34,37 35,37 36,37 36,38 37,38 38,39 38,39 38,40 39,41 39,41 39,42 39,43 39,44 39,44 38,45 38,46 38,46 37,47 36,47 36,48 35,48 34,48 34,48 33,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" fill="none" points="33,26 34,26 34,26 35,26 36,26 36,27 37,27 38,28 38,28 38,29 39,30 39,30 39,31 39,32 39,33 39,33 38,34 38,35 38,35 37,36 36,36 36,37 35,37 34,37 34,37 33,37 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.874486" x1="33" x2="33" y1="26" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.220656" x1="9" x2="9" y1="76" y2="56"/>
    <polyline arcFlag="1" fill="none" points="52,48 51,48 51,48 50,48 49,48 49,49 48,49 47,50 47,50 47,51 46,52 46,52 46,53 46,54 46,55 46,55 47,56 47,57 47,57 48,58 49,58 49,59 50,59 51,59 51,59 52,59 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" fill="none" points="52,37 51,37 51,37 50,37 49,37 49,38 48,38 47,39 47,39 47,40 46,41 46,41 46,42 46,43 46,44 46,44 47,45 47,46 47,46 48,47 49,47 49,48 50,48 51,48 51,48 52,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" fill="none" points="52,26 51,26 51,26 50,26 49,26 49,27 48,27 47,28 47,28 47,29 46,30 46,30 46,31 46,32 46,33 46,33 47,34 47,35 47,35 48,36 49,36 49,37 50,37 51,37 51,37 52,37 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a01d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a0ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a1540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a21e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a3440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a4060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a47a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a5220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a5af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a63e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a6ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a79e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a8360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a8d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a9620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29aae10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29abb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_259bdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29ad110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ae2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29aec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29af760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29b4bd0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b5920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29b1560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29b2a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29b3cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29b7650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1500" width="2212" x="3086" y="-1314"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3093" y="-1192"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3092" y="-1072"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-39632">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6385" ObjectName="SW-CX_QSH.CX_QSH_3011SW"/>
     <cge:Meas_Ref ObjectId="39632"/>
    <cge:TPSR_Ref TObjectID="6385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.000000 -761.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10897" ObjectName="SW-CX_QSH.CX_QSH_30117SW"/>
     <cge:Meas_Ref ObjectId="58284"/>
    <cge:TPSR_Ref TObjectID="10897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -569.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10900" ObjectName="SW-CX_QSH.CX_QSH_0016SW"/>
     <cge:Meas_Ref ObjectId="58286"/>
    <cge:TPSR_Ref TObjectID="10900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -463.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10898" ObjectName="SW-CX_QSH.CX_QSH_0011SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -508.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10899" ObjectName="SW-CX_QSH.CX_QSH_00117SW"/>
     <cge:Meas_Ref ObjectId="58285"/>
    <cge:TPSR_Ref TObjectID="10899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6387" ObjectName="SW-CX_QSH.CX_QSH_3021SW"/>
     <cge:Meas_Ref ObjectId="39634"/>
    <cge:TPSR_Ref TObjectID="6387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58300">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4545.000000 -761.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10902" ObjectName="SW-CX_QSH.CX_QSH_30217SW"/>
     <cge:Meas_Ref ObjectId="58300"/>
    <cge:TPSR_Ref TObjectID="10902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -569.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10905" ObjectName="SW-CX_QSH.CX_QSH_0026SW"/>
     <cge:Meas_Ref ObjectId="58303"/>
    <cge:TPSR_Ref TObjectID="10905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -463.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10903" ObjectName="SW-CX_QSH.CX_QSH_0021SW"/>
     <cge:Meas_Ref ObjectId="58301"/>
    <cge:TPSR_Ref TObjectID="10903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -508.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10904" ObjectName="SW-CX_QSH.CX_QSH_00217SW"/>
     <cge:Meas_Ref ObjectId="58302"/>
    <cge:TPSR_Ref TObjectID="10904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39664">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 -881.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6402" ObjectName="SW-CX_QSH.CX_QSH_39020SW"/>
     <cge:Meas_Ref ObjectId="39664"/>
    <cge:TPSR_Ref TObjectID="6402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39663">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.000000 -899.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6401" ObjectName="SW-CX_QSH.CX_QSH_3902SW"/>
     <cge:Meas_Ref ObjectId="39663"/>
    <cge:TPSR_Ref TObjectID="6401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -954.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11215" ObjectName="SW-CX_QSH.CX_QSH_39017SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6397" ObjectName="SW-CX_QSH.CX_QSH_3121SW"/>
     <cge:Meas_Ref ObjectId="39658"/>
    <cge:TPSR_Ref TObjectID="6397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6398" ObjectName="SW-CX_QSH.CX_QSH_3122SW"/>
     <cge:Meas_Ref ObjectId="39659"/>
    <cge:TPSR_Ref TObjectID="6398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 -925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10909" ObjectName="SW-CX_QSH.CX_QSH_31227SW"/>
     <cge:Meas_Ref ObjectId="58314"/>
    <cge:TPSR_Ref TObjectID="10909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10908" ObjectName="SW-CX_QSH.CX_QSH_31217SW"/>
     <cge:Meas_Ref ObjectId="58313"/>
    <cge:TPSR_Ref TObjectID="10908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39647">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6393" ObjectName="SW-CX_QSH.CX_QSH_3621SW"/>
     <cge:Meas_Ref ObjectId="39647"/>
    <cge:TPSR_Ref TObjectID="6393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3597.000000 -914.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11207" ObjectName="SW-CX_QSH.CX_QSH_36217SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3598.000000 -969.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11203" ObjectName="SW-CX_QSH.CX_QSH_36260SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39649">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3597.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6395" ObjectName="SW-CX_QSH.CX_QSH_36267SW"/>
     <cge:Meas_Ref ObjectId="39649"/>
    <cge:TPSR_Ref TObjectID="6395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39648">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -985.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6394" ObjectName="SW-CX_QSH.CX_QSH_3626SW"/>
     <cge:Meas_Ref ObjectId="39648"/>
    <cge:TPSR_Ref TObjectID="6394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -1065.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11201" ObjectName="SW-CX_QSH.CX_QSH_3620SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39636">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6389" ObjectName="SW-CX_QSH.CX_QSH_3611SW"/>
     <cge:Meas_Ref ObjectId="39636"/>
    <cge:TPSR_Ref TObjectID="6389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10906" ObjectName="SW-CX_QSH.CX_QSH_36117SW"/>
     <cge:Meas_Ref ObjectId="58310"/>
    <cge:TPSR_Ref TObjectID="10906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58311">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10907" ObjectName="SW-CX_QSH.CX_QSH_36160SW"/>
     <cge:Meas_Ref ObjectId="58311"/>
    <cge:TPSR_Ref TObjectID="10907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39638">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -1040.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6391" ObjectName="SW-CX_QSH.CX_QSH_36167SW"/>
     <cge:Meas_Ref ObjectId="39638"/>
    <cge:TPSR_Ref TObjectID="6391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39637">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.000000 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6390" ObjectName="SW-CX_QSH.CX_QSH_3616SW"/>
     <cge:Meas_Ref ObjectId="39637"/>
    <cge:TPSR_Ref TObjectID="6390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.000000 -1067.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11198" ObjectName="SW-CX_QSH.CX_QSH_3610SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -887.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6400" ObjectName="SW-CX_QSH.CX_QSH_39010SW"/>
     <cge:Meas_Ref ObjectId="39662"/>
    <cge:TPSR_Ref TObjectID="6400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -905.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6399" ObjectName="SW-CX_QSH.CX_QSH_3901SW"/>
     <cge:Meas_Ref ObjectId="39661"/>
    <cge:TPSR_Ref TObjectID="6399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -960.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11215" ObjectName="SW-CX_QSH.CX_QSH_39017SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6445" ObjectName="SW-CX_QSH.CX_QSH_0671SW"/>
     <cge:Meas_Ref ObjectId="39773"/>
    <cge:TPSR_Ref TObjectID="6445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -384.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6446" ObjectName="SW-CX_QSH.CX_QSH_06717SW"/>
     <cge:Meas_Ref ObjectId="39774"/>
    <cge:TPSR_Ref TObjectID="6446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -279.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6447" ObjectName="SW-CX_QSH.CX_QSH_0672SW"/>
     <cge:Meas_Ref ObjectId="39775"/>
    <cge:TPSR_Ref TObjectID="6447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6448" ObjectName="SW-CX_QSH.CX_QSH_0676SW"/>
     <cge:Meas_Ref ObjectId="39776"/>
    <cge:TPSR_Ref TObjectID="6448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -155.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6449" ObjectName="SW-CX_QSH.CX_QSH_06767SW"/>
     <cge:Meas_Ref ObjectId="39777"/>
    <cge:TPSR_Ref TObjectID="6449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -477.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6404" ObjectName="SW-CX_QSH.CX_QSH_0901SW"/>
     <cge:Meas_Ref ObjectId="39669"/>
    <cge:TPSR_Ref TObjectID="6404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3614.000000 -529.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6405" ObjectName="SW-CX_QSH.CX_QSH_09017SW"/>
     <cge:Meas_Ref ObjectId="39670"/>
    <cge:TPSR_Ref TObjectID="6405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39757">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6439" ObjectName="SW-CX_QSH.CX_QSH_0651SW"/>
     <cge:Meas_Ref ObjectId="39757"/>
    <cge:TPSR_Ref TObjectID="6439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -384.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6440" ObjectName="SW-CX_QSH.CX_QSH_06517SW"/>
     <cge:Meas_Ref ObjectId="39758"/>
    <cge:TPSR_Ref TObjectID="6440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -279.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6441" ObjectName="SW-CX_QSH.CX_QSH_0652SW"/>
     <cge:Meas_Ref ObjectId="39759"/>
    <cge:TPSR_Ref TObjectID="6441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6442" ObjectName="SW-CX_QSH.CX_QSH_0656SW"/>
     <cge:Meas_Ref ObjectId="39760"/>
    <cge:TPSR_Ref TObjectID="6442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -155.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6443" ObjectName="SW-CX_QSH.CX_QSH_06567SW"/>
     <cge:Meas_Ref ObjectId="39761"/>
    <cge:TPSR_Ref TObjectID="6443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6433" ObjectName="SW-CX_QSH.CX_QSH_0631SW"/>
     <cge:Meas_Ref ObjectId="39741"/>
    <cge:TPSR_Ref TObjectID="6433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39742">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -384.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6434" ObjectName="SW-CX_QSH.CX_QSH_06317SW"/>
     <cge:Meas_Ref ObjectId="39742"/>
    <cge:TPSR_Ref TObjectID="6434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39743">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 -279.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6435" ObjectName="SW-CX_QSH.CX_QSH_0632SW"/>
     <cge:Meas_Ref ObjectId="39743"/>
    <cge:TPSR_Ref TObjectID="6435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6436" ObjectName="SW-CX_QSH.CX_QSH_0636SW"/>
     <cge:Meas_Ref ObjectId="39744"/>
    <cge:TPSR_Ref TObjectID="6436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39745">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -155.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6437" ObjectName="SW-CX_QSH.CX_QSH_06367SW"/>
     <cge:Meas_Ref ObjectId="39745"/>
    <cge:TPSR_Ref TObjectID="6437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6456" ObjectName="SW-CX_QSH.CX_QSH_0611SW"/>
     <cge:Meas_Ref ObjectId="39805"/>
    <cge:TPSR_Ref TObjectID="6456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -384.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6457" ObjectName="SW-CX_QSH.CX_QSH_06117SW"/>
     <cge:Meas_Ref ObjectId="39806"/>
    <cge:TPSR_Ref TObjectID="6457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6458" ObjectName="SW-CX_QSH.CX_QSH_0612SW"/>
     <cge:Meas_Ref ObjectId="39807"/>
    <cge:TPSR_Ref TObjectID="6458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39809">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -150.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6460" ObjectName="SW-CX_QSH.CX_QSH_0616SW"/>
     <cge:Meas_Ref ObjectId="39809"/>
    <cge:TPSR_Ref TObjectID="6460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.000000 -204.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6459" ObjectName="SW-CX_QSH.CX_QSH_06160SW"/>
     <cge:Meas_Ref ObjectId="39808"/>
    <cge:TPSR_Ref TObjectID="6459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39810">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3588.000000 -127.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6461" ObjectName="SW-CX_QSH.CX_QSH_06167SW"/>
     <cge:Meas_Ref ObjectId="39810"/>
    <cge:TPSR_Ref TObjectID="6461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6409" ObjectName="SW-CX_QSH.CX_QSH_0661SW"/>
     <cge:Meas_Ref ObjectId="39677"/>
    <cge:TPSR_Ref TObjectID="6409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6410" ObjectName="SW-CX_QSH.CX_QSH_06617SW"/>
     <cge:Meas_Ref ObjectId="39678"/>
    <cge:TPSR_Ref TObjectID="6410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6411" ObjectName="SW-CX_QSH.CX_QSH_0662SW"/>
     <cge:Meas_Ref ObjectId="39679"/>
    <cge:TPSR_Ref TObjectID="6411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6412" ObjectName="SW-CX_QSH.CX_QSH_0666SW"/>
     <cge:Meas_Ref ObjectId="39680"/>
    <cge:TPSR_Ref TObjectID="6412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6413" ObjectName="SW-CX_QSH.CX_QSH_06667SW"/>
     <cge:Meas_Ref ObjectId="39681"/>
    <cge:TPSR_Ref TObjectID="6413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -102.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39725">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6427" ObjectName="SW-CX_QSH.CX_QSH_0621SW"/>
     <cge:Meas_Ref ObjectId="39725"/>
    <cge:TPSR_Ref TObjectID="6427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5094.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6428" ObjectName="SW-CX_QSH.CX_QSH_06217SW"/>
     <cge:Meas_Ref ObjectId="39726"/>
    <cge:TPSR_Ref TObjectID="6428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6429" ObjectName="SW-CX_QSH.CX_QSH_0622SW"/>
     <cge:Meas_Ref ObjectId="39727"/>
    <cge:TPSR_Ref TObjectID="6429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6430" ObjectName="SW-CX_QSH.CX_QSH_0626SW"/>
     <cge:Meas_Ref ObjectId="39728"/>
    <cge:TPSR_Ref TObjectID="6430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5094.000000 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6431" ObjectName="SW-CX_QSH.CX_QSH_06267SW"/>
     <cge:Meas_Ref ObjectId="39729"/>
    <cge:TPSR_Ref TObjectID="6431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39693">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6415" ObjectName="SW-CX_QSH.CX_QSH_0641SW"/>
     <cge:Meas_Ref ObjectId="39693"/>
    <cge:TPSR_Ref TObjectID="6415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6416" ObjectName="SW-CX_QSH.CX_QSH_06417SW"/>
     <cge:Meas_Ref ObjectId="39694"/>
    <cge:TPSR_Ref TObjectID="6416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6417" ObjectName="SW-CX_QSH.CX_QSH_0642SW"/>
     <cge:Meas_Ref ObjectId="39695"/>
    <cge:TPSR_Ref TObjectID="6417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -171.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6418" ObjectName="SW-CX_QSH.CX_QSH_0646SW"/>
     <cge:Meas_Ref ObjectId="39696"/>
    <cge:TPSR_Ref TObjectID="6418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6419" ObjectName="SW-CX_QSH.CX_QSH_06467SW"/>
     <cge:Meas_Ref ObjectId="39697"/>
    <cge:TPSR_Ref TObjectID="6419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6421" ObjectName="SW-CX_QSH.CX_QSH_0721SW"/>
     <cge:Meas_Ref ObjectId="39709"/>
    <cge:TPSR_Ref TObjectID="6421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6422" ObjectName="SW-CX_QSH.CX_QSH_07217SW"/>
     <cge:Meas_Ref ObjectId="39710"/>
    <cge:TPSR_Ref TObjectID="6422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6423" ObjectName="SW-CX_QSH.CX_QSH_0722SW"/>
     <cge:Meas_Ref ObjectId="39711"/>
    <cge:TPSR_Ref TObjectID="6423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6424" ObjectName="SW-CX_QSH.CX_QSH_0726SW"/>
     <cge:Meas_Ref ObjectId="39712"/>
    <cge:TPSR_Ref TObjectID="6424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6425" ObjectName="SW-CX_QSH.CX_QSH_07267SW"/>
     <cge:Meas_Ref ObjectId="39713"/>
    <cge:TPSR_Ref TObjectID="6425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6463" ObjectName="SW-CX_QSH.CX_QSH_0681SW"/>
     <cge:Meas_Ref ObjectId="39821"/>
    <cge:TPSR_Ref TObjectID="6463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39822">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6464" ObjectName="SW-CX_QSH.CX_QSH_06817SW"/>
     <cge:Meas_Ref ObjectId="39822"/>
    <cge:TPSR_Ref TObjectID="6464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39823">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6465" ObjectName="SW-CX_QSH.CX_QSH_0682SW"/>
     <cge:Meas_Ref ObjectId="39823"/>
    <cge:TPSR_Ref TObjectID="6465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39825">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -153.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6467" ObjectName="SW-CX_QSH.CX_QSH_0686SW"/>
     <cge:Meas_Ref ObjectId="39825"/>
    <cge:TPSR_Ref TObjectID="6467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 -207.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6466" ObjectName="SW-CX_QSH.CX_QSH_06860SW"/>
     <cge:Meas_Ref ObjectId="39824"/>
    <cge:TPSR_Ref TObjectID="6466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39826">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -130.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6468" ObjectName="SW-CX_QSH.CX_QSH_06867SW"/>
     <cge:Meas_Ref ObjectId="39826"/>
    <cge:TPSR_Ref TObjectID="6468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -480.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6406" ObjectName="SW-CX_QSH.CX_QSH_0902SW"/>
     <cge:Meas_Ref ObjectId="39671"/>
    <cge:TPSR_Ref TObjectID="6406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6407" ObjectName="SW-CX_QSH.CX_QSH_09027SW"/>
     <cge:Meas_Ref ObjectId="39672"/>
    <cge:TPSR_Ref TObjectID="6407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6451" ObjectName="SW-CX_QSH.CX_QSH_0121SW"/>
     <cge:Meas_Ref ObjectId="39790"/>
    <cge:TPSR_Ref TObjectID="6451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6453" ObjectName="SW-CX_QSH.CX_QSH_0122SW"/>
     <cge:Meas_Ref ObjectId="39793"/>
    <cge:TPSR_Ref TObjectID="6453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6452" ObjectName="SW-CX_QSH.CX_QSH_01217SW"/>
     <cge:Meas_Ref ObjectId="39791"/>
    <cge:TPSR_Ref TObjectID="6452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6454" ObjectName="SW-CX_QSH.CX_QSH_01227SW"/>
     <cge:Meas_Ref ObjectId="39794"/>
    <cge:TPSR_Ref TObjectID="6454"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SZ" endPointId="0" endStationName="CX_QSH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_SheQing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3868,-1211 3868,-1147 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18139" ObjectName="AC-35kV.LN_SheQing"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3868,-1211 3868,-1147 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2451fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -760.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2358ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -507.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f24530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4519.000000 -760.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3467c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 -507.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b21d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 -880.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a1b970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -953.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf84f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -976.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eff3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -976.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2671a00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 -913.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28cbf60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3571.000000 -968.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_316f8a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 -1037.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3484510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -915.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f3f900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 -970.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f26d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -1039.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345fb50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 -886.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f1cf60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -959.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f27640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 -383.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2499c00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 -154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2366df0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3588.000000 -528.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3386a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -383.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee8660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34610f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 -383.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f07ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 -154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f0afb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -383.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec3b00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -203.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f11560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -126.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f10b80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2421000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.000000 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eec550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5148.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f2f830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5148.000000 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f088d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a3550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f3bbc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34866d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_348a460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1feb590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4470.000000 -206.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242bcf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.000000 -129.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3362d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -531.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f83d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4213.000000 -585.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_338bf40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 -587.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2371c20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 40.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2469790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 29.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2e002d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-766 3896,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2451fc0@0" ObjectIDZND0="10897@0" Pin0InfoVect0LinkObjId="SW-58284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2451fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-766 3896,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f160d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-766 3950,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10897@1" ObjectIDZND0="6384@x" ObjectIDZND1="6385@x" Pin0InfoVect0LinkObjId="SW-39631_0" Pin0InfoVect1LinkObjId="SW-39632_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-766 3950,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340d0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-626 3949,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="6469@0" ObjectIDZND0="10900@0" Pin0InfoVect0LinkObjId="SW-58286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-626 3949,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e537f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-455 3949,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6382@0" ObjectIDZND0="10898@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-455 3949,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2fc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-564 3968,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10896@x" ObjectIDND1="10900@x" ObjectIDZND0="g_1f76080@0" Pin0InfoVect0LinkObjId="g_1f76080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58278_0" Pin1InfoVect1LinkObjId="SW-58286_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-564 3968,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32addb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-551 3949,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10896@1" ObjectIDZND0="g_1f76080@0" ObjectIDZND1="10900@x" Pin0InfoVect0LinkObjId="g_1f76080_0" Pin0InfoVect1LinkObjId="SW-58286_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-551 3949,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f07dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-564 3949,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10896@x" ObjectIDND1="g_1f76080@0" ObjectIDZND0="10900@1" Pin0InfoVect0LinkObjId="SW-58286_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58278_0" Pin1InfoVect1LinkObjId="g_1f76080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-564 3949,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_308cec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-513 3965,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10896@x" ObjectIDND1="10898@x" ObjectIDZND0="10899@0" Pin0InfoVect0LinkObjId="SW-58285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58278_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-513 3965,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e425d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-513 4019,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10899@1" ObjectIDZND0="g_2358ab0@0" Pin0InfoVect0LinkObjId="g_2358ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-513 4019,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-504 3949,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10898@0" ObjectIDZND0="10896@x" ObjectIDZND1="10899@x" Pin0InfoVect0LinkObjId="SW-58278_0" Pin0InfoVect1LinkObjId="SW-58285_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-504 3949,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2369e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-513 3949,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10898@x" ObjectIDND1="10899@x" ObjectIDZND0="10896@0" Pin0InfoVect0LinkObjId="SW-58278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-58285_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-513 3949,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2462a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-706 3950,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6469@1" ObjectIDZND0="6384@0" Pin0InfoVect0LinkObjId="SW-39631_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-706 3950,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2462340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-750 3950,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6384@1" ObjectIDZND0="6385@x" ObjectIDZND1="10897@x" Pin0InfoVect0LinkObjId="SW-39632_0" Pin0InfoVect1LinkObjId="SW-58284_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-750 3950,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f206f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-766 3950,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6384@x" ObjectIDND1="10897@x" ObjectIDZND0="6385@1" Pin0InfoVect0LinkObjId="SW-39632_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39631_0" Pin1InfoVect1LinkObjId="SW-58284_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-766 3950,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efe4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-820 3950,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6385@0" ObjectIDZND0="6380@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-820 3950,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f073d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-766 4550,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2f24530@0" ObjectIDZND0="10902@0" Pin0InfoVect0LinkObjId="SW-58300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f24530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-766 4550,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ee8230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4586,-766 4604,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10902@1" ObjectIDZND0="6386@x" ObjectIDZND1="6387@x" Pin0InfoVect0LinkObjId="SW-39633_0" Pin0InfoVect1LinkObjId="SW-39634_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4586,-766 4604,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e461c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-626 4603,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="6470@0" ObjectIDZND0="10905@0" Pin0InfoVect0LinkObjId="SW-58303_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-626 4603,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cdcbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-458 4603,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="10903@1" Pin0InfoVect0LinkObjId="SW-58301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-458 4603,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3464120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-564 4622,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10901@x" ObjectIDND1="10905@x" ObjectIDZND0="g_1f76df0@0" Pin0InfoVect0LinkObjId="g_1f76df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58299_0" Pin1InfoVect1LinkObjId="SW-58303_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-564 4622,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34646d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-551 4603,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10901@1" ObjectIDZND0="g_1f76df0@0" ObjectIDZND1="10905@x" Pin0InfoVect0LinkObjId="g_1f76df0_0" Pin0InfoVect1LinkObjId="SW-58303_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-551 4603,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3463270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-564 4603,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10901@x" ObjectIDND1="g_1f76df0@0" ObjectIDZND0="10905@1" Pin0InfoVect0LinkObjId="SW-58303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58299_0" Pin1InfoVect1LinkObjId="g_1f76df0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-564 4603,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3463820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-513 4619,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10901@x" ObjectIDND1="10903@x" ObjectIDZND0="10904@0" Pin0InfoVect0LinkObjId="SW-58302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58299_0" Pin1InfoVect1LinkObjId="SW-58301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-513 4619,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34623c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-513 4673,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10904@1" ObjectIDZND0="g_3467c90@0" Pin0InfoVect0LinkObjId="g_3467c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58302_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-513 4673,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3462bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-504 4603,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10903@0" ObjectIDZND0="10901@x" ObjectIDZND1="10904@x" Pin0InfoVect0LinkObjId="SW-58299_0" Pin0InfoVect1LinkObjId="SW-58302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-504 4603,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3461d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-513 4603,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10903@x" ObjectIDND1="10904@x" ObjectIDZND0="10901@0" Pin0InfoVect0LinkObjId="SW-58299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58301_0" Pin1InfoVect1LinkObjId="SW-58302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-513 4603,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3460290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-706 4604,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6470@1" ObjectIDZND0="6386@0" Pin0InfoVect0LinkObjId="SW-39633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-706 4604,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3374780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-750 4604,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6386@1" ObjectIDZND0="6387@x" ObjectIDZND1="10902@x" Pin0InfoVect0LinkObjId="SW-39634_0" Pin0InfoVect1LinkObjId="SW-58300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39633_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-750 4604,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3373c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-766 4604,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6386@x" ObjectIDND1="10902@x" ObjectIDZND0="6387@1" Pin0InfoVect0LinkObjId="SW-39634_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39633_0" Pin1InfoVect1LinkObjId="SW-58300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-766 4604,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3373580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-820 4604,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6387@0" ObjectIDZND0="6381@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39634_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-820 4604,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2453ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-845 4499,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6381@0" ObjectIDZND0="6401@x" ObjectIDZND1="6402@x" Pin0InfoVect0LinkObjId="SW-39663_0" Pin0InfoVect1LinkObjId="SW-39664_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3373580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-845 4499,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3285e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-886 4515,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6381@0" ObjectIDND1="6401@x" ObjectIDZND0="6402@0" Pin0InfoVect0LinkObjId="SW-39664_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3373580_0" Pin1InfoVect1LinkObjId="SW-39663_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-886 4515,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a25e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-886 4569,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6402@1" ObjectIDZND0="g_32b21d0@0" Pin0InfoVect0LinkObjId="g_32b21d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39664_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-886 4569,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28b08f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-886 4499,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6381@0" ObjectIDND1="6402@x" ObjectIDZND0="6401@1" Pin0InfoVect0LinkObjId="SW-39663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3373580_0" Pin1InfoVect1LinkObjId="SW-39664_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-886 4499,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb9a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-1010 4499,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d7b650@1" ObjectIDZND0="g_2e53c30@0" Pin0InfoVect0LinkObjId="g_2e53c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d7b650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-1010 4499,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f24b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-959 4534,-959 4534,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d7b650@0" ObjectIDND1="6401@x" ObjectIDND2="11215@x" ObjectIDZND0="g_31e24b0@0" Pin0InfoVect0LinkObjId="g_31e24b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d7b650_0" Pin1InfoVect1LinkObjId="SW-39663_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-959 4534,-959 4534,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23cc770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-940 4499,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6401@0" ObjectIDZND0="g_31e24b0@0" ObjectIDZND1="g_1d7b650@0" ObjectIDZND2="11215@x" Pin0InfoVect0LinkObjId="g_31e24b0_0" Pin0InfoVect1LinkObjId="g_1d7b650_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-940 4499,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_342dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-959 4499,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_31e24b0@0" ObjectIDND1="6401@x" ObjectIDND2="11215@x" ObjectIDZND0="g_1d7b650@0" Pin0InfoVect0LinkObjId="g_1d7b650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_31e24b0_0" Pin1InfoVect1LinkObjId="SW-39663_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-959 4499,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ec0310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-959 4444,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a1b970@0" ObjectIDZND0="11215@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a1b970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-959 4444,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-959 4499,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="11215@1" ObjectIDZND0="g_31e24b0@0" ObjectIDZND1="g_1d7b650@0" ObjectIDZND2="6401@x" Pin0InfoVect0LinkObjId="g_31e24b0_0" Pin0InfoVect1LinkObjId="g_1d7b650_0" Pin0InfoVect2LinkObjId="SW-39663_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-959 4499,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb7dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-845 4428,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="6381@0" ObjectIDZND0="g_26d31c0@0" Pin0InfoVect0LinkObjId="g_26d31c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3373580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-845 4428,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24632e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-845 4212,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6380@0" ObjectIDZND0="6397@1" Pin0InfoVect0LinkObjId="SW-39658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efe4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-845 4212,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2462c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-845 4318,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6381@0" ObjectIDZND0="6398@1" Pin0InfoVect0LinkObjId="SW-39659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3373580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-845 4318,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2459df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-901 4212,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6397@0" ObjectIDZND0="6396@x" ObjectIDZND1="10908@x" Pin0InfoVect0LinkObjId="SW-39657_0" Pin0InfoVect1LinkObjId="SW-58313_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-901 4212,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2357280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-901 4318,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6398@0" ObjectIDZND0="6396@x" ObjectIDZND1="10909@x" Pin0InfoVect0LinkObjId="SW-39657_0" Pin0InfoVect1LinkObjId="SW-58314_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-901 4318,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24642a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-914 4253,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6397@x" ObjectIDND1="10908@x" ObjectIDZND0="6396@1" Pin0InfoVect0LinkObjId="SW-39657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39658_0" Pin1InfoVect1LinkObjId="SW-58313_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-914 4253,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfcdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-914 4318,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6396@0" ObjectIDZND0="6398@x" ObjectIDZND1="10909@x" Pin0InfoVect0LinkObjId="SW-39659_0" Pin0InfoVect1LinkObjId="SW-58314_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-914 4318,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3934a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-914 4212,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6396@x" ObjectIDND1="6397@x" ObjectIDZND0="10908@1" Pin0InfoVect0LinkObjId="SW-58313_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39657_0" Pin1InfoVect1LinkObjId="SW-39658_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-914 4212,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb9c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-966 4212,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10908@0" ObjectIDZND0="g_2cf84f0@0" Pin0InfoVect0LinkObjId="g_2cf84f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58313_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-966 4212,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a284f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-914 4318,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6396@x" ObjectIDND1="6398@x" ObjectIDZND0="10909@1" Pin0InfoVect0LinkObjId="SW-58314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39657_0" Pin1InfoVect1LinkObjId="SW-39659_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-914 4318,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3279ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-966 4318,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10909@0" ObjectIDZND0="g_2eff3c0@0" Pin0InfoVect0LinkObjId="g_2eff3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-966 4318,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e302d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-845 3652,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6380@0" ObjectIDZND0="6393@1" Pin0InfoVect0LinkObjId="SW-39647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efe4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-845 3652,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e66850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-902 3652,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6393@0" ObjectIDZND0="6392@x" ObjectIDZND1="11207@x" Pin0InfoVect0LinkObjId="SW-39646_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-902 3652,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e43070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-919 3638,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6392@x" ObjectIDND1="6393@x" ObjectIDZND0="11207@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39646_0" Pin1InfoVect1LinkObjId="SW-39647_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-919 3638,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e43620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-919 3588,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11207@0" ObjectIDZND0="g_2671a00@0" Pin0InfoVect0LinkObjId="g_2671a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-919 3588,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b7c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-919 3652,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6393@x" ObjectIDND1="11207@x" ObjectIDZND0="6392@0" Pin0InfoVect0LinkObjId="SW-39646_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39647_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-919 3652,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ad600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-974 3639,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6392@x" ObjectIDND1="6394@x" ObjectIDZND0="11203@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39646_0" Pin1InfoVect1LinkObjId="SW-39648_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-974 3639,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdc9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3603,-974 3589,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11203@0" ObjectIDZND0="g_28cbf60@0" Pin0InfoVect0LinkObjId="g_28cbf60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3603,-974 3589,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f07190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-961 3652,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6392@1" ObjectIDZND0="6394@x" ObjectIDZND1="11203@x" Pin0InfoVect0LinkObjId="SW-39648_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39646_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-961 3652,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbc010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-1043 3638,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_240bbb0@0" ObjectIDND1="11201@x" ObjectIDND2="6394@x" ObjectIDZND0="6395@1" Pin0InfoVect0LinkObjId="SW-39649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_240bbb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-39648_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-1043 3638,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbd700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-1043 3588,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6395@0" ObjectIDZND0="g_316f8a0@0" Pin0InfoVect0LinkObjId="g_316f8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-1043 3588,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbc5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-974 3652,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6392@x" ObjectIDND1="11203@x" ObjectIDZND0="6394@1" Pin0InfoVect0LinkObjId="SW-39648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39646_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-974 3652,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd45d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-1026 3652,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6394@0" ObjectIDZND0="g_240bbb0@0" ObjectIDZND1="11201@x" ObjectIDZND2="6395@x" Pin0InfoVect0LinkObjId="g_240bbb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-39649_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-1026 3652,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3934ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-1043 3652,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6394@x" ObjectIDND1="6395@x" ObjectIDZND0="g_240bbb0@0" ObjectIDZND1="11201@x" Pin0InfoVect0LinkObjId="g_240bbb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39648_0" Pin1InfoVect1LinkObjId="SW-39649_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-1043 3652,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cba6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-1127 3629,-1127 3629,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6394@x" ObjectIDND1="6395@x" ObjectIDND2="11201@x" ObjectIDZND0="g_240bbb0@0" Pin0InfoVect0LinkObjId="g_240bbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39648_0" Pin1InfoVect1LinkObjId="SW-39649_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-1127 3629,-1127 3629,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb0c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-1062 3652,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6394@x" ObjectIDND1="6395@x" ObjectIDND2="11201@x" ObjectIDZND0="g_240bbb0@0" Pin0InfoVect0LinkObjId="g_240bbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39648_0" Pin1InfoVect1LinkObjId="SW-39649_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-1062 3652,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3e210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-1127 3652,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="6394@x" ObjectIDND1="6395@x" ObjectIDND2="11201@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39648_0" Pin1InfoVect1LinkObjId="SW-39649_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-1127 3652,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e43bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-1062 3683,-1062 3683,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_240bbb0@0" ObjectIDND1="6394@x" ObjectIDND2="6395@x" ObjectIDZND0="11201@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_240bbb0_0" Pin1InfoVect1LinkObjId="SW-39648_0" Pin1InfoVect2LinkObjId="SW-39649_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-1062 3683,-1062 3683,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2fa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-1106 3683,-1119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="11201@0" ObjectIDZND0="g_23e4c40@0" Pin0InfoVect0LinkObjId="g_23e4c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-1106 3683,-1119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfd650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-845 3868,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6380@0" ObjectIDZND0="6389@1" Pin0InfoVect0LinkObjId="SW-39636_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efe4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-845 3868,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dc6f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-904 3868,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6389@0" ObjectIDZND0="6388@x" ObjectIDZND1="10906@x" Pin0InfoVect0LinkObjId="SW-39635_0" Pin0InfoVect1LinkObjId="SW-58310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39636_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-904 3868,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3484990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-921 3854,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6388@x" ObjectIDND1="6389@x" ObjectIDZND0="10906@1" Pin0InfoVect0LinkObjId="SW-58310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39635_0" Pin1InfoVect1LinkObjId="SW-39636_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-921 3854,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3484750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-921 3804,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10906@0" ObjectIDZND0="g_3484510@0" Pin0InfoVect0LinkObjId="g_3484510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-921 3804,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33737a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-921 3868,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6389@x" ObjectIDND1="10906@x" ObjectIDZND0="6388@0" Pin0InfoVect0LinkObjId="SW-39635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39636_0" Pin1InfoVect1LinkObjId="SW-58310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-921 3868,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3278e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-976 3855,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6388@x" ObjectIDND1="6390@x" ObjectIDZND0="10907@1" Pin0InfoVect0LinkObjId="SW-58311_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39635_0" Pin1InfoVect1LinkObjId="SW-39637_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-976 3855,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3274dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-976 3805,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10907@0" ObjectIDZND0="g_2f3f900@0" Pin0InfoVect0LinkObjId="g_2f3f900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58311_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-976 3805,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dc2870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-963 3868,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6388@1" ObjectIDZND0="6390@x" ObjectIDZND1="10907@x" Pin0InfoVect0LinkObjId="SW-39637_0" Pin0InfoVect1LinkObjId="SW-58311_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-963 3868,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f23cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1045 3854,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3460030@0" ObjectIDND1="18139@1" ObjectIDND2="11198@x" ObjectIDZND0="6391@1" Pin0InfoVect0LinkObjId="SW-39638_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3460030_0" Pin1InfoVect1LinkObjId="g_3463a40_1" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1045 3854,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24335d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-1045 3804,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6391@0" ObjectIDZND0="g_2f26d90@0" Pin0InfoVect0LinkObjId="g_2f26d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39638_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-1045 3804,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e465d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-976 3868,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6388@x" ObjectIDND1="10907@x" ObjectIDZND0="6390@1" Pin0InfoVect0LinkObjId="SW-39637_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39635_0" Pin1InfoVect1LinkObjId="SW-58311_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-976 3868,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3fb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1028 3868,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="6390@0" ObjectIDZND0="g_3460030@0" ObjectIDZND1="18139@1" ObjectIDZND2="11198@x" Pin0InfoVect0LinkObjId="g_3460030_0" Pin0InfoVect1LinkObjId="g_3463a40_1" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39637_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1028 3868,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e40c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1045 3868,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="6390@x" ObjectIDND1="6391@x" ObjectIDZND0="g_3460030@0" ObjectIDZND1="18139@1" ObjectIDZND2="11198@x" Pin0InfoVect0LinkObjId="g_3460030_0" Pin0InfoVect1LinkObjId="g_3463a40_1" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39637_0" Pin1InfoVect1LinkObjId="SW-39638_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1045 3868,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e973a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1129 3845,-1129 3845,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6390@x" ObjectIDND1="6391@x" ObjectIDND2="11198@x" ObjectIDZND0="g_3460030@0" Pin0InfoVect0LinkObjId="g_3460030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39637_0" Pin1InfoVect1LinkObjId="SW-39638_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1129 3845,-1129 3845,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34648f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1064 3868,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="6390@x" ObjectIDND1="6391@x" ObjectIDND2="11198@x" ObjectIDZND0="g_3460030@0" ObjectIDZND1="18139@1" Pin0InfoVect0LinkObjId="g_3460030_0" Pin0InfoVect1LinkObjId="g_3463a40_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39637_0" Pin1InfoVect1LinkObjId="SW-39638_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1064 3868,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3463a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1129 3868,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="6390@x" ObjectIDND1="6391@x" ObjectIDND2="11198@x" ObjectIDZND0="18139@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39637_0" Pin1InfoVect1LinkObjId="SW-39638_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1129 3868,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245a030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1064 3899,-1064 3899,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3460030@0" ObjectIDND1="18139@1" ObjectIDND2="6390@x" ObjectIDZND0="11198@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3460030_0" Pin1InfoVect1LinkObjId="g_3463a40_1" Pin1InfoVect2LinkObjId="SW-39637_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1064 3899,-1064 3899,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24594f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3899,-1108 3899,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="11198@0" ObjectIDZND0="g_3372a20@0" Pin0InfoVect0LinkObjId="g_3372a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3899,-1108 3899,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_244c4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-845 4047,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6380@0" ObjectIDZND0="6399@x" ObjectIDZND1="6400@x" Pin0InfoVect0LinkObjId="SW-39661_0" Pin0InfoVect1LinkObjId="SW-39662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efe4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-845 4047,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1d260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-892 4063,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6380@0" ObjectIDND1="6399@x" ObjectIDZND0="6400@0" Pin0InfoVect0LinkObjId="SW-39662_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2efe4b0_0" Pin1InfoVect1LinkObjId="SW-39661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-892 4063,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-892 4117,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6400@1" ObjectIDZND0="g_345fb50@0" Pin0InfoVect0LinkObjId="g_345fb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-892 4117,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef02f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-892 4047,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6380@0" ObjectIDND1="6400@x" ObjectIDZND0="6399@1" Pin0InfoVect0LinkObjId="SW-39661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2efe4b0_0" Pin1InfoVect1LinkObjId="SW-39662_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-892 4047,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3488550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-1016 4047,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e01650@1" ObjectIDZND0="g_2eeda90@0" Pin0InfoVect0LinkObjId="g_2eeda90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e01650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-1016 4047,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c3c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-965 4082,-965 4082,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e01650@0" ObjectIDND1="6399@x" ObjectIDND2="11215@x" ObjectIDZND0="g_32ae030@0" Pin0InfoVect0LinkObjId="g_32ae030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e01650_0" Pin1InfoVect1LinkObjId="SW-39661_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-965 4082,-965 4082,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3fba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-946 4047,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6399@0" ObjectIDZND0="g_32ae030@0" ObjectIDZND1="g_2e01650@0" ObjectIDZND2="11215@x" Pin0InfoVect0LinkObjId="g_32ae030_0" Pin0InfoVect1LinkObjId="g_2e01650_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-946 4047,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3171a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-965 4047,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_32ae030@0" ObjectIDND1="6399@x" ObjectIDND2="11215@x" ObjectIDZND0="g_2e01650@0" Pin0InfoVect0LinkObjId="g_2e01650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_32ae030_0" Pin1InfoVect1LinkObjId="SW-39661_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-965 4047,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef56b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-965 3992,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2f1cf60@0" ObjectIDZND0="11215@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f1cf60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-965 3992,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dc7240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4028,-965 4047,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="11215@1" ObjectIDZND0="g_32ae030@0" ObjectIDZND1="g_2e01650@0" ObjectIDZND2="6399@x" Pin0InfoVect0LinkObjId="g_32ae030_0" Pin0InfoVect1LinkObjId="g_2e01650_0" Pin0InfoVect2LinkObjId="SW-39661_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4028,-965 4047,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eeb6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-455 4120,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6382@0" ObjectIDZND0="6445@0" Pin0InfoVect0LinkObjId="SW-39773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-455 4120,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30fc960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-389 4137,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6444@x" ObjectIDND1="6445@x" ObjectIDZND0="6446@0" Pin0InfoVect0LinkObjId="SW-39774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39772_0" Pin1InfoVect1LinkObjId="SW-39773_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-389 4137,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30fcf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-389 4191,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6446@1" ObjectIDZND0="g_2f27640@0" Pin0InfoVect0LinkObjId="g_2f27640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-389 4191,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34676e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-401 4120,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6445@1" ObjectIDZND0="6444@x" ObjectIDZND1="6446@x" Pin0InfoVect0LinkObjId="SW-39772_0" Pin0InfoVect1LinkObjId="SW-39774_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-401 4120,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dbf2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-389 4120,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6445@x" ObjectIDND1="6446@x" ObjectIDZND0="6444@1" Pin0InfoVect0LinkObjId="SW-39772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39773_0" Pin1InfoVect1LinkObjId="SW-39774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-389 4120,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3388130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4121,-160 4106,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2f13790@0" ObjectIDND1="g_31e4460@0" ObjectIDND2="6448@x" ObjectIDZND0="g_3383290@0" Pin0InfoVect0LinkObjId="g_3383290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f13790_0" Pin1InfoVect1LinkObjId="g_31e4460_0" Pin1InfoVect2LinkObjId="SW-39776_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4121,-160 4106,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e7980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4121,-160 4137,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3383290@0" ObjectIDND1="g_2f13790@0" ObjectIDND2="g_31e4460@0" ObjectIDZND0="6449@0" Pin0InfoVect0LinkObjId="SW-39777_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3383290_0" Pin1InfoVect1LinkObjId="g_2f13790_0" Pin1InfoVect2LinkObjId="g_31e4460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4121,-160 4137,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eea810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-160 4191,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6449@1" ObjectIDZND0="g_2499c00@0" Pin0InfoVect0LinkObjId="g_2499c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39777_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-160 4191,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2364190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-455 3674,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6382@0" ObjectIDZND0="6404@1" Pin0InfoVect0LinkObjId="SW-39669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-455 3674,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f477a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3655,-534 3674,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6405@1" ObjectIDZND0="g_2eed600@0" ObjectIDZND1="6404@x" Pin0InfoVect0LinkObjId="g_2eed600_0" Pin0InfoVect1LinkObjId="SW-39669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3655,-534 3674,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eed3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-518 3674,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6404@0" ObjectIDZND0="g_2eed600@0" ObjectIDZND1="6405@x" Pin0InfoVect0LinkObjId="g_2eed600_0" Pin0InfoVect1LinkObjId="SW-39670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-518 3674,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f27b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-534 3674,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6404@x" ObjectIDND1="6405@x" ObjectIDZND0="g_2eed600@0" Pin0InfoVect0LinkObjId="g_2eed600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39669_0" Pin1InfoVect1LinkObjId="SW-39670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-534 3674,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2366b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-534 3619,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2366df0@0" ObjectIDZND0="6405@0" Pin0InfoVect0LinkObjId="SW-39670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2366df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-534 3619,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3e4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-455 3961,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6382@0" ObjectIDZND0="6439@0" Pin0InfoVect0LinkObjId="SW-39757_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-455 3961,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33859d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-389 3978,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6438@x" ObjectIDND1="6439@x" ObjectIDZND0="6440@0" Pin0InfoVect0LinkObjId="SW-39758_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39756_0" Pin1InfoVect1LinkObjId="SW-39757_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-389 3978,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3384f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-389 4032,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6440@1" ObjectIDZND0="g_3386a90@0" Pin0InfoVect0LinkObjId="g_3386a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-389 4032,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cdd210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-401 3961,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6439@1" ObjectIDZND0="6438@x" ObjectIDZND1="6440@x" Pin0InfoVect0LinkObjId="SW-39756_0" Pin0InfoVect1LinkObjId="SW-39758_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39757_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-401 3961,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e327c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-389 3961,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6439@x" ObjectIDND1="6440@x" ObjectIDZND0="6438@1" Pin0InfoVect0LinkObjId="SW-39756_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39757_0" Pin1InfoVect1LinkObjId="SW-39758_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-389 3961,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee9090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-160 3947,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6442@x" ObjectIDND1="6443@x" ObjectIDZND0="g_2ef85a0@0" Pin0InfoVect0LinkObjId="g_2ef85a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39760_0" Pin1InfoVect1LinkObjId="SW-39761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-160 3947,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee92f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-160 3961,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2ef85a0@0" ObjectIDND1="6442@x" ObjectIDND2="6443@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ef85a0_0" Pin1InfoVect1LinkObjId="SW-39760_0" Pin1InfoVect2LinkObjId="SW-39761_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-160 3961,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33763b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-160 3978,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2ef85a0@0" ObjectIDND1="6442@x" ObjectIDZND0="6443@0" Pin0InfoVect0LinkObjId="SW-39761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ef85a0_0" Pin1InfoVect1LinkObjId="SW-39760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-160 3978,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee8b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-160 4032,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6443@1" ObjectIDZND0="g_2ee8660@0" Pin0InfoVect0LinkObjId="g_2ee8660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-160 4032,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee9ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-455 3803,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6382@0" ObjectIDZND0="6433@0" Pin0InfoVect0LinkObjId="SW-39741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-455 3803,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f6940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-389 3820,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6432@x" ObjectIDND1="6433@x" ObjectIDZND0="6434@0" Pin0InfoVect0LinkObjId="SW-39742_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39740_0" Pin1InfoVect1LinkObjId="SW-39741_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-389 3820,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f6ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-389 3874,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6434@1" ObjectIDZND0="g_34610f0@0" Pin0InfoVect0LinkObjId="g_34610f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-389 3874,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3398390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-401 3803,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6433@1" ObjectIDZND0="6432@x" ObjectIDZND1="6434@x" Pin0InfoVect0LinkObjId="SW-39740_0" Pin0InfoVect1LinkObjId="SW-39742_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-401 3803,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23eff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-389 3803,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6433@x" ObjectIDND1="6434@x" ObjectIDZND0="6432@1" Pin0InfoVect0LinkObjId="SW-39740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39741_0" Pin1InfoVect1LinkObjId="SW-39742_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-389 3803,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3397de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-160 3789,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6436@x" ObjectIDND1="6437@x" ObjectIDZND0="g_2f47a90@0" Pin0InfoVect0LinkObjId="g_2f47a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39744_0" Pin1InfoVect1LinkObjId="SW-39745_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-160 3789,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3384830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-160 3803,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2f47a90@0" ObjectIDND1="6436@x" ObjectIDND2="6437@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f47a90_0" Pin1InfoVect1LinkObjId="SW-39744_0" Pin1InfoVect2LinkObjId="SW-39745_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-160 3803,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e41810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-160 3820,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2f47a90@0" ObjectIDND1="6436@x" ObjectIDZND0="6437@0" Pin0InfoVect0LinkObjId="SW-39745_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f47a90_0" Pin1InfoVect1LinkObjId="SW-39744_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-160 3820,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e41a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-160 3874,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6437@1" ObjectIDZND0="g_2f07ab0@0" Pin0InfoVect0LinkObjId="g_2f07ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-160 3874,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f48230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-455 3648,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6382@0" ObjectIDZND0="6456@0" Pin0InfoVect0LinkObjId="SW-39805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-455 3648,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eef950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-389 3665,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6455@x" ObjectIDND1="6456@x" ObjectIDZND0="6457@0" Pin0InfoVect0LinkObjId="SW-39806_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39804_0" Pin1InfoVect1LinkObjId="SW-39805_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-389 3665,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eefbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3701,-389 3719,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6457@1" ObjectIDZND0="g_2f0afb0@0" Pin0InfoVect0LinkObjId="g_2f0afb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3701,-389 3719,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_244aa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-401 3648,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6456@1" ObjectIDZND0="6455@x" ObjectIDZND1="6457@x" Pin0InfoVect0LinkObjId="SW-39804_0" Pin0InfoVect1LinkObjId="SW-39806_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39805_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-401 3648,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f12e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-389 3648,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6456@x" ObjectIDND1="6457@x" ObjectIDZND0="6455@1" Pin0InfoVect0LinkObjId="SW-39804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39805_0" Pin1InfoVect1LinkObjId="SW-39806_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-389 3648,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec38a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3630,-209 3648,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6459@1" ObjectIDZND0="g_3479730@0" ObjectIDZND1="6460@x" Pin0InfoVect0LinkObjId="g_3479730_0" Pin0InfoVect1LinkObjId="SW-39809_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39808_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3630,-209 3648,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30c6210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-191 3648,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6460@0" ObjectIDZND0="g_3479730@0" ObjectIDZND1="6459@x" Pin0InfoVect0LinkObjId="g_3479730_0" Pin0InfoVect1LinkObjId="SW-39808_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39809_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-191 3648,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30c6470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-143 3663,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3443620@0" ObjectIDND1="6461@x" ObjectIDND2="6460@x" ObjectIDZND0="g_242fac0@0" Pin0InfoVect0LinkObjId="g_242fac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3443620_0" Pin1InfoVect1LinkObjId="SW-39810_0" Pin1InfoVect2LinkObjId="SW-39809_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-143 3663,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30c66d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-143 3648,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_242fac0@0" ObjectIDND1="g_3443620@0" ObjectIDND2="6461@x" ObjectIDZND0="6460@1" Pin0InfoVect0LinkObjId="SW-39809_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_242fac0_0" Pin1InfoVect1LinkObjId="g_3443620_0" Pin1InfoVect2LinkObjId="SW-39810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-143 3648,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f11300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-132 3648,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="6461@1" ObjectIDZND0="g_242fac0@0" ObjectIDZND1="6460@x" ObjectIDZND2="g_3443620@0" Pin0InfoVect0LinkObjId="g_242fac0_0" Pin0InfoVect1LinkObjId="SW-39809_0" Pin0InfoVect2LinkObjId="g_3443620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-132 3648,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34433c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-132 3648,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3443620@0" ObjectIDND1="6461@x" ObjectIDZND0="g_242fac0@0" ObjectIDZND1="6460@x" Pin0InfoVect0LinkObjId="g_242fac0_0" Pin0InfoVect1LinkObjId="SW-39809_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3443620_0" Pin1InfoVect1LinkObjId="SW-39810_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-132 3648,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3383030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-118 3648,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3443620@1" ObjectIDZND0="g_242fac0@0" ObjectIDZND1="6460@x" ObjectIDZND2="6461@x" Pin0InfoVect0LinkObjId="g_242fac0_0" Pin0InfoVect1LinkObjId="SW-39809_0" Pin0InfoVect2LinkObjId="SW-39810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3443620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-118 3648,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1f580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-458 4734,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="6409@0" Pin0InfoVect0LinkObjId="SW-39677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-458 4734,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2389680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-392 4751,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6408@x" ObjectIDND1="6409@x" ObjectIDZND0="6410@0" Pin0InfoVect0LinkObjId="SW-39678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39676_0" Pin1InfoVect1LinkObjId="SW-39677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-392 4751,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23898e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-392 4805,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6410@1" ObjectIDZND0="g_2f10b80@0" Pin0InfoVect0LinkObjId="g_2f10b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-392 4805,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_231e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-404 4734,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6409@1" ObjectIDZND0="6408@x" ObjectIDZND1="6410@x" Pin0InfoVect0LinkObjId="SW-39676_0" Pin0InfoVect1LinkObjId="SW-39678_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-404 4734,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-392 4734,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6409@x" ObjectIDND1="6410@x" ObjectIDZND0="6408@1" Pin0InfoVect0LinkObjId="SW-39676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39677_0" Pin1InfoVect1LinkObjId="SW-39678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-392 4734,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-335 4749,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6408@x" ObjectIDND1="6411@x" ObjectIDZND0="g_247c300@0" Pin0InfoVect0LinkObjId="g_247c300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39676_0" Pin1InfoVect1LinkObjId="SW-39679_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-335 4749,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4fea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-349 4734,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6408@0" ObjectIDZND0="g_247c300@0" ObjectIDZND1="6411@x" Pin0InfoVect0LinkObjId="g_247c300_0" Pin0InfoVect1LinkObjId="SW-39679_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-349 4734,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f85050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-163 4720,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6412@x" ObjectIDND1="0@x" ObjectIDND2="6413@x" ObjectIDZND0="g_2f1dc20@0" Pin0InfoVect0LinkObjId="g_2f1dc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39680_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-39681_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-163 4720,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3482c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-163 4751,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f1dc20@0" ObjectIDND1="6412@x" ObjectIDND2="0@x" ObjectIDZND0="6413@0" Pin0InfoVect0LinkObjId="SW-39681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f1dc20_0" Pin1InfoVect1LinkObjId="SW-39680_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-163 4751,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3482ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-163 4805,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6413@1" ObjectIDZND0="g_2421000@0" Pin0InfoVect0LinkObjId="g_2421000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-163 4805,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ee270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-163 4734,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f1dc20@0" ObjectIDND1="6412@x" ObjectIDND2="6413@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f1dc20_0" Pin1InfoVect1LinkObjId="SW-39680_0" Pin1InfoVect2LinkObjId="SW-39681_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-163 4734,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23ee4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-96 4817,-96 4817,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24288b0@0" ObjectIDND1="0@x" ObjectIDZND0="g_23ee990@0" Pin0InfoVect0LinkObjId="g_23ee990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24288b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-96 4817,-96 4817,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23ee730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-107 4734,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_23ee990@0" ObjectIDZND1="g_24288b0@0" Pin0InfoVect0LinkObjId="g_23ee990_0" Pin0InfoVect1LinkObjId="g_24288b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-107 4734,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f5bb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-25 4749,-25 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24288b0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2f004e0@0" Pin0InfoVect0LinkObjId="g_2f004e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24288b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-25 4749,-25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3362490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-25 4734,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2f004e0@0" ObjectIDND1="g_24288b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f004e0_0" Pin1InfoVect1LinkObjId="g_24288b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-25 4734,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f3d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,29 4734,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,29 4734,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3378740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,134 4734,150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,134 4734,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33789a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,71 4734,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2efe990@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2efe990_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,71 4734,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2efe730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,87 4734,98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2efe990@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2efe990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,87 4734,98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f522b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,87 4718,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2efe990@0" Pin0InfoVect0LinkObjId="g_2efe990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,87 4718,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f52510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,87 4670,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2efe990@1" ObjectIDZND0="g_2eff030@0" Pin0InfoVect0LinkObjId="g_2eff030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efe990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,87 4670,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_338f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-458 5082,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="6427@0" Pin0InfoVect0LinkObjId="SW-39725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-458 5082,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336f4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-392 5099,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6426@x" ObjectIDND1="6427@x" ObjectIDZND0="6428@0" Pin0InfoVect0LinkObjId="SW-39726_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39724_0" Pin1InfoVect1LinkObjId="SW-39725_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-392 5099,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336f700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-392 5153,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6428@1" ObjectIDZND0="g_2eec550@0" Pin0InfoVect0LinkObjId="g_2eec550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-392 5153,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-404 5082,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6427@1" ObjectIDZND0="6426@x" ObjectIDZND1="6428@x" Pin0InfoVect0LinkObjId="SW-39724_0" Pin0InfoVect1LinkObjId="SW-39726_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-404 5082,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2388100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-392 5082,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6427@x" ObjectIDND1="6428@x" ObjectIDZND0="6426@1" Pin0InfoVect0LinkObjId="SW-39724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39725_0" Pin1InfoVect1LinkObjId="SW-39726_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-392 5082,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2388360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-335 5097,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6426@x" ObjectIDND1="6429@x" ObjectIDZND0="g_242ed50@0" Pin0InfoVect0LinkObjId="g_242ed50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39724_0" Pin1InfoVect1LinkObjId="SW-39727_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-335 5097,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23885c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-349 5082,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6426@0" ObjectIDZND0="g_242ed50@0" ObjectIDZND1="6429@x" Pin0InfoVect0LinkObjId="g_242ed50_0" Pin0InfoVect1LinkObjId="SW-39727_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-349 5082,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33e4ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-163 5068,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6430@x" ObjectIDND1="6431@x" ObjectIDZND0="g_33f7780@0" Pin0InfoVect0LinkObjId="g_33f7780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39728_0" Pin1InfoVect1LinkObjId="SW-39729_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-163 5068,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33e4d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-163 5082,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_33f7780@0" ObjectIDND1="6430@x" ObjectIDND2="6431@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33f7780_0" Pin1InfoVect1LinkObjId="SW-39728_0" Pin1InfoVect2LinkObjId="SW-39729_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-163 5082,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2c4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-163 5099,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_33f7780@0" ObjectIDND1="6430@x" ObjectIDZND0="6431@0" Pin0InfoVect0LinkObjId="SW-39729_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33f7780_0" Pin1InfoVect1LinkObjId="SW-39728_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-163 5099,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-163 5153,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6431@1" ObjectIDZND0="g_1f2f830@0" Pin0InfoVect0LinkObjId="g_1f2f830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39729_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-163 5153,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2efdf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-458 4912,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="6415@0" Pin0InfoVect0LinkObjId="SW-39693_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-458 4912,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f7f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-392 4929,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6414@x" ObjectIDND1="6415@x" ObjectIDZND0="6416@0" Pin0InfoVect0LinkObjId="SW-39694_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39692_0" Pin1InfoVect1LinkObjId="SW-39693_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-392 4929,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f7fbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4965,-392 4983,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6416@1" ObjectIDZND0="g_1f088d0@0" Pin0InfoVect0LinkObjId="g_1f088d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39694_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4965,-392 4983,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f7fe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-404 4912,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6415@1" ObjectIDZND0="6414@x" ObjectIDZND1="6416@x" Pin0InfoVect0LinkObjId="SW-39692_0" Pin0InfoVect1LinkObjId="SW-39694_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39693_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-404 4912,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2424570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-392 4912,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6415@x" ObjectIDND1="6416@x" ObjectIDZND0="6414@1" Pin0InfoVect0LinkObjId="SW-39692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39693_0" Pin1InfoVect1LinkObjId="SW-39694_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-392 4912,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24247d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-335 4927,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6414@x" ObjectIDND1="6417@x" ObjectIDZND0="g_242e050@0" Pin0InfoVect0LinkObjId="g_242e050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39692_0" Pin1InfoVect1LinkObjId="SW-39695_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-335 4927,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2424a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-349 4912,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6414@0" ObjectIDZND0="g_242e050@0" ObjectIDZND1="6417@x" Pin0InfoVect0LinkObjId="g_242e050_0" Pin0InfoVect1LinkObjId="SW-39695_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-349 4912,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-163 4898,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6418@x" ObjectIDND1="6419@x" ObjectIDZND0="g_3436c20@0" Pin0InfoVect0LinkObjId="g_3436c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39696_0" Pin1InfoVect1LinkObjId="SW-39697_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-163 4898,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-163 4912,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_3436c20@0" ObjectIDND1="6418@x" ObjectIDND2="6419@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3436c20_0" Pin1InfoVect1LinkObjId="SW-39696_0" Pin1InfoVect2LinkObjId="SW-39697_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-163 4912,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ecb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-163 4929,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3436c20@0" ObjectIDND1="6418@x" ObjectIDZND0="6419@0" Pin0InfoVect0LinkObjId="SW-39697_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3436c20_0" Pin1InfoVect1LinkObjId="SW-39696_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-163 4929,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ecd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4965,-163 4983,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6419@1" ObjectIDZND0="g_20a3550@0" Pin0InfoVect0LinkObjId="g_20a3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4965,-163 4983,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f6440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-458 4363,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="6421@0" Pin0InfoVect0LinkObjId="SW-39709_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-458 4363,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3ccd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-392 4380,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6420@x" ObjectIDND1="6421@x" ObjectIDZND0="6422@0" Pin0InfoVect0LinkObjId="SW-39710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39708_0" Pin1InfoVect1LinkObjId="SW-39709_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-392 4380,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3cf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-392 4434,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6422@1" ObjectIDZND0="g_1f3bbc0@0" Pin0InfoVect0LinkObjId="g_1f3bbc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-392 4434,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3d190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-404 4363,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6421@1" ObjectIDZND0="6420@x" ObjectIDZND1="6422@x" Pin0InfoVect0LinkObjId="SW-39708_0" Pin0InfoVect1LinkObjId="SW-39710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-404 4363,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3360760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-392 4363,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6421@x" ObjectIDND1="6422@x" ObjectIDZND0="6420@1" Pin0InfoVect0LinkObjId="SW-39708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39709_0" Pin1InfoVect1LinkObjId="SW-39710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-392 4363,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33609c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-335 4378,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6420@x" ObjectIDND1="6423@x" ObjectIDZND0="g_247ab90@0" Pin0InfoVect0LinkObjId="g_247ab90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39708_0" Pin1InfoVect1LinkObjId="SW-39711_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-335 4378,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3360c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-349 4363,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6420@0" ObjectIDZND0="g_247ab90@0" ObjectIDZND1="6423@x" Pin0InfoVect0LinkObjId="g_247ab90_0" Pin0InfoVect1LinkObjId="SW-39711_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-349 4363,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34427e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-163 4349,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6424@x" ObjectIDND1="6425@x" ObjectIDZND0="g_3441af0@0" Pin0InfoVect0LinkObjId="g_3441af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39712_0" Pin1InfoVect1LinkObjId="SW-39713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-163 4349,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3442a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-163 4363,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_3441af0@0" ObjectIDND1="6424@x" ObjectIDND2="6425@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3441af0_0" Pin1InfoVect1LinkObjId="SW-39712_0" Pin1InfoVect2LinkObjId="SW-39713_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-163 4363,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34870c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-163 4380,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3441af0@0" ObjectIDND1="6424@x" ObjectIDZND0="6425@0" Pin0InfoVect0LinkObjId="SW-39713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3441af0_0" Pin1InfoVect1LinkObjId="SW-39712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-163 4380,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3487320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-163 4434,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6425@1" ObjectIDZND0="g_34866d0@0" Pin0InfoVect0LinkObjId="g_34866d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-163 4434,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fa65f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-458 4555,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="6463@0" Pin0InfoVect0LinkObjId="SW-39821_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-458 4555,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f89770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-392 4572,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6462@x" ObjectIDND1="6463@x" ObjectIDZND0="6464@0" Pin0InfoVect0LinkObjId="SW-39822_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39820_0" Pin1InfoVect1LinkObjId="SW-39821_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-392 4572,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f899d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-392 4626,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6464@1" ObjectIDZND0="g_348a460@0" Pin0InfoVect0LinkObjId="g_348a460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39822_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-392 4626,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f89c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-404 4555,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6463@1" ObjectIDZND0="6462@x" ObjectIDZND1="6464@x" Pin0InfoVect0LinkObjId="SW-39820_0" Pin0InfoVect1LinkObjId="SW-39822_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-404 4555,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2426870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-392 4555,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6463@x" ObjectIDND1="6464@x" ObjectIDZND0="6462@1" Pin0InfoVect0LinkObjId="SW-39820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39821_0" Pin1InfoVect1LinkObjId="SW-39822_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-392 4555,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2426ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-335 4570,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6462@x" ObjectIDND1="6465@x" ObjectIDZND0="g_247b650@0" Pin0InfoVect0LinkObjId="g_247b650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39820_0" Pin1InfoVect1LinkObjId="SW-39823_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-335 4570,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2426d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-349 4555,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6462@0" ObjectIDZND0="g_247b650@0" ObjectIDZND1="6465@x" Pin0InfoVect0LinkObjId="g_247b650_0" Pin0InfoVect1LinkObjId="SW-39823_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-349 4555,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feb330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-212 4555,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6466@1" ObjectIDZND0="g_1fdc110@0" ObjectIDZND1="6467@x" Pin0InfoVect0LinkObjId="g_1fdc110_0" Pin0InfoVect1LinkObjId="SW-39825_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39824_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-212 4555,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1febfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-194 4555,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6467@0" ObjectIDZND0="g_1fdc110@0" ObjectIDZND1="6466@x" Pin0InfoVect0LinkObjId="g_1fdc110_0" Pin0InfoVect1LinkObjId="SW-39824_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39825_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-194 4555,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e7c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-146 4570,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_242c980@0" ObjectIDND1="6468@x" ObjectIDND2="6467@x" ObjectIDZND0="g_2430830@0" Pin0InfoVect0LinkObjId="g_2430830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_242c980_0" Pin1InfoVect1LinkObjId="SW-39826_0" Pin1InfoVect2LinkObjId="SW-39825_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-146 4570,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e7e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-146 4555,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2430830@0" ObjectIDND1="g_242c980@0" ObjectIDND2="6468@x" ObjectIDZND0="6467@1" Pin0InfoVect0LinkObjId="SW-39825_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2430830_0" Pin1InfoVect1LinkObjId="g_242c980_0" Pin1InfoVect2LinkObjId="SW-39826_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-146 4555,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242b830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-135 4500,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_242bcf0@0" ObjectIDZND0="6468@0" Pin0InfoVect0LinkObjId="SW-39826_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_242bcf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-135 4500,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242ba90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4536,-135 4555,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="6468@1" ObjectIDZND0="g_2430830@0" ObjectIDZND1="6467@x" ObjectIDZND2="g_242c980@0" Pin0InfoVect0LinkObjId="g_2430830_0" Pin0InfoVect1LinkObjId="SW-39825_0" Pin0InfoVect2LinkObjId="g_242c980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39826_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4536,-135 4555,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-135 4555,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_242c980@0" ObjectIDND1="6468@x" ObjectIDZND0="g_2430830@0" ObjectIDZND1="6467@x" Pin0InfoVect0LinkObjId="g_2430830_0" Pin0InfoVect1LinkObjId="SW-39825_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_242c980_0" Pin1InfoVect1LinkObjId="SW-39826_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-135 4555,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-81 4555,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_33675c0@0" ObjectIDZND0="g_242c980@0" Pin0InfoVect0LinkObjId="g_242c980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33675c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-81 4555,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242d3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-121 4555,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_242c980@1" ObjectIDZND0="g_2430830@0" ObjectIDZND1="6467@x" ObjectIDZND2="6468@x" Pin0InfoVect0LinkObjId="g_2430830_0" Pin0InfoVect1LinkObjId="SW-39825_0" Pin0InfoVect2LinkObjId="SW-39826_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_242c980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-121 4555,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f8df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-458 4443,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="6406@1" Pin0InfoVect0LinkObjId="SW-39671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-458 4443,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3362b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-537 4388,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3362d90@0" ObjectIDZND0="6407@0" Pin0InfoVect0LinkObjId="SW-39672_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3362d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-537 4388,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340f0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-455 4219,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6382@0" ObjectIDZND0="6451@1" Pin0InfoVect0LinkObjId="SW-39790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-455 4219,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23513d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-458 4324,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6383@0" ObjectIDZND0="6453@1" Pin0InfoVect0LinkObjId="SW-39793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-458 4324,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2351630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-508 4219,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6451@0" ObjectIDZND0="6450@x" ObjectIDZND1="6452@x" Pin0InfoVect0LinkObjId="SW-39788_0" Pin0InfoVect1LinkObjId="SW-39791_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-508 4219,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2351890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-509 4324,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="6453@0" ObjectIDZND0="g_2351d50@0" ObjectIDZND1="6450@x" ObjectIDZND2="6454@x" Pin0InfoVect0LinkObjId="g_2351d50_0" Pin0InfoVect1LinkObjId="SW-39788_0" Pin0InfoVect2LinkObjId="SW-39794_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39793_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-509 4324,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2351af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-523 4294,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6450@x" ObjectIDND1="6454@x" ObjectIDND2="6453@x" ObjectIDZND0="g_2351d50@0" Pin0InfoVect0LinkObjId="g_2351d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39788_0" Pin1InfoVect1LinkObjId="SW-39794_0" Pin1InfoVect2LinkObjId="SW-39793_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-523 4294,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f833d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-523 4324,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2351d50@0" ObjectIDND1="6450@x" ObjectIDND2="6453@x" ObjectIDZND0="6454@1" Pin0InfoVect0LinkObjId="SW-39794_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2351d50_0" Pin1InfoVect1LinkObjId="SW-39788_0" Pin1InfoVect2LinkObjId="SW-39793_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-523 4324,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f83630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-578 4324,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6454@0" ObjectIDZND0="g_338bf40@0" Pin0InfoVect0LinkObjId="g_338bf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-578 4324,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f83890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-523 4219,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6450@x" ObjectIDND1="6451@x" ObjectIDZND0="6452@1" Pin0InfoVect0LinkObjId="SW-39791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39788_0" Pin1InfoVect1LinkObjId="SW-39790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-523 4219,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f83af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-577 4219,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6452@0" ObjectIDZND0="g_1f83d50@0" Pin0InfoVect0LinkObjId="g_1f83d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-577 4219,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc0690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-275 4225,-275 4225,-111 4120,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f13790@0" ObjectIDND1="g_31e4460@0" ObjectIDZND0="g_3383290@0" ObjectIDZND1="6448@x" ObjectIDZND2="6449@x" Pin0InfoVect0LinkObjId="g_3383290_0" Pin0InfoVect1LinkObjId="SW-39776_0" Pin0InfoVect2LinkObjId="SW-39777_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f13790_0" Pin1InfoVect1LinkObjId="g_31e4460_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-275 4225,-275 4225,-111 4120,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc1000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4288,-275 4272,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f13790@0" ObjectIDZND0="g_3383290@0" ObjectIDZND1="6448@x" ObjectIDZND2="6449@x" Pin0InfoVect0LinkObjId="g_3383290_0" Pin0InfoVect1LinkObjId="SW-39776_0" Pin0InfoVect2LinkObjId="SW-39777_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f13790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4288,-275 4272,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc2a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-275 4272,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3383290@0" ObjectIDND1="6448@x" ObjectIDND2="6449@x" ObjectIDZND0="g_31e4460@0" Pin0InfoVect0LinkObjId="g_31e4460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3383290_0" Pin1InfoVect1LinkObjId="SW-39776_0" Pin1InfoVect2LinkObjId="SW-39777_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-275 4272,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc34e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-160 4120,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3383290@0" ObjectIDND1="6448@x" ObjectIDND2="6449@x" ObjectIDZND0="g_2f13790@0" ObjectIDZND1="g_31e4460@0" Pin0InfoVect0LinkObjId="g_2f13790_0" Pin0InfoVect1LinkObjId="g_31e4460_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3383290_0" Pin1InfoVect1LinkObjId="SW-39776_0" Pin1InfoVect2LinkObjId="SW-39777_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-160 4120,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc3740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-111 4120,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="g_2f13790@0" ObjectIDND1="g_31e4460@0" ObjectIDND2="g_3383290@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f13790_0" Pin1InfoVect1LinkObjId="g_31e4460_0" Pin1InfoVect2LinkObjId="g_3383290_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-111 4120,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f54140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-163 5082,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_33f7780@0" ObjectIDND1="6431@x" ObjectIDZND0="6430@1" Pin0InfoVect0LinkObjId="SW-39728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33f7780_0" Pin1InfoVect1LinkObjId="SW-39729_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-163 5082,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f543a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-210 5082,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6430@0" ObjectIDZND0="g_1f533f0@1" Pin0InfoVect0LinkObjId="g_1f533f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-210 5082,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f54600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-276 5082,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f533f0@0" ObjectIDZND0="6429@1" Pin0InfoVect0LinkObjId="SW-39727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f533f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-276 5082,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f54860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-324 5082,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="6429@0" ObjectIDZND0="6426@x" ObjectIDZND1="g_242ed50@0" Pin0InfoVect0LinkObjId="SW-39724_0" Pin0InfoVect1LinkObjId="g_242ed50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-324 5082,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2429600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-25 4734,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2f004e0@0" ObjectIDND1="0@x" ObjectIDZND0="g_24288b0@1" Pin0InfoVect0LinkObjId="g_24288b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f004e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-25 4734,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2429860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-87 4734,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_24288b0@0" ObjectIDZND0="g_23ee990@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_23ee990_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24288b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-87 4734,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2429ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-325 4912,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="6417@0" ObjectIDZND0="6414@x" ObjectIDZND1="g_242e050@0" Pin0InfoVect0LinkObjId="SW-39692_0" Pin0InfoVect1LinkObjId="g_242e050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-325 4912,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2429d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-277 4912,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f526a0@0" ObjectIDZND0="6417@1" Pin0InfoVect0LinkObjId="SW-39695_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f526a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-277 4912,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2429f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-163 4912,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3436c20@0" ObjectIDND1="6419@x" ObjectIDZND0="6418@1" Pin0InfoVect0LinkObjId="SW-39696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3436c20_0" Pin1InfoVect1LinkObjId="SW-39697_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-163 4912,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242a1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-212 4912,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6418@0" ObjectIDZND0="g_1f526a0@1" Pin0InfoVect0LinkObjId="g_1f526a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-212 4912,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242a440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-163 4734,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f1dc20@0" ObjectIDND1="0@x" ObjectIDND2="6413@x" ObjectIDZND0="6412@1" Pin0InfoVect0LinkObjId="SW-39680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f1dc20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-39681_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-163 4734,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242a6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-210 4734,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6412@0" ObjectIDZND0="g_1fdce60@1" Pin0InfoVect0LinkObjId="g_1fdce60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-210 4734,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242a900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-276 4734,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fdce60@0" ObjectIDZND0="6411@1" Pin0InfoVect0LinkObjId="SW-39679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fdce60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-276 4734,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-324 4734,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="6411@0" ObjectIDZND0="6408@x" ObjectIDZND1="g_247c300@0" Pin0InfoVect0LinkObjId="SW-39676_0" Pin0InfoVect1LinkObjId="g_247c300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-324 4734,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242adc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-212 4555,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6467@x" ObjectIDND1="6466@x" ObjectIDZND0="g_1fdc110@1" Pin0InfoVect0LinkObjId="g_1fdc110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39825_0" Pin1InfoVect1LinkObjId="SW-39824_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-212 4555,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_242b020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-275 4555,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fdc110@0" ObjectIDZND0="6465@1" Pin0InfoVect0LinkObjId="SW-39823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fdc110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-275 4555,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f20f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-325 4555,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="6465@0" ObjectIDZND0="6462@x" ObjectIDZND1="g_247b650@0" Pin0InfoVect0LinkObjId="SW-39820_0" Pin0InfoVect1LinkObjId="g_247b650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-325 4555,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f21190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-163 4363,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3441af0@0" ObjectIDND1="6425@x" ObjectIDZND0="6424@1" Pin0InfoVect0LinkObjId="SW-39712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3441af0_0" Pin1InfoVect1LinkObjId="SW-39713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-163 4363,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f213f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-209 4363,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6424@0" ObjectIDZND0="g_1fdb3c0@1" Pin0InfoVect0LinkObjId="g_1fdb3c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-209 4363,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f21650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-276 4363,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fdb3c0@0" ObjectIDZND0="6423@1" Pin0InfoVect0LinkObjId="SW-39711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fdb3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-276 4363,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f218b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-325 4363,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="6423@0" ObjectIDZND0="6420@x" ObjectIDZND1="g_247ab90@0" Pin0InfoVect0LinkObjId="SW-39708_0" Pin0InfoVect1LinkObjId="g_247ab90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-325 4363,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f21b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-160 4120,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3383290@0" ObjectIDND1="g_2f13790@0" ObjectIDND2="g_31e4460@0" ObjectIDZND0="6448@1" Pin0InfoVect0LinkObjId="SW-39776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3383290_0" Pin1InfoVect1LinkObjId="g_2f13790_0" Pin1InfoVect2LinkObjId="g_31e4460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-160 4120,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f21d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-207 4120,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6448@0" ObjectIDZND0="g_347bab0@1" Pin0InfoVect0LinkObjId="g_347bab0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-207 4120,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f21fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-273 4120,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_347bab0@0" ObjectIDZND0="6447@1" Pin0InfoVect0LinkObjId="SW-39775_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_347bab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-273 4120,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f22230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-160 3961,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2ef85a0@0" ObjectIDND1="6443@x" ObjectIDZND0="6442@1" Pin0InfoVect0LinkObjId="SW-39760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ef85a0_0" Pin1InfoVect1LinkObjId="SW-39761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-160 3961,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f22490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-207 3961,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6442@0" ObjectIDZND0="g_347ad60@1" Pin0InfoVect0LinkObjId="g_347ad60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-207 3961,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f226f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-273 3961,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_347ad60@0" ObjectIDZND0="6441@1" Pin0InfoVect0LinkObjId="SW-39759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_347ad60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-273 3961,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f22950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-160 3803,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2f47a90@0" ObjectIDND1="6437@x" ObjectIDZND0="6436@1" Pin0InfoVect0LinkObjId="SW-39744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f47a90_0" Pin1InfoVect1LinkObjId="SW-39745_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-160 3803,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f22bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-207 3803,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6436@0" ObjectIDZND0="g_347a0a0@1" Pin0InfoVect0LinkObjId="g_347a0a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-207 3803,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f22e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-273 3803,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_347a0a0@0" ObjectIDZND0="6435@1" Pin0InfoVect0LinkObjId="SW-39743_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_347a0a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-273 3803,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f23070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-219 3648,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3479730@1" ObjectIDZND0="6460@x" ObjectIDZND1="6459@x" Pin0InfoVect0LinkObjId="SW-39809_0" Pin0InfoVect1LinkObjId="SW-39808_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3479730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-219 3648,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f232d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-272 3648,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3479730@0" ObjectIDZND0="6458@1" Pin0InfoVect0LinkObjId="SW-39807_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3479730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-272 3648,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3a2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-523 4245,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6451@x" ObjectIDND1="6452@x" ObjectIDZND0="6450@1" Pin0InfoVect0LinkObjId="SW-39788_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39790_0" Pin1InfoVect1LinkObjId="SW-39791_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-523 4245,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3ac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-523 4294,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6450@0" ObjectIDZND0="g_2351d50@0" ObjectIDZND1="6454@x" ObjectIDZND2="6453@x" Pin0InfoVect0LinkObjId="g_2351d50_0" Pin0InfoVect1LinkObjId="SW-39794_0" Pin0InfoVect2LinkObjId="SW-39793_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-523 4294,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-523 4324,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2351d50@0" ObjectIDND1="6450@x" ObjectIDZND0="6454@x" ObjectIDZND1="6453@x" Pin0InfoVect0LinkObjId="SW-39794_0" Pin0InfoVect1LinkObjId="SW-39793_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2351d50_0" Pin1InfoVect1LinkObjId="SW-39788_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-523 4324,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb9030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-77 3648,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2372620@0" ObjectIDZND0="g_3443620@0" Pin0InfoVect0LinkObjId="g_3443620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2372620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-77 3648,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23723f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3571,-132 3593,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2f11560@0" ObjectIDZND0="6461@0" Pin0InfoVect0LinkObjId="SW-39810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f11560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3571,-132 3593,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33663b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-7 3648,15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2372620@1" ObjectIDZND0="g_2371c20@0" Pin0InfoVect0LinkObjId="g_2371c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2372620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-7 3648,15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3366610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3583,-209 3583,-7 3648,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="switch" ObjectIDND0="g_2ec3b00@0" ObjectIDND1="6459@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ec3b00_0" Pin1InfoVect1LinkObjId="SW-39808_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3583,-209 3583,-7 3648,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3367100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3571,-209 3583,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ec3b00@0" ObjectIDZND0="6459@x" Pin0InfoVect0LinkObjId="SW-39808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ec3b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3571,-209 3583,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3367360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3583,-209 3594,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ec3b00@0" ObjectIDZND0="6459@0" Pin0InfoVect0LinkObjId="SW-39808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ec3b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3583,-209 3594,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246a1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-11 4555,2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_33675c0@1" ObjectIDZND0="g_2469790@0" Pin0InfoVect0LinkObjId="g_2469790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33675c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-11 4555,2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_247d680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-212 4494,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1feb590@0" ObjectIDZND0="6466@x" Pin0InfoVect0LinkObjId="SW-39824_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1feb590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-212 4494,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_247d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-212 4501,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1feb590@0" ObjectIDZND0="6466@0" Pin0InfoVect0LinkObjId="SW-39824_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1feb590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-212 4501,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_247db40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4554,-10 4494,-10 4494,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" EndDevType1="switch" ObjectIDZND0="g_1feb590@0" ObjectIDZND1="6466@x" Pin0InfoVect0LinkObjId="g_1feb590_0" Pin0InfoVect1LinkObjId="SW-39824_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4554,-10 4494,-10 4494,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f77b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-346 3648,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6455@0" ObjectIDZND0="6458@0" Pin0InfoVect0LinkObjId="SW-39807_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-346 3648,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f77dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-346 3803,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6432@0" ObjectIDZND0="6435@0" Pin0InfoVect0LinkObjId="SW-39743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-346 3803,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f78020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-346 3961,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6438@0" ObjectIDZND0="6441@0" Pin0InfoVect0LinkObjId="SW-39759_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39756_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-346 3961,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f78280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-346 4120,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6444@0" ObjectIDZND0="6447@0" Pin0InfoVect0LinkObjId="SW-39775_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-346 4120,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f9160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-537 4443,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6406@x" ObjectIDND1="6407@x" ObjectIDZND0="g_3363790@0" Pin0InfoVect0LinkObjId="g_3363790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39671_0" Pin1InfoVect1LinkObjId="SW-39672_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-537 4443,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f9c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-521 4443,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6406@0" ObjectIDZND0="g_3363790@0" ObjectIDZND1="6407@x" Pin0InfoVect0LinkObjId="g_3363790_0" Pin0InfoVect1LinkObjId="SW-39672_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-521 4443,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f9eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-537 4424,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3363790@0" ObjectIDND1="6406@x" ObjectIDZND0="6407@1" Pin0InfoVect0LinkObjId="SW-39672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3363790_0" Pin1InfoVect1LinkObjId="SW-39671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-537 4424,-537 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6381" cx="4499" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6381" cx="4428" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6380" cx="4047" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6380" cx="3950" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6382" cx="3949" cy="-455" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6381" cx="4604" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="4603" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6380" cx="4212" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6381" cx="4318" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6380" cx="3652" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6380" cx="3868" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6382" cx="4120" cy="-455" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6382" cx="3674" cy="-455" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6382" cx="3961" cy="-455" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6382" cx="3803" cy="-455" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6382" cx="3648" cy="-455" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="4734" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4734" cy="150" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="5082" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="4912" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="4363" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="4555" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="4443" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6382" cx="4219" cy="-455" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6383" cx="4324" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-28" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3380.000000 -1080.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28" ObjectName="DYN-CX_QSH"/>
     <cge:Meas_Ref ObjectId="28"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338da50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4331.000000 -67.000000) translate(0,15)">大窝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f733c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4517.000000 35.000000) translate(0,15)">2号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f736e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4612.000000 163.000000) translate(0,15)">10kV金泰焊材厂配电室10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f73b90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4771.000000 14.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f73da0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4858.000000 -74.000000) translate(0,15)">工业园区I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f74180" transform="matrix(1.000000 0.000000 0.000000 1.000000 3589.000000 39.000000) translate(0,15)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f744a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3763.000000 -67.000000) translate(0,15)">冶炼厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f746e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 -67.000000) translate(0,15)">张武庄煤矿Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f74910" transform="matrix(1.000000 0.000000 0.000000 1.000000 4065.000000 -67.000000) translate(0,15)">干海资集镇线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f75210" transform="matrix(1.000000 0.000000 0.000000 1.000000 4225.000000 -305.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f75490" transform="matrix(1.000000 0.000000 0.000000 1.000000 5023.000000 -74.000000) translate(0,15)">工业园区II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f756d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3597.000000 -477.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e4f80" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -480.000000) translate(0,15)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e51c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -679.000000) translate(0,15)">10kVII段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e5be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4640.000000 -700.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e6130" transform="matrix(1.000000 0.000000 0.000000 1.000000 3982.000000 -671.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e63b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3579.000000 -678.000000) translate(0,15)">10kVI段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e6600" transform="matrix(1.000000 0.000000 0.000000 1.000000 4394.000000 -747.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e6830" transform="matrix(1.000000 0.000000 0.000000 1.000000 4403.000000 -1104.000000) translate(0,15)">35kVII段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e7120" transform="matrix(1.000000 0.000000 0.000000 1.000000 3960.000000 -1104.000000) translate(0,15)">35kVI段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e73a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3832.000000 -1262.000000) translate(0,15)">舍清线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_337a1e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4251.000000 -904.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_337a740" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -1259.000000) translate(0,15)">煤矿I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_337a9c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4241.000000 -513.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337ac00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3661.000000 -955.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337b560" transform="matrix(1.000000 0.000000 0.000000 1.000000 3659.000000 -891.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337bac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3659.000000 -1015.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337bf20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3603.000000 -1066.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337c260" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 -957.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa2540" transform="matrix(1.000000 0.000000 0.000000 1.000000 3875.000000 -893.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa2750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3875.000000 -1017.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa2990" transform="matrix(1.000000 0.000000 0.000000 1.000000 3816.000000 -1071.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa2bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4054.000000 -935.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa3410" transform="matrix(1.000000 0.000000 0.000000 1.000000 4065.000000 -915.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa3690" transform="matrix(1.000000 0.000000 0.000000 1.000000 4507.000000 -932.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa38d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4517.000000 -909.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa3b10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4254.000000 -938.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa3d50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -890.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa3f90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4325.000000 -890.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa41d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3537.000000 -866.000000) translate(0,15)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa4410" transform="matrix(1.000000 0.000000 0.000000 1.000000 4652.000000 -866.000000) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa4650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3959.000000 -744.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa4890" transform="matrix(1.000000 0.000000 0.000000 1.000000 3957.000000 -809.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa4ad0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4613.000000 -744.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2464de0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -809.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2465020" transform="matrix(1.000000 0.000000 0.000000 1.000000 4248.000000 -545.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2465260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4172.000000 -497.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24654a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4331.000000 -498.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24656e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4168.000000 -569.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2465920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4331.000000 -567.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2465b60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3681.000000 -507.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2465da0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3617.000000 -560.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2465fe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4450.000000 -510.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466220" transform="matrix(1.000000 0.000000 0.000000 1.000000 4385.000000 -563.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466460" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -367.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24666a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3664.000000 -431.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24668e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3655.000000 -306.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466b20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3669.000000 -414.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3655.000000 -180.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466fa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -235.000000) translate(0,12)">06160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24671e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3590.000000 -126.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2467420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3812.000000 -367.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbe970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3810.000000 -426.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbebb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3810.000000 -306.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbedf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3810.000000 -201.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbf030" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -186.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbf270" transform="matrix(1.000000 0.000000 0.000000 1.000000 3826.000000 -412.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbf4b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3970.000000 -367.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbf6f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3968.000000 -426.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbfb50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3968.000000 -306.000000) translate(0,12)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbfd90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3968.000000 -201.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbffd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3982.000000 -183.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc0210" transform="matrix(1.000000 0.000000 0.000000 1.000000 3990.000000 -410.000000) translate(0,12)">06517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc0450" transform="matrix(1.000000 0.000000 0.000000 1.000000 4129.000000 -367.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc39a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4127.000000 -426.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc3e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4127.000000 -306.000000) translate(0,12)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc4090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4127.000000 -201.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc42d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4141.000000 -184.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc4510" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -411.000000) translate(0,12)">06717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc4750" transform="matrix(1.000000 0.000000 0.000000 1.000000 4372.000000 -370.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc4990" transform="matrix(1.000000 0.000000 0.000000 1.000000 4370.000000 -429.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc4bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4381.000000 -414.000000) translate(0,12)">07217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc4e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4370.000000 -309.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc5050" transform="matrix(1.000000 0.000000 0.000000 1.000000 4370.000000 -204.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc5290" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -189.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34681b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4564.000000 -370.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34683f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4562.000000 -429.000000) translate(0,12)">0681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468850" transform="matrix(1.000000 0.000000 0.000000 1.000000 4562.000000 -309.000000) translate(0,12)">0682</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468a90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4570.000000 -414.000000) translate(0,12)">06817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468cd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4562.000000 -183.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468f10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4498.000000 -238.000000) translate(0,12)">06860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3469150" transform="matrix(1.000000 0.000000 0.000000 1.000000 4503.000000 -130.000000) translate(0,12)">06867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3469390" transform="matrix(1.000000 0.000000 0.000000 1.000000 4743.000000 -370.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34695d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4741.000000 -429.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3469810" transform="matrix(1.000000 0.000000 0.000000 1.000000 4741.000000 -309.000000) translate(0,12)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3469a50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -414.000000) translate(0,12)">06617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3469c90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4741.000000 -204.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3469ed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -189.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346a110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4921.000000 -370.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346a450" transform="matrix(1.000000 0.000000 0.000000 1.000000 4919.000000 -429.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346a8b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4919.000000 -309.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e19e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -414.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1c20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4919.000000 -204.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1e60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -189.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e20a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5091.000000 -370.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e22e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5089.000000 -429.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2520" transform="matrix(1.000000 0.000000 0.000000 1.000000 5089.000000 -309.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2760" transform="matrix(1.000000 0.000000 0.000000 1.000000 5097.000000 -414.000000) translate(0,12)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e29a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5096.000000 -189.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5089.000000 -204.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f4a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3114.000000 -1042.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f4a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3114.000000 -1042.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f4a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3114.000000 -1042.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f4a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3114.000000 -1042.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f4a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3114.000000 -1042.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f4a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3114.000000 -1042.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f4a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3114.000000 -1042.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f31be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3112.000000 -582.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1fabac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3254.500000 -1160.500000) translate(0,16)">清水河变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fac9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -1102.000000) translate(0,12)">3610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1face10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3816.000000 -1000.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fad050" transform="matrix(1.000000 0.000000 0.000000 1.000000 3818.000000 -945.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fad290" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -1095.000000) translate(0,12)">3620</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fad4d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -999.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fad710" transform="matrix(1.000000 0.000000 0.000000 1.000000 3596.000000 -943.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f38110" transform="matrix(1.000000 0.000000 0.000000 1.000000 3990.000000 -990.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f38350" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -955.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f38590" transform="matrix(1.000000 0.000000 0.000000 1.000000 4325.000000 -955.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f387d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4442.000000 -985.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f38a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3894.000000 -792.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f38c50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3968.000000 -544.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f38e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3959.000000 -602.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f390d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3956.000000 -486.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f39310" transform="matrix(1.000000 0.000000 0.000000 1.000000 3967.000000 -507.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f39550" transform="matrix(1.000000 0.000000 0.000000 1.000000 4548.000000 -792.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f39790" transform="matrix(1.000000 0.000000 0.000000 1.000000 4612.000000 -545.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f399d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4613.000000 -600.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f39c10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4619.000000 -508.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f39e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -492.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f3a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4280.000000 -239.000000) translate(0,12)">0670</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb8450" transform="matrix(1.000000 0.000000 0.000000 1.000000 4740.000000 -152.000000) translate(0,15)">金泰焊材厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb88d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4682.000000 -134.000000) translate(0,12)">K0501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb8b90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4682.000000 2.000000) translate(0,12)">K0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb8df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4684.000000 52.000000) translate(0,12)">K052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_247edb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4827.000000 -18.000000) translate(0,15)">S9-63/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470a80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3781.000000 -674.000000) translate(0,12)">SFZ11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470a80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3781.000000 -674.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470a80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3781.000000 -674.000000) translate(0,42)">YN  d11   Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4641.000000 -677.000000) translate(0,12)">SFZ11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4641.000000 -677.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4641.000000 -677.000000) translate(0,42)">YN  d11   Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482790" transform="matrix(1.000000 0.000000 0.000000 1.000000 4382.000000 -725.000000) translate(0,12)">S11-50/10GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482790" transform="matrix(1.000000 0.000000 0.000000 1.000000 4382.000000 -725.000000) translate(0,27)">10.5±2×2.5%/0.4kV</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-39631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -715.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6384" ObjectName="SW-CX_QSH.CX_QSH_301BK"/>
     <cge:Meas_Ref ObjectId="39631"/>
    <cge:TPSR_Ref TObjectID="6384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10896" ObjectName="SW-CX_QSH.CX_QSH_001BK"/>
     <cge:Meas_Ref ObjectId="58278"/>
    <cge:TPSR_Ref TObjectID="10896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -715.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6386" ObjectName="SW-CX_QSH.CX_QSH_302BK"/>
     <cge:Meas_Ref ObjectId="39633"/>
    <cge:TPSR_Ref TObjectID="6386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58299">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10901" ObjectName="SW-CX_QSH.CX_QSH_002BK"/>
     <cge:Meas_Ref ObjectId="58299"/>
    <cge:TPSR_Ref TObjectID="10901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -904.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6396" ObjectName="SW-CX_QSH.CX_QSH_312BK"/>
     <cge:Meas_Ref ObjectId="39657"/>
    <cge:TPSR_Ref TObjectID="6396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39646">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -926.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6392" ObjectName="SW-CX_QSH.CX_QSH_362BK"/>
     <cge:Meas_Ref ObjectId="39646"/>
    <cge:TPSR_Ref TObjectID="6392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.000000 -928.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6388" ObjectName="SW-CX_QSH.CX_QSH_361BK"/>
     <cge:Meas_Ref ObjectId="39635"/>
    <cge:TPSR_Ref TObjectID="6388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6444" ObjectName="SW-CX_QSH.CX_QSH_067BK"/>
     <cge:Meas_Ref ObjectId="39772"/>
    <cge:TPSR_Ref TObjectID="6444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6438" ObjectName="SW-CX_QSH.CX_QSH_065BK"/>
     <cge:Meas_Ref ObjectId="39756"/>
    <cge:TPSR_Ref TObjectID="6438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39740">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6432" ObjectName="SW-CX_QSH.CX_QSH_063BK"/>
     <cge:Meas_Ref ObjectId="39740"/>
    <cge:TPSR_Ref TObjectID="6432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6455" ObjectName="SW-CX_QSH.CX_QSH_061BK"/>
     <cge:Meas_Ref ObjectId="39804"/>
    <cge:TPSR_Ref TObjectID="6455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6408" ObjectName="SW-CX_QSH.CX_QSH_066BK"/>
     <cge:Meas_Ref ObjectId="39676"/>
    <cge:TPSR_Ref TObjectID="6408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 80.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39724">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6426" ObjectName="SW-CX_QSH.CX_QSH_062BK"/>
     <cge:Meas_Ref ObjectId="39724"/>
    <cge:TPSR_Ref TObjectID="6426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6414" ObjectName="SW-CX_QSH.CX_QSH_064BK"/>
     <cge:Meas_Ref ObjectId="39692"/>
    <cge:TPSR_Ref TObjectID="6414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39708">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6420" ObjectName="SW-CX_QSH.CX_QSH_072BK"/>
     <cge:Meas_Ref ObjectId="39708"/>
    <cge:TPSR_Ref TObjectID="6420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39820">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6462" ObjectName="SW-CX_QSH.CX_QSH_068BK"/>
     <cge:Meas_Ref ObjectId="39820"/>
    <cge:TPSR_Ref TObjectID="6462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -513.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6450" ObjectName="SW-CX_QSH.CX_QSH_012BK"/>
     <cge:Meas_Ref ObjectId="39788"/>
    <cge:TPSR_Ref TObjectID="6450"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_QSH.CX_QSH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9167"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 -621.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 -621.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6469" ObjectName="TF-CX_QSH.CX_QSH_1T"/>
    <cge:TPSR_Ref TObjectID="6469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_QSH.CX_QSH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9171"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -621.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -621.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6470" ObjectName="TF-CX_QSH.CX_QSH_2T"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3202.500000 -1112.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-39547" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -763.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39547" ObjectName="CX_QSH:CX_QSH_302BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78572" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3245.538462 -1002.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78572" ObjectName="CX_QSH:CX_QSH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79885" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3245.538462 -959.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79885" ObjectName="CX_QSH:CX_QSH_sumQ"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3214" y="-1171"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3214" y="-1171"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3166" y="-1188"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3166" y="-1188"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb9310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 34.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fba550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 19.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2370dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3725.000000 4.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2371740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3540.000000 -62.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23719e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3571.000000 -77.000000) translate(0,12)">I(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247de90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -58.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247e3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -73.000000) translate(0,12)">I(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247e6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 278.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247e930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4753.000000 263.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247eb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 248.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247ffd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 659.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2480230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 644.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2480470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 629.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247fd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 557.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 542.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3832.000000 527.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3812.000000 754.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 739.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3824.000000 724.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 567.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2478050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 552.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2478260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4712.000000 537.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2478b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 761.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2478d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 746.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2478fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4667.000000 731.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2475dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 1309.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246f970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3568.000000 1294.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246fbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 1279.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 1044.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 1029.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2470840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4212.000000 1014.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 456.000000) translate(0,12)">Uo（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2483410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3441.000000 440.750000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2483690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3449.000000 470.500000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24838d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3449.000000 484.250000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2483b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3449.000000 498.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247a020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5185.000000 464.000000) translate(0,12)">Uo（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247a290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5170.000000 448.750000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247a4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.000000 478.500000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247a710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.000000 492.250000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247a950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.000000 506.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24876e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3407.000000 859.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2487950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3407.000000 873.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2487c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3413.000000 830.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2487d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3399.000000 816.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24871f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3407.000000 844.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e42120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 870.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_338d390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 884.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329dc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 841.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8c270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 827.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3920a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 855.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1d7b650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.000000 -974.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e24b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4527.000000 -1023.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e53c30">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 4519.000000 -1070.000000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26d31c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 -749.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_240bbb0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3622.000000 -1064.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e4c40">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3693.000000 -1191.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3460030">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3838.000000 -1066.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3372a20">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3909.000000 -1193.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e01650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -980.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ae030">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4075.000000 -1029.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eeda90">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 4067.000000 -1076.000000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e4460">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4282.000000 -183.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eed600">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3647.000000 -656.000000)" xlink:href="#lightningRod:shape87"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f13790">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4342.500000 -268.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef85a0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3893.500000 -152.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f47a90">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3735.500000 -152.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3443620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -82.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3383290">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4052.500000 -152.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f1dc20">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4666.500000 -155.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ee990">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4827.000000 8.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f004e0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4803.500000 -17.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2efe990">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 4723.500000 78.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eff030">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4645.500000 67.500000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33f7780">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5014.500000 -155.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3436c20">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4844.500000 -155.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3441af0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4295.500000 -155.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242c980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -85.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3363790">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4415.000000 -659.000000)" xlink:href="#lightningRod:shape87"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2351d50">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4287.000000 -457.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3479730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -214.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347a0a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3798.000000 -215.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347ad60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -215.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347bab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4115.000000 -215.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdb3c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4358.000000 -218.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdc110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -217.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdce60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.000000 -218.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f526a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.000000 -219.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f533f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -218.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24288b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.000000 -29.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2372620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -2.000000)" xlink:href="#lightningRod:shape156"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33675c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -6.000000)" xlink:href="#lightningRod:shape156"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247ab90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -327.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247b650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -327.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247c300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -327.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242e050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -327.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242ed50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 -327.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242fac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -135.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2430830">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -138.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f76080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -556.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f76df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -556.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3214" y="-1171"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3166" y="-1188"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_QSH.CX_QSH_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-845 4722,-845 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6381" ObjectName="BS-CX_QSH.CX_QSH_3IIM"/>
    <cge:TPSR_Ref TObjectID="6381"/></metadata>
   <polyline fill="none" opacity="0" points="4308,-845 4722,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QSH.CX_QSH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-845 4229,-845 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6380" ObjectName="BS-CX_QSH.CX_QSH_3IM"/>
    <cge:TPSR_Ref TObjectID="6380"/></metadata>
   <polyline fill="none" opacity="0" points="3528,-845 4229,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QSH.CX_QSH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-455 4238,-455 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6382" ObjectName="BS-CX_QSH.CX_QSH_9IM"/>
    <cge:TPSR_Ref TObjectID="6382"/></metadata>
   <polyline fill="none" opacity="0" points="3604,-455 4238,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4704,150 4764,150 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4704,150 4764,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QSH.CX_QSH_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4306,-458 5156,-458 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6383" ObjectName="BS-CX_QSH.CX_QSH_9IIM"/>
    <cge:TPSR_Ref TObjectID="6383"/></metadata>
   <polyline fill="none" opacity="0" points="4306,-458 5156,-458 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39559" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3639.000000 -1308.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6392"/>
     <cge:Term_Ref ObjectID="9011"/>
    <cge:TPSR_Ref TObjectID="6392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39560" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3639.000000 -1308.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6392"/>
     <cge:Term_Ref ObjectID="9011"/>
    <cge:TPSR_Ref TObjectID="6392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39557" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3639.000000 -1308.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6392"/>
     <cge:Term_Ref ObjectID="9011"/>
    <cge:TPSR_Ref TObjectID="6392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39564" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -1043.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6396"/>
     <cge:Term_Ref ObjectID="9019"/>
    <cge:TPSR_Ref TObjectID="6396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39565" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -1043.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6396"/>
     <cge:Term_Ref ObjectID="9019"/>
    <cge:TPSR_Ref TObjectID="6396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39562" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -1043.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6396"/>
     <cge:Term_Ref ObjectID="9019"/>
    <cge:TPSR_Ref TObjectID="6396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39624" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4269.000000 -659.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6450"/>
     <cge:Term_Ref ObjectID="9127"/>
    <cge:TPSR_Ref TObjectID="6450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39625" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4269.000000 -659.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6450"/>
     <cge:Term_Ref ObjectID="9127"/>
    <cge:TPSR_Ref TObjectID="6450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39622" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4269.000000 -659.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6450"/>
     <cge:Term_Ref ObjectID="9127"/>
    <cge:TPSR_Ref TObjectID="6450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-39577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5262.000000 -506.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6383"/>
     <cge:Term_Ref ObjectID="8994"/>
    <cge:TPSR_Ref TObjectID="6383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-39578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5262.000000 -506.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6383"/>
     <cge:Term_Ref ObjectID="8994"/>
    <cge:TPSR_Ref TObjectID="6383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-39579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5262.000000 -506.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6383"/>
     <cge:Term_Ref ObjectID="8994"/>
    <cge:TPSR_Ref TObjectID="6383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-39630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5262.000000 -506.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6383"/>
     <cge:Term_Ref ObjectID="8994"/>
    <cge:TPSR_Ref TObjectID="6383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-39583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5262.000000 -506.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6383"/>
     <cge:Term_Ref ObjectID="8994"/>
    <cge:TPSR_Ref TObjectID="6383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-39574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3531.000000 -498.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6382"/>
     <cge:Term_Ref ObjectID="8993"/>
    <cge:TPSR_Ref TObjectID="6382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-39575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3531.000000 -498.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6382"/>
     <cge:Term_Ref ObjectID="8993"/>
    <cge:TPSR_Ref TObjectID="6382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-39576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3531.000000 -498.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6382"/>
     <cge:Term_Ref ObjectID="8993"/>
    <cge:TPSR_Ref TObjectID="6382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-39629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3531.000000 -498.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6382"/>
     <cge:Term_Ref ObjectID="8993"/>
    <cge:TPSR_Ref TObjectID="6382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-39582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3531.000000 -498.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6382"/>
     <cge:Term_Ref ObjectID="8993"/>
    <cge:TPSR_Ref TObjectID="6382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39540" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -754.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6384"/>
     <cge:Term_Ref ObjectID="8995"/>
    <cge:TPSR_Ref TObjectID="6384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39541" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -754.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6384"/>
     <cge:Term_Ref ObjectID="8995"/>
    <cge:TPSR_Ref TObjectID="6384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39538" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -754.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6384"/>
     <cge:Term_Ref ObjectID="8995"/>
    <cge:TPSR_Ref TObjectID="6384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39609" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6432"/>
     <cge:Term_Ref ObjectID="9091"/>
    <cge:TPSR_Ref TObjectID="6432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39610" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6432"/>
     <cge:Term_Ref ObjectID="9091"/>
    <cge:TPSR_Ref TObjectID="6432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39607" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6432"/>
     <cge:Term_Ref ObjectID="9091"/>
    <cge:TPSR_Ref TObjectID="6432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39614" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3941.000000 -34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6438"/>
     <cge:Term_Ref ObjectID="9103"/>
    <cge:TPSR_Ref TObjectID="6438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39615" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3941.000000 -34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6438"/>
     <cge:Term_Ref ObjectID="9103"/>
    <cge:TPSR_Ref TObjectID="6438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39612" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3941.000000 -34.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6438"/>
     <cge:Term_Ref ObjectID="9103"/>
    <cge:TPSR_Ref TObjectID="6438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39619" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4099.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6444"/>
     <cge:Term_Ref ObjectID="9115"/>
    <cge:TPSR_Ref TObjectID="6444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39620" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4099.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6444"/>
     <cge:Term_Ref ObjectID="9115"/>
    <cge:TPSR_Ref TObjectID="6444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39617" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4099.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6444"/>
     <cge:Term_Ref ObjectID="9115"/>
    <cge:TPSR_Ref TObjectID="6444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39599" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4330.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6420"/>
     <cge:Term_Ref ObjectID="9067"/>
    <cge:TPSR_Ref TObjectID="6420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39600" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4330.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6420"/>
     <cge:Term_Ref ObjectID="9067"/>
    <cge:TPSR_Ref TObjectID="6420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39597" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4330.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6420"/>
     <cge:Term_Ref ObjectID="9067"/>
    <cge:TPSR_Ref TObjectID="6420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39586" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4815.000000 -278.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6408"/>
     <cge:Term_Ref ObjectID="9043"/>
    <cge:TPSR_Ref TObjectID="6408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39591" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4815.000000 -278.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6408"/>
     <cge:Term_Ref ObjectID="9043"/>
    <cge:TPSR_Ref TObjectID="6408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39584" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4815.000000 -278.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6408"/>
     <cge:Term_Ref ObjectID="9043"/>
    <cge:TPSR_Ref TObjectID="6408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39594" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4904.000000 -39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6414"/>
     <cge:Term_Ref ObjectID="9055"/>
    <cge:TPSR_Ref TObjectID="6414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39595" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4904.000000 -39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6414"/>
     <cge:Term_Ref ObjectID="9055"/>
    <cge:TPSR_Ref TObjectID="6414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39592" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4904.000000 -39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6414"/>
     <cge:Term_Ref ObjectID="9055"/>
    <cge:TPSR_Ref TObjectID="6414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39604" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5060.000000 -39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6426"/>
     <cge:Term_Ref ObjectID="9079"/>
    <cge:TPSR_Ref TObjectID="6426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39605" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5060.000000 -39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6426"/>
     <cge:Term_Ref ObjectID="9079"/>
    <cge:TPSR_Ref TObjectID="6426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39602" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5060.000000 -39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6426"/>
     <cge:Term_Ref ObjectID="9079"/>
    <cge:TPSR_Ref TObjectID="6426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39588" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3599.000000 63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6455"/>
     <cge:Term_Ref ObjectID="9137"/>
    <cge:TPSR_Ref TObjectID="6455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39587" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3599.000000 63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6455"/>
     <cge:Term_Ref ObjectID="9137"/>
    <cge:TPSR_Ref TObjectID="6455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39590" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4525.000000 58.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6462"/>
     <cge:Term_Ref ObjectID="9151"/>
    <cge:TPSR_Ref TObjectID="6462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39589" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4525.000000 58.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6462"/>
     <cge:Term_Ref ObjectID="9151"/>
    <cge:TPSR_Ref TObjectID="6462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58349" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -557.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58349" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10896"/>
     <cge:Term_Ref ObjectID="15186"/>
    <cge:TPSR_Ref TObjectID="10896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58350" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -557.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58350" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10896"/>
     <cge:Term_Ref ObjectID="15186"/>
    <cge:TPSR_Ref TObjectID="10896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -557.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10896"/>
     <cge:Term_Ref ObjectID="15186"/>
    <cge:TPSR_Ref TObjectID="10896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58354" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4758.000000 -567.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58354" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10901"/>
     <cge:Term_Ref ObjectID="15196"/>
    <cge:TPSR_Ref TObjectID="10901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4758.000000 -567.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10901"/>
     <cge:Term_Ref ObjectID="15196"/>
    <cge:TPSR_Ref TObjectID="10901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58352" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4758.000000 -567.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58352" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10901"/>
     <cge:Term_Ref ObjectID="15196"/>
    <cge:TPSR_Ref TObjectID="10901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -747.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6386"/>
     <cge:Term_Ref ObjectID="8999"/>
    <cge:TPSR_Ref TObjectID="6386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -747.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6386"/>
     <cge:Term_Ref ObjectID="8999"/>
    <cge:TPSR_Ref TObjectID="6386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-39570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -885.000000) translate(0,12)">39570.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39570" ObjectName="CX_QSH.CX_QSH_3IIM:F"/>
     <cge:PSR_Ref ObjectID="6381"/>
     <cge:Term_Ref ObjectID="8992"/>
    <cge:TPSR_Ref TObjectID="6381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-39571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -885.000000) translate(0,27)">39571.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39571" ObjectName="CX_QSH.CX_QSH_3IIM:F"/>
     <cge:PSR_Ref ObjectID="6381"/>
     <cge:Term_Ref ObjectID="8992"/>
    <cge:TPSR_Ref TObjectID="6381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-39572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -885.000000) translate(0,42)">39572.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39572" ObjectName="CX_QSH.CX_QSH_3IIM:F"/>
     <cge:PSR_Ref ObjectID="6381"/>
     <cge:Term_Ref ObjectID="8992"/>
    <cge:TPSR_Ref TObjectID="6381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-39573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -885.000000) translate(0,57)">39573.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39573" ObjectName="CX_QSH.CX_QSH_3IIM:F"/>
     <cge:PSR_Ref ObjectID="6381"/>
     <cge:Term_Ref ObjectID="8992"/>
    <cge:TPSR_Ref TObjectID="6381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-39581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -885.000000) translate(0,72)">39581.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39581" ObjectName="CX_QSH.CX_QSH_3IIM:F"/>
     <cge:PSR_Ref ObjectID="6381"/>
     <cge:Term_Ref ObjectID="8992"/>
    <cge:TPSR_Ref TObjectID="6381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-39566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3466.000000 -874.000000) translate(0,12)">39566.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39566" ObjectName="CX_QSH.CX_QSH_3IM:F"/>
     <cge:PSR_Ref ObjectID="6380"/>
     <cge:Term_Ref ObjectID="8991"/>
    <cge:TPSR_Ref TObjectID="6380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-39567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3466.000000 -874.000000) translate(0,27)">39567.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39567" ObjectName="CX_QSH.CX_QSH_3IM:F"/>
     <cge:PSR_Ref ObjectID="6380"/>
     <cge:Term_Ref ObjectID="8991"/>
    <cge:TPSR_Ref TObjectID="6380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-39568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3466.000000 -874.000000) translate(0,42)">39568.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39568" ObjectName="CX_QSH.CX_QSH_3IM:F"/>
     <cge:PSR_Ref ObjectID="6380"/>
     <cge:Term_Ref ObjectID="8991"/>
    <cge:TPSR_Ref TObjectID="6380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-39569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3466.000000 -874.000000) translate(0,57)">39569.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39569" ObjectName="CX_QSH.CX_QSH_3IM:F"/>
     <cge:PSR_Ref ObjectID="6380"/>
     <cge:Term_Ref ObjectID="8991"/>
    <cge:TPSR_Ref TObjectID="6380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-39580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3466.000000 -874.000000) translate(0,72)">39580.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39580" ObjectName="CX_QSH.CX_QSH_3IM:F"/>
     <cge:PSR_Ref ObjectID="6380"/>
     <cge:Term_Ref ObjectID="8991"/>
    <cge:TPSR_Ref TObjectID="6380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3847.000000 -1308.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6388"/>
     <cge:Term_Ref ObjectID="9003"/>
    <cge:TPSR_Ref TObjectID="6388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3847.000000 -1308.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6388"/>
     <cge:Term_Ref ObjectID="9003"/>
    <cge:TPSR_Ref TObjectID="6388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3847.000000 -1308.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6388"/>
     <cge:Term_Ref ObjectID="9003"/>
    <cge:TPSR_Ref TObjectID="6388"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_QSH"/>
</svg>