<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-5" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2392 -1149 1951 931">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape8">
    
   </symbol>
   <symbol id="dynamicPoint:shape9">
    
   </symbol>
   <symbol id="dynamicPoint:shape10">
    <rect fill="none" height="25" stroke="rgb(0,255,0)" stroke-width="1.2" width="26" x="1" y="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape11">
    <rect fill="none" height="25" stroke="rgb(0,255,0)" stroke-width="1.2" width="26" x="1" y="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape204">
    <rect fill="none" height="18" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape205">
    <rect fill="rgb(0,255,0)" fillStyle="1" height="18" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape206">
    <rect fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape207">
    <rect fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3140ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31dc190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31dcac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31dda80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31deaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31df580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31dfec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_31e0670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26c6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26c6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2784700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2784700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_316c4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_316c4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_316cfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_316ec30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_316f8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3170330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3170a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3172f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3172330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3172970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31e7d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31e8f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31e98f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31ea3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31eada0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31e2760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31e3090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31e4230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31e4e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31e62d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3059b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33e3d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33e0d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="941" width="1961" x="2387" y="-1154"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2411" x2="3051" y1="-915" y2="-915"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2779" x2="2779" y1="-915" y2="-584"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3223" x2="3223" y1="-562" y2="-562"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3700" x2="4340" y1="-916" y2="-916"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4068" x2="4068" y1="-916" y2="-585"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3456" x2="3456" y1="-395" y2="-395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2407" x2="3052" y1="-550" y2="-550"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2407" x2="3052" y1="-511" y2="-511"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2410" x2="3053" y1="-464" y2="-464"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2409" x2="3052" y1="-426" y2="-426"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2410" x2="3053" y1="-384" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2407" x2="3052" y1="-345" y2="-345"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2408" x2="3051" y1="-304" y2="-304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2408" x2="3051" y1="-257" y2="-257"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3696" x2="4341" y1="-551" y2="-551"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3696" x2="4341" y1="-512" y2="-512"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3699" x2="4342" y1="-465" y2="-465"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3698" x2="4341" y1="-427" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3699" x2="4342" y1="-385" y2="-385"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3696" x2="4341" y1="-346" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3697" x2="4340" y1="-305" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3697" x2="4340" y1="-258" y2="-258"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-6329">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3538.000000 -877.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1051" ObjectName="SW-CX_YZ.CX_YZ_361BK"/>
     <cge:Meas_Ref ObjectId="6329"/>
    <cge:TPSR_Ref TObjectID="1051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-6368">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3366.000000 -859.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1065" ObjectName="SW-CX_YZ.CX_YZ_312BK"/>
     <cge:Meas_Ref ObjectId="6368"/>
    <cge:TPSR_Ref TObjectID="1065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-6267">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3215.000000 -834.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1034" ObjectName="SW-CX_YZ.CX_YZ_353BK"/>
     <cge:Meas_Ref ObjectId="6267"/>
    <cge:TPSR_Ref TObjectID="1034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3216.259346 -572.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41472" ObjectName="SW-CX_YZ.CX_YZ_1ATS_11QSBK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="41472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3286.259346 -572.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41473" ObjectName="SW-CX_YZ.CX_YZ_1ATS_12QSBK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="41473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3447.259346 -572.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41474" ObjectName="SW-CX_YZ.CX_YZ_2ATS_21QSBK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="41474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3520.259346 -573.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41475" ObjectName="SW-CX_YZ.CX_YZ_2ATS_22QSBK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="41475"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YZ.CX_YZ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3164,-942 3362,-942 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="919" ObjectName="BS-CX_YZ.CX_YZ_3IM"/>
    <cge:TPSR_Ref TObjectID="919"/></metadata>
   <polyline fill="none" opacity="0" points="3164,-942 3362,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YZ.CX_YZ_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3400,-943 3596,-943 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="920" ObjectName="BS-CX_YZ.CX_YZ_3IIM"/>
    <cge:TPSR_Ref TObjectID="920"/></metadata>
   <polyline fill="none" opacity="0" points="3400,-943 3596,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3200,-426 3328,-426 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3200,-426 3328,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3424,-424 3552,-424 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3424,-424 3552,-424 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e4e2d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3219.000000 -766.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31293f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3523.000000 -765.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 2553.500000 -1071.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5534" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2828.000000 -885.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5534" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5535" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2828.000000 -833.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5535" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5531" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2828.000000 -775.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5531" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5532" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2828.000000 -726.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5532" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5533" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2828.000000 -673.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5533" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5536" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2828.000000 -630.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5536" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5556" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4131.000000 -897.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5556" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5557" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4131.000000 -852.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5557" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5553" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4131.000000 -804.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5553" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5554" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4131.000000 -751.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5554" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5555" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4131.000000 -698.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5555" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5558" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4131.000000 -642.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5558" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178626" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -513.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178626"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178623" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -474.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178623"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178630" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3707.000000 -517.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178630"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178627" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3709.000000 -470.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178627"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178538" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -438.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178538"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178537" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -397.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178537"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178539" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -350.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178539"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178536" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -307.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178536"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178540" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -265.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178540"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178541" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 2422.000000 -223.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178541"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178544" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3709.000000 -432.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178544"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178543" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3709.000000 -392.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178543"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178545" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3709.000000 -348.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178545"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178542" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3709.000000 -303.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178542"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178546" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3709.000000 -262.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178546"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178547" type="0">
    <use transform="matrix(0.903226 -0.000000 0.000000 -0.903226 3709.000000 -221.000000)" xlink:href="#dynamicPoint:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178547"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178624" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3221.000000 -510.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178624"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178625" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3291.000000 -510.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178625"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178628" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.000000 -510.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178628"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-178629" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3525.000000 -511.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="178629"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_31da680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2540.500000 -1116.500000) translate(0,16)">腰站变站用变一次接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3149fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3500.000000 -977.000000) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3144d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3169.000000 -972.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3144f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -731.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3518090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -731.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26e72e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3233.000000 -863.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a9460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3231.000000 -911.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ab4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3436.000000 -912.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34601a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3293.000000 -920.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d8e6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3538.000000 -862.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a9fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -910.000000) translate(0,12)">3612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d9e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3369.000000 -896.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_316de40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3374.000000 -859.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da9d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.259346 -601.000000) translate(0,12)">11QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2daee30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3305.259346 -601.000000) translate(0,12)">12QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2787b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3411.259346 -601.000000) translate(0,12)">21QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3441dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3540.259346 -601.000000) translate(0,12)">22QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e388e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3178.000000 -453.000000) translate(0,15)">380VI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27bb150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3506.000000 -451.000000) translate(0,15)">380VII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3091ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3307.259346 -526.000000) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27974d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3436.259346 -527.000000) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4c870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3198.259346 -524.000000) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e224b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3540.259346 -528.000000) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2d9ee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -536.000000) translate(0,15)">1号站用变380V进线柜1ATS在0位置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_30ac0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -499.000000) translate(0,15)">1号站用变380V进线柜装置Ⅰ故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2d321a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -540.000000) translate(0,15)">2号站用变380V进线柜2ATS在0位置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_33ca630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -493.000000) translate(0,15)">2号站用变380V进线柜装置Ⅱ故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_33ed650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.000000 -574.000000) translate(0,15)">1号站用变信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_33eb750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2698.000000 -945.000000) translate(0,15)">遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3052ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -883.000000) translate(0,15)">1号站用变35kV侧353断路器有功P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_31e10e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2913.000000 -883.000000) translate(0,15)"> MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26b24c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -826.000000) translate(0,15)">1号站用变35kV侧353断路器无功Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2e19f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2913.000000 -831.000000) translate(0,15)"> MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29f8250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -774.000000) translate(0,15)">1号站用变35kV侧353断路器A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2e352f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2913.000000 -773.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2e292f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -723.000000) translate(0,15)">1号站用变35kV侧353断路器B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2e3db20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2913.000000 -725.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27896f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -673.000000) translate(0,15)">1号站用变35kV侧353断路器C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2d9b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2913.000000 -670.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26b8880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -630.000000) translate(0,15)">1号站用变35kV侧353断路器功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_316f270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.000000 -461.000000) translate(0,15)">1号站用变35kV侧353SF6气压低闭锁操作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1b170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -420.000000) translate(0,15)">1号站用变35kV侧353SF6气压低告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_317fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -373.000000) translate(0,15)">1号站用变35kV侧353保护装置异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_30e8020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -330.000000) translate(0,15)">1号站用变35kV侧353弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -288.000000) translate(0,15)">1号站用变35kV侧353控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2dab080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -248.000000) translate(0,15)">1号站用变35kV侧353通讯故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_314ed00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -945.000000) translate(0,15)">遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26e6a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 -802.000000) translate(0,15)">2号站用变35kV侧361断路器A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2d9bd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -895.000000) translate(0,15)"> MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2d44070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 -848.000000) translate(0,15)">2号站用变35kV侧361断路器无功Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2a01270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4216.000000 -848.000000) translate(0,15)"> MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3517b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 -895.000000) translate(0,15)">2号站用变35kV侧361断路器有功P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2741b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4221.000000 -802.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_30e7d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 -749.000000) translate(0,15)">2号站用变35kV侧361断路器B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2794170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -749.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_270d400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 -696.000000) translate(0,15)">2号站用变35kV侧361断路器C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_31901c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4223.000000 -696.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_31110d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 -638.000000) translate(0,15)">2号站用变35kV侧361断路器功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2d3b250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -453.000000) translate(0,15)">2号站用变35kV侧361SF6气压低闭锁操作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27cdd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -413.000000) translate(0,15)">2号站用变35kV侧361SF6气压低告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_316fdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -369.000000) translate(0,15)">2号站用变35kV侧361保护装置异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29e0660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -325.000000) translate(0,15)">2号站用变35kV侧361弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_29e4d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -284.000000) translate(0,15)">2号站用变35kV侧361控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2e19a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -243.000000) translate(0,15)">2号站用变35kV侧通讯故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_31c76d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -575.000000) translate(0,15)">2号站用变信号</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="366" stroke="rgb(0,255,0)" stroke-width="1" width="646" x="3696" y="-584"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="363" stroke="rgb(0,255,0)" stroke-width="1" width="646" x="2407" y="-583"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2393" y="-1148"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="377" stroke="rgb(0,255,0)" stroke-width="1" width="646" x="2407" y="-960"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="377" stroke="rgb(0,255,0)" stroke-width="1" width="645" x="3696" y="-961"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3239.000000 -751.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3239.000000 -751.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3543.000000 -749.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3543.000000 -749.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="920" cx="3528" cy="-942" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="920" cx="3434" cy="-942" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3263" cy="-426" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3487" cy="-424" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="919" cx="3337" cy="-941" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="919" cx="3224" cy="-941" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_220kV_腰站变电站.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="267" x="2503" y="-1127"/></g>
   <g href="楚雄地区_220kV_腰站变电站.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="2462" y="-1144"/></g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3446,-626 3446,-628 3446,-629 3446,-631 3447,-632 3448,-634 3448,-635 3449,-636 3450,-637 3451,-638 3452,-638 3454,-639 3455,-639 3456,-639 3457,-639 3459,-638 3460,-638 3461,-637 3462,-636 3463,-635 3463,-634 3464,-632 3465,-631 3465,-629 3465,-628 3465,-626 " stroke="rgb(0,255,0)" stroke-width="0.255938"/>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2e1ae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3337,-922 3337,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1066@1" ObjectIDZND0="919@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6369_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3337,-922 3337,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f19a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-942 3528,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="920@0" ObjectIDZND0="1052@1" Pin0InfoVect0LinkObjId="SW-6330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3176960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-942 3528,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d5e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-885 3528,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1052@0" ObjectIDZND0="1051@1" Pin0InfoVect0LinkObjId="SW-6329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-885 3528,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-841 3528,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="1051@0" ObjectIDZND0="g_31293f0@0" Pin0InfoVect0LinkObjId="g_31293f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-841 3528,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26b5000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-744 3528,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_31293f0@1" Pin0InfoVect0LinkObjId="g_31293f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4e2d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-744 3528,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31039e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3224,-941 3224,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="919@0" ObjectIDZND0="1035@1" Pin0InfoVect0LinkObjId="SW-6268_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1ae50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3224,-941 3224,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2daf130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3224,-886 3224,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1035@0" ObjectIDZND0="1034@1" Pin0InfoVect0LinkObjId="SW-6267_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6268_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3224,-886 3224,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3176960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3434,-921 3434,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1067@1" ObjectIDZND0="920@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3434,-921 3434,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ff530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3224,-842 3224,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="1034@0" ObjectIDZND0="g_2e4e2d0@0" Pin0InfoVect0LinkObjId="g_2e4e2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6267_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3224,-842 3224,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31d86c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3224,-771 3224,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2e4e2d0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2e4e2d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4e2d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3224,-771 3224,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3151bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3435,-885 3435,-869 3402,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1067@0" ObjectIDZND0="1065@0" Pin0InfoVect0LinkObjId="SW-6368_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3435,-885 3435,-869 3402,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cedc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3375,-869 3337,-869 3337,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1065@1" ObjectIDZND0="1066@0" Pin0InfoVect0LinkObjId="SW-6369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-6368_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3375,-869 3337,-869 3337,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314a160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3234,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3234,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e35cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3456,-608 3456,-654 3456,-657 3225,-657 3225,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="41474@1" ObjectIDZND0="41472@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3456,-608 3456,-654 3456,-657 3225,-657 3225,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d2650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3529,-628 3465,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="41475@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3529,-628 3465,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d5d5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3529,-660 3529,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41475@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3529,-660 3529,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e1ef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3529,-628 3529,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41475@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3529,-628 3529,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26b38c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3446,-626 3297,-626 3295,-626 3295,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41473@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3446,-626 3297,-626 3295,-626 3295,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e09a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3225,-528 3225,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41472@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3225,-528 3225,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3ebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3295,-528 3295,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41473@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3295,-528 3295,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33e5140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3456,-528 3456,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41478@2" ObjectIDZND0="41474@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2818380_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3456,-528 3456,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3440f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3529,-529 3529,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41479@2" ObjectIDZND0="41475@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ca1c0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3529,-529 3529,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26db0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3295,-474 3295,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="busSection" ObjectIDND0="0@0" ObjectIDND1="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e4e2d0_0" Pin1InfoVect1LinkObjId="g_2e4e2d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3295,-474 3295,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ca1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3529,-474 3529,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="41479@x" ObjectIDND1="0@0" ObjectIDND2="41478@x" ObjectIDZND0="41479@2" Pin0InfoVect0LinkObjId="g_2dae6f0_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2dae6f0_0" Pin1InfoVect1LinkObjId="g_2e4e2d0_0" Pin1InfoVect2LinkObjId="g_2818380_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3529,-474 3529,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b73e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3296,-474 3295,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2e4e2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4e2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3296,-474 3295,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dae6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-474 3529,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="switch" EndDevType1="busSection" EndDevType2="switch" ObjectIDND0="41479@x" ObjectIDND1="0@0" ObjectIDND2="41478@x" ObjectIDZND0="41479@x" ObjectIDZND1="0@0" ObjectIDZND2="41478@x" Pin0InfoVect0LinkObjId="g_30ca1c0_0" Pin0InfoVect1LinkObjId="g_2e4e2d0_0" Pin0InfoVect2LinkObjId="g_2818380_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30ca1c0_0" Pin1InfoVect1LinkObjId="g_2e4e2d0_0" Pin1InfoVect2LinkObjId="g_2818380_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-474 3529,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2daaa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3263,-474 3263,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2e4e2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4e2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3263,-474 3263,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28a6300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-474 3487,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="busSection" EndDevType0="busSection" ObjectIDND0="41479@x" ObjectIDND1="41479@x" ObjectIDND2="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2e4e2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30ca1c0_0" Pin1InfoVect1LinkObjId="g_30ca1c0_0" Pin1InfoVect2LinkObjId="g_2e4e2d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-474 3487,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d352b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3529,-474 3487,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="busSection" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="41479@x" ObjectIDND1="41479@x" ObjectIDND2="0@0" ObjectIDZND0="0@0" ObjectIDZND1="41478@x" Pin0InfoVect0LinkObjId="g_2e4e2d0_0" Pin0InfoVect1LinkObjId="g_2818380_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30ca1c0_0" Pin1InfoVect1LinkObjId="g_30ca1c0_0" Pin1InfoVect2LinkObjId="g_2e4e2d0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3529,-474 3487,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2818380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-474 3456,-474 3456,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDND1="41479@x" ObjectIDND2="41479@x" ObjectIDZND0="41478@2" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e4e2d0_0" Pin1InfoVect1LinkObjId="g_30ca1c0_0" Pin1InfoVect2LinkObjId="g_30ca1c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-474 3456,-474 3456,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3107710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3295,-474 3263,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2e4e2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4e2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3295,-474 3263,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2814260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3263,-474 3225,-474 3225,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="busSection" ObjectIDND0="0@0" ObjectIDND1="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e4e2d0_0" Pin1InfoVect1LinkObjId="g_2e4e2d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3263,-474 3225,-474 3225,-510 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-6369">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3328.000000 -881.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1066" ObjectName="SW-CX_YZ.CX_YZ_3121SW"/>
     <cge:Meas_Ref ObjectId="6369"/>
    <cge:TPSR_Ref TObjectID="1066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-6370">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3426.000000 -880.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1067" ObjectName="SW-CX_YZ.CX_YZ_3122SW"/>
     <cge:Meas_Ref ObjectId="6370"/>
    <cge:TPSR_Ref TObjectID="1067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-6268">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3215.000000 -881.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1035" ObjectName="SW-CX_YZ.CX_YZ_3531SW"/>
     <cge:Meas_Ref ObjectId="6268"/>
    <cge:TPSR_Ref TObjectID="1035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-6330">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.000000 -880.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1052" ObjectName="SW-CX_YZ.CX_YZ_3612SW"/>
     <cge:Meas_Ref ObjectId="6330"/>
    <cge:TPSR_Ref TObjectID="1052"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="267" x="2503" y="-1127"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="267" x="2503" y="-1127"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="2462" y="-1144"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="2462" y="-1144"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>