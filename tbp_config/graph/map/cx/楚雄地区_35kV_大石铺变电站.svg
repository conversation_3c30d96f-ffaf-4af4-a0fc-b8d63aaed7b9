<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-30" aopId="2884350" id="thSvg" product="E8000V2" version="1.0" viewBox="3078 -1444 2853 1496">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape27">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504" x1="15" x2="15" y1="33" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="15" x2="54" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="60" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="69" x2="69" y1="74" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="72" x2="72" y1="65" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="75" x2="75" y1="67" y2="71"/>
    <rect height="13" stroke-width="0.424575" width="29" x="31" y="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.206839" x1="37" x2="15" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="15" x2="15" y1="102" y2="110"/>
    <polyline arcFlag="1" points="37,24 37,24 38,24 39,24 39,24 40,25 41,25 41,26 42,26 42,27 42,28 43,28 43,29 43,30 43,31 42,31 42,32 42,33 41,33 41,34 40,34 39,35 39,35 38,35 37,35 37,35 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.303559" x1="25" x2="4" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.303559" x1="25" x2="4" y1="25" y2="25"/>
    <polyline arcFlag="1" points="37,35 37,35 38,35 39,35 39,35 40,36 41,36 41,37 42,37 42,38 42,39 43,39 43,40 43,41 43,42 42,42 42,43 42,44 41,44 41,45 40,45 39,46 39,46 38,46 37,46 37,46 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.787037" x1="37" x2="37" y1="55" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.779167" x1="37" x2="37" y1="24" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.733906" x1="15" x2="15" y1="25" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.353978" x1="48" x2="48" y1="47" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.241011" x1="37" x2="15" y1="14" y2="14"/>
    <polyline arcFlag="1" points="15,102 13,102 11,101 9,101 7,100 6,99 4,98 3,96 2,94 1,93 1,91 1,89 1,87 1,85 2,84 3,82 4,81 6,79 7,78 9,77 11,77 13,76 15,76 16,76 18,77 20,77 22,78 23,79 25,81 26,82 27,84 28,85 28,87 28,89 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="15" x2="28" y1="89" y2="89"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="8" x2="8" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.75" width="18" x="21" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="1" x2="1" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="4" x2="4" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="7" x2="7" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="21" x2="7" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="46" x2="26" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="28" x2="26" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape181">
    <polyline points="5,5 5,7 5,8 5,9 6,10 6,11 7,12 8,13 9,14 10,15 12,16 13,16 15,16 16,17 18,17 19,16 21,16 22,16 24,15 25,14 26,13 27,12 28,11 29,10 29,9 29,8 29,7 29,5 " stroke-width="0.402481"/>
   </symbol>
   <symbol id="load:shape12">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="6" x2="6" y1="25" y2="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,8 6,2 11,8 " stroke-width="1.14286"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape7">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="27" x2="3" y1="10" y2="10"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,18 1,10 13,2 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape27_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape76_0">
    <circle cx="16" cy="58" fillStyle="0" r="16" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,15 22,28 10,28 16,15 16,16 16,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="58" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="58" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="58" y2="63"/>
   </symbol>
   <symbol id="transformer2:shape76_1">
    <circle cx="16" cy="82" fillStyle="0" r="16" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="12" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="78" y2="86"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape53">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="70" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="26" x2="23" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="29" x2="26" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="26" x2="26" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="41" x2="38" y1="78" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="44" x2="41" y1="75" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="41" x2="41" y1="78" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="68" x2="56" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="58" x2="66" y1="96" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="60" x2="64" y1="99" y2="99"/>
    <rect height="27" stroke-width="0.416667" width="14" x="34" y="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="58"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="72" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="78" y2="78"/>
    <ellipse cx="40" cy="78" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="26" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="26" cy="78" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="30" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="81" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="28" y1="81" y2="77"/>
    <circle cx="40" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="41" x2="38" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="44" x2="41" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="41" x2="41" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="62" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="62" x2="62" y1="78" y2="93"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ccdae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ccebc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ccf6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd0470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd0d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd1690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd1ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cd2900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cd3f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cd3f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd5740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd5740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2cd83c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd9dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2cda970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2cdb7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cdbf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdd490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cddcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cde2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cdeca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdf840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ce01c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ce0cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ce1670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ce2ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ce3720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ce4900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ce5570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cf3510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ceb5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2ced020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2ce7480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1506" width="2863" x="3073" y="-1449"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-325 5007,-325 5005,-324 5004,-324 5002,-324 5001,-324 5000,-323 4999,-323 4999,-322 4999,-322 4999,-322 4999,-321 5000,-321 5001,-321 5002,-320 5003,-320 5005,-320 5006,-319 5008,-319 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-319 5007,-319 5005,-318 5004,-318 5002,-318 5001,-318 5000,-317 4999,-317 4999,-316 4999,-316 4999,-316 4999,-315 5000,-315 5001,-315 5002,-314 5003,-314 5005,-314 5006,-313 5008,-313 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-313 5007,-313 5005,-312 5004,-312 5002,-312 5001,-312 5000,-311 4999,-311 4999,-310 4999,-310 4999,-310 4999,-309 5000,-309 5001,-309 5002,-308 5003,-308 5005,-308 5006,-307 5008,-307 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-306 5007,-306 5005,-305 5004,-305 5002,-305 5001,-305 5000,-304 4999,-304 4999,-303 4999,-303 4999,-303 4999,-302 5000,-302 5001,-302 5002,-301 5003,-301 5005,-301 5006,-300 5008,-300 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-300 5007,-300 5005,-299 5004,-299 5002,-299 5001,-299 5000,-298 4999,-298 4999,-297 4999,-297 4999,-297 4999,-296 5000,-296 5001,-296 5002,-295 5003,-295 5005,-295 5006,-294 5008,-294 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-293 5007,-293 5005,-292 5004,-292 5002,-292 5001,-292 5000,-291 4999,-291 4999,-290 4999,-290 4999,-290 4999,-289 5000,-289 5001,-289 5002,-288 5003,-288 5005,-288 5006,-287 5008,-287 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-287 5007,-287 5005,-286 5004,-286 5002,-286 5001,-286 5000,-285 4999,-285 4999,-284 4999,-284 4999,-284 4999,-283 5000,-283 5001,-283 5002,-282 5003,-282 5005,-282 5006,-281 5008,-281 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-281 5007,-281 5005,-280 5004,-280 5002,-280 5001,-280 5000,-279 4999,-279 4999,-278 4999,-278 4999,-278 4999,-277 5000,-277 5001,-277 5002,-276 5003,-276 5005,-276 5006,-275 5008,-275 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4639" x2="4639" y1="-348" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5837" x2="5837" y1="-352" y2="-328"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4957" x2="4957" y1="-272" y2="-218"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4948" x2="4957" y1="-334" y2="-320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4957" x2="4957" y1="-320" y2="-297"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4957" x2="4957" y1="-360" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5006" x2="5006" y1="-379" y2="-336"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4957" x2="5006" y1="-360" y2="-360"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5006" x2="5006" y1="-264" y2="-241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5006" x2="5006" y1="-218" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5006" x2="5050" y1="-250" y2="-250"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5052" x2="5052" y1="-195" y2="-154"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5006" x2="5052" y1="-154" y2="-154"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5006" x2="5006" y1="-275" y2="-259"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5006" x2="5006" y1="-341" y2="-325"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4991" x2="4991" y1="-338" y2="-259"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5044" x2="5051" y1="-218" y2="-211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5048" x2="5058" y1="-223" y2="-213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5050" x2="5050" y1="-249" y2="-222"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5052" x2="5052" y1="-210" y2="-187"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5035" x2="5055" y1="-210" y2="-210"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5049" x2="5035" y1="-226" y2="-211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5066" x2="5050" y1="-223" y2="-223"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5052" x2="5066" y1="-207" y2="-222"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5016" x2="5025" y1="-176" y2="-176"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5027" x2="5027" y1="-179" y2="-173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5029" x2="5029" y1="-178" y2="-174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5025" x2="5025" y1="-180" y2="-172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5060" x2="5069" y1="-175" y2="-175"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5071" x2="5071" y1="-178" y2="-172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5073" x2="5073" y1="-177" y2="-173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5069" x2="5069" y1="-179" y2="-171"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5015" x2="5024" y1="-136" y2="-136"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5026" x2="5026" y1="-139" y2="-133"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5028" x2="5028" y1="-138" y2="-134"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5024" x2="5024" y1="-140" y2="-132"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5884" x2="5884" y1="-783" y2="-759"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-34677">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -1181.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5488" ObjectName="SW-CX_DSP.CX_DSP_388BK"/>
     <cge:Meas_Ref ObjectId="34677"/>
    <cge:TPSR_Ref TObjectID="5488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34798">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -609.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5509" ObjectName="SW-CX_DSP.CX_DSP_083BK"/>
     <cge:Meas_Ref ObjectId="34798"/>
    <cge:TPSR_Ref TObjectID="5509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 -610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5513" ObjectName="SW-CX_DSP.CX_DSP_084BK"/>
     <cge:Meas_Ref ObjectId="34821"/>
    <cge:TPSR_Ref TObjectID="5513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -792.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5489" ObjectName="SW-CX_DSP.CX_DSP_001BK"/>
     <cge:Meas_Ref ObjectId="34679"/>
    <cge:TPSR_Ref TObjectID="5489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -609.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12510" ObjectName="SW-CX_DSP.CX_DSP_086BK"/>
     <cge:Meas_Ref ObjectId="66328"/>
    <cge:TPSR_Ref TObjectID="12510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34844">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -609.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5517" ObjectName="SW-CX_DSP.CX_DSP_085BK"/>
     <cge:Meas_Ref ObjectId="34844"/>
    <cge:TPSR_Ref TObjectID="5517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -614.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5505" ObjectName="SW-CX_DSP.CX_DSP_082BK"/>
     <cge:Meas_Ref ObjectId="34775"/>
    <cge:TPSR_Ref TObjectID="5505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34851">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -646.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5520" ObjectName="SW-CX_DSP.CX_DSP_012BK"/>
     <cge:Meas_Ref ObjectId="34851"/>
    <cge:TPSR_Ref TObjectID="5520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 -610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5501" ObjectName="SW-CX_DSP.CX_DSP_081BK"/>
     <cge:Meas_Ref ObjectId="34752"/>
    <cge:TPSR_Ref TObjectID="5501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80409">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17589" ObjectName="SW-CX_DSP.CX_DSP_071BK"/>
     <cge:Meas_Ref ObjectId="80409"/>
    <cge:TPSR_Ref TObjectID="17589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80436">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.000000 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17593" ObjectName="SW-CX_DSP.CX_DSP_072BK"/>
     <cge:Meas_Ref ObjectId="80436"/>
    <cge:TPSR_Ref TObjectID="17593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5387.000000 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17597" ObjectName="SW-CX_DSP.CX_DSP_073BK"/>
     <cge:Meas_Ref ObjectId="80463"/>
    <cge:TPSR_Ref TObjectID="17597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17601" ObjectName="SW-CX_DSP.CX_DSP_074BK"/>
     <cge:Meas_Ref ObjectId="80490"/>
    <cge:TPSR_Ref TObjectID="17601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80540">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5828.000000 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17608" ObjectName="SW-CX_DSP.CX_DSP_076BK"/>
     <cge:Meas_Ref ObjectId="80540"/>
    <cge:TPSR_Ref TObjectID="17608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -800.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17581" ObjectName="SW-CX_DSP.CX_DSP_002BK"/>
     <cge:Meas_Ref ObjectId="80305"/>
    <cge:TPSR_Ref TObjectID="17581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -1156.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17573" ObjectName="SW-CX_DSP.CX_DSP_387BK"/>
     <cge:Meas_Ref ObjectId="80290"/>
    <cge:TPSR_Ref TObjectID="17573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80369">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.000000 -1074.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17584" ObjectName="SW-CX_DSP.CX_DSP_312BK"/>
     <cge:Meas_Ref ObjectId="80369"/>
    <cge:TPSR_Ref TObjectID="17584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-154219">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -231.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26161" ObjectName="SW-CX_DSP.CX_DSP_412BK"/>
     <cge:Meas_Ref ObjectId="154219"/>
    <cge:TPSR_Ref TObjectID="26161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-154220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -162.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26162" ObjectName="SW-CX_DSP.CX_DSP_401BK"/>
     <cge:Meas_Ref ObjectId="154220"/>
    <cge:TPSR_Ref TObjectID="26162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 -43.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 -128.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 30.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3c76af0">
    <use class="BV-10KV" transform="matrix(1.014706 -0.000000 0.000000 -1.039604 5670.000000 -805.000000)" xlink:href="#voltageTransformer:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d05d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -806.000000)" xlink:href="#voltageTransformer:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362e5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4188.000000 -1356.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362eff0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5451.000000 -1335.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DSP" endPointId="0" endStationName="CX_BLXC" flowDrawDirect="1" flowShape="0" id="AC-35kV.baida_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4164,-1398 4164,-1429 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6573" ObjectName="AC-35kV.baida_line"/>
    <cge:TPSR_Ref TObjectID="6573_SS-30"/></metadata>
   <polyline fill="none" opacity="0" points="4164,-1398 4164,-1429 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_DSP" flowDrawDirect="1" flowShape="0" id="AC-35kV.donglvdaTdsp_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5426,-1380 5426,-1408 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38055" ObjectName="AC-35kV.donglvdaTdsp_line"/>
    <cge:TPSR_Ref TObjectID="38055_SS-30"/></metadata>
   <polyline fill="none" opacity="0" points="5426,-1380 5426,-1408 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_DSP.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5231.000000 -369.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41617" ObjectName="EC-CX_DSP.072Ld"/>
    <cge:TPSR_Ref TObjectID="41617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DSP.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5390.000000 -369.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34487" ObjectName="EC-CX_DSP.073Ld"/>
    <cge:TPSR_Ref TObjectID="34487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DSP.086Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -368.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34486" ObjectName="EC-CX_DSP.086Ld"/>
    <cge:TPSR_Ref TObjectID="34486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3439.000000 30.000000)" xlink:href="#load:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DSP.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -50.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38162" ObjectName="EC-CX_DSP.082Ld"/>
    <cge:TPSR_Ref TObjectID="38162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DSP.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -362.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38165" ObjectName="EC-CX_DSP.083Ld"/>
    <cge:TPSR_Ref TObjectID="38165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DSP.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 -88.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38164" ObjectName="EC-CX_DSP.084Ld"/>
    <cge:TPSR_Ref TObjectID="38164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DSP.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 -152.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38163" ObjectName="EC-CX_DSP.081Ld"/>
    <cge:TPSR_Ref TObjectID="38163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 -401.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3d53800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 3918.000000 -186.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3865780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 4314.000000 -391.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b58010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 3745.000000 -393.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c69f10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 3745.000000 -186.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac8ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 3918.000000 -390.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c56ad0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 4149.000000 -388.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d0c3d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 4485.000000 -416.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c9fa00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 4488.000000 -186.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c54790">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4103.000000 -1373.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cbca70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 3801.000000 -184.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c62b20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 4194.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d2c500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d726e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5232.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d147d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5391.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cd2280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5551.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c3d8b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5688.000000 -601.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c689c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5832.000000 -530.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ccef90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 -527.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c49c90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -599.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cc3f70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5366.000000 -1354.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c5d710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5421.000000 -1271.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d746e0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 3596.000000 -130.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cad310">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4777.000000 -129.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d1b3e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5049.000000 -350.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_362fa20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -551.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3632140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 -540.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3633020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4110.000000 -540.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31655b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -541.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3166330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -544.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3167210">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 4604.000000 -72.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_316c4c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 4340.000000 -206.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_316dcc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5259.000000 -356.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e56190">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5415.000000 -358.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_317aeb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -344.000000)" xlink:href="#lightningRod:shape181"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3192.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78571" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3241.538462 -984.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78571" ObjectName="CX_DSP:CX_DSP_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79819" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3241.538462 -943.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79819" ObjectName="CX_DSP:CX_DSP_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80238" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5559.000000 -941.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80238" ObjectName="CX_DSP:CX_DSP_2T_Tmp"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34618" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4053.000000 -1206.590909) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5488"/>
     <cge:Term_Ref ObjectID="7960"/>
    <cge:TPSR_Ref TObjectID="5488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-34619" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4053.000000 -1206.590909) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5488"/>
     <cge:Term_Ref ObjectID="7960"/>
    <cge:TPSR_Ref TObjectID="5488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34609" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4053.000000 -1206.590909) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5488"/>
     <cge:Term_Ref ObjectID="7960"/>
    <cge:TPSR_Ref TObjectID="5488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-34620" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4053.000000 -1206.590909) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5488"/>
     <cge:Term_Ref ObjectID="7960"/>
    <cge:TPSR_Ref TObjectID="5488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34629" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 3726.000000 -140.590909) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5501"/>
     <cge:Term_Ref ObjectID="7986"/>
    <cge:TPSR_Ref TObjectID="5501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-34630" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 3726.000000 -140.590909) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5501"/>
     <cge:Term_Ref ObjectID="7986"/>
    <cge:TPSR_Ref TObjectID="5501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34631" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 3726.000000 -140.590909) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5501"/>
     <cge:Term_Ref ObjectID="7986"/>
    <cge:TPSR_Ref TObjectID="5501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34644" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 3974.000000 -596.590909) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5513"/>
     <cge:Term_Ref ObjectID="8010"/>
    <cge:TPSR_Ref TObjectID="5513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-34645" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 3974.000000 -596.590909) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5513"/>
     <cge:Term_Ref ObjectID="8010"/>
    <cge:TPSR_Ref TObjectID="5513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34646" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 3974.000000 -596.590909) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5513"/>
     <cge:Term_Ref ObjectID="8010"/>
    <cge:TPSR_Ref TObjectID="5513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34639" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4149.000000 -333.590909) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5509"/>
     <cge:Term_Ref ObjectID="8002"/>
    <cge:TPSR_Ref TObjectID="5509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-34640" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4149.000000 -333.590909) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5509"/>
     <cge:Term_Ref ObjectID="8002"/>
    <cge:TPSR_Ref TObjectID="5509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34641" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4149.000000 -333.590909) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5509"/>
     <cge:Term_Ref ObjectID="8002"/>
    <cge:TPSR_Ref TObjectID="5509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34634" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.823559 -0.000000 -0.000000 0.818182 4536.508772 -611.590909) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5505"/>
     <cge:Term_Ref ObjectID="7994"/>
    <cge:TPSR_Ref TObjectID="5505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-34635" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.823559 -0.000000 -0.000000 0.818182 4536.508772 -611.590909) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5505"/>
     <cge:Term_Ref ObjectID="7994"/>
    <cge:TPSR_Ref TObjectID="5505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34636" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.823559 -0.000000 -0.000000 0.818182 4536.508772 -611.590909) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5505"/>
     <cge:Term_Ref ObjectID="7994"/>
    <cge:TPSR_Ref TObjectID="5505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34656" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4911.000000 -639.590909) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5520"/>
     <cge:Term_Ref ObjectID="8024"/>
    <cge:TPSR_Ref TObjectID="5520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-34657" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4911.000000 -639.590909) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5520"/>
     <cge:Term_Ref ObjectID="8024"/>
    <cge:TPSR_Ref TObjectID="5520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34654" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.885714 -0.000000 -0.000000 0.818182 4911.000000 -639.590909) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5520"/>
     <cge:Term_Ref ObjectID="8024"/>
    <cge:TPSR_Ref TObjectID="5520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-34623" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -850.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9679"/>
     <cge:Term_Ref ObjectID="13765"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-34624" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -850.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9679"/>
     <cge:Term_Ref ObjectID="13765"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-34625" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -850.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9679"/>
     <cge:Term_Ref ObjectID="13765"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-34659" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -850.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9679"/>
     <cge:Term_Ref ObjectID="13765"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-34626" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -850.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9679"/>
     <cge:Term_Ref ObjectID="13765"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-34627" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -850.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9679"/>
     <cge:Term_Ref ObjectID="13765"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-34628" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -850.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9679"/>
     <cge:Term_Ref ObjectID="13765"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-54847" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5900.000000 -832.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54847" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9680"/>
     <cge:Term_Ref ObjectID="13766"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-54848" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5900.000000 -832.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9680"/>
     <cge:Term_Ref ObjectID="13766"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-54849" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5900.000000 -832.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9680"/>
     <cge:Term_Ref ObjectID="13766"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154876" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5900.000000 -832.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9680"/>
     <cge:Term_Ref ObjectID="13766"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-54850" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5900.000000 -832.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54850" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9680"/>
     <cge:Term_Ref ObjectID="13766"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-54851" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5900.000000 -832.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9680"/>
     <cge:Term_Ref ObjectID="13766"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-54852" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5900.000000 -832.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9680"/>
     <cge:Term_Ref ObjectID="13766"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-34675" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4303.000000 -894.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5523"/>
     <cge:Term_Ref ObjectID="8030"/>
    <cge:TPSR_Ref TObjectID="5523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-34621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4303.000000 -894.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5523"/>
     <cge:Term_Ref ObjectID="8030"/>
    <cge:TPSR_Ref TObjectID="5523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66325" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -341.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12510"/>
     <cge:Term_Ref ObjectID="17282"/>
    <cge:TPSR_Ref TObjectID="12510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66326" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -341.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66326" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12510"/>
     <cge:Term_Ref ObjectID="17282"/>
    <cge:TPSR_Ref TObjectID="12510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66322" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -341.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12510"/>
     <cge:Term_Ref ObjectID="17282"/>
    <cge:TPSR_Ref TObjectID="12510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80600" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -227.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5517"/>
     <cge:Term_Ref ObjectID="8020"/>
    <cge:TPSR_Ref TObjectID="5517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80598" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -227.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5517"/>
     <cge:Term_Ref ObjectID="8020"/>
    <cge:TPSR_Ref TObjectID="5517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80249" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5115.000000 -328.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17589"/>
     <cge:Term_Ref ObjectID="24083"/>
    <cge:TPSR_Ref TObjectID="17589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80250" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5115.000000 -328.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17589"/>
     <cge:Term_Ref ObjectID="24083"/>
    <cge:TPSR_Ref TObjectID="17589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80246" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5115.000000 -328.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17589"/>
     <cge:Term_Ref ObjectID="24083"/>
    <cge:TPSR_Ref TObjectID="17589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80255" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5246.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17593"/>
     <cge:Term_Ref ObjectID="24188"/>
    <cge:TPSR_Ref TObjectID="17593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80256" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5246.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17593"/>
     <cge:Term_Ref ObjectID="24188"/>
    <cge:TPSR_Ref TObjectID="17593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80252" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5246.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17593"/>
     <cge:Term_Ref ObjectID="24188"/>
    <cge:TPSR_Ref TObjectID="17593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80261" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5415.000000 -346.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17597"/>
     <cge:Term_Ref ObjectID="24196"/>
    <cge:TPSR_Ref TObjectID="17597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80262" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5415.000000 -346.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17597"/>
     <cge:Term_Ref ObjectID="24196"/>
    <cge:TPSR_Ref TObjectID="17597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80258" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5415.000000 -346.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17597"/>
     <cge:Term_Ref ObjectID="24196"/>
    <cge:TPSR_Ref TObjectID="17597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80267" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5642.000000 -351.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17601"/>
     <cge:Term_Ref ObjectID="24204"/>
    <cge:TPSR_Ref TObjectID="17601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80268" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5642.000000 -351.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17601"/>
     <cge:Term_Ref ObjectID="24204"/>
    <cge:TPSR_Ref TObjectID="17601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80264" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5642.000000 -351.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17601"/>
     <cge:Term_Ref ObjectID="24204"/>
    <cge:TPSR_Ref TObjectID="17601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80274" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5858.000000 -244.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17608"/>
     <cge:Term_Ref ObjectID="24218"/>
    <cge:TPSR_Ref TObjectID="17608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80271" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5858.000000 -244.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17608"/>
     <cge:Term_Ref ObjectID="24218"/>
    <cge:TPSR_Ref TObjectID="17608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80232" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -1209.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17573"/>
     <cge:Term_Ref ObjectID="24051"/>
    <cge:TPSR_Ref TObjectID="17573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80233" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -1209.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17573"/>
     <cge:Term_Ref ObjectID="24051"/>
    <cge:TPSR_Ref TObjectID="17573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80214" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -1209.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17573"/>
     <cge:Term_Ref ObjectID="24051"/>
    <cge:TPSR_Ref TObjectID="17573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-80234" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -1209.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17573"/>
     <cge:Term_Ref ObjectID="24051"/>
    <cge:TPSR_Ref TObjectID="17573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80243" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1154.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17584"/>
     <cge:Term_Ref ObjectID="24073"/>
    <cge:TPSR_Ref TObjectID="17584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80244" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1154.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17584"/>
     <cge:Term_Ref ObjectID="24073"/>
    <cge:TPSR_Ref TObjectID="17584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80240" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -1154.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17584"/>
     <cge:Term_Ref ObjectID="24073"/>
    <cge:TPSR_Ref TObjectID="17584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-80239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5559.000000 -956.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17621"/>
     <cge:Term_Ref ObjectID="24244"/>
    <cge:TPSR_Ref TObjectID="17621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-54844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -832.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5489"/>
     <cge:Term_Ref ObjectID="7962"/>
    <cge:TPSR_Ref TObjectID="5489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-54845" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -832.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54845" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5489"/>
     <cge:Term_Ref ObjectID="7962"/>
    <cge:TPSR_Ref TObjectID="5489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-54835" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -832.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54835" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5489"/>
     <cge:Term_Ref ObjectID="7962"/>
    <cge:TPSR_Ref TObjectID="5489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-54846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -832.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5489"/>
     <cge:Term_Ref ObjectID="7962"/>
    <cge:TPSR_Ref TObjectID="5489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-80235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5548.000000 -844.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17581"/>
     <cge:Term_Ref ObjectID="24067"/>
    <cge:TPSR_Ref TObjectID="17581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-80236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5548.000000 -844.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17581"/>
     <cge:Term_Ref ObjectID="24067"/>
    <cge:TPSR_Ref TObjectID="17581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-80220" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5548.000000 -844.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17581"/>
     <cge:Term_Ref ObjectID="24067"/>
    <cge:TPSR_Ref TObjectID="17581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-80237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5548.000000 -844.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17581"/>
     <cge:Term_Ref ObjectID="24067"/>
    <cge:TPSR_Ref TObjectID="17581"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3204" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3204" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3156" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3156" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4122" y="-1213"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4122" y="-1213"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="5461" y="-990"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="5461" y="-990"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5385" y="-1183"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5385" y="-1183"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4749" y="-1073"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4749" y="-1073"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3720" y="-639"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3720" y="-639"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3893" y="-639"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3893" y="-639"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4124" y="-639"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4124" y="-639"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4290" y="-638"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4290" y="-638"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4460" y="-641"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4460" y="-641"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4648" y="-638"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4648" y="-638"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4913" y="-681"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4913" y="-681"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5094" y="-635"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5094" y="-635"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5246" y="-633"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5246" y="-633"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5405" y="-633"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5405" y="-633"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5565" y="-633"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5565" y="-633"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5846" y="-633"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5846" y="-633"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3355" y="-1159"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3355" y="-1159"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3355" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3355" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="27" qtmmishow="hidden" width="95" x="3106" y="-752"/>
    </a>
   <metadata/><rect fill="white" height="27" opacity="0" stroke="white" transform="" width="95" x="3106" y="-752"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3496" y="-1183"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3496" y="-1183"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4208" y="-923"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4208" y="-923"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="28" qtmmishow="hidden" width="86" x="3108" y="-698"/>
    </a>
   <metadata/><rect fill="white" height="28" opacity="0" stroke="white" transform="" width="86" x="3108" y="-698"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3204" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3156" y="-1194"/></g>
   <g href="35kV大石铺变35kV白大线388断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4122" y="-1213"/></g>
   <g href="35kV大石铺变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="5461" y="-990"/></g>
   <g href="35kV大石铺变2号主变387断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5385" y="-1183"/></g>
   <g href="35kV大石铺变35kV内桥312断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4749" y="-1073"/></g>
   <g href="35kV大石铺变10kV燃二厂Ⅰ回线081断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3720" y="-639"/></g>
   <g href="35kV大石铺变10kV燃二厂Ⅳ回线084断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3893" y="-639"/></g>
   <g href="35kV大石铺变10kV紫溪Ⅰ回线083断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4124" y="-639"/></g>
   <g href="35kV大石铺变10kV紫溪集镇线086断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4290" y="-638"/></g>
   <g href="35kV大石铺变10kV燃二厂Ⅱ回线082断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4460" y="-641"/></g>
   <g href="35kV大石铺变10kV1号电容器085断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4648" y="-638"/></g>
   <g href="35kV大石铺变10kV分段012断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4913" y="-681"/></g>
   <g href="35kV大石铺变10kV1号接地变071断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5094" y="-635"/></g>
   <g href="35kV大石铺变10kV紫溪Ⅱ回线072断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5246" y="-633"/></g>
   <g href="35kV大石铺变10kV紫溪集镇Ⅱ回线073断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5405" y="-633"/></g>
   <g href="35kV大石铺变10kV备用四074断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5565" y="-633"/></g>
   <g href="35kV大石铺变10kV2号电容器076断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5846" y="-633"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3355" y="-1159"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3355" y="-1194"/></g>
   <g href="35kV大石铺变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="27" qtmmishow="hidden" width="95" x="3106" y="-752"/></g>
   <g href="AVC大石铺站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3496" y="-1183"/></g>
   <g href="35kV大石铺变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4208" y="-923"/></g>
   <g href="35kV大石铺变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="28" qtmmishow="hidden" width="86" x="3108" y="-698"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c52530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 231.000000) translate(0,12)">Q(MVA):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0e700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 213.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d10ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5794.000000 245.000000) translate(0,12)">Q(MVA):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d10e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5808.000000 227.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d11820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4710.000000 1154.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d12180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 1139.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d12740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 1124.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6f780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 1192.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6f9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 1177.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6fc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 1161.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c706f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 1207.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d61200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5498.000000 1195.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d616f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5523.000000 1180.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d61930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5528.000000 1164.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d61b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5509.000000 1210.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4b8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 818.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4bb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 803.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4bd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 787.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4bfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 833.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4c770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5480.000000 831.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4c990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5505.000000 816.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4cbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5510.000000 800.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4ce10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5491.000000 846.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4962" x2="4951" y1="215" y2="215"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4961" x2="4953" y1="212" y2="212"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4964" x2="4949" y1="218" y2="218"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4962" x2="4951" y1="215" y2="215"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5012" x2="5001" y1="105" y2="105"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5011" x2="5003" y1="102" y2="102"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5014" x2="4999" y1="108" y2="108"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="5012" x2="5001" y1="105" y2="105"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4994" x2="5007" y1="343" y2="335"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4995" x2="5030" y1="341" y2="305"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4994" x2="5001" y1="343" y2="330"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4994" x2="5007" y1="343" y2="335"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.750000 133.000000 -98.000000)">
    <polyline DF8003:Layer="PUBLIC" fill="none" points="4820,206 4818,207 4816,208 4815,208 4813,209 4812,210 4812,211 4812,212 4812,213 4812,214 4813,215 4814,216 4816,217 4817,217 4819,218 4822,219 4824,219 4827,220 " stroke="rgb(0,255,0)" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,206 4819,205 4817,204 4816,204 4814,203 4813,202 4813,201 4813,200 4813,199 4813,198 4814,197 4815,196 4817,195 4818,195 4820,194 4823,193 4825,193 4828,192 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <metadata/><polyline fill="none" opacity="0" points="4820,206 4818,207 4816,208 4815,208 4813,209 4812,210 4812,211 4812,212 4812,213 4812,214 4813,215 4814,216 4816,217 4817,217 4819,218 4822,219 4824,219 4827,220 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5a620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 143.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5bc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 128.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5cc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3688.000000 113.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5d5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 598.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3913.000000 583.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5daa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 568.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5ddd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 333.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5e030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 318.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5e270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 303.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5e5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 340.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5e800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4226.000000 325.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5ea40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 310.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.929825 -0.000000 0.000000 -1.000000 438.771930 41.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5ed70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 654.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 639.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5f250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 624.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5f580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 642.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5f7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4848.000000 627.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5fa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 612.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5fd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.000000 331.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e5ffb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 316.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e601f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.000000 301.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e60520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 349.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e60780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5183.000000 334.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e609c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5208.000000 319.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e60cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5369.000000 349.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e60f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5358.000000 334.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e61190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5383.000000 319.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e614c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5586.000000 352.000000) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e61720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5575.000000 337.000000) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e61960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5600.000000 322.000000) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30d0040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 1439.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4193" cy="1432" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30d06f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5388.000000 1409.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="5395" cy="1402" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-34690">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -1091.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5493" ObjectName="SW-CX_DSP.CX_DSP_3881SW"/>
     <cge:Meas_Ref ObjectId="34690"/>
    <cge:TPSR_Ref TObjectID="5493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -1268.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17615" ObjectName="SW-CX_DSP.CX_DSP_3886SW"/>
     <cge:Meas_Ref ObjectId="80568"/>
    <cge:TPSR_Ref TObjectID="17615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34802">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -658.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5510" ObjectName="SW-CX_DSP.CX_DSP_0831SW"/>
     <cge:Meas_Ref ObjectId="34802"/>
    <cge:TPSR_Ref TObjectID="5510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5511" ObjectName="SW-CX_DSP.CX_DSP_0836SW"/>
     <cge:Meas_Ref ObjectId="34803"/>
    <cge:TPSR_Ref TObjectID="5511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34825">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 -659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5514" ObjectName="SW-CX_DSP.CX_DSP_0841SW"/>
     <cge:Meas_Ref ObjectId="34825"/>
    <cge:TPSR_Ref TObjectID="5514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34826">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 -476.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5515" ObjectName="SW-CX_DSP.CX_DSP_0846SW"/>
     <cge:Meas_Ref ObjectId="34826"/>
    <cge:TPSR_Ref TObjectID="5515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -738.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5495" ObjectName="SW-CX_DSP.CX_DSP_0011SW"/>
     <cge:Meas_Ref ObjectId="34692"/>
    <cge:TPSR_Ref TObjectID="5495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -658.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12511" ObjectName="SW-CX_DSP.CX_DSP_0861SW"/>
     <cge:Meas_Ref ObjectId="66337"/>
    <cge:TPSR_Ref TObjectID="12511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66329">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -488.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12512" ObjectName="SW-CX_DSP.CX_DSP_0866SW"/>
     <cge:Meas_Ref ObjectId="66329"/>
    <cge:TPSR_Ref TObjectID="12512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80604">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5518" ObjectName="SW-CX_DSP.CX_DSP_0851SW"/>
     <cge:Meas_Ref ObjectId="80604"/>
    <cge:TPSR_Ref TObjectID="5518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80517">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17605" ObjectName="SW-CX_DSP.CX_DSP_0856SW"/>
     <cge:Meas_Ref ObjectId="80517"/>
    <cge:TPSR_Ref TObjectID="17605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -664.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5506" ObjectName="SW-CX_DSP.CX_DSP_0821SW"/>
     <cge:Meas_Ref ObjectId="34779"/>
    <cge:TPSR_Ref TObjectID="5506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34780">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -503.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5507" ObjectName="SW-CX_DSP.CX_DSP_0826SW"/>
     <cge:Meas_Ref ObjectId="34780"/>
    <cge:TPSR_Ref TObjectID="5507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34858">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.000000 -666.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5521" ObjectName="SW-CX_DSP.CX_DSP_0121SW"/>
     <cge:Meas_Ref ObjectId="34858"/>
    <cge:TPSR_Ref TObjectID="5521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34859">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.377778 -662.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5522" ObjectName="SW-CX_DSP.CX_DSP_0122SW"/>
     <cge:Meas_Ref ObjectId="34859"/>
    <cge:TPSR_Ref TObjectID="5522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 -666.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5502" ObjectName="SW-CX_DSP.CX_DSP_0811SW"/>
     <cge:Meas_Ref ObjectId="34756"/>
    <cge:TPSR_Ref TObjectID="5502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34757">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5503" ObjectName="SW-CX_DSP.CX_DSP_0816SW"/>
     <cge:Meas_Ref ObjectId="34757"/>
    <cge:TPSR_Ref TObjectID="5503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218946">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34076" ObjectName="SW-CX_DSP.CX_DSP_4423SW"/>
     <cge:Meas_Ref ObjectId="218946"/>
    <cge:TPSR_Ref TObjectID="34076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34077" ObjectName="SW-CX_DSP.CX_DSP_4425SW"/>
     <cge:Meas_Ref ObjectId="218947"/>
    <cge:TPSR_Ref TObjectID="34077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34827">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3913.000000 -281.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5516" ObjectName="SW-CX_DSP.CX_DSP_4445SW"/>
     <cge:Meas_Ref ObjectId="34827"/>
    <cge:TPSR_Ref TObjectID="5516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218945">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34082" ObjectName="SW-CX_DSP.CX_DSP_4473SW"/>
     <cge:Meas_Ref ObjectId="218945"/>
    <cge:TPSR_Ref TObjectID="34082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -746.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5500" ObjectName="SW-CX_DSP.CX_DSP_0901SW"/>
     <cge:Meas_Ref ObjectId="34734"/>
    <cge:TPSR_Ref TObjectID="5500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-154222">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.000000 -236.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26164" ObjectName="SW-CX_DSP.CX_DSP_4122SW"/>
     <cge:Meas_Ref ObjectId="154222"/>
    <cge:TPSR_Ref TObjectID="26164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80573">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -949.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17620" ObjectName="SW-CX_DSP.CX_DSP_30117SW"/>
     <cge:Meas_Ref ObjectId="80573"/>
    <cge:TPSR_Ref TObjectID="17620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80571">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -1154.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17618" ObjectName="SW-CX_DSP.CX_DSP_38817SW"/>
     <cge:Meas_Ref ObjectId="80571"/>
    <cge:TPSR_Ref TObjectID="17618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80570">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -1240.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17617" ObjectName="SW-CX_DSP.CX_DSP_38860SW"/>
     <cge:Meas_Ref ObjectId="80570"/>
    <cge:TPSR_Ref TObjectID="17617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80569">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -1319.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17616" ObjectName="SW-CX_DSP.CX_DSP_38867SW"/>
     <cge:Meas_Ref ObjectId="80569"/>
    <cge:TPSR_Ref TObjectID="17616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80410">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17590" ObjectName="SW-CX_DSP.CX_DSP_0712SW"/>
     <cge:Meas_Ref ObjectId="80410"/>
    <cge:TPSR_Ref TObjectID="17590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80411">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -541.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17591" ObjectName="SW-CX_DSP.CX_DSP_0713SW"/>
     <cge:Meas_Ref ObjectId="80411"/>
    <cge:TPSR_Ref TObjectID="17591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80437">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17594" ObjectName="SW-CX_DSP.CX_DSP_0722SW"/>
     <cge:Meas_Ref ObjectId="80437"/>
    <cge:TPSR_Ref TObjectID="17594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80438">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.000000 -550.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17595" ObjectName="SW-CX_DSP.CX_DSP_0723SW"/>
     <cge:Meas_Ref ObjectId="80438"/>
    <cge:TPSR_Ref TObjectID="17595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80439">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.000000 -421.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17596" ObjectName="SW-CX_DSP.CX_DSP_0726SW"/>
     <cge:Meas_Ref ObjectId="80439"/>
    <cge:TPSR_Ref TObjectID="17596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5387.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17598" ObjectName="SW-CX_DSP.CX_DSP_0732SW"/>
     <cge:Meas_Ref ObjectId="80464"/>
    <cge:TPSR_Ref TObjectID="17598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5387.000000 -550.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17599" ObjectName="SW-CX_DSP.CX_DSP_0733SW"/>
     <cge:Meas_Ref ObjectId="80465"/>
    <cge:TPSR_Ref TObjectID="17599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5387.000000 -421.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17600" ObjectName="SW-CX_DSP.CX_DSP_0736SW"/>
     <cge:Meas_Ref ObjectId="80466"/>
    <cge:TPSR_Ref TObjectID="17600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17602" ObjectName="SW-CX_DSP.CX_DSP_0742SW"/>
     <cge:Meas_Ref ObjectId="80491"/>
    <cge:TPSR_Ref TObjectID="17602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -550.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17603" ObjectName="SW-CX_DSP.CX_DSP_0743SW"/>
     <cge:Meas_Ref ObjectId="80492"/>
    <cge:TPSR_Ref TObjectID="17603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5547.000000 -421.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17604" ObjectName="SW-CX_DSP.CX_DSP_0746SW"/>
     <cge:Meas_Ref ObjectId="80493"/>
    <cge:TPSR_Ref TObjectID="17604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5688.000000 -655.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17613" ObjectName="SW-CX_DSP.CX_DSP_0752SW"/>
     <cge:Meas_Ref ObjectId="80566"/>
    <cge:TPSR_Ref TObjectID="17613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80541">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5828.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17609" ObjectName="SW-CX_DSP.CX_DSP_0762SW"/>
     <cge:Meas_Ref ObjectId="80541"/>
    <cge:TPSR_Ref TObjectID="17609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80542">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5828.000000 -475.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17610" ObjectName="SW-CX_DSP.CX_DSP_0766SW"/>
     <cge:Meas_Ref ObjectId="80542"/>
    <cge:TPSR_Ref TObjectID="17610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80544">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5828.000000 -278.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17612" ObjectName="SW-CX_DSP.CX_DSP_07660SW"/>
     <cge:Meas_Ref ObjectId="80544"/>
    <cge:TPSR_Ref TObjectID="17612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80567">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5713.000000 -642.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17614" ObjectName="SW-CX_DSP.CX_DSP_07527SW"/>
     <cge:Meas_Ref ObjectId="80567"/>
    <cge:TPSR_Ref TObjectID="17614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80543">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5847.000000 -461.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17611" ObjectName="SW-CX_DSP.CX_DSP_07667SW"/>
     <cge:Meas_Ref ObjectId="80543"/>
    <cge:TPSR_Ref TObjectID="17611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80519">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -275.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17607" ObjectName="SW-CX_DSP.CX_DSP_08560SW"/>
     <cge:Meas_Ref ObjectId="80519"/>
    <cge:TPSR_Ref TObjectID="17607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80518">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -458.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17606" ObjectName="SW-CX_DSP.CX_DSP_08567SW"/>
     <cge:Meas_Ref ObjectId="80518"/>
    <cge:TPSR_Ref TObjectID="17606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-34727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -653.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5499" ObjectName="SW-CX_DSP.CX_DSP_0871SW"/>
     <cge:Meas_Ref ObjectId="34727"/>
    <cge:TPSR_Ref TObjectID="5499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -849.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17583" ObjectName="SW-CX_DSP.CX_DSP_0026SW"/>
     <cge:Meas_Ref ObjectId="80307"/>
    <cge:TPSR_Ref TObjectID="17583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -742.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17582" ObjectName="SW-CX_DSP.CX_DSP_0022SW"/>
     <cge:Meas_Ref ObjectId="80306"/>
    <cge:TPSR_Ref TObjectID="17582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -1093.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17575" ObjectName="SW-CX_DSP.CX_DSP_3872SW"/>
     <cge:Meas_Ref ObjectId="80292"/>
    <cge:TPSR_Ref TObjectID="17575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80291">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -1207.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17574" ObjectName="SW-CX_DSP.CX_DSP_3876SW"/>
     <cge:Meas_Ref ObjectId="80291"/>
    <cge:TPSR_Ref TObjectID="17574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5417.000000 -1071.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17576" ObjectName="SW-CX_DSP.CX_DSP_3022SW"/>
     <cge:Meas_Ref ObjectId="80293"/>
    <cge:TPSR_Ref TObjectID="17576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5361.000000 -1007.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17580" ObjectName="SW-CX_DSP.CX_DSP_30227SW"/>
     <cge:Meas_Ref ObjectId="80304"/>
    <cge:TPSR_Ref TObjectID="17580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5438.000000 -1140.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17579" ObjectName="SW-CX_DSP.CX_DSP_38727SW"/>
     <cge:Meas_Ref ObjectId="80296"/>
    <cge:TPSR_Ref TObjectID="17579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5438.000000 -1197.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17578" ObjectName="SW-CX_DSP.CX_DSP_38760SW"/>
     <cge:Meas_Ref ObjectId="80295"/>
    <cge:TPSR_Ref TObjectID="17578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5438.000000 -1258.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17577" ObjectName="SW-CX_DSP.CX_DSP_38767SW"/>
     <cge:Meas_Ref ObjectId="80294"/>
    <cge:TPSR_Ref TObjectID="17577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80287">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5702.000000 -748.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17572" ObjectName="SW-CX_DSP.CX_DSP_0902SW"/>
     <cge:Meas_Ref ObjectId="80287"/>
    <cge:TPSR_Ref TObjectID="17572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80370">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4652.000000 -1079.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17585" ObjectName="SW-CX_DSP.CX_DSP_3121SW"/>
     <cge:Meas_Ref ObjectId="80370"/>
    <cge:TPSR_Ref TObjectID="17585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80372">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -1079.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17586" ObjectName="SW-CX_DSP.CX_DSP_3122SW"/>
     <cge:Meas_Ref ObjectId="80372"/>
    <cge:TPSR_Ref TObjectID="17586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80375">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -1036.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17588" ObjectName="SW-CX_DSP.CX_DSP_31227SW"/>
     <cge:Meas_Ref ObjectId="80375"/>
    <cge:TPSR_Ref TObjectID="17588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80374">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4693.000000 -1035.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17587" ObjectName="SW-CX_DSP.CX_DSP_31217SW"/>
     <cge:Meas_Ref ObjectId="80374"/>
    <cge:TPSR_Ref TObjectID="17587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-154223">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 -167.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26165" ObjectName="SW-CX_DSP.CX_DSP_4011SW"/>
     <cge:Meas_Ref ObjectId="154223"/>
    <cge:TPSR_Ref TObjectID="26165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80572">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -996.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17619" ObjectName="SW-CX_DSP.CX_DSP_3011SW"/>
     <cge:Meas_Ref ObjectId="80572"/>
    <cge:TPSR_Ref TObjectID="17619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-175192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -362.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27359" ObjectName="SW-CX_DSP.CX_DSP_4472SW"/>
     <cge:Meas_Ref ObjectId="175192"/>
    <cge:TPSR_Ref TObjectID="27359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 25.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 25.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3738.000000 25.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 7.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4997.000000 -370.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17592" ObjectName="SW-CX_DSP.CX_DSP_0030SW"/>
     <cge:Meas_Ref ObjectId="80412"/>
    <cge:TPSR_Ref TObjectID="17592"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_DSP.CX_DSP_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4624.000000 -342.000000)" xlink:href="#capacitor:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17672" ObjectName="CB-CX_DSP.CX_DSP_1C"/>
    <cge:TPSR_Ref TObjectID="17672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_DSP.CX_DSP_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5822.000000 -345.000000)" xlink:href="#capacitor:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17673" ObjectName="CB-CX_DSP.CX_DSP_2C"/>
    <cge:TPSR_Ref TObjectID="17673"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2cdb190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-664 4115,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5510@1" ObjectIDZND0="5509@1" Pin0InfoVect0LinkObjId="SW-34798_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-664 4115,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bf7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-665 3884,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5514@1" ObjectIDZND0="5513@1" Pin0InfoVect0LinkObjId="SW-34821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34825_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-665 3884,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29faa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-721 4281,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9679@0" ObjectIDZND0="12511@0" Pin0InfoVect0LinkObjId="SW-66337_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-721 4281,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aadf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-663 4281,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12511@1" ObjectIDZND0="12510@1" Pin0InfoVect0LinkObjId="SW-66328_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-663 4281,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bf1030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-721 4639,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9679@0" ObjectIDZND0="5518@0" Pin0InfoVect0LinkObjId="SW-80604_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-721 4639,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c88f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-664 4639,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5518@1" ObjectIDZND0="5517@1" Pin0InfoVect0LinkObjId="SW-34844_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-664 4639,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35befd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-721 4452,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9679@0" ObjectIDZND0="5506@0" Pin0InfoVect0LinkObjId="SW-34779_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-721 4452,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b54810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-669 4452,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5506@1" ObjectIDZND0="5505@1" Pin0InfoVect0LinkObjId="SW-34775_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34779_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-669 4452,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35b4930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4861,-721 4861,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9679@0" ObjectIDZND0="5521@0" Pin0InfoVect0LinkObjId="SW-34858_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4861,-721 4861,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37715f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-703 4993,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5522@0" ObjectIDZND0="9680@0" Pin0InfoVect0LinkObjId="g_3ccc700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-703 4993,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35e21b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-460 3752,-460 3752,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34076@x" ObjectIDND1="5503@x" ObjectIDZND0="g_3b58010@0" Pin0InfoVect0LinkObjId="g_3b58010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218946_0" Pin1InfoVect1LinkObjId="SW-34757_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-460 3752,-460 3752,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d184c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-138 4529,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-138 4529,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_376e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-827 4163,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="5489@1" ObjectIDZND0="5523@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-827 4163,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d181f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-721 4163,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9679@0" ObjectIDZND0="5495@1" Pin0InfoVect0LinkObjId="SW-34692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-721 4163,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d226e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-779 4163,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5495@0" ObjectIDZND0="5489@0" Pin0InfoVect0LinkObjId="SW-34679_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-779 4163,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d023e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-645 3711,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5501@1" ObjectIDZND0="5502@1" Pin0InfoVect0LinkObjId="SW-34756_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34752_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-645 3711,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c3b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-707 3711,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5502@0" ObjectIDZND0="9679@0" Pin0InfoVect0LinkObjId="g_3d0bf30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34756_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-707 3711,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_388c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3752,-240 3752,-257 3711,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3c69f10@0" ObjectIDZND0="34076@x" ObjectIDZND1="34077@x" ObjectIDZND2="38163@x" Pin0InfoVect0LinkObjId="SW-218946_0" Pin0InfoVect1LinkObjId="SW-218947_0" Pin0InfoVect2LinkObjId="EC-CX_DSP.081Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c69f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3752,-240 3752,-257 3711,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d53180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-386 3711,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34076@0" ObjectIDZND0="g_3b58010@0" ObjectIDZND1="5503@x" Pin0InfoVect0LinkObjId="g_3b58010_0" Pin0InfoVect1LinkObjId="SW-34757_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-386 3711,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d73200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-339 3711,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c69f10@0" ObjectIDND1="38163@x" ObjectIDND2="34077@x" ObjectIDZND0="34076@1" Pin0InfoVect0LinkObjId="SW-218946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c69f10_0" Pin1InfoVect1LinkObjId="EC-CX_DSP.081Ld_0" Pin1InfoVect2LinkObjId="SW-218947_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-339 3711,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b00f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-339 3749,-339 3749,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c69f10@0" ObjectIDND1="38163@x" ObjectIDND2="34076@x" ObjectIDZND0="34077@0" Pin0InfoVect0LinkObjId="SW-218947_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c69f10_0" Pin1InfoVect1LinkObjId="EC-CX_DSP.081Ld_0" Pin1InfoVect2LinkObjId="SW-218946_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-339 3749,-339 3749,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b93b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-294 3749,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34077@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218947_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-294 3749,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bffbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-179 3711,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38163@0" ObjectIDZND0="34076@x" ObjectIDZND1="34077@x" ObjectIDZND2="g_3c69f10@0" Pin0InfoVect0LinkObjId="SW-218946_0" Pin0InfoVect1LinkObjId="SW-218947_0" Pin0InfoVect2LinkObjId="g_3c69f10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DSP.081Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-179 3711,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-257 3711,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c69f10@0" ObjectIDND1="38163@x" ObjectIDZND0="34076@x" ObjectIDZND1="34077@x" Pin0InfoVect0LinkObjId="SW-218946_0" Pin0InfoVect1LinkObjId="SW-218947_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c69f10_0" Pin1InfoVect1LinkObjId="EC-CX_DSP.081Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-257 3711,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d67860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-457 3925,-457 3925,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5515@x" ObjectIDND1="0@x" ObjectIDZND0="g_3ac8ef0@0" Pin0InfoVect0LinkObjId="g_3ac8ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-34826_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-457 3925,-457 3925,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3828150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3922,-288 3922,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5516@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34827_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3922,-288 3922,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c73f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-455 4156,-455 4156,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="5511@x" ObjectIDND1="38165@x" ObjectIDZND0="g_3c56ad0@0" Pin0InfoVect0LinkObjId="g_3c56ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-34803_0" Pin1InfoVect1LinkObjId="EC-CX_DSP.083Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-455 4156,-455 4156,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c30530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-389 4115,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="38165@0" ObjectIDZND0="g_3c56ad0@0" ObjectIDZND1="5511@x" Pin0InfoVect0LinkObjId="g_3c56ad0_0" Pin0InfoVect1LinkObjId="SW-34803_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DSP.083Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-389 4115,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30c3c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-483 4492,-483 4492,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27359@x" ObjectIDND1="5507@x" ObjectIDZND0="g_3d0c3d0@0" Pin0InfoVect0LinkObjId="g_3d0c3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-175192_0" Pin1InfoVect1LinkObjId="SW-34780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-483 4492,-483 4492,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d7a490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-667 4993,-656 4940,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5522@1" ObjectIDZND0="5520@0" Pin0InfoVect0LinkObjId="SW-34851_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-667 4993,-656 4940,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c03bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-656 4861,-656 4861,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5520@1" ObjectIDZND0="5521@1" Pin0InfoVect0LinkObjId="SW-34858_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-656 4861,-656 4861,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_383dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-428 3884,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3ac8ef0@0" ObjectIDZND1="5515@x" Pin0InfoVect0LinkObjId="g_3ac8ef0_0" Pin0InfoVect1LinkObjId="SW-34826_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-428 3884,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c89db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-445 4321,-458 4281,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3865780@0" ObjectIDZND0="12512@x" ObjectIDZND1="34486@x" Pin0InfoVect0LinkObjId="SW-66329_0" Pin0InfoVect1LinkObjId="EC-CX_DSP.086Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3865780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-445 4321,-458 4281,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac84f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-458 4281,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3865780@0" ObjectIDND1="12512@x" ObjectIDZND0="34486@0" Pin0InfoVect0LinkObjId="EC-CX_DSP.086Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3865780_0" Pin1InfoVect1LinkObjId="SW-66329_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-458 4281,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d5c2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4531,-426 4531,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="27359@x" ObjectIDZND1="g_3d0c3d0@0" ObjectIDZND2="5507@x" Pin0InfoVect0LinkObjId="SW-175192_0" Pin0InfoVect1LinkObjId="g_3d0c3d0_0" Pin0InfoVect2LinkObjId="SW-34780_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4531,-426 4531,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d5c4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4531,-412 4531,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="27359@x" ObjectIDND1="g_3d0c3d0@0" ObjectIDND2="5507@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-175192_0" Pin1InfoVect1LinkObjId="g_3d0c3d0_0" Pin1InfoVect2LinkObjId="SW-34780_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4531,-412 4531,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d0c870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3925,-240 3925,-257 3884,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_3d53800@0" ObjectIDZND0="26165@x" ObjectIDZND1="38164@x" ObjectIDZND2="5516@x" Pin0InfoVect0LinkObjId="SW-154223_0" Pin0InfoVect1LinkObjId="EC-CX_DSP.084Ld_0" Pin0InfoVect2LinkObjId="SW-34827_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d53800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3925,-240 3925,-257 3884,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d0bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-700 3884,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5514@0" ObjectIDZND0="9679@0" Pin0InfoVect0LinkObjId="g_3c3b770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34825_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-700 3884,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d858d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-721 4558,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9679@0" ObjectIDZND0="5500@1" Pin0InfoVect0LinkObjId="SW-34734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-721 4558,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c54530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-787 4558,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="5500@0" ObjectIDZND0="g_3d05d70@0" Pin0InfoVect0LinkObjId="g_3d05d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-787 4558,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b56ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-238 3808,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3cbca70@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cbca70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-238 3808,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b7e760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4201,-236 4201,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3c62b20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c62b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4201,-236 4201,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b3d330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4149,-956 4164,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="17620@0" ObjectIDZND0="17619@x" ObjectIDZND1="5523@x" Pin0InfoVect0LinkObjId="SW-80572_0" Pin0InfoVect1LinkObjId="g_376e6d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80573_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4149,-956 4164,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b3d520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-1247 4164,-1247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17617@0" ObjectIDZND0="17615@x" ObjectIDZND1="5488@x" Pin0InfoVect0LinkObjId="SW-80568_0" Pin0InfoVect1LinkObjId="SW-34677_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-1247 4164,-1247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c72a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-721 5086,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9680@0" ObjectIDZND0="17590@1" Pin0InfoVect0LinkObjId="SW-80410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37715f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-721 5086,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c38340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-662 5086,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17590@0" ObjectIDZND0="17589@1" Pin0InfoVect0LinkObjId="SW-80409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-662 5086,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d2c2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-612 5086,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17589@0" ObjectIDZND0="17591@1" Pin0InfoVect0LinkObjId="SW-80411_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-612 5086,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d71cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-546 5086,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17591@0" ObjectIDZND0="g_3d2c500@1" Pin0InfoVect0LinkObjId="g_3d2c500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-546 5086,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c88630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5237,-721 5237,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9680@0" ObjectIDZND0="17594@1" Pin0InfoVect0LinkObjId="SW-80437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37715f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5237,-721 5237,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d36cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5237,-662 5237,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17594@0" ObjectIDZND0="17593@1" Pin0InfoVect0LinkObjId="SW-80436_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5237,-662 5237,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d72480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5237,-613 5237,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17593@0" ObjectIDZND0="17595@1" Pin0InfoVect0LinkObjId="SW-80438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5237,-613 5237,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cc6cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5237,-555 5237,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17595@0" ObjectIDZND0="g_3d726e0@1" Pin0InfoVect0LinkObjId="g_3d726e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5237,-555 5237,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d08ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5237,-481 5237,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d726e0@0" ObjectIDZND0="17596@1" Pin0InfoVect0LinkObjId="SW-80439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d726e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5237,-481 5237,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b25a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5396,-721 5396,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9680@0" ObjectIDZND0="17598@1" Pin0InfoVect0LinkObjId="SW-80464_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37715f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5396,-721 5396,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d7b960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5396,-662 5396,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17598@0" ObjectIDZND0="17597@1" Pin0InfoVect0LinkObjId="SW-80463_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80464_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5396,-662 5396,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d14570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5396,-612 5396,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17597@0" ObjectIDZND0="17599@1" Pin0InfoVect0LinkObjId="SW-80465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5396,-612 5396,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c73ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5396,-555 5396,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17599@0" ObjectIDZND0="g_3d147d0@1" Pin0InfoVect0LinkObjId="g_3d147d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5396,-555 5396,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c9b620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5396,-481 5396,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d147d0@0" ObjectIDZND0="17600@1" Pin0InfoVect0LinkObjId="SW-80466_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d147d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5396,-481 5396,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d161e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-721 5556,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9680@0" ObjectIDZND0="17602@1" Pin0InfoVect0LinkObjId="SW-80491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37715f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-721 5556,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-662 5556,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17602@0" ObjectIDZND0="17601@1" Pin0InfoVect0LinkObjId="SW-80490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-662 5556,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c48d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-612 5556,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17601@0" ObjectIDZND0="17603@1" Pin0InfoVect0LinkObjId="SW-80492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-612 5556,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cd2b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-555 5556,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17603@0" ObjectIDZND0="g_3cd2280@1" Pin0InfoVect0LinkObjId="g_3cd2280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-555 5556,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cc11d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5556,-481 5556,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3cd2280@0" ObjectIDZND0="17604@1" Pin0InfoVect0LinkObjId="SW-80493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cd2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5556,-481 5556,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c3d650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5697,-721 5697,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9680@0" ObjectIDZND0="17613@1" Pin0InfoVect0LinkObjId="SW-80566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37715f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5697,-721 5697,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c02ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5697,-606 5697,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3c3d8b0@1" ObjectIDZND0="17679@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3d8b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5697,-606 5697,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d2b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5837,-721 5837,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9680@0" ObjectIDZND0="17609@1" Pin0InfoVect0LinkObjId="SW-80541_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37715f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5837,-721 5837,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c68760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5837,-662 5837,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17609@0" ObjectIDZND0="17608@1" Pin0InfoVect0LinkObjId="SW-80540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80541_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5837,-662 5837,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d69470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5837,-612 5837,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="17608@0" ObjectIDZND0="g_3c689c0@1" Pin0InfoVect0LinkObjId="g_3c689c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5837,-612 5837,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d7b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5837,-535 5837,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3c689c0@0" ObjectIDZND0="17610@1" Pin0InfoVect0LinkObjId="SW-80542_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c689c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5837,-535 5837,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cc5fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5718,-649 5697,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17614@0" ObjectIDZND0="17613@x" ObjectIDZND1="g_3c3d8b0@0" Pin0InfoVect0LinkObjId="SW-80566_0" Pin0InfoVect1LinkObjId="g_3c3d8b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5718,-649 5697,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d17280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5697,-660 5697,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17613@0" ObjectIDZND0="17614@x" ObjectIDZND1="g_3c3d8b0@0" Pin0InfoVect0LinkObjId="SW-80567_0" Pin0InfoVect1LinkObjId="g_3c3d8b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5697,-660 5697,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d174e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5697,-649 5697,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17614@x" ObjectIDND1="17613@x" ObjectIDZND0="g_3c3d8b0@0" Pin0InfoVect0LinkObjId="g_3c3d8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80567_0" Pin1InfoVect1LinkObjId="SW-80566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5697,-649 5697,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d70cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5837,-468 5851,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="17610@x" ObjectIDND1="17673@x" ObjectIDZND0="17611@0" Pin0InfoVect0LinkObjId="SW-80543_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80542_0" Pin1InfoVect1LinkObjId="CB-CX_DSP.CX_DSP_2C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5837,-468 5851,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3194f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5837,-477 5837,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="17610@0" ObjectIDZND0="17673@x" ObjectIDZND1="17611@x" Pin0InfoVect0LinkObjId="CB-CX_DSP.CX_DSP_2C_0" Pin0InfoVect1LinkObjId="SW-80543_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80542_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5837,-477 5837,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3195110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5837,-468 5837,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="17610@x" ObjectIDND1="17611@x" ObjectIDZND0="17673@0" Pin0InfoVect0LinkObjId="CB-CX_DSP.CX_DSP_2C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80542_0" Pin1InfoVect1LinkObjId="SW-80543_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5837,-468 5837,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d35a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-617 4639,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5517@0" ObjectIDZND0="g_3ccef90@1" Pin0InfoVect0LinkObjId="g_3ccef90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-617 4639,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d35c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-532 4639,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ccef90@0" ObjectIDZND0="17605@0" Pin0InfoVect0LinkObjId="SW-80517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ccef90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-532 4639,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c66350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-465 4653,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="17605@x" ObjectIDND1="17672@x" ObjectIDZND0="17606@0" Pin0InfoVect0LinkObjId="SW-80518_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80517_0" Pin1InfoVect1LinkObjId="CB-CX_DSP.CX_DSP_1C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-465 4653,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c665b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-474 4639,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="17605@1" ObjectIDZND0="17606@x" ObjectIDZND1="17672@x" Pin0InfoVect0LinkObjId="SW-80518_0" Pin0InfoVect1LinkObjId="CB-CX_DSP.CX_DSP_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80517_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-474 4639,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c66810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-465 4639,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="17606@x" ObjectIDND1="17605@x" ObjectIDZND0="17672@0" Pin0InfoVect0LinkObjId="CB-CX_DSP.CX_DSP_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80518_0" Pin1InfoVect1LinkObjId="SW-80517_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-465 4639,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dfd250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-699 4115,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5510@0" ObjectIDZND0="9679@0" Pin0InfoVect0LinkObjId="g_3c3b770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34802_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-699 4115,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c49a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-721 4776,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9679@0" ObjectIDZND0="5499@1" Pin0InfoVect0LinkObjId="SW-34727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-721 4776,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c2d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-854 5426,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17583@0" ObjectIDZND0="17581@1" Pin0InfoVect0LinkObjId="SW-80305_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-854 5426,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c0ba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-808 5426,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17581@0" ObjectIDZND0="17582@1" Pin0InfoVect0LinkObjId="SW-80306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-808 5426,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ccc4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-890 5426,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="17583@1" ObjectIDZND0="17621@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-890 5426,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ccc700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-747 5426,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17582@0" ObjectIDZND0="9680@0" Pin0InfoVect0LinkObjId="g_37715f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-747 5426,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d57250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5412,-1014 5426,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="17580@0" ObjectIDZND0="17576@x" ObjectIDZND1="17621@x" Pin0InfoVect0LinkObjId="SW-80293_0" Pin0InfoVect1LinkObjId="g_3ccc4a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5412,-1014 5426,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d57440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-995 5426,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17621@0" ObjectIDZND0="17580@x" ObjectIDZND1="17576@x" Pin0InfoVect0LinkObjId="SW-80304_0" Pin0InfoVect1LinkObjId="SW-80293_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ccc4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-995 5426,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d57630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1014 5426,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="17580@x" ObjectIDND1="17621@x" ObjectIDZND0="17576@1" Pin0InfoVect0LinkObjId="SW-80293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80304_0" Pin1InfoVect1LinkObjId="g_3ccc4a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1014 5426,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d57820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5443,-1147 5426,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17579@0" ObjectIDZND0="17575@x" ObjectIDZND1="17573@x" Pin0InfoVect0LinkObjId="SW-80292_0" Pin0InfoVect1LinkObjId="SW-80290_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5443,-1147 5426,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d57a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1134 5426,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17575@0" ObjectIDZND0="17579@x" ObjectIDZND1="17573@x" Pin0InfoVect0LinkObjId="SW-80296_0" Pin0InfoVect1LinkObjId="SW-80290_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1134 5426,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d57c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1147 5426,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="17575@x" ObjectIDND1="17579@x" ObjectIDZND0="17573@0" Pin0InfoVect0LinkObjId="SW-80290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80292_0" Pin1InfoVect1LinkObjId="SW-80296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1147 5426,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d57eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5443,-1204 5426,-1204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="17578@0" ObjectIDZND0="17573@x" ObjectIDZND1="17574@x" Pin0InfoVect0LinkObjId="SW-80290_0" Pin0InfoVect1LinkObjId="SW-80291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5443,-1204 5426,-1204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d580e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1191 5426,-1204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17573@1" ObjectIDZND0="17578@x" ObjectIDZND1="17574@x" Pin0InfoVect0LinkObjId="SW-80295_0" Pin0InfoVect1LinkObjId="SW-80291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1191 5426,-1204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d58310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1204 5426,-1212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17573@x" ObjectIDND1="17578@x" ObjectIDZND0="17574@1" Pin0InfoVect0LinkObjId="SW-80291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80290_0" Pin1InfoVect1LinkObjId="SW-80295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1204 5426,-1212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d41650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5443,-1265 5426,-1265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17577@0" ObjectIDZND0="17574@x" ObjectIDZND1="g_3c5d710@0" Pin0InfoVect0LinkObjId="SW-80291_0" Pin0InfoVect1LinkObjId="g_3c5d710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5443,-1265 5426,-1265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d418b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1248 5426,-1265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17574@0" ObjectIDZND0="17577@x" ObjectIDZND1="g_3c5d710@0" Pin0InfoVect0LinkObjId="SW-80294_0" Pin0InfoVect1LinkObjId="g_3c5d710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1248 5426,-1265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c76630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5711,-721 5711,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9680@0" ObjectIDZND0="17572@1" Pin0InfoVect0LinkObjId="SW-80287_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37715f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5711,-721 5711,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c76890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5711,-789 5711,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="17572@0" ObjectIDZND0="g_3c76af0@0" Pin0InfoVect0LinkObjId="g_3c76af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5711,-789 5711,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5dec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1265 5426,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17574@x" ObjectIDND1="17577@x" ObjectIDZND0="g_3c5d710@0" Pin0InfoVect0LinkObjId="g_3c5d710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80291_0" Pin1InfoVect1LinkObjId="SW-80294_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1265 5426,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5e0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1329 5426,-1344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="voltageTransformer" ObjectIDND0="g_3c5d710@1" ObjectIDZND0="g_3cc3f70@0" ObjectIDZND1="38055@1" ObjectIDZND2="g_362eff0@0" Pin0InfoVect0LinkObjId="g_3cc3f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_362eff0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c5d710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1329 5426,-1344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5456,-1344 5426,-1344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_362eff0@0" ObjectIDZND0="g_3c5d710@0" ObjectIDZND1="g_3cc3f70@0" ObjectIDZND2="38055@1" Pin0InfoVect0LinkObjId="g_3c5d710_0" Pin0InfoVect1LinkObjId="g_3cc3f70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_362eff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5456,-1344 5426,-1344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c60e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1247 4164,-1273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="17617@x" ObjectIDND1="5488@x" ObjectIDZND0="17615@1" Pin0InfoVect0LinkObjId="SW-80568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80570_0" Pin1InfoVect1LinkObjId="SW-34677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1247 4164,-1273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c61070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1216 4164,-1247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5488@1" ObjectIDZND0="17617@x" ObjectIDZND1="17615@x" Pin0InfoVect0LinkObjId="SW-80570_0" Pin0InfoVect1LinkObjId="SW-80568_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1216 4164,-1247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c61260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-1161 4164,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="17618@0" ObjectIDZND0="5488@x" ObjectIDZND1="5493@x" Pin0InfoVect0LinkObjId="SW-34677_0" Pin0InfoVect1LinkObjId="SW-34690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80571_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-1161 4164,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c61cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1189 4164,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5488@0" ObjectIDZND0="17618@x" ObjectIDZND1="5493@x" Pin0InfoVect0LinkObjId="SW-80571_0" Pin0InfoVect1LinkObjId="SW-34690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1189 4164,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c61f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1161 4164,-1132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="17618@x" ObjectIDND1="5488@x" ObjectIDZND0="5493@0" Pin0InfoVect0LinkObjId="SW-34690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80571_0" Pin1InfoVect1LinkObjId="SW-34677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1161 4164,-1132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d24880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-956 4164,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="17620@x" ObjectIDND1="5523@x" ObjectIDZND0="17619@0" Pin0InfoVect0LinkObjId="SW-80572_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80573_0" Pin1InfoVect1LinkObjId="g_376e6d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-956 4164,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d24ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-929 4164,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5523@0" ObjectIDZND0="17620@x" ObjectIDZND1="17619@x" Pin0InfoVect0LinkObjId="SW-80573_0" Pin0InfoVect1LinkObjId="SW-80572_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_376e6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-929 4164,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d28f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-1084 4171,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17585@1" ObjectIDZND0="18159@0" Pin0InfoVect0LinkObjId="g_3c9a8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-1084 4171,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4eee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-1042 4705,-1042 4705,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="17587@0" ObjectIDZND0="17584@x" ObjectIDZND1="17585@x" Pin0InfoVect0LinkObjId="SW-80369_0" Pin0InfoVect1LinkObjId="SW-80370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-1042 4705,-1042 4705,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4f9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4749,-1084 4705,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17584@1" ObjectIDZND0="17587@x" ObjectIDZND1="17585@x" Pin0InfoVect0LinkObjId="SW-80374_0" Pin0InfoVect1LinkObjId="SW-80370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80369_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4749,-1084 4705,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4fc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-1084 4647,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="17587@x" ObjectIDND1="17584@x" ObjectIDZND0="17585@0" Pin0InfoVect0LinkObjId="SW-80370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80374_0" Pin1InfoVect1LinkObjId="SW-80369_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-1084 4647,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4fe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1043 4830,-1043 4830,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17588@0" ObjectIDZND0="17586@x" ObjectIDZND1="17584@x" Pin0InfoVect0LinkObjId="SW-80372_0" Pin0InfoVect1LinkObjId="SW-80369_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1043 4830,-1043 4830,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c50980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-1084 4830,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17586@0" ObjectIDZND0="17588@x" ObjectIDZND1="17584@x" Pin0InfoVect0LinkObjId="SW-80375_0" Pin0InfoVect1LinkObjId="SW-80369_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-1084 4830,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c50be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-1084 4776,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="17588@x" ObjectIDND1="17586@x" ObjectIDZND0="17584@0" Pin0InfoVect0LinkObjId="SW-80369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80375_0" Pin1InfoVect1LinkObjId="SW-80372_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-1084 4776,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d04600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-658 4776,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5499@0" ObjectIDZND0="g_3c49c90@0" Pin0InfoVect0LinkObjId="g_3c49c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-658 4776,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d04860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-604 4776,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3c49c90@1" ObjectIDZND0="17675@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c49c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-604 4776,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d05260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-455 4115,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3c56ad0@0" ObjectIDND1="38165@x" ObjectIDZND0="5511@1" Pin0InfoVect0LinkObjId="SW-34803_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c56ad0_0" Pin1InfoVect1LinkObjId="EC-CX_DSP.083Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-455 4115,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d05450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-457 3884,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3ac8ef0@0" ObjectIDND1="0@x" ObjectIDZND0="5515@1" Pin0InfoVect0LinkObjId="SW-34826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ac8ef0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-457 3884,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d05640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-460 3711,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3b58010@0" ObjectIDND1="34076@x" ObjectIDZND0="5503@1" Pin0InfoVect0LinkObjId="SW-34757_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b58010_0" Pin1InfoVect1LinkObjId="SW-218946_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-460 3711,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c97160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-1380 4164,-1380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3c54790@0" ObjectIDZND0="g_362e5c0@0" ObjectIDZND1="17616@x" ObjectIDZND2="17615@x" Pin0InfoVect0LinkObjId="g_362e5c0_0" Pin0InfoVect1LinkObjId="SW-80569_0" Pin0InfoVect2LinkObjId="SW-80568_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c54790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-1380 4164,-1380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c97ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1380 4164,-1399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3c54790@0" ObjectIDND1="g_362e5c0@0" ObjectIDND2="17616@x" ObjectIDZND0="6573@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c54790_0" Pin1InfoVect1LinkObjId="g_362e5c0_0" Pin1InfoVect2LinkObjId="SW-80569_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1380 4164,-1399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c97ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-1326 4164,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="17616@0" ObjectIDZND0="17615@x" ObjectIDZND1="g_362e5c0@0" ObjectIDZND2="g_3c54790@0" Pin0InfoVect0LinkObjId="SW-80568_0" Pin0InfoVect1LinkObjId="g_362e5c0_0" Pin0InfoVect2LinkObjId="g_3c54790_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-1326 4164,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c98720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1309 4164,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="17615@0" ObjectIDZND0="17616@x" ObjectIDZND1="g_362e5c0@0" ObjectIDZND2="g_3c54790@0" Pin0InfoVect0LinkObjId="SW-80569_0" Pin0InfoVect1LinkObjId="g_362e5c0_0" Pin0InfoVect2LinkObjId="g_3c54790_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1309 4164,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c98980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4193,-1365 4164,-1365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_362e5c0@0" ObjectIDZND0="17616@x" ObjectIDZND1="17615@x" ObjectIDZND2="g_3c54790@0" Pin0InfoVect0LinkObjId="SW-80569_0" Pin0InfoVect1LinkObjId="SW-80568_0" Pin0InfoVect2LinkObjId="g_3c54790_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_362e5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4193,-1365 4164,-1365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c99470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1326 4164,-1365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="17616@x" ObjectIDND1="17615@x" ObjectIDZND0="g_362e5c0@0" ObjectIDZND1="g_3c54790@0" ObjectIDZND2="6573@1" Pin0InfoVect0LinkObjId="g_362e5c0_0" Pin0InfoVect1LinkObjId="g_3c54790_0" Pin0InfoVect2LinkObjId="g_3c97ad0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80569_0" Pin1InfoVect1LinkObjId="SW-80568_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1326 4164,-1365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c996d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1365 4164,-1380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_362e5c0@0" ObjectIDND1="17616@x" ObjectIDND2="17615@x" ObjectIDZND0="g_3c54790@0" ObjectIDZND1="6573@1" Pin0InfoVect0LinkObjId="g_3c54790_0" Pin0InfoVect1LinkObjId="g_3c97ad0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_362e5c0_0" Pin1InfoVect1LinkObjId="SW-80569_0" Pin1InfoVect2LinkObjId="SW-80568_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1365 4164,-1380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c99930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1382 5426,-1361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="38055@1" ObjectIDZND0="g_3c5d710@0" ObjectIDZND1="g_362eff0@0" ObjectIDZND2="g_3cc3f70@0" Pin0InfoVect0LinkObjId="g_3c5d710_0" Pin0InfoVect1LinkObjId="g_362eff0_0" Pin0InfoVect2LinkObjId="g_3cc3f70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1382 5426,-1361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1344 5426,-1361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_3c5d710@0" ObjectIDND1="g_362eff0@0" ObjectIDZND0="g_3cc3f70@0" ObjectIDZND1="38055@1" Pin0InfoVect0LinkObjId="g_3cc3f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c5d710_0" Pin1InfoVect1LinkObjId="g_362eff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1344 5426,-1361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1361 5410,-1361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_3c5d710@0" ObjectIDND1="g_362eff0@0" ObjectIDND2="38055@1" ObjectIDZND0="g_3cc3f70@0" Pin0InfoVect0LinkObjId="g_3cc3f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c5d710_0" Pin1InfoVect1LinkObjId="g_362eff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1361 5410,-1361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9a8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1037 4164,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17619@1" ObjectIDZND0="18159@0" Pin0InfoVect0LinkObjId="g_3d28f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1037 4164,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d2f340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-1096 4164,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5493@1" ObjectIDZND0="18159@0" Pin0InfoVect0LinkObjId="g_3d28f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-1096 4164,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d2fb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-1084 5415,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17586@1" ObjectIDZND0="18160@0" Pin0InfoVect0LinkObjId="g_3d30580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-1084 5415,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d30580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1098 5426,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17575@1" ObjectIDZND0="18160@0" Pin0InfoVect0LinkObjId="g_3d2fb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1098 5426,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d30db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-1066 5426,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17576@0" ObjectIDZND0="18160@0" Pin0InfoVect0LinkObjId="g_3d2fb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-1066 5426,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d31880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-458 4281,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3865780@0" ObjectIDND1="34486@x" ObjectIDZND0="12512@1" Pin0InfoVect0LinkObjId="SW-66329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3865780_0" Pin1InfoVect1LinkObjId="EC-CX_DSP.086Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-458 4281,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb6bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-276 3965,-241 3985,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="26161@1" Pin0InfoVect0LinkObjId="SW-154219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-276 3965,-241 3985,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb6de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-241 4089,-241 4089,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26164@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-154222_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-241 4089,-241 4089,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb74c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-241 4040,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26161@0" ObjectIDZND0="26164@0" Pin0InfoVect0LinkObjId="SW-154222_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-154219_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-241 4040,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb9990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-172 3899,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3d53800@0" ObjectIDND1="5516@x" ObjectIDND2="5516@x" ObjectIDZND0="26165@0" Pin0InfoVect0LinkObjId="SW-154223_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d53800_0" Pin1InfoVect1LinkObjId="SW-34827_0" Pin1InfoVect2LinkObjId="SW-34827_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-172 3899,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cba480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-257 3884,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3d53800@0" ObjectIDND1="5516@x" ObjectIDND2="5516@x" ObjectIDZND0="26165@x" ObjectIDZND1="38164@x" Pin0InfoVect0LinkObjId="SW-154223_0" Pin0InfoVect1LinkObjId="EC-CX_DSP.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d53800_0" Pin1InfoVect1LinkObjId="SW-34827_0" Pin1InfoVect2LinkObjId="SW-34827_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-257 3884,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cba6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-172 3884,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3d53800@0" ObjectIDND1="5516@x" ObjectIDND2="5516@x" ObjectIDZND0="38164@0" Pin0InfoVect0LinkObjId="EC-CX_DSP.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d53800_0" Pin1InfoVect1LinkObjId="SW-34827_0" Pin1InfoVect2LinkObjId="SW-34827_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-172 3884,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c6bb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-172 3950,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26165@1" ObjectIDZND0="26162@1" Pin0InfoVect0LinkObjId="SW-154220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-154223_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-172 3950,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c6dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-158 3992,-172 3977,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="26162@0" Pin0InfoVect0LinkObjId="SW-154220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-158 3992,-172 3977,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d61db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-301 4491,-301 4491,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="busSection" ObjectIDND0="34082@x" ObjectIDND1="g_3c9fa00@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218945_0" Pin1InfoVect1LinkObjId="g_3c9fa00_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-301 4491,-301 4491,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d62720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-319 4452,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34082@1" ObjectIDZND0="0@0" ObjectIDZND1="g_3c9fa00@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3c9fa00_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218945_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-319 4452,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d4a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4531,-412 4452,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="27359@x" ObjectIDZND1="g_3d0c3d0@0" ObjectIDZND2="5507@x" Pin0InfoVect0LinkObjId="SW-175192_0" Pin0InfoVect1LinkObjId="g_3d0c3d0_0" Pin0InfoVect2LinkObjId="SW-34780_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4531,-412 4452,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d4a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-367 4452,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27359@1" ObjectIDZND0="34082@0" Pin0InfoVect0LinkObjId="SW-218945_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-175192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-367 4452,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c8a560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,20 3800,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,20 3800,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c8a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,20 3840,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,20 3840,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c8b150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3587,2 3587,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3587,2 3587,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c8dfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3587,-52 3587,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3587,-52 3587,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d74480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3587,-96 3587,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3d746e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d746e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3587,-96 3587,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d773b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-240 4495,-257 4452,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3c9fa00@0" ObjectIDZND0="0@0" ObjectIDZND1="34082@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-218945_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9fa00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-240 4495,-257 4452,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d775a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-257 4452,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_3c9fa00@0" ObjectIDND1="0@x" ObjectIDND2="38162@x" ObjectIDZND0="0@0" ObjectIDZND1="34082@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-218945_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c9fa00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="EC-CX_DSP.082Ld_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-257 4452,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d77790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-138 4492,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="busSection" EndDevType0="switch" ObjectIDND0="38162@x" ObjectIDND1="g_3c9fa00@0" ObjectIDND2="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_DSP.082Ld_0" Pin1InfoVect1LinkObjId="g_3c9fa00_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-138 4492,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d78200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-77 4452,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="38162@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3c9fa00@0" ObjectIDZND2="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3c9fa00_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DSP.082Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-77 4452,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3cad0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-138 4666,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3cad310@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cad310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-138 4666,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d1c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-426 5056,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="17592@x" ObjectIDND1="0@x" ObjectIDZND0="g_3d1b3e0@0" Pin0InfoVect0LinkObjId="g_3d1b3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80412_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-426 5056,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d1cc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-426 5056,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDZND0="g_3d1b3e0@0" ObjectIDZND1="17592@x" Pin0InfoVect0LinkObjId="g_3d1b3e0_0" Pin0InfoVect1LinkObjId="SW-80412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-426 5056,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d1f520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-411 5006,-426 5056,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="17592@0" ObjectIDZND0="g_3d1b3e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3d1b3e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-411 5006,-426 5056,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d83100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-444 5086,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3d2c500@0" Pin0InfoVect0LinkObjId="g_3d2c500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-444 5086,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3631560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-412 4452,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3d0c3d0@0" ObjectIDND1="5507@x" ObjectIDZND0="27359@0" Pin0InfoVect0LinkObjId="SW-175192_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d0c3d0_0" Pin1InfoVect1LinkObjId="SW-34780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-412 4452,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36317c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-483 4452,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3d0c3d0@0" ObjectIDND1="5507@x" ObjectIDZND0="27359@x" Pin0InfoVect0LinkObjId="SW-175192_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d0c3d0_0" Pin1InfoVect1LinkObjId="SW-34780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-483 4452,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3631a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-511 4452,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5507@1" ObjectIDZND0="g_3d0c3d0@0" ObjectIDZND1="27359@x" Pin0InfoVect0LinkObjId="g_3d0c3d0_0" Pin0InfoVect1LinkObjId="SW-175192_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-511 4452,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3631c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-622 4452,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5505@0" ObjectIDZND0="g_362fa20@1" Pin0InfoVect0LinkObjId="g_362fa20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-622 4452,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3631ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-556 4452,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_362fa20@0" ObjectIDZND0="5507@0" Pin0InfoVect0LinkObjId="SW-34780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_362fa20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-556 4452,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3632b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-617 4281,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="12510@0" ObjectIDZND0="g_3632140@1" Pin0InfoVect0LinkObjId="g_3632140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-617 4281,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3632dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-545 4281,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3632140@0" ObjectIDZND0="12512@0" Pin0InfoVect0LinkObjId="SW-66329_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3632140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-545 4281,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31650f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-617 4115,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5509@0" ObjectIDZND0="g_3633020@1" Pin0InfoVect0LinkObjId="g_3633020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-617 4115,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3165350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-545 4115,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3633020@0" ObjectIDZND0="5511@0" Pin0InfoVect0LinkObjId="SW-34803_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3633020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-545 4115,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3165e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-618 3884,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5513@0" ObjectIDZND0="g_31655b0@1" Pin0InfoVect0LinkObjId="g_31655b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-618 3884,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31660d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-546 3884,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31655b0@0" ObjectIDZND0="5515@0" Pin0InfoVect0LinkObjId="SW-34826_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31655b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-546 3884,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3166d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-527 3711,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5503@0" ObjectIDZND0="g_3166330@0" Pin0InfoVect0LinkObjId="g_3166330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-34757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-527 3711,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3166fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-602 3711,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3166330@1" ObjectIDZND0="5501@0" Pin0InfoVect0LinkObjId="SW-34752_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3166330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-602 3711,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3167f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-126 4611,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_3167210@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3167210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-126 4611,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3168a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-138 4611,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3167210@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3167210_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-138 4611,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3168cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-138 4629,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_3167210@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3167210_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-138 4629,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3168f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3877,20 3936,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3877,20 3936,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_316af40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3466,20 3587,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3466,20 3587,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_316b8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,20 3587,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3743,20 3587,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_316bb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-126 3992,20 3972,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-126 3992,20 3972,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_316d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-260 4347,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_316c4c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_316c4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-260 4347,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_316da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-257 4452,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3c9fa00@0" ObjectIDND1="0@0" ObjectIDND2="34082@x" ObjectIDZND0="0@x" ObjectIDZND1="38162@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-CX_DSP.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c9fa00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-218945_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-257 4452,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_316e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-410 5237,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_316dcc0@0" ObjectIDZND0="41617@x" ObjectIDZND1="17596@x" Pin0InfoVect0LinkObjId="EC-CX_DSP.072Ld_0" Pin0InfoVect1LinkObjId="SW-80439_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_316dcc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-410 5237,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e55cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5237,-426 5237,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="17596@0" ObjectIDZND0="g_316dcc0@0" ObjectIDZND1="41617@x" Pin0InfoVect0LinkObjId="g_316dcc0_0" Pin0InfoVect1LinkObjId="EC-CX_DSP.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5237,-426 5237,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e55f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5237,-410 5237,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_316dcc0@0" ObjectIDND1="17596@x" ObjectIDZND0="41617@0" Pin0InfoVect0LinkObjId="EC-CX_DSP.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_316dcc0_0" Pin1InfoVect1LinkObjId="SW-80439_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5237,-410 5237,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e56ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-412 5396,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3e56190@0" ObjectIDZND0="17600@x" ObjectIDZND1="34487@x" Pin0InfoVect0LinkObjId="SW-80466_0" Pin0InfoVect1LinkObjId="EC-CX_DSP.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e56190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-412 5396,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e579b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5396,-426 5396,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="17600@0" ObjectIDZND0="g_3e56190@0" ObjectIDZND1="34487@x" Pin0InfoVect0LinkObjId="g_3e56190_0" Pin0InfoVect1LinkObjId="EC-CX_DSP.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80466_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5396,-426 5396,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e57c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5396,-412 5396,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3e56190@0" ObjectIDND1="17600@x" ObjectIDZND0="34487@0" Pin0InfoVect0LinkObjId="EC-CX_DSP.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e56190_0" Pin1InfoVect1LinkObjId="SW-80466_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5396,-412 5396,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-257 3884,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3d53800@0" ObjectIDND1="26165@x" ObjectIDND2="38164@x" ObjectIDZND0="5516@x" ObjectIDZND1="5516@x" ObjectIDZND2="g_3d53800@0" Pin0InfoVect0LinkObjId="SW-34827_0" Pin0InfoVect1LinkObjId="SW-34827_0" Pin0InfoVect2LinkObjId="g_3d53800_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d53800_0" Pin1InfoVect1LinkObjId="SW-154223_0" Pin1InfoVect2LinkObjId="EC-CX_DSP.084Ld_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-257 3884,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317c820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-333 3922,-333 3922,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="5516@x" ObjectIDND1="g_3d53800@0" ObjectIDND2="26165@x" ObjectIDZND0="5516@0" Pin0InfoVect0LinkObjId="SW-34827_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-34827_0" Pin1InfoVect1LinkObjId="g_3d53800_0" Pin1InfoVect2LinkObjId="SW-154223_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-333 3922,-333 3922,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317ca80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-332 3884,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5516@x" ObjectIDND1="g_3d53800@0" ObjectIDND2="26165@x" ObjectIDZND0="5516@x" ObjectIDZND1="g_3d53800@0" ObjectIDZND2="26165@x" Pin0InfoVect0LinkObjId="SW-34827_0" Pin0InfoVect1LinkObjId="g_3d53800_0" Pin0InfoVect2LinkObjId="SW-154223_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-34827_0" Pin1InfoVect1LinkObjId="g_3d53800_0" Pin1InfoVect2LinkObjId="SW-154223_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-332 3884,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317cce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-349 3884,-350 3884,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_317aeb0@1" ObjectIDZND0="5516@x" ObjectIDZND1="5516@x" ObjectIDZND2="g_3d53800@0" Pin0InfoVect0LinkObjId="SW-34827_0" Pin0InfoVect1LinkObjId="SW-34827_0" Pin0InfoVect2LinkObjId="g_3d53800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_317aeb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-349 3884,-350 3884,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317cf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4463,-349 4607,-350 4607,-182 4607,-172 4889,-176 4889,-56 5556,-56 5556,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_317aeb0@0" ObjectIDZND0="17604@0" Pin0InfoVect0LinkObjId="SW-80493_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_317aeb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4463,-349 4607,-350 4607,-182 4607,-172 4889,-176 4889,-56 5556,-56 5556,-426 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-30" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3376.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30" ObjectName="DYN-CX_DSP"/>
     <cge:Meas_Ref ObjectId="30"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="5052" cy="-175" fill="none" fillStyle="0" r="10" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5006" cy="-176" fill="none" fillStyle="0" r="10" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5006" cy="-137" fill="none" fillStyle="0" r="10" stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DSP.CX_DSP_1IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3671,-721 4900,-721 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9679" ObjectName="BS-CX_DSP.CX_DSP_1IM"/>
    <cge:TPSR_Ref TObjectID="9679"/></metadata>
   <polyline fill="none" opacity="0" points="3671,-721 4900,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-276 4006,-276 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3641,-276 4006,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-276 4590,-276 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4059,-276 4590,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DSP.CX_DSP_1IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4952,-721 5886,-721 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9680" ObjectName="BS-CX_DSP.CX_DSP_1IIM"/>
    <cge:TPSR_Ref TObjectID="9680"/></metadata>
   <polyline fill="none" opacity="0" points="4952,-721 5886,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DSP.CX_DSP_35_1M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-1084 4172,-1084 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18159" ObjectName="BS-CX_DSP.CX_DSP_35_1M"/>
    <cge:TPSR_Ref TObjectID="18159"/></metadata>
   <polyline fill="none" opacity="0" points="4155,-1084 4172,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DSP.CX_DSP_35_2M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5414,-1084 5438,-1084 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18160" ObjectName="BS-CX_DSP.CX_DSP_35_2M"/>
    <cge:TPSR_Ref TObjectID="18160"/></metadata>
   <polyline fill="none" opacity="0" points="5414,-1084 5438,-1084 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="4993" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5237" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5396" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5556" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5697" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5837" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4201" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3749" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3922" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3808" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4281" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4639" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4452" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4861" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="3711" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="3884" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4115" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4776" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5426" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4558" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5711" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9679" cx="4163" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18159" cx="4171" cy="-1084" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18159" cx="4164" cy="-1084" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18160" cx="5415" cy="-1084" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18160" cx="5426" cy="-1084" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18160" cx="5426" cy="-1084" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4089" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3965" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18159" cx="4164" cy="-1084" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4491" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9680" cx="5086" cy="-721" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4347" cy="-276" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3083" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3079" y="-1196"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3082" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="23" stroke="rgb(0,255,0)" stroke-width="1" width="12" x="4951" y="-296"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="117" stroke="rgb(0,255,0)" stroke-width="1" width="33" x="4939" y="-353"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="90" stroke="rgb(0,255,0)" stroke-width="1" width="48" x="4982" y="-346"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="133" stroke="rgb(0,255,0)" stroke-width="1" width="88" x="4987" y="-252"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="23" stroke="rgb(0,255,0)" stroke-width="1" width="12" x="5000" y="-241"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3496" y="-1182"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0d990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4173.000000 -820.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d02bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4169.000000 -768.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cbf1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -1213.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb4430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -1123.000000) translate(0,12)">3881</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c32d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -1297.000000) translate(0,12)">3886</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b7c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -1352.000000) translate(0,12)">38867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b564f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -776.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d5c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3720.000000 -639.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_388f6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 -693.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c71ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 -516.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d237e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3893.000000 -639.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d2e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3891.000000 -689.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cdf230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3891.000000 -506.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c33870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -639.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ae2060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -689.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37710f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -512.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d2c730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -641.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c9d9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4458.000000 -691.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c92860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -533.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c92a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4648.000000 -638.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_376f0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -688.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b93df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 -503.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cce000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4913.000000 -681.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c7a760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -696.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d509b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.377778 -692.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d53d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -1412.000000) translate(0,15)">35kV白大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca8850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -742.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c480c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3602.000000 -299.000000) translate(0,15)">10kVⅠ段旁母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c3e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -304.000000) translate(0,15)">10kVⅡ段旁母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3598790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -746.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9ee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -126.000000) translate(0,15)">前进Ⅱ回线T燃料二厂支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9ee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -126.000000) translate(0,33)">（10kV前进线电源）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35766b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4540.000000 -439.000000) translate(0,15)">生</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35766b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4540.000000 -439.000000) translate(0,33)">活</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35766b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4540.000000 -439.000000) translate(0,51)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9c940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -534.000000) translate(0,15)">燃</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9c940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -534.000000) translate(0,33)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9c940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -534.000000) translate(0,51)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9c940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -534.000000) translate(0,69)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9c940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -534.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9c940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -534.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 -532.000000) translate(0,15)">紫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 -532.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 -532.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 -532.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 -532.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b37250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5520.000000 -563.000000) translate(0,15)">燃</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b37250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5520.000000 -563.000000) translate(0,33)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b37250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5520.000000 -563.000000) translate(0,51)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b37250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5520.000000 -563.000000) translate(0,69)">Ⅳ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b37250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5520.000000 -563.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b37250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5520.000000 -563.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d5d150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -531.000000) translate(0,15)">燃</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d5d150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -531.000000) translate(0,33)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d5d150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -531.000000) translate(0,51)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d5d150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -531.000000) translate(0,69)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d5d150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -531.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d5d150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -531.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3d2cab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3244.500000 -1165.500000) translate(0,16)">大石铺变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_376f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3101.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3101.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3101.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3101.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3101.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3101.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3101.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cd3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -894.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cd3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -894.000000) translate(0,33)">SZ10-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cd3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -894.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cd3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -894.000000) translate(0,69)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cd3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -894.000000) translate(0,87)">Ud=7.16%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cd3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 -894.000000) translate(0,105)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c89fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -150.000000) translate(0,12)">4003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a23620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -163.000000) translate(0,12)">430</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a23830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4500.000000 -166.000000) translate(0,12)">4302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c37650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -894.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d5e4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -879.000000) translate(0,12)">油温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8b280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -688.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b76fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4290.000000 -638.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b771e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4287.000000 -518.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d22da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -515.000000) translate(0,15)">紫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d22da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -515.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d22da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -515.000000) translate(0,51)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d22da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -515.000000) translate(0,69)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d22da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -515.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b38670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4169.000000 -1027.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b61580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -981.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b3d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.500000 -1189.000000) translate(0,12)">38817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b5d7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -1273.000000) translate(0,12)">38860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d21930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5247.000000 -688.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d21e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5246.000000 -573.000000) translate(0,12)">0723</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c9e500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5246.000000 -449.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c903d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -501.000000) translate(0,15)">紫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c903d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -501.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c903d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -501.000000) translate(0,51)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c903d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -501.000000) translate(0,69)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c903d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -501.000000) translate(0,87)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c903d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -501.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c903d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5357.000000 -501.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc1430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5566.000000 -688.000000) translate(0,12)">0742</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc1920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -573.000000) translate(0,12)">0743</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c91bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -449.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3852.000000 -515.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3852.000000 -515.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c91dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3852.000000 -515.000000) translate(0,51)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dfad80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5847.000000 -688.000000) translate(0,12)">0762</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ca7b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5846.000000 -509.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ca80f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5707.000000 -689.000000) translate(0,12)">0752</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d17740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5721.000000 -638.000000) translate(0,12)">07527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d17c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5799.000000 -273.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b13f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5857.000000 -458.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c36d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -456.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cced50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4615.000000 -267.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cd0560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5849.000000 -316.000000) translate(0,12)">07660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c4a660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -687.000000) translate(0,12)">0871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c0bcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5442.000000 -823.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c0c1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5437.000000 -877.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c0c3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5436.000000 -762.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc33c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5385.000000 -1183.000000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc38b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5379.000000 -1120.000000) translate(0,12)">3872</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc3af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5376.000000 -1234.000000) translate(0,12)">3876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc3d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5444.500000 -1290.000000) translate(0,12)">38767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d569e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5432.000000 -1056.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d57010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5368.000000 -1041.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d58540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5445.000000 -1173.000000) translate(0,12)">38727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d589f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5444.500000 -1229.000000) translate(0,12)">38760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5225.000000 -955.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5225.000000 -955.000000) translate(0,33)">SZ10-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5225.000000 -955.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5225.000000 -955.000000) translate(0,69)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5225.000000 -955.000000) translate(0,87)">Ud=7.16%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5225.000000 -955.000000) translate(0,105)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5390.000000 -1346.000000) translate(0,15)">东</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5390.000000 -1346.000000) translate(0,33)">吕</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5390.000000 -1346.000000) translate(0,51)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5390.000000 -1346.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.000000 -899.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c5f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.000000 -899.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c60480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5746.000000 -900.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c60480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5746.000000 -900.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c50e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -1110.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c513d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.000000 -1112.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c51610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -1030.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c51850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -1032.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c51a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4648.000000 -310.000000) translate(0,12)">08560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c51cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5718.000000 -778.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d12ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5470.000000 -956.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d130b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5470.000000 -941.000000) translate(0,12)">油温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c95a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3218.000000 -241.000000) translate(0,16)">3876245</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d31ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3218.000000 -212.000000) translate(0,16)">5704</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d32550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5462.000000 -988.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d32b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -1073.000000) translate(0,15)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d33bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5094.000000 -635.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d342d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5246.000000 -633.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d34720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5405.000000 -633.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb63a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5565.000000 -633.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb67a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5846.000000 -633.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_3c6de00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -188.000000) translate(0,9)">N1塔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3987.000000 -265.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6efd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4042.000000 -267.000000) translate(0,12)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6f210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -164.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6f450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -169.000000) translate(0,12)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4a760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -392.000000) translate(0,12)">4472</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -391.000000) translate(0,12)">N6塔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4b350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4408.000000 -338.000000) translate(0,12)">N7塔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3d4d790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3366.000000 -1151.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3d4e8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3366.000000 -1186.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c807b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3943.000000 -5.000000) translate(0,12)">4002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c8aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -6.000000) translate(0,12)">4006</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c8af10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3747.000000 -5.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d75120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3431.000000 34.000000) translate(0,15)">波罗哨线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d75f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3598.000000 -73.000000) translate(0,12)">Z411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d76570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3594.000000 -24.000000) translate(0,12)">Z4111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d767b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.000000 27.000000) translate(0,12)">N48塔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d769f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 35.000000) translate(0,12)">N45</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cadd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4641.000000 -164.000000) translate(0,12)">4301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cae240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -126.000000) translate(0,15)">燃料二厂支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cae480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -309.000000) translate(0,15)">燃二厂配电室</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3caebe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -153.000000) translate(0,15)">大石铺支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3caf560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -317.000000) translate(0,12)">4425</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3caf7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 -375.000000) translate(0,12)">4423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cafa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -311.000000) translate(0,12)">4445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cafc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -339.000000) translate(0,12)">4473</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cafea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -4.000000) translate(0,12)">400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb0640" transform="matrix(1.583333 -0.000000 -0.000000 1.800000 3106.000000 -752.000000) translate(0,12)">公用设备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d80f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4985.000000 -96.000000) translate(0,15)">1号接地变及</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d80f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4985.000000 -96.000000) translate(0,33)">消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d82bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5093.000000 -571.000000) translate(0,12)">0713</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d832f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5093.000000 -687.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362d0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4965.000000 -400.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e61ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -165.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e61ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -165.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3170410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3213.000000 -150.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3171230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3512.500000 -1171.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3173250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -923.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3177470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5660.000000 -484.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31780b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -485.000000) translate(0,12)">10kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3178330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -464.000000) translate(0,15)">紫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3178330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -464.000000) translate(0,33)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3178330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -464.000000) translate(0,51)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3178330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -464.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3178330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -464.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3178570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -823.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3178e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -838.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3179370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -854.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31795f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -806.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3179830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -791.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3179a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -775.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3179cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -760.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3179ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5851.000000 -804.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317a130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5851.000000 -819.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317a370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5851.000000 -835.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317a5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5857.000000 -787.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317a7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5843.000000 -772.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317aa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5843.000000 -756.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317ac70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5843.000000 -741.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_317d1d0" transform="matrix(0.895833 -0.000000 -0.000000 1.176471 3109.791667 -697.058824) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30d0cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5411.000000 -688.000000) translate(0,12)">0732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30d0fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5411.000000 -581.000000) translate(0,12)">0733</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30d1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5411.000000 -454.000000) translate(0,12)">0736</text>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DSP.CX_DSP_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24246"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5388.000000 -903.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5388.000000 -903.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17621" ObjectName="TF-CX_DSP.CX_DSP_2T"/>
    <cge:TPSR_Ref TObjectID="17621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5070.000000 -346.000000)" xlink:href="#transformer2:shape76_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5070.000000 -346.000000)" xlink:href="#transformer2:shape76_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DSP.CX_DSP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8032"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -836.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -836.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5523" ObjectName="TF-CX_DSP.CX_DSP_1T"/>
    <cge:TPSR_Ref TObjectID="5523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DSP.CX_DSP_ZYB1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24302"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.782609 -0.000000 0.000000 -0.846939 4764.000000 -495.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.782609 -0.000000 0.000000 -0.846939 4764.000000 -495.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17675" ObjectName="TF-CX_DSP.CX_DSP_ZYB1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DSP.CX_DSP_ZYB2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24312"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.782609 -0.000000 0.000000 -0.846939 5685.000000 -497.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.782609 -0.000000 0.000000 -0.846939 5685.000000 -497.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17679" ObjectName="TF-CX_DSP.CX_DSP_ZYB2"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DSP"/>
</svg>