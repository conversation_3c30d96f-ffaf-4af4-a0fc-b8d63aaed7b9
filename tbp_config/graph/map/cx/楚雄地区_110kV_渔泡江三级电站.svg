<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-54" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3113 -1200 1915 1204">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="0.06"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape35">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="11" x2="9" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="28" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="4" x2="2" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="15" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="15" x2="13" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="15" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="11" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="9" y2="7"/>
    <circle cx="11" cy="24" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="6" cy="15" fillStyle="0" rx="6" ry="6.5" stroke-width="0.431185"/>
    <circle cx="11" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="14" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="6" x2="4" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="6" x2="4" y1="13" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b7ea60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b7f950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b80310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b81330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b82490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b82e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b83960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b84320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_24c1e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_24c1e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b87100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b87100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b88c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b88c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2b89be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b8b840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b8c4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b8d340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b8da90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b8f310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b8ff70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b90830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b90ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b920d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b92a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b93540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b93f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b95520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b95f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b970e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b97d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ba6180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b9e520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2b98f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2b9a280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1214" width="1925" x="3108" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3114" y="-596"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -583.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8297" ObjectName="SW-CX_YPJ.CX_YPJ_00167SW"/>
     <cge:Meas_Ref ObjectId="46771"/>
    <cge:TPSR_Ref TObjectID="8297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -495.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8306" ObjectName="SW-CX_YPJ.CX_YPJ_001XC1"/>
     <cge:Meas_Ref ObjectId="46788"/>
    <cge:TPSR_Ref TObjectID="8306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -567.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10854" ObjectName="SW-CX_YPJ.CX_YPJ_001XC"/>
     <cge:Meas_Ref ObjectId="46788"/>
    <cge:TPSR_Ref TObjectID="10854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46765">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.928571 0.000000 0.000000 -0.673913 4053.428571 -773.543478)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8291" ObjectName="SW-CX_YPJ.CX_YPJ_1811SW"/>
     <cge:Meas_Ref ObjectId="46765"/>
    <cge:TPSR_Ref TObjectID="8291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46766">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -878.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8292" ObjectName="SW-CX_YPJ.CX_YPJ_1816SW"/>
     <cge:Meas_Ref ObjectId="46766"/>
    <cge:TPSR_Ref TObjectID="8292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -437.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8308" ObjectName="SW-CX_YPJ.CX_YPJ_081XC"/>
     <cge:Meas_Ref ObjectId="46789"/>
    <cge:TPSR_Ref TObjectID="8308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -360.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10856" ObjectName="SW-CX_YPJ.CX_YPJ_081XC1"/>
     <cge:Meas_Ref ObjectId="46789"/>
    <cge:TPSR_Ref TObjectID="10856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -436.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8312" ObjectName="SW-CX_YPJ.CX_YPJ_083XC"/>
     <cge:Meas_Ref ObjectId="46791"/>
    <cge:TPSR_Ref TObjectID="8312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -362.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8313" ObjectName="SW-CX_YPJ.CX_YPJ_083XC1"/>
     <cge:Meas_Ref ObjectId="46791"/>
    <cge:TPSR_Ref TObjectID="8313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46795">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -526.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10864" ObjectName="SW-CX_YPJ.CX_YPJ_GG_SW"/>
     <cge:Meas_Ref ObjectId="46795"/>
    <cge:TPSR_Ref TObjectID="10864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 -587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8305" ObjectName="SW-CX_YPJ.CX_YPJ_08467SW"/>
     <cge:Meas_Ref ObjectId="46779"/>
    <cge:TPSR_Ref TObjectID="8305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -496.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8315" ObjectName="SW-CX_YPJ.CX_YPJ_084XC1"/>
     <cge:Meas_Ref ObjectId="46792"/>
    <cge:TPSR_Ref TObjectID="8315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -571.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8314" ObjectName="SW-CX_YPJ.CX_YPJ_084XC"/>
     <cge:Meas_Ref ObjectId="46792"/>
    <cge:TPSR_Ref TObjectID="8314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46768">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -857.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8294" ObjectName="SW-CX_YPJ.CX_YPJ_18160SW"/>
     <cge:Meas_Ref ObjectId="46768"/>
    <cge:TPSR_Ref TObjectID="8294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46769">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -934.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8295" ObjectName="SW-CX_YPJ.CX_YPJ_18167SW"/>
     <cge:Meas_Ref ObjectId="46769"/>
    <cge:TPSR_Ref TObjectID="8295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46767">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -742.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8293" ObjectName="SW-CX_YPJ.CX_YPJ_18117SW"/>
     <cge:Meas_Ref ObjectId="46767"/>
    <cge:TPSR_Ref TObjectID="8293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -264.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8309" ObjectName="SW-CX_YPJ.CX_YPJ_081TVSW"/>
     <cge:Meas_Ref ObjectId="46793"/>
    <cge:TPSR_Ref TObjectID="8309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -438.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8310" ObjectName="SW-CX_YPJ.CX_YPJ_082XC"/>
     <cge:Meas_Ref ObjectId="46790"/>
    <cge:TPSR_Ref TObjectID="8310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -361.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10855" ObjectName="SW-CX_YPJ.CX_YPJ_082XC1"/>
     <cge:Meas_Ref ObjectId="46790"/>
    <cge:TPSR_Ref TObjectID="10855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -265.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8311" ObjectName="SW-CX_YPJ.CX_YPJ_082TVSW"/>
     <cge:Meas_Ref ObjectId="46794"/>
    <cge:TPSR_Ref TObjectID="8311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -225.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8301" ObjectName="SW-CX_YPJ.CX_YPJ_0816SW"/>
     <cge:Meas_Ref ObjectId="46775"/>
    <cge:TPSR_Ref TObjectID="8301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -226.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8303" ObjectName="SW-CX_YPJ.CX_YPJ_0826SW"/>
     <cge:Meas_Ref ObjectId="46777"/>
    <cge:TPSR_Ref TObjectID="8303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4614.000000 -323.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8299" ObjectName="SW-CX_YPJ.CX_YPJ_08367SW"/>
     <cge:Meas_Ref ObjectId="46773"/>
    <cge:TPSR_Ref TObjectID="8299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 -179.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.000000 -177.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YPJ.CX_YPJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-480 4834,-480 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8289" ObjectName="BS-CX_YPJ.CX_YPJ_9IM"/>
    <cge:TPSR_Ref TObjectID="8289"/></metadata>
   <polyline fill="none" opacity="0" points="3809,-480 4834,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YPJ.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-829 4075,-829 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49510" ObjectName="BS-CX_YPJ.XM"/>
    <cge:TPSR_Ref TObjectID="49510"/></metadata>
   <polyline fill="none" opacity="0" points="4060,-829 4075,-829 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_YPJ.083LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -248.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49508" ObjectName="EC-CX_YPJ.083LD"/>
    <cge:TPSR_Ref TObjectID="49508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YPJ.084LD">
    <use class="BKBV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4683.000000 -714.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49509" ObjectName="EC-CX_YPJ.084LD"/>
    <cge:TPSR_Ref TObjectID="49509"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2273e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3963.000000 -951.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2274900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -874.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2275390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -759.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2275e20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 -667.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2184490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -600.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2184f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -604.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21859b0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4597.500000 -279.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_236c4e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4378.000000 -523.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_236d0d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 -96.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_236d710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -140.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d8f50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -220.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2381ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -97.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2382520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4458.000000 -138.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2310940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -93.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2313980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -221.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b3dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -560.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b49b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4993.000000 -723.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b6a30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 -603.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b81e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -607.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22bb790">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -94.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2272600">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -330.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2273100">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -331.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2186440">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -760.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21871b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -1016.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218bc10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 -617.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23076d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4660.000000 -834.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2308440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -141.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2308e60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -140.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2309880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4957.000000 -665.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227ec70">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4642.500000 -351.500000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227f9e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -291.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2280b20">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4967.000000 -737.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46694" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3600.000000 -1048.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46694" ObjectName="CX_YPJ:CX_YPJ_GG_QT_46"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46697" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -1019.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46697" ObjectName="CX_YPJ:CX_YPJ_GG_QT_49"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46695" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3729.000000 -1048.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46695" ObjectName="CX_YPJ:CX_YPJ_GG_QT_47"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46696" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1048.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46696" ObjectName="CX_YPJ:CX_YPJ_GG_QT_48"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46698" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -995.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46698" ObjectName="CX_YPJ:CX_YPJ_GG_QT_50"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46699" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -969.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46699" ObjectName="CX_YPJ:CX_YPJ_GG_QT_51"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46700" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -946.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46700" ObjectName="CX_YPJ:CX_YPJ_GG_QT_52"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46701" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -926.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46701" ObjectName="CX_YPJ:CX_YPJ_GG_QT_53"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46702" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -902.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46702" ObjectName="CX_YPJ:CX_YPJ_GG_QT_54"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46703" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -876.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46703" ObjectName="CX_YPJ:CX_YPJ_GG_QT_55"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46704" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -852.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46704" ObjectName="CX_YPJ:CX_YPJ_GG_QT_56"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46705" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -830.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46705" ObjectName="CX_YPJ:CX_YPJ_GG_QT_57"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78582" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3255.538462 -1015.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78582" ObjectName="CX_YPJ:CX_YPJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-70650" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3255.538462 -974.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70650" ObjectName="CX_YPJ:CX_YPJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46658" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8290"/>
     <cge:Term_Ref ObjectID="11664"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46659" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8290"/>
     <cge:Term_Ref ObjectID="11664"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46660" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8290"/>
     <cge:Term_Ref ObjectID="11664"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46675" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -569.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8296"/>
     <cge:Term_Ref ObjectID="11676"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46676" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -569.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46676" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8296"/>
     <cge:Term_Ref ObjectID="11676"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46677" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -569.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8296"/>
     <cge:Term_Ref ObjectID="11676"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46685" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -569.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8304"/>
     <cge:Term_Ref ObjectID="11692"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46686" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -569.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8304"/>
     <cge:Term_Ref ObjectID="11692"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46687" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -569.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8304"/>
     <cge:Term_Ref ObjectID="11692"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46663" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8300"/>
     <cge:Term_Ref ObjectID="11684"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46664" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8300"/>
     <cge:Term_Ref ObjectID="11684"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46665" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8300"/>
     <cge:Term_Ref ObjectID="11684"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46669" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8302"/>
     <cge:Term_Ref ObjectID="11688"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46670" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8302"/>
     <cge:Term_Ref ObjectID="11688"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46671" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8302"/>
     <cge:Term_Ref ObjectID="11688"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46680" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8298"/>
     <cge:Term_Ref ObjectID="11680"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46681" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8298"/>
     <cge:Term_Ref ObjectID="11680"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46682" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8298"/>
     <cge:Term_Ref ObjectID="11680"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-46692" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -531.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8289"/>
     <cge:Term_Ref ObjectID="11663"/>
    <cge:TPSR_Ref TObjectID="8289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-46693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -531.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8289"/>
     <cge:Term_Ref ObjectID="11663"/>
    <cge:TPSR_Ref TObjectID="8289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-46690" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3992.000000 -855.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8288"/>
     <cge:Term_Ref ObjectID="11662"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-46691" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3992.000000 -855.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8288"/>
     <cge:Term_Ref ObjectID="11662"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46764">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8290" ObjectName="SW-CX_YPJ.CX_YPJ_181BK"/>
     <cge:Meas_Ref ObjectId="46764"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -394.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8300" ObjectName="SW-CX_YPJ.CX_YPJ_081BK"/>
     <cge:Meas_Ref ObjectId="46774"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -524.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8296" ObjectName="SW-CX_YPJ.CX_YPJ_001BK"/>
     <cge:Meas_Ref ObjectId="46770"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -393.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8298" ObjectName="SW-CX_YPJ.CX_YPJ_083BK"/>
     <cge:Meas_Ref ObjectId="46772"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -528.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8304" ObjectName="SW-CX_YPJ.CX_YPJ_084BK"/>
     <cge:Meas_Ref ObjectId="46778"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -395.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8302" ObjectName="SW-CX_YPJ.CX_YPJ_082BK"/>
     <cge:Meas_Ref ObjectId="46776"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -783.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YPJ" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuda_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4068,-1042 4068,-1079 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11716" ObjectName="AC-110kV.yuda_line"/>
    <cge:TPSR_Ref TObjectID="11716_SS-54"/></metadata>
   <polyline fill="none" opacity="0" points="4068,-1042 4068,-1079 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_YPJ.CX_YPJ3_GN1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -158.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15890" ObjectName="SM-CX_YPJ.CX_YPJ3_GN1"/>
    <cge:TPSR_Ref TObjectID="15890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YPJ.CX_YPJ3_GN2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -159.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15891" ObjectName="SM-CX_YPJ.CX_YPJ3_GN2"/>
    <cge:TPSR_Ref TObjectID="15891"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="3951" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4624" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4353" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4674" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4067" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4353" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49510" cx="4067" cy="-829" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49510" cx="4067" cy="-829" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23be790" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3904.000000 -138.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23b88b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4590.000000 -182.000000) translate(0,15)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23aa610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4316.000000 -647.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22be030" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3993.000000 -91.000000) translate(0,15)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22cc370" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4312.000000 -137.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20dbff0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4402.000000 -86.000000) translate(0,15)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_234d050" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4630.000000 -932.000000) translate(0,15)">10kV渔铁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e9ce0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4702.000000 -737.000000) translate(0,15)">10kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e9ce0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4702.000000 -737.000000) translate(0,33)">隔离变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ea1f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4920.000000 -928.000000) translate(0,15)">至10kV铁锁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ea470" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4931.000000 -555.000000) translate(0,15)">2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b8dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4083.000000 -864.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2314cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3930.000000 -701.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b92e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -423.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22c1370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -423.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22c2da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -424.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bc220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -261.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bc850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -262.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bccb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -500.000000) translate(0,12)">水位年:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bcef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -639.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4689.000000 -557.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -635.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4081.000000 -553.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bdb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -794.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231dc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -813.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231de30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -909.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231e070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -925.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231e2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4017.000000 -983.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2361780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21e2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21e2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21e2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21e2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21e2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21e2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21e2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_22be480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3272.000000 -1166.500000) translate(0,16)">渔泡江三级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23659a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -335.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2365ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3529.000000 -1048.000000) translate(0,12)">水位年:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e1500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3657.000000 -1048.000000) translate(0,12)">水位月:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e17c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.000000 -1048.000000) translate(0,12)">水位日:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2187f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -745.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2187f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -745.000000) translate(0,33)">SF10-27500/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2187f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -745.000000) translate(0,51)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2187f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -745.000000) translate(0,69)">121±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2276a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -295.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2276a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -295.000000) translate(0,33)">SF-J10-18/3400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2276a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -295.000000) translate(0,51)">Pe:10(11)MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2276a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -295.000000) translate(0,69)">Ue:10.5V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2276a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -295.000000) translate(0,87)">Ie:687.3A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2276a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -295.000000) translate(0,105)">cos∮：0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_227aee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -297.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_227aee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -297.000000) translate(0,33)">SF-J10-18/3400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_227aee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -297.000000) translate(0,51)">Pe:10(11)MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_227aee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -297.000000) translate(0,69)">Ue:10.5V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_227aee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -297.000000) translate(0,87)">Ie:687.3A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_227aee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3723.000000 -297.000000) translate(0,105)">cos∮：0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -1060.000000) translate(0,12)">渔大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_221c380" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3247.000000 -240.000000) translate(0,15)">6217952</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_221c380" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3247.000000 -240.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_221c380" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3247.000000 -240.000000) translate(0,51)">6217949</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231fda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 879.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23200b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 864.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2320330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 849.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2320560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 569.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2320860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 554.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2320aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 539.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2320ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 437.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2321180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 422.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23213c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 407.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23217e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 437.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2321aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 422.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2321ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 407.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2322100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 437.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23223c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 422.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2322600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 407.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2322a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 569.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2322ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 554.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2322f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 539.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e1dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 1022.000000) translate(0,12)">地索水文站(姚安)降雨量(mm):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 998.000000) translate(0,12)">地索水文站(姚安)流量(m3/s):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23638a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 973.000000) translate(0,12)">黎武水文站(石羊)降雨量(mm):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_225af00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 949.000000) translate(0,12)">黎武水文站(石羊)流量(m3/s):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_225b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 926.000000) translate(0,12)">渔泡江站降雨量(mm):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_225bcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 902.000000) translate(0,12)">渔泡江站来水量(万m3):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2253da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 877.000000) translate(0,12)">渔泡江站用水量(万m3):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2254300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 853.000000) translate(0,12)">渔泡江站水库水位(m):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2254860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 831.000000) translate(0,12)">渔泡江站水库有效库容(万m3):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2218da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 852.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221a100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 838.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221b230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 531.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221b420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 517.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -699.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -699.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YPJ.CX_YPJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="11720"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -651.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -651.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8317" ObjectName="TF-CX_YPJ.CX_YPJ_1T"/>
    <cge:TPSR_Ref TObjectID="8317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4609.000000 -260.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4609.000000 -260.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4947.000000 -664.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4947.000000 -664.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37323" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5905" ObjectName="DYN-CX_YPJ"/>
     <cge:Meas_Ref ObjectId="37323"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22cc840">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4363.500000 -604.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227b180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -174.000000)" xlink:href="#voltageTransformer:shape35"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227cc70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -181.000000)" xlink:href="#voltageTransformer:shape35"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a8f70">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4003.500000 -1010.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_22d2aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-338 3921,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="8309@x" ObjectIDND1="8301@x" ObjectIDND2="15890@x" ObjectIDZND0="g_2272600@0" Pin0InfoVect0LinkObjId="g_2272600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46793_0" Pin1InfoVect1LinkObjId="SW-46775_0" Pin1InfoVect2LinkObjId="SM-CX_YPJ.CX_YPJ3_GN1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-338 3921,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22cc180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-960 3988,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8295@0" ObjectIDZND0="g_2273e70@0" Pin0InfoVect0LinkObjId="g_2273e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-960 3988,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c1700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-609 4126,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2184490@0" ObjectIDZND0="8297@1" Pin0InfoVect0LinkObjId="SW-46771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2184490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-609 4126,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c18f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-609 4067,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="8297@0" ObjectIDZND0="10854@x" ObjectIDZND1="g_22b6a30@0" ObjectIDZND2="8317@x" Pin0InfoVect0LinkObjId="SW-46788_0" Pin0InfoVect1LinkObjId="g_22b6a30_0" Pin0InfoVect2LinkObjId="g_23248d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-609 4067,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2323950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-532 4067,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8296@0" ObjectIDZND0="8306@0" Pin0InfoVect0LinkObjId="SW-46788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-532 4067,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2323b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-574 4067,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10854@1" ObjectIDZND0="8296@1" Pin0InfoVect0LinkObjId="SW-46770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-574 4067,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23244f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-609 4067,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="8297@x" ObjectIDND1="g_22b6a30@0" ObjectIDND2="8317@x" ObjectIDZND0="10854@0" Pin0InfoVect0LinkObjId="SW-46788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="g_22b6a30_0" Pin1InfoVect2LinkObjId="g_23248d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-609 4067,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23246e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-609 4037,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="8297@x" ObjectIDND1="10854@x" ObjectIDND2="8317@x" ObjectIDZND0="g_22b6a30@0" Pin0InfoVect0LinkObjId="g_22b6a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="SW-46788_0" Pin1InfoVect2LinkObjId="g_23248d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-609 4037,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23248d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-609 4067,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="8297@x" ObjectIDND1="10854@x" ObjectIDND2="g_22b6a30@0" ObjectIDZND0="8317@0" Pin0InfoVect0LinkObjId="g_2324cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="SW-46788_0" Pin1InfoVect2LinkObjId="g_22b6a30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-609 4067,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2324ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-768 4008,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2275390@0" ObjectIDZND0="8293@0" Pin0InfoVect0LinkObjId="SW-46767_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2275390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-768 4008,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2324cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-768 4067,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8293@1" ObjectIDZND0="8317@x" ObjectIDZND1="8291@x" ObjectIDZND2="g_2186440@0" Pin0InfoVect0LinkObjId="g_23248d0_0" Pin0InfoVect1LinkObjId="SW-46765_0" Pin0InfoVect2LinkObjId="g_2186440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-768 4067,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2325660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-736 4067,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8317@1" ObjectIDZND0="8293@x" ObjectIDZND1="8291@x" ObjectIDZND2="g_2186440@0" Pin0InfoVect0LinkObjId="SW-46767_0" Pin0InfoVect1LinkObjId="SW-46765_0" Pin0InfoVect2LinkObjId="g_2186440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23248d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-736 4067,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_239f490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-768 4067,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8293@x" ObjectIDND1="8317@x" ObjectIDND2="g_2186440@0" ObjectIDZND0="8291@0" Pin0InfoVect0LinkObjId="SW-46765_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46767_0" Pin1InfoVect1LinkObjId="g_23248d0_0" Pin1InfoVect2LinkObjId="g_2186440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-768 4067,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_239f680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-883 4008,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2274900@0" ObjectIDZND0="8294@0" Pin0InfoVect0LinkObjId="SW-46768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2274900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-883 4008,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_239f870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-883 4067,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8294@1" ObjectIDZND0="8290@x" ObjectIDZND1="8292@x" Pin0InfoVect0LinkObjId="SW-46764_0" Pin0InfoVect1LinkObjId="SW-46766_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46768_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-883 4067,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23a0030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-883 4067,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8294@x" ObjectIDND1="8292@x" ObjectIDZND0="8290@1" Pin0InfoVect0LinkObjId="SW-46764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46768_0" Pin1InfoVect1LinkObjId="SW-46766_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-883 4067,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23a1950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-900 4067,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8292@0" ObjectIDZND0="8294@x" ObjectIDZND1="8290@x" Pin0InfoVect0LinkObjId="SW-46768_0" Pin0InfoVect1LinkObjId="SW-46764_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-900 4067,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23017c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-461 3951,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8308@0" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_22c0440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-461 3951,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23019b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-429 3951,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8300@1" ObjectIDZND0="8308@1" Pin0InfoVect0LinkObjId="SW-46789_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-429 3951,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23042d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-301 3951,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8309@x" ObjectIDND1="8301@x" ObjectIDND2="15890@x" ObjectIDZND0="g_2272600@0" ObjectIDZND1="10856@x" Pin0InfoVect0LinkObjId="g_2272600_0" Pin0InfoVect1LinkObjId="SW-46789_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46793_0" Pin1InfoVect1LinkObjId="SW-46775_0" Pin1InfoVect2LinkObjId="SM-CX_YPJ.CX_YPJ3_GN1_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-301 3951,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23044c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-402 3951,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8300@0" ObjectIDZND0="10856@0" Pin0InfoVect0LinkObjId="SW-46789_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-402 3951,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23046b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-366 3951,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10856@1" ObjectIDZND0="g_2272600@0" ObjectIDZND1="8309@x" ObjectIDZND2="8301@x" Pin0InfoVect0LinkObjId="g_2272600_0" Pin0InfoVect1LinkObjId="SW-46793_0" Pin0InfoVect2LinkObjId="SW-46775_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46789_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-366 3951,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bde40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-207 3951,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15890@0" ObjectIDZND0="g_2272600@0" ObjectIDZND1="10856@x" ObjectIDZND2="8309@x" Pin0InfoVect0LinkObjId="g_2272600_0" Pin0InfoVect1LinkObjId="SW-46789_0" Pin0InfoVect2LinkObjId="SW-46793_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YPJ.CX_YPJ3_GN1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-207 3951,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c0440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-460 4624,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8312@0" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23017c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-460 4624,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2339f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-428 4624,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8298@1" ObjectIDZND0="8312@1" Pin0InfoVect0LinkObjId="SW-46791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-428 4624,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_233c240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-386 4623,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8313@0" ObjectIDZND0="8298@0" Pin0InfoVect0LinkObjId="SW-46772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-386 4623,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_233c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-513 4385,-513 4385,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8289@0" ObjectIDND1="10864@x" ObjectIDZND0="g_236c4e0@0" Pin0InfoVect0LinkObjId="g_236c4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23017c0_0" Pin1InfoVect1LinkObjId="SW-46795_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-513 4385,-513 4385,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_233cbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-480 4353,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8289@0" ObjectIDZND0="g_236c4e0@0" ObjectIDZND1="10864@x" Pin0InfoVect0LinkObjId="g_236c4e0_0" Pin0InfoVect1LinkObjId="SW-46795_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23017c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-480 4353,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cc650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-532 4353,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="10864@1" ObjectIDZND0="g_236c4e0@0" ObjectIDZND1="8289@0" Pin0InfoVect0LinkObjId="g_236c4e0_0" Pin0InfoVect1LinkObjId="g_23017c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46795_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-532 4353,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_234cc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4749,-613 4733,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2184f20@0" ObjectIDZND0="8305@1" Pin0InfoVect0LinkObjId="SW-46779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2184f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4749,-613 4733,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_234ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-613 4674,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8305@0" ObjectIDZND0="8314@x" ObjectIDZND1="g_22b81e0@0" ObjectIDZND2="g_218bc10@0" Pin0InfoVect0LinkObjId="SW-46792_0" Pin0InfoVect1LinkObjId="g_22b81e0_0" Pin0InfoVect2LinkObjId="g_218bc10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-613 4674,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e86e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-578 4674,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8314@1" ObjectIDZND0="8304@1" Pin0InfoVect0LinkObjId="SW-46778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-578 4674,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e88d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-613 4674,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8305@x" ObjectIDND1="g_22b81e0@0" ObjectIDND2="g_218bc10@0" ObjectIDZND0="8314@0" Pin0InfoVect0LinkObjId="SW-46792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="g_22b81e0_0" Pin1InfoVect2LinkObjId="g_218bc10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-613 4674,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e8af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-613 4644,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8305@x" ObjectIDND1="8314@x" ObjectIDND2="g_218bc10@0" ObjectIDZND0="g_22b81e0@0" Pin0InfoVect0LinkObjId="g_22b81e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="SW-46792_0" Pin1InfoVect2LinkObjId="g_218bc10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-613 4644,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e9680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-536 4674,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8304@0" ObjectIDZND0="8315@0" Pin0InfoVect0LinkObjId="SW-46792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-536 4674,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e98a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-502 4674,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8315@1" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23017c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-502 4674,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e9ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-501 4067,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8306@1" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23017c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-501 4067,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d8d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-301 3872,-301 3872,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2272600@0" ObjectIDND1="10856@x" ObjectIDND2="8301@x" ObjectIDZND0="8309@0" Pin0InfoVect0LinkObjId="SW-46793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2272600_0" Pin1InfoVect1LinkObjId="SW-46789_0" Pin1InfoVect2LinkObjId="SW-46775_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-301 3872,-301 3872,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d96f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-271 3872,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8309@1" ObjectIDZND0="g_22d8f50@1" Pin0InfoVect0LinkObjId="g_22d8f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46793_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-271 3872,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-225 3872,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_22d8f50@0" ObjectIDZND0="g_227b180@0" Pin0InfoVect0LinkObjId="g_227b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d8f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-225 3872,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22db710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-339 4323,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="8311@x" ObjectIDND1="8303@x" ObjectIDND2="15891@x" ObjectIDZND0="g_2273100@0" Pin0InfoVect0LinkObjId="g_2273100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46794_0" Pin1InfoVect1LinkObjId="SW-46777_0" Pin1InfoVect2LinkObjId="SM-CX_YPJ.CX_YPJ3_GN2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-339 4323,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_237e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-462 4353,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8310@0" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23017c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-462 4353,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_237ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-430 4353,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8302@1" ObjectIDZND0="8310@1" Pin0InfoVect0LinkObjId="SW-46790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46776_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-430 4353,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2381660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-302 4353,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8311@x" ObjectIDND1="8303@x" ObjectIDND2="15891@x" ObjectIDZND0="g_2273100@0" ObjectIDZND1="10855@x" Pin0InfoVect0LinkObjId="g_2273100_0" Pin0InfoVect1LinkObjId="SW-46790_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46794_0" Pin1InfoVect1LinkObjId="SW-46777_0" Pin1InfoVect2LinkObjId="SM-CX_YPJ.CX_YPJ3_GN2_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-302 4353,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2381880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-403 4353,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8302@0" ObjectIDZND0="10855@0" Pin0InfoVect0LinkObjId="SW-46790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-403 4353,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2381aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-367 4353,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10855@1" ObjectIDZND0="g_2273100@0" ObjectIDZND1="8311@x" ObjectIDZND2="8303@x" Pin0InfoVect0LinkObjId="g_2273100_0" Pin0InfoVect1LinkObjId="SW-46794_0" Pin0InfoVect2LinkObjId="SW-46777_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-367 4353,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2381cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-208 4353,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15891@0" ObjectIDZND0="g_2273100@0" ObjectIDZND1="10855@x" ObjectIDZND2="8311@x" Pin0InfoVect0LinkObjId="g_2273100_0" Pin0InfoVect1LinkObjId="SW-46790_0" Pin0InfoVect2LinkObjId="SW-46794_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YPJ.CX_YPJ3_GN2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-208 4353,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2310720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4467,-143 4467,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2382520@0" ObjectIDZND0="g_2310940@0" Pin0InfoVect0LinkObjId="g_2310940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2382520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4467,-143 4467,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2313760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-302 4274,-302 4274,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2273100@0" ObjectIDND1="10855@x" ObjectIDND2="8303@x" ObjectIDZND0="8311@0" Pin0InfoVect0LinkObjId="SW-46794_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2273100_0" Pin1InfoVect1LinkObjId="SW-46790_0" Pin1InfoVect2LinkObjId="SW-46777_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-302 4274,-302 4274,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2314120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-272 4274,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8311@1" ObjectIDZND0="g_2313980@1" Pin0InfoVect0LinkObjId="g_2313980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-272 4274,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2314340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-226 4274,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2313980@0" ObjectIDZND0="g_227cc70@0" Pin0InfoVect0LinkObjId="g_227cc70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2313980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-226 4274,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2314560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-287 4420,-302 4353,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8303@0" ObjectIDZND0="g_2273100@0" ObjectIDZND1="10855@x" ObjectIDZND2="8311@x" Pin0InfoVect0LinkObjId="g_2273100_0" Pin0InfoVect1LinkObjId="SW-46790_0" Pin0InfoVect2LinkObjId="SW-46794_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-287 4420,-302 4353,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2314780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-207 4420,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2308e60@0" ObjectIDND1="0@x" ObjectIDZND0="8303@1" Pin0InfoVect0LinkObjId="SW-46777_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2308e60_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-207 4420,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22b4570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-609 4353,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_22cc840@0" ObjectIDZND0="g_22b3dd0@1" Pin0InfoVect0LinkObjId="g_22b3dd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22cc840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-609 4353,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22b4790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-565 4353,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_22b3dd0@0" ObjectIDZND0="10864@0" Pin0InfoVect0LinkObjId="SW-46795_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b3dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-565 4353,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22b65f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4066,-717 4006,-717 4006,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="8317@x" ObjectIDZND0="g_2275e20@0" Pin0InfoVect0LinkObjId="g_2275e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23248d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4066,-717 4006,-717 4006,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22b6810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-768 4110,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="8293@x" ObjectIDND1="8317@x" ObjectIDND2="8291@x" ObjectIDZND0="g_2186440@0" Pin0InfoVect0LinkObjId="g_2186440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46767_0" Pin1InfoVect1LinkObjId="g_23248d0_0" Pin1InfoVect2LinkObjId="SW-46765_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-768 4110,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bbb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-130 4065,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22bb790@0" ObjectIDZND0="g_236d710@0" Pin0InfoVect0LinkObjId="g_236d710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22bb790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-130 4065,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-301 4018,-301 4018,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2272600@0" ObjectIDND1="10856@x" ObjectIDND2="8309@x" ObjectIDZND0="8301@0" Pin0InfoVect0LinkObjId="SW-46775_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2272600_0" Pin1InfoVect1LinkObjId="SW-46789_0" Pin1InfoVect2LinkObjId="SW-46793_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-301 4018,-301 4018,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22bbfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-219 4018,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8301@1" ObjectIDZND0="g_2308440@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2308440_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46775_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-219 4018,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-305 4588,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21859b0@0" ObjectIDZND0="8299@1" Pin0InfoVect0LinkObjId="SW-46773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21859b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-305 4588,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218c410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-613 4674,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8305@x" ObjectIDND1="8314@x" ObjectIDND2="g_22b81e0@0" ObjectIDZND0="g_218bc10@0" Pin0InfoVect0LinkObjId="g_218bc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="SW-46792_0" Pin1InfoVect2LinkObjId="g_22b81e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-613 4674,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23064c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-784 4674,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-784 4674,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2306720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-842 4655,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_23076d0@0" Pin0InfoVect0LinkObjId="g_23076d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-842 4655,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2307210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-818 4674,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_23076d0@0" Pin0InfoVect0LinkObjId="g_23076d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-818 4674,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2307470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-842 4674,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_23076d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_23076d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-842 4674,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-132 4018,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_236d0d0@0" ObjectIDZND0="g_2308440@0" Pin0InfoVect0LinkObjId="g_2308440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_236d0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-132 4018,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-199 4018,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2308440@1" ObjectIDZND0="8301@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-46775_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2308440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-199 4018,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230a760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-207 4420,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8303@x" ObjectIDND1="0@x" ObjectIDZND0="g_2308e60@1" Pin0InfoVect0LinkObjId="g_2308e60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46777_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-207 4420,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_230a9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-145 4420,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2308e60@0" ObjectIDZND0="g_2381ee0@0" Pin0InfoVect0LinkObjId="g_2381ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2308e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-145 4420,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22760c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-176 4065,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_236d710@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_236d710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-176 4065,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2276320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-203 4065,-209 4018,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="8301@x" ObjectIDZND1="g_2308440@0" Pin0InfoVect0LinkObjId="SW-46775_0" Pin0InfoVect1LinkObjId="g_2308440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-203 4065,-209 4018,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2276580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-207 4467,-207 4467,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8303@x" ObjectIDND1="g_2308e60@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46777_0" Pin1InfoVect1LinkObjId="g_2308e60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-207 4467,-207 4467,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22767e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4467,-184 4467,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2382520@1" Pin0InfoVect0LinkObjId="g_2382520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4467,-184 4467,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_227ea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-349 4588,-356 4651,-356 4651,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8299@0" ObjectIDZND0="g_227ec70@0" Pin0InfoVect0LinkObjId="g_227ec70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-349 4588,-356 4651,-356 4651,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2280400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-275 4623,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="49508@0" ObjectIDZND0="g_227f9e0@0" Pin0InfoVect0LinkObjId="g_227f9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YPJ.083LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-275 4623,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2280660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-349 4624,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_227f9e0@1" ObjectIDZND0="8313@1" Pin0InfoVect0LinkObjId="SW-46791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_227f9e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-349 4624,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22808c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4962,-670 4962,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2309880@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2309880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4962,-670 4962,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22816d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-787 4961,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2280b20@0" ObjectIDZND0="g_22b49b0@0" Pin0InfoVect0LinkObjId="g_22b49b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2280b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-787 4961,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22a4d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-777 5000,-809 4961,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22b49b0@0" ObjectIDZND0="g_2280b20@0" Pin0InfoVect0LinkObjId="g_2280b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b49b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-777 5000,-809 4961,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22a4fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-809 4961,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_2280b20@0" ObjectIDND1="g_22b49b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2280b20_0" Pin1InfoVect1LinkObjId="g_22b49b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-809 4961,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22a5210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4962,-723 4962,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2309880@1" ObjectIDZND0="g_2280b20@1" Pin0InfoVect0LinkObjId="g_2280b20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2309880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4962,-723 4962,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22a9810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-960 4067,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="8295@1" ObjectIDZND0="8292@x" ObjectIDZND1="g_21871b0@0" ObjectIDZND2="11716@1" Pin0InfoVect0LinkObjId="SW-46766_0" Pin0InfoVect1LinkObjId="g_21871b0_0" Pin0InfoVect2LinkObjId="g_22ab050_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-960 4067,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22aa300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-936 4067,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="8292@1" ObjectIDZND0="8295@x" ObjectIDZND1="g_21871b0@0" ObjectIDZND2="11716@1" Pin0InfoVect0LinkObjId="SW-46769_0" Pin0InfoVect1LinkObjId="g_21871b0_0" Pin0InfoVect2LinkObjId="g_22ab050_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-936 4067,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22aa560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-1024 4067,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_21871b0@0" ObjectIDZND0="8295@x" ObjectIDZND1="8292@x" ObjectIDZND2="11716@1" Pin0InfoVect0LinkObjId="SW-46769_0" Pin0InfoVect1LinkObjId="SW-46766_0" Pin0InfoVect2LinkObjId="g_22ab050_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21871b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-1024 4067,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ab050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1024 4067,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_21871b0@0" ObjectIDND1="8295@x" ObjectIDND2="8292@x" ObjectIDZND0="11716@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21871b0_0" Pin1InfoVect1LinkObjId="SW-46769_0" Pin1InfoVect2LinkObjId="SW-46766_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1024 4067,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ab2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-998 4020,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="8295@x" ObjectIDND1="8292@x" ObjectIDND2="g_21871b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46769_0" Pin1InfoVect1LinkObjId="SW-46766_0" Pin1InfoVect2LinkObjId="g_21871b0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-998 4020,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22abda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-960 4067,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="8295@x" ObjectIDND1="8292@x" ObjectIDZND0="g_21871b0@0" ObjectIDZND1="11716@1" Pin0InfoVect0LinkObjId="g_21871b0_0" Pin0InfoVect1LinkObjId="g_22ab050_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46769_0" Pin1InfoVect1LinkObjId="SW-46766_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-960 4067,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ac000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-998 4067,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="8295@x" ObjectIDND1="8292@x" ObjectIDZND0="g_21871b0@0" ObjectIDZND1="11716@1" Pin0InfoVect0LinkObjId="g_21871b0_0" Pin0InfoVect1LinkObjId="g_22ab050_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46769_0" Pin1InfoVect1LinkObjId="SW-46766_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-998 4067,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ac4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-843 4067,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="8290@0" ObjectIDZND0="49510@0" Pin0InfoVect0LinkObjId="g_22ac720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-843 4067,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ac720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-812 4067,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8291@1" ObjectIDZND0="49510@0" Pin0InfoVect0LinkObjId="g_22ac4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46765_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-812 4067,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_221dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-675 4674,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_218bc10@1" ObjectIDZND0="49509@0" Pin0InfoVect0LinkObjId="EC-CX_YPJ.084LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218bc10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-675 4674,-687 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YPJ"/>
</svg>