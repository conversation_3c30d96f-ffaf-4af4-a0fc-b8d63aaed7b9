<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-52" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3112 -1343 3158 1373">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape121">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="7" y1="18" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="7" x2="17" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="17" y1="18" y2="12"/>
    <ellipse cx="12" cy="35" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="39" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="35" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="39" y2="35"/>
    <ellipse cx="12" cy="15" rx="11" ry="12.5" stroke-width="1.22172"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape155">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="59" x2="50" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="60" x2="60" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="17" x2="17" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="24" x2="24" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="25" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="43" x2="43" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="50" x2="50" y1="48" y2="31"/>
    <circle cx="28" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="32" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="65" x2="65" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="62" x2="62" y1="44" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape167">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="36" y1="32" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="20" y1="6" y2="35"/>
    <rect height="31" stroke-width="0.5" width="16" x="12" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="31" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="4" y2="4"/>
    <ellipse cx="14" cy="18" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="24" x2="24" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="24" y1="8" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="10" y2="12"/>
    <ellipse cx="8" cy="10" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape22_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="35" x2="42" y1="0" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="49" x2="42" y1="10" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="42" x2="49" y1="0" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="42" x2="35" y1="10" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="41" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="20" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="5" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="13" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="12" x2="20" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape22_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.853125" x1="52" x2="43" y1="10" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.853125" x1="43" x2="52" y1="0" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="51" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.853125" x1="3" x2="12" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.853125" x1="12" x2="3" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape22-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="35" x2="42" y1="0" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="49" x2="42" y1="10" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="42" x2="49" y1="0" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="42" x2="35" y1="10" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="41" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="21" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="6" x2="14" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="14" x2="6" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="13" x2="21" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape22-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="14" x2="6" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="49" x2="42" y1="10" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="42" x2="49" y1="0" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="50" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7875" x1="6" x2="14" y1="9" y2="0"/>
   </symbol>
   <symbol id="transformer:shape0_0">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="20" x2="20" y1="32" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="32"/>
   </symbol>
   <symbol id="transformer:shape0_1">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape0-2">
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="62" y1="37" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="62" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="55" y1="46" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_34b1a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34b2be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34b35d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34b4270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34b50a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34b5b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34b6650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_34b7080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2afe340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2afe340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34ba170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34ba170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34bbb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34bbb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_34bcb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34be790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34bf3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34c02c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34c0ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34c2360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34c3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34c3920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34c40e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34c51c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34c5b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34c6630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34c6ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34c8470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34c9010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34ca040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34d9450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34cc570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34cd5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34ceb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1383" width="3168" x="3107" y="-1348"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="760" stroke="rgb(255,0,0)" stroke-width="1" width="1867" x="4403" y="-1343"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3113" y="-570"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3113" y="-1050"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3113" y="-1170"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-42863">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -726.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7151" ObjectName="SW-CX_YIZ.CX_YIZ_10117SW"/>
     <cge:Meas_Ref ObjectId="42863"/>
    <cge:TPSR_Ref TObjectID="7151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42854">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -871.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7142" ObjectName="SW-CX_YIZ.CX_YIZ_12117SW"/>
     <cge:Meas_Ref ObjectId="42854"/>
    <cge:TPSR_Ref TObjectID="7142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42957">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.000000 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7184" ObjectName="SW-CX_YIZ.CX_YIZ_6211SW"/>
     <cge:Meas_Ref ObjectId="42957"/>
    <cge:TPSR_Ref TObjectID="7184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42858">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -854.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7146" ObjectName="SW-CX_YIZ.CX_YIZ_19010SW"/>
     <cge:Meas_Ref ObjectId="42858"/>
    <cge:TPSR_Ref TObjectID="7146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42853">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3761.000000 -956.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7141" ObjectName="SW-CX_YIZ.CX_YIZ_1216SW"/>
     <cge:Meas_Ref ObjectId="42853"/>
    <cge:TPSR_Ref TObjectID="7141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42852">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3761.000000 -825.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7140" ObjectName="SW-CX_YIZ.CX_YIZ_1211SW"/>
     <cge:Meas_Ref ObjectId="42852"/>
    <cge:TPSR_Ref TObjectID="7140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42857">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3985.000000 -889.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7145" ObjectName="SW-CX_YIZ.CX_YIZ_1901SW"/>
     <cge:Meas_Ref ObjectId="42857"/>
    <cge:TPSR_Ref TObjectID="7145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42862">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3761.000000 -746.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7150" ObjectName="SW-CX_YIZ.CX_YIZ_1011SW"/>
     <cge:Meas_Ref ObjectId="42862"/>
    <cge:TPSR_Ref TObjectID="7150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42855">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -934.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7143" ObjectName="SW-CX_YIZ.CX_YIZ_12160SW"/>
     <cge:Meas_Ref ObjectId="42855"/>
    <cge:TPSR_Ref TObjectID="7143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42856">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -1006.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7144" ObjectName="SW-CX_YIZ.CX_YIZ_12167SW"/>
     <cge:Meas_Ref ObjectId="42856"/>
    <cge:TPSR_Ref TObjectID="7144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42859">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 -950.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7147" ObjectName="SW-CX_YIZ.CX_YIZ_19017SW"/>
     <cge:Meas_Ref ObjectId="42859"/>
    <cge:TPSR_Ref TObjectID="7147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42867">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -725.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7155" ObjectName="SW-CX_YIZ.CX_YIZ_10217SW"/>
     <cge:Meas_Ref ObjectId="42867"/>
    <cge:TPSR_Ref TObjectID="7155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42866">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4130.000000 -745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7154" ObjectName="SW-CX_YIZ.CX_YIZ_1021SW"/>
     <cge:Meas_Ref ObjectId="42866"/>
    <cge:TPSR_Ref TObjectID="7154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -444.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10822" ObjectName="SW-CX_YIZ.CX_YIZ_6001XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42940">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 -373.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10830" ObjectName="SW-CX_YIZ.CX_YIZ_622XC"/>
     <cge:Meas_Ref ObjectId="42940"/>
    <cge:TPSR_Ref TObjectID="10830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42940">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 -284.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10831" ObjectName="SW-CX_YIZ.CX_YIZ_622XC1"/>
     <cge:Meas_Ref ObjectId="42940"/>
    <cge:TPSR_Ref TObjectID="10831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42951">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -370.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10832" ObjectName="SW-CX_YIZ.CX_YIZ_623XC"/>
     <cge:Meas_Ref ObjectId="42951"/>
    <cge:TPSR_Ref TObjectID="10832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42951">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -281.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10833" ObjectName="SW-CX_YIZ.CX_YIZ_623XC1"/>
     <cge:Meas_Ref ObjectId="42951"/>
    <cge:TPSR_Ref TObjectID="10833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42929">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4535.000000 -368.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10828" ObjectName="SW-CX_YIZ.CX_YIZ_624XC"/>
     <cge:Meas_Ref ObjectId="42929"/>
    <cge:TPSR_Ref TObjectID="10828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42929">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4535.000000 -279.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10829" ObjectName="SW-CX_YIZ.CX_YIZ_624XC1"/>
     <cge:Meas_Ref ObjectId="42929"/>
    <cge:TPSR_Ref TObjectID="10829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42959">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7186" ObjectName="SW-CX_YIZ.CX_YIZ_6252SW"/>
     <cge:Meas_Ref ObjectId="42959"/>
    <cge:TPSR_Ref TObjectID="7186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.076923 4734.000000 -450.846154)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10823" ObjectName="SW-CX_YIZ.CX_YIZ_6002XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42861">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3685.000000 -573.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7149" ObjectName="SW-CX_YIZ.CX_YIZ_1010SW"/>
     <cge:Meas_Ref ObjectId="42861"/>
    <cge:TPSR_Ref TObjectID="7149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42865">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -574.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7153" ObjectName="SW-CX_YIZ.CX_YIZ_1020SW"/>
     <cge:Meas_Ref ObjectId="42865"/>
    <cge:TPSR_Ref TObjectID="7153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.607595 3977.000000 -217.683544)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7178" ObjectName="SW-CX_YIZ.CX_YIZ_622LCSW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="7178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.607595 4315.000000 -216.683544)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7180" ObjectName="SW-CX_YIZ.CX_YIZ_623LCSW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="7180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.607595 4668.000000 -213.683544)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7176" ObjectName="SW-CX_YIZ.CX_YIZ_624LCSW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="7176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42930">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 -230.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7177" ObjectName="SW-CX_YIZ.CX_YIZ_622CKSW"/>
     <cge:Meas_Ref ObjectId="42930"/>
    <cge:TPSR_Ref TObjectID="7177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42941">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -229.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7179" ObjectName="SW-CX_YIZ.CX_YIZ_623CKSW"/>
     <cge:Meas_Ref ObjectId="42941"/>
    <cge:TPSR_Ref TObjectID="7179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42919">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -226.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7175" ObjectName="SW-CX_YIZ.CX_YIZ_624CKSW"/>
     <cge:Meas_Ref ObjectId="42919"/>
    <cge:TPSR_Ref TObjectID="7175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.000000 -748.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -802.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -716.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -901.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -913.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -840.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -841.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42874">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4153.500000 -461.500000)" xlink:href="#switch2:shape22_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7181" ObjectName="SW-CX_YIZ.CX_YIZ_6022SW"/>
     <cge:Meas_Ref ObjectId="42874"/>
    <cge:TPSR_Ref TObjectID="7181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42952">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3785.500000 -427.500000)" xlink:href="#switch2:shape22_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10672" ObjectName="SW-CX_YIZ.CX_YIZ_6011SW"/>
     <cge:Meas_Ref ObjectId="42952"/>
    <cge:TPSR_Ref TObjectID="10672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4571.500000 -648.500000)" xlink:href="#switch2:shape22_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 -973.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 -884.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.607595 5271.000000 -818.683544)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5188.000000 -831.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5428.000000 -964.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5428.000000 -877.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.607595 5561.000000 -811.683544)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5478.000000 -824.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5726.000000 -973.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5726.000000 -884.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.607595 5859.000000 -818.683544)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5776.000000 -831.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 -1102.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 -1030.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5427.000000 -1034.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c1e680">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3939.000000 -520.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c6dd90">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -139.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c34910">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -136.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bacb00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -134.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b79860">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.076923 4728.000000 -535.923077)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b7bb40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 -671.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d1980">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5183.000000 -739.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3216c50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5473.000000 -732.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324d670">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5771.000000 -739.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YIZ" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yiwan_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3776,-1165 3776,-1108 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11713" ObjectName="AC-110kV.yiwan_line"/>
    <cge:TPSR_Ref TObjectID="11713_SS-52"/></metadata>
   <polyline fill="none" opacity="0" points="3776,-1165 3776,-1108 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YIZ.CX_YIZ_T1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="14954"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -584.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -584.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="10820" ObjectName="TF-CX_YIZ.CX_YIZ_T1"/>
    <cge:TPSR_Ref TObjectID="10820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.989899 3650.000000 -80.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -0.989899 3650.000000 -80.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.979798 4865.000000 -79.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -0.979798 4865.000000 -79.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.979798 6033.000000 -649.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.979798 6033.000000 -649.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_YIZ.CX_YZ_GN2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -164.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15893" ObjectName="SM-CX_YIZ.CX_YZ_GN2"/>
    <cge:TPSR_Ref TObjectID="15893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YIZ.CX_YZ_GN3">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -161.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15894" ObjectName="SM-CX_YIZ.CX_YZ_GN3"/>
    <cge:TPSR_Ref TObjectID="15894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YIZ.CX_YZ_GN1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -159.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15892" ObjectName="SM-CX_YIZ.CX_YZ_GN1"/>
    <cge:TPSR_Ref TObjectID="15892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -764.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5410.000000 -757.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5708.000000 -764.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_2bbb670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-826 3776,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7137@0" ObjectIDZND0="7140@0" Pin0InfoVect0LinkObjId="SW-42852_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa3ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-826 3776,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bba660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-268 3854,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="10831@x" ObjectIDND1="7178@x" ObjectIDND2="7177@x" ObjectIDZND0="15893@0" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42940_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-42930_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-268 3854,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bbaaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-826 3776,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7137@0" ObjectIDZND0="7150@1" Pin0InfoVect0LinkObjId="SW-42862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa3ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-826 3776,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c58bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-752 3799,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7148@x" ObjectIDND1="7150@x" ObjectIDZND0="7151@0" Pin0InfoVect0LinkObjId="SW-42863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42860_0" Pin1InfoVect1LinkObjId="SW-42862_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-752 3799,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c58da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3835,-752 3845,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7151@1" ObjectIDZND0="g_2c58f90@0" Pin0InfoVect0LinkObjId="g_2c58f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3835,-752 3845,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c63770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-752 3776,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7151@x" ObjectIDND1="7148@x" ObjectIDZND0="7150@0" Pin0InfoVect0LinkObjId="SW-42862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42863_0" Pin1InfoVect1LinkObjId="SW-42860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-752 3776,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c63960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-733 3776,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7148@1" ObjectIDZND0="7151@x" ObjectIDZND1="7150@x" Pin0InfoVect0LinkObjId="SW-42863_0" Pin0InfoVect1LinkObjId="SW-42862_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-733 3776,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c65200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-897 3799,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7139@x" ObjectIDND1="7140@x" ObjectIDZND0="7142@0" Pin0InfoVect0LinkObjId="SW-42854_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42851_0" Pin1InfoVect1LinkObjId="SW-42852_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-897 3799,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-883 3776,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7140@1" ObjectIDZND0="7142@x" ObjectIDZND1="7139@x" Pin0InfoVect0LinkObjId="SW-42854_0" Pin0InfoVect1LinkObjId="SW-42851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42852_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-883 3776,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c44870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-897 3776,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7142@x" ObjectIDND1="7140@x" ObjectIDZND0="7139@0" Pin0InfoVect0LinkObjId="SW-42851_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42854_0" Pin1InfoVect1LinkObjId="SW-42852_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-897 3776,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b9ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-880 4027,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7137@0" ObjectIDND1="7145@x" ObjectIDZND0="7146@0" Pin0InfoVect0LinkObjId="SW-42858_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2aa3ab0_0" Pin1InfoVect1LinkObjId="SW-42857_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-880 4027,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b9ad40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-880 4071,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7146@1" ObjectIDZND0="g_2b9af30@0" Pin0InfoVect0LinkObjId="g_2b9af30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-880 4071,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c11d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-960 3798,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7139@x" ObjectIDND1="7141@x" ObjectIDZND0="7143@0" Pin0InfoVect0LinkObjId="SW-42855_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42851_0" Pin1InfoVect1LinkObjId="SW-42853_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-960 3798,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2be46a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-942 3776,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7139@1" ObjectIDZND0="7143@x" ObjectIDZND1="7141@x" Pin0InfoVect0LinkObjId="SW-42855_0" Pin0InfoVect1LinkObjId="SW-42853_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-942 3776,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2be4890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-960 3776,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7143@x" ObjectIDND1="7139@x" ObjectIDZND0="7141@0" Pin0InfoVect0LinkObjId="SW-42853_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42855_0" Pin1InfoVect1LinkObjId="SW-42851_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-960 3776,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2be6870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3834,-1032 3845,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7144@1" ObjectIDZND0="g_2b082f0@0" Pin0InfoVect0LinkObjId="g_2b082f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3834,-1032 3845,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bad180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-976 4022,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2a7de00@0" ObjectIDND1="g_2b6e6b0@0" ObjectIDND2="7145@x" ObjectIDZND0="7147@0" Pin0InfoVect0LinkObjId="SW-42859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a7de00_0" Pin1InfoVect1LinkObjId="g_2b6e6b0_0" Pin1InfoVect2LinkObjId="SW-42857_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-976 4022,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bad370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4058,-976 4071,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7147@1" ObjectIDZND0="g_2bad560@0" Pin0InfoVect0LinkObjId="g_2bad560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4058,-976 4071,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bae2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-826 4000,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7137@0" ObjectIDZND0="7145@x" ObjectIDZND1="7146@x" Pin0InfoVect0LinkObjId="SW-42857_0" Pin0InfoVect1LinkObjId="SW-42858_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa3ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-826 4000,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bae4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-880 4000,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7137@0" ObjectIDND1="7146@x" ObjectIDZND0="7145@0" Pin0InfoVect0LinkObjId="SW-42857_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2aa3ab0_0" Pin1InfoVect1LinkObjId="SW-42858_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-880 4000,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2baf9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-706 3776,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="7148@0" ObjectIDZND0="10820@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-706 3776,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c21aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-751 4168,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7154@x" ObjectIDND1="7152@x" ObjectIDZND0="7155@0" Pin0InfoVect0LinkObjId="SW-42867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42866_0" Pin1InfoVect1LinkObjId="SW-42864_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-751 4168,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c21cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-751 4219,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7155@1" ObjectIDZND0="g_2c21ee0@0" Pin0InfoVect0LinkObjId="g_2c21ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-751 4219,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c22810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-751 4145,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7155@x" ObjectIDND1="7152@x" ObjectIDZND0="7154@0" Pin0InfoVect0LinkObjId="SW-42866_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42867_0" Pin1InfoVect1LinkObjId="SW-42864_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-751 4145,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c2fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-732 4145,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7152@1" ObjectIDZND0="7154@x" ObjectIDZND1="7155@x" Pin0InfoVect0LinkObjId="SW-42866_0" Pin0InfoVect1LinkObjId="SW-42867_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-732 4145,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c1bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-705 4145,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer" ObjectIDND0="7152@0" ObjectIDZND0="10821@1" Pin0InfoVect0LinkObjId="g_2b657b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-705 4145,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c58800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-397 3854,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10830@0" ObjectIDZND0="7173@0" Pin0InfoVect0LinkObjId="g_2c371c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-397 3854,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bb1390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-320 3824,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="10831@x" ObjectIDND1="8753@x" ObjectIDZND0="g_2a82190@0" Pin0InfoVect0LinkObjId="g_2a82190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42940_0" Pin1InfoVect1LinkObjId="SW-42939_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-320 3824,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c732e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-320 3854,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a82190@0" ObjectIDND1="8753@x" ObjectIDZND0="10831@0" Pin0InfoVect0LinkObjId="SW-42940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a82190_0" Pin1InfoVect1LinkObjId="SW-42939_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-320 3854,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c73500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-290 3854,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10831@1" ObjectIDZND0="15893@x" ObjectIDZND1="7178@x" ObjectIDZND2="7177@x" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN2_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-42930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-290 3854,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c73e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-268 3904,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="15893@x" ObjectIDND1="10831@x" ObjectIDZND0="7178@x" ObjectIDZND1="7177@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-42930_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN2_0" Pin1InfoVect1LinkObjId="SW-42940_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-268 3904,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c6f740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-224 3936,-224 3936,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b4b4a0@0" ObjectIDND1="7177@x" ObjectIDZND0="g_2b958f0@0" Pin0InfoVect0LinkObjId="g_2b958f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4b4a0_0" Pin1InfoVect1LinkObjId="SW-42930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-224 3936,-224 3936,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c708d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-413 3665,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7173@0" ObjectIDZND0="7184@1" Pin0InfoVect0LinkObjId="SW-42957_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c58800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-413 3665,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-361 3665,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7184@0" ObjectIDZND0="g_2bfb0d0@0" ObjectIDZND1="g_2ad2d80@0" Pin0InfoVect0LinkObjId="g_2bfb0d0_0" Pin0InfoVect1LinkObjId="g_2ad2d80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-361 3665,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc34b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-291 3665,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7183@1" ObjectIDZND0="g_2bfb0d0@0" ObjectIDZND1="g_2ad2d80@0" Pin0InfoVect0LinkObjId="g_2bfb0d0_0" Pin0InfoVect1LinkObjId="g_2ad2d80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-291 3665,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc36d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-255 3632,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7183@x" ObjectIDND1="g_2acd3f0@0" ObjectIDZND0="g_2b94c30@0" Pin0InfoVect0LinkObjId="g_2b94c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42956_0" Pin1InfoVect1LinkObjId="g_2acd3f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-255 3632,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc4160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-255 3665,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2b94c30@0" ObjectIDND1="g_2acd3f0@0" ObjectIDZND0="7183@0" Pin0InfoVect0LinkObjId="SW-42956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b94c30_0" Pin1InfoVect1LinkObjId="g_2acd3f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-255 3665,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b914f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-265 4192,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="10833@x" ObjectIDND1="7180@x" ObjectIDND2="7179@x" ObjectIDZND0="15894@0" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42951_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-42941_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-265 4192,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c423e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-317 4162,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="10833@x" ObjectIDND1="8754@x" ObjectIDZND0="g_2a80630@0" Pin0InfoVect0LinkObjId="g_2a80630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42951_0" Pin1InfoVect1LinkObjId="SW-42950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-317 4162,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c42600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-287 4192,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10833@1" ObjectIDZND0="15894@x" ObjectIDZND1="7180@x" ObjectIDZND2="7179@x" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN3_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-42941_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42951_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-287 4192,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c42820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-265 4242,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="15894@x" ObjectIDND1="10833@x" ObjectIDZND0="7180@x" ObjectIDZND1="7179@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-42941_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN3_0" Pin1InfoVect1LinkObjId="SW-42951_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-265 4242,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c362c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-221 4274,-221 4274,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b4cc10@0" ObjectIDND1="7179@x" ObjectIDZND0="g_2b96660@0" Pin0InfoVect0LinkObjId="g_2b96660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4cc10_0" Pin1InfoVect1LinkObjId="SW-42941_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-221 4274,-221 4274,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c371c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-394 4192,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10832@0" ObjectIDZND0="7173@0" Pin0InfoVect0LinkObjId="g_2c58800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42951_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-394 4192,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c3b890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-317 4192,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a80630@0" ObjectIDND1="8754@x" ObjectIDZND0="10833@0" Pin0InfoVect0LinkObjId="SW-42951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a80630_0" Pin1InfoVect1LinkObjId="SW-42950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-317 4192,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ba9220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-263 4545,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="10829@x" ObjectIDND1="7176@x" ObjectIDND2="7175@x" ObjectIDZND0="15892@0" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42929_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-42919_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-263 4545,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bac050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-315 4515,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="10829@x" ObjectIDND1="8752@x" ObjectIDZND0="g_2a813e0@0" Pin0InfoVect0LinkObjId="g_2a813e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42929_0" Pin1InfoVect1LinkObjId="SW-42928_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-315 4515,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bac2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-285 4545,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10829@1" ObjectIDZND0="15892@x" ObjectIDZND1="7176@x" ObjectIDZND2="7175@x" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN1_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-42919_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-285 4545,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bac500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-263 4595,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10829@x" ObjectIDND1="15892@x" ObjectIDZND0="7176@x" ObjectIDZND1="7175@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-42919_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42929_0" Pin1InfoVect1LinkObjId="SM-CX_YIZ.CX_YZ_GN1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-263 4595,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c15f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-219 4627,-219 4627,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b53e30@0" ObjectIDND1="7175@x" ObjectIDZND0="g_2b973d0@0" Pin0InfoVect0LinkObjId="g_2b973d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b53e30_0" Pin1InfoVect1LinkObjId="SW-42919_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-219 4627,-219 4627,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c16530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-390 4545,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10828@0" ObjectIDZND0="7174@0" Pin0InfoVect0LinkObjId="g_2b726c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-390 4545,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c16720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-315 4545,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a813e0@0" ObjectIDND1="8752@x" ObjectIDZND0="10829@0" Pin0InfoVect0LinkObjId="SW-42929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a813e0_0" Pin1InfoVect1LinkObjId="SW-42928_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-315 4545,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c18d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-413 4880,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7174@0" ObjectIDZND0="7186@1" Pin0InfoVect0LinkObjId="SW-42959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c16530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-413 4880,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c18fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-360 4880,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7186@0" ObjectIDZND0="g_2ad3a00@0" ObjectIDZND1="g_2bc89b0@0" Pin0InfoVect0LinkObjId="g_2ad3a00_0" Pin0InfoVect1LinkObjId="g_2bc89b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-360 4880,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c19770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-290 4880,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7185@1" ObjectIDZND0="g_2ad3a00@0" ObjectIDZND1="g_2bc89b0@0" Pin0InfoVect0LinkObjId="g_2ad3a00_0" Pin0InfoVect1LinkObjId="g_2bc89b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42958_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-290 4880,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c199d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-254 4847,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7185@x" ObjectIDND1="g_2acc9d0@0" ObjectIDZND0="g_2b98140@0" Pin0InfoVect0LinkObjId="g_2b98140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42958_0" Pin1InfoVect1LinkObjId="g_2acc9d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-254 4847,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2c19c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-254 4880,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2b98140@0" ObjectIDND1="g_2acc9d0@0" ObjectIDZND0="7185@0" Pin0InfoVect0LinkObjId="SW-42958_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b98140_0" Pin1InfoVect1LinkObjId="g_2acc9d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-254 4880,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a7d940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-1002 4000,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2a7de00@0" ObjectIDZND0="7147@x" ObjectIDZND1="g_2b6e6b0@0" ObjectIDZND2="7145@x" Pin0InfoVect0LinkObjId="SW-42859_0" Pin0InfoVect1LinkObjId="g_2b6e6b0_0" Pin0InfoVect2LinkObjId="SW-42857_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7de00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-1002 4000,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a7dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="4145,-826 4145,-976 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="7137@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa3ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-826 4145,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf9db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3700,-650 3700,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="10820@x" ObjectIDND1="g_2b0b9d0@0" ObjectIDND2="g_2bf6a90@0" ObjectIDZND0="7149@1" Pin0InfoVect0LinkObjId="SW-42861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2baf9c0_0" Pin1InfoVect1LinkObjId="g_2b0b9d0_0" Pin1InfoVect2LinkObjId="g_2bf6a90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3700,-650 3700,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bfa010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3700,-595 3700,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7149@0" ObjectIDZND0="g_2b08ad0@0" Pin0InfoVect0LinkObjId="g_2b08ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3700,-595 3700,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bfaee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-650 3700,-650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10820@x" ObjectIDZND0="7149@x" ObjectIDZND1="g_2b0b9d0@0" ObjectIDZND2="g_2bf6a90@0" Pin0InfoVect0LinkObjId="SW-42861_0" Pin0InfoVect1LinkObjId="g_2b0b9d0_0" Pin0InfoVect2LinkObjId="g_2bf6a90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2baf9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-650 3700,-650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bfb820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-351 3665,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7184@x" ObjectIDND1="g_2ad2d80@0" ObjectIDZND0="g_2bfb0d0@1" Pin0InfoVect0LinkObjId="g_2bfb0d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42957_0" Pin1InfoVect1LinkObjId="g_2ad2d80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-351 3665,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bfba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-313 3665,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2bfb0d0@0" ObjectIDZND0="7183@x" ObjectIDZND1="g_2ad2d80@0" Pin0InfoVect0LinkObjId="SW-42956_0" Pin0InfoVect1LinkObjId="g_2ad2d80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bfb0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-313 3665,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bfbce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-351 3696,-351 3696,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7184@x" ObjectIDND1="g_2bfb0d0@0" ObjectIDZND0="g_2ad2d80@0" Pin0InfoVect0LinkObjId="g_2ad2d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42957_0" Pin1InfoVect1LinkObjId="g_2bfb0d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-351 3696,-351 3696,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bfbf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-313 3696,-305 3665,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2ad2d80@1" ObjectIDZND0="7183@x" ObjectIDZND1="g_2bfb0d0@0" Pin0InfoVect0LinkObjId="SW-42956_0" Pin0InfoVect1LinkObjId="g_2bfb0d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad2d80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-313 3696,-305 3665,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b4bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-224 3904,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b958f0@0" ObjectIDND1="7177@x" ObjectIDZND0="g_2b4b4a0@1" Pin0InfoVect0LinkObjId="g_2b4b4a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b958f0_0" Pin1InfoVect1LinkObjId="SW-42930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-224 3904,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b4bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-175 3904,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b4b4a0@0" ObjectIDZND0="g_2c6dd90@0" Pin0InfoVect0LinkObjId="g_2c6dd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4b4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-175 3904,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b4c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-133 3984,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c700b0@0" ObjectIDZND0="g_2b4c130@0" Pin0InfoVect0LinkObjId="g_2b4c130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c700b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-133 3984,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b4d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-158 4242,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2c34910@0" ObjectIDZND0="g_2b4cc10@0" Pin0InfoVect0LinkObjId="g_2b4cc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c34910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-158 4242,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b4d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-204 4242,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b4cc10@1" ObjectIDZND0="g_2b96660@0" ObjectIDZND1="7179@x" Pin0InfoVect0LinkObjId="g_2b96660_0" Pin0InfoVect1LinkObjId="SW-42941_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4cc10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-204 4242,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b4e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4322,-133 4322,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c364e0@0" ObjectIDZND0="g_2b4d950@0" Pin0InfoVect0LinkObjId="g_2b4d950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c364e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4322,-133 4322,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b52110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-651 4067,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="10821@x" ObjectIDND1="g_2b0dea0@0" ObjectIDND2="g_2b4eda0@0" ObjectIDZND0="7153@1" Pin0InfoVect0LinkObjId="SW-42865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c1bb20_0" Pin1InfoVect1LinkObjId="g_2b0dea0_0" Pin1InfoVect2LinkObjId="g_2b4eda0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-651 4067,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b52370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-596 4067,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7153@0" ObjectIDZND0="g_2b09520@0" Pin0InfoVect0LinkObjId="g_2b09520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-596 4067,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b525d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-651 4067,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10821@x" ObjectIDZND0="7153@x" ObjectIDZND1="g_2b0dea0@0" ObjectIDZND2="g_2b4eda0@0" Pin0InfoVect0LinkObjId="SW-42865_0" Pin0InfoVect1LinkObjId="g_2b0dea0_0" Pin0InfoVect2LinkObjId="g_2b4eda0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c1bb20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-651 4067,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc7580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-219 4595,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b973d0@0" ObjectIDND1="7175@x" ObjectIDZND0="g_2b53e30@1" Pin0InfoVect0LinkObjId="g_2b53e30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b973d0_0" Pin1InfoVect1LinkObjId="SW-42919_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-219 4595,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc77b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-172 4595,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b53e30@0" ObjectIDZND0="g_2bacb00@0" Pin0InfoVect0LinkObjId="g_2bacb00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b53e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-172 4595,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc8290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-132 4675,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c161a0@0" ObjectIDZND0="g_2bc7a10@0" Pin0InfoVect0LinkObjId="g_2bc7a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c161a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-132 4675,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc84f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-350 4911,-350 4911,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7186@x" ObjectIDND1="g_2bc89b0@0" ObjectIDZND0="g_2ad3a00@0" Pin0InfoVect0LinkObjId="g_2ad3a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42959_0" Pin1InfoVect1LinkObjId="g_2bc89b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-350 4911,-350 4911,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc8750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-312 4911,-304 4880,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2ad3a00@1" ObjectIDZND0="7185@x" ObjectIDZND1="g_2bc89b0@0" Pin0InfoVect0LinkObjId="SW-42958_0" Pin0InfoVect1LinkObjId="g_2bc89b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad3a00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-312 4911,-304 4880,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc9230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-350 4880,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7186@x" ObjectIDND1="g_2ad3a00@0" ObjectIDZND0="g_2bc89b0@1" Pin0InfoVect0LinkObjId="g_2bc89b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42959_0" Pin1InfoVect1LinkObjId="g_2ad3a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-350 4880,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc9490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-311 4880,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2bc89b0@0" ObjectIDZND0="7185@x" ObjectIDZND1="g_2ad3a00@0" Pin0InfoVect0LinkObjId="SW-42958_0" Pin0InfoVect1LinkObjId="g_2ad3a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc89b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-311 4880,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bc9f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-526 4744,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2bc96f0@1" ObjectIDZND0="g_2b79860@0" Pin0InfoVect0LinkObjId="g_2b79860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc96f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-526 4744,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bcb110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-693 4641,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2b7bb40@0" ObjectIDZND0="g_2bcab40@0" Pin0InfoVect0LinkObjId="g_2bcab40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b7bb40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-693 4641,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b82d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-315 4545,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="10829@x" ObjectIDND1="g_2a813e0@0" ObjectIDZND0="8752@0" Pin0InfoVect0LinkObjId="SW-42928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42929_0" Pin1InfoVect1LinkObjId="g_2a813e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-315 4545,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b82f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-359 4545,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8752@1" ObjectIDZND0="10828@1" Pin0InfoVect0LinkObjId="SW-42929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-359 4545,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b83140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-317 4192,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="10833@x" ObjectIDND1="g_2a80630@0" ObjectIDZND0="8754@0" Pin0InfoVect0LinkObjId="SW-42950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42951_0" Pin1InfoVect1LinkObjId="g_2a80630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-317 4192,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b83330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-361 4192,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8754@1" ObjectIDZND0="10832@1" Pin0InfoVect0LinkObjId="SW-42951_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-361 4192,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b84db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-320 3854,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2a82190@0" ObjectIDND1="10831@x" ObjectIDZND0="8753@0" Pin0InfoVect0LinkObjId="SW-42939_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a82190_0" Pin1InfoVect1LinkObjId="SW-42940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-320 3854,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b84fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-360 3854,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8753@1" ObjectIDZND0="10830@1" Pin0InfoVect0LinkObjId="SW-42940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42939_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-360 3854,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa3ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-803 4145,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7154@1" ObjectIDZND0="7137@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-803 4145,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2bdf560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-256 3984,-268 3904,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="7178@0" ObjectIDZND0="15893@x" ObjectIDZND1="10831@x" ObjectIDZND2="7177@x" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN2_0" Pin0InfoVect1LinkObjId="SW-42940_0" Pin0InfoVect2LinkObjId="SW-42930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-256 3984,-268 3904,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b17ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-252 4675,-263 4595,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="7176@0" ObjectIDZND0="10829@x" ObjectIDZND1="15892@x" ObjectIDZND2="7175@x" Pin0InfoVect0LinkObjId="SW-42929_0" Pin0InfoVect1LinkObjId="SM-CX_YIZ.CX_YZ_GN1_0" Pin0InfoVect2LinkObjId="SW-42919_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-252 4675,-263 4595,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b18250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4322,-255 4322,-265 4242,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="7180@0" ObjectIDZND0="15894@x" ObjectIDZND1="10833@x" ObjectIDZND2="7179@x" Pin0InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN3_0" Pin0InfoVect1LinkObjId="SW-42951_0" Pin0InfoVect2LinkObjId="SW-42941_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4322,-255 4322,-265 4242,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b1b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-268 3904,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15893@x" ObjectIDND1="10831@x" ObjectIDND2="7178@x" ObjectIDZND0="7177@0" Pin0InfoVect0LinkObjId="SW-42930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN2_0" Pin1InfoVect1LinkObjId="SW-42940_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-268 3904,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b1b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-237 3904,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7177@1" ObjectIDZND0="g_2b958f0@0" ObjectIDZND1="g_2b4b4a0@0" Pin0InfoVect0LinkObjId="g_2b958f0_0" Pin0InfoVect1LinkObjId="g_2b4b4a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-237 3904,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b1e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-265 4242,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15894@x" ObjectIDND1="10833@x" ObjectIDND2="7180@x" ObjectIDZND0="7179@0" Pin0InfoVect0LinkObjId="SW-42941_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_YIZ.CX_YZ_GN3_0" Pin1InfoVect1LinkObjId="SW-42951_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-265 4242,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b1e690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-236 4242,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7179@1" ObjectIDZND0="g_2b96660@0" ObjectIDZND1="g_2b4cc10@0" Pin0InfoVect0LinkObjId="g_2b96660_0" Pin0InfoVect1LinkObjId="g_2b4cc10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42941_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-236 4242,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b299d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-263 4595,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="10829@x" ObjectIDND1="15892@x" ObjectIDND2="7176@x" ObjectIDZND0="7175@0" Pin0InfoVect0LinkObjId="SW-42919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42929_0" Pin1InfoVect1LinkObjId="SM-CX_YIZ.CX_YZ_GN1_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-263 4595,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b29c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-233 4595,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7175@1" ObjectIDZND0="g_2b973d0@0" ObjectIDZND1="g_2b53e30@0" Pin0InfoVect0LinkObjId="g_2b973d0_0" Pin0InfoVect1LinkObjId="g_2b53e30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-233 4595,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2b960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4550,-748 4562,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2b2aea0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2aea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4550,-748 4562,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b40610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-766 4706,-783 4671,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_2a7f8c0@0" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7f8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-766 4706,-783 4671,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b40870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-783 4671,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_2a7f8c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a7f8c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-783 4671,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b40ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-740 4641,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bcab40@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcab40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-740 4641,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b40d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-772 4641,-783 4671,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="0@0" ObjectIDZND0="g_2a7f8c0@0" ObjectIDZND1="0@0" Pin0InfoVect0LinkObjId="g_2a7f8c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-772 4641,-783 4671,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b445c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-835 4562,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-835 4562,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b47ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-748 4562,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2b2aea0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b2aea0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-748 4562,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b55230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-809 4562,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-809 4562,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b55490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-766 4562,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2b2aea0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b2aea0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-766 4562,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b564a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-959 4591,-935 4562,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2bcc120@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2ad4680@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2ad4680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcc120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-959 4591,-935 4562,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b59db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-935 4562,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bcc120@0" ObjectIDND1="g_2ad4680@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bcc120_0" Pin1InfoVect1LinkObjId="g_2ad4680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-935 4562,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b60090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-847 4562,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-847 4562,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b633b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-848 4737,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-848 4737,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b04df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-920 4737,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-920 4737,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b05050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-881 4737,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-881 4737,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b052b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-908 4562,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-908 4562,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b05510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-871 4562,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-871 4562,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b0c240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-650 3624,-650 3624,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7149@x" ObjectIDND1="10820@x" ObjectIDND2="g_2bf6a90@0" ObjectIDZND0="g_2b0b9d0@1" Pin0InfoVect0LinkObjId="g_2b0b9d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42861_0" Pin1InfoVect1LinkObjId="g_2baf9c0_0" Pin1InfoVect2LinkObjId="g_2bf6a90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-650 3624,-650 3624,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b0cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3700,-650 3661,-650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7149@x" ObjectIDND1="10820@x" ObjectIDZND0="g_2b0b9d0@0" ObjectIDZND1="g_2bf6a90@0" Pin0InfoVect0LinkObjId="g_2b0b9d0_0" Pin0InfoVect1LinkObjId="g_2bf6a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42861_0" Pin1InfoVect1LinkObjId="g_2baf9c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3700,-650 3661,-650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b0cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-650 3661,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2b0b9d0@0" ObjectIDND1="7149@x" ObjectIDND2="10820@x" ObjectIDZND0="g_2bf6a90@0" Pin0InfoVect0LinkObjId="g_2bf6a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b0b9d0_0" Pin1InfoVect1LinkObjId="SW-42861_0" Pin1InfoVect2LinkObjId="g_2baf9c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-650 3661,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b0dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3624,-596 3624,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2b0b9d0@0" ObjectIDZND0="g_2b0d1f0@0" Pin0InfoVect0LinkObjId="g_2b0d1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0b9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3624,-596 3624,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b0f160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-600 3997,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2b0dea0@0" ObjectIDZND0="g_2b0e710@0" Pin0InfoVect0LinkObjId="g_2b0e710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0dea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-600 3997,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b0f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4028,-651 3997,-651 3997,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7153@x" ObjectIDND1="10821@x" ObjectIDND2="g_2b4eda0@0" ObjectIDZND0="g_2b0dea0@1" Pin0InfoVect0LinkObjId="g_2b0dea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42865_0" Pin1InfoVect1LinkObjId="g_2c1bb20_0" Pin1InfoVect2LinkObjId="g_2b4eda0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4028,-651 3997,-651 3997,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b0feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-651 4028,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7153@x" ObjectIDND1="10821@x" ObjectIDZND0="g_2b0dea0@0" ObjectIDZND1="g_2b4eda0@0" Pin0InfoVect0LinkObjId="g_2b0dea0_0" Pin0InfoVect1LinkObjId="g_2b4eda0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42865_0" Pin1InfoVect1LinkObjId="g_2c1bb20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-651 4028,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b10110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4028,-651 4028,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="g_2b0dea0@0" ObjectIDND1="7153@x" ObjectIDND2="10821@x" ObjectIDZND0="g_2b4eda0@0" Pin0InfoVect0LinkObjId="g_2b4eda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b0dea0_0" Pin1InfoVect1LinkObjId="SW-42865_0" Pin1InfoVect2LinkObjId="g_2c1bb20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4028,-651 4028,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b10c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-180 3984,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b4c130@1" ObjectIDZND0="7178@x" ObjectIDZND1="g_2b110c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2b110c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4c130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-180 3984,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b10e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-214 3984,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7178@1" ObjectIDZND0="g_2b4c130@0" ObjectIDZND1="g_2b110c0@0" Pin0InfoVect0LinkObjId="g_2b4c130_0" Pin0InfoVect1LinkObjId="g_2b110c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-214 3984,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ac9a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-190 4016,-190 4016,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b4c130@0" ObjectIDND1="7178@x" ObjectIDZND0="g_2b110c0@1" Pin0InfoVect0LinkObjId="g_2b110c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4c130_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-190 4016,-190 4016,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ac9ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-126 4016,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b110c0@0" ObjectIDZND0="g_2c74090@0" Pin0InfoVect0LinkObjId="g_2c74090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b110c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-126 4016,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ac9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4354,1 4354,-6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b11ae0@0" Pin0InfoVect0LinkObjId="g_2b11ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4354,1 4354,-6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2aca160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4354,-59 4354,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b11ae0@1" ObjectIDZND0="g_2c42a40@1" Pin0InfoVect0LinkObjId="g_2c42a40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b11ae0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4354,-59 4354,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4322,-181 4322,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b4d950@1" ObjectIDZND0="7180@x" ObjectIDZND1="g_2b12500@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2b12500_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4d950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4322,-181 4322,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4322,-213 4322,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7180@1" ObjectIDZND0="g_2b4d950@0" ObjectIDZND1="g_2b12500@0" Pin0InfoVect0LinkObjId="g_2b4d950_0" Pin0InfoVect1LinkObjId="g_2b12500_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4322,-213 4322,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acb0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4354,-115 4354,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c42a40@0" ObjectIDZND0="g_2b12500@0" Pin0InfoVect0LinkObjId="g_2b12500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c42a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4354,-115 4354,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acb330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4354,-182 4354,-190 4322,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b12500@1" ObjectIDZND0="g_2b4d950@0" ObjectIDZND1="7180@x" Pin0InfoVect0LinkObjId="g_2b4d950_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b12500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4354,-182 4354,-190 4322,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acde10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-174 3665,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2acd3f0@0" Pin0InfoVect0LinkObjId="g_2acd3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-174 3665,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ace070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-240 3665,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2acd3f0@1" ObjectIDZND0="7183@x" ObjectIDZND1="g_2b94c30@0" Pin0InfoVect0LinkObjId="SW-42956_0" Pin0InfoVect1LinkObjId="g_2b94c30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acd3f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-240 3665,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ace2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-79 3665,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-79 3665,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acedc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-181 4675,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2bc7a10@1" ObjectIDZND0="7176@x" ObjectIDZND1="g_2acbfb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2acbfb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc7a10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-181 4675,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acf020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-210 4675,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7176@1" ObjectIDZND0="g_2bc7a10@0" ObjectIDZND1="g_2acbfb0@0" Pin0InfoVect0LinkObjId="g_2bc7a10_0" Pin0InfoVect1LinkObjId="g_2acbfb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-210 4675,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acf280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4708,0 4708,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2acb590@0" Pin0InfoVect0LinkObjId="g_2acb590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4708,0 4708,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acf4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4708,-61 4708,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2acb590@1" ObjectIDZND0="g_2bac760@1" Pin0InfoVect0LinkObjId="g_2bac760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acb590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4708,-61 4708,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acf740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-111 4707,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2bac760@0" ObjectIDZND0="g_2acbfb0@0" Pin0InfoVect0LinkObjId="g_2acbfb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bac760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-111 4707,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acf9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-179 4707,-188 4675,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2acbfb0@1" ObjectIDZND0="g_2bc7a10@0" ObjectIDZND1="7176@x" Pin0InfoVect0LinkObjId="g_2bc7a10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acbfb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-179 4707,-188 4675,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acfc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-170 4880,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2acc9d0@0" Pin0InfoVect0LinkObjId="g_2acc9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-170 4880,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2acfe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-244 4880,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_2acc9d0@1" ObjectIDZND0="7185@x" ObjectIDZND1="g_2b98140@0" Pin0InfoVect0LinkObjId="SW-42958_0" Pin0InfoVect1LinkObjId="g_2b98140_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acc9d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-244 4880,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ad28c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,3 4016,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2ad1f60@0" Pin0InfoVect0LinkObjId="g_2ad1f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,3 4016,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ad2b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-57 4016,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2ad1f60@1" ObjectIDZND0="g_2c74090@1" Pin0InfoVect0LinkObjId="g_2c74090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad1f60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-57 4016,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ad5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-935 4562,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2bcc120@0" ObjectIDND1="0@x" ObjectIDZND0="g_2ad4680@0" Pin0InfoVect0LinkObjId="g_2ad4680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bcc120_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-935 4562,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ad6440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-468 3955,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="10822@0" ObjectIDZND0="g_2b530c0@0" Pin0InfoVect0LinkObjId="g_2b530c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-468 3955,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ad66a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-511 3955,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b530c0@1" ObjectIDZND0="g_2c1e680@0" Pin0InfoVect0LinkObjId="g_2c1e680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b530c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-511 3955,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ad6900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-444 3908,-444 3908,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="7173@0" ObjectIDND1="10822@x" ObjectIDZND0="g_2a7eb10@0" Pin0InfoVect0LinkObjId="g_2a7eb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c58800_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-444 3908,-444 3908,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ad73f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-413 3931,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="7173@0" ObjectIDZND0="g_2a7eb10@0" ObjectIDZND1="10822@x" Pin0InfoVect0LinkObjId="g_2a7eb10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c58800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-413 3931,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ad7650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-444 3955,-444 3955,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="g_2a7eb10@0" ObjectIDND1="7173@0" ObjectIDZND0="10822@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a7eb10_0" Pin1InfoVect1LinkObjId="g_2c58800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-444 3955,-444 3955,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b657b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-579 4144,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" ObjectIDND0="g_2ad78b0@1" ObjectIDZND0="10821@0" Pin0InfoVect0LinkObjId="g_2c1bb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad78b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-579 4144,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b69780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4544,-413 4544,-449 4144,-449 4144,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7174@0" ObjectIDZND0="7181@0" Pin0InfoVect0LinkObjId="SW-42874_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c16530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4544,-413 4544,-449 4144,-449 4144,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b699f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-510 4144,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7181@1" ObjectIDZND0="g_2ad78b0@0" Pin0InfoVect0LinkObjId="g_2ad78b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42874_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-510 4144,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b6d9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-697 4562,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-697 4562,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b6e1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-589 3776,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="10820@0" ObjectIDZND0="10672@1" Pin0InfoVect0LinkObjId="SW-42952_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2baf9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-589 3776,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b6e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-432 3776,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10672@0" ObjectIDZND0="7173@0" Pin0InfoVect0LinkObjId="g_2c58800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-432 3776,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b70110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-961 4000,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b6e6b0@0" ObjectIDZND0="7145@x" ObjectIDZND1="7147@x" ObjectIDZND2="g_2a7de00@0" Pin0InfoVect0LinkObjId="SW-42857_0" Pin0InfoVect1LinkObjId="SW-42859_0" Pin0InfoVect2LinkObjId="g_2a7de00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b6e6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-961 4000,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b70c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-947 4000,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="7145@1" ObjectIDZND0="g_2b6e6b0@0" ObjectIDZND1="7147@x" ObjectIDZND2="g_2a7de00@0" Pin0InfoVect0LinkObjId="g_2b6e6b0_0" Pin0InfoVect1LinkObjId="SW-42859_0" Pin0InfoVect2LinkObjId="g_2a7de00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42857_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-947 4000,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b70e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-961 4000,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b6e6b0@0" ObjectIDND1="7145@x" ObjectIDZND0="7147@x" ObjectIDZND1="g_2a7de00@0" Pin0InfoVect0LinkObjId="SW-42859_0" Pin0InfoVect1LinkObjId="g_2a7de00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b6e6b0_0" Pin1InfoVect1LinkObjId="SW-42857_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-961 4000,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b710c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3834,-960 3845,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7143@1" ObjectIDZND0="g_2c11f60@0" Pin0InfoVect0LinkObjId="g_2c11f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3834,-960 3845,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b71320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3835,-897 3847,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7142@1" ObjectIDZND0="g_2c653f0@0" Pin0InfoVect0LinkObjId="g_2c653f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3835,-897 3847,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b71fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-631 4562,-631 4562,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b71580@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b71580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-631 4562,-631 4562,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b72200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-631 4199,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" ObjectIDND0="g_2b71580@1" ObjectIDZND0="10821@2" Pin0InfoVect0LinkObjId="g_2c1bb20_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b71580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-631 4199,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b72460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-493 4744,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bc96f0@0" ObjectIDZND0="10823@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc96f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-493 4744,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b726c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4695,-464 4695,-448 4722,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_2bd03c0@0" ObjectIDZND0="7174@0" ObjectIDZND1="10823@x" Pin0InfoVect0LinkObjId="g_2c16530_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd03c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4695,-464 4695,-448 4722,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b731b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-413 4721,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="7174@0" ObjectIDZND0="10823@x" ObjectIDZND1="g_2bd03c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2bd03c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c16530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-413 4721,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2b73410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-448 4744,-448 4744,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="7174@0" ObjectIDND1="g_2bd03c0@0" ObjectIDZND0="10823@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c16530_0" Pin1InfoVect1LinkObjId="g_2bd03c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-448 4744,-448 4744,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b73f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3762,-1070 3776,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b09f70@0" ObjectIDZND0="7144@x" ObjectIDZND1="7141@x" ObjectIDZND2="g_2b7e830@0" Pin0InfoVect0LinkObjId="SW-42856_0" Pin0InfoVect1LinkObjId="SW-42853_0" Pin0InfoVect2LinkObjId="g_2b7e830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b09f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3762,-1070 3776,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b74a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-1032 3776,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="7144@0" ObjectIDZND0="7141@x" ObjectIDZND1="g_2b09f70@0" ObjectIDZND2="g_2b7e830@0" Pin0InfoVect0LinkObjId="SW-42853_0" Pin0InfoVect1LinkObjId="g_2b09f70_0" Pin0InfoVect2LinkObjId="g_2b7e830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-1032 3776,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b75500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-1014 3776,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="7141@1" ObjectIDZND0="7144@x" ObjectIDZND1="g_2b09f70@0" ObjectIDZND2="g_2b7e830@0" Pin0InfoVect0LinkObjId="SW-42856_0" Pin0InfoVect1LinkObjId="g_2b09f70_0" Pin0InfoVect2LinkObjId="g_2b7e830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42853_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-1014 3776,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b75760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-1032 3776,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="7144@x" ObjectIDND1="7141@x" ObjectIDZND0="g_2b09f70@0" ObjectIDZND1="g_2b7e830@0" ObjectIDZND2="11713@1" Pin0InfoVect0LinkObjId="g_2b09f70_0" Pin0InfoVect1LinkObjId="g_2b7e830_0" Pin0InfoVect2LinkObjId="g_2b76710_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42856_0" Pin1InfoVect1LinkObjId="SW-42853_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-1032 3776,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b759c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-1096 3805,-1085 3776,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b7e830@0" ObjectIDZND0="g_2b09f70@0" ObjectIDZND1="7144@x" ObjectIDZND2="7141@x" Pin0InfoVect0LinkObjId="g_2b09f70_0" Pin0InfoVect1LinkObjId="SW-42856_0" Pin0InfoVect2LinkObjId="SW-42853_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b7e830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-1096 3805,-1085 3776,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b764b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-1070 3776,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2b09f70@0" ObjectIDND1="7144@x" ObjectIDND2="7141@x" ObjectIDZND0="g_2b7e830@0" ObjectIDZND1="11713@1" Pin0InfoVect0LinkObjId="g_2b7e830_0" Pin0InfoVect1LinkObjId="g_2b76710_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b09f70_0" Pin1InfoVect1LinkObjId="SW-42856_0" Pin1InfoVect2LinkObjId="SW-42853_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-1070 3776,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b76710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-1085 3776,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2b7e830@0" ObjectIDND1="g_2b09f70@0" ObjectIDND2="7144@x" ObjectIDZND0="11713@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b7e830_0" Pin1InfoVect1LinkObjId="g_2b09f70_0" Pin1InfoVect2LinkObjId="SW-42856_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-1085 3776,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31c8df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-868 5148,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-868 5148,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d0040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-920 5118,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_31d5290@0" Pin0InfoVect0LinkObjId="g_31d5290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-920 5118,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d02a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-890 5148,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-890 5148,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d0500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-868 5198,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-868 5198,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d3710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5198,-824 5230,-824 5230,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_31d6db0@0" ObjectIDZND0="g_31d6040@0" Pin0InfoVect0LinkObjId="g_31d6040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_31d6db0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5198,-824 5230,-824 5230,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d4820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-995 5148,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-995 5148,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d4a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-920 5148,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_31d5290@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_31d5290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-920 5148,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d7630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5198,-824 5198,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_31d6040@0" ObjectIDZND0="g_31d6db0@1" Pin0InfoVect0LinkObjId="g_31d6db0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_31d6040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5198,-824 5198,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d7890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5198,-777 5198,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_31d6db0@0" ObjectIDZND0="g_31d1980@0" Pin0InfoVect0LinkObjId="g_31d1980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d6db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5198,-777 5198,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d8370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5278,-737 5278,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_31d3970@0" ObjectIDZND0="g_31d7af0@0" Pin0InfoVect0LinkObjId="g_31d7af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d3970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5278,-737 5278,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d8f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-920 5148,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_31d5290@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_31d5290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-920 5148,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31d9120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-967 5148,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-967 5148,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31dcdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5278,-857 5278,-868 5198,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5278,-857 5278,-868 5198,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31dfd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5198,-868 5198,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5198,-868 5198,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31dffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5198,-838 5198,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_31d6db0@0" ObjectIDZND1="g_31d6040@0" Pin0InfoVect0LinkObjId="g_31d6db0_0" Pin0InfoVect1LinkObjId="g_31d6040_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5198,-838 5198,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31e1b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5278,-786 5278,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_31d7af0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_31e0f70@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_31e0f70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d7af0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5278,-786 5278,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31e1d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5278,-815 5278,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_31d7af0@0" ObjectIDZND1="g_31e0f70@0" Pin0InfoVect0LinkObjId="g_31d7af0_0" Pin0InfoVect1LinkObjId="g_31e0f70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5278,-815 5278,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31e1ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5311,-605 5311,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_31e07e0@0" Pin0InfoVect0LinkObjId="g_31e07e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5311,-605 5311,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31e2250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5311,-666 5311,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_31e07e0@1" ObjectIDZND0="g_31d0760@1" Pin0InfoVect0LinkObjId="g_31d0760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31e07e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5311,-666 5311,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31e24b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5310,-716 5310,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_31d0760@0" ObjectIDZND0="g_31e0f70@0" Pin0InfoVect0LinkObjId="g_31e0f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31d0760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5310,-716 5310,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31e2710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5310,-784 5310,-793 5278,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_31e0f70@1" ObjectIDZND0="g_31d7af0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_31d7af0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31e0f70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5310,-784 5310,-793 5278,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_320e040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-861 5438,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-861 5438,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3215250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-913 5408,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3219f90@0" Pin0InfoVect0LinkObjId="g_3219f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-913 5408,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32154f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-883 5438,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-883 5438,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3215790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-861 5488,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-861 5488,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32189e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-817 5520,-817 5520,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_321b920@0" ObjectIDND1="0@x" ObjectIDZND0="g_321abb0@0" Pin0InfoVect0LinkObjId="g_321abb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_321b920_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-817 5520,-817 5520,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3219b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-988 5438,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-988 5438,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3219d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-913 5438,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_3219f90@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3219f90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-913 5438,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_321c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-817 5488,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_321abb0@0" ObjectIDND1="0@x" ObjectIDZND0="g_321b920@1" Pin0InfoVect0LinkObjId="g_321b920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_321abb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-817 5488,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_321c440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-770 5488,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_321b920@0" ObjectIDZND0="g_3216c50@0" Pin0InfoVect0LinkObjId="g_3216c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_321b920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-770 5488,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_321cf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5568,-730 5568,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3218c80@0" ObjectIDZND0="g_321c6e0@0" Pin0InfoVect0LinkObjId="g_321c6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3218c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5568,-730 5568,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_321db10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-913 5438,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3219f90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3219f90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-913 5438,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_321dd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5438,-960 5438,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5438,-960 5438,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3221a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5568,-850 5568,-861 5488,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5568,-850 5568,-861 5488,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32249e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-861 5488,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-861 5488,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3224c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-831 5488,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_321b920@0" ObjectIDZND1="g_321abb0@0" Pin0InfoVect0LinkObjId="g_321b920_0" Pin0InfoVect1LinkObjId="g_321abb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-831 5488,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32266a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5568,-779 5568,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_321c6e0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3225c80@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3225c80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_321c6e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5568,-779 5568,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3226940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5568,-808 5568,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_321c6e0@0" ObjectIDZND1="g_3225c80@0" Pin0InfoVect0LinkObjId="g_321c6e0_0" Pin0InfoVect1LinkObjId="g_3225c80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5568,-808 5568,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3226be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5601,-598 5601,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_32254f0@0" Pin0InfoVect0LinkObjId="g_32254f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5601,-598 5601,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3226e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5601,-659 5601,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_32254f0@1" ObjectIDZND0="g_3215a30@1" Pin0InfoVect0LinkObjId="g_3215a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32254f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5601,-659 5601,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3227120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5600,-709 5600,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3215a30@0" ObjectIDZND0="g_3225c80@0" Pin0InfoVect0LinkObjId="g_3225c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3215a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5600,-709 5600,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32273c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5600,-777 5600,-786 5568,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3225c80@1" ObjectIDZND0="0@x" ObjectIDZND1="g_321c6e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_321c6e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3225c80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5600,-777 5600,-786 5568,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3244d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-868 5736,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-868 5736,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_324bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-924 5706,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3250910@0" Pin0InfoVect0LinkObjId="g_3250910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-924 5706,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_324bf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-890 5736,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-890 5736,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_324c1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-868 5786,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-868 5786,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_324f400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5786,-824 5818,-824 5818,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_32522a0@0" ObjectIDND1="0@x" ObjectIDZND0="g_3251530@0" Pin0InfoVect0LinkObjId="g_3251530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32522a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5786,-824 5818,-824 5818,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32504b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-995 5736,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-995 5736,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32506e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-924 5736,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_3250910@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3250910_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-924 5736,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3252b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5786,-824 5786,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3251530@0" ObjectIDND1="0@x" ObjectIDZND0="g_32522a0@1" Pin0InfoVect0LinkObjId="g_32522a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3251530_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5786,-824 5786,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3252dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5786,-777 5786,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_32522a0@0" ObjectIDZND0="g_324d670@0" Pin0InfoVect0LinkObjId="g_324d670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32522a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5786,-777 5786,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32538e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5866,-737 5866,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_324f6a0@0" ObjectIDZND0="g_3253060@0" Pin0InfoVect0LinkObjId="g_3253060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_324f6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5866,-737 5866,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3254410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-924 5736,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3250910@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3250910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-924 5736,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3254640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5736,-967 5736,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5736,-967 5736,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32582f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5866,-857 5866,-868 5786,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5866,-857 5866,-868 5786,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325b2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5786,-868 5786,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5786,-868 5786,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325b570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5786,-838 5786,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_32522a0@0" ObjectIDZND1="g_3251530@0" Pin0InfoVect0LinkObjId="g_32522a0_0" Pin0InfoVect1LinkObjId="g_3251530_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5786,-838 5786,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5866,-786 5866,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3253060@1" ObjectIDZND0="0@x" ObjectIDZND1="g_325c570@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_325c570_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3253060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5866,-786 5866,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5866,-815 5866,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3253060@0" ObjectIDZND1="g_325c570@0" Pin0InfoVect0LinkObjId="g_3253060_0" Pin0InfoVect1LinkObjId="g_325c570_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5866,-815 5866,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325d4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5899,-605 5899,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_325bde0@0" Pin0InfoVect0LinkObjId="g_325bde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5899,-605 5899,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5899,-666 5899,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_325bde0@1" ObjectIDZND0="g_324c450@1" Pin0InfoVect0LinkObjId="g_324c450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_325bde0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5899,-666 5899,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325da10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5898,-716 5898,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_324c450@0" ObjectIDZND0="g_325c570@0" Pin0InfoVect0LinkObjId="g_325c570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_324c450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5898,-716 5898,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_325dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5898,-784 5898,-793 5866,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_325c570@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3253060@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3253060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_325c570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5898,-784 5898,-793 5866,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c16a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-1041 5148,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-1041 5148,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34bc600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-1109 5148,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-1109 5148,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3278c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-1070 5148,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-1070 5148,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e6e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-1041 5437,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-1041 5437,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c72fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-1078 5437,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_31bd500@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31bd500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-1078 5437,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3555ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5469,-1138 5469,-1125 5437,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3555700@0" ObjectIDZND0="g_31bd500@0" ObjectIDZND1="g_2bd2880@0" Pin0InfoVect0LinkObjId="g_31bd500_0" Pin0InfoVect1LinkObjId="g_2bd2880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3555700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5469,-1138 5469,-1125 5437,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd2020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-1109 5437,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_31bd500@1" ObjectIDZND0="g_3555700@0" ObjectIDZND1="g_2bd2880@0" Pin0InfoVect0LinkObjId="g_3555700_0" Pin0InfoVect1LinkObjId="g_2bd2880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31bd500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-1109 5437,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_354a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5437,-1125 5437,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3555700@0" ObjectIDND1="g_31bd500@0" ObjectIDZND0="g_2bd2880@0" Pin0InfoVect0LinkObjId="g_2bd2880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3555700_0" Pin1InfoVect1LinkObjId="g_31bd500_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5437,-1125 5437,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35d0af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6049,-1017 6049,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3546890@1" Pin0InfoVect0LinkObjId="g_3546890_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6049,-1017 6049,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35d90d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6049,-934 6049,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3546890@0" ObjectIDZND0="g_35d8e50@1" Pin0InfoVect0LinkObjId="g_35d8e50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3546890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6049,-934 6049,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35d9fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6049,-876 6049,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_35d8e50@0" ObjectIDZND0="g_35d9580@1" Pin0InfoVect0LinkObjId="g_35d9580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35d8e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6049,-876 6049,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35db170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6049,-793 6049,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_35d9580@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35d9580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6049,-793 6049,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b56e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-946 4737,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_2bcdc80@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bcdc80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-946 4737,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3678860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4769,-959 4769,-946 4737,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bcdc80@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcdc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4769,-959 4769,-946 4737,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375e6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-946 4737,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bcdc80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcdc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-946 4737,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3575730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-1000 4562,-1010 4563,-1010 4563,-997 4562,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2ad4680@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad4680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-1000 4562,-1010 4563,-1010 4563,-997 4562,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_360bb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-1135 5148,-1232 4562,-1232 4562,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2add010@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2add010_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-1135 5148,-1232 4562,-1232 4562,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_372c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-1126 5148,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2add010@0" Pin0InfoVect0LinkObjId="g_2add010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-1126 5148,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35961c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5148,-1135 5180,-1135 5180,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_2add010@0" Pin0InfoVect0LinkObjId="g_2add010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5148,-1135 5180,-1135 5180,-1148 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42826" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3704.000000 -951.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7139"/>
     <cge:Term_Ref ObjectID="10389"/>
    <cge:TPSR_Ref TObjectID="7139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42827" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3704.000000 -951.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7139"/>
     <cge:Term_Ref ObjectID="10389"/>
    <cge:TPSR_Ref TObjectID="7139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42828" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3704.000000 -951.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7139"/>
     <cge:Term_Ref ObjectID="10389"/>
    <cge:TPSR_Ref TObjectID="7139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-42832" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3704.000000 -951.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7139"/>
     <cge:Term_Ref ObjectID="10389"/>
    <cge:TPSR_Ref TObjectID="7139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42816" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3864.000000 -733.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42816" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7148"/>
     <cge:Term_Ref ObjectID="10407"/>
    <cge:TPSR_Ref TObjectID="7148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42817" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3864.000000 -733.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7148"/>
     <cge:Term_Ref ObjectID="10407"/>
    <cge:TPSR_Ref TObjectID="7148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42818" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3864.000000 -733.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42818" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7148"/>
     <cge:Term_Ref ObjectID="10407"/>
    <cge:TPSR_Ref TObjectID="7148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42901" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7183"/>
     <cge:Term_Ref ObjectID="10433"/>
    <cge:TPSR_Ref TObjectID="7183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42902" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7183"/>
     <cge:Term_Ref ObjectID="10433"/>
    <cge:TPSR_Ref TObjectID="7183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42903" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7183"/>
     <cge:Term_Ref ObjectID="10433"/>
    <cge:TPSR_Ref TObjectID="7183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42889" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.000000 -53.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8753"/>
     <cge:Term_Ref ObjectID="12365"/>
    <cge:TPSR_Ref TObjectID="8753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42890" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.000000 -53.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8753"/>
     <cge:Term_Ref ObjectID="12365"/>
    <cge:TPSR_Ref TObjectID="8753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42891" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.000000 -53.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8753"/>
     <cge:Term_Ref ObjectID="12365"/>
    <cge:TPSR_Ref TObjectID="8753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42895" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -55.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8754"/>
     <cge:Term_Ref ObjectID="12367"/>
    <cge:TPSR_Ref TObjectID="8754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42896" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -55.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8754"/>
     <cge:Term_Ref ObjectID="12367"/>
    <cge:TPSR_Ref TObjectID="8754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42897" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -55.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8754"/>
     <cge:Term_Ref ObjectID="12367"/>
    <cge:TPSR_Ref TObjectID="8754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42883" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8752"/>
     <cge:Term_Ref ObjectID="12363"/>
    <cge:TPSR_Ref TObjectID="8752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42884" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8752"/>
     <cge:Term_Ref ObjectID="12363"/>
    <cge:TPSR_Ref TObjectID="8752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42885" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8752"/>
     <cge:Term_Ref ObjectID="12363"/>
    <cge:TPSR_Ref TObjectID="8752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42906" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7185"/>
     <cge:Term_Ref ObjectID="10437"/>
    <cge:TPSR_Ref TObjectID="7185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42907" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7185"/>
     <cge:Term_Ref ObjectID="10437"/>
    <cge:TPSR_Ref TObjectID="7185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42908" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7185"/>
     <cge:Term_Ref ObjectID="10437"/>
    <cge:TPSR_Ref TObjectID="7185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-42911" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -442.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7173"/>
     <cge:Term_Ref ObjectID="10375"/>
    <cge:TPSR_Ref TObjectID="7173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-42912" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -442.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7173"/>
     <cge:Term_Ref ObjectID="10375"/>
    <cge:TPSR_Ref TObjectID="7173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-42913" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -442.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7173"/>
     <cge:Term_Ref ObjectID="10375"/>
    <cge:TPSR_Ref TObjectID="7173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-42914" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -442.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7173"/>
     <cge:Term_Ref ObjectID="10375"/>
    <cge:TPSR_Ref TObjectID="7173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-42915" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -439.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7174"/>
     <cge:Term_Ref ObjectID="10376"/>
    <cge:TPSR_Ref TObjectID="7174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-42916" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -439.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7174"/>
     <cge:Term_Ref ObjectID="10376"/>
    <cge:TPSR_Ref TObjectID="7174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-42917" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -439.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7174"/>
     <cge:Term_Ref ObjectID="10376"/>
    <cge:TPSR_Ref TObjectID="7174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-42918" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -439.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7174"/>
     <cge:Term_Ref ObjectID="10376"/>
    <cge:TPSR_Ref TObjectID="7174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-42843" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -856.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7137"/>
     <cge:Term_Ref ObjectID="10373"/>
    <cge:TPSR_Ref TObjectID="7137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-42844" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -856.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7137"/>
     <cge:Term_Ref ObjectID="10373"/>
    <cge:TPSR_Ref TObjectID="7137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-42845" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -856.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42845" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7137"/>
     <cge:Term_Ref ObjectID="10373"/>
    <cge:TPSR_Ref TObjectID="7137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-42846" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -856.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7137"/>
     <cge:Term_Ref ObjectID="10373"/>
    <cge:TPSR_Ref TObjectID="7137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42821" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -739.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7152"/>
     <cge:Term_Ref ObjectID="10415"/>
    <cge:TPSR_Ref TObjectID="7152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42822" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -739.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42822" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7152"/>
     <cge:Term_Ref ObjectID="10415"/>
    <cge:TPSR_Ref TObjectID="7152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42823" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -739.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7152"/>
     <cge:Term_Ref ObjectID="10415"/>
    <cge:TPSR_Ref TObjectID="7152"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3241" y="-1148"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3241" y="-1148"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1165"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1165"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3241" y="-1148"/></g>
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1165"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-42860">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.000000 -698.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7148" ObjectName="SW-CX_YIZ.CX_YIZ_101BK"/>
     <cge:Meas_Ref ObjectId="42860"/>
    <cge:TPSR_Ref TObjectID="7148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42851">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.000000 -907.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7139" ObjectName="SW-CX_YIZ.CX_YIZ_121BK"/>
     <cge:Meas_Ref ObjectId="42851"/>
    <cge:TPSR_Ref TObjectID="7139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42939">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8753" ObjectName="SW-CX_YIZ.CX_YIZ_622BK"/>
     <cge:Meas_Ref ObjectId="42939"/>
    <cge:TPSR_Ref TObjectID="8753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42864">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 -697.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7152" ObjectName="SW-CX_YIZ.CX_YIZ_102BK"/>
     <cge:Meas_Ref ObjectId="42864"/>
    <cge:TPSR_Ref TObjectID="7152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42956">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7183" ObjectName="SW-CX_YIZ.CX_YIZ_621BK"/>
     <cge:Meas_Ref ObjectId="42956"/>
    <cge:TPSR_Ref TObjectID="7183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42950">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8754" ObjectName="SW-CX_YIZ.CX_YIZ_623BK"/>
     <cge:Meas_Ref ObjectId="42950"/>
    <cge:TPSR_Ref TObjectID="8754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42928">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8752" ObjectName="SW-CX_YIZ.CX_YIZ_624BK"/>
     <cge:Meas_Ref ObjectId="42928"/>
    <cge:TPSR_Ref TObjectID="8752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42958">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -255.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7185" ObjectName="SW-CX_YIZ.CX_YIZ_625BK"/>
     <cge:Meas_Ref ObjectId="42958"/>
    <cge:TPSR_Ref TObjectID="7185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 -758.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 -863.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4728.000000 -873.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5139.000000 -932.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5429.000000 -925.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5727.000000 -932.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5139.000000 -1062.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YIZ.CX_YIZ_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-413 4282,-413 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7173" ObjectName="BS-CX_YIZ.CX_YIZ_6IM"/>
    <cge:TPSR_Ref TObjectID="7173"/></metadata>
   <polyline fill="none" opacity="0" points="3620,-413 4282,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YIZ.CX_YIZ_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-826 4205,-826 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7137" ObjectName="BS-CX_YIZ.CX_YIZ_1IM"/>
    <cge:TPSR_Ref TObjectID="7137"/></metadata>
   <polyline fill="none" opacity="0" points="3637,-826 4205,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-835 4865,-835 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4435,-835 4865,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YIZ.CX_YIZ_6IIM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-413 4961,-413 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7174" ObjectName="BS-CX_YIZ.CX_YIZ_6IIM"/>
    <cge:TPSR_Ref TObjectID="7174"/></metadata>
   <polyline fill="none" opacity="0" points="4429,-413 4961,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-1017 6206,-1017 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5050,-1017 6206,-1017 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c58f90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -743.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c653f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -888.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b9af30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -871.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c11f60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -951.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bad560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -967.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c21ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4214.000000 -742.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b082f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -1023.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b08ad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 -558.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b09520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -559.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0d1f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3615.000000 -557.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0e710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -564.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3229.000000 -1089.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116471" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3242.538462 -986.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116471" ObjectName="CX_YIZ:CX_YIZ_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116470" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3241.538462 -942.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116470" ObjectName="CX_YIZ:CX_YIZ_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37321" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -1057.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5903" ObjectName="DYN-CX_YIZ"/>
     <cge:Meas_Ref ObjectId="37321"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd19e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 733.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 718.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7fe20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 703.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.016393 -0.000000 0.000000 -1.000000 -170.180328 681.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b80f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 733.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b81240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 718.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b81480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 703.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2ca00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 441.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2d030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.000000 412.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2d5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 426.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2db50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3490.000000 398.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2dec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 856.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3510.000000 827.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 841.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3501.000000 813.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 865.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 836.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2ed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 850.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2efd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 822.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2f300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4976.000000 439.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2f570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 410.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2f7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4976.000000 424.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2f9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 396.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ceb3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3649.000000 950.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ceb630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 935.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ceb870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 920.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cebab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 906.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4136.000000 -1002.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="7173" cx="3854" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7173" cx="3665" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7173" cx="4192" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7174" cx="4880" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7137" cx="3776" cy="-826" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7137" cx="3776" cy="-826" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7137" cx="4000" cy="-826" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7174" cx="4545" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7137" cx="4145" cy="-826" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4562" cy="-835" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4562" cy="-835" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4737" cy="-835" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7174" cx="4544" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7173" cx="3776" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5148" cy="-1017" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5438" cy="-1017" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5736" cy="-1017" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5148" cy="-1017" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5437" cy="-1017" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6049" cy="-1017" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YIZ.CX_YIZ_T2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="15068"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -585.000000)" xlink:href="#transformer:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="15070"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -585.000000)" xlink:href="#transformer:shape0_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="15072"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -585.000000)" xlink:href="#transformer:shape0-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="10821" ObjectName="TF-CX_YIZ.CX_YIZ_T2"/>
    <cge:TPSR_Ref TObjectID="10821"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6d560" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3793.000000 -679.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a6dfd0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3802.000000 -163.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29d8780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3749.000000 -1191.000000) translate(0,15)">迤万线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bbace0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4744.000000 -1289.000000) translate(0,15)">迤资联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c71260" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3878.000000 -566.000000) translate(0,12)">6kV I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c58a20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3576.000000 -45.000000) translate(0,15)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bb1610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4035.000000 -135.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b8fb30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4149.000000 -154.000000) translate(0,15)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c36d00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4373.000000 -136.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c3cd40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4499.000000 -153.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c38490" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4726.000000 -136.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b9d680" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4848.000000 -28.000000) translate(0,15)">2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba0940" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4682.000000 -1060.000000) translate(0,15)">（备用）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7b5f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4674.000000 -575.000000) translate(0,12)">6kV II段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7d610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4632.000000 -663.000000) translate(0,12)">6kV Ⅲ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7d610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4632.000000 -663.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b98ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3786.000000 -937.000000) translate(0,12)">121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b996c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -924.000000) translate(0,12)">12117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b99a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -986.000000) translate(0,12)">12160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b99ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -1058.000000) translate(0,12)">12167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9a0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -906.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9a600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -1002.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9a880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -936.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf5d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -872.000000) translate(0,12)">1211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf5f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -1003.000000) translate(0,12)">1216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf6190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3638.000000 -848.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf63d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -727.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf6610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3727.000000 -793.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf6850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3798.000000 -778.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfa270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3707.000000 -620.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfc1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -283.000000) translate(0,12)">621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3672.000000 -386.000000) translate(0,12)">6211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4e430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -726.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4e920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -792.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4eb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -777.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b53940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -621.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bca1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3619.000000 -433.000000) translate(0,12)">6kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bca6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4890.000000 -433.000000) translate(0,12)">6kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bca900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4811.000000 -851.000000) translate(0,12)">6kVⅢ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bcea30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -358.000000) translate(0,12)">622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bcef20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4201.000000 -355.000000) translate(0,12)">623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bcf260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -353.000000) translate(0,12)">624</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bcf7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -284.000000) translate(0,12)">625</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bcfd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4887.000000 -385.000000) translate(0,12)">6252</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd0180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -683.000000) translate(0,12)">6031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b85190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3134.000000 -549.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b88ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1028.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b88ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1028.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b88ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1028.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b88ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1028.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b88ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1028.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b88ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1028.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b88ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1028.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2aa2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3267.000000 -1137.500000) translate(0,16)">迤资一级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa41c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4156.000000 -498.000000) translate(0,12)">6022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b29e90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4904.000000 -160.000000) translate(0,15)">42B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b2aa40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3605.000000 -159.000000) translate(0,15)">41B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2fc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -460.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2fe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 -678.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b300b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -743.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b300b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -743.000000) translate(0,33)">SF9-20000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b300b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -743.000000) translate(0,51)">20000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b300b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -743.000000) translate(0,69)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b300b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -743.000000) translate(0,87)">121±2х2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -607.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -607.000000) translate(0,33)">SF9-12500/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -607.000000) translate(0,51)">12±2х2.5%/6.3/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -607.000000) translate(0,69)">12500/8000/4000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -607.000000) translate(0,87)">YN,d11,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -140.000000) translate(0,15)">2号发电厂参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -140.000000) translate(0,33)">SF8000-8/2600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -140.000000) translate(0,51)">8000kW   6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3b840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -140.000000) translate(0,69)">COS=0.8   916A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3ca70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -131.000000) translate(0,15)">1号发电厂参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3ca70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -131.000000) translate(0,33)">SF3200-10/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3ca70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -131.000000) translate(0,51)">3200kW   6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3ca70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -131.000000) translate(0,69)">COS=0.8   367A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3cfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -132.000000) translate(0,15)">2号发电厂参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3cfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -132.000000) translate(0,33)">SF8000-8/2600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3cfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -132.000000) translate(0,51)">8000kW   6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3cfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -132.000000) translate(0,69)">COS=0.8   916A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b556f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -788.000000) translate(0,12)">603</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -891.000000) translate(0,12)">631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -903.000000) translate(0,12)">632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b078c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4131.000000 -1024.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2cf1590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3246.000000 -212.000000) translate(0,16)">5174</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2cf2530" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3244.000000 -179.000000) translate(0,16)">6011766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2cf3680" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3136.000000 -130.000000) translate(0,16)">柯翔林：15368681197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31ac7d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5098.000000 -711.000000) translate(0,15)">4号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d4250" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5251.000000 -646.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d85d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5157.000000 -958.000000) translate(0,12)">642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31e0210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5108.000000 -746.000000) translate(0,15)">1400kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_321d200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5447.000000 -951.000000) translate(0,12)">643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3253b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5745.000000 -958.000000) translate(0,12)">644</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2ce90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5159.000000 -1092.000000) translate(0,12)">641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f62a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5408.000000 -1255.000000) translate(0,12)">6kV Ⅳ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f62a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5408.000000 -1255.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3595c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6096.000000 -1044.000000) translate(0,12)">6kVⅣ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3828f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5398.000000 -736.000000) translate(0,15)">2500kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38290d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5395.000000 -705.000000) translate(0,15)">5号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3829240" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5541.000000 -636.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_382e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5697.000000 -745.000000) translate(0,15)">2500kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_382e730" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5687.000000 -710.000000) translate(0,15)">6号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_382e8a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5840.000000 -645.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3828a70" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6016.000000 -629.000000) translate(0,15)">3号厂用变</text>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c74090">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -65.000000)" xlink:href="#lightningRod:shape121"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c700b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -109.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c42a40">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.000000 -69.000000)" xlink:href="#lightningRod:shape121"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c364e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 -109.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bac760">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -66.000000)" xlink:href="#lightningRod:shape121"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c161a0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4661.000000 -108.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b7e830">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3798.000000 -1091.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7de00">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -997.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7eb10">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -472.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7f8c0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4699.267767 -712.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a80630">
    <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4167.000000 -309.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a813e0">
    <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4520.000000 -307.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a82190">
    <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3829.000000 -312.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b94c30">
    <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3637.000000 -247.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b958f0">
    <use class="BV-6KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3929.267767 -152.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b96660">
    <use class="BV-6KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4267.267767 -149.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b973d0">
    <use class="BV-6KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4620.267767 -147.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b98140">
    <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4852.000000 -246.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bf6a90">
    <use class="BV-110KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3654.267767 -575.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfb0d0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -308.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4b4a0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -170.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4c130">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3975.000000 -144.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4cc10">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 -168.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4d950">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 -145.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4eda0">
    <use class="BV-110KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4021.267767 -576.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b530c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -475.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b53e30">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -167.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc7a10">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -145.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc89b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -306.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc96f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.076923 4735.000000 -487.461538)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcab40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -704.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcc120">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 -954.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcdc80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4762.000000 -954.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd03c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -459.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2aea0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4496.500000 -755.085786)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b09f70">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3767.000000 -1030.000000)" xlink:href="#lightningRod:shape155"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0b9d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3619.000000 -591.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0dea0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -595.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b110c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -121.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b11ae0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 -1.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b12500">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 -124.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acb590">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4703.000000 -3.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acbfb0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4702.000000 -121.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acc9d0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -186.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acd3f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -182.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad1f60">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 1.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad2d80">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -308.000000)" xlink:href="#lightningRod:shape167"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad3a00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4891.000000 -307.000000)" xlink:href="#lightningRod:shape167"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad4680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.000000 -942.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad78b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4139.000000 -521.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6e6b0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3989.000000 -921.000000)" xlink:href="#lightningRod:shape155"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b71580">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4540.500000 -626.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d0760">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5298.000000 -669.000000)" xlink:href="#lightningRod:shape121"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d3970">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5264.000000 -713.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d5290">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5123.000000 -912.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d6040">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5223.267767 -752.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d6db0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5189.000000 -772.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31d7af0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5269.000000 -750.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e07e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5306.000000 -608.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e0f70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5305.000000 -726.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3215a30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5588.000000 -662.000000)" xlink:href="#lightningRod:shape121"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3218c80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5554.000000 -706.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3219f90">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5413.000000 -905.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_321abb0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5513.267767 -745.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_321b920">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5479.000000 -765.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_321c6e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5559.000000 -743.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32254f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5596.000000 -601.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3225c80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5595.000000 -719.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324c450">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5886.000000 -669.000000)" xlink:href="#lightningRod:shape121"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_324f6a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5852.000000 -713.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3250910">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5711.000000 -916.707107)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3251530">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5811.267767 -752.353553)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32522a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5777.000000 -772.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3253060">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5857.000000 -750.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_325bde0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5894.000000 -608.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_325c570">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5893.000000 -726.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2add010">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5173.000000 -1143.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31bd500">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5428.000000 -1073.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd2880">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5451.000000 -1168.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3555700">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5462.000000 -1133.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3546890">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6044.000000 -929.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d8e50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6040.000000 -871.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35d9580">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6044.000000 -788.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YIZ"/>
</svg>