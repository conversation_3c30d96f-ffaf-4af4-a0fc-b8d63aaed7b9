<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-47" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3336 -1250 2008 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="25" y1="33" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="15" x2="3" y1="60" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="9" y1="59" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="13" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="7" x2="10" y1="65" y2="65"/>
    <circle cx="40" cy="38" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="33" cy="32" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="40" cy="28" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="18" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="17" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="9" x2="9" y1="25" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="18" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="17" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="16"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="28" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape114">
    <polyline points="30,16 5,16 5,8 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <polyline points="30,36 5,36 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="34" x2="30" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15697" x1="30" x2="30" y1="36" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="26" x2="30" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.18573" x1="49" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15695" x1="44" x2="44" y1="31" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.19217" x1="49" x2="44" y1="26" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="35" x2="30" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15697" x1="30" x2="30" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="26" x2="30" y1="20" y2="17"/>
    <circle cx="30" cy="18" r="11" stroke-width="1.15698"/>
    <ellipse cx="44" cy="27" rx="11.5" ry="11" stroke-width="1.15698"/>
    <circle cx="30" cy="35" r="11" stroke-width="1.15698"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape128">
    <polyline points="31,17 6,17 6,9 " stroke-width="1"/>
    <circle cx="31" cy="36" r="11" stroke-width="1"/>
    <ellipse cx="45" cy="28" rx="11.5" ry="11" stroke-width="1"/>
    <circle cx="31" cy="19" r="11" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="50" x2="45" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="45" y1="32" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="50" x2="45" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="31" y1="40" y2="37"/>
    <polyline points="31,37 6,37 6,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="26" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="2018" x="3331" y="-1255"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46101">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -893.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8024" ObjectName="SW-CX_DDH.CX_DDH_171BK"/>
     <cge:Meas_Ref ObjectId="46101"/>
    <cge:TPSR_Ref TObjectID="8024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46075">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -483.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8010" ObjectName="SW-CX_DDH.CX_DDH_071BK"/>
     <cge:Meas_Ref ObjectId="46075"/>
    <cge:TPSR_Ref TObjectID="8010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46086">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4523.000000 -484.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8016" ObjectName="SW-CX_DDH.CX_DDH_072BK"/>
     <cge:Meas_Ref ObjectId="46086"/>
    <cge:TPSR_Ref TObjectID="8016"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DDH.CX_DDH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-576 5308,-576 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10524" ObjectName="BS-CX_DDH.CX_DDH_9IM"/>
    <cge:TPSR_Ref TObjectID="10524"/></metadata>
   <polyline fill="none" opacity="0" points="3812,-576 5308,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DDH.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-890 4184,-890 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48329" ObjectName="BS-CX_DDH.XM"/>
    <cge:TPSR_Ref TObjectID="48329"/></metadata>
   <polyline fill="none" opacity="0" points="4162,-890 4184,-890 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5283.000000 -1201.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_265a670" refnum="0">
    <use class="BV-0KV" transform="matrix(1.173913 -0.000000 0.000000 -1.000000 4058.000000 -987.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3603e90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.956522 4262.000000 -927.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_410fd10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.956522 4000.000000 -316.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35ec6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.956522 4588.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ace300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 4629.000000 -144.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22edd20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 4041.000000 -145.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3396520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 4250.000000 -727.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3396c20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 4205.000000 -611.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a78980">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.000000 -1050.000000)" xlink:href="#lightningRod:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a8d160">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4023.000000 -455.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a46670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -414.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb72a0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4017.000000 -264.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd3d90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -415.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a46e00">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4605.000000 -268.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f16e60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4876.000000 -661.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e46380">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4855.000000 -706.000000)" xlink:href="#lightningRod:shape114"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e49870">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5287.000000 -957.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7ade0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5287.000000 -779.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4201ed0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5298.000000 -1037.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc6370">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5126.000000 -482.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a06910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5130.000000 -361.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a07520">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5130.000000 -191.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ca0df0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4289.000000 -1010.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb5b50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5306.000000 -1068.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3df3ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -619.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3df4bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 -329.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3393850">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.000000 -330.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3394600">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4927.000000 -694.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3da3410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4667.000000 -214.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acecf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.000000 -214.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3522c20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -506.000000)" xlink:href="#lightningRod:shape128"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ebc070">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -745.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ef160">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -746.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3604e70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -507.000000)" xlink:href="#lightningRod:shape128"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c7edb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -456.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3455.000000 -1166.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78577" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3479.538462 -1066.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78577" ObjectName="CX_DDH:CX_DDH_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46141" prefix="P  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -956.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8024"/>
     <cge:Term_Ref ObjectID="11493"/>
    <cge:TPSR_Ref TObjectID="8024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46142" prefix="Q " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -956.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8024"/>
     <cge:Term_Ref ObjectID="11493"/>
    <cge:TPSR_Ref TObjectID="8024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46138" prefix="Ia  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -956.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8024"/>
     <cge:Term_Ref ObjectID="11493"/>
    <cge:TPSR_Ref TObjectID="8024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-46144" prefix="Hz " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -956.000000) translate(0,57)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8024"/>
     <cge:Term_Ref ObjectID="11493"/>
    <cge:TPSR_Ref TObjectID="8024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-46153" prefix="Ua  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -656.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10524"/>
     <cge:Term_Ref ObjectID="14661"/>
    <cge:TPSR_Ref TObjectID="10524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-46154" prefix="Ub " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -656.000000) translate(0,27)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10524"/>
     <cge:Term_Ref ObjectID="14661"/>
    <cge:TPSR_Ref TObjectID="10524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-46155" prefix="Uc " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -656.000000) translate(0,42)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10524"/>
     <cge:Term_Ref ObjectID="14661"/>
    <cge:TPSR_Ref TObjectID="10524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-46156" prefix="Uab " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -656.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10524"/>
     <cge:Term_Ref ObjectID="14661"/>
    <cge:TPSR_Ref TObjectID="10524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46121" prefix="P  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -140.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8010"/>
     <cge:Term_Ref ObjectID="11465"/>
    <cge:TPSR_Ref TObjectID="8010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46122" prefix="Q " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -140.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8010"/>
     <cge:Term_Ref ObjectID="11465"/>
    <cge:TPSR_Ref TObjectID="8010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46118" prefix="Ia  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -140.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8010"/>
     <cge:Term_Ref ObjectID="11465"/>
    <cge:TPSR_Ref TObjectID="8010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-46124" prefix="Hz " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -140.000000) translate(0,57)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46124" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8010"/>
     <cge:Term_Ref ObjectID="11465"/>
    <cge:TPSR_Ref TObjectID="8010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46131" prefix="P  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -139.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8016"/>
     <cge:Term_Ref ObjectID="11477"/>
    <cge:TPSR_Ref TObjectID="8016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46132" prefix="Q " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -139.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8016"/>
     <cge:Term_Ref ObjectID="11477"/>
    <cge:TPSR_Ref TObjectID="8016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46128" prefix="Ia  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -139.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8016"/>
     <cge:Term_Ref ObjectID="11477"/>
    <cge:TPSR_Ref TObjectID="8016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-46134" prefix="Hz " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -139.000000) translate(0,57)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8016"/>
     <cge:Term_Ref ObjectID="11477"/>
    <cge:TPSR_Ref TObjectID="8016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-46135" prefix="Uab  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -893.000000) translate(0,12)">Uab   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48329"/>
     <cge:Term_Ref ObjectID="47362"/>
    <cge:TPSR_Ref TObjectID="48329"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3467" y="-1225"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3467" y="-1225"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3418" y="-1242"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3418" y="-1242"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3467" y="-1225"/></g>
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3418" y="-1242"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46104">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.130890 -967.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8025" ObjectName="SW-CX_DDH.CX_DDH_17167SW"/>
     <cge:Meas_Ref ObjectId="46104"/>
    <cge:TPSR_Ref TObjectID="8025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46106">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -990.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8027" ObjectName="SW-CX_DDH.CX_DDH_1719SW"/>
     <cge:Meas_Ref ObjectId="46106"/>
    <cge:TPSR_Ref TObjectID="8027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46107">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -934.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8028" ObjectName="SW-CX_DDH.CX_DDH_17197SW"/>
     <cge:Meas_Ref ObjectId="46107"/>
    <cge:TPSR_Ref TObjectID="8028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46098">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -630.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8022" ObjectName="SW-CX_DDH.CX_DDH_00117SW"/>
     <cge:Meas_Ref ObjectId="46098"/>
    <cge:TPSR_Ref TObjectID="8022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -529.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8011" ObjectName="SW-CX_DDH.CX_DDH_071XC"/>
     <cge:Meas_Ref ObjectId="46076"/>
    <cge:TPSR_Ref TObjectID="8011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -454.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8012" ObjectName="SW-CX_DDH.CX_DDH_071XC1"/>
     <cge:Meas_Ref ObjectId="46076"/>
    <cge:TPSR_Ref TObjectID="8012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46077">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8013" ObjectName="SW-CX_DDH.CX_DDH_07167SW"/>
     <cge:Meas_Ref ObjectId="46077"/>
    <cge:TPSR_Ref TObjectID="8013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -417.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46078">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.000000 -243.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8014" ObjectName="SW-CX_DDH.CX_DDH_0719SW"/>
     <cge:Meas_Ref ObjectId="46078"/>
    <cge:TPSR_Ref TObjectID="8014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46087">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 -529.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8017" ObjectName="SW-CX_DDH.CX_DDH_072XC"/>
     <cge:Meas_Ref ObjectId="46087"/>
    <cge:TPSR_Ref TObjectID="8017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46087">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 -453.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8018" ObjectName="SW-CX_DDH.CX_DDH_072XC1"/>
     <cge:Meas_Ref ObjectId="46087"/>
    <cge:TPSR_Ref TObjectID="8018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -323.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8019" ObjectName="SW-CX_DDH.CX_DDH_07267SW"/>
     <cge:Meas_Ref ObjectId="46088"/>
    <cge:TPSR_Ref TObjectID="8019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4623.000000 -247.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8020" ObjectName="SW-CX_DDH.CX_DDH_0729SW"/>
     <cge:Meas_Ref ObjectId="46089"/>
    <cge:TPSR_Ref TObjectID="8020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -622.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5125.000000 -447.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5125.000000 -533.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46105">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -922.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8026" ObjectName="SW-CX_DDH.CX_DDH_1716SW"/>
     <cge:Meas_Ref ObjectId="46105"/>
    <cge:TPSR_Ref TObjectID="8026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46100">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8023" ObjectName="SW-CX_DDH.CX_DDH_1010SW"/>
     <cge:Meas_Ref ObjectId="46100"/>
    <cge:TPSR_Ref TObjectID="8023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4613.000000 -418.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46097">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -605.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8021" ObjectName="SW-CX_DDH.CX_DDH_0011SW"/>
     <cge:Meas_Ref ObjectId="46097"/>
    <cge:TPSR_Ref TObjectID="8021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-311528">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.347826 4157.000000 -849.086957)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48330" ObjectName="SW-CX_DDH.XB"/>
     <cge:Meas_Ref ObjectId="311528"/>
    <cge:TPSR_Ref TObjectID="48330"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DDH.CX_DDH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="11505"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.130890 -750.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.130890 -750.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8029" ObjectName="TF-CX_DDH.CX_DDH_1T"/>
    <cge:TPSR_Ref TObjectID="8029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -201.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -201.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -205.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -205.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 -157.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 -157.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -158.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -158.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5277.000000 -852.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5277.000000 -852.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -261.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -261.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_41023d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4268,-1055 4268,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2a78980@0" ObjectIDZND0="g_3ca0df0@0" ObjectIDZND1="8028@x" ObjectIDZND2="8027@x" Pin0InfoVect0LinkObjId="g_3ca0df0_0" Pin0InfoVect1LinkObjId="SW-46107_0" Pin0InfoVect2LinkObjId="SW-46106_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a78980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4268,-1055 4268,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2656c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4268,-1016 4293,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a78980@0" ObjectIDND1="8028@x" ObjectIDND2="8027@x" ObjectIDZND0="g_3ca0df0@0" Pin0InfoVect0LinkObjId="g_3ca0df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a78980_0" Pin1InfoVect1LinkObjId="SW-46107_0" Pin1InfoVect2LinkObjId="SW-46106_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4268,-1016 4293,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e07890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4268,-1016 4268,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2a78980@0" ObjectIDND1="g_3ca0df0@0" ObjectIDND2="8027@x" ObjectIDZND0="8028@1" Pin0InfoVect0LinkObjId="SW-46107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a78980_0" Pin1InfoVect1LinkObjId="g_3ca0df0_0" Pin1InfoVect2LinkObjId="SW-46106_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4268,-1016 4268,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ceb340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4268,-956 4268,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8028@0" ObjectIDZND0="g_3603e90@0" Pin0InfoVect0LinkObjId="g_3603e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4268,-956 4268,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40718f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4171,-703 4214,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_3df3ef0@0" ObjectIDZND1="8022@x" Pin0InfoVect0LinkObjId="g_3df3ef0_0" Pin0InfoVect1LinkObjId="SW-46098_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4171,-703 4214,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cbf4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-677 4260,-703 4214,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3df3ef0@0" ObjectIDZND0="8022@x" Pin0InfoVect0LinkObjId="SW-46098_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df3ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4260,-677 4260,-703 4214,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_425ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-993 4079,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8025@1" ObjectIDZND0="g_265a670@0" Pin0InfoVect0LinkObjId="g_265a670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-993 4079,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36238d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-333 4006,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_410fd10@0" ObjectIDZND0="8013@0" Pin0InfoVect0LinkObjId="SW-46077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_410fd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-333 4006,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cbbcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-380 4006,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8013@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2a46670@0" ObjectIDZND2="8012@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_2a46670_0" Pin0InfoVect2LinkObjId="SW-46076_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-380 4006,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f6910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-269 4026,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3fb72a0@1" ObjectIDZND0="8014@0" Pin0InfoVect0LinkObjId="SW-46078_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fb72a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-269 4026,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f6b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-269 4088,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="8014@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2acecf0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_2acecf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-269 4088,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4404360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-160 4134,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-160 4134,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44045c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-248 4134,-269 4088,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="8014@x" ObjectIDZND1="g_2acecf0@0" Pin0InfoVect0LinkObjId="SW-46078_0" Pin0InfoVect1LinkObjId="g_2acecf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-248 4134,-269 4088,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3394be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-576 4532,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10524@0" ObjectIDZND0="8017@0" Pin0InfoVect0LinkObjId="SW-46087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e49600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-576 4532,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2506190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-334 4594,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_35ec6a0@0" ObjectIDZND0="8019@0" Pin0InfoVect0LinkObjId="SW-46088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35ec6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-334 4594,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fd3b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-381 4594,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8019@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3fd3d90@0" ObjectIDZND2="8018@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_3fd3d90_0" Pin0InfoVect2LinkObjId="SW-46087_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-381 4594,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_411f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4600,-273 4614,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a46e00@1" ObjectIDZND0="8020@0" Pin0InfoVect0LinkObjId="SW-46089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a46e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4600,-273 4614,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_411f410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-273 4676,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="8020@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3da3410@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_3da3410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-273 4676,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_426d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-164 4722,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-164 4722,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_426d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-252 4722,-273 4676,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="8020@x" ObjectIDZND1="g_3da3410@0" Pin0InfoVect0LinkObjId="SW-46089_0" Pin0InfoVect1LinkObjId="g_3da3410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-252 4722,-273 4676,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e49600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4934,-635 4934,-608 4934,-607 4885,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_3394600@0" ObjectIDZND0="10524@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_4670610_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3394600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4934,-635 4934,-608 4934,-607 4885,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aa34a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5293,-1139 5313,-1139 5313,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_4201ed0@0" ObjectIDND1="0@x" ObjectIDZND0="g_3fb5b50@0" Pin0InfoVect0LinkObjId="g_3fb5b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4201ed0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5293,-1139 5313,-1139 5313,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc6bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-576 5135,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10524@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e49600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-576 5135,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2acd340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-491 4032,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2a8d160@1" ObjectIDZND0="g_3522c20@0" Pin0InfoVect0LinkObjId="g_3522c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8d160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-491 4032,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_466fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4885,-576 4885,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10524@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3394600@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_3394600_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e49600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4885,-576 4885,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4670150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-536 4532,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8017@1" ObjectIDZND0="8016@1" Pin0InfoVect0LinkObjId="SW-46086_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-536 4532,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46703b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-492 4532,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8016@0" ObjectIDZND0="8018@0" Pin0InfoVect0LinkObjId="SW-46087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-492 4532,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4670610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-553 3944,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8011@0" ObjectIDZND0="10524@0" Pin0InfoVect0LinkObjId="g_3e49600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-553 3944,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e07ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-478 3944,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8012@0" ObjectIDZND0="8010@0" Pin0InfoVect0LinkObjId="SW-46075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-478 3944,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e07d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-518 3944,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8010@1" ObjectIDZND0="8011@1" Pin0InfoVect0LinkObjId="SW-46076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-518 3944,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e07f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-196 5135,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2a07520@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a07520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-196 5135,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e081e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-540 5135,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3fc6370@1" Pin0InfoVect0LinkObjId="g_3fc6370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-540 5135,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e08440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-487 5135,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3fc6370@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fc6370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-487 5135,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e086a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-453 5135,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2a06910@0" Pin0InfoVect0LinkObjId="g_2a06910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-453 5135,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e08900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4885,-628 4885,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="10524@0" ObjectIDZND1="g_3394600@0" Pin0InfoVect0LinkObjId="g_3e49600_0" Pin0InfoVect1LinkObjId="g_3394600_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4885,-628 4885,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e08b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4885,-646 4885,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3f16e60@0" Pin0InfoVect0LinkObjId="g_3f16e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4885,-646 4885,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fb7790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4885,-697 4885,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3f16e60@1" ObjectIDZND0="g_3e46380@0" Pin0InfoVect0LinkObjId="g_3e46380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f16e60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4885,-697 4885,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fb79f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-784 5292,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2a7ade0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7ade0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-784 5292,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fb7c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-1042 5292,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4201ed0@1" ObjectIDZND0="g_3e49870@0" Pin0InfoVect0LinkObjId="g_3e49870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4201ed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-1042 5292,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fb8720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-1139 5292,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_3fb5b50@0" ObjectIDND1="0@x" ObjectIDZND0="g_4201ed0@0" Pin0InfoVect0LinkObjId="g_4201ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fb5b50_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-1139 5292,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fb8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-1174 5292,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3fb5b50@0" ObjectIDZND1="g_4201ed0@0" Pin0InfoVect0LinkObjId="g_3fb5b50_0" Pin0InfoVect1LinkObjId="g_4201ed0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-1174 5292,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fb5920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-928 4172,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8024@1" ObjectIDZND0="8026@0" Pin0InfoVect0LinkObjId="SW-46105_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-928 4172,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3662bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4676,-273 4676,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="8020@x" ObjectIDND1="0@x" ObjectIDZND0="g_3da3410@1" Pin0InfoVect0LinkObjId="g_3da3410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46089_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4676,-273 4676,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3663e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4676,-219 4676,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3da3410@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da3410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4676,-219 4676,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2acde40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4676,-162 4676,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4676,-162 4676,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ace0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4676,-193 4638,-193 4638,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_2ace300@0" Pin0InfoVect0LinkObjId="g_2ace300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4676,-193 4638,-193 4638,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22edac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-194 4050,-194 4050,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_22edd20@0" Pin0InfoVect0LinkObjId="g_22edd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-194 4050,-194 4050,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3522500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-250 4088,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_2acecf0@1" ObjectIDZND0="8014@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-46078_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acecf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-250 4088,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3522760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-145 4088,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-145 4088,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35229c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-205 4088,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2acecf0@0" Pin0InfoVect0LinkObjId="g_2acecf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-205 4088,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a7e200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-817 4174,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_3ebc070@0" ObjectIDND1="g_33ef160@0" ObjectIDND2="8023@x" ObjectIDZND0="8029@x" Pin0InfoVect0LinkObjId="g_3e840a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ebc070_0" Pin1InfoVect1LinkObjId="g_33ef160_0" Pin1InfoVect2LinkObjId="SW-46100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-817 4174,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a7e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4287,-817 4287,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8029@x" ObjectIDND1="8023@x" ObjectIDND2="g_33ef160@0" ObjectIDZND0="g_3ebc070@0" Pin0InfoVect0LinkObjId="g_3ebc070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a7e200_0" Pin1InfoVect1LinkObjId="SW-46100_0" Pin1InfoVect2LinkObjId="g_33ef160_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4287,-817 4287,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e840a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4287,-817 4259,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_3ebc070@0" ObjectIDND1="g_33ef160@0" ObjectIDZND0="8029@x" ObjectIDZND1="8023@x" Pin0InfoVect0LinkObjId="g_2a7e200_0" Pin0InfoVect1LinkObjId="SW-46100_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ebc070_0" Pin1InfoVect1LinkObjId="g_33ef160_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4287,-817 4259,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e84300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4312,-803 4312,-817 4287,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="g_33ef160@0" ObjectIDZND0="g_3ebc070@0" ObjectIDZND1="8029@x" ObjectIDZND2="8023@x" Pin0InfoVect0LinkObjId="g_3ebc070_0" Pin0InfoVect1LinkObjId="g_2a7e200_0" Pin0InfoVect2LinkObjId="SW-46100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ef160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4312,-803 4312,-817 4287,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e84560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-751 4259,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3396520@0" ObjectIDZND0="8023@0" Pin0InfoVect0LinkObjId="SW-46100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3396520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-751 4259,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e847c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-802 4259,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8023@1" ObjectIDZND0="8029@x" ObjectIDZND1="g_3ebc070@0" ObjectIDZND2="g_33ef160@0" Pin0InfoVect0LinkObjId="g_2a7e200_0" Pin0InfoVect1LinkObjId="g_3ebc070_0" Pin0InfoVect2LinkObjId="g_33ef160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-802 4259,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e84a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1016 4268,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8027@1" ObjectIDZND0="g_2a78980@0" ObjectIDZND1="g_3ca0df0@0" ObjectIDZND2="8028@x" Pin0InfoVect0LinkObjId="g_2a78980_0" Pin0InfoVect1LinkObjId="g_3ca0df0_0" Pin0InfoVect2LinkObjId="SW-46107_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46106_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1016 4268,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_433a610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-837 5292,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2a7ade0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7ade0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-837 5292,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_433a870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5292,-945 5292,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3e49870@1" Pin0InfoVect0LinkObjId="g_3e49870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5292,-945 5292,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-249 5135,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2a07520@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a07520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-249 5135,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c8730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-354 5135,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2a06910@1" Pin0InfoVect0LinkObjId="g_2a06910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-354 5135,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c8990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-460 4032,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a8d160@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8d160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-460 4032,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c8bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-423 4032,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="8013@x" ObjectIDZND1="8012@x" ObjectIDZND2="g_3fb72a0@0" Pin0InfoVect0LinkObjId="SW-46077_0" Pin0InfoVect1LinkObjId="SW-46076_0" Pin0InfoVect2LinkObjId="g_3fb72a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-423 4032,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36049b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-401 4032,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8013@x" ObjectIDND1="8012@x" ObjectIDND2="g_3fb72a0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2a46670@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_2a46670_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46077_0" Pin1InfoVect1LinkObjId="SW-46076_0" Pin1InfoVect2LinkObjId="g_3fb72a0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-401 4032,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3604c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-492 4623,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3c7edb0@1" ObjectIDZND0="g_3604e70@0" Pin0InfoVect0LinkObjId="g_3604e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c7edb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-492 4623,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_343d7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-461 4623,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3c7edb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c7edb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-461 4623,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343da30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-424 4623,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="8019@x" ObjectIDZND1="8018@x" ObjectIDZND2="g_2a46e00@0" Pin0InfoVect0LinkObjId="SW-46088_0" Pin0InfoVect1LinkObjId="SW-46087_0" Pin0InfoVect2LinkObjId="g_2a46e00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-424 4623,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c7fec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-402 4623,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8019@x" ObjectIDND1="8018@x" ObjectIDND2="g_2a46e00@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3fd3d90@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_3fd3d90_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46088_0" Pin1InfoVect1LinkObjId="SW-46087_0" Pin1InfoVect2LinkObjId="g_2a46e00_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-402 4623,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fcccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-402 4661,-402 4661,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="8019@x" ObjectIDND2="8018@x" ObjectIDZND0="g_3fd3d90@0" Pin0InfoVect0LinkObjId="g_3fd3d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="SW-46088_0" Pin1InfoVect2LinkObjId="SW-46087_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-402 4661,-402 4661,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fccf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4073,-419 4073,-401 4032,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2a46670@0" ObjectIDZND0="0@x" ObjectIDZND1="8013@x" ObjectIDZND2="8012@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="SW-46077_0" Pin0InfoVect2LinkObjId="SW-46076_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a46670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4073,-419 4073,-401 4032,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fce250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-635 4214,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3396c20@0" ObjectIDZND0="8022@0" Pin0InfoVect0LinkObjId="SW-46098_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3396c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-635 4214,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3396330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-688 4214,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8022@1" ObjectIDZND0="g_3df3ef0@0" Pin0InfoVect0LinkObjId="g_3df3ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46098_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-688 4214,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a79060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4171,-755 4171,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="8029@0" ObjectIDZND0="8021@1" Pin0InfoVect0LinkObjId="SW-46097_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7e200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4171,-755 4171,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a79250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4171,-627 4171,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8021@0" ObjectIDZND0="10524@0" Pin0InfoVect0LinkObjId="g_3e49600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46097_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4171,-627 4171,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3578780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-401 3944,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="hydroGenerator" ObjectIDND0="g_3df4bc0@0" ObjectIDND1="8013@x" ObjectIDND2="0@x" ObjectIDZND0="8012@x" ObjectIDZND1="g_3fb72a0@0" ObjectIDZND2="15736@x" Pin0InfoVect0LinkObjId="SW-46076_0" Pin0InfoVect1LinkObjId="g_3fb72a0_0" Pin0InfoVect2LinkObjId="SM-CX_DDH.CX_DDH_GN1_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df4bc0_0" Pin1InfoVect1LinkObjId="SW-46077_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-401 3944,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35790f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-387 3976,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="hydroGenerator" ObjectIDND0="g_3df4bc0@0" ObjectIDZND0="8012@x" ObjectIDZND1="g_3fb72a0@0" ObjectIDZND2="15736@x" Pin0InfoVect0LinkObjId="SW-46076_0" Pin0InfoVect1LinkObjId="g_3fb72a0_0" Pin0InfoVect2LinkObjId="SM-CX_DDH.CX_DDH_GN1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df4bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-387 3976,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3579300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-401 4006,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="hydroGenerator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8012@x" ObjectIDND1="g_3fb72a0@0" ObjectIDND2="15736@x" ObjectIDZND0="8013@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2a46670@0" Pin0InfoVect0LinkObjId="SW-46077_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="g_2a46670_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46076_0" Pin1InfoVect1LinkObjId="g_3fb72a0_0" Pin1InfoVect2LinkObjId="SM-CX_DDH.CX_DDH_GN1_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-401 4006,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff8370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-460 3944,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8012@1" ObjectIDZND0="g_3df4bc0@0" ObjectIDZND1="8013@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3df4bc0_0" Pin0InfoVect1LinkObjId="SW-46077_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-460 3944,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff85d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-269 3944,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3fb72a0@0" ObjectIDZND0="g_3df4bc0@0" ObjectIDZND1="8013@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3df4bc0_0" Pin0InfoVect1LinkObjId="SW-46077_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fb72a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-269 3944,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff90a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-401 3944,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="hydroGenerator" ObjectIDND0="g_3df4bc0@0" ObjectIDND1="8013@x" ObjectIDND2="0@x" ObjectIDZND0="g_3fb72a0@0" ObjectIDZND1="15736@x" Pin0InfoVect0LinkObjId="g_3fb72a0_0" Pin0InfoVect1LinkObjId="SM-CX_DDH.CX_DDH_GN1_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df4bc0_0" Pin1InfoVect1LinkObjId="SW-46077_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-401 3944,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff9300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-269 3944,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="g_3fb72a0@0" ObjectIDND1="g_3df4bc0@0" ObjectIDND2="8013@x" ObjectIDZND0="15736@0" Pin0InfoVect0LinkObjId="SM-CX_DDH.CX_DDH_GN1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3fb72a0_0" Pin1InfoVect1LinkObjId="g_3df4bc0_0" Pin1InfoVect2LinkObjId="SW-46077_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-269 3944,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff9560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-402 4532,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="hydroGenerator" ObjectIDND0="g_3393850@0" ObjectIDND1="8019@x" ObjectIDND2="0@x" ObjectIDZND0="8018@x" ObjectIDZND1="g_2a46e00@0" ObjectIDZND2="15737@x" Pin0InfoVect0LinkObjId="SW-46087_0" Pin0InfoVect1LinkObjId="g_2a46e00_0" Pin0InfoVect2LinkObjId="SM-CX_DDH.CX_DDH_GN2_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3393850_0" Pin1InfoVect1LinkObjId="SW-46088_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-402 4532,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33eca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-388 4564,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="hydroGenerator" ObjectIDND0="g_3393850@0" ObjectIDZND0="8018@x" ObjectIDZND1="g_2a46e00@0" ObjectIDZND2="15737@x" Pin0InfoVect0LinkObjId="SW-46087_0" Pin0InfoVect1LinkObjId="g_2a46e00_0" Pin0InfoVect2LinkObjId="SM-CX_DDH.CX_DDH_GN2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3393850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-388 4564,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ecc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-402 4594,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="hydroGenerator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8018@x" ObjectIDND1="g_2a46e00@0" ObjectIDND2="15737@x" ObjectIDZND0="8019@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3fd3d90@0" Pin0InfoVect0LinkObjId="SW-46088_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="g_3fd3d90_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46087_0" Pin1InfoVect1LinkObjId="g_2a46e00_0" Pin1InfoVect2LinkObjId="SM-CX_DDH.CX_DDH_GN2_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-402 4594,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ed730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-459 4532,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8018@1" ObjectIDZND0="g_3393850@0" ObjectIDZND1="8019@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3393850_0" Pin0InfoVect1LinkObjId="SW-46088_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-459 4532,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ed990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-273 4532,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2a46e00@0" ObjectIDZND0="g_3393850@0" ObjectIDZND1="8019@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3393850_0" Pin0InfoVect1LinkObjId="SW-46088_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a46e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-273 4532,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ee460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-402 4532,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="hydroGenerator" ObjectIDND0="g_3393850@0" ObjectIDND1="8019@x" ObjectIDND2="0@x" ObjectIDZND0="g_2a46e00@0" ObjectIDZND1="15737@x" Pin0InfoVect0LinkObjId="g_2a46e00_0" Pin0InfoVect1LinkObjId="SM-CX_DDH.CX_DDH_GN2_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3393850_0" Pin1InfoVect1LinkObjId="SW-46088_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-402 4532,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353e340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-273 4532,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="g_2a46e00@0" ObjectIDND1="g_3393850@0" ObjectIDND2="8019@x" ObjectIDZND0="15737@0" Pin0InfoVect0LinkObjId="SM-CX_DDH.CX_DDH_GN2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a46e00_0" Pin1InfoVect1LinkObjId="g_3393850_0" Pin1InfoVect2LinkObjId="SW-46088_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-273 4532,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_264c4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1016 4172,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="8025@x" ObjectIDND1="8026@x" ObjectIDZND0="11712@1" Pin0InfoVect0LinkObjId="g_2c65bc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46104_0" Pin1InfoVect1LinkObjId="SW-46105_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1016 4172,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_264ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1016 4206,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="11712@1" ObjectIDND1="8025@x" ObjectIDND2="8026@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_264c4c0_1" Pin1InfoVect1LinkObjId="SW-46104_0" Pin1InfoVect2LinkObjId="SW-46105_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1016 4206,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c65bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-978 4172,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" ObjectIDND0="8026@1" ObjectIDZND0="11712@1" ObjectIDZND1="8025@x" Pin0InfoVect0LinkObjId="g_264c4c0_1" Pin0InfoVect1LinkObjId="SW-46104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-978 4172,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c65e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-995 4172,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="8025@x" ObjectIDND1="8026@x" ObjectIDZND0="11712@1" Pin0InfoVect0LinkObjId="g_264c4c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46104_0" Pin1InfoVect1LinkObjId="SW-46105_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-995 4172,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c66080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-995 4166,-994 4145,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11712@1" ObjectIDND1="8026@x" ObjectIDZND0="8025@0" Pin0InfoVect0LinkObjId="SW-46104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_264c4c0_1" Pin1InfoVect1LinkObjId="SW-46105_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-995 4166,-994 4145,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec2290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-835 4172,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="8029@1" ObjectIDZND0="48330@0" Pin0InfoVect0LinkObjId="SW-311528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7e200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-835 4172,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec24f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-890 4172,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="48329@0" ObjectIDZND0="8024@0" Pin0InfoVect0LinkObjId="SW-46101_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ec2d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-890 4172,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec2d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-869 4172,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48330@1" ObjectIDZND0="48329@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-311528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-869 4172,-890 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37316" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 -1134.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5898" ObjectName="DYN-CX_DDH"/>
     <cge:Meas_Ref ObjectId="37316"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3337" y="-649"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3337" y="-1129"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3337" y="-1249"/>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_DDH.CX_DDH_GN2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.000000 -169.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15737" ObjectName="SM-CX_DDH.CX_DDH_GN2"/>
    <cge:TPSR_Ref TObjectID="15737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DDH.CX_DDH_GN1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 -172.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15736" ObjectName="SM-CX_DDH.CX_DDH_GN1"/>
    <cge:TPSR_Ref TObjectID="15736"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4670870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5253.000000 -745.000000) translate(0,15)">2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fe9fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4842.000000 -776.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fea200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -1220.000000) translate(0,15)">至10kV小厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fea440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -1112.000000) translate(0,15)">多</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fea440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -1112.000000) translate(0,33)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fea440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -1112.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fea9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4250.000000 -1137.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca0030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -604.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca02b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -162.000000) translate(0,15)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca04f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -254.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4156.000000 -237.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca0970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -162.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca0bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4499.000000 -162.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c935e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -923.000000) translate(0,12)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fcd180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -969.000000) translate(0,12)">1716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fcd710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.000000 -1020.000000) translate(0,12)">17167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fcd950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -1043.000000) translate(0,12)">1719</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fcdb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4275.000000 -981.000000) translate(0,12)">17197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fcddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4216.000000 -791.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fce010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -847.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2381000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -652.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23814f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4192.000000 -606.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2381730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.000000 -512.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2381970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -369.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2381bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -294.000000) translate(0,12)">0719</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2381df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4541.000000 -513.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2382030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4615.000000 -299.000000) translate(0,12)">0729</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2382270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4601.000000 -370.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -628.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -1107.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -1107.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -1107.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -1107.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -1107.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -1107.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a79e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3350.000000 -1107.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3ce9890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -1214.500000) translate(0,16)">多底河电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ceab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -280.000000) translate(0,15)">2号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ceab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -280.000000) translate(0,33)">SF25-10/3300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ceab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -280.000000) translate(0,51)">Pe=25MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ceab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -280.000000) translate(0,69)">Ue=10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ceab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -280.000000) translate(0,87)">Ie=1617.2A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ceab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -280.000000) translate(0,105)">COS=0.85</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -274.000000) translate(0,15)">1号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -274.000000) translate(0,33)">SF25-10/3300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -274.000000) translate(0,51)">Pe=25MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -274.000000) translate(0,69)">Ue=10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -274.000000) translate(0,87)">Ie=1617.2A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -274.000000) translate(0,105)">COS=0.85</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -810.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -810.000000) translate(0,33)">SF10-63000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -810.000000) translate(0,51)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd4890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -810.000000) translate(0,69)">121±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_411b280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3470.000000 -268.000000) translate(0,13)">53143</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_411c210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3468.000000 -239.000000) translate(0,13)">6391238</text>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DDH" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.duowan_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4172,-1059 4172,-1100 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11712" ObjectName="AC-110kV.duowan_line"/>
    <cge:TPSR_Ref TObjectID="11712_SS-47"/></metadata>
   <polyline fill="none" opacity="0" points="4172,-1059 4172,-1100 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="10524" cx="3944" cy="-576" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10524" cx="4532" cy="-576" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10524" cx="5135" cy="-576" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10524" cx="4171" cy="-576" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48329" cx="4172" cy="-890" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48329" cx="4172" cy="-890" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DDH"/>
</svg>