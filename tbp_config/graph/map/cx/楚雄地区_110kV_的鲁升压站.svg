<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-105" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 2034 1204">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="91" y2="82"/>
    <polyline points="10,25 10,39 " stroke-width="1"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="40"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <polyline points="11,25 11,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="9" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="98" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="91" y2="82"/>
    <polyline points="10,25 10,39 " stroke-width="1"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="98" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="9" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="17" y2="8"/>
    <polyline points="11,25 11,40 " stroke-width="1"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="4" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="25" y2="25"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_22e0930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape171">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="26" y1="17" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="17" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="8" x2="13" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="9" x2="12" y1="2" y2="2"/>
    <polyline arcFlag="1" points="11,30 11,30 11,30 11,30 12,30 12,31 12,31 12,31 13,31 13,32 13,32 13,32 13,33 13,33 13,33 13,34 13,34 13,35 13,35 12,35 12,35 12,36 12,36 11,36 11,36 11,36 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="10,18 11,18 11,18 11,18 11,18 12,18 12,18 12,19 12,19 12,19 12,20 13,20 13,20 13,21 13,21 13,21 12,22 12,22 12,22 12,23 12,23 12,23 11,23 11,24 11,24 11,24 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="11,24 11,24 11,24 11,24 12,24 12,24 12,25 12,25 13,25 13,25 13,26 13,26 13,26 13,27 13,27 13,28 13,28 13,28 13,29 12,29 12,29 12,29 12,30 11,30 11,30 11,30 " stroke-width="0.0170053"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="11" x2="11" y1="47" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="10" x2="10" y1="17" y2="6"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1214" width="2044" x="3112" y="-1205"/>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-743 4183,-664 4166,-664 " stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3124" y="-596"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-57070">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.712733 4256.000000 -812.108216)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10646" ObjectName="SW-CX_DL.CX_DL_1811SW"/>
     <cge:Meas_Ref ObjectId="57070"/>
    <cge:TPSR_Ref TObjectID="10646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57073">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -953.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10649" ObjectName="SW-CX_DL.CX_DL_1816SW"/>
     <cge:Meas_Ref ObjectId="57073"/>
    <cge:TPSR_Ref TObjectID="10649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57074">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -1005.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10650" ObjectName="SW-CX_DL.CX_DL_18167SW"/>
     <cge:Meas_Ref ObjectId="57074"/>
    <cge:TPSR_Ref TObjectID="10650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57071">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -876.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10647" ObjectName="SW-CX_DL.CX_DL_18117SW"/>
     <cge:Meas_Ref ObjectId="57071"/>
    <cge:TPSR_Ref TObjectID="10647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57072">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -939.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10648" ObjectName="SW-CX_DL.CX_DL_18160SW"/>
     <cge:Meas_Ref ObjectId="57072"/>
    <cge:TPSR_Ref TObjectID="10648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57069">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -796.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10645" ObjectName="SW-CX_DL.CX_DL_18110SW"/>
     <cge:Meas_Ref ObjectId="57069"/>
    <cge:TPSR_Ref TObjectID="10645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3574.000000 -319.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10636" ObjectName="SW-CX_DL.CX_DL_09317SW"/>
     <cge:Meas_Ref ObjectId="57061"/>
    <cge:TPSR_Ref TObjectID="10636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10634" ObjectName="SW-CX_DL.CX_DL_09217SW"/>
     <cge:Meas_Ref ObjectId="57059"/>
    <cge:TPSR_Ref TObjectID="10634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57063">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -319.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10638" ObjectName="SW-CX_DL.CX_DL_09117SW"/>
     <cge:Meas_Ref ObjectId="57063"/>
    <cge:TPSR_Ref TObjectID="10638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4024.000000 -316.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10632" ObjectName="SW-CX_DL.CX_DL_08817SW"/>
     <cge:Meas_Ref ObjectId="57057"/>
    <cge:TPSR_Ref TObjectID="10632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57055">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -321.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10630" ObjectName="SW-CX_DL.CX_DL_08717SW"/>
     <cge:Meas_Ref ObjectId="57055"/>
    <cge:TPSR_Ref TObjectID="10630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -319.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10628" ObjectName="SW-CX_DL.CX_DL_08617SW"/>
     <cge:Meas_Ref ObjectId="57053"/>
    <cge:TPSR_Ref TObjectID="10628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57051">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10626" ObjectName="SW-CX_DL.CX_DL_08517SW"/>
     <cge:Meas_Ref ObjectId="57051"/>
    <cge:TPSR_Ref TObjectID="10626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57049">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4624.000000 -317.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10624" ObjectName="SW-CX_DL.CX_DL_08417SW"/>
     <cge:Meas_Ref ObjectId="57049"/>
    <cge:TPSR_Ref TObjectID="10624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57047">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -316.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10622" ObjectName="SW-CX_DL.CX_DL_08317SW"/>
     <cge:Meas_Ref ObjectId="57047"/>
    <cge:TPSR_Ref TObjectID="10622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57045">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -317.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10620" ObjectName="SW-CX_DL.CX_DL_08217SW"/>
     <cge:Meas_Ref ObjectId="57045"/>
    <cge:TPSR_Ref TObjectID="10620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57043">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5074.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10618" ObjectName="SW-CX_DL.CX_DL_08117SW"/>
     <cge:Meas_Ref ObjectId="57043"/>
    <cge:TPSR_Ref TObjectID="10618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57065">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10640" ObjectName="SW-CX_DL.CX_DL_08917SW"/>
     <cge:Meas_Ref ObjectId="57065"/>
    <cge:TPSR_Ref TObjectID="10640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57067">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -594.018519)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10642" ObjectName="SW-CX_DL.CX_DL_001XC"/>
     <cge:Meas_Ref ObjectId="57067"/>
    <cge:TPSR_Ref TObjectID="10642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57067">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -513.018519)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10643" ObjectName="SW-CX_DL.CX_DL_001XC1"/>
     <cge:Meas_Ref ObjectId="57067"/>
    <cge:TPSR_Ref TObjectID="10643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -503.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11900" ObjectName="SW-CX_DL.CX_DL_10kvXC"/>
     <cge:Meas_Ref ObjectId="57105"/>
    <cge:TPSR_Ref TObjectID="11900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57075">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -679.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10651" ObjectName="SW-CX_DL.CX_DL_1010SW"/>
     <cge:Meas_Ref ObjectId="57075"/>
    <cge:TPSR_Ref TObjectID="10651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-255509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3395.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42221" ObjectName="SW-CX_DL.CX_DL_09417SW"/>
     <cge:Meas_Ref ObjectId="255509"/>
    <cge:TPSR_Ref TObjectID="42221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267557">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3920.000000 -195.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43244" ObjectName="SW-CX_DL.CX_DL_09167SW"/>
     <cge:Meas_Ref ObjectId="267557"/>
    <cge:TPSR_Ref TObjectID="43244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3870.000000 -203.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P11">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3377.000000 -187.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43299" ObjectName="SM-CX_DL.P11"/>
    <cge:TPSR_Ref TObjectID="43299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3555.000000 -191.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43298" ObjectName="SM-CX_DL.P10"/>
    <cge:TPSR_Ref TObjectID="43298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P9">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -194.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43295" ObjectName="SM-CX_DL.P9"/>
    <cge:TPSR_Ref TObjectID="43295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P8">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 -193.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43294" ObjectName="SM-CX_DL.P8"/>
    <cge:TPSR_Ref TObjectID="43294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P7">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -195.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43293" ObjectName="SM-CX_DL.P7"/>
    <cge:TPSR_Ref TObjectID="43293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P6">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 -192.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43292" ObjectName="SM-CX_DL.P6"/>
    <cge:TPSR_Ref TObjectID="43292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P5">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -192.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43291" ObjectName="SM-CX_DL.P5"/>
    <cge:TPSR_Ref TObjectID="43291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P4">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 -192.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43290" ObjectName="SM-CX_DL.P4"/>
    <cge:TPSR_Ref TObjectID="43290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -193.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43289" ObjectName="SM-CX_DL.P3"/>
    <cge:TPSR_Ref TObjectID="43289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4905.000000 -198.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43288" ObjectName="SM-CX_DL.P2"/>
    <cge:TPSR_Ref TObjectID="43288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DL.P1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5055.000000 -197.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43287" ObjectName="SM-CX_DL.P1"/>
    <cge:TPSR_Ref TObjectID="43287"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DL" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongwandiTdl_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4264,-1118 4264,-1170 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="14289" ObjectName="AC-110kV.yongwandiTdl_line"/>
    <cge:TPSR_Ref TObjectID="14289_SS-105"/></metadata>
   <polyline fill="none" opacity="0" points="4264,-1118 4264,-1170 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3f36f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -314.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4f640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e65fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -312.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f98270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4220.000000 -317.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a2a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fe53f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4670.000000 -313.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad26e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4820.000000 -312.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_409a4d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -313.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42ba220" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 -314.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42299b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4523.000000 -311.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35de5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c9b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 -1001.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ca510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -935.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ade850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -872.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dedc80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -792.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dee640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 -616.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e25900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -628.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d74230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -624.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41a7670" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3441.000000 -314.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2643340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -181.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_33530b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-881 4264,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="breaker" ObjectIDND0="10647@x" ObjectIDND1="48131@0" ObjectIDZND0="10644@0" Pin0InfoVect0LinkObjId="SW-57068_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57071_0" Pin1InfoVect1LinkObjId="g_40c4e70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-881 4264,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33532a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-1010 4198,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10650@0" ObjectIDZND0="g_33c9b70@0" Pin0InfoVect0LinkObjId="g_33c9b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-1010 4198,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3353490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-881 4245,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="10644@x" ObjectIDND1="48131@0" ObjectIDZND0="10647@1" Pin0InfoVect0LinkObjId="SW-57071_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57068_0" Pin1InfoVect1LinkObjId="g_40c4e70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-881 4245,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3353680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-881 4199,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10647@0" ObjectIDZND0="g_2ade850@0" Pin0InfoVect0LinkObjId="g_2ade850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57071_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-881 4199,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35ccff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4208,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10799@x" ObjectIDND1="10651@x" ObjectIDZND0="g_2623fe0@0" Pin0InfoVect0LinkObjId="g_2623fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35cd220_0" Pin1InfoVect1LinkObjId="SW-57075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4208,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35cd220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4263,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2623fe0@0" ObjectIDND1="10651@x" ObjectIDZND0="10799@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2623fe0_0" Pin1InfoVect1LinkObjId="SW-57075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4263,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35cd450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-944 4199,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10648@0" ObjectIDZND0="g_33ca510@0" Pin0InfoVect0LinkObjId="g_33ca510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-944 4199,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35cd680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-944 4245,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10644@x" ObjectIDND1="10649@x" ObjectIDZND0="10648@1" Pin0InfoVect0LinkObjId="SW-57072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57068_0" Pin1InfoVect1LinkObjId="SW-57073_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-944 4245,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35cd8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-927 4264,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10644@1" ObjectIDZND0="10648@x" ObjectIDZND1="10649@x" Pin0InfoVect0LinkObjId="SW-57072_0" Pin0InfoVect1LinkObjId="SW-57073_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-927 4264,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35cdae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-944 4264,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="10648@x" ObjectIDND1="10644@x" ObjectIDZND0="10649@1" Pin0InfoVect0LinkObjId="SW-57073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57072_0" Pin1InfoVect1LinkObjId="SW-57068_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-944 4264,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35cdd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-552 4012,-552 4012,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="11900@x" ObjectIDND1="g_352acc0@0" ObjectIDZND0="g_34e03a0@0" Pin0InfoVect0LinkObjId="g_34e03a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57105_0" Pin1InfoVect1LinkObjId="g_352acc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-552 4012,-552 4012,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e29bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-624 3933,-624 3933,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_3e25900@0" Pin0InfoVect0LinkObjId="g_3e25900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-624 3933,-624 3933,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e29df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-552 3966,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="11900@x" ObjectIDND1="g_34e03a0@0" ObjectIDZND0="g_352acc0@1" Pin0InfoVect0LinkObjId="g_352acc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57105_0" Pin1InfoVect1LinkObjId="g_34e03a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-552 3966,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e2a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-605 3966,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_352acc0@0" ObjectIDZND0="g_35effe0@0" Pin0InfoVect0LinkObjId="g_35effe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_352acc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-605 3966,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e2a2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4244,-1010 4264,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10650@1" ObjectIDZND0="10649@x" ObjectIDZND1="g_469c7b0@0" ObjectIDZND2="g_43f99e0@0" Pin0InfoVect0LinkObjId="SW-57073_0" Pin0InfoVect1LinkObjId="g_469c7b0_0" Pin0InfoVect2LinkObjId="g_43f99e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4244,-1010 4264,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e2a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-1010 4264,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="10650@x" ObjectIDND1="g_469c7b0@0" ObjectIDND2="g_43f99e0@0" ObjectIDZND0="10649@0" Pin0InfoVect0LinkObjId="SW-57073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57074_0" Pin1InfoVect1LinkObjId="g_469c7b0_0" Pin1InfoVect2LinkObjId="g_43f99e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-1010 4264,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e2a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-801 4245,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="10646@x" ObjectIDND1="10799@x" ObjectIDZND0="10645@1" Pin0InfoVect0LinkObjId="SW-57069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57070_0" Pin1InfoVect1LinkObjId="g_35cd220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-801 4245,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f3530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-801 4199,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10645@0" ObjectIDZND0="g_3dedc80@0" Pin0InfoVect0LinkObjId="g_3dedc80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-801 4199,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f3790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-761 4265,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10799@1" ObjectIDZND0="10645@x" ObjectIDZND1="10646@x" Pin0InfoVect0LinkObjId="SW-57069_0" Pin0InfoVect1LinkObjId="SW-57070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35cd220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-761 4265,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f39f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4229,-1079 4264,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_469c7b0@0" ObjectIDZND0="10650@x" ObjectIDZND1="10649@x" ObjectIDZND2="g_43f99e0@0" Pin0InfoVect0LinkObjId="SW-57074_0" Pin0InfoVect1LinkObjId="SW-57073_0" Pin0InfoVect2LinkObjId="g_43f99e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_469c7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4229,-1079 4264,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f3c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3815,-625 3825,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10640@1" ObjectIDZND0="g_3dee640@0" Pin0InfoVect0LinkObjId="g_3dee640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3815,-625 3825,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f3eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-638 3761,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10640@x" ObjectIDND1="10639@x" ObjectIDND2="g_3d4b770@0" ObjectIDZND0="g_3ebf8f0@0" Pin0InfoVect0LinkObjId="g_3ebf8f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57065_0" Pin1InfoVect1LinkObjId="SW-57064_0" Pin1InfoVect2LinkObjId="g_3d4b770_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-638 3761,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34f4110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3715,-656 3715,-638 3761,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_3d4b770@0" ObjectIDZND0="10640@x" ObjectIDZND1="10639@x" ObjectIDZND2="g_3ebf8f0@0" Pin0InfoVect0LinkObjId="SW-57065_0" Pin0InfoVect1LinkObjId="SW-57064_0" Pin0InfoVect2LinkObjId="g_3ebf8f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d4b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3715,-656 3715,-638 3761,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354e0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-716 3761,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3ebf8f0@1" Pin0InfoVect0LinkObjId="g_3ebf8f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-716 3761,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354e320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-720 3797,-731 3760,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_29c9bc0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c9bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-720 3797,-731 3760,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354e580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-324 3625,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10636@1" ObjectIDZND0="g_35de5e0@0" Pin0InfoVect0LinkObjId="g_35de5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57061_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-324 3625,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-324 3579,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3e5d1d0@0" ObjectIDND1="10636@x" ObjectIDND2="g_3e5d1d0@0" ObjectIDZND0="10636@0" Pin0InfoVect0LinkObjId="SW-57061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e5d1d0_0" Pin1InfoVect1LinkObjId="SW-57061_0" Pin1InfoVect2LinkObjId="g_3e5d1d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-324 3579,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10636@x" ObjectIDND1="g_3e5d1d0@0" ObjectIDND2="g_360de50@0" ObjectIDZND0="10636@x" ObjectIDZND1="g_3e5d1d0@0" ObjectIDZND2="g_360de50@0" Pin0InfoVect0LinkObjId="SW-57061_0" Pin0InfoVect1LinkObjId="g_3e5d1d0_0" Pin0InfoVect2LinkObjId="g_360de50_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57061_0" Pin1InfoVect1LinkObjId="g_3e5d1d0_0" Pin1InfoVect2LinkObjId="g_360de50_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_354eca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-324 3560,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="10636@x" ObjectIDND1="g_3e5d1d0@0" ObjectIDND2="10636@x" ObjectIDZND0="10635@0" Pin0InfoVect0LinkObjId="SW-57060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57061_0" Pin1InfoVect1LinkObjId="g_3e5d1d0_0" Pin1InfoVect2LinkObjId="SW-57061_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-324 3560,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3371e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-324 3520,-324 3520,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10636@x" ObjectIDND1="10636@x" ObjectIDND2="g_3e5d1d0@0" ObjectIDZND0="g_3e5d1d0@0" Pin0InfoVect0LinkObjId="g_3e5d1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57061_0" Pin1InfoVect1LinkObjId="SW-57061_0" Pin1InfoVect2LinkObjId="g_3e5d1d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-324 3520,-324 3520,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3372080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-324 3560,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="10636@x" ObjectIDND1="g_3e5d1d0@0" ObjectIDND2="10636@x" ObjectIDZND0="g_360de50@1" Pin0InfoVect0LinkObjId="g_360de50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57061_0" Pin1InfoVect1LinkObjId="g_3e5d1d0_0" Pin1InfoVect2LinkObjId="SW-57061_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-324 3560,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33722e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-253 3560,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_360de50@0" ObjectIDZND0="43298@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_360de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-253 3560,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3372540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3765,-323 3775,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10634@1" ObjectIDZND0="g_3f36f50@0" Pin0InfoVect0LinkObjId="g_3f36f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3765,-323 3775,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33727a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-323 3729,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3676eb0@0" ObjectIDND1="g_35a3720@0" ObjectIDND2="g_3676eb0@0" ObjectIDZND0="10634@0" Pin0InfoVect0LinkObjId="SW-57059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3676eb0_0" Pin1InfoVect1LinkObjId="g_35a3720_0" Pin1InfoVect2LinkObjId="g_3676eb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-323 3729,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3372a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3676eb0@0" ObjectIDND1="g_35a3720@0" ObjectIDND2="10634@x" ObjectIDZND0="g_3676eb0@0" ObjectIDZND1="g_35a3720@0" ObjectIDZND2="10634@x" Pin0InfoVect0LinkObjId="g_3676eb0_0" Pin0InfoVect1LinkObjId="g_35a3720_0" Pin0InfoVect2LinkObjId="SW-57059_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3676eb0_0" Pin1InfoVect1LinkObjId="g_35a3720_0" Pin1InfoVect2LinkObjId="SW-57059_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_466fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-323 3710,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3676eb0@0" ObjectIDND1="g_35a3720@0" ObjectIDND2="g_3676eb0@0" ObjectIDZND0="10633@0" Pin0InfoVect0LinkObjId="SW-57058_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3676eb0_0" Pin1InfoVect1LinkObjId="g_35a3720_0" Pin1InfoVect2LinkObjId="g_3676eb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-323 3710,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_466fd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-323 3670,-323 3670,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3676eb0@0" ObjectIDND1="g_3676eb0@0" ObjectIDND2="g_35a3720@0" ObjectIDZND0="g_35a3720@0" Pin0InfoVect0LinkObjId="g_35a3720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3676eb0_0" Pin1InfoVect1LinkObjId="g_3676eb0_0" Pin1InfoVect2LinkObjId="g_35a3720_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-323 3670,-323 3670,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_466ff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-323 3710,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_35a3720@0" ObjectIDND1="g_3676eb0@0" ObjectIDND2="g_35a3720@0" ObjectIDZND0="g_3676eb0@1" Pin0InfoVect0LinkObjId="g_3676eb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35a3720_0" Pin1InfoVect1LinkObjId="g_3676eb0_0" Pin1InfoVect2LinkObjId="g_35a3720_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-323 3710,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46701d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-253 3710,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_3676eb0@0" ObjectIDZND0="43295@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P9_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3676eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-253 3710,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4670430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-324 3925,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10638@1" ObjectIDZND0="g_2b4f640@0" Pin0InfoVect0LinkObjId="g_2b4f640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-324 3925,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4670690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-324 3879,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_34de760@0" ObjectIDND1="g_3db1330@0" ObjectIDND2="g_34de760@0" ObjectIDZND0="10638@0" Pin0InfoVect0LinkObjId="SW-57063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34de760_0" Pin1InfoVect1LinkObjId="g_3db1330_0" Pin1InfoVect2LinkObjId="g_34de760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-324 3879,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a91f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_34de760@0" ObjectIDND1="g_3db1330@0" ObjectIDND2="10638@x" ObjectIDZND0="g_34de760@0" ObjectIDZND1="g_3db1330@0" ObjectIDZND2="10638@x" Pin0InfoVect0LinkObjId="g_34de760_0" Pin0InfoVect1LinkObjId="g_3db1330_0" Pin0InfoVect2LinkObjId="SW-57063_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34de760_0" Pin1InfoVect1LinkObjId="g_3db1330_0" Pin1InfoVect2LinkObjId="SW-57063_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a92160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-324 3861,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34de760@0" ObjectIDND1="g_3db1330@0" ObjectIDND2="g_34de760@0" ObjectIDZND0="10637@0" Pin0InfoVect0LinkObjId="SW-57062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34de760_0" Pin1InfoVect1LinkObjId="g_3db1330_0" Pin1InfoVect2LinkObjId="g_34de760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-324 3861,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a923c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-324 3820,-324 3820,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_34de760@0" ObjectIDND1="g_34de760@0" ObjectIDND2="g_3db1330@0" ObjectIDZND0="g_3db1330@0" Pin0InfoVect0LinkObjId="g_3db1330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34de760_0" Pin1InfoVect1LinkObjId="g_34de760_0" Pin1InfoVect2LinkObjId="g_3db1330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-324 3820,-324 3820,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a92620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-324 3861,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3db1330@0" ObjectIDND1="g_34de760@0" ObjectIDND2="g_3db1330@0" ObjectIDZND0="g_34de760@1" Pin0InfoVect0LinkObjId="g_34de760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3db1330_0" Pin1InfoVect1LinkObjId="g_34de760_0" Pin1InfoVect2LinkObjId="g_3db1330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-324 3861,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a92880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-321 4075,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10632@1" ObjectIDZND0="g_3e65fc0@0" Pin0InfoVect0LinkObjId="g_3e65fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-321 4075,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a92ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-321 4029,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3425dd0@0" ObjectIDND1="g_22ec670@0" ObjectIDND2="g_3425dd0@0" ObjectIDZND0="10632@0" Pin0InfoVect0LinkObjId="SW-57057_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3425dd0_0" Pin1InfoVect1LinkObjId="g_22ec670_0" Pin1InfoVect2LinkObjId="g_3425dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-321 4029,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3425dd0@0" ObjectIDND1="g_22ec670@0" ObjectIDND2="10632@x" ObjectIDZND0="g_3425dd0@0" ObjectIDZND1="g_22ec670@0" ObjectIDZND2="10632@x" Pin0InfoVect0LinkObjId="g_3425dd0_0" Pin0InfoVect1LinkObjId="g_22ec670_0" Pin0InfoVect2LinkObjId="SW-57057_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3425dd0_0" Pin1InfoVect1LinkObjId="g_22ec670_0" Pin1InfoVect2LinkObjId="SW-57057_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348f150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-321 4010,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3425dd0@0" ObjectIDND1="g_22ec670@0" ObjectIDND2="g_3425dd0@0" ObjectIDZND0="10631@0" Pin0InfoVect0LinkObjId="SW-57056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3425dd0_0" Pin1InfoVect1LinkObjId="g_22ec670_0" Pin1InfoVect2LinkObjId="g_3425dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-321 4010,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348f3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-321 3970,-321 3970,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3425dd0@0" ObjectIDND1="g_3425dd0@0" ObjectIDND2="g_22ec670@0" ObjectIDZND0="g_22ec670@0" Pin0InfoVect0LinkObjId="g_22ec670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3425dd0_0" Pin1InfoVect1LinkObjId="g_3425dd0_0" Pin1InfoVect2LinkObjId="g_22ec670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-321 3970,-321 3970,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348f610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-321 4010,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22ec670@0" ObjectIDND1="g_3425dd0@0" ObjectIDND2="g_22ec670@0" ObjectIDZND0="g_3425dd0@1" Pin0InfoVect0LinkObjId="g_3425dd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22ec670_0" Pin1InfoVect1LinkObjId="g_3425dd0_0" Pin1InfoVect2LinkObjId="g_22ec670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-321 4010,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348f870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-253 4010,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_3425dd0@0" ObjectIDZND0="43294@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P8_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3425dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-253 4010,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348fad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-326 4225,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10630@1" ObjectIDZND0="g_3f98270@0" Pin0InfoVect0LinkObjId="g_3f98270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-326 4225,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361e4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-326 4179,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_367caa0@0" ObjectIDND1="g_3e94ff0@0" ObjectIDND2="g_367caa0@0" ObjectIDZND0="10630@0" Pin0InfoVect0LinkObjId="SW-57055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_367caa0_0" Pin1InfoVect1LinkObjId="g_3e94ff0_0" Pin1InfoVect2LinkObjId="g_367caa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-326 4179,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361e730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_367caa0@0" ObjectIDND1="g_3e94ff0@0" ObjectIDND2="10630@x" ObjectIDZND0="g_367caa0@0" ObjectIDZND1="g_3e94ff0@0" ObjectIDZND2="10630@x" Pin0InfoVect0LinkObjId="g_367caa0_0" Pin0InfoVect1LinkObjId="g_3e94ff0_0" Pin0InfoVect2LinkObjId="SW-57055_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_367caa0_0" Pin1InfoVect1LinkObjId="g_3e94ff0_0" Pin1InfoVect2LinkObjId="SW-57055_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361e990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-326 4160,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_367caa0@0" ObjectIDND1="g_3e94ff0@0" ObjectIDND2="g_367caa0@0" ObjectIDZND0="10629@0" Pin0InfoVect0LinkObjId="SW-57054_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_367caa0_0" Pin1InfoVect1LinkObjId="g_3e94ff0_0" Pin1InfoVect2LinkObjId="g_367caa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-326 4160,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361ebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-326 4120,-326 4120,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_367caa0@0" ObjectIDND1="g_367caa0@0" ObjectIDND2="g_3e94ff0@0" ObjectIDZND0="g_3e94ff0@0" Pin0InfoVect0LinkObjId="g_3e94ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_367caa0_0" Pin1InfoVect1LinkObjId="g_367caa0_0" Pin1InfoVect2LinkObjId="g_3e94ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-326 4120,-326 4120,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361ee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-326 4160,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3e94ff0@0" ObjectIDND1="g_367caa0@0" ObjectIDND2="g_3e94ff0@0" ObjectIDZND0="g_367caa0@1" Pin0InfoVect0LinkObjId="g_367caa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e94ff0_0" Pin1InfoVect1LinkObjId="g_367caa0_0" Pin1InfoVect2LinkObjId="g_3e94ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-326 4160,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_361f0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-253 4160,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_367caa0@0" ObjectIDZND0="43293@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P7_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367caa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-253 4160,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e49030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-324 4375,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10628@1" ObjectIDZND0="g_35a2a50@0" Pin0InfoVect0LinkObjId="g_35a2a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57053_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-324 4375,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e49290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-324 4329,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_231bff0@0" ObjectIDND1="g_354f140@0" ObjectIDND2="g_231bff0@0" ObjectIDZND0="10628@0" Pin0InfoVect0LinkObjId="SW-57053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_231bff0_0" Pin1InfoVect1LinkObjId="g_354f140_0" Pin1InfoVect2LinkObjId="g_231bff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-324 4329,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e494f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_231bff0@0" ObjectIDND1="g_354f140@0" ObjectIDND2="10628@x" ObjectIDZND0="g_231bff0@0" ObjectIDZND1="g_354f140@0" ObjectIDZND2="10628@x" Pin0InfoVect0LinkObjId="g_231bff0_0" Pin0InfoVect1LinkObjId="g_354f140_0" Pin0InfoVect2LinkObjId="SW-57053_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_231bff0_0" Pin1InfoVect1LinkObjId="g_354f140_0" Pin1InfoVect2LinkObjId="SW-57053_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e49750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-324 4310,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_231bff0@0" ObjectIDND1="g_354f140@0" ObjectIDND2="g_231bff0@0" ObjectIDZND0="10627@0" Pin0InfoVect0LinkObjId="SW-57052_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_231bff0_0" Pin1InfoVect1LinkObjId="g_354f140_0" Pin1InfoVect2LinkObjId="g_231bff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-324 4310,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e499b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-324 4270,-324 4270,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_231bff0@0" ObjectIDND1="g_231bff0@0" ObjectIDND2="g_354f140@0" ObjectIDZND0="g_354f140@0" Pin0InfoVect0LinkObjId="g_354f140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_231bff0_0" Pin1InfoVect1LinkObjId="g_231bff0_0" Pin1InfoVect2LinkObjId="g_354f140_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-324 4270,-324 4270,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e49c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-324 4310,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_354f140@0" ObjectIDND1="g_231bff0@0" ObjectIDND2="g_354f140@0" ObjectIDZND0="g_231bff0@1" Pin0InfoVect0LinkObjId="g_231bff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_354f140_0" Pin1InfoVect1LinkObjId="g_231bff0_0" Pin1InfoVect2LinkObjId="g_354f140_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-324 4310,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de4780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-252 4310,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_231bff0@0" ObjectIDZND0="43292@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P6_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231bff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-252 4310,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de49e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-320 4479,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_43a6420@0" ObjectIDND1="g_3c9a6f0@0" ObjectIDND2="g_43a6420@0" ObjectIDZND0="10626@0" Pin0InfoVect0LinkObjId="SW-57051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43a6420_0" Pin1InfoVect1LinkObjId="g_3c9a6f0_0" Pin1InfoVect2LinkObjId="g_43a6420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-320 4479,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de4c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_43a6420@0" ObjectIDND1="g_3c9a6f0@0" ObjectIDND2="10626@x" ObjectIDZND0="g_43a6420@0" ObjectIDZND1="g_3c9a6f0@0" ObjectIDZND2="10626@x" Pin0InfoVect0LinkObjId="g_43a6420_0" Pin0InfoVect1LinkObjId="g_3c9a6f0_0" Pin0InfoVect2LinkObjId="SW-57051_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43a6420_0" Pin1InfoVect1LinkObjId="g_3c9a6f0_0" Pin1InfoVect2LinkObjId="SW-57051_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de4ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-320 4460,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_43a6420@0" ObjectIDND1="g_3c9a6f0@0" ObjectIDND2="g_43a6420@0" ObjectIDZND0="10625@0" Pin0InfoVect0LinkObjId="SW-57050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43a6420_0" Pin1InfoVect1LinkObjId="g_3c9a6f0_0" Pin1InfoVect2LinkObjId="g_43a6420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-320 4460,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de5100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-320 4420,-320 4420,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_43a6420@0" ObjectIDND1="g_43a6420@0" ObjectIDND2="g_3c9a6f0@0" ObjectIDZND0="g_3c9a6f0@0" Pin0InfoVect0LinkObjId="g_3c9a6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43a6420_0" Pin1InfoVect1LinkObjId="g_43a6420_0" Pin1InfoVect2LinkObjId="g_3c9a6f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-320 4420,-320 4420,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de5360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-320 4460,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3c9a6f0@0" ObjectIDND1="g_43a6420@0" ObjectIDND2="g_3c9a6f0@0" ObjectIDZND0="g_43a6420@1" Pin0InfoVect0LinkObjId="g_43a6420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c9a6f0_0" Pin1InfoVect1LinkObjId="g_43a6420_0" Pin1InfoVect2LinkObjId="g_3c9a6f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-320 4460,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d02d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-252 4460,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_43a6420@0" ObjectIDZND0="43291@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P5_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43a6420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-252 4460,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d02fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-322 4675,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10624@1" ObjectIDZND0="g_3fe53f0@0" Pin0InfoVect0LinkObjId="g_3fe53f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57049_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-322 4675,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d03240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-322 4629,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3eef240@0" ObjectIDND1="g_3db3840@0" ObjectIDND2="g_3eef240@0" ObjectIDZND0="10624@0" Pin0InfoVect0LinkObjId="SW-57049_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3eef240_0" Pin1InfoVect1LinkObjId="g_3db3840_0" Pin1InfoVect2LinkObjId="g_3eef240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-322 4629,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d034a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3eef240@0" ObjectIDND1="g_3db3840@0" ObjectIDND2="10624@x" ObjectIDZND0="g_3eef240@0" ObjectIDZND1="g_3db3840@0" ObjectIDZND2="10624@x" Pin0InfoVect0LinkObjId="g_3eef240_0" Pin0InfoVect1LinkObjId="g_3db3840_0" Pin0InfoVect2LinkObjId="SW-57049_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3eef240_0" Pin1InfoVect1LinkObjId="g_3db3840_0" Pin1InfoVect2LinkObjId="SW-57049_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d03700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-322 4610,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3eef240@0" ObjectIDND1="g_3db3840@0" ObjectIDND2="g_3eef240@0" ObjectIDZND0="10623@0" Pin0InfoVect0LinkObjId="SW-57048_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3eef240_0" Pin1InfoVect1LinkObjId="g_3db3840_0" Pin1InfoVect2LinkObjId="g_3eef240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-322 4610,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d03960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-322 4570,-322 4570,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3eef240@0" ObjectIDND1="g_3eef240@0" ObjectIDND2="g_3db3840@0" ObjectIDZND0="g_3db3840@0" Pin0InfoVect0LinkObjId="g_3db3840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3eef240_0" Pin1InfoVect1LinkObjId="g_3eef240_0" Pin1InfoVect2LinkObjId="g_3db3840_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-322 4570,-322 4570,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-322 4610,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3db3840@0" ObjectIDND1="g_3eef240@0" ObjectIDND2="g_3db3840@0" ObjectIDZND0="g_3eef240@1" Pin0InfoVect0LinkObjId="g_3eef240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3db3840_0" Pin1InfoVect1LinkObjId="g_3eef240_0" Pin1InfoVect2LinkObjId="g_3db3840_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-322 4610,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f0da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-251 4610,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_3eef240@0" ObjectIDZND0="43290@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eef240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-251 4610,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f1000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4815,-321 4825,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10622@1" ObjectIDZND0="g_2ad26e0@0" Pin0InfoVect0LinkObjId="g_2ad26e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57047_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4815,-321 4825,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f1260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-321 4779,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="10621@x" ObjectIDND1="g_2670540@0" ObjectIDND2="g_40cf270@0" ObjectIDZND0="10622@0" Pin0InfoVect0LinkObjId="SW-57047_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57046_0" Pin1InfoVect1LinkObjId="g_2670540_0" Pin1InfoVect2LinkObjId="g_40cf270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-321 4779,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-321 4760,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10621@x" ObjectIDND1="g_2670540@0" ObjectIDND2="10622@x" ObjectIDZND0="10621@x" ObjectIDZND1="g_2670540@0" ObjectIDZND2="10622@x" Pin0InfoVect0LinkObjId="SW-57046_0" Pin0InfoVect1LinkObjId="g_2670540_0" Pin0InfoVect2LinkObjId="SW-57047_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57046_0" Pin1InfoVect1LinkObjId="g_2670540_0" Pin1InfoVect2LinkObjId="SW-57047_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-321 4760,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f1720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-321 4720,-321 4720,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="10621@x" ObjectIDND1="g_2670540@0" ObjectIDND2="10622@x" ObjectIDZND0="g_40cf270@0" Pin0InfoVect0LinkObjId="g_40cf270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57046_0" Pin1InfoVect1LinkObjId="g_2670540_0" Pin1InfoVect2LinkObjId="SW-57047_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-321 4720,-321 4720,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_363ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-321 4760,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10621@x" ObjectIDND1="10622@x" ObjectIDND2="g_40cf270@0" ObjectIDZND0="g_2670540@1" Pin0InfoVect0LinkObjId="g_2670540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57046_0" Pin1InfoVect1LinkObjId="SW-57047_0" Pin1InfoVect2LinkObjId="g_40cf270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-321 4760,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_363f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-251 4760,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2670540@0" ObjectIDZND0="43289@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2670540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-251 4760,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_363f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4965,-322 4975,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10620@1" ObjectIDZND0="g_409a4d0@0" Pin0InfoVect0LinkObjId="g_409a4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57045_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4965,-322 4975,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_363f650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-322 4929,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ea4860@0" ObjectIDND1="g_360f900@0" ObjectIDND2="g_3ea4860@0" ObjectIDZND0="10620@0" Pin0InfoVect0LinkObjId="SW-57045_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ea4860_0" Pin1InfoVect1LinkObjId="g_360f900_0" Pin1InfoVect2LinkObjId="g_3ea4860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-322 4929,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_363f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3ea4860@0" ObjectIDND1="g_360f900@0" ObjectIDND2="10620@x" ObjectIDZND0="g_3ea4860@0" ObjectIDZND1="g_360f900@0" ObjectIDZND2="10620@x" Pin0InfoVect0LinkObjId="g_3ea4860_0" Pin0InfoVect1LinkObjId="g_360f900_0" Pin0InfoVect2LinkObjId="SW-57045_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ea4860_0" Pin1InfoVect1LinkObjId="g_360f900_0" Pin1InfoVect2LinkObjId="SW-57045_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_363fb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-322 4870,-322 4870,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_360f900@0" ObjectIDND1="g_3ea4860@0" ObjectIDND2="g_360f900@0" ObjectIDZND0="g_3ea4860@0" Pin0InfoVect0LinkObjId="g_3ea4860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_360f900_0" Pin1InfoVect1LinkObjId="g_3ea4860_0" Pin1InfoVect2LinkObjId="g_360f900_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-322 4870,-322 4870,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b3fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-322 4910,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3ea4860@0" ObjectIDND1="g_3ea4860@0" ObjectIDND2="g_360f900@0" ObjectIDZND0="g_360f900@1" Pin0InfoVect0LinkObjId="g_360f900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ea4860_0" Pin1InfoVect1LinkObjId="g_3ea4860_0" Pin1InfoVect2LinkObjId="g_360f900_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-322 4910,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-251 4910,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_360f900@0" ObjectIDZND0="43288@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_360f900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-251 4910,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5115,-323 5125,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10618@1" ObjectIDZND0="g_42ba220@0" Pin0InfoVect0LinkObjId="g_42ba220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5115,-323 5125,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b46c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-323 5079,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="10617@x" ObjectIDND1="g_2a79eb0@0" ObjectIDND2="g_32601a0@0" ObjectIDZND0="10618@0" Pin0InfoVect0LinkObjId="SW-57043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57042_0" Pin1InfoVect1LinkObjId="g_2a79eb0_0" Pin1InfoVect2LinkObjId="g_32601a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-323 5079,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-323 5060,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10617@x" ObjectIDND1="g_2a79eb0@0" ObjectIDND2="10618@x" ObjectIDZND0="10617@x" ObjectIDZND1="g_2a79eb0@0" ObjectIDZND2="10618@x" Pin0InfoVect0LinkObjId="SW-57042_0" Pin0InfoVect1LinkObjId="g_2a79eb0_0" Pin0InfoVect2LinkObjId="SW-57043_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57042_0" Pin1InfoVect1LinkObjId="g_2a79eb0_0" Pin1InfoVect2LinkObjId="SW-57043_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-323 5060,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-323 5020,-323 5020,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="10617@x" ObjectIDND1="g_2a79eb0@0" ObjectIDND2="10618@x" ObjectIDZND0="g_32601a0@0" Pin0InfoVect0LinkObjId="g_32601a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57042_0" Pin1InfoVect1LinkObjId="g_2a79eb0_0" Pin1InfoVect2LinkObjId="SW-57043_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-323 5020,-323 5020,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_410cd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-323 5060,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10617@x" ObjectIDND1="10618@x" ObjectIDND2="g_32601a0@0" ObjectIDZND0="g_2a79eb0@1" Pin0InfoVect0LinkObjId="g_2a79eb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57042_0" Pin1InfoVect1LinkObjId="SW-57043_0" Pin1InfoVect2LinkObjId="g_32601a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-323 5060,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_410cff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-251 5060,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2a79eb0@0" ObjectIDZND0="43287@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a79eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-251 5060,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_410d250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-625 3779,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="10639@x" ObjectIDND1="g_3d4b770@0" ObjectIDND2="g_3ebf8f0@0" ObjectIDZND0="10640@0" Pin0InfoVect0LinkObjId="SW-57065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57064_0" Pin1InfoVect1LinkObjId="g_3d4b770_0" Pin1InfoVect2LinkObjId="g_3ebf8f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-625 3779,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_410d4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-735 4756,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3c9d4b0@1" ObjectIDZND0="g_2628250@1" Pin0InfoVect0LinkObjId="g_2628250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9d4b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-735 4756,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_410d710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-659 4756,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2628250@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2628250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-659 4756,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_410d970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4658,-832 4755,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDZND0="g_3c9d4b0@0" ObjectIDZND1="g_35337c0@0" Pin0InfoVect0LinkObjId="g_3c9d4b0_0" Pin0InfoVect1LinkObjId="g_35337c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4658,-832 4755,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b55dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-832 4855,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_3c9d4b0@0" ObjectIDND1="g_35337c0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c9d4b0_0" Pin1InfoVect1LinkObjId="g_35337c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-832 4855,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b56020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-785 4806,-804 4755,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_35337c0@0" ObjectIDZND0="g_3c9d4b0@0" Pin0InfoVect0LinkObjId="g_3c9d4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35337c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-785 4806,-804 4755,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b56280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-780 4755,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3c9d4b0@0" ObjectIDZND0="g_35337c0@0" Pin0InfoVect0LinkObjId="g_35337c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9d4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-780 4755,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b564e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-804 4755,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_3c9d4b0@0" ObjectIDND1="g_35337c0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c9d4b0_0" Pin1InfoVect1LinkObjId="g_35337c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-804 4755,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b56740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-321 4760,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2670540@0" ObjectIDND1="10622@x" ObjectIDND2="g_40cf270@0" ObjectIDZND0="10621@0" Pin0InfoVect0LinkObjId="SW-57046_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2670540_0" Pin1InfoVect1LinkObjId="SW-57047_0" Pin1InfoVect2LinkObjId="g_40cf270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-321 4760,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b569a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-322 4910,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3ea4860@0" ObjectIDND1="g_360f900@0" ObjectIDND2="g_3ea4860@0" ObjectIDZND0="10619@0" Pin0InfoVect0LinkObjId="SW-57044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ea4860_0" Pin1InfoVect1LinkObjId="g_360f900_0" Pin1InfoVect2LinkObjId="g_3ea4860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-322 4910,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a89140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-323 5060,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a79eb0@0" ObjectIDND1="10618@x" ObjectIDND2="g_32601a0@0" ObjectIDZND0="10617@0" Pin0InfoVect0LinkObjId="SW-57042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a79eb0_0" Pin1InfoVect1LinkObjId="SW-57043_0" Pin1InfoVect2LinkObjId="g_32601a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-323 5060,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a893a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-320 4528,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10626@1" ObjectIDZND0="g_42299b0@0" Pin0InfoVect0LinkObjId="g_42299b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-320 4528,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a89600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-1079 4264,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_469c7b0@0" ObjectIDND1="g_43f99e0@0" ObjectIDND2="14289@1" ObjectIDZND0="10650@x" ObjectIDZND1="10649@x" Pin0InfoVect0LinkObjId="SW-57074_0" Pin0InfoVect1LinkObjId="SW-57073_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_469c7b0_0" Pin1InfoVect1LinkObjId="g_43f99e0_0" Pin1InfoVect2LinkObjId="g_3440960_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-1079 4264,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a89860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-607 3761,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10639@1" ObjectIDZND0="10640@x" ObjectIDZND1="g_3d4b770@0" ObjectIDZND2="g_3ebf8f0@0" Pin0InfoVect0LinkObjId="SW-57065_0" Pin0InfoVect1LinkObjId="g_3d4b770_0" Pin0InfoVect2LinkObjId="g_3ebf8f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-607 3761,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a89ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-625 3761,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10640@x" ObjectIDND1="10639@x" ObjectIDZND0="g_3d4b770@0" ObjectIDZND1="g_3ebf8f0@0" Pin0InfoVect0LinkObjId="g_3d4b770_0" Pin0InfoVect1LinkObjId="g_3ebf8f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57065_0" Pin1InfoVect1LinkObjId="SW-57064_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-625 3761,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a89d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-552 3966,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_352acc0@0" ObjectIDND1="g_34e03a0@0" ObjectIDZND0="11900@1" Pin0InfoVect0LinkObjId="SW-57105_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_352acc0_0" Pin1InfoVect1LinkObjId="g_34e03a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-552 3966,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3440700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-253 3861,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34de760@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34de760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-253 3861,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3440960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-1094 4264,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_469c7b0@0" ObjectIDND1="10650@x" ObjectIDND2="10649@x" ObjectIDZND0="14289@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_469c7b0_0" Pin1InfoVect1LinkObjId="SW-57074_0" Pin1InfoVect2LinkObjId="SW-57073_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-1094 4264,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3440bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-1083 4298,-1094 4264,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_43f99e0@0" ObjectIDZND0="g_469c7b0@0" ObjectIDZND1="10650@x" ObjectIDZND2="10649@x" Pin0InfoVect0LinkObjId="g_469c7b0_0" Pin0InfoVect1LinkObjId="SW-57074_0" Pin0InfoVect2LinkObjId="SW-57073_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43f99e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-1083 4298,-1094 4264,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3440e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-1094 4264,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_43f99e0@0" ObjectIDND1="14289@1" ObjectIDZND0="g_469c7b0@0" ObjectIDZND1="10650@x" ObjectIDZND2="10649@x" Pin0InfoVect0LinkObjId="g_469c7b0_0" Pin0InfoVect1LinkObjId="SW-57074_0" Pin0InfoVect2LinkObjId="SW-57073_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_43f99e0_0" Pin1InfoVect1LinkObjId="g_3440960_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-1094 4264,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32edca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4166,-650 4166,-664 4151,-664 4151,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3d74230@0" ObjectIDZND0="10651@1" Pin0InfoVect0LinkObjId="SW-57075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d74230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4166,-650 4166,-664 4151,-664 4151,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32edf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4151,-743 4151,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10799@x" ObjectIDND1="g_2623fe0@0" ObjectIDZND0="10651@0" Pin0InfoVect0LinkObjId="SW-57075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35cd220_0" Pin1InfoVect1LinkObjId="g_2623fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4151,-743 4151,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a64fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-601 4264,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10642@1" ObjectIDZND0="10641@1" Pin0InfoVect0LinkObjId="SW-57066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57067_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-601 4264,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a651b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-557 4264,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10641@0" ObjectIDZND0="10643@0" Pin0InfoVect0LinkObjId="SW-57067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-557 4264,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a653e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-635 4313,-647 4264,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_25d4520@0" ObjectIDZND0="10642@x" ObjectIDZND1="10799@x" Pin0InfoVect0LinkObjId="SW-57067_0" Pin0InfoVect1LinkObjId="g_35cd220_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d4520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-635 4313,-647 4264,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43ae0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-681 4264,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10799@0" ObjectIDZND0="10642@x" ObjectIDZND1="g_25d4520@0" Pin0InfoVect0LinkObjId="SW-57067_0" Pin0InfoVect1LinkObjId="g_25d4520_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35cd220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-681 4264,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43ae2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-647 4264,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_25d4520@0" ObjectIDND1="10799@x" ObjectIDZND0="10642@0" Pin0InfoVect0LinkObjId="SW-57067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d4520_0" Pin1InfoVect1LinkObjId="g_35cd220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-647 4264,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a7e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3436,-323 3446,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42221@1" ObjectIDZND0="g_41a7670@0" Pin0InfoVect0LinkObjId="g_41a7670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-255509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3436,-323 3446,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a8080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3382,-323 3400,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="42221@x" ObjectIDND1="g_354c870@0" ObjectIDND2="g_34d09f0@0" ObjectIDZND0="42221@0" Pin0InfoVect0LinkObjId="SW-255509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-255509_0" Pin1InfoVect1LinkObjId="g_354c870_0" Pin1InfoVect2LinkObjId="g_34d09f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3382,-323 3400,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a82b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3382,-323 3382,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="42221@x" ObjectIDND1="g_354c870@0" ObjectIDND2="g_34d09f0@0" ObjectIDZND0="42221@x" ObjectIDZND1="g_354c870@0" ObjectIDZND2="g_34d09f0@0" Pin0InfoVect0LinkObjId="SW-255509_0" Pin0InfoVect1LinkObjId="g_354c870_0" Pin0InfoVect2LinkObjId="g_34d09f0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-255509_0" Pin1InfoVect1LinkObjId="g_354c870_0" Pin1InfoVect2LinkObjId="g_34d09f0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3382,-323 3382,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c6aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3382,-322 3382,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="42221@x" ObjectIDND1="42221@x" ObjectIDND2="g_354c870@0" ObjectIDZND0="42219@0" Pin0InfoVect0LinkObjId="SW-255507_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-255509_0" Pin1InfoVect1LinkObjId="SW-255509_0" Pin1InfoVect2LinkObjId="g_354c870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3382,-322 3382,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c6ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3381,-441 3381,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="42219@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_2aa4140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-255507_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3381,-441 3381,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3382,-323 3341,-323 3341,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42221@x" ObjectIDND1="42221@x" ObjectIDND2="g_354c870@0" ObjectIDZND0="g_354c870@0" Pin0InfoVect0LinkObjId="g_354c870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-255509_0" Pin1InfoVect1LinkObjId="SW-255509_0" Pin1InfoVect2LinkObjId="g_354c870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3382,-323 3341,-323 3341,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c71a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3382,-323 3382,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42221@x" ObjectIDND1="42221@x" ObjectIDND2="g_354c870@0" ObjectIDZND0="g_34d09f0@1" Pin0InfoVect0LinkObjId="g_34d09f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-255509_0" Pin1InfoVect1LinkObjId="SW-255509_0" Pin1InfoVect2LinkObjId="g_354c870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3382,-323 3382,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c7400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3382,-249 3382,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_34d09f0@0" ObjectIDZND0="43299@0" Pin0InfoVect0LinkObjId="SM-CX_DL.P11_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d09f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3382,-249 3382,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa4140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-443 3560,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10635@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-443 3560,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4139e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-442 3710,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10633@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-442 3710,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_413a530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-516 3761,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10639@0" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-516 3761,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3610560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-442 3861,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10637@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-442 3861,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3610d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-510 3966,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11900@0" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57105_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-510 3966,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40aed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-442 4010,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10631@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-442 4010,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40af540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-442 4160,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10629@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57054_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-442 4160,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f6f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-519 4264,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10643@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57067_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-519 4264,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-441 4310,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10627@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57052_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-441 4310,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f7970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-440 4460,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10625@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-440 4460,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cab9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-440 4610,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10623@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57048_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-440 4610,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cac1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-441 4760,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10621@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57046_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-441 4760,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db7f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-441 4910,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10619@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-441 4910,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db8780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-441 5060,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="10617@1" ObjectIDZND0="10666@0" Pin0InfoVect0LinkObjId="g_33c6ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-441 5060,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2643cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-190 3925,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43244@0" ObjectIDZND0="g_2643340@0" Pin0InfoVect0LinkObjId="g_2643340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-190 3925,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2643f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-190 3879,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_22e0340@0" ObjectIDZND0="43244@1" Pin0InfoVect0LinkObjId="SW-267557_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_22e0340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-190 3879,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e3bb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-208 3861,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43244@x" ObjectIDZND1="g_22e0340@0" Pin0InfoVect0LinkObjId="SW-267557_0" Pin0InfoVect1LinkObjId="g_22e0340_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-208 3861,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e3bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-190 3861,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="43244@x" ObjectIDND1="0@x" ObjectIDZND0="g_22e0340@0" Pin0InfoVect0LinkObjId="g_22e0340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267557_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-190 3861,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c4e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-881 4264,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="busSection" ObjectIDND0="10647@x" ObjectIDND1="10644@x" ObjectIDZND0="48131@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57071_0" Pin1InfoVect1LinkObjId="SW-57068_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-881 4264,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c50d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-816 4265,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="10646@1" ObjectIDZND0="10645@x" ObjectIDZND1="10799@x" Pin0InfoVect0LinkObjId="SW-57069_0" Pin0InfoVect1LinkObjId="g_35cd220_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-816 4265,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c5330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-864 4265,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="48131@0" ObjectIDZND0="10646@0" Pin0InfoVect0LinkObjId="SW-57070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40c4e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-864 4265,-841 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="3381" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="3560" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="3710" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="3761" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="3861" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="3966" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4010" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4160" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4310" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4460" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4610" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4760" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4910" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="5060" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10666" cx="4264" cy="-482" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48131" cx="4264" cy="-864" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48131" cx="4265" cy="-864" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-56762" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3412.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10447" ObjectName="DYN-CX_DL"/>
     <cge:Meas_Ref ObjectId="56762"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_261f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e184e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e184e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e184e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e184e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e184e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e184e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e184e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2aae930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3277.000000 -1166.500000) translate(0,16)">的鲁升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22e0df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4270.000000 -1143.000000) translate(0,15)">永万的线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40feea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4665.000000 -872.000000) translate(0,15)">35kV维的变10kV的鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -852.000000) translate(0,15)">N34</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4619.000000 -614.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,15)">2号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,33)">S11-630/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,51)">630kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,69)">10±2х2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,87)">D,YN11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40ff730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -670.000000) translate(0,105)">U%=4.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349dad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -844.000000) translate(0,15)">1号接地站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349dc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4186.000000 -778.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -670.000000) translate(0,15)">10kV消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349dfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -681.000000) translate(0,15)">10kV汇流段TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.000000 -784.000000) translate(0,15)">1号接地站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.000000 -784.000000) translate(0,33)">DKSC-1000/10.5-630/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.000000 -784.000000) translate(0,51)">（含315kVA消弧线圈容量）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.000000 -784.000000) translate(0,69)">10.5±5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.000000 -784.000000) translate(0,87)">ZN,YN11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.000000 -784.000000) translate(0,105)">U%=6.0</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -776.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -776.000000) translate(0,33)">SZ11-50000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -776.000000) translate(0,51)">121±8х1.25%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -776.000000) translate(0,69)">50000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -776.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -776.000000) translate(0,105)">U%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3479.000000 -181.000000) translate(0,15)">    10号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3479.000000 -181.000000) translate(0,33)">（37-40号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3479.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3638.000000 -181.000000) translate(0,15)">    9号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3638.000000 -181.000000) translate(0,33)">（33-36号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3638.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -181.000000) translate(0,15)">    8号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -181.000000) translate(0,33)">（29-32号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -181.000000) translate(0,15)">    7号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -181.000000) translate(0,33)">（25-28号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -181.000000) translate(0,15)">    6号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -181.000000) translate(0,33)">（21-24号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433bb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -181.000000) translate(0,15)">    5号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433bb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -181.000000) translate(0,33)">（17-20号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433bb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4540.000000 -181.000000) translate(0,15)">    4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4540.000000 -181.000000) translate(0,33)">（13-16号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4540.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -181.000000) translate(0,15)">    3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -181.000000) translate(0,33)">（9-12号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.000000 -181.000000) translate(0,15)">    2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.000000 -181.000000) translate(0,33)">（5-8号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4990.000000 -181.000000) translate(0,15)">    1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4990.000000 -181.000000) translate(0,33)">（1-4号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4990.000000 -181.000000) translate(0,51)">      4х1MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.000000 -135.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.000000 -135.000000) translate(0,33)">      12MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329ca40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -921.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c0ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4270.000000 -983.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c1150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4270.000000 -867.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c1520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -907.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c1730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -828.000000) translate(0,12)">18110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3394eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -970.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33950f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4206.000000 -1036.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3395330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -578.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dcc280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5072.000000 -405.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dcc760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.000000 -351.000000) translate(0,12)">08117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dcc9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -404.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dccbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -350.000000) translate(0,12)">08217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4228ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4772.000000 -405.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42290e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -351.000000) translate(0,12)">08317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42292f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4623.000000 -404.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4229610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -350.000000) translate(0,12)">08417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -404.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -350.000000) translate(0,12)">08517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4323.000000 -404.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -350.000000) translate(0,12)">08617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4173.000000 -404.000000) translate(0,12)">087</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -350.000000) translate(0,12)">08717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -404.000000) translate(0,12)">088</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -350.000000) translate(0,12)">08817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3873.000000 -404.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35863c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -350.000000) translate(0,12)">09117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f89190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3722.000000 -404.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f893d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3727.000000 -350.000000) translate(0,12)">09217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f89610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 -405.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f89850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -351.000000) translate(0,12)">09317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -569.000000) translate(0,12)">089</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.000000 -619.000000) translate(0,12)">08917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3314.000000 -523.000000) translate(0,12)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5e770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -702.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3352c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -256.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f60b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -194.000000) translate(0,12)">4726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f60b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -194.000000) translate(0,27)">3205614</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3314.000000 -182.000000) translate(0,15)">   11号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3314.000000 -182.000000) translate(0,33)">（立竜光伏电站）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3314.000000 -182.000000) translate(0,51)">临时方案1~2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3314.000000 -182.000000) translate(0,69)">升压变2х3.15MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41a7420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3398.000000 -349.000000) translate(0,12)">09417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40960b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3398.000000 -402.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d07780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -180.000000) translate(0,12)">09167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_404c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3423.000000 -1032.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4183,-708 4178,-718 4188,-718 4183,-708 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4183,-696 4178,-686 4188,-686 4183,-696 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3898,-191 " stroke="rgb(255,0,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3866,-226 3896,-226 3896,-189 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35effe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3951.000000 -639.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DL.CX_DL_T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="15031"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -676.018519)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -676.018519)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="10799" ObjectName="TF-CX_DL.CX_DL_T"/>
    <cge:TPSR_Ref TObjectID="10799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3776.000000 -711.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3776.000000 -711.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4741.000000 -643.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4741.000000 -643.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78587" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3255.538462 -1016.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78587" ObjectName="CX_DL:CX_DL_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79714" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3253.538462 -971.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79714" ObjectName="CX_DL:CX_DL_sumQ"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3396,-1046 3393,-1049 3393,-995 3396,-998 3396,-1046" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3396,-1046 3393,-1049 3542,-1049 3539,-1046 3396,-1046" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3396,-998 3393,-995 3542,-995 3539,-998 3396,-998" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3539,-1046 3542,-1049 3542,-995 3539,-998 3539,-1046" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3396" y="-1046"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3396" y="-1046"/>
    </a>
   <metadata/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f2740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 890.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f2a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 875.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f2c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 860.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f2fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 588.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41ac700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 573.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41ac8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 558.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41acbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3281.000000 96.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a818a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3270.000000 81.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a81ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3295.000000 66.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d74c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 986.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c9180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 955.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c9380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 971.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c95b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 912.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c9780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 941.666667) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c9980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 927.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f1980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 540.666667) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363fd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3483.000000 588.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b4de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3483.000000 556.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_410dbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3483.000000 572.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f60c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 511.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b56c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3475.000000 525.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DL.CX_DL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3336,-482 5118,-482 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10666" ObjectName="BS-CX_DL.CX_DL_9IM"/>
    <cge:TPSR_Ref TObjectID="10666"/></metadata>
   <polyline fill="none" opacity="0" points="3336,-482 5118,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DL.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-864 4268,-864 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48131" ObjectName="BS-CX_DL.XM"/>
    <cge:TPSR_Ref TObjectID="48131"/></metadata>
   <polyline fill="none" opacity="0" points="4257,-864 4268,-864 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-57169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -987.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-57170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -987.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-57171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -987.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -987.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-57172" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -987.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57172" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -987.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -889.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -889.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -889.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10644"/>
     <cge:Term_Ref ObjectID="14816"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -588.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10641"/>
     <cge:Term_Ref ObjectID="14810"/>
    <cge:TPSR_Ref TObjectID="10641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -588.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10641"/>
     <cge:Term_Ref ObjectID="14810"/>
    <cge:TPSR_Ref TObjectID="10641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -588.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10641"/>
     <cge:Term_Ref ObjectID="14810"/>
    <cge:TPSR_Ref TObjectID="10641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3542.000000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10635"/>
     <cge:Term_Ref ObjectID="14798"/>
    <cge:TPSR_Ref TObjectID="10635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3542.000000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10635"/>
     <cge:Term_Ref ObjectID="14798"/>
    <cge:TPSR_Ref TObjectID="10635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3542.000000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10635"/>
     <cge:Term_Ref ObjectID="14798"/>
    <cge:TPSR_Ref TObjectID="10635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.600000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10633"/>
     <cge:Term_Ref ObjectID="14794"/>
    <cge:TPSR_Ref TObjectID="10633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.600000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10633"/>
     <cge:Term_Ref ObjectID="14794"/>
    <cge:TPSR_Ref TObjectID="10633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.600000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10633"/>
     <cge:Term_Ref ObjectID="14794"/>
    <cge:TPSR_Ref TObjectID="10633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.200000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10637"/>
     <cge:Term_Ref ObjectID="14802"/>
    <cge:TPSR_Ref TObjectID="10637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.200000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10637"/>
     <cge:Term_Ref ObjectID="14802"/>
    <cge:TPSR_Ref TObjectID="10637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.200000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10637"/>
     <cge:Term_Ref ObjectID="14802"/>
    <cge:TPSR_Ref TObjectID="10637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.800000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10631"/>
     <cge:Term_Ref ObjectID="14790"/>
    <cge:TPSR_Ref TObjectID="10631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.800000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10631"/>
     <cge:Term_Ref ObjectID="14790"/>
    <cge:TPSR_Ref TObjectID="10631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.800000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10631"/>
     <cge:Term_Ref ObjectID="14790"/>
    <cge:TPSR_Ref TObjectID="10631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.400000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10629"/>
     <cge:Term_Ref ObjectID="14786"/>
    <cge:TPSR_Ref TObjectID="10629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.400000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10629"/>
     <cge:Term_Ref ObjectID="14786"/>
    <cge:TPSR_Ref TObjectID="10629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.400000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10629"/>
     <cge:Term_Ref ObjectID="14786"/>
    <cge:TPSR_Ref TObjectID="10629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10627"/>
     <cge:Term_Ref ObjectID="14782"/>
    <cge:TPSR_Ref TObjectID="10627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10627"/>
     <cge:Term_Ref ObjectID="14782"/>
    <cge:TPSR_Ref TObjectID="10627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10627"/>
     <cge:Term_Ref ObjectID="14782"/>
    <cge:TPSR_Ref TObjectID="10627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.600000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10625"/>
     <cge:Term_Ref ObjectID="14778"/>
    <cge:TPSR_Ref TObjectID="10625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57131" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.600000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10625"/>
     <cge:Term_Ref ObjectID="14778"/>
    <cge:TPSR_Ref TObjectID="10625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.600000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10625"/>
     <cge:Term_Ref ObjectID="14778"/>
    <cge:TPSR_Ref TObjectID="10625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.200000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10623"/>
     <cge:Term_Ref ObjectID="14774"/>
    <cge:TPSR_Ref TObjectID="10623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.200000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10623"/>
     <cge:Term_Ref ObjectID="14774"/>
    <cge:TPSR_Ref TObjectID="10623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57125" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.200000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10623"/>
     <cge:Term_Ref ObjectID="14774"/>
    <cge:TPSR_Ref TObjectID="10623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4746.800000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10621"/>
     <cge:Term_Ref ObjectID="14770"/>
    <cge:TPSR_Ref TObjectID="10621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4746.800000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10621"/>
     <cge:Term_Ref ObjectID="14770"/>
    <cge:TPSR_Ref TObjectID="10621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4746.800000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10621"/>
     <cge:Term_Ref ObjectID="14770"/>
    <cge:TPSR_Ref TObjectID="10621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.400000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10619"/>
     <cge:Term_Ref ObjectID="14766"/>
    <cge:TPSR_Ref TObjectID="10619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.400000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10619"/>
     <cge:Term_Ref ObjectID="14766"/>
    <cge:TPSR_Ref TObjectID="10619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.400000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10619"/>
     <cge:Term_Ref ObjectID="14766"/>
    <cge:TPSR_Ref TObjectID="10619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -97.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10617"/>
     <cge:Term_Ref ObjectID="14762"/>
    <cge:TPSR_Ref TObjectID="10617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -97.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10617"/>
     <cge:Term_Ref ObjectID="14762"/>
    <cge:TPSR_Ref TObjectID="10617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -97.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10617"/>
     <cge:Term_Ref ObjectID="14762"/>
    <cge:TPSR_Ref TObjectID="10617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-57180" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -585.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10666"/>
     <cge:Term_Ref ObjectID="14852"/>
    <cge:TPSR_Ref TObjectID="10666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-57181" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -585.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10666"/>
     <cge:Term_Ref ObjectID="14852"/>
    <cge:TPSR_Ref TObjectID="10666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-57182" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -585.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10666"/>
     <cge:Term_Ref ObjectID="14852"/>
    <cge:TPSR_Ref TObjectID="10666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57186" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -585.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10666"/>
     <cge:Term_Ref ObjectID="14852"/>
    <cge:TPSR_Ref TObjectID="10666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-57183" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -585.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10666"/>
     <cge:Term_Ref ObjectID="14852"/>
    <cge:TPSR_Ref TObjectID="10666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57187" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -585.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10666"/>
     <cge:Term_Ref ObjectID="14852"/>
    <cge:TPSR_Ref TObjectID="10666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-255525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -96.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42219"/>
     <cge:Term_Ref ObjectID="17731"/>
    <cge:TPSR_Ref TObjectID="42219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-255526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -96.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42219"/>
     <cge:Term_Ref ObjectID="17731"/>
    <cge:TPSR_Ref TObjectID="42219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-255524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3370.000000 -96.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42219"/>
     <cge:Term_Ref ObjectID="17731"/>
    <cge:TPSR_Ref TObjectID="42219"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-57068">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -892.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10644" ObjectName="SW-CX_DL.CX_DL_181BK"/>
     <cge:Meas_Ref ObjectId="57068"/>
    <cge:TPSR_Ref TObjectID="10644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57066">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -549.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10641" ObjectName="SW-CX_DL.CX_DL_001BK"/>
     <cge:Meas_Ref ObjectId="57066"/>
    <cge:TPSR_Ref TObjectID="10641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3550.000000 -344.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10635" ObjectName="SW-CX_DL.CX_DL_093BK"/>
     <cge:Meas_Ref ObjectId="57060"/>
    <cge:TPSR_Ref TObjectID="10635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57058">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3700.000000 -343.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10633" ObjectName="SW-CX_DL.CX_DL_092BK"/>
     <cge:Meas_Ref ObjectId="57058"/>
    <cge:TPSR_Ref TObjectID="10633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.000000 -343.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10631" ObjectName="SW-CX_DL.CX_DL_088BK"/>
     <cge:Meas_Ref ObjectId="57056"/>
    <cge:TPSR_Ref TObjectID="10631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57054">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -343.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10629" ObjectName="SW-CX_DL.CX_DL_087BK"/>
     <cge:Meas_Ref ObjectId="57054"/>
    <cge:TPSR_Ref TObjectID="10629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -343.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10637" ObjectName="SW-CX_DL.CX_DL_091BK"/>
     <cge:Meas_Ref ObjectId="57062"/>
    <cge:TPSR_Ref TObjectID="10637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57050">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.000000 -341.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10625" ObjectName="SW-CX_DL.CX_DL_085BK"/>
     <cge:Meas_Ref ObjectId="57050"/>
    <cge:TPSR_Ref TObjectID="10625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57048">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 -341.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10623" ObjectName="SW-CX_DL.CX_DL_084BK"/>
     <cge:Meas_Ref ObjectId="57048"/>
    <cge:TPSR_Ref TObjectID="10623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -342.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10627" ObjectName="SW-CX_DL.CX_DL_086BK"/>
     <cge:Meas_Ref ObjectId="57052"/>
    <cge:TPSR_Ref TObjectID="10627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57046">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -342.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10621" ObjectName="SW-CX_DL.CX_DL_083BK"/>
     <cge:Meas_Ref ObjectId="57046"/>
    <cge:TPSR_Ref TObjectID="10621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57044">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 -342.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10619" ObjectName="SW-CX_DL.CX_DL_082BK"/>
     <cge:Meas_Ref ObjectId="57044"/>
    <cge:TPSR_Ref TObjectID="10619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57042">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5050.000000 -342.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10617" ObjectName="SW-CX_DL.CX_DL_081BK"/>
     <cge:Meas_Ref ObjectId="57042"/>
    <cge:TPSR_Ref TObjectID="10617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -508.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10639" ObjectName="SW-CX_DL.CX_DL_089BK"/>
     <cge:Meas_Ref ObjectId="57064"/>
    <cge:TPSR_Ref TObjectID="10639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-255507">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3371.000000 -342.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42219" ObjectName="SW-CX_DL.CX_DL_094BK"/>
     <cge:Meas_Ref ObjectId="255507"/>
    <cge:TPSR_Ref TObjectID="42219"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
   <g href="AVC的鲁1.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3396" y="-1046"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3c9d4b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4761.000000 -730.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2623fe0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -671.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d4520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -576.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e03a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4005.000000 -646.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352acc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3957.000000 -610.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_469c7b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -1039.018519)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43f99e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1024.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d4b770">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3723.000000 -715.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ebf8f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 -646.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e5d1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3513.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_360de50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3555.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a3720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -252.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3676eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3db1330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34de760">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3856.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ec670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3963.000000 -250.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3425dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e94ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -255.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_367caa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354f140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231bff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 -247.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c9a6f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -249.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43a6420">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -247.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3db3840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.000000 -251.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3eef240">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 -246.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40cf270">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -250.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2670540">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -246.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ea4860">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -251.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_360f900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4905.000000 -246.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32601a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 -252.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a79eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5055.000000 -246.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2628250">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.000000 -654.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35337c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -727.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e0340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 -142.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c9bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -673.000000)" xlink:href="#lightningRod:shape171"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354c870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3334.000000 -252.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d09f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3377.000000 -244.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DL"/>
</svg>