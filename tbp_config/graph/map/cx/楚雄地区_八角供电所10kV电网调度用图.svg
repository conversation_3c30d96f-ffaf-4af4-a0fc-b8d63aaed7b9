<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="512" id="thSvg" viewBox="38 -2782119 6693 4602">
 
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="4612" width="6703" x="33" y="-2782124"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="0" fill="none" points="6409,-2777734 6409,-2777732 6410,-2777731 6410,-2777729 6411,-2777728 6412,-2777726 6413,-2777725 6414,-2777724 6416,-2777723 6417,-2777722 6419,-2777721 6420,-2777721 6422,-2777721 6424,-2777721 6426,-2777721 6427,-2777721 6429,-2777722 6430,-2777723 6432,-2777724 6433,-2777725 6434,-2777726 6435,-2777728 6436,-2777729 6436,-2777731 6437,-2777732 6437,-2777734 " stroke="rgb(255,255,255)" stroke-width="0.045"/>
   <polyline DF8003:Layer="0" fill="none" points="6438,-2777734 6438,-2777732 6439,-2777731 6439,-2777729 6440,-2777728 6441,-2777726 6442,-2777725 6443,-2777724 6445,-2777723 6446,-2777722 6448,-2777721 6449,-2777721 6451,-2777721 6453,-2777721 6455,-2777721 6456,-2777721 6458,-2777722 6459,-2777723 6461,-2777724 6462,-2777725 6463,-2777726 6464,-2777728 6465,-2777729 6465,-2777731 6466,-2777732 6466,-2777734 " stroke="rgb(255,255,255)" stroke-width="0.045"/>
   <polyline DF8003:Layer="0" fill="none" points="3553,-2781304 3555,-2781304 3557,-2781304 3558,-2781305 3560,-2781306 3562,-2781307 3563,-2781308 3564,-2781309 3565,-2781311 3566,-2781312 3567,-2781314 3567,-2781316 3568,-2781318 3568,-2781319 3567,-2781321 3567,-2781323 3566,-2781325 3565,-2781326 3564,-2781328 3563,-2781329 3562,-2781330 3560,-2781331 3558,-2781332 3557,-2781333 3555,-2781333 3553,-2781333 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="3553,-2781275 3555,-2781275 3557,-2781275 3558,-2781276 3560,-2781277 3562,-2781278 3563,-2781279 3564,-2781280 3565,-2781282 3566,-2781283 3567,-2781285 3567,-2781287 3568,-2781289 3568,-2781290 3567,-2781292 3567,-2781294 3566,-2781296 3565,-2781297 3564,-2781299 3563,-2781300 3562,-2781301 3560,-2781302 3558,-2781303 3557,-2781304 3555,-2781304 3553,-2781304 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="5023,-2780271 5023,-2780269 5023,-2780267 5024,-2780266 5025,-2780264 5026,-2780262 5027,-2780261 5028,-2780260 5030,-2780259 5031,-2780258 5033,-2780257 5035,-2780257 5037,-2780256 5038,-2780256 5040,-2780257 5042,-2780257 5044,-2780258 5045,-2780259 5047,-2780260 5048,-2780261 5049,-2780262 5050,-2780264 5051,-2780266 5052,-2780267 5052,-2780269 5052,-2780271 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="5052,-2780271 5052,-2780269 5052,-2780267 5053,-2780266 5054,-2780264 5055,-2780262 5056,-2780261 5057,-2780260 5059,-2780259 5060,-2780258 5062,-2780257 5064,-2780257 5066,-2780256 5067,-2780256 5069,-2780257 5071,-2780257 5073,-2780258 5074,-2780259 5076,-2780260 5077,-2780261 5078,-2780262 5079,-2780264 5080,-2780266 5081,-2780267 5081,-2780269 5081,-2780271 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="1407,-2781151 1407,-2781149 1408,-2781148 1408,-2781146 1409,-2781144 1410,-2781143 1411,-2781142 1413,-2781140 1414,-2781139 1416,-2781139 1417,-2781138 1419,-2781138 1421,-2781137 1422,-2781137 1424,-2781138 1426,-2781138 1427,-2781139 1429,-2781139 1430,-2781140 1432,-2781142 1433,-2781143 1434,-2781144 1435,-2781146 1435,-2781148 1436,-2781149 1436,-2781151 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1436,-2781151 1436,-2781149 1437,-2781148 1437,-2781146 1438,-2781144 1439,-2781143 1440,-2781142 1442,-2781140 1443,-2781139 1445,-2781139 1446,-2781138 1448,-2781138 1450,-2781137 1451,-2781137 1453,-2781138 1455,-2781138 1456,-2781139 1458,-2781139 1459,-2781140 1461,-2781142 1462,-2781143 1463,-2781144 1464,-2781146 1464,-2781148 1465,-2781149 1465,-2781151 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="987,-2779837 987,-2779835 987,-2779833 987,-2779832 988,-2779830 989,-2779828 990,-2779827 991,-2779825 992,-2779824 994,-2779823 996,-2779822 997,-2779822 999,-2779821 1001,-2779821 1003,-2779821 1005,-2779822 1006,-2779822 1008,-2779823 1010,-2779824 1011,-2779825 1012,-2779827 1013,-2779828 1014,-2779830 1015,-2779832 1015,-2779833 1015,-2779835 1015,-2779837 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1016,-2779837 1016,-2779835 1016,-2779833 1016,-2779832 1017,-2779830 1018,-2779828 1019,-2779827 1020,-2779825 1021,-2779824 1023,-2779823 1025,-2779822 1026,-2779822 1028,-2779821 1030,-2779821 1032,-2779821 1034,-2779822 1035,-2779822 1037,-2779823 1039,-2779824 1040,-2779825 1041,-2779827 1042,-2779828 1043,-2779830 1044,-2779832 1044,-2779833 1044,-2779835 1044,-2779837 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1553,-2779742 1553,-2779740 1553,-2779738 1554,-2779737 1555,-2779735 1556,-2779734 1557,-2779732 1558,-2779731 1559,-2779730 1561,-2779729 1563,-2779728 1564,-2779728 1566,-2779728 1568,-2779728 1570,-2779728 1571,-2779728 1573,-2779729 1575,-2779730 1576,-2779731 1577,-2779732 1578,-2779734 1579,-2779735 1580,-2779737 1581,-2779738 1581,-2779740 1581,-2779742 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1581,-2779742 1581,-2779740 1581,-2779738 1582,-2779737 1583,-2779735 1584,-2779733 1585,-2779732 1586,-2779731 1588,-2779730 1589,-2779729 1591,-2779728 1593,-2779728 1595,-2779727 1596,-2779727 1598,-2779728 1600,-2779728 1602,-2779729 1603,-2779730 1605,-2779731 1606,-2779732 1607,-2779733 1608,-2779735 1609,-2779737 1610,-2779738 1610,-2779740 1610,-2779742 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="1328,-2778864 1328,-2778862 1329,-2778861 1329,-2778859 1330,-2778857 1331,-2778856 1332,-2778855 1334,-2778853 1335,-2778852 1337,-2778852 1338,-2778851 1340,-2778851 1342,-2778850 1343,-2778850 1345,-2778851 1347,-2778851 1348,-2778852 1350,-2778852 1351,-2778853 1353,-2778855 1354,-2778856 1355,-2778857 1356,-2778859 1356,-2778861 1357,-2778862 1357,-2778864 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1357,-2778864 1357,-2778862 1358,-2778861 1358,-2778859 1359,-2778857 1360,-2778856 1361,-2778855 1363,-2778853 1364,-2778852 1366,-2778852 1367,-2778851 1369,-2778851 1371,-2778850 1372,-2778850 1374,-2778851 1376,-2778851 1377,-2778852 1379,-2778852 1380,-2778853 1382,-2778855 1383,-2778856 1384,-2778857 1385,-2778859 1385,-2778861 1386,-2778862 1386,-2778864 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="659,-2778908 661,-2778908 662,-2778909 664,-2778909 666,-2778910 667,-2778911 668,-2778912 670,-2778914 671,-2778915 671,-2778917 672,-2778918 672,-2778920 673,-2778922 673,-2778923 672,-2778925 672,-2778927 671,-2778928 671,-2778930 670,-2778931 668,-2778933 667,-2778934 666,-2778935 664,-2778936 662,-2778936 661,-2778937 659,-2778937 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="659,-2778937 661,-2778937 662,-2778938 664,-2778938 666,-2778939 667,-2778940 668,-2778941 670,-2778943 671,-2778944 671,-2778946 672,-2778947 672,-2778949 673,-2778951 673,-2778952 672,-2778954 672,-2778956 671,-2778957 671,-2778959 670,-2778960 668,-2778962 667,-2778963 666,-2778964 664,-2778965 662,-2778965 661,-2778966 659,-2778966 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1328,-2778314 1328,-2778312 1328,-2778310 1329,-2778309 1330,-2778307 1331,-2778305 1332,-2778304 1333,-2778303 1335,-2778302 1336,-2778301 1338,-2778300 1340,-2778300 1342,-2778299 1343,-2778299 1345,-2778300 1347,-2778300 1349,-2778301 1350,-2778302 1352,-2778303 1353,-2778304 1354,-2778305 1355,-2778307 1356,-2778309 1357,-2778310 1357,-2778312 1357,-2778314 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="1357,-2778314 1357,-2778312 1357,-2778310 1358,-2778309 1359,-2778307 1360,-2778305 1361,-2778304 1362,-2778303 1364,-2778302 1365,-2778301 1367,-2778300 1369,-2778300 1371,-2778299 1372,-2778299 1374,-2778300 1376,-2778300 1378,-2778301 1379,-2778302 1381,-2778303 1382,-2778304 1383,-2778305 1384,-2778307 1385,-2778309 1386,-2778310 1386,-2778312 1386,-2778314 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="1049,-2778314 1049,-2778312 1049,-2778310 1050,-2778309 1051,-2778307 1052,-2778305 1053,-2778304 1054,-2778303 1056,-2778302 1057,-2778301 1059,-2778300 1061,-2778300 1063,-2778299 1064,-2778299 1066,-2778300 1068,-2778300 1070,-2778301 1071,-2778302 1073,-2778303 1074,-2778304 1075,-2778305 1076,-2778307 1077,-2778309 1078,-2778310 1078,-2778312 1078,-2778314 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="1078,-2778314 1078,-2778312 1078,-2778310 1079,-2778309 1080,-2778307 1081,-2778305 1082,-2778304 1083,-2778303 1085,-2778302 1086,-2778301 1088,-2778300 1090,-2778300 1092,-2778299 1093,-2778299 1095,-2778300 1097,-2778300 1099,-2778301 1100,-2778302 1102,-2778303 1103,-2778304 1104,-2778305 1105,-2778307 1106,-2778309 1107,-2778310 1107,-2778312 1107,-2778314 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="976,-2778242 978,-2778242 980,-2778242 981,-2778243 983,-2778244 984,-2778245 986,-2778246 987,-2778247 988,-2778248 989,-2778250 990,-2778252 990,-2778253 990,-2778255 990,-2778257 990,-2778259 990,-2778260 989,-2778262 988,-2778264 987,-2778265 986,-2778266 984,-2778267 983,-2778268 981,-2778269 980,-2778270 978,-2778270 976,-2778270 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="976,-2778213 978,-2778213 980,-2778213 981,-2778213 983,-2778214 985,-2778215 986,-2778216 988,-2778217 989,-2778218 990,-2778220 991,-2778222 991,-2778223 992,-2778225 992,-2778227 992,-2778229 991,-2778231 991,-2778232 990,-2778234 989,-2778236 988,-2778237 986,-2778238 985,-2778239 983,-2778240 981,-2778241 980,-2778241 978,-2778241 976,-2778241 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1867,-2777943 1867,-2777941 1867,-2777939 1868,-2777938 1869,-2777936 1870,-2777934 1871,-2777933 1872,-2777932 1874,-2777931 1875,-2777930 1877,-2777929 1879,-2777929 1881,-2777928 1882,-2777928 1884,-2777929 1886,-2777929 1888,-2777930 1889,-2777931 1891,-2777932 1892,-2777933 1893,-2777934 1894,-2777936 1895,-2777938 1896,-2777939 1896,-2777941 1896,-2777943 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="1896,-2777943 1896,-2777941 1896,-2777939 1897,-2777938 1898,-2777936 1899,-2777934 1900,-2777933 1901,-2777932 1903,-2777931 1904,-2777930 1906,-2777929 1908,-2777929 1910,-2777928 1911,-2777928 1913,-2777929 1915,-2777929 1917,-2777930 1918,-2777931 1920,-2777932 1921,-2777933 1922,-2777934 1923,-2777936 1924,-2777938 1925,-2777939 1925,-2777941 1925,-2777943 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="2678,-2778122 2680,-2778122 2682,-2778122 2683,-2778122 2685,-2778123 2687,-2778124 2688,-2778125 2690,-2778126 2691,-2778127 2692,-2778129 2693,-2778131 2693,-2778132 2694,-2778134 2694,-2778136 2694,-2778138 2693,-2778140 2693,-2778141 2692,-2778143 2691,-2778145 2690,-2778146 2688,-2778147 2687,-2778148 2685,-2778149 2683,-2778150 2682,-2778150 2680,-2778150 2678,-2778150 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="2678,-2778093 2680,-2778093 2682,-2778093 2683,-2778093 2685,-2778094 2687,-2778095 2688,-2778096 2690,-2778097 2691,-2778098 2692,-2778100 2693,-2778102 2693,-2778103 2694,-2778105 2694,-2778107 2694,-2778109 2693,-2778111 2693,-2778112 2692,-2778114 2691,-2778116 2690,-2778117 2688,-2778118 2687,-2778119 2685,-2778120 2683,-2778121 2682,-2778121 2680,-2778121 2678,-2778121 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="2678,-2777822 2680,-2777822 2682,-2777822 2683,-2777823 2685,-2777824 2687,-2777825 2688,-2777826 2689,-2777827 2690,-2777829 2691,-2777830 2692,-2777832 2692,-2777834 2693,-2777836 2693,-2777837 2692,-2777839 2692,-2777841 2691,-2777843 2690,-2777844 2689,-2777846 2688,-2777847 2687,-2777848 2685,-2777849 2683,-2777850 2682,-2777851 2680,-2777851 2678,-2777851 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="2678,-2777794 2680,-2777794 2682,-2777794 2683,-2777795 2685,-2777796 2686,-2777797 2688,-2777798 2689,-2777799 2690,-2777800 2691,-2777802 2692,-2777804 2692,-2777805 2692,-2777807 2692,-2777809 2692,-2777811 2692,-2777812 2691,-2777814 2690,-2777816 2689,-2777817 2688,-2777818 2686,-2777819 2685,-2777820 2683,-2777821 2682,-2777822 2680,-2777822 2678,-2777822 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="3389,-2777816 3391,-2777816 3393,-2777816 3394,-2777817 3396,-2777818 3398,-2777819 3399,-2777820 3400,-2777821 3401,-2777823 3402,-2777824 3403,-2777826 3403,-2777828 3404,-2777830 3404,-2777831 3403,-2777833 3403,-2777835 3402,-2777837 3401,-2777838 3400,-2777840 3399,-2777841 3398,-2777842 3396,-2777843 3394,-2777844 3393,-2777845 3391,-2777845 3389,-2777845 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="3389,-2777787 3391,-2777787 3393,-2777787 3394,-2777788 3396,-2777789 3398,-2777790 3399,-2777791 3400,-2777792 3401,-2777794 3402,-2777795 3403,-2777797 3403,-2777799 3404,-2777801 3404,-2777802 3403,-2777804 3403,-2777806 3402,-2777808 3401,-2777809 3400,-2777811 3399,-2777812 3398,-2777813 3396,-2777814 3394,-2777815 3393,-2777816 3391,-2777816 3389,-2777816 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="4321,-2777792 4322,-2777791 4323,-2777789 4325,-2777788 4326,-2777787 4328,-2777786 4330,-2777786 4331,-2777786 4333,-2777785 4335,-2777786 4337,-2777786 4339,-2777786 4340,-2777787 4342,-2777788 4343,-2777789 4345,-2777790 4346,-2777792 4346,-2777794 4347,-2777795 4348,-2777797 4348,-2777799 4348,-2777801 4348,-2777802 4347,-2777804 4347,-2777806 4346,-2777808 4345,-2777809 " stroke="rgb(255,255,255)" stroke-width="0.055"/>
   <polyline DF8003:Layer="0" fill="none" points="4298,-2777776 4299,-2777775 4300,-2777773 4302,-2777772 4303,-2777771 4305,-2777770 4306,-2777770 4308,-2777769 4310,-2777769 4311,-2777769 4313,-2777770 4315,-2777770 4316,-2777771 4318,-2777772 4319,-2777773 4321,-2777774 4322,-2777775 4323,-2777777 4323,-2777779 4324,-2777780 4324,-2777782 4324,-2777784 4324,-2777786 4323,-2777787 4323,-2777789 4322,-2777791 4321,-2777792 " stroke="rgb(255,255,255)" stroke-width="0.0525"/>
   <polyline DF8003:Layer="0" fill="none" points="4204,-2778662 4204,-2778660 4204,-2778658 4204,-2778657 4205,-2778655 4206,-2778653 4207,-2778652 4208,-2778650 4209,-2778649 4211,-2778648 4213,-2778647 4214,-2778647 4216,-2778646 4218,-2778646 4220,-2778646 4222,-2778647 4223,-2778647 4225,-2778648 4227,-2778649 4228,-2778650 4229,-2778652 4230,-2778653 4231,-2778655 4232,-2778657 4232,-2778658 4232,-2778660 4232,-2778662 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="4233,-2778662 4233,-2778660 4233,-2778658 4233,-2778657 4234,-2778655 4235,-2778653 4236,-2778652 4237,-2778650 4238,-2778649 4240,-2778648 4242,-2778647 4243,-2778647 4245,-2778646 4247,-2778646 4249,-2778646 4251,-2778647 4252,-2778647 4254,-2778648 4256,-2778649 4257,-2778650 4258,-2778652 4259,-2778653 4260,-2778655 4261,-2778657 4261,-2778658 4261,-2778660 4261,-2778662 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="4204,-2779272 4204,-2779270 4204,-2779269 4205,-2779267 4205,-2779265 4206,-2779264 4207,-2779262 4208,-2779261 4210,-2779260 4211,-2779259 4213,-2779258 4214,-2779258 4216,-2779257 4218,-2779257 4220,-2779257 4222,-2779258 4223,-2779258 4225,-2779259 4226,-2779260 4228,-2779261 4229,-2779262 4230,-2779264 4231,-2779265 4231,-2779267 4232,-2779269 4232,-2779270 4232,-2779272 " stroke="rgb(255,255,255)" stroke-width="0.045"/>
   <polyline DF8003:Layer="0" fill="none" points="4233,-2779272 4233,-2779270 4233,-2779269 4234,-2779267 4234,-2779265 4235,-2779264 4236,-2779262 4237,-2779261 4239,-2779260 4240,-2779259 4242,-2779258 4243,-2779258 4245,-2779257 4247,-2779257 4249,-2779257 4251,-2779258 4252,-2779258 4254,-2779259 4255,-2779260 4257,-2779261 4258,-2779262 4259,-2779264 4260,-2779265 4260,-2779267 4261,-2779269 4261,-2779270 4261,-2779272 " stroke="rgb(255,255,255)" stroke-width="0.045"/>
   <polyline DF8003:Layer="0" fill="none" points="3091,-2779798 3091,-2779796 3091,-2779794 3092,-2779793 3093,-2779791 3094,-2779789 3095,-2779788 3096,-2779787 3098,-2779786 3099,-2779785 3101,-2779784 3103,-2779784 3105,-2779783 3106,-2779783 3108,-2779784 3110,-2779784 3112,-2779785 3113,-2779786 3115,-2779787 3116,-2779788 3117,-2779789 3118,-2779791 3119,-2779793 3120,-2779794 3120,-2779796 3120,-2779798 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="3120,-2779798 3120,-2779796 3120,-2779794 3121,-2779793 3122,-2779791 3123,-2779789 3124,-2779788 3125,-2779787 3127,-2779786 3128,-2779785 3130,-2779784 3132,-2779784 3134,-2779783 3135,-2779783 3137,-2779784 3139,-2779784 3141,-2779785 3142,-2779786 3144,-2779787 3145,-2779788 3146,-2779789 3147,-2779791 3148,-2779793 3149,-2779794 3149,-2779796 3149,-2779798 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="2953,-2778833 2955,-2778833 2956,-2778834 2958,-2778834 2960,-2778835 2961,-2778836 2962,-2778837 2964,-2778839 2965,-2778840 2965,-2778842 2966,-2778843 2966,-2778845 2967,-2778847 2967,-2778848 2966,-2778850 2966,-2778852 2965,-2778853 2965,-2778855 2964,-2778856 2962,-2778858 2961,-2778859 2960,-2778860 2958,-2778861 2956,-2778861 2955,-2778862 2953,-2778862 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="2953,-2778804 2955,-2778804 2956,-2778805 2958,-2778805 2960,-2778806 2961,-2778807 2962,-2778808 2964,-2778810 2965,-2778811 2965,-2778813 2966,-2778814 2966,-2778816 2967,-2778818 2967,-2778819 2966,-2778821 2966,-2778823 2965,-2778824 2965,-2778826 2964,-2778827 2962,-2778829 2961,-2778830 2960,-2778831 2958,-2778832 2956,-2778832 2955,-2778833 2953,-2778833 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="4306,-2778314 4306,-2778312 4306,-2778310 4307,-2778309 4308,-2778307 4309,-2778305 4310,-2778304 4311,-2778303 4313,-2778302 4314,-2778301 4316,-2778300 4318,-2778300 4320,-2778299 4321,-2778299 4323,-2778300 4325,-2778300 4327,-2778301 4328,-2778302 4330,-2778303 4331,-2778304 4332,-2778305 4333,-2778307 4334,-2778309 4335,-2778310 4335,-2778312 4335,-2778314 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="5150,-2779447 5150,-2779445 5151,-2779444 5151,-2779442 5152,-2779440 5153,-2779439 5154,-2779438 5156,-2779436 5157,-2779435 5159,-2779435 5160,-2779434 5162,-2779434 5164,-2779433 5165,-2779433 5167,-2779434 5169,-2779434 5170,-2779435 5172,-2779435 5173,-2779436 5175,-2779438 5176,-2779439 5177,-2779440 5178,-2779442 5178,-2779444 5179,-2779445 5179,-2779447 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="5179,-2779447 5179,-2779445 5180,-2779444 5180,-2779442 5181,-2779440 5182,-2779439 5183,-2779438 5185,-2779436 5186,-2779435 5188,-2779435 5189,-2779434 5191,-2779434 5193,-2779433 5194,-2779433 5196,-2779434 5198,-2779434 5199,-2779435 5201,-2779435 5202,-2779436 5204,-2779438 5205,-2779439 5206,-2779440 5207,-2779442 5207,-2779444 5208,-2779445 5208,-2779447 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="1009,-2778792 1011,-2778792 1013,-2778792 1014,-2778793 1016,-2778794 1018,-2778795 1019,-2778796 1020,-2778797 1021,-2778799 1022,-2778800 1023,-2778802 1023,-2778804 1024,-2778806 1024,-2778807 1023,-2778809 1023,-2778811 1022,-2778813 1021,-2778814 1020,-2778816 1019,-2778817 1018,-2778818 1016,-2778819 1014,-2778820 1013,-2778821 1011,-2778821 1009,-2778821 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="1009,-2778821 1011,-2778821 1013,-2778821 1014,-2778822 1016,-2778823 1018,-2778824 1019,-2778825 1020,-2778826 1021,-2778828 1022,-2778829 1023,-2778831 1023,-2778833 1024,-2778835 1024,-2778836 1023,-2778838 1023,-2778840 1022,-2778842 1021,-2778843 1020,-2778845 1019,-2778846 1018,-2778847 1016,-2778848 1014,-2778849 1013,-2778850 1011,-2778850 1009,-2778850 " stroke="rgb(255,255,255)" stroke-width="0.0475"/>
   <polyline DF8003:Layer="0" fill="none" points="285,-2778349 287,-2778349 288,-2778350 290,-2778350 292,-2778351 293,-2778352 294,-2778353 296,-2778355 297,-2778356 297,-2778358 298,-2778359 298,-2778361 299,-2778363 299,-2778364 298,-2778366 298,-2778368 297,-2778369 297,-2778371 296,-2778372 294,-2778374 293,-2778375 292,-2778376 290,-2778377 288,-2778377 287,-2778378 285,-2778378 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="285,-2778320 287,-2778320 288,-2778321 290,-2778321 292,-2778322 293,-2778323 294,-2778324 296,-2778326 297,-2778327 297,-2778329 298,-2778330 298,-2778332 299,-2778334 299,-2778335 298,-2778337 298,-2778339 297,-2778340 297,-2778342 296,-2778343 294,-2778345 293,-2778346 292,-2778347 290,-2778348 288,-2778348 287,-2778349 285,-2778349 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="767,-2778350 769,-2778350 770,-2778351 772,-2778351 774,-2778352 775,-2778353 776,-2778354 778,-2778356 779,-2778357 779,-2778359 780,-2778360 780,-2778362 781,-2778364 781,-2778365 780,-2778367 780,-2778369 779,-2778370 779,-2778372 778,-2778373 776,-2778375 775,-2778376 774,-2778377 772,-2778378 770,-2778378 769,-2778379 767,-2778379 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="767,-2778321 769,-2778321 770,-2778322 772,-2778322 774,-2778323 775,-2778324 776,-2778325 778,-2778327 779,-2778328 779,-2778330 780,-2778331 780,-2778333 781,-2778335 781,-2778336 780,-2778338 780,-2778340 779,-2778341 779,-2778343 778,-2778344 776,-2778346 775,-2778347 774,-2778348 772,-2778349 770,-2778349 769,-2778350 767,-2778350 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="976,-2777980 978,-2777980 979,-2777981 981,-2777981 983,-2777982 984,-2777983 985,-2777984 987,-2777986 988,-2777987 988,-2777989 989,-2777990 989,-2777992 990,-2777994 990,-2777995 989,-2777997 989,-2777999 988,-2778000 988,-2778002 987,-2778003 985,-2778005 984,-2778006 983,-2778007 981,-2778008 979,-2778008 978,-2778009 976,-2778009 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="976,-2777951 978,-2777951 979,-2777952 981,-2777952 983,-2777953 984,-2777954 985,-2777955 987,-2777957 988,-2777958 988,-2777960 989,-2777961 989,-2777963 990,-2777965 990,-2777966 989,-2777968 989,-2777970 988,-2777971 988,-2777973 987,-2777974 985,-2777976 984,-2777977 983,-2777978 981,-2777979 979,-2777979 978,-2777980 976,-2777980 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="3192,-2781529 3194,-2781529 3195,-2781530 3197,-2781530 3199,-2781531 3200,-2781532 3201,-2781533 3203,-2781535 3204,-2781536 3204,-2781538 3205,-2781539 3205,-2781541 3206,-2781543 3206,-2781544 3205,-2781546 3205,-2781548 3204,-2781549 3204,-2781551 3203,-2781552 3201,-2781554 3200,-2781555 3199,-2781556 3197,-2781557 3195,-2781557 3194,-2781558 3192,-2781558 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="3192,-2781500 3194,-2781500 3195,-2781501 3197,-2781501 3199,-2781502 3200,-2781503 3201,-2781504 3203,-2781506 3204,-2781507 3204,-2781509 3205,-2781510 3205,-2781512 3206,-2781514 3206,-2781515 3205,-2781517 3205,-2781519 3204,-2781520 3204,-2781522 3203,-2781523 3201,-2781525 3200,-2781526 3199,-2781527 3197,-2781528 3195,-2781528 3194,-2781529 3192,-2781529 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
   <polyline DF8003:Layer="0" fill="none" points="4335,-2778308 4335,-2778306 4336,-2778305 4336,-2778303 4337,-2778301 4338,-2778300 4339,-2778299 4341,-2778297 4342,-2778296 4344,-2778296 4345,-2778295 4347,-2778295 4349,-2778294 4350,-2778294 4352,-2778295 4354,-2778295 4355,-2778296 4357,-2778296 4358,-2778297 4360,-2778299 4361,-2778300 4362,-2778301 4363,-2778303 4363,-2778305 4364,-2778306 4364,-2778308 " stroke="rgb(255,255,255)" stroke-width="0.04625"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="0" fill="none" points="5339,-2777529 5339,-2777649 6731,-2777649 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="5339,-2777585 6731,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="5787,-2777529 5787,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="5435,-2777529 5435,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="5563,-2777529 5563,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="5659,-2777529 5659,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="6515,-2777529 6515,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="6011,-2777529 6011,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="5883,-2777529 5883,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="6354,-2777529 6354,-2777649 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <polyline DF8003:Layer="0" fill="none" points="6154,-2777529 6154,-2777585 " stroke="rgb(255,255,255)" stroke-width="0.5"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="40" qtmmishow="hidden" width="320" x="570" y="-2781823"/>
    </a>
   <metadata/><rect fill="white" height="40" opacity="0" stroke="white" transform="" width="320" x="570" y="-2781823"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="533" y="-2781842"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="533" y="-2781842"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="40" qtmmishow="hidden" width="320" x="570" y="-2781823"/></g>
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="533" y="-2781842"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1584" x2="1616" y1="-2780184" y2="-2780152"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1525" y1="-2778174" y2="-2778174"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1525" x2="1493" y1="-2778126" y2="-2778126"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1493" y1="-2778126" y2="-2778174"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1525" x2="1525" y1="-2778174" y2="-2778126"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5146" x2="5146" y1="-2777760" y2="-2777856"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6050" x2="6018" y1="-2781457" y2="-2781457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6018" x2="6050" y1="-2781505" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6018" x2="6018" y1="-2781457" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6050" x2="6050" y1="-2781505" y2="-2781457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6018" x2="6050" y1="-2781547" y2="-2781515"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6034" x2="6034" y1="-2781626" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1525" y1="-2781547" y2="-2781515"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1509" x2="1509" y1="-2781626" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1525" x2="1493" y1="-2781457" y2="-2781457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1525" y1="-2781505" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1493" y1="-2781457" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1525" x2="1525" y1="-2781505" y2="-2781457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1838" x2="1870" y1="-2781547" y2="-2781515"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1854" x2="1854" y1="-2781626" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1870" x2="1838" y1="-2781457" y2="-2781457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1838" x2="1870" y1="-2781505" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1838" x2="1838" y1="-2781457" y2="-2781505"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1870" x2="1870" y1="-2781505" y2="-2781457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1854" x2="1854" y1="-2781457" y2="-2780780"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1509" x2="1509" y1="-2781457" y2="-2778354"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1838" x2="1870" y1="-2781396" y2="-2781364"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1838" x2="1870" y1="-2781440" y2="-2781408"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1525" y1="-2781385" y2="-2781353"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1492" x2="1524" y1="-2781440" y2="-2781408"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6034" x2="6034" y1="-2781457" y2="-2780780"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5109" x2="5109" y1="-2780616" y2="-2780664"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5141" x2="5109" y1="-2780616" y2="-2780616"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5141" x2="5141" y1="-2780664" y2="-2780616"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="5125" y1="-2778812" y2="-2780616"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="5125" y1="-2780664" y2="-2780780"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1469" x2="1549" y1="-2778354" y2="-2778354"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1549" x2="1549" y1="-2778354" y2="-2778274"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1549" x2="1469" y1="-2778274" y2="-2778274"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1469" x2="1469" y1="-2778274" y2="-2778354"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6018" x2="6050" y1="-2781349" y2="-2781317"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5113" x2="5113" y1="-2780520" y2="-2780468"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5137" x2="5137" y1="-2780520" y2="-2780468"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5130" x2="5130" y1="-2777904" y2="-2777856"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5130" x2="5162" y1="-2777904" y2="-2777904"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5162" x2="5130" y1="-2777856" y2="-2777856"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5162" x2="5162" y1="-2777904" y2="-2777856"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5146" x2="5146" y1="-2777904" y2="-2777904"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5146" x2="5146" y1="-2777904" y2="-2778094"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="6018" x2="6050" y1="-2781418" y2="-2781386"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6454" x2="6395" y1="-2777771" y2="-2777771"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6454" x2="6395" y1="-2777801" y2="-2777801"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6425" x2="6454" y1="-2777801" y2="-2777801"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1854" x2="6034" y1="-2780780" y2="-2780780"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2428" x2="2428" y1="-2780780" y2="-2781407"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2428" x2="5582" y1="-2781562" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2814" x2="2814" y1="-2781451" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3193" x2="3193" y1="-2781562" y2="-2781344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3193" x2="3959" y1="-2781344" y2="-2781344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4357" x2="4357" y1="-2781451" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4650" x2="4650" y1="-2781451" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4922" x2="4922" y1="-2781451" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5143" x2="5143" y1="-2781451" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3339" x2="3339" y1="-2781231" y2="-2781344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3959" x2="3959" y1="-2781232" y2="-2781345"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2412" x2="2444" y1="-2781392" y2="-2781360"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5141" x2="5109" y1="-2780664" y2="-2780664"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4738" x2="4770" y1="-2781579" y2="-2781547"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5108" x2="5140" y1="-2780764" y2="-2780732"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="6230" y1="-2779448" y2="-2779448"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5829" x2="5829" y1="-2779448" y2="-2778872"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6441" x2="6230" y1="-2779448" y2="-2779448"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6230" x2="6230" y1="-2779558" y2="-2779448"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5955" x2="5955" y1="-2779558" y2="-2779448"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1509" x2="2636" y1="-2780168" y2="-2780168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1494" x2="1526" y1="-2781036" y2="-2781004"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1509" x2="2040" y1="-2780563" y2="-2780563"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1797" x2="1797" y1="-2780452" y2="-2780563"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2033" x2="2033" y1="-2780281" y2="-2780168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2033" x2="2033" y1="-2780057" y2="-2780168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2374" x2="2374" y1="-2780057" y2="-2780168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2636" x2="2636" y1="-2780057" y2="-2780168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1088" x2="1088" y1="-2780475" y2="-2779494"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="862" x2="1509" y1="-2780475" y2="-2780475"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1088" x2="521" y1="-2780137" y2="-2780137"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="521" x2="521" y1="-2780026" y2="-2780137"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="805" x2="805" y1="-2780026" y2="-2780137"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1088" x2="521" y1="-2779837" y2="-2779837"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="521" x2="521" y1="-2779725" y2="-2779837"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1350" x2="1382" y1="-2780489" y2="-2780457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="149" x2="1509" y1="-2778864" y2="-2778864"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1010" x2="1010" y1="-2778753" y2="-2778864"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="841" x2="841" y1="-2778976" y2="-2778864"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1203" x2="1203" y1="-2778976" y2="-2778864"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="659" x2="659" y1="-2778864" y2="-2779266"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="56" x2="1469" y1="-2778314" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="553" x2="553" y1="-2778202" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1190" x2="1190" y1="-2778202" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="767" x2="767" y1="-2778426" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="976" x2="976" y1="-2778314" y2="-2777794"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1509" x2="1509" y1="-2778274" y2="-2778174"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1509" x2="5352" y1="-2777760" y2="-2777760"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4276" y1="-2777760" y2="-2777834"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1525" y1="-2778526" y2="-2778494"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1509" x2="1509" y1="-2778126" y2="-2777760"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1509" x2="2040" y1="-2777943" y2="-2777943"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1797" x2="1797" y1="-2778054" y2="-2777943"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2678" x2="2678" y1="-2777760" y2="-2778291"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2567" x2="2678" y1="-2778048" y2="-2778048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5034" x2="4276" y1="-2778314" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4845" x2="4845" y1="-2778425" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4655" x2="4655" y1="-2778203" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5034" x2="5034" y1="-2778425" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="2953" y1="-2780475" y2="-2780475"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2953" x2="2953" y1="-2780475" y2="-2778594"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3077" x2="3077" y1="-2779916" y2="-2779525"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="5004" x2="5036" y1="-2777776" y2="-2777744"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4909" y1="-2777746" y2="-2777746"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4909" y1="-2777776" y2="-2777776"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4938" x2="4968" y1="-2777776" y2="-2777776"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1070" x2="1102" y1="-2780104" y2="-2780072"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1442" x2="1474" y1="-2778878" y2="-2778846"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1525" y1="-2778111" y2="-2778079"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1577" x2="1609" y1="-2777959" y2="-2777927"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3726" x2="3758" y1="-2777834" y2="-2777802"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1493" x2="1525" y1="-2777812" y2="-2777780"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2412" x2="2444" y1="-2780847" y2="-2780815"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="904" x2="936" y1="-2777869" y2="-2777837"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="996" x2="1028" y1="-2777920" y2="-2777888"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5323" x2="5323" y1="-2781450" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5367" x2="5399" y1="-2781578" y2="-2781546"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5514" x2="5455" y1="-2781548" y2="-2781548"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5514" x2="5455" y1="-2781578" y2="-2781578"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="810" x2="810" y1="-2777742" y2="-2777853"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="608" x2="608" y1="-2777742" y2="-2777854"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="423" x2="423" y1="-2777743" y2="-2777853"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="322" x2="354" y1="-2777870" y2="-2777838"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2412" x2="2444" y1="-2781455" y2="-2781455"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2444" x2="2412" y1="-2781407" y2="-2781407"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2412" x2="2412" y1="-2781407" y2="-2781455"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2444" x2="2444" y1="-2781455" y2="-2781407"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2428" x2="2428" y1="-2781455" y2="-2781562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3553" x2="3553" y1="-2781106" y2="-2781344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="874" x2="815" y1="-2777841" y2="-2777841"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="875" x2="816" y1="-2777871" y2="-2777871"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5111" x2="5143" y1="-2779920" y2="-2779888"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="4551" x2="4583" y1="-2777774" y2="-2777742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4283" x2="4315" y1="-2779944" y2="-2779912"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4356" x2="4318" y1="-2779936" y2="-2779936"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4356" x2="4318" y1="-2779917" y2="-2779917"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="56" x2="56" y1="-2778314" y2="-2778241"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="40" x2="40" y1="-2778241" y2="-2778193"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="40" x2="72" y1="-2778241" y2="-2778241"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="72" x2="40" y1="-2778193" y2="-2778193"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="72" x2="72" y1="-2778241" y2="-2778193"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="40" x2="72" y1="-2778298" y2="-2778266"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="56" x2="56" y1="-2778193" y2="-2778093"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="56" x2="40" y1="-2778093" y2="-2778106"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="56" x2="70" y1="-2778093" y2="-2778105"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="546" x2="424" y1="-2779169" y2="-2779169"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="375" x2="339" y1="-2779169" y2="-2779169"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="605" x2="637" y1="-2779186" y2="-2779154"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="494" x2="459" y1="-2779182" y2="-2779182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="494" x2="459" y1="-2779157" y2="-2779157"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="488" x2="488" y1="-2778630" y2="-2778581"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="443" x2="454" y1="-2778675" y2="-2778663"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="477" x2="488" y1="-2778641" y2="-2778630"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="496" x2="480" y1="-2778680" y2="-2778680"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="487" x2="487" y1="-2778810" y2="-2778680"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="487" x2="487" y1="-2778834" y2="-2778864"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4259" x2="4259" y1="-2777834" y2="-2777882"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4291" x2="4259" y1="-2777834" y2="-2777834"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4291" x2="4291" y1="-2777882" y2="-2777834"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4291" x2="4259" y1="-2777882" y2="-2777882"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4258" x2="4290" y1="-2777928" y2="-2777896"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4276" y1="-2777882" y2="-2780475"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5107" x2="5139" y1="-2780607" y2="-2780575"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5814" x2="5846" y1="-2778976" y2="-2778944"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4229" x2="4261" y1="-2778982" y2="-2778950"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4226" x2="4258" y1="-2779093" y2="-2779061"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4229" x2="4261" y1="-2779197" y2="-2779165"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4218" x2="4250" y1="-2779652" y2="-2779620"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4142" x2="4142" y1="-2779483" y2="-2779638"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4127" x2="4159" y1="-2779624" y2="-2779592"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3963" x2="3963" y1="-2779561" y2="-2779638"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3883" x2="3915" y1="-2779653" y2="-2779621"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2979" x2="3011" y1="-2779932" y2="-2779900"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3086" x2="3118" y1="-2779672" y2="-2779640"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2966" x2="2998" y1="-2779295" y2="-2779263"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2967" x2="2999" y1="-2779138" y2="-2779106"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2891" x2="2923" y1="-2778950" y2="-2778918"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2891" x2="2923" y1="-2778776" y2="-2778744"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4289" x2="4321" y1="-2779815" y2="-2779783"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3077" x2="2953" y1="-2779916" y2="-2779916"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4100" x2="4132" y1="-2780364" y2="-2780332"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4100" x2="4132" y1="-2780269" y2="-2780237"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3639" x2="3671" y1="-2780364" y2="-2780332"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2275" x2="2307" y1="-2779296" y2="-2779264"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2291" x2="2291" y1="-2779370" y2="-2779249"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2307" x2="2275" y1="-2779201" y2="-2779201"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2275" x2="2307" y1="-2779249" y2="-2779249"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2275" x2="2275" y1="-2779201" y2="-2779249"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2307" x2="2307" y1="-2779249" y2="-2779201"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2291" x2="2291" y1="-2779201" y2="-2777760"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2273" x2="2305" y1="-2779054" y2="-2779022"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2275" x2="2307" y1="-2779184" y2="-2779152"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="390" x2="390" y1="-2778158" y2="-2778314"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="375" x2="407" y1="-2778299" y2="-2778267"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="149" x2="149" y1="-2778976" y2="-2778864"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="233" x2="265" y1="-2778434" y2="-2778402"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="137" x2="285" y1="-2778421" y2="-2778421"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="285" x2="285" y1="-2778533" y2="-2778314"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6347" x2="6499" y1="-2777734" y2="-2777734"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6458" x2="6498" y1="-2777842" y2="-2777842"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6410" x2="6458" y1="-2777858" y2="-2777858"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6458" x2="6410" y1="-2777826" y2="-2777826"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6410" x2="6410" y1="-2777826" y2="-2777858"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6458" x2="6458" y1="-2777858" y2="-2777826"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6352" x2="6410" y1="-2777842" y2="-2777842"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6409" x2="6441" y1="-2777885" y2="-2777917"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6352" x2="6498" y1="-2777901" y2="-2777901"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6345" x2="6492" y1="-2777785" y2="-2777785"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2428" x2="2574" y1="-2781273" y2="-2781273"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2428" x2="2574" y1="-2781004" y2="-2781004"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="4922" y1="-2780271" y2="-2780271"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="5269" y1="-2780009" y2="-2780009"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="5269" y1="-2779709" y2="-2779709"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="4922" y1="-2779219" y2="-2779219"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5125" x2="4922" y1="-2778999" y2="-2778999"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5829" x2="5727" y1="-2779064" y2="-2779064"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1509" x2="1305" y1="-2781151" y2="-2781151"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1524" x2="1524" y1="-2780963" y2="-2780933"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1524" x2="1524" y1="-2780933" y2="-2780992"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1494" x2="1494" y1="-2780933" y2="-2780992"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1088" x2="1233" y1="-2780008" y2="-2780008"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1088" x2="1233" y1="-2780273" y2="-2780273"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1088" x2="1233" y1="-2779703" y2="-2779703"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1509" x2="1655" y1="-2779742" y2="-2779742"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1514" x2="1659" y1="-2778701" y2="-2778701"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="659" x2="557" y1="-2779065" y2="-2779065"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="976" x2="865" y1="-2778155" y2="-2778155"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1509" x2="1305" y1="-2777842" y2="-2777842"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2982" x2="2982" y1="-2777760" y2="-2777963"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3389" x2="3389" y1="-2777760" y2="-2777963"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3742" x2="3742" y1="-2777760" y2="-2777963"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4003" x2="4003" y1="-2777760" y2="-2777963"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4814" x2="4814" y1="-2777760" y2="-2777965"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4416" y1="-2778529" y2="-2778529"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4132" y1="-2778662" y2="-2778662"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4132" y1="-2778862" y2="-2778862"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4031" y1="-2779079" y2="-2779079"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4132" y1="-2779272" y2="-2779272"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="3357" y1="-2780376" y2="-2780376"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2953" x2="2953" y1="-2778594" y2="-2778449"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3077" x2="3221" y1="-2779798" y2="-2779798"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="976" x2="865" y1="-2778029" y2="-2778029"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4442" y1="-2777760" y2="-2777877"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="975" x2="248" y1="-2777853" y2="-2777853"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="976" x2="1120" y1="-2777901" y2="-2777901"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2445" x2="2445" y1="-2781059" y2="-2781118"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2428" x2="2428" y1="-2781069" y2="-2781128"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2401" x2="2401" y1="-2781064" y2="-2781123"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4419" x2="4275" y1="-2779927" y2="-2779927"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="659" x2="594" y1="-2779169" y2="-2779169"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="546" x2="594" y1="-2779186" y2="-2779186"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="594" x2="546" y1="-2779154" y2="-2779154"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="546" x2="546" y1="-2779154" y2="-2779186"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="594" x2="594" y1="-2779186" y2="-2779154"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="375" x2="423" y1="-2779186" y2="-2779186"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="423" x2="375" y1="-2779154" y2="-2779154"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="375" x2="375" y1="-2779154" y2="-2779186"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="423" x2="423" y1="-2779186" y2="-2779154"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4027" y1="-2778969" y2="-2778969"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4027" y1="-2779183" y2="-2779183"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="3717" y1="-2779638" y2="-2779638"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3077" x2="3239" y1="-2779654" y2="-2779654"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2953" x2="3157" y1="-2779277" y2="-2779277"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2953" x2="3158" y1="-2779119" y2="-2779119"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2953" x2="2704" y1="-2778933" y2="-2778933"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2953" x2="2704" y1="-2778760" y2="-2778760"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4480" y1="-2779796" y2="-2779796"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4276" x2="4420" y1="-2780446" y2="-2780446"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4115" x2="4115" y1="-2780376" y2="-2779893"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4115" x2="3866" y1="-2780278" y2="-2780278"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4115" x2="3867" y1="-2780156" y2="-2780156"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3654" x2="3654" y1="-2780376" y2="-2780022"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3654" x2="3406" y1="-2780156" y2="-2780156"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="0.5" x1="137" x2="137" y1="-2778421" y2="-2778624"/>
   <line DF8003:Layer="10KV母线" stroke="rgb(255,255,255)" stroke-width="4" x1="1103" x2="2242" y1="-2781626" y2="-2781626"/>
   <line DF8003:Layer="10KV母线" stroke="rgb(255,255,255)" stroke-width="4" x1="5766" x2="6394" y1="-2781626" y2="-2781626"/>
   <line DF8003:Layer="10KV母线" stroke="rgb(255,255,255)" stroke-width="4" x1="1731" x2="2678" y1="-2779370" y2="-2779370"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1752.000000 -2780347.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1766.000000 -2780386.000000) translate(0,20)">下村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 703.000000 -2780510.000000) translate(0,20)">大村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 667.000000 -2780469.000000) translate(0,20)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 774.000000 -2779961.000000) translate(0,20)">中村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1316.000000 -2780266.000000) translate(0,20)">S9-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1308.000000 -2779992.000000) translate(0,20)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1308.000000 -2780032.000000) translate(0,20)">上必达</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 756.000000 -2779922.000000) translate(0,20)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1301.000000 -2780302.000000) translate(0,20)">必达村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1222.000000 -2779673.000000) translate(0,20)">黄果树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 492.000000 -2779659.000000) translate(0,20)">白泥塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -2779381.000000) translate(0,20)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1036.000000 -2779422.000000) translate(0,20)">背阴山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1136.000000 -2780530.000000) translate(0,20)">10kV 必 达 支 线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 460.000000 -2779922.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -2779951.000000) translate(0,20)">S8-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1958.000000 -2779994.000000) translate(0,20)">壮底私变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2063.000000 -2780305.000000) translate(0,20)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2063.000000 -2780349.000000) translate(0,20)">竹园私变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2603.000000 -2779984.000000) translate(0,20)">上务那</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2318.000000 -2779942.000000) translate(0,20)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2323.000000 -2779985.000000) translate(0,20)">下务那</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1749.000000 -2779777.000000) translate(0,20)">瓦房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1728.000000 -2779740.000000) translate(0,20)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 600.000000 -2779359.000000) translate(0,20)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 611.000000 -2779401.000000) translate(0,20)">黄草地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 970.000000 -2778684.000000) translate(0,20)">万木兰</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 966.000000 -2778647.000000) translate(0,20)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1148.000000 -2779079.000000) translate(0,20)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1163.000000 -2779117.000000) translate(0,20)">黑泥坝</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 361.000000 -2779067.000000) translate(0,20)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 388.000000 -2779105.000000) translate(0,20)">后山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 81.000000 -2779065.000000) translate(0,20)">S9-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 66.000000 -2779108.000000) translate(0,20)">八角村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 792.000000 -2779111.000000) translate(0,20)">核桃树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 784.000000 -2779076.000000) translate(0,20)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1744.000000 -2778725.000000) translate(0,20)">干海子</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1738.000000 -2778686.000000) translate(0,20)">S7-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1138.000000 -2778105.000000) translate(0,20)">S9-250kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1109.000000 -2778140.000000) translate(0,20)">八角集镇1号公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 524.000000 -2778101.000000) translate(0,20)">S11-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 540.000000 -2778139.000000) translate(0,20)">阴东山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 684.000000 -2778015.000000) translate(0,20)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 695.000000 -2778051.000000) translate(0,20)">天生坝</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 918.000000 -2777685.000000) translate(0,20)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 953.000000 -2777721.000000) translate(0,20)">老厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1753.000000 -2778151.000000) translate(0,20)">S8-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1770.000000 -2778189.000000) translate(0,20)">老烧箐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2478.000000 -2778021.000000) translate(0,20)">麦地湾</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2468.000000 -2777984.000000) translate(0,20)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2641.000000 -2778432.000000) translate(0,20)">罗黑村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2629.000000 -2778394.000000) translate(0,20)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3355.000000 -2778103.000000) translate(0,20)">大龙潭</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3341.000000 -2778065.000000) translate(0,20)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3923.000000 -2778094.000000) translate(0,20)">大麦地村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3950.000000 -2778058.000000) translate(0,20)">S9-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4509.000000 -2777950.000000) translate(0,20)">泥期苴</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4503.000000 -2777915.000000) translate(0,20)">S8-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3121.000000 -2780409.000000) translate(0,20)">大歇场私变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3165.000000 -2780373.000000) translate(0,20)">S8-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3957.000000 -2778699.000000) translate(0,20)">杨梅树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3828.000000 -2779124.000000) translate(0,20)">下村公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -2778561.000000) translate(0,20)">马鹿塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4989.000000 -2778522.000000) translate(0,20)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4999.000000 -2778557.000000) translate(0,20)">高家地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4486.000000 -2778519.000000) translate(0,20)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4523.000000 -2778557.000000) translate(0,20)">桃树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3932.000000 -2778859.000000) translate(0,20)">S7-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3950.000000 -2778900.000000) translate(0,20)">磨盘山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -2780432.000000) translate(0,20)">S9-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4486.000000 -2780468.000000) translate(0,20)">洒州村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2779460.000000) translate(0,20)">红梨树公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2906.000000 -2778367.000000) translate(0,20)">芭蕉箐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2903.000000 -2778327.000000) translate(0,20)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3688.000000 -2778061.000000) translate(0,20)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3703.000000 -2778100.000000) translate(0,20)">法拉本</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5061.000000 -2777955.000000) translate(0,20)">扎郎支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2036.000000 -2780525.000000) translate(0,20)">大凹</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2013.000000 -2780490.000000) translate(0,20)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 253.000000 -2778621.000000) translate(0,20)">S11-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 263.000000 -2778659.000000) translate(0,20)">攀枝花</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 711.000000 -2778523.000000) translate(0,20)">S11-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 729.000000 -2778563.000000) translate(0,20)">小水田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2930.000000 -2778062.000000) translate(0,20)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2891.000000 -2778102.000000) translate(0,20)">麦地湾抽水站专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 1481.000000 -2778338.000000) translate(0,13)">葫芦山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 1481.000000 -2778338.000000) translate(0,29)">四方架</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6076.000000 -2781490.000000) translate(0,20)">065</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 5894.000000 -2781592.000000) translate(0,32)">10kV一三联网线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6076.000000 -2781541.000000) translate(0,20)">0651</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="80" transform="matrix(1.000000 0.000000 0.000000 1.000000 1475.000000 -2781919.000000) translate(0,64)">五街河三级电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1729.000000 -2781586.000000) translate(0,32)">10kV一三联网线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1883.000000 -2781489.000000) translate(0,32)">092</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1877.000000 -2781536.000000) translate(0,32)">0921</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1880.000000 -2781378.000000) translate(0,32)">0926</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1877.000000 -2781430.000000) translate(0,32)">0923</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1535.000000 -2781374.000000) translate(0,32)">0916</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1533.000000 -2781429.000000) translate(0,32)">0913</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1545.000000 -2781490.000000) translate(0,32)">091</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1537.000000 -2781536.000000) translate(0,32)">0911</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1364.000000 -2781576.000000) translate(0,32)">10KV八角线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5663.000000 -2778992.000000) translate(0,20)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5657.000000 -2779029.000000) translate(0,20)">干坝</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4866.000000 -2778957.000000) translate(0,20)">新田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6431.000000 -2779407.000000) translate(0,20)">小水井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5925.000000 -2779701.000000) translate(0,20)">四甲村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6134.000000 -2779707.000000) translate(0,20)">法古苴大龙潭</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4850.000000 -2778921.000000) translate(0,20)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5354.000000 -2779733.000000) translate(0,20)">龙竹棚</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5342.000000 -2779693.000000) translate(0,20)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4828.000000 -2780225.000000) translate(0,20)">大平掌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4843.000000 -2780186.000000) translate(0,20)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5360.000000 -2780039.000000) translate(0,20)">罗么地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4813.000000 -2779190.000000) translate(0,20)">法古苴村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6414.000000 -2779364.000000) translate(0,20)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5910.000000 -2779665.000000) translate(0,20)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4838.000000 -2779147.000000) translate(0,20)">S9-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5753.000000 -2778765.000000) translate(0,20)">SHB15-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5769.000000 -2778802.000000) translate(0,20)">董家公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5074.000000 -2778702.000000) translate(0,20)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5080.000000 -2778741.000000) translate(0,20)">小箐边</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5152.000000 -2780480.000000) translate(0,20)">CT：40/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5155.000000 -2780516.000000) translate(0,20)">计量</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -2781160.000000) translate(0,20)">歇凉树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3897.000000 -2781119.000000) translate(0,20)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3919.000000 -2781163.000000) translate(0,20)">响水箐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3280.000000 -2781120.000000) translate(0,20)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4593.000000 -2781376.000000) translate(0,20)">大平地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4590.000000 -2781336.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2764.000000 -2781340.000000) translate(0,20)">S9-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2749.000000 -2781380.000000) translate(0,20)">庄房村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5252.000000 -2781380.000000) translate(0,20)">老君山电信机房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5291.000000 -2781341.000000) translate(0,20)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1145.000000 -2781187.000000) translate(0,20)">岔河</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1133.000000 -2781150.000000) translate(0,20)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -2778137.000000) translate(0,20)">扎营盘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4584.000000 -2778099.000000) translate(0,20)">S11-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3219.000000 -2779775.000000) translate(0,20)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1971.000000 -2777916.000000) translate(0,20)">大山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1951.000000 -2777876.000000) translate(0,20)">S11-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6076.000000 -2781412.000000) translate(0,20)">0653</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6076.000000 -2781334.000000) translate(0,20)">0656</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1567.000000 -2780231.000000) translate(0,20)">10kV 上 务  那  支 线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="80" transform="matrix(1.000000 0.000000 0.000000 1.000000 5757.000000 -2781917.000000) translate(0,64)">五街河一级电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 676.000000 -2778143.000000) translate(0,20)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 671.000000 -2778179.000000) translate(0,20)">山萝卜地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2544.000000 -2781206.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2547.000000 -2781245.000000) translate(0,20)">庄房大村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4305.000000 -2781329.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4311.000000 -2781376.000000) translate(0,20)">纸房箐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2538.000000 -2780934.000000) translate(0,20)">S7-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2522.000000 -2780972.000000) translate(0,20)">三级站取水口公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3603.000000 -2781075.000000) translate(0,20)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3579.000000 -2781119.000000) translate(0,20)">李常荣私变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4874.000000 -2781376.000000) translate(0,20)">在可么</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5087.000000 -2781376.000000) translate(0,20)">土地塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5856.000000 -2779376.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5856.000000 -2779376.000000) translate(0,44)">董</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5856.000000 -2779376.000000) translate(0,68)">家</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5856.000000 -2779376.000000) translate(0,92)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5856.000000 -2779376.000000) translate(0,116)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5856.000000 -2779376.000000) translate(0,140)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5249.000000 -2779486.000000) translate(0,20)">10kV大龙潭分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5047.000000 -2780707.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5047.000000 -2780707.000000) translate(0,44)">法</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5047.000000 -2780707.000000) translate(0,68)">古</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5047.000000 -2780707.000000) translate(0,92)">苴</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5047.000000 -2780707.000000) translate(0,116)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5047.000000 -2780707.000000) translate(0,140)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1340.000000 -2780983.000000) translate(0,20)">CT：50/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1532.000000 -2780607.000000) translate(0,20)">10kV大凹支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1546.000000 -2777994.000000) translate(0,20)">10kV大山支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1203.000000 -2777777.000000) translate(0,20)">S11-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4766.000000 -2778061.000000) translate(0,20)">S8-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4773.000000 -2778098.000000) translate(0,20)">小马田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -2779266.000000) translate(0,20)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3892.000000 -2779333.000000) translate(0,20)">大桥地移动基站专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1165.000000 -2778825.000000) translate(0,20)">10kV八角村委会支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1147.000000 -2778426.000000) translate(0,32)">10kV攀枝花支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4313.000000 -2778294.000000) translate(0,20)">10KV高家地分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 5400.000000 -2777792.000000) translate(0,32)">至中山变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 -2781119.000000) translate(0,20)">10kV岔河支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1534.000000 -2779711.000000) translate(0,20)">10kV瓦房支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 775.000000 -2779814.000000) translate(0,20)">10kV白泥塘分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 684.000000 -2780184.000000) translate(0,20)">10kV大干田分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 716.000000 -2779159.000000) translate(0,20)">10KV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 716.000000 -2779159.000000) translate(0,44)">黄</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 716.000000 -2779159.000000) translate(0,68)">草</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 716.000000 -2779159.000000) translate(0,92)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 716.000000 -2779159.000000) translate(0,116)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 716.000000 -2779159.000000) translate(0,140)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 716.000000 -2779159.000000) translate(0,164)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 999.000000 -2778263.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 999.000000 -2778263.000000) translate(0,44)">老</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 999.000000 -2778263.000000) translate(0,68)">厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 999.000000 -2778263.000000) translate(0,92)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 999.000000 -2778263.000000) translate(0,116)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 999.000000 -2778263.000000) translate(0,140)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2763.000000 -2778110.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2763.000000 -2778110.000000) translate(0,44)">罗</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2763.000000 -2778110.000000) translate(0,68)">黑</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2763.000000 -2778110.000000) translate(0,92)">村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2763.000000 -2778110.000000) translate(0,116)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2763.000000 -2778110.000000) translate(0,140)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3426.000000 -2778019.000000) translate(0,20)">10KV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3426.000000 -2778019.000000) translate(0,44)">大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3426.000000 -2778019.000000) translate(0,68)">龙</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3426.000000 -2778019.000000) translate(0,92)">潭</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3426.000000 -2778019.000000) translate(0,116)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3426.000000 -2778019.000000) translate(0,140)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -2778016.000000) translate(0,20)">10KV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -2778016.000000) translate(0,44)">法</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -2778016.000000) translate(0,68)">拉</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -2778016.000000) translate(0,92)">本</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -2778016.000000) translate(0,116)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -2778016.000000) translate(0,140)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2962.000000 -2778623.000000) translate(0,20)">10KV芭蕉箐分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3994.000000 -2778628.000000) translate(0,20)">10kV杨梅树分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4296.000000 -2778138.000000) translate(0,20)">10KV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4296.000000 -2778138.000000) translate(0,44)">洒</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4296.000000 -2778138.000000) translate(0,68)">州</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4296.000000 -2778138.000000) translate(0,92)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4296.000000 -2778138.000000) translate(0,116)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4454.000000 -2777865.000000) translate(0,20)">10kV泥期苴支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4935.000000 -2777704.000000) translate(0,20)">扎郎隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4836.000000 -2780333.000000) translate(0,20)">10kV大平掌分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,20)">10KV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,44)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,68)">庄</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,92)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,116)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,140)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,164)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,188)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2781273.000000) translate(0,212)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 676.000000 -2777736.000000) translate(0,20)">宏兴锌矿1号专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 740.000000 -2777647.000000) translate(0,20)">S11-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 485.000000 -2777729.000000) translate(0,20)">宏兴锌矿2号专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 547.000000 -2777641.000000) translate(0,20)">S11-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" transform="matrix(1.000000 0.000000 0.000000 1.000000 1181.000000 -2777907.000000) translate(0,26)">SH15-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1177.000000 -2777942.000000) translate(0,20)">桃园公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5548.000000 -2781545.000000) translate(0,20)">老君山沙场</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5576.000000 -2781512.000000) translate(0,20)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -2781343.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5050.000000 -2781342.000000) translate(0,20)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 284.000000 -2777766.000000) translate(0,20)">宏兴锌矿3号专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 341.000000 -2777646.000000) translate(0,20)">S11-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 81.000000 -2777876.000000) translate(0,20)">宏兴锌矿4号专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 143.000000 -2777796.000000) translate(0,20)">S11-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -2777817.000000) translate(0,20)">CT:50/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5454.000000 -2781620.000000) translate(0,20)">CT:5/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 81.000000 -2778155.000000) translate(0,32)">至哨房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3521.000000 -2781289.000000) translate(0,20)">10kV李常</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3521.000000 -2781289.000000) translate(0,44)">   荣私</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3521.000000 -2781289.000000) translate(0,68)">   变分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3521.000000 -2781289.000000) translate(0,92)">   支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3105.000000 -2781446.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3105.000000 -2781446.000000) translate(0,44)">响水</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3105.000000 -2781446.000000) translate(0,68)">箐分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3105.000000 -2781446.000000) translate(0,92)">支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3956.000000 -2781609.000000) translate(0,20)">10KV庄  房  支  线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 761.000000 -2777914.000000) translate(0,20)">CT:25/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2486.000000 -2781111.000000) translate(0,20)">CT：20/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 782.000000 -2778352.000000) translate(0,16)">BZ044</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 208.000000 -2778371.000000) translate(0,16)">BZ042</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 430.000000 -2777944.000000) translate(0,16)">10kV老厂分支线宏兴锌矿分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4164.000000 -2779309.000000) translate(0,20)">BZ038</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3104.000000 -2779848.000000) translate(0,20)">BZ039</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3213.000000 -2779888.000000) translate(0,20)">蚂蚁堆移动基站专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4485.000000 -2779961.000000) translate(0,20)">滑石板电信基站专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4491.000000 -2779922.000000) translate(0,20)">S11-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4298.000000 -2779908.000000) translate(0,13)">CT：5/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 86.000000 -2778286.000000) translate(0,16)">Z0491</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 82.000000 -2778221.000000) translate(0,16)">Z049</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 312.000000 -2778474.000000) translate(0,10)">10kV攀枝花公变分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 225.000000 -2779254.000000) translate(0,20)">恒鑫矿业专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 237.000000 -2779218.000000) translate(0,20)">S11-315kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 443.000000 -2779248.000000) translate(0,13)">CT：25/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2672.000000 -2781306.000000) translate(0,20)">#7杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3508.000000 -2781034.000000) translate(0,20)">#4杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4410.000000 -2781424.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5856.000000 -2778868.000000) translate(0,20)">#13杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6278.000000 -2779588.000000) translate(0,20)">#22杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6527.000000 -2779469.000000) translate(0,20)">#26杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5191.000000 -2778805.000000) translate(0,20)">#30杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4862.000000 -2777989.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4700.000000 -2778193.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4383.000000 -2777949.000000) translate(0,20)">#5杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4049.000000 -2778745.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4067.000000 -2779325.000000) translate(0,16)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4405.000000 -2780005.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3300.000000 -2780431.000000) translate(0,20)">#6杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -2779813.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -2777999.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3629.000000 -2778009.000000) translate(0,20)">#5杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3268.000000 -2778009.000000) translate(0,20)">#8杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1122.000000 -2777867.000000) translate(0,20)">#4杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 815.000000 -2778195.000000) translate(0,10)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 820.000000 -2778067.000000) translate(0,10)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 791.000000 -2778466.000000) translate(0,10)">#5杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 580.000000 -2778177.000000) translate(0,10)">#2杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 730.000000 -2777848.000000) translate(0,20)">#9杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 139.000000 -2777916.000000) translate(0,20)">#20杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 586.000000 -2777907.000000) translate(0,20)">#13杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 443.000000 -2777843.000000) translate(0,20)">#16杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1652.000000 -2778779.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 1411.000000 -2778956.000000) translate(0,13)">#83杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1101.000000 -2779009.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1064.000000 -2778733.000000) translate(0,20)">#6杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 856.000000 -2780003.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1217.000000 -2780237.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2094.000000 -2780046.000000) translate(0,20)">#12杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2424.000000 -2780040.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -2781235.000000) translate(0,20)">#9杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2514.000000 -2781344.000000) translate(0,20)">10kV大村分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4079.000000 -2781542.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4079.000000 -2781542.000000) translate(0,44)">         核桃</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4079.000000 -2781542.000000) translate(0,68)">         树分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4079.000000 -2781542.000000) translate(0,92)">         支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1120.000000 -2780354.000000) translate(0,20)">10kV必达村委会分     支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1088.000000 -2779525.000000) translate(0,20)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1122.000000 -2779978.000000) translate(0,20)">10kV上必达</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1122.000000 -2779978.000000) translate(0,44)">分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1761.000000 -2780142.000000) translate(0,20)">10kV壮底</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1761.000000 -2780142.000000) translate(0,44)">       分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1203.000000 -2779008.000000) translate(0,20)">10kV         黑泥坝         分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 970.000000 -2778793.000000) translate(0,20)">10kV万木兰分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 783.000000 -2778424.000000) translate(0,10)">10kV小水田分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 565.000000 -2778293.000000) translate(0,10)">10kV阴东山分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1090.000000 -2777982.000000) translate(0,20)">10kV桃园分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4303.000000 -2778499.000000) translate(0,20)">10kV        桃树分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3002.000000 -2779860.000000) translate(0,20)">10kV红梨树分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 503.000000 -2778815.000000) translate(0,13)">37</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -2777815.000000) translate(0,20)">八角集镇2号公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5354.000000 -2777544.000000) translate(0,20)">制图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5576.000000 -2777545.000000) translate(0,20)">审核</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5801.000000 -2777544.000000) translate(0,20)">批准</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6035.000000 -2777543.000000) translate(0,20)">绘制日</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6384.000000 -2777543.000000) translate(0,20)">生效日</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5451.000000 -2777541.000000) translate(0,20)">胡助平</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5670.000000 -2777544.000000) translate(0,20)">余明光</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5909.000000 -2777545.000000) translate(0,20)">刘峰</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6167.000000 -2777543.000000) translate(0,20)">2012.07.02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6538.000000 -2777544.000000) translate(0,20)">2013.01.09</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" transform="matrix(1.000000 0.000000 0.000000 1.000000 1189.000000 -2779638.000000) translate(0,26)">S13-M-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 452.000000 -2779627.000000) translate(0,20)">S13-M-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2562.000000 -2779946.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5348.000000 -2780002.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 -2778524.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3897.000000 -2778664.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6152.000000 -2779671.000000) translate(0,20)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3747.000000 -2778966.000000) translate(0,20)">SH15-M-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3829.000000 -2779007.000000) translate(0,20)">塘坊公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4064.000000 -2778995.000000) translate(0,13)">10kV塘坊支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4036.000000 -2778962.000000) translate(0,13)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3751.000000 -2779086.000000) translate(0,20)">SH15-M-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4060.000000 -2779107.000000) translate(0,13)">10kV下村支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4036.000000 -2779074.000000) translate(0,13)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3747.000000 -2779180.000000) translate(0,20)">SH15-M-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -2779217.000000) translate(0,20)">麦地心1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4040.000000 -2779209.000000) translate(0,13)">10kV麦地心1号支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4036.000000 -2779177.000000) translate(0,13)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3634.000000 -2779577.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3650.000000 -2779612.000000) translate(0,20)">老二箐公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3944.000000 -2779684.000000) translate(0,16)">10kV老二箐支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3726.000000 -2779632.000000) translate(0,13)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4224.000000 -2779636.000000) translate(0,13)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4050.000000 -2779380.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4049.000000 -2779417.000000) translate(0,20)">麦地心2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -2779658.000000) translate(0,13)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4095.000000 -2779596.000000) translate(0,13)">10kV麦地心2号支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4150.000000 -2779503.000000) translate(0,13)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3870.000000 -2779457.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3870.000000 -2779494.000000) translate(0,20)">麦地心3号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3959.000000 -2779657.000000) translate(0,13)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3881.000000 -2779636.000000) translate(0,13)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4549.000000 -2779787.000000) translate(0,20)">SH15-M-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4567.000000 -2779823.000000) translate(0,20)">滑石板公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4309.000000 -2779821.000000) translate(0,13)">10kV滑石板支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4464.000000 -2779794.000000) translate(0,13)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4302.000000 -2779790.000000) translate(0,13)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -2780523.000000) translate(0,20)">10KV芭蕉箐分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3102.000000 -2779689.000000) translate(0,13)">10kV李家支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3297.000000 -2779637.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3308.000000 -2779672.000000) translate(0,20)">李家公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2779422.000000) translate(0,20)">SH15-M-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2983.000000 -2779312.000000) translate(0,13)">10kV龙潭1号支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3070.000000 -2779217.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3089.000000 -2779254.000000) translate(0,20)">龙潭1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2983.000000 -2779154.000000) translate(0,13)">10kV龙潭2号支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3071.000000 -2779060.000000) translate(0,20)">SH15-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3089.000000 -2779096.000000) translate(0,20)">龙潭2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2438.000000 -2778931.000000) translate(0,20)">SH15-M-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2457.000000 -2778967.000000) translate(0,20)">大村公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2717.000000 -2778959.000000) translate(0,13)">10kV大村支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2707.000000 -2778930.000000) translate(0,13)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2438.000000 -2778757.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2457.000000 -2778793.000000) translate(0,20)">瓦窑公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2717.000000 -2778785.000000) translate(0,13)">10kV瓦窑支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2707.000000 -2778757.000000) translate(0,13)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2910.000000 -2778779.000000) translate(0,13)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3852.000000 -2780403.000000) translate(0,13)">10kV大歇场支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -2780398.000000) translate(0,13)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4128.000000 -2780135.000000) translate(0,13)">10kV丫口支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3808.000000 -2780325.000000) translate(0,20)">SH15-M-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3798.000000 -2780254.000000) translate(0,20)">大青树公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3940.000000 -2780275.000000) translate(0,13)">10kV大青树支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -2780276.000000) translate(0,13)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4121.000000 -2780288.000000) translate(0,13)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4141.000000 -2780326.000000) translate(0,13)">10kV丫口支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3808.000000 -2780202.000000) translate(0,20)">SH15-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3800.000000 -2780135.000000) translate(0,20)">红光公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3870.000000 -2780153.000000) translate(0,13)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4122.000000 -2780165.000000) translate(0,13)">9-10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3951.000000 -2779864.000000) translate(0,20)">丫口公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3945.000000 -2779827.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 4093.000000 -2779911.000000) translate(0,13)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -2780165.000000) translate(0,13)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3348.000000 -2780202.000000) translate(0,20)">SH15-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3339.000000 -2780135.000000) translate(0,20)">田房1号公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3409.000000 -2780153.000000) translate(0,13)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3650.000000 -2780396.000000) translate(0,13)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3663.000000 -2780306.000000) translate(0,13)">10kV田房支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3428.000000 -2779988.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3641.000000 -2780040.000000) translate(0,13)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3438.000000 -2780022.000000) translate(0,20)">田房2号公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -2780139.000000) translate(0,13)">10kV田房支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 2169.000000 -2779352.000000) translate(0,32)">10kV中角线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 2319.000000 -2779233.000000) translate(0,32)">093</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 2314.000000 -2779285.000000) translate(0,32)">0931</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 2314.000000 -2779174.000000) translate(0,32)">0936</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 2178.000000 -2778190.000000) translate(0,32)">10kV中角线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1928.000000 -2777746.000000) translate(0,32)">10kV八角线</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 297.000000 -2778055.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 297.000000 -2778092.000000) translate(0,20)">小水田公变</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 382.000000 -2778333.000000) translate(0,13)">23</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 342.000000 -2778271.000000) translate(0,13)">10kV小水田支线</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 397.000000 -2778178.000000) translate(0,13)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 290.000000 -2778428.000000) translate(0,13)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 143.000000 -2778541.000000) translate(0,13)">10kV小村</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 143.000000 -2778541.000000) translate(0,29)">支线</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 146.000000 -2778621.000000) translate(0,13)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 100.000000 -2778715.000000) translate(0,20)">SH15-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 100.000000 -2778752.000000) translate(0,20)">小村公变</text>
   <text DF8003:Layer="0" fill="rgb(43,43,43)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 611.000000 -2781814.000000) translate(0,16)">八角供电所10kV电网调度用图</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2783.000000 -2781596.000000) translate(0,20)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3305.000000 -2781377.000000) translate(0,20)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3913.000000 -2781377.000000) translate(0,20)">#26杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2403.000000 -2780770.000000) translate(0,20)">#61杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4326.000000 -2781596.000000) translate(0,20)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4620.000000 -2781596.000000) translate(0,20)">#28杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4892.000000 -2781596.000000) translate(0,20)">#32杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5113.000000 -2781596.000000) translate(0,20)">#37杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2445.000000 -2781316.000000) translate(0,20)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2452.000000 -2781420.000000) translate(0,20)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -2781596.000000) translate(0,20)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3350.000000 -2780862.000000) translate(0,32)">10kV一三联网线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3575.000000 -2781314.000000) translate(0,20)">BZ058</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4759.000000 -2781599.000000) translate(0,20)">#29杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -2781582.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -2781582.000000) translate(0,44)">ZO586</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -2780805.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -2780805.000000) translate(0,44)">ZO541</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -2780645.000000) translate(0,20)">ZO54</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5136.000000 -2780287.000000) translate(0,20)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5006.000000 -2780252.000000) translate(0,20)">BZ056</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5136.000000 -2779234.000000) translate(0,20)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5136.000000 -2779014.000000) translate(0,20)">#23杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5840.000000 -2779080.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5924.000000 -2779441.000000) translate(0,20)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 6192.000000 -2779446.000000) translate(0,20)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5790.000000 -2779486.000000) translate(0,20)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5033.000000 -2780027.000000) translate(0,20)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5033.000000 -2779724.000000) translate(0,20)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5033.000000 -2779466.000000) translate(0,20)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1520.000000 -2781167.000000) translate(0,20)">#117杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1393.000000 -2781201.000000) translate(0,20)">BZ029</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1541.000000 -2781048.000000) translate(0,20)">116</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1541.000000 -2781048.000000) translate(0,44)">FO181</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1394.000000 -2780584.000000) translate(0,20)">#109杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -2780490.000000) translate(0,20)">#103杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2354.000000 -2780208.000000) translate(0,20)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -2780202.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1391.000000 -2780188.000000) translate(0,20)">#94杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1013.000000 -2780288.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 990.000000 -2780022.000000) translate(0,20)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1102.000000 -2780156.000000) translate(0,20)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1094.000000 -2779854.000000) translate(0,20)">#18杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 989.000000 -2779716.000000) translate(0,20)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1417.000000 -2779755.000000) translate(0,20)">#85杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1422.000000 -2778714.000000) translate(0,20)">#76杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 973.000000 -2779887.000000) translate(0,20)">BZ036</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1539.000000 -2779791.000000) translate(0,20)">BZ035</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1540.000000 -2778523.000000) translate(0,32)">F0161</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1520.000000 -2777857.000000) translate(0,20)">#59杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1413.000000 -2777962.000000) translate(0,20)">#61杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4193.000000 -2778551.000000) translate(0,20)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4622.000000 -2778353.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4816.000000 -2778307.000000) translate(0,20)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2778876.000000) translate(0,20)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2779093.000000) translate(0,20)">#26杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2779286.000000) translate(0,20)">#28杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4186.000000 -2780458.000000) translate(0,20)">#40杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2780390.000000) translate(0,20)">#39杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3054.000000 -2779814.000000) translate(0,20)">3</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4981.000000 -2777869.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4981.000000 -2777869.000000) translate(0,44)">FO131</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4783.000000 -2777757.000000) translate(0,20)">#37杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3980.000000 -2777755.000000) translate(0,20)">#49杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3372.000000 -2777755.000000) translate(0,20)">#55杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2951.000000 -2777755.000000) translate(0,20)">#56杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2649.000000 -2777755.000000) translate(0,20)">#57杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1178.000000 -2778334.000000) translate(0,10)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 958.000000 -2778334.000000) translate(0,10)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 742.000000 -2778309.000000) translate(0,10)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 539.000000 -2778332.000000) translate(0,10)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 929.000000 -2778052.000000) translate(0,10)">#15杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1523.000000 -2778885.000000) translate(0,20)">#83杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 119.000000 -2778859.000000) translate(0,20)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 990.000000 -2778901.000000) translate(0,20)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1182.000000 -2778854.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1763.000000 -2780601.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1322.000000 -2780453.000000) translate(0,20)">Z0271</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1111.000000 -2780093.000000) translate(0,20)">Z0272</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1578.000000 -2780140.000000) translate(0,20)">Z0261</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1414.000000 -2778914.000000) translate(0,20)">Z0251</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1314.000000 -2778914.000000) translate(0,20)">BZ025</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 541.000000 -2778950.000000) translate(0,20)">BZ034</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1524.000000 -2778393.000000) translate(0,20)">#66杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1314.000000 -2778364.000000) translate(0,20)">BZ022</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1035.000000 -2778364.000000) translate(0,20)">BZ023</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 884.000000 -2778265.000000) translate(0,20)">BZ024</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1540.000000 -2778109.000000) translate(0,32)">F0151</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1540.000000 -2778166.000000) translate(0,32)">F015</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1557.000000 -2777919.000000) translate(0,20)">Z0211</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1853.000000 -2777993.000000) translate(0,20)">BZ021</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2687.000000 -2778061.000000) translate(0,20)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4250.000000 -2777755.000000) translate(0,20)">#44杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(0.819152 0.573576 -0.573576 0.819152 4345.659649 -2777803.765834) translate(0,20)">BZ031</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4289.000000 -2778678.000000) translate(0,20)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4190.000000 -2778712.000000) translate(0,20)">BZ037</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2855.000000 -2779932.000000) translate(0,20)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2845.000000 -2778841.000000) translate(0,20)">BZ018</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4193.000000 -2778330.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1990.000000 -2780625.000000) translate(0,20)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1096.000000 -2779526.000000) translate(0,20)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 600.000000 -2779266.000000) translate(0,20)">#12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 247.000000 -2778533.000000) translate(0,10)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 983.000000 -2777867.000000) translate(0,20)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1540.000000 -2777810.000000) translate(0,32)">F0141</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1317.000000 -2777874.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1975.000000 -2778003.000000) translate(0,20)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2576.000000 -2777829.000000) translate(0,20)">BZ019</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3284.000000 -2777835.000000) translate(0,20)">BZ033</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3639.000000 -2777824.000000) translate(0,20)">Z0321</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2875.000000 -2778475.000000) translate(0,20)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3091.000000 -2779560.000000) translate(0,20)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4285.000000 -2778364.000000) translate(0,20)">BZ016</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4189.000000 -2777872.000000) translate(0,20)">Z015</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 4449.000000 -2777906.000000) translate(0,6)">#5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4982.000000 -2777736.000000) translate(0,20)">#36杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2440.000000 -2781176.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2459.000000 -2780886.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2459.000000 -2780886.000000) translate(0,44)">ZO571</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 873.000000 -2777801.000000) translate(0,20)">Z0242</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1012.000000 -2777939.000000) translate(0,20)">Z0245</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 894.000000 -2777912.000000) translate(0,20)">#23杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5268.000000 -2781599.000000) translate(0,20)">#42杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5353.000000 -2781550.000000) translate(0,20)">Z0588</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5092.000000 -2780822.000000) translate(0,20)">#36杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5006.000000 -2778313.000000) translate(0,20)">#15杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4304.000000 -2777829.000000) translate(0,20)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3711.000000 -2777754.000000) translate(0,20)">#54杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1757.000000 -2777934.000000) translate(0,20)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1867.000000 -2777934.000000) translate(0,20)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 928.000000 -2778173.000000) translate(0,10)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 939.000000 -2778284.000000) translate(0,10)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 254.000000 -2778309.000000) translate(0,10)">#29杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 810.000000 -2778862.000000) translate(0,20)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 630.000000 -2778860.000000) translate(0,20)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 590.000000 -2779106.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 810.000000 -2780137.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -2780057.000000) translate(0,20)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2609.000000 -2780210.000000) translate(0,20)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5582.000000 -2781626.000000) translate(0,20)">#45杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5357.000000 -2781612.000000) translate(0,20)">#43杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4296.000000 -2777881.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5140.000000 -2779494.000000) translate(0,20)">BZ055</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5151.000000 -2780742.000000) translate(0,20)">#36杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -2780677.000000) translate(0,20)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2853.000000 -2778873.000000) translate(0,20)">#21杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1043.000000 -2778309.000000) translate(0,10)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 308.000000 -2777929.000000) translate(0,20)">#18杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2592.000000 -2778141.000000) translate(0,20)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2457.000000 -2781493.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2457.000000 -2781493.000000) translate(0,44)">ZO58</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2453.000000 -2781419.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2453.000000 -2781419.000000) translate(0,44)">ZO581</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3523.000000 -2781380.000000) translate(0,20)">#25杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4795.000000 -2780287.000000) translate(0,20)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4421.000000 -2778587.000000) translate(0,20)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2696.000000 -2778153.000000) translate(0,20)">BZ041</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2703.000000 -2778327.000000) translate(0,20)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1418.000000 -2777795.000000) translate(0,20)">#59杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1403.000000 -2778150.000000) translate(0,20)">#66杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1415.000000 -2778510.000000) translate(0,20)">#67杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1309.000000 -2778949.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 987.000000 -2777985.000000) translate(0,20)">BZ043</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 306.000000 -2777892.000000) translate(0,20)">Z0243</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 550.000000 -2779709.000000) translate(0,20)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 981.000000 -2779924.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1226.000000 -2780063.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 795.000000 -2780539.000000) translate(0,20)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1652.000000 -2779794.000000) translate(0,20)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1401.000000 -2781236.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5152.000000 -2779962.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5152.000000 -2779962.000000) translate(0,44)">ZO544</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5085.000000 -2779906.000000) translate(0,20)">#8</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4514.000000 -2777772.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4514.000000 -2777772.000000) translate(0,44)">FO132</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4564.000000 -2777804.000000) translate(0,20)">#43杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3222.000000 -2781538.000000) translate(0,20)">BZ057</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2709.000000 -2777836.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 907.000000 -2778823.000000) translate(0,20)">BZ045</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 1034.000000 -2778829.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 720.000000 -2778369.000000) translate(0,10)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 901.000000 -2777831.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4186.000000 -2778759.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4183.000000 -2779944.000000) translate(0,20)">#35杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2779972.000000) translate(0,20)">K0156</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 83.000000 -2778259.000000) translate(0,20)">#33杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 526.000000 -2779214.000000) translate(0,20)">K046</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 573.000000 -2779145.000000) translate(0,20)">K0461</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 373.000000 -2779145.000000) translate(0,20)">K001</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 662.000000 -2779184.000000) translate(0,20)">#10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 612.000000 -2779215.000000) translate(0,20)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 280.000000 -2779144.000000) translate(0,20)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 460.000000 -2779219.000000) translate(0,20)">#4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 2277.000000 -2777748.000000) translate(0,13)">58号杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 5791.000000 -2777720.000000) translate(0,13)">注：红色部份为本次更改设备。</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 474.000000 -2778892.000000) translate(0,16)">14</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 494.000000 -2778833.000000) translate(0,15)">K0345</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 498.000000 -2778806.000000) translate(0,15)">10kV八角烤烟育苗基地支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 446.000000 -2778537.000000) translate(0,15)">S11-M-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 0.000000 0.000000 1.000000 473.000000 -2778814.000000) translate(0,13)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 502.000000 -2778642.000000) translate(0,15)">八角烤烟育苗基地专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4182.000000 -2777924.000000) translate(0,20)">Z0151</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5147.000000 -2780648.000000) translate(0,20)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5147.000000 -2780648.000000) translate(0,44)">ZO546</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5836.000000 -2778992.000000) translate(0,20)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 5851.000000 -2778957.000000) translate(0,20)">Z0551</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2778983.000000) translate(0,20)">#23杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4162.000000 -2778963.000000) translate(0,20)">Z0152</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4159.000000 -2779074.000000) translate(0,20)">Z0153</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2779197.000000) translate(0,20)">#27杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4162.000000 -2779178.000000) translate(0,20)">Z0160</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4282.000000 -2779652.000000) translate(0,20)">#29杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4162.000000 -2779682.000000) translate(0,20)">Z0161</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4148.000000 -2779591.000000) translate(0,20)">Z0163</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3827.000000 -2779682.000000) translate(0,20)">Z0162</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4189.000000 -2779808.000000) translate(0,20)">#34杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4325.000000 -2779792.000000) translate(0,20)">Z0164</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2971.000000 -2779899.000000) translate(0,20)">Z0154</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3121.000000 -2779650.000000) translate(0,20)">Z0155</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3043.000000 -2779670.000000) translate(0,20)">10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3261.000000 -2779699.000000) translate(0,20)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2862.000000 -2779296.000000) translate(0,20)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3001.000000 -2779273.000000) translate(0,20)">Z0156</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3182.000000 -2779325.000000) translate(0,20)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2863.000000 -2779138.000000) translate(0,20)">#15杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3002.000000 -2779115.000000) translate(0,20)">Z0157</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3183.000000 -2779168.000000) translate(0,20)">4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2959.000000 -2778947.000000) translate(0,20)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2825.000000 -2778930.000000) translate(0,20)">Z0168</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2959.000000 -2778774.000000) translate(0,20)">#22杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2825.000000 -2778756.000000) translate(0,20)">Z0169</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4135.000000 -2780355.000000) translate(0,20)">Z0165</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 4135.000000 -2780257.000000) translate(0,20)">Z0166</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 3674.000000 -2780355.000000) translate(0,20)">Z0167</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="80" transform="matrix(1.000000 0.000000 0.000000 1.000000 1683.000000 -2779512.000000) translate(0,64)">35kV 八 角 变 电 站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 2213.000000 -2779040.000000) translate(0,20)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 0.000000 0.000000 1.000000 2301.000000 -2779074.000000) translate(0,32)">F0133</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 396.000000 -2778266.000000) translate(0,20)">Z0231</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 191.000000 -2778461.000000) translate(0,20)">Z0232</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 0.000000 0.000000 1.000000 473.000000 -2779959.000000) translate(0,20)">大干田</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 6527.000000 -2777808.000000) translate(0,16)">高压计量箱</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 6545.000000 -2777749.000000) translate(0,16)">跌落保险</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 6545.000000 -2777853.000000) translate(0,16)">断路器</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 6544.000000 -2777918.000000) translate(0,16)">隔离开关</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 5470.000000 -2777635.000000) translate(0,16)">\W5.8498;  \W1; 八角供电所10kV电网调度用图</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 2955.000000 -2782119.000000) translate(0,16)"> 八角供电所电网接线图</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 6442.000000 -2777976.000000) translate(0,16)"> 图  例</text>
   <text DF8003:Layer="10KV母线" fill="rgb(255,255,255)" font-family="SimSun" font-size="80" transform="matrix(1.000000 0.000000 0.000000 1.000000 1589.000000 -2781806.000000) translate(0,64)">10kV母线</text>
   <text DF8003:Layer="10KV母线" fill="rgb(255,255,255)" font-family="SimSun" font-size="80" transform="matrix(1.000000 0.000000 0.000000 1.000000 5831.000000 -2781801.000000) translate(0,64)">10kV母线</text>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="0" cx="5113" cy="-2780494" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5113" cy="-2780494" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5137" cy="-2780494" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5137" cy="-2780494" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3553" cy="-2781086" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3553" cy="-2781066" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2814" cy="-2781431" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2814" cy="-2781411" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4357" cy="-2781431" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4357" cy="-2781411" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4650" cy="-2781431" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4650" cy="-2781411" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4922" cy="-2781431" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4922" cy="-2781411" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5143" cy="-2781431" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5143" cy="-2781411" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5602" cy="-2781565" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5622" cy="-2781565" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3339" cy="-2781211" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3339" cy="-2781191" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3959" cy="-2781212" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3959" cy="-2781192" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2594" cy="-2781273" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2614" cy="-2781273" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2594" cy="-2781004" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2614" cy="-2781004" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4902" cy="-2780271" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4882" cy="-2780271" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5289" cy="-2780009" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5309" cy="-2780009" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5289" cy="-2779709" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5309" cy="-2779709" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4902" cy="-2779219" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4882" cy="-2779219" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4902" cy="-2778999" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4882" cy="-2778999" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5127" cy="-2778792" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5127" cy="-2778772" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="6461" cy="-2779448" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="6481" cy="-2779448" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="6230" cy="-2779578" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="6230" cy="-2779598" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5955" cy="-2779578" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5955" cy="-2779598" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5707" cy="-2779064" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5687" cy="-2779064" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5831" cy="-2778852" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5831" cy="-2778832" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1285" cy="-2781151" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1265" cy="-2781151" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-2780432" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-2780412" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2060" cy="-2780563" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2080" cy="-2780563" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2033" cy="-2780301" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2033" cy="-2780321" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2033" cy="-2780037" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2033" cy="-2780017" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2374" cy="-2780037" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2374" cy="-2780017" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2636" cy="-2780037" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2636" cy="-2780017" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="822" cy="-2780475" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="842" cy="-2780475" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1253" cy="-2780008" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1273" cy="-2780008" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1253" cy="-2780273" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1273" cy="-2780273" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1253" cy="-2779703" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1273" cy="-2779703" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1088" cy="-2779474" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1088" cy="-2779454" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="521" cy="-2780006" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="521" cy="-2779986" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="805" cy="-2780006" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="805" cy="-2779986" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="521" cy="-2779705" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="521" cy="-2779685" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1675" cy="-2779742" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1695" cy="-2779742" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1679" cy="-2778701" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1699" cy="-2778701" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1010" cy="-2778733" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1010" cy="-2778713" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="841" cy="-2778996" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="841" cy="-2779016" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1203" cy="-2778996" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1203" cy="-2779016" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="659" cy="-2779286" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="659" cy="-2779306" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="537" cy="-2779065" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="517" cy="-2779065" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="553" cy="-2778182" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="553" cy="-2778162" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1190" cy="-2778182" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1190" cy="-2778162" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="767" cy="-2778446" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="767" cy="-2778466" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="285" cy="-2778553" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="285" cy="-2778573" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="976" cy="-2777774" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="976" cy="-2777754" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="845" cy="-2778155" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="825" cy="-2778155" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-2778074" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-2778094" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2060" cy="-2777943" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2080" cy="-2777943" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1285" cy="-2777842" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1265" cy="-2777842" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2547" cy="-2778048" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2527" cy="-2778048" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2678" cy="-2778311" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2678" cy="-2778331" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2982" cy="-2777983" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2982" cy="-2778003" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3389" cy="-2777983" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3389" cy="-2778003" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3742" cy="-2777983" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3742" cy="-2778003" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4003" cy="-2777983" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4003" cy="-2778003" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4814" cy="-2777985" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4814" cy="-2778005" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4845" cy="-2778445" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4845" cy="-2778465" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4655" cy="-2778183" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4655" cy="-2778163" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5034" cy="-2778445" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5034" cy="-2778465" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4436" cy="-2778529" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4456" cy="-2778529" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4112" cy="-2778662" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4092" cy="-2778662" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4112" cy="-2778862" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4092" cy="-2778862" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4011" cy="-2779082" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3991" cy="-2779082" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4112" cy="-2779272" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4092" cy="-2779272" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3337" cy="-2780376" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3317" cy="-2780376" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2953" cy="-2778429" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2953" cy="-2778409" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3077" cy="-2779505" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3077" cy="-2779485" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3241" cy="-2779798" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3261" cy="-2779798" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="845" cy="-2778029" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="825" cy="-2778029" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4458" cy="-2777888" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4475" cy="-2777900" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="228" cy="-2777852" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="208" cy="-2777852" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1140" cy="-2777901" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1160" cy="-2777901" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5323" cy="-2781430" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5323" cy="-2781410" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="810" cy="-2777722" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="810" cy="-2777702" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="608" cy="-2777722" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="608" cy="-2777702" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="423" cy="-2777723" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="423" cy="-2777703" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4459" cy="-2779928" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4439" cy="-2779928" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4339" cy="-2779936" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4339" cy="-2779917" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="318" cy="-2779168" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="298" cy="-2779168" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4007" cy="-2778969" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3987" cy="-2778969" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4007" cy="-2779183" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3987" cy="-2779183" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3697" cy="-2779638" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3677" cy="-2779638" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4142" cy="-2779463" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4142" cy="-2779443" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3963" cy="-2779541" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3963" cy="-2779521" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3259" cy="-2779654" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3279" cy="-2779654" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3177" cy="-2779277" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3197" cy="-2779277" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3178" cy="-2779119" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3198" cy="-2779119" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-2778933" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2664" cy="-2778933" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-2778760" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2664" cy="-2778760" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4500" cy="-2779796" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4520" cy="-2779796" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4440" cy="-2780446" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4460" cy="-2780446" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4115" cy="-2779873" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4115" cy="-2779853" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3846" cy="-2780278" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3826" cy="-2780278" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3847" cy="-2780156" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3827" cy="-2780156" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3386" cy="-2780156" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3366" cy="-2780156" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3654" cy="-2780002" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3654" cy="-2779982" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="390" cy="-2778138" fill="none" r="20" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="390" cy="-2778118" fill="none" r="20" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="149" cy="-2778996" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="149" cy="-2779016" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="137" cy="-2778644" fill="none" r="20" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="137" cy="-2778664" fill="none" r="20" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1524" cy="-2780963" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1524" cy="-2780963" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1494" cy="-2780963" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1494" cy="-2780963" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="2407" cy="-2781084" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="2457" cy="-2781086" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="6425" cy="-2777771" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="6425" cy="-2777771" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="6425" cy="-2777801" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="6425" cy="-2777801" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="4938" cy="-2777746" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="4938" cy="-2777746" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="4938" cy="-2777776" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="5484" cy="-2781548" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="5484" cy="-2781578" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="845" cy="-2777841" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="845" cy="-2777871" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="477" cy="-2779182" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="477" cy="-2779157" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="6334,-2778000 6693,-2778000 6693,-2777688 6334,-2777688 6334,-2778000 " stroke="rgb(255,255,255)"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="标注、文字:0.000000 0.000000" layer11="粗线:0.000000 0.000000" layer12="35KV母线:0.000000 0.000000" layer13="10KV线路:0.000000 0.000000" layer14="10KV母线:0.000000 0.000000" layer15="35KV线路:0.000000 0.000000" layer16="虚线:0.000000 0.000000" layer17="图框（粗实线）:0.000000 0.000000" layer18="Defpoints:0.000000 0.000000" layer19="PEN4:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="0:0.000000 0.000000" layer21="主干线:0.000000 0.000000" layer22="次干线:0.000000 0.000000" layer23="支线:0.000000 0.000000" layer24="公变:0.000000 0.000000" layer25="自变:0.000000 0.000000" layer26="图框（细实线）:0.000000 0.000000" layer27="标注、文字:0.000000 0.000000" layer28="粗线:0.000000 0.000000" layer29="35KV母线:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer30="10KV线路:0.000000 0.000000" layer31="10KV母线:0.000000 0.000000" layer32="35KV线路:0.000000 0.000000" layer33="虚线:0.000000 0.000000" layer34="图框（粗实线）:0.000000 0.000000" layer35="Defpoints:0.000000 0.000000" layer36="PEN4:0.000000 0.000000" layer37="PUBLIC:0.000000 0.000000" layer4="主干线:0.000000 0.000000" layer5="次干线:0.000000 0.000000" layer6="支线:0.000000 0.000000" layer7="公变:0.000000 0.000000" layer8="自变:0.000000 0.000000" layer9="图框（细实线）:0.000000 0.000000" layerN="38"/>
</svg>