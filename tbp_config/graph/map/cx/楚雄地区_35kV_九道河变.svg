<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-289" aopId="1835526" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1165 2463 1388">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape45">
    <polyline arcFlag="1" points="19,100 17,100 15,99 14,99 12,98 11,97 9,96 8,94 7,92 7,91 6,89 6,87 6,85 7,83 7,82 8,80 9,79 11,77 12,76 14,75 15,75 17,74 19,74 21,74 23,75 24,75 26,76 27,77 29,79 30,80 31,82 31,83 32,85 32,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="100" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="87" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="voltageTransformer:shape144">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="15" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="34" x2="30" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="34" x2="30" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="27" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="33" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="41" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="30" x2="27" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="39" y2="36"/>
    <circle cx="30" cy="23" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="14" y1="25" y2="25"/>
    <circle cx="40" cy="30" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8e690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8f050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a8f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a906b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a918b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a924c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a93070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a93aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2a998a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9b530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9c180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9d060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a9d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa0a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa2570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa3a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa4f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa5a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa6aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ab5eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa8d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aa9f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aab4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1398" width="2473" x="11" y="-1170"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-240356">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40236" ObjectName="SW-CX_JDH.CX_JDH_001BK"/>
     <cge:Meas_Ref ObjectId="240356"/>
    <cge:TPSR_Ref TObjectID="40236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 676.000000 -111.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40257" ObjectName="SW-CX_JDH.CX_JDH_032BK"/>
     <cge:Meas_Ref ObjectId="240396"/>
    <cge:TPSR_Ref TObjectID="40257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240433">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1530.127932 -117.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40287" ObjectName="SW-CX_JDH.CX_JDH_012BK"/>
     <cge:Meas_Ref ObjectId="240433"/>
    <cge:TPSR_Ref TObjectID="40287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1913.286780 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40273" ObjectName="SW-CX_JDH.CX_JDH_042BK"/>
     <cge:Meas_Ref ObjectId="240416"/>
    <cge:TPSR_Ref TObjectID="40273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240411">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1775.689765 -110.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40269" ObjectName="SW-CX_JDH.CX_JDH_041BK"/>
     <cge:Meas_Ref ObjectId="240411"/>
    <cge:TPSR_Ref TObjectID="40269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240427">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2236.189765 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40281" ObjectName="SW-CX_JDH.CX_JDH_044BK"/>
     <cge:Meas_Ref ObjectId="240427"/>
    <cge:TPSR_Ref TObjectID="40281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240364">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.708955 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40242" ObjectName="SW-CX_JDH.CX_JDH_002BK"/>
     <cge:Meas_Ref ObjectId="240364"/>
    <cge:TPSR_Ref TObjectID="40242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240354">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40307" ObjectName="SW-CX_JDH.CX_JDH_301BK"/>
     <cge:Meas_Ref ObjectId="240354"/>
    <cge:TPSR_Ref TObjectID="40307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240371">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40245" ObjectName="SW-CX_JDH.CX_JDH_371BK"/>
     <cge:Meas_Ref ObjectId="240371"/>
    <cge:TPSR_Ref TObjectID="40245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240362">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.708955 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40239" ObjectName="SW-CX_JDH.CX_JDH_302BK"/>
     <cge:Meas_Ref ObjectId="240362"/>
    <cge:TPSR_Ref TObjectID="40239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1113.000000 -109.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40265" ObjectName="SW-CX_JDH.CX_JDH_035BK"/>
     <cge:Meas_Ref ObjectId="240406"/>
    <cge:TPSR_Ref TObjectID="40265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240390">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 523.000000 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40251" ObjectName="SW-CX_JDH.CX_JDH_031BK"/>
     <cge:Meas_Ref ObjectId="240390"/>
    <cge:TPSR_Ref TObjectID="40251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240401">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40261" ObjectName="SW-CX_JDH.CX_JDH_033BK"/>
     <cge:Meas_Ref ObjectId="240401"/>
    <cge:TPSR_Ref TObjectID="40261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2074.286780 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40277" ObjectName="SW-CX_JDH.CX_JDH_043BK"/>
     <cge:Meas_Ref ObjectId="240421"/>
    <cge:TPSR_Ref TObjectID="40277"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d57d50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1376.000000 -862.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c770e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -463.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247d630">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1598.089552 39.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343cac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1240.589552 45.500000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_JDH.CX_JDH_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2226.189765 180.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40316" ObjectName="CB-CX_JDH.CX_JDH_Cb2"/>
    <cge:TPSR_Ref TObjectID="40316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_JDH.CX_JDH_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 180.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40315" ObjectName="CB-CX_JDH.CX_JDH_Cb1"/>
    <cge:TPSR_Ref TObjectID="40315"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_JDH.CX_JDH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="60833"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1245.000000 -378.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1245.000000 -378.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="40301" ObjectName="TF-CX_JDH.CX_JDH_1T"/>
    <cge:TPSR_Ref TObjectID="40301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_JDH.CX_JDH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="60837"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.708955 -376.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.708955 -376.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="40302" ObjectName="TF-CX_JDH.CX_JDH_2T"/>
    <cge:TPSR_Ref TObjectID="40302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2395.892324 61.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2395.892324 61.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.892324 74.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.892324 74.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d33280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1501.000000 -803.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c75480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -517.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4ce10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1385.000000 -564.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27c31d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -8.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3186d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4f7f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1917.286780 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4a340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1889.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3cdf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.689765 -7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d29c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1749.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce4660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2383.892324 -7.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce66f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2240.189765 -9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cede60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -864.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e1dd50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.000000 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34647e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 -25.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3469130">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2198.000000 81.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3480040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 84.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3480e40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 -27.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3481b20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1624.089552 -34.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34824f0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 1.000000 1574.910448 -40.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24808a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -474.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24812c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248edb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1386.000000 -1005.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2490920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 496.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2491650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2494910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24987e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 800.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a0fd0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1285.410448 -27.500000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a16f0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1220.410448 -39.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3448880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2078.286780 -9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_344c750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3457460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2199.892324 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3458190">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2275.892324 112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3458ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2418.089552 -117.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3460480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 959.089552 -121.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0d8b0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 491.500000 177.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0e450">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2213.500000 177.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0eff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -314.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0fed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.000000 -309.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40257"/>
     <cge:Term_Ref ObjectID="60743"/>
    <cge:TPSR_Ref TObjectID="40257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40257"/>
     <cge:Term_Ref ObjectID="60743"/>
    <cge:TPSR_Ref TObjectID="40257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40257"/>
     <cge:Term_Ref ObjectID="60743"/>
    <cge:TPSR_Ref TObjectID="40257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 869.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40261"/>
     <cge:Term_Ref ObjectID="60751"/>
    <cge:TPSR_Ref TObjectID="40261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 869.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40261"/>
     <cge:Term_Ref ObjectID="60751"/>
    <cge:TPSR_Ref TObjectID="40261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 869.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40261"/>
     <cge:Term_Ref ObjectID="60751"/>
    <cge:TPSR_Ref TObjectID="40261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40265"/>
     <cge:Term_Ref ObjectID="60759"/>
    <cge:TPSR_Ref TObjectID="40265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40265"/>
     <cge:Term_Ref ObjectID="60759"/>
    <cge:TPSR_Ref TObjectID="40265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40265"/>
     <cge:Term_Ref ObjectID="60759"/>
    <cge:TPSR_Ref TObjectID="40265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1781.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40269"/>
     <cge:Term_Ref ObjectID="60767"/>
    <cge:TPSR_Ref TObjectID="40269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1781.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40269"/>
     <cge:Term_Ref ObjectID="60767"/>
    <cge:TPSR_Ref TObjectID="40269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1781.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40269"/>
     <cge:Term_Ref ObjectID="60767"/>
    <cge:TPSR_Ref TObjectID="40269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1944.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40273"/>
     <cge:Term_Ref ObjectID="60775"/>
    <cge:TPSR_Ref TObjectID="40273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1944.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40273"/>
     <cge:Term_Ref ObjectID="60775"/>
    <cge:TPSR_Ref TObjectID="40273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1944.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40273"/>
     <cge:Term_Ref ObjectID="60775"/>
    <cge:TPSR_Ref TObjectID="40273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2092.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40277"/>
     <cge:Term_Ref ObjectID="60783"/>
    <cge:TPSR_Ref TObjectID="40277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2092.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40277"/>
     <cge:Term_Ref ObjectID="60783"/>
    <cge:TPSR_Ref TObjectID="40277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2092.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40277"/>
     <cge:Term_Ref ObjectID="60783"/>
    <cge:TPSR_Ref TObjectID="40277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2350.000000 143.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40281"/>
     <cge:Term_Ref ObjectID="60791"/>
    <cge:TPSR_Ref TObjectID="40281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2350.000000 143.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40281"/>
     <cge:Term_Ref ObjectID="60791"/>
    <cge:TPSR_Ref TObjectID="40281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 461.000000 -168.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40251"/>
     <cge:Term_Ref ObjectID="60731"/>
    <cge:TPSR_Ref TObjectID="40251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 461.000000 -168.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40251"/>
     <cge:Term_Ref ObjectID="60731"/>
    <cge:TPSR_Ref TObjectID="40251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-240535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -292.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40305"/>
     <cge:Term_Ref ObjectID="60840"/>
    <cge:TPSR_Ref TObjectID="40305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-240536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -292.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40305"/>
     <cge:Term_Ref ObjectID="60840"/>
    <cge:TPSR_Ref TObjectID="40305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-240537" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -292.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40305"/>
     <cge:Term_Ref ObjectID="60840"/>
    <cge:TPSR_Ref TObjectID="40305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-240541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -292.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40305"/>
     <cge:Term_Ref ObjectID="60840"/>
    <cge:TPSR_Ref TObjectID="40305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-240538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -292.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40305"/>
     <cge:Term_Ref ObjectID="60840"/>
    <cge:TPSR_Ref TObjectID="40305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-240542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -294.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40306"/>
     <cge:Term_Ref ObjectID="60841"/>
    <cge:TPSR_Ref TObjectID="40306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-240543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -294.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40306"/>
     <cge:Term_Ref ObjectID="60841"/>
    <cge:TPSR_Ref TObjectID="40306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-240544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -294.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40306"/>
     <cge:Term_Ref ObjectID="60841"/>
    <cge:TPSR_Ref TObjectID="40306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-240548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -294.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40306"/>
     <cge:Term_Ref ObjectID="60841"/>
    <cge:TPSR_Ref TObjectID="40306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-240545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2398.000000 -294.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40306"/>
     <cge:Term_Ref ObjectID="60841"/>
    <cge:TPSR_Ref TObjectID="40306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 -281.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40236"/>
     <cge:Term_Ref ObjectID="60701"/>
    <cge:TPSR_Ref TObjectID="40236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 -281.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40236"/>
     <cge:Term_Ref ObjectID="60701"/>
    <cge:TPSR_Ref TObjectID="40236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 -281.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40236"/>
     <cge:Term_Ref ObjectID="60701"/>
    <cge:TPSR_Ref TObjectID="40236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1580.000000 -281.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40242"/>
     <cge:Term_Ref ObjectID="60713"/>
    <cge:TPSR_Ref TObjectID="40242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1580.000000 -281.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40242"/>
     <cge:Term_Ref ObjectID="60713"/>
    <cge:TPSR_Ref TObjectID="40242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1580.000000 -281.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40242"/>
     <cge:Term_Ref ObjectID="60713"/>
    <cge:TPSR_Ref TObjectID="40242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -776.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40245"/>
     <cge:Term_Ref ObjectID="60719"/>
    <cge:TPSR_Ref TObjectID="40245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -776.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40245"/>
     <cge:Term_Ref ObjectID="60719"/>
    <cge:TPSR_Ref TObjectID="40245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -776.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40245"/>
     <cge:Term_Ref ObjectID="60719"/>
    <cge:TPSR_Ref TObjectID="40245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 -624.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40239"/>
     <cge:Term_Ref ObjectID="60707"/>
    <cge:TPSR_Ref TObjectID="40239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240500" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 -624.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240500" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40239"/>
     <cge:Term_Ref ObjectID="60707"/>
    <cge:TPSR_Ref TObjectID="40239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 -624.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40239"/>
     <cge:Term_Ref ObjectID="60707"/>
    <cge:TPSR_Ref TObjectID="40239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 -624.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40307"/>
     <cge:Term_Ref ObjectID="60842"/>
    <cge:TPSR_Ref TObjectID="40307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 -624.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40307"/>
     <cge:Term_Ref ObjectID="60842"/>
    <cge:TPSR_Ref TObjectID="40307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 -624.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40307"/>
     <cge:Term_Ref ObjectID="60842"/>
    <cge:TPSR_Ref TObjectID="40307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-240528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -762.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40304"/>
     <cge:Term_Ref ObjectID="60839"/>
    <cge:TPSR_Ref TObjectID="40304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-240529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -762.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40304"/>
     <cge:Term_Ref ObjectID="60839"/>
    <cge:TPSR_Ref TObjectID="40304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-240530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -762.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40304"/>
     <cge:Term_Ref ObjectID="60839"/>
    <cge:TPSR_Ref TObjectID="40304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-240534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -762.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40304"/>
     <cge:Term_Ref ObjectID="60839"/>
    <cge:TPSR_Ref TObjectID="40304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-240531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -762.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40304"/>
     <cge:Term_Ref ObjectID="60839"/>
    <cge:TPSR_Ref TObjectID="40304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-240489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1139.000000 -480.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40301"/>
     <cge:Term_Ref ObjectID="60834"/>
    <cge:TPSR_Ref TObjectID="40301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-240488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1139.000000 -480.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40301"/>
     <cge:Term_Ref ObjectID="60834"/>
    <cge:TPSR_Ref TObjectID="40301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-240552" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.000000 -70.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40287"/>
     <cge:Term_Ref ObjectID="60803"/>
    <cge:TPSR_Ref TObjectID="40287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-240553" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.000000 -70.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40287"/>
     <cge:Term_Ref ObjectID="60803"/>
    <cge:TPSR_Ref TObjectID="40287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-240549" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.000000 -70.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40287"/>
     <cge:Term_Ref ObjectID="60803"/>
    <cge:TPSR_Ref TObjectID="40287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-240515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 -480.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40302"/>
     <cge:Term_Ref ObjectID="60838"/>
    <cge:TPSR_Ref TObjectID="40302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-240514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 -480.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40302"/>
     <cge:Term_Ref ObjectID="60838"/>
    <cge:TPSR_Ref TObjectID="40302"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-240372">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -980.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40246" ObjectName="SW-CX_JDH.CX_JDH_3716SW"/>
     <cge:Meas_Ref ObjectId="240372"/>
    <cge:TPSR_Ref TObjectID="40246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240375">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1454.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40250" ObjectName="SW-CX_JDH.CX_JDH_37167SW"/>
     <cge:Meas_Ref ObjectId="240375"/>
    <cge:TPSR_Ref TObjectID="40250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240374">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40249" ObjectName="SW-CX_JDH.CX_JDH_37160SW"/>
     <cge:Meas_Ref ObjectId="240374"/>
    <cge:TPSR_Ref TObjectID="40249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240357">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -215.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40237" ObjectName="SW-CX_JDH.CX_JDH_001XC"/>
     <cge:Meas_Ref ObjectId="240357"/>
    <cge:TPSR_Ref TObjectID="40237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240357">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -280.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40238" ObjectName="SW-CX_JDH.CX_JDH_001XC1"/>
     <cge:Meas_Ref ObjectId="240357"/>
    <cge:TPSR_Ref TObjectID="40238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240398">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40260" ObjectName="SW-CX_JDH.CX_JDH_03260SW"/>
     <cge:Meas_Ref ObjectId="240398"/>
    <cge:TPSR_Ref TObjectID="40260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 675.000000 -150.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40258" ObjectName="SW-CX_JDH.CX_JDH_032XC"/>
     <cge:Meas_Ref ObjectId="240397"/>
    <cge:TPSR_Ref TObjectID="40258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 675.000000 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40259" ObjectName="SW-CX_JDH.CX_JDH_032XC1"/>
     <cge:Meas_Ref ObjectId="240397"/>
    <cge:TPSR_Ref TObjectID="40259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240434">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.127932 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40289" ObjectName="SW-CX_JDH.CX_JDH_012XC1"/>
     <cge:Meas_Ref ObjectId="240434"/>
    <cge:TPSR_Ref TObjectID="40289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.127932 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40290" ObjectName="SW-CX_JDH.CX_JDH_0121SW"/>
     <cge:Meas_Ref ObjectId="240435"/>
    <cge:TPSR_Ref TObjectID="40290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.127932 -98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40290" ObjectName="SW-CX_JDH.CX_JDH_0121SW"/>
     <cge:Meas_Ref ObjectId="240435"/>
    <cge:TPSR_Ref TObjectID="40290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240434">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.127932 -156.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40288" ObjectName="SW-CX_JDH.CX_JDH_012XC"/>
     <cge:Meas_Ref ObjectId="240434"/>
    <cge:TPSR_Ref TObjectID="40288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240418">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40276" ObjectName="SW-CX_JDH.CX_JDH_04260SW"/>
     <cge:Meas_Ref ObjectId="240418"/>
    <cge:TPSR_Ref TObjectID="40276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240417">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1912.286780 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40274" ObjectName="SW-CX_JDH.CX_JDH_042XC"/>
     <cge:Meas_Ref ObjectId="240417"/>
    <cge:TPSR_Ref TObjectID="40274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240417">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1912.286780 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40275" ObjectName="SW-CX_JDH.CX_JDH_042XC1"/>
     <cge:Meas_Ref ObjectId="240417"/>
    <cge:TPSR_Ref TObjectID="40275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40272" ObjectName="SW-CX_JDH.CX_JDH_04160SW"/>
     <cge:Meas_Ref ObjectId="240413"/>
    <cge:TPSR_Ref TObjectID="40272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.689765 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40270" ObjectName="SW-CX_JDH.CX_JDH_041XC"/>
     <cge:Meas_Ref ObjectId="240412"/>
    <cge:TPSR_Ref TObjectID="40270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.689765 -84.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40271" ObjectName="SW-CX_JDH.CX_JDH_041XC1"/>
     <cge:Meas_Ref ObjectId="240412"/>
    <cge:TPSR_Ref TObjectID="40271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2416.892324 -158.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40299" ObjectName="SW-CX_JDH.CX_JDH_0451XC"/>
     <cge:Meas_Ref ObjectId="240452"/>
    <cge:TPSR_Ref TObjectID="40299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2416.892324 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40300" ObjectName="SW-CX_JDH.CX_JDH_0451XC1"/>
     <cge:Meas_Ref ObjectId="240452"/>
    <cge:TPSR_Ref TObjectID="40300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2284.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40284" ObjectName="SW-CX_JDH.CX_JDH_04460SW"/>
     <cge:Meas_Ref ObjectId="240429"/>
    <cge:TPSR_Ref TObjectID="40284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.189765 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40282" ObjectName="SW-CX_JDH.CX_JDH_044XC"/>
     <cge:Meas_Ref ObjectId="240428"/>
    <cge:TPSR_Ref TObjectID="40282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2195.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40286" ObjectName="SW-CX_JDH.CX_JDH_04467SW"/>
     <cge:Meas_Ref ObjectId="240431"/>
    <cge:TPSR_Ref TObjectID="40286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2236.189765 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40285" ObjectName="SW-CX_JDH.CX_JDH_0446SW"/>
     <cge:Meas_Ref ObjectId="240430"/>
    <cge:TPSR_Ref TObjectID="40285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.189765 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40283" ObjectName="SW-CX_JDH.CX_JDH_044XC1"/>
     <cge:Meas_Ref ObjectId="240428"/>
    <cge:TPSR_Ref TObjectID="40283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240365">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -214.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40243" ObjectName="SW-CX_JDH.CX_JDH_002XC"/>
     <cge:Meas_Ref ObjectId="240365"/>
    <cge:TPSR_Ref TObjectID="40243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240365">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -279.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40244" ObjectName="SW-CX_JDH.CX_JDH_002XC1"/>
     <cge:Meas_Ref ObjectId="240365"/>
    <cge:TPSR_Ref TObjectID="40244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240355">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40235" ObjectName="SW-CX_JDH.CX_JDH_301XC1"/>
     <cge:Meas_Ref ObjectId="240355"/>
    <cge:TPSR_Ref TObjectID="40235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240355">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40234" ObjectName="SW-CX_JDH.CX_JDH_301XC"/>
     <cge:Meas_Ref ObjectId="240355"/>
    <cge:TPSR_Ref TObjectID="40234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240373">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -726.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40247" ObjectName="SW-CX_JDH.CX_JDH_371XC"/>
     <cge:Meas_Ref ObjectId="240373"/>
    <cge:TPSR_Ref TObjectID="40247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240373">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -794.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40248" ObjectName="SW-CX_JDH.CX_JDH_371XC1"/>
     <cge:Meas_Ref ObjectId="240373"/>
    <cge:TPSR_Ref TObjectID="40248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40241" ObjectName="SW-CX_JDH.CX_JDH_302XC1"/>
     <cge:Meas_Ref ObjectId="240363"/>
    <cge:TPSR_Ref TObjectID="40241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40240" ObjectName="SW-CX_JDH.CX_JDH_302XC"/>
     <cge:Meas_Ref ObjectId="240363"/>
    <cge:TPSR_Ref TObjectID="40240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40268" ObjectName="SW-CX_JDH.CX_JDH_03560SW"/>
     <cge:Meas_Ref ObjectId="240408"/>
    <cge:TPSR_Ref TObjectID="40268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 -148.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40266" ObjectName="SW-CX_JDH.CX_JDH_035XC"/>
     <cge:Meas_Ref ObjectId="240407"/>
    <cge:TPSR_Ref TObjectID="40266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 -83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40267" ObjectName="SW-CX_JDH.CX_JDH_035XC1"/>
     <cge:Meas_Ref ObjectId="240407"/>
    <cge:TPSR_Ref TObjectID="40267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240392">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40254" ObjectName="SW-CX_JDH.CX_JDH_03160SW"/>
     <cge:Meas_Ref ObjectId="240392"/>
    <cge:TPSR_Ref TObjectID="40254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40252" ObjectName="SW-CX_JDH.CX_JDH_031XC"/>
     <cge:Meas_Ref ObjectId="240391"/>
    <cge:TPSR_Ref TObjectID="40252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240394">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 469.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40256" ObjectName="SW-CX_JDH.CX_JDH_03167SW"/>
     <cge:Meas_Ref ObjectId="240394"/>
    <cge:TPSR_Ref TObjectID="40256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 523.000000 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40255" ObjectName="SW-CX_JDH.CX_JDH_0316SW"/>
     <cge:Meas_Ref ObjectId="240393"/>
    <cge:TPSR_Ref TObjectID="40255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40253" ObjectName="SW-CX_JDH.CX_JDH_031XC1"/>
     <cge:Meas_Ref ObjectId="240391"/>
    <cge:TPSR_Ref TObjectID="40253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1622.892324 -102.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40296" ObjectName="SW-CX_JDH.CX_JDH_0902XC1"/>
     <cge:Meas_Ref ObjectId="240443"/>
    <cge:TPSR_Ref TObjectID="40296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1622.892324 -149.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40295" ObjectName="SW-CX_JDH.CX_JDH_0902XC"/>
     <cge:Meas_Ref ObjectId="240443"/>
    <cge:TPSR_Ref TObjectID="40295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240441">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -629.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40291" ObjectName="SW-CX_JDH.CX_JDH_3901XC"/>
     <cge:Meas_Ref ObjectId="240441"/>
    <cge:TPSR_Ref TObjectID="40291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240441">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -582.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40292" ObjectName="SW-CX_JDH.CX_JDH_3901XC1"/>
     <cge:Meas_Ref ObjectId="240441"/>
    <cge:TPSR_Ref TObjectID="40292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240403">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40264" ObjectName="SW-CX_JDH.CX_JDH_03360SW"/>
     <cge:Meas_Ref ObjectId="240403"/>
    <cge:TPSR_Ref TObjectID="40264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40262" ObjectName="SW-CX_JDH.CX_JDH_033XC"/>
     <cge:Meas_Ref ObjectId="240402"/>
    <cge:TPSR_Ref TObjectID="40262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40263" ObjectName="SW-CX_JDH.CX_JDH_033XC1"/>
     <cge:Meas_Ref ObjectId="240402"/>
    <cge:TPSR_Ref TObjectID="40263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240442">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1285.607676 -97.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40294" ObjectName="SW-CX_JDH.CX_JDH_0901XC1"/>
     <cge:Meas_Ref ObjectId="240442"/>
    <cge:TPSR_Ref TObjectID="40294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240442">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1285.607676 -144.500000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40293" ObjectName="SW-CX_JDH.CX_JDH_0901XC"/>
     <cge:Meas_Ref ObjectId="240442"/>
    <cge:TPSR_Ref TObjectID="40293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240423">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2115.000000 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40280" ObjectName="SW-CX_JDH.CX_JDH_04360SW"/>
     <cge:Meas_Ref ObjectId="240423"/>
    <cge:TPSR_Ref TObjectID="40280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240422">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.286780 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40278" ObjectName="SW-CX_JDH.CX_JDH_043XC"/>
     <cge:Meas_Ref ObjectId="240422"/>
    <cge:TPSR_Ref TObjectID="40278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240422">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.286780 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40279" ObjectName="SW-CX_JDH.CX_JDH_043XC1"/>
     <cge:Meas_Ref ObjectId="240422"/>
    <cge:TPSR_Ref TObjectID="40279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.892324 -162.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40297" ObjectName="SW-CX_JDH.CX_JDH_0341XC"/>
     <cge:Meas_Ref ObjectId="240451"/>
    <cge:TPSR_Ref TObjectID="40297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.892324 -92.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40298" ObjectName="SW-CX_JDH.CX_JDH_0341XC1"/>
     <cge:Meas_Ref ObjectId="240451"/>
    <cge:TPSR_Ref TObjectID="40298"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -1111.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1117.000000 107.277778)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.689765 114.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1917.286780 108.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 107.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2078.286780 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2423.000000 113.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.000000 127.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_34d7890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-648 1276,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40234@0" ObjectIDZND0="40304@0" Pin0InfoVect0LinkObjId="g_2d61480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-648 1276,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2931180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-971 1508,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40249@1" ObjectIDZND0="g_2c11430@0" Pin0InfoVect0LinkObjId="g_2c11430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-971 1508,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c12180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1495,-1043 1508,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40250@1" ObjectIDZND0="g_2d608f0@0" Pin0InfoVect0LinkObjId="g_2d608f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240375_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1495,-1043 1508,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d61480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-653 1441,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40291@0" ObjectIDZND0="40304@0" Pin0InfoVect0LinkObjId="g_34d7890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240441_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-653 1441,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cbfe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-276 1276,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40236@1" ObjectIDZND0="40238@1" Pin0InfoVect0LinkObjId="SW-240357_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-276 1276,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cbe480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-239 1276,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40237@1" ObjectIDZND0="40236@0" Pin0InfoVect0LinkObjId="SW-240356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-239 1276,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb8cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-222 1276,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40237@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_34d0880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-222 1276,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c10320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-146 685,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40257@1" ObjectIDZND0="40258@1" Pin0InfoVect0LinkObjId="SW-240397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="685,-146 685,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce79f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-109 685,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40259@1" ObjectIDZND0="40257@0" Pin0InfoVect0LinkObjId="SW-240396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240397_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="685,-109 685,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d04310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-36 719,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28708d0@0" ObjectIDZND0="40260@0" Pin0InfoVect0LinkObjId="SW-240398_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28708d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-36 719,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d0880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-174 685,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40258@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_2bb8cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="685,-174 685,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cbef10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-92 685,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40259@0" ObjectIDZND0="g_27c31d0@1" Pin0InfoVect0LinkObjId="g_27c31d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="685,-92 685,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c83ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-152 1539,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40287@1" ObjectIDZND0="40288@1" Pin0InfoVect0LinkObjId="SW-240434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-152 1539,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd00f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-115 1539,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40289@1" ObjectIDZND0="40287@0" Pin0InfoVect0LinkObjId="SW-240433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-115 1539,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb67c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-180 1539,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40288@0" ObjectIDZND0="40306@0" Pin0InfoVect0LinkObjId="g_2d30a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-180 1539,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d4aca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-173 1384,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40290@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_2bb8cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-173 1384,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_319fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-105 1384,-81 1539,-81 1539,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40290@0" ObjectIDZND0="40289@0" Pin0InfoVect0LinkObjId="SW-240434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-105 1384,-81 1539,-81 1539,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce75c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-156 1384,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40290@1" ObjectIDZND0="40290@1" Pin0InfoVect0LinkObjId="SW-240435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-156 1384,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28760a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,-38 1963,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c2f160@0" ObjectIDZND0="40276@0" Pin0InfoVect0LinkObjId="SW-240418_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c2f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1963,-38 1963,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d30a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-176 1922,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40274@0" ObjectIDZND0="40306@0" Pin0InfoVect0LinkObjId="g_2bb67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-176 1922,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d41460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1896,-83 1963,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d4a340@0" ObjectIDZND0="40276@1" Pin0InfoVect0LinkObjId="SW-240418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d4a340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1896,-83 1963,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccd190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-94 1922,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40275@0" ObjectIDZND0="g_2d4f7f0@1" Pin0InfoVect0LinkObjId="g_2d4f7f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-94 1922,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d3b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-148 1922,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40273@1" ObjectIDZND0="40274@1" Pin0InfoVect0LinkObjId="SW-240417_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-148 1922,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c126b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-111 1922,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40275@1" ObjectIDZND0="40273@0" Pin0InfoVect0LinkObjId="SW-240416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-111 1922,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d084e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1823,-35 1823,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d08d50@0" ObjectIDZND0="40272@0" Pin0InfoVect0LinkObjId="SW-240413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d08d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1823,-35 1823,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-173 1785,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40270@0" ObjectIDZND0="40306@0" Pin0InfoVect0LinkObjId="g_2bb67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-173 1785,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7b450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1756,-80 1823,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34d29c0@0" ObjectIDZND0="40272@1" Pin0InfoVect0LinkObjId="SW-240413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d29c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1756,-80 1823,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d7ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-91 1785,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40271@0" ObjectIDZND0="g_2d3cdf0@1" Pin0InfoVect0LinkObjId="g_2d3cdf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-91 1785,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccb130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-145 1785,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40269@1" ObjectIDZND0="40270@1" Pin0InfoVect0LinkObjId="SW-240412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-145 1785,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc1450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-108 1785,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40271@1" ObjectIDZND0="40269@0" Pin0InfoVect0LinkObjId="SW-240411_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-108 1785,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce3680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-147 2245,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40281@1" ObjectIDZND0="40282@1" Pin0InfoVect0LinkObjId="SW-240428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240427_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-147 2245,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d3a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-110 2245,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40283@1" ObjectIDZND0="40281@0" Pin0InfoVect0LinkObjId="SW-240427_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240428_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-110 2245,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb48a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2293,-38 2293,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c7d6c0@0" ObjectIDZND0="40284@0" Pin0InfoVect0LinkObjId="SW-240429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7d6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2293,-38 2293,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d57930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-14 2245,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ce66f0@0" ObjectIDZND0="40285@1" Pin0InfoVect0LinkObjId="SW-240430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce66f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-14 2245,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1b3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-175 2245,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40282@0" ObjectIDZND0="40306@0" Pin0InfoVect0LinkObjId="g_2bb67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-175 2245,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d3ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2228,5 2228,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c74b00@0" ObjectIDZND0="40286@1" Pin0InfoVect0LinkObjId="SW-240431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c74b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2228,5 2228,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5c980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-93 2245,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40283@0" ObjectIDZND0="g_2ce66f0@1" Pin0InfoVect0LinkObjId="g_2ce66f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-93 2245,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-275 1633,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40242@1" ObjectIDZND0="40244@1" Pin0InfoVect0LinkObjId="SW-240365_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240364_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-275 1633,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-238 1633,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40243@1" ObjectIDZND0="40242@0" Pin0InfoVect0LinkObjId="SW-240364_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240365_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-238 1633,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c76bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-732 1441,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40247@0" ObjectIDZND0="40304@0" Pin0InfoVect0LinkObjId="g_34d7890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-732 1441,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d5d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1508,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cede60@0" ObjectIDND1="g_2d57d50@0" ObjectIDND2="40248@x" ObjectIDZND0="g_2d33280@0" Pin0InfoVect0LinkObjId="g_2d33280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cede60_0" Pin1InfoVect1LinkObjId="g_2d57d50_0" Pin1InfoVect2LinkObjId="SW-240373_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1508,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d5d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-869 1441,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_2cede60@0" ObjectIDZND0="g_2d33280@0" ObjectIDZND1="g_2d57d50@0" ObjectIDZND2="40248@x" Pin0InfoVect0LinkObjId="g_2d33280_0" Pin0InfoVect1LinkObjId="g_2d57d50_0" Pin0InfoVect2LinkObjId="SW-240373_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cede60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-869 1441,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d5d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1447,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1447,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d5dbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1392,-572 1392,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="40292@x" ObjectIDND1="g_2c75480@0" ObjectIDZND0="g_2d4ce10@0" Pin0InfoVect0LinkObjId="g_2d4ce10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240441_0" Pin1InfoVect1LinkObjId="g_2c75480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1392,-572 1392,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d5de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-588 1441,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40292@0" ObjectIDZND0="g_2d4ce10@0" ObjectIDZND1="g_2c75480@0" Pin0InfoVect0LinkObjId="g_2d4ce10_0" Pin0InfoVect1LinkObjId="g_2c75480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240441_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-588 1441,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c76e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1441,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d4ce10@0" ObjectIDND1="40292@x" ObjectIDZND0="g_2c75480@0" Pin0InfoVect0LinkObjId="g_2c75480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d4ce10_0" Pin1InfoVect1LinkObjId="SW-240441_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1441,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cfd2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-522 1441,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2c75480@1" ObjectIDZND0="g_2c770e0@0" Pin0InfoVect0LinkObjId="g_2c770e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c75480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-522 1441,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c71850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-617 1276,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40307@1" ObjectIDZND0="40234@1" Pin0InfoVect0LinkObjId="SW-240355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240354_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-617 1276,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c71ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-580 1276,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40235@1" ObjectIDZND0="40307@0" Pin0InfoVect0LinkObjId="SW-240354_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240355_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-580 1276,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ceabb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-787 1441,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40245@1" ObjectIDZND0="40248@1" Pin0InfoVect0LinkObjId="SW-240373_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-787 1441,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ceae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-750 1441,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40247@1" ObjectIDZND0="40245@0" Pin0InfoVect0LinkObjId="SW-240371_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-750 1441,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ced9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cedc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cee740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-971 1441,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="40246@x" ObjectIDND1="40249@x" ObjectIDZND0="g_2cede60@1" Pin0InfoVect0LinkObjId="g_2cede60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240372_0" Pin1InfoVect1LinkObjId="SW-240374_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-971 1441,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cee9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-985 1441,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="40246@0" ObjectIDZND0="g_2cede60@0" ObjectIDZND1="40249@x" Pin0InfoVect0LinkObjId="g_2cede60_0" Pin0InfoVect1LinkObjId="SW-240374_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-985 1441,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ceec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-971 1458,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="40246@x" ObjectIDND1="g_2cede60@0" ObjectIDZND0="40249@0" Pin0InfoVect0LinkObjId="SW-240374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240372_0" Pin1InfoVect1LinkObjId="g_2cede60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-971 1458,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ceee60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-563 1276,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40235@0" ObjectIDZND0="g_24808a0@1" Pin0InfoVect0LinkObjId="g_24808a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-563 1276,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cef0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-479 1276,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_24808a0@0" ObjectIDZND0="40301@1" Pin0InfoVect0LinkObjId="g_2d0fc70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24808a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-479 1276,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e14030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-617 1633,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40239@1" ObjectIDZND0="40240@1" Pin0InfoVect0LinkObjId="SW-240363_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-617 1633,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e14290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-580 1633,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40241@1" ObjectIDZND0="40239@0" Pin0InfoVect0LinkObjId="SW-240362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-580 1633,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e16d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1669,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1669,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e16fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-680 1633,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40304@0" ObjectIDZND0="40240@0" Pin0InfoVect0LinkObjId="SW-240363_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d7890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-680 1633,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e17210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1592,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e17470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-481 1633,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_24812c0@0" ObjectIDZND0="40302@1" Pin0InfoVect0LinkObjId="g_2d10b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24812c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-481 1633,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e176d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-563 1633,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40241@0" ObjectIDZND0="g_24812c0@1" Pin0InfoVect0LinkObjId="g_24812c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-563 1633,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e18350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,88 685,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_27c31d0@0" Pin0InfoVect0LinkObjId="g_27c31d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d57d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="685,88 685,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e185b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-186 968,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40297@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_2bb8cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="968,-186 968,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e18810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="652,-81 719,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3186d70@0" ObjectIDZND0="40260@1" Pin0InfoVect0LinkObjId="SW-240398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3186d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="652,-81 719,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e1a660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-144 1122,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40265@1" ObjectIDZND0="40266@1" Pin0InfoVect0LinkObjId="SW-240407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-144 1122,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e1a8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-107 1122,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40267@1" ObjectIDZND0="40265@0" Pin0InfoVect0LinkObjId="SW-240406_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-107 1122,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e1daf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-34 1156,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e1d100@0" ObjectIDZND0="40268@0" Pin0InfoVect0LinkObjId="SW-240408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1d100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-34 1156,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e1e9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-172 1122,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40266@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_2bb8cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-172 1122,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e214e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-90 1122,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40267@0" ObjectIDZND0="g_34647e0@1" Pin0InfoVect0LinkObjId="g_34647e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-90 1122,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3464580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1089,-79 1156,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e1dd50@0" ObjectIDZND0="40268@1" Pin0InfoVect0LinkObjId="SW-240408_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1dd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1089,-79 1156,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3464e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-30 1122,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_34647e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2d57d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34647e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-30 1122,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3465b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-12 1785,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2d3cdf0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2d57d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d3cdf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-12 1785,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3466780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-15 1922,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2d4f7f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2d57d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d4f7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-15 1922,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34669e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2391,-61 2427,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_2ce4660@0" ObjectIDZND0="40300@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-240452_0" Pin0InfoVect1LinkObjId="g_2d57d50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce4660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2391,-61 2427,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3466c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-95 2427,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="40300@0" ObjectIDZND0="g_2ce4660@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2ce4660_0" Pin0InfoVect1LinkObjId="g_2d57d50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-95 2427,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3466ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-61 2427,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="40300@x" ObjectIDND1="g_2ce4660@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2d57d50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240452_0" Pin1InfoVect1LinkObjId="g_2ce4660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-61 2427,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3469770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,49 2245,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="40285@0" ObjectIDZND0="40316@0" Pin0InfoVect0LinkObjId="CB-CX_JDH.CX_JDH_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,49 2245,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34699d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2283,58 2229,58 2228,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3458190@0" ObjectIDZND0="40286@0" Pin0InfoVect0LinkObjId="SW-240431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3458190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2283,58 2229,58 2228,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3469c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,50 2204,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3469130@0" Pin0InfoVect0LinkObjId="g_3469130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2204,50 2204,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3469e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2207,-80 2293,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3457460@0" ObjectIDZND0="40284@1" Pin0InfoVect0LinkObjId="SW-240429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3457460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-80 2293,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-147 532,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40251@1" ObjectIDZND0="40252@1" Pin0InfoVect0LinkObjId="SW-240391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,-147 532,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-110 532,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40253@1" ObjectIDZND0="40251@0" Pin0InfoVect0LinkObjId="SW-240390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,-110 532,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346f160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-38 567,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_346e790@0" ObjectIDZND0="40254@0" Pin0InfoVect0LinkObjId="SW-240392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_346e790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,-38 567,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-175 532,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40252@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_2bb8cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,-175 532,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34780f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,5 502,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3476bd0@0" ObjectIDZND0="40256@1" Pin0InfoVect0LinkObjId="SW-240394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3476bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="502,5 502,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3480720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,49 532,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="40255@0" ObjectIDZND0="40315@0" Pin0InfoVect0LinkObjId="CB-CX_JDH.CX_JDH_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,49 532,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3480980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="557,58 503,58 502,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2491650@0" ObjectIDZND0="40256@0" Pin0InfoVect0LinkObjId="SW-240394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2491650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="557,58 503,58 502,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3480be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="503,-80 567,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2490920@0" ObjectIDZND0="40254@1" Pin0InfoVect0LinkObjId="SW-240392_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2490920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="503,-80 567,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34818c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-93 532,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40253@0" ObjectIDZND0="g_3480e40@1" Pin0InfoVect0LinkObjId="g_3480e40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,-93 532,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2480180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,44 1633,3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_247d630@0" ObjectIDZND0="g_3481b20@0" Pin0InfoVect0LinkObjId="g_3481b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247d630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,44 1633,3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24803e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1632,-54 1568,-54 1568,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3481b20@0" ObjectIDZND0="g_34824f0@0" Pin0InfoVect0LinkObjId="g_34824f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3481b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1632,-54 1568,-54 1568,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-205 1633,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40306@0" ObjectIDZND0="40295@0" Pin0InfoVect0LinkObjId="SW-240443_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb67c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-205 1633,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2481ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-221 1633,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40243@0" ObjectIDZND0="40306@0" Pin0InfoVect0LinkObjId="g_2bb67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-221 1633,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2481f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1020 1441,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="40246@1" ObjectIDZND0="40250@x" ObjectIDZND1="0@x" ObjectIDZND2="g_248edb0@0" Pin0InfoVect0LinkObjId="SW-240375_0" Pin0InfoVect1LinkObjId="g_2d57d50_0" Pin0InfoVect2LinkObjId="g_248edb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1020 1441,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24821a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1043 1459,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="40246@x" ObjectIDND1="0@x" ObjectIDND2="g_248edb0@0" ObjectIDZND0="40250@0" Pin0InfoVect0LinkObjId="SW-240375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-240372_0" Pin1InfoVect1LinkObjId="g_2d57d50_0" Pin1InfoVect2LinkObjId="g_248edb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1043 1459,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2482c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="478,50 478,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3480040@0" Pin0InfoVect0LinkObjId="g_3480040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="478,50 478,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24889f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-125 1633,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40295@1" ObjectIDZND0="40296@1" Pin0InfoVect0LinkObjId="SW-240443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240443_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-125 1633,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248eb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-606 1441,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40292@1" ObjectIDZND0="40291@1" Pin0InfoVect0LinkObjId="SW-240441_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-606 1441,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1116 1441,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_248edb0@0" ObjectIDZND1="40250@x" ObjectIDZND2="40246@x" Pin0InfoVect0LinkObjId="g_248edb0_0" Pin0InfoVect1LinkObjId="SW-240375_0" Pin0InfoVect2LinkObjId="SW-240372_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d57d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1116 1441,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1078 1441,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_248edb0@0" ObjectIDZND0="40250@x" ObjectIDZND1="40246@x" Pin0InfoVect0LinkObjId="SW-240375_0" Pin0InfoVect1LinkObjId="SW-240372_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d57d50_0" Pin1InfoVect1LinkObjId="g_248edb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1078 1441,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1078 1393,-1078 1393,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="40250@x" ObjectIDND2="40246@x" ObjectIDZND0="g_248edb0@0" Pin0InfoVect0LinkObjId="g_248edb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d57d50_0" Pin1InfoVect1LinkObjId="SW-240375_0" Pin1InfoVect2LinkObjId="SW-240372_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1078 1393,-1078 1393,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2490200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1441,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2cede60@0" ObjectIDND1="g_2d33280@0" ObjectIDND2="g_2d57d50@0" ObjectIDZND0="40248@0" Pin0InfoVect0LinkObjId="SW-240373_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cede60_0" Pin1InfoVect1LinkObjId="g_2d33280_0" Pin1InfoVect2LinkObjId="g_2d57d50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1441,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2490460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1384,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2cede60@0" ObjectIDND1="g_2d33280@0" ObjectIDND2="40248@x" ObjectIDZND0="g_2d57d50@0" Pin0InfoVect0LinkObjId="g_2d57d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cede60_0" Pin1InfoVect1LinkObjId="g_2d33280_0" Pin1InfoVect2LinkObjId="SW-240373_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1384,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24906c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,13 532,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40255@1" ObjectIDZND0="g_3480e40@0" Pin0InfoVect0LinkObjId="g_3480e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,13 532,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2494450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-148 840,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40261@1" ObjectIDZND0="40262@1" Pin0InfoVect0LinkObjId="SW-240402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-148 840,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24946b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-111 840,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40263@1" ObjectIDZND0="40261@0" Pin0InfoVect0LinkObjId="SW-240401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-111 840,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2498580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="874,-38 874,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2497b30@0" ObjectIDZND0="40264@0" Pin0InfoVect0LinkObjId="SW-240403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2497b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="874,-38 874,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2499510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-176 840,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40262@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_2bb8cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-176 840,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249c5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-94 840,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40263@0" ObjectIDZND0="g_2494910@1" Pin0InfoVect0LinkObjId="g_2494910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-94 840,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a04e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,86 840,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2494910@0" Pin0InfoVect0LinkObjId="g_2494910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d57d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,86 840,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a0740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-83 874,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_24987e0@0" ObjectIDZND0="40264@1" Pin0InfoVect0LinkObjId="SW-240403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24987e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-83 874,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,50 1276,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_343cac0@0" ObjectIDZND0="g_24a0fd0@0" Pin0InfoVect0LinkObjId="g_24a0fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343cac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,50 1276,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343f7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-50 1213,-50 1213,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="40294@x" ObjectIDND1="g_24a0fd0@0" ObjectIDZND0="g_24a16f0@0" Pin0InfoVect0LinkObjId="g_24a16f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240442_0" Pin1InfoVect1LinkObjId="g_24a0fd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-50 1213,-50 1213,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343fa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-73 1276,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40294@0" ObjectIDZND0="g_24a16f0@0" ObjectIDZND1="g_24a0fd0@0" Pin0InfoVect0LinkObjId="g_24a16f0_0" Pin0InfoVect1LinkObjId="g_24a0fd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-73 1276,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-50 1276,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24a16f0@0" ObjectIDND1="40294@x" ObjectIDZND0="g_24a0fd0@1" Pin0InfoVect0LinkObjId="g_24a0fd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24a16f0_0" Pin1InfoVect1LinkObjId="SW-240442_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-50 1276,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3445dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-120 1276,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40293@1" ObjectIDZND0="40294@1" Pin0InfoVect0LinkObjId="SW-240442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-120 1276,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344c4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2124,-37 2124,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_344baa0@0" ObjectIDZND0="40280@0" Pin0InfoVect0LinkObjId="SW-240423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_344baa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2124,-37 2124,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-175 2083,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40278@0" ObjectIDZND0="40306@0" Pin0InfoVect0LinkObjId="g_2bb67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-175 2083,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3450520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2057,-82 2124,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_344c750@0" ObjectIDZND0="40280@1" Pin0InfoVect0LinkObjId="SW-240423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_344c750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2057,-82 2124,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3450780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-93 2083,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40279@0" ObjectIDZND0="g_3448880@1" Pin0InfoVect0LinkObjId="g_3448880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-93 2083,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3455b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-147 2083,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40277@1" ObjectIDZND0="40278@1" Pin0InfoVect0LinkObjId="SW-240422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240421_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-147 2083,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3455dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-110 2083,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40279@1" ObjectIDZND0="40277@0" Pin0InfoVect0LinkObjId="SW-240421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-110 2083,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3457200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-15 2083,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3448880@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2d57d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3448880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-15 2083,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3459890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-165 2427,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40299@1" ObjectIDZND0="g_3458ec0@0" Pin0InfoVect0LinkObjId="g_3458ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-165 2427,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3459af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-112 2427,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40300@1" ObjectIDZND0="g_3458ec0@1" Pin0InfoVect0LinkObjId="g_3458ec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-112 2427,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3459d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-182 2427,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40299@0" ObjectIDZND0="40306@0" Pin0InfoVect0LinkObjId="g_2bb67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-182 2427,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3460e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-169 968,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40297@1" ObjectIDZND0="g_3460480@0" Pin0InfoVect0LinkObjId="g_3460480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="968,-169 968,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34610b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-116 968,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40298@1" ObjectIDZND0="g_3460480@1" Pin0InfoVect0LinkObjId="g_3460480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="968,-116 968,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3461310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-19 968,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="40298@0" Pin0InfoVect0LinkObjId="SW-240451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d57d50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="968,-19 968,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d0df90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,177 532,183 507,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2d0d8b0@0" Pin0InfoVect0LinkObjId="g_2d0d8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="532,177 532,183 507,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d0e1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,183 478,183 478,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d0d8b0@1" ObjectIDZND0="g_3480040@1" Pin0InfoVect0LinkObjId="g_3480040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0d8b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="496,183 478,183 478,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d0eb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,177 2245,183 2229,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2d0e450@0" Pin0InfoVect0LinkObjId="g_2d0e450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,177 2245,183 2229,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d0ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2218,183 2204,183 2204,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d0e450@1" ObjectIDZND0="g_3469130@1" Pin0InfoVect0LinkObjId="g_3469130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0e450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2218,183 2204,183 2204,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-304 1276,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40238@0" ObjectIDZND0="g_2d0eff0@0" Pin0InfoVect0LinkObjId="g_2d0eff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-304 1276,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d0fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-372 1276,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2d0eff0@1" ObjectIDZND0="40301@0" Pin0InfoVect0LinkObjId="g_2cef0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0eff0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-372 1276,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d108f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-303 1633,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40244@0" ObjectIDZND0="g_2d0fed0@0" Pin0InfoVect0LinkObjId="g_2d0fed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-303 1633,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d10b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-367 1633,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2d0fed0@1" ObjectIDZND0="40302@0" Pin0InfoVect0LinkObjId="g_2e17470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d0fed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-367 1633,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d124b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-96 1633,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="40296@0" Pin0InfoVect0LinkObjId="SW-240443_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-96 1633,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d13310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-83 1633,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDZND0="g_3481b20@0" ObjectIDZND1="g_34824f0@0" Pin0InfoVect0LinkObjId="g_3481b20_0" Pin0InfoVect1LinkObjId="g_34824f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-83 1633,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d13570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-54 1633,-29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_34824f0@0" ObjectIDZND0="g_3481b20@1" Pin0InfoVect0LinkObjId="g_3481b20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34824f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-54 1633,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a64480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-137 1276,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40293@0" ObjectIDZND0="40305@0" Pin0InfoVect0LinkObjId="g_2bb8cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-137 1276,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a65ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2428,109 2428,61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2428,109 2428,61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a685b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="969,123 969,75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="969,123 969,75 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-240049" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.500000 -911.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40118" ObjectName="DYN-CX_JDH"/>
     <cge:Meas_Ref ObjectId="240049"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d23a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 716.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d24670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.000000 701.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d24f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 762.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d251c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 747.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d25400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 732.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d25a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 654.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d262b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 668.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d26810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d27180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 776.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d27420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1517.000000 746.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d27660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 761.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d27990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 465.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d28d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 480.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d297f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 246.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d29aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 441.000000 231.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d29ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 292.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d29f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 277.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2a160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 262.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2a490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2348.000000 248.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2a700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.000000 233.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2a940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 294.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2ab80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 279.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2adc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 264.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2b0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 815.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2b350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2b590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 805.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2b8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2bb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1089.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2bd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2c090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2c2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1738.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2c530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1714.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2c860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1885.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2cac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2cd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1875.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2d030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2033.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2d290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2047.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2d4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2d800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1151.000000 281.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2da60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1165.000000 251.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2dca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1141.000000 266.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2dfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1523.000000 281.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2e230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 251.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2e470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 266.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2ec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1394.000000 71.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d2eec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 41.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a61980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1384.000000 56.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a61cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 624.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a61f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1172.000000 594.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a62150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 609.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a62480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1521.000000 624.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a626e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1535.000000 594.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a62920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 609.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a630d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 465.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 480.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 153.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 395.000000 168.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2309.000000 -159.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2284.000000 -144.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c11430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -965.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d608f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -1037.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28708d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.000000 -18.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c2f160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d08d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1817.000000 -17.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7d6c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2287.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c74b00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2222.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf1a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2198.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e1d100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1150.000000 -16.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346e790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3476bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 496.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3477660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2497b30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_344baa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2118.000000 -19.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d58160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2cbf850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">九道河变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc6d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -416.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc6d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -416.000000) translate(0,33)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc6d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -416.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc6d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -416.000000) translate(0,69)">Yd11 Uk%=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e9b750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -611.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ea42e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1337.000000 -1165.000000) translate(0,15)">35kV中村线及九道河T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3174970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -416.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3174970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -416.000000) translate(0,33)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3174970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -416.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3174970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -416.000000) translate(0,69)">Yd11 Uk%=7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cf2e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.500000 -460.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d60090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1594.000000 84.000000) translate(0,12)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d47080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1683.000000 -707.000000) translate(0,15)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2403.000000 123.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c832c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 130.000000) translate(0,12)">九道河隧洞支洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c83510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 935.000000 127.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c837c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 122.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c78630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 124.000000) translate(0,12)">凤凰山倒虹吸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 -426.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.000000 -426.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a09a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 125.000000) translate(0,12)">九道河倒虹吸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3456030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2069.000000 123.000000) translate(0,12)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d10db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1237.000000 92.000000) translate(0,12)">10kV I母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d11950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1744.000000 130.000000) translate(0,12)">鲁支河渡槽线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d137d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -997.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -1070.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -1015.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d147c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -781.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1453.000000 -652.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1645.000000 -611.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d151b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -269.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d153f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -269.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -226.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1509.000000 -225.000000) translate(0,12)">IIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d161b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 -140.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d163f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -70.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.000000 -142.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 -72.000000) translate(0,12)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -138.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -68.000000) translate(0,12)">03560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1799.000000 -139.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d173a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -69.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1970.000000 -72.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d178c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 -142.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -141.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2131.000000 -71.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 -114.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d181c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1654.000000 -120.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -146.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1399.000000 -150.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 -141.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2300.000000 -69.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2142.000000 24.000000) translate(0,12)">04467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 24.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d19180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 541.000000 -141.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d193c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -69.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d19600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 24.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d19840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.000000 23.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d19a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 462.000000 208.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1a6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2179.000000 195.000000) translate(0,12)">10kV2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d225e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1290.000000 -611.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a64000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 985.000000 -143.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a64240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2444.000000 -144.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a6a4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -67.000000) translate(0,17)">孟慧凯：15912027237</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_JDH.CX_JDH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-680 1722,-680 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40304" ObjectName="BS-CX_JDH.CX_JDH_3IM"/>
    <cge:TPSR_Ref TObjectID="40304"/></metadata>
   <polyline fill="none" opacity="0" points="1105,-680 1722,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_JDH.CX_JDH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="464,-204 1415,-204 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40305" ObjectName="BS-CX_JDH.CX_JDH_9IM"/>
    <cge:TPSR_Ref TObjectID="40305"/></metadata>
   <polyline fill="none" opacity="0" points="464,-204 1415,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_JDH.CX_JDH_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1510,-205 2471,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40306" ObjectName="BS-CX_JDH.CX_JDH_9IIM"/>
    <cge:TPSR_Ref TObjectID="40306"/></metadata>
   <polyline fill="none" opacity="0" points="1510,-205 2471,-205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="1539" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="1922" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="1785" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="2245" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="2427" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="1633" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40304" cx="1276" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40304" cx="1441" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40304" cx="1441" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40304" cx="1633" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="1633" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="1276" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="685" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="1384" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="968" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="1122" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="532" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="840" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40305" cx="1276" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40306" cx="2083" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_JDH"/>
</svg>