<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-325" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1199 2140 1344">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="25" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="51" x2="51" y1="43" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="55" x2="55" y1="34" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="58" x2="58" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="35" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="35" x2="35" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="42" x2="42" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="16" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="4"/>
    <circle cx="41" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="17" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="26" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="20" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="26" y1="9" y2="5"/>
    <circle cx="35" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="18" y2="15"/>
    <circle cx="29" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="18" y2="15"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1c98060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape29_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape29_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="34"/>
   </symbol>
   <symbol id="switch2:shape29-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape29-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="34"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape72_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="8" y1="77" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="81" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="81" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="15" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="17" y1="77" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape72_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape75_0">
    <circle cx="15" cy="85" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="62" y2="67"/>
    <circle cx="15" cy="63" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="48" y2="6"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 49,62 49,33 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="49" y1="29" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="54" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="52" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="50" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.382653" x1="16" x2="9" y1="81" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.382653" x1="16" x2="23" y1="81" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.382653" x1="9" x2="23" y1="93" y2="93"/>
   </symbol>
   <symbol id="transformer2:shape75_1"/>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23eb790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23ec720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ed110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23eddb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23eefe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23efc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23f0820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23f1220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23f2c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23f2c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23f43e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23f43e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23f6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23f6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_23f7260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23f8ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23f9b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23faa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23fb300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23fcac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23fd7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23fe080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23fe840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23ff920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24002a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2400d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2401750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2402bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2403770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24047a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24053e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2413bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2406cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_24082c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_24097f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1354" width="2150" x="3111" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.423034" x1="3803" x2="3803" y1="-785" y2="-771"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3803" x2="3803" y1="-730" y2="-762"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3823" x2="3823" y1="-730" y2="-757"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3823" x2="3823" y1="-785" y2="-761"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3783" x2="3823" y1="-730" y2="-730"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-304649">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.789474 -552.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47232" ObjectName="SW-CX_SDJ.CX_SDJ_301BK"/>
     <cge:Meas_Ref ObjectId="304649"/>
    <cge:TPSR_Ref TObjectID="47232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304642">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.605263 -865.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47227" ObjectName="SW-CX_SDJ.CX_SDJ_101BK"/>
     <cge:Meas_Ref ObjectId="304642"/>
    <cge:TPSR_Ref TObjectID="47227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304670">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -402.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47247" ObjectName="SW-CX_SDJ.CX_SDJ_381BK"/>
     <cge:Meas_Ref ObjectId="304670"/>
    <cge:TPSR_Ref TObjectID="47247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304675">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47251" ObjectName="SW-CX_SDJ.CX_SDJ_382BK"/>
     <cge:Meas_Ref ObjectId="304675"/>
    <cge:TPSR_Ref TObjectID="47251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47235" ObjectName="SW-CX_SDJ.CX_SDJ_385BK"/>
     <cge:Meas_Ref ObjectId="304656"/>
    <cge:TPSR_Ref TObjectID="47235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47255" ObjectName="SW-CX_SDJ.CX_SDJ_383BK"/>
     <cge:Meas_Ref ObjectId="304680"/>
    <cge:TPSR_Ref TObjectID="47255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304664">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -398.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47242" ObjectName="SW-CX_SDJ.CX_SDJ_384BK"/>
     <cge:Meas_Ref ObjectId="304664"/>
    <cge:TPSR_Ref TObjectID="47242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -399.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 -397.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -397.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 -2.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.000000 -13.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4623.000000 -680.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -678.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5066.000000 -681.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5179.000000 -679.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c50000">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3884.605263 -1061.000000)" xlink:href="#currentTransformer:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_SDJ.P1-381">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3678.000000 -103.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47527" ObjectName="SM-CX_SDJ.P1-381"/>
    <cge:TPSR_Ref TObjectID="47527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_SDJ.P2-382">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 -106.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47528" ObjectName="SM-CX_SDJ.P2-382"/>
    <cge:TPSR_Ref TObjectID="47528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_SDJ.P3-383">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -101.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47529" ObjectName="SM-CX_SDJ.P3-383"/>
    <cge:TPSR_Ref TObjectID="47529"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QX" endPointId="0" endStationName="CX_SDJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.qijing_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3897,-1142 3897,-1182 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47293" ObjectName="AC-110kV.qijing_line"/>
    <cge:TPSR_Ref TObjectID="47293_SS-325"/></metadata>
   <polyline fill="none" opacity="0" points="3897,-1142 3897,-1182 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -100.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 -98.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4778.000000 -98.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c62ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 4.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b26ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.605263 -1010.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b27770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.605263 -913.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2e860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -712.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1cd9b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-919 3829,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47229@1" ObjectIDZND0="g_1b27770@0" Pin0InfoVect0LinkObjId="g_1b27770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-919 3829,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b22b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-1016 3832,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47230@1" ObjectIDZND0="g_1b26ce0@0" Pin0InfoVect0LinkObjId="g_1b26ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304645_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-1016 3832,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c4f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-1016 3897,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="47230@0" ObjectIDZND0="g_1c50000@0" ObjectIDZND1="g_1d20c30@0" ObjectIDZND2="47293@1" Pin0InfoVect0LinkObjId="g_1c50000_0" Pin0InfoVect1LinkObjId="g_1d20c30_0" Pin0InfoVect2LinkObjId="g_1d22040_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-1016 3897,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c4fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-988 3897,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="47228@1" ObjectIDZND0="47230@x" ObjectIDZND1="g_1c50000@0" ObjectIDZND2="g_1d20c30@0" Pin0InfoVect0LinkObjId="SW-304645_0" Pin0InfoVect1LinkObjId="g_1c50000_0" Pin0InfoVect2LinkObjId="g_1d20c30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304643_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-988 3897,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d201f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-1099 3897,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c50000@0" ObjectIDZND0="47230@x" ObjectIDZND1="47228@x" ObjectIDZND2="g_1d20c30@0" Pin0InfoVect0LinkObjId="SW-304645_0" Pin0InfoVect1LinkObjId="SW-304643_0" Pin0InfoVect2LinkObjId="g_1d20c30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c50000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-1099 3897,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d20a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-1016 3897,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="47230@x" ObjectIDND1="47228@x" ObjectIDZND0="g_1c50000@0" ObjectIDZND1="g_1d20c30@0" ObjectIDZND2="47293@1" Pin0InfoVect0LinkObjId="g_1c50000_0" Pin0InfoVect1LinkObjId="g_1d20c30_0" Pin0InfoVect2LinkObjId="g_1d22040_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-304645_0" Pin1InfoVect1LinkObjId="SW-304643_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-1016 3897,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d21600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-1117 3897,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d20c30@0" ObjectIDZND0="g_1c50000@0" ObjectIDZND1="47230@x" ObjectIDZND2="47228@x" Pin0InfoVect0LinkObjId="g_1c50000_0" Pin0InfoVect1LinkObjId="SW-304645_0" Pin0InfoVect2LinkObjId="SW-304643_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d20c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-1117 3897,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d21e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-1099 3897,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1c50000@0" ObjectIDND1="47230@x" ObjectIDND2="47228@x" ObjectIDZND0="g_1d20c30@0" ObjectIDZND1="47293@1" Pin0InfoVect0LinkObjId="g_1d20c30_0" Pin0InfoVect1LinkObjId="g_1d22040_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c50000_0" Pin1InfoVect1LinkObjId="SW-304645_0" Pin1InfoVect2LinkObjId="SW-304643_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-1099 3897,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d22040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-1117 3897,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="currentTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1d20c30@0" ObjectIDND1="g_1c50000@0" ObjectIDND2="47230@x" ObjectIDZND0="47293@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d20c30_0" Pin1InfoVect1LinkObjId="g_1c50000_0" Pin1InfoVect2LinkObjId="SW-304645_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-1117 3897,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d38f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3850,-647 3850,-638 3897,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1b22d10@0" ObjectIDZND0="47234@x" ObjectIDZND1="47261@x" Pin0InfoVect0LinkObjId="SW-304650_0" Pin0InfoVect1LinkObjId="g_1b2f5d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b22d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3850,-647 3850,-638 3897,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-599 3897,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47234@1" ObjectIDZND0="47232@1" Pin0InfoVect0LinkObjId="SW-304649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-599 3897,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2e0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-560 3897,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47232@0" ObjectIDZND0="47233@1" Pin0InfoVect0LinkObjId="SW-304650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-560 3897,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2e2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-530 3897,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47233@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c6d6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-530 3897,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6d300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-611 4366,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19cde60@1" ObjectIDZND0="47260@1" Pin0InfoVect0LinkObjId="SW-304685_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19cde60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-611 4366,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-652 4366,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="47260@0" ObjectIDZND0="g_1cd2ac0@0" Pin0InfoVect0LinkObjId="g_1cd2ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-652 4366,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6d6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-542 4366,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47259@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-542 4366,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd1e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-584 4318,-571 4366,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1c2e490@0" ObjectIDZND0="47259@x" ObjectIDZND1="g_19cde60@0" Pin0InfoVect0LinkObjId="SW-304685_0" Pin0InfoVect1LinkObjId="g_19cde60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c2e490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-584 4318,-571 4366,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd26e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-559 4366,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47259@1" ObjectIDZND0="g_1c2e490@0" ObjectIDZND1="g_19cde60@0" Pin0InfoVect0LinkObjId="g_1c2e490_0" Pin0InfoVect1LinkObjId="g_19cde60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-559 4366,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd28d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-571 4366,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1c2e490@0" ObjectIDND1="47259@x" ObjectIDZND0="g_19cde60@0" Pin0InfoVect0LinkObjId="g_19cde60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c2e490_0" Pin1InfoVect1LinkObjId="SW-304685_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-571 4366,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd5240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd5460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-410 3683,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47247@0" ObjectIDZND0="47249@1" Pin0InfoVect0LinkObjId="SW-304671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-410 3683,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd5680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-449 3683,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47248@1" ObjectIDZND0="47247@1" Pin0InfoVect0LinkObjId="SW-304670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304671_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-449 3683,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd58a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-187 3683,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1cd4920@0" ObjectIDZND0="47527@0" Pin0InfoVect0LinkObjId="SM-CX_SDJ.P1-381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cd4920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-187 3683,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c5f9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5fbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-408 3855,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47251@0" ObjectIDZND0="47253@1" Pin0InfoVect0LinkObjId="SW-304676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-408 3855,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5fe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-447 3855,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47252@1" ObjectIDZND0="47251@1" Pin0InfoVect0LinkObjId="SW-304675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-447 3855,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c60020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-185 3855,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1c5f0a0@0" ObjectIDZND0="47528@0" Pin0InfoVect0LinkObjId="SM-CX_SDJ.P2-382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c5f0a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-185 3855,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c60240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-238 3855,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c5f0a0@1" ObjectIDZND0="47253@x" ObjectIDZND1="g_1c92db0@0" ObjectIDZND2="47254@x" Pin0InfoVect0LinkObjId="SW-304676_0" Pin0InfoVect1LinkObjId="g_1c92db0_0" Pin0InfoVect2LinkObjId="SW-304677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c5f0a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-238 3855,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c60460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-341 3855,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c5f0a0@0" ObjectIDND1="g_1c92db0@0" ObjectIDND2="47254@x" ObjectIDZND0="47253@0" Pin0InfoVect0LinkObjId="SW-304676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c5f0a0_0" Pin1InfoVect1LinkObjId="g_1c92db0_0" Pin1InfoVect2LinkObjId="SW-304677_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-341 3855,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c60680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-321 3896,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c92db0@0" ObjectIDZND0="47253@x" ObjectIDZND1="g_1c5f0a0@0" ObjectIDZND2="47254@x" Pin0InfoVect0LinkObjId="SW-304676_0" Pin0InfoVect1LinkObjId="g_1c5f0a0_0" Pin0InfoVect2LinkObjId="SW-304677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c92db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-321 3896,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c608a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-320 3935,-341 3896,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47254@0" ObjectIDZND0="g_1c92db0@0" ObjectIDZND1="47253@x" ObjectIDZND2="g_1c5f0a0@0" Pin0InfoVect0LinkObjId="g_1c92db0_0" Pin0InfoVect1LinkObjId="SW-304676_0" Pin0InfoVect2LinkObjId="g_1c5f0a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-320 3935,-341 3896,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c60ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-341 3855,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1c92db0@0" ObjectIDND1="47254@x" ObjectIDZND0="47253@x" ObjectIDZND1="g_1c5f0a0@0" Pin0InfoVect0LinkObjId="SW-304676_0" Pin0InfoVect1LinkObjId="g_1c5f0a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c92db0_0" Pin1InfoVect1LinkObjId="SW-304677_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-341 3855,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ccd2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccd4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-408 4018,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47235@0" ObjectIDZND0="47237@1" Pin0InfoVect0LinkObjId="SW-304657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-408 4018,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccd710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-447 4018,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47236@1" ObjectIDZND0="47235@1" Pin0InfoVect0LinkObjId="SW-304656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-447 4018,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccd930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-236 4018,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1ccc9b0@1" ObjectIDZND0="47237@x" ObjectIDZND1="g_1ccbe40@0" ObjectIDZND2="47238@x" Pin0InfoVect0LinkObjId="SW-304657_0" Pin0InfoVect1LinkObjId="g_1ccbe40_0" Pin0InfoVect2LinkObjId="SW-304658_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ccc9b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-236 4018,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccdb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-339 4018,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1ccc9b0@0" ObjectIDND1="g_1ccbe40@0" ObjectIDND2="47238@x" ObjectIDZND0="47237@0" Pin0InfoVect0LinkObjId="SW-304657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ccc9b0_0" Pin1InfoVect1LinkObjId="g_1ccbe40_0" Pin1InfoVect2LinkObjId="SW-304658_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-339 4018,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccdd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-321 4060,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1ccbe40@0" ObjectIDZND0="47237@x" ObjectIDZND1="g_1ccc9b0@0" ObjectIDZND2="47238@x" Pin0InfoVect0LinkObjId="SW-304657_0" Pin0InfoVect1LinkObjId="g_1ccc9b0_0" Pin0InfoVect2LinkObjId="SW-304658_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ccbe40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-321 4060,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccdf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-318 4099,-339 4060,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47238@0" ObjectIDZND0="g_1ccbe40@0" ObjectIDZND1="47237@x" ObjectIDZND2="g_1ccc9b0@0" Pin0InfoVect0LinkObjId="g_1ccbe40_0" Pin0InfoVect1LinkObjId="SW-304657_0" Pin0InfoVect2LinkObjId="g_1ccc9b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-318 4099,-339 4060,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cce1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-339 4018,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1ccbe40@0" ObjectIDND1="47238@x" ObjectIDZND0="47237@x" ObjectIDZND1="g_1ccc9b0@0" Pin0InfoVect0LinkObjId="SW-304657_0" Pin0InfoVect1LinkObjId="g_1ccc9b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ccbe40_0" Pin1InfoVect1LinkObjId="SW-304658_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-339 4018,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d25570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d257d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-408 4176,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47255@0" ObjectIDZND0="47257@1" Pin0InfoVect0LinkObjId="SW-304681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-408 4176,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d25a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-447 4176,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47256@1" ObjectIDZND0="47255@1" Pin0InfoVect0LinkObjId="SW-304680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-447 4176,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d25c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-185 4176,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1d251d0@0" ObjectIDZND0="47529@0" Pin0InfoVect0LinkObjId="SM-CX_SDJ.P3-383_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d251d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-185 4176,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d25ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-238 4176,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1d251d0@1" ObjectIDZND0="47257@x" ObjectIDZND1="g_1cc0600@0" ObjectIDZND2="47258@x" Pin0InfoVect0LinkObjId="SW-304681_0" Pin0InfoVect1LinkObjId="g_1cc0600_0" Pin0InfoVect2LinkObjId="SW-304682_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d251d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-238 4176,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d26150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-341 4176,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d251d0@0" ObjectIDND1="g_1cc0600@0" ObjectIDND2="47258@x" ObjectIDZND0="47257@0" Pin0InfoVect0LinkObjId="SW-304681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d251d0_0" Pin1InfoVect1LinkObjId="g_1cc0600_0" Pin1InfoVect2LinkObjId="SW-304682_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-341 4176,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d263b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-321 4217,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cc0600@0" ObjectIDZND0="47257@x" ObjectIDZND1="g_1d251d0@0" ObjectIDZND2="47258@x" Pin0InfoVect0LinkObjId="SW-304681_0" Pin0InfoVect1LinkObjId="g_1d251d0_0" Pin0InfoVect2LinkObjId="SW-304682_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc0600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-321 4217,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d26610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4256,-320 4256,-341 4217,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47258@0" ObjectIDZND0="g_1cc0600@0" ObjectIDZND1="47257@x" ObjectIDZND2="g_1d251d0@0" Pin0InfoVect0LinkObjId="g_1cc0600_0" Pin0InfoVect1LinkObjId="SW-304681_0" Pin0InfoVect2LinkObjId="g_1d251d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4256,-320 4256,-341 4217,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d26870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-341 4176,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1cc0600@0" ObjectIDND1="47258@x" ObjectIDZND0="47257@x" ObjectIDZND1="g_1d251d0@0" Pin0InfoVect0LinkObjId="SW-304681_0" Pin0InfoVect1LinkObjId="g_1d251d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cc0600_0" Pin1InfoVect1LinkObjId="SW-304682_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-341 4176,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c59580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c597e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-406 4339,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47242@0" ObjectIDZND0="47244@1" Pin0InfoVect0LinkObjId="SW-304665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-406 4339,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c59a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-445 4339,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47243@1" ObjectIDZND0="47242@1" Pin0InfoVect0LinkObjId="SW-304664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-445 4339,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c59ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-183 4339,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1c49f80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c49f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-183 4339,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c59f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-236 4339,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c49f80@1" ObjectIDZND0="47244@x" ObjectIDZND1="g_1c49250@0" ObjectIDZND2="47245@x" Pin0InfoVect0LinkObjId="SW-304665_0" Pin0InfoVect1LinkObjId="g_1c49250_0" Pin0InfoVect2LinkObjId="SW-304666_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c49f80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-236 4339,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5a160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-339 4339,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c49f80@0" ObjectIDND1="g_1c49250@0" ObjectIDND2="47245@x" ObjectIDZND0="47244@0" Pin0InfoVect0LinkObjId="SW-304665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c49f80_0" Pin1InfoVect1LinkObjId="g_1c49250_0" Pin1InfoVect2LinkObjId="SW-304666_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-339 4339,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5a3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-319 4380,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c49250@0" ObjectIDZND0="47244@x" ObjectIDZND1="g_1c49f80@0" ObjectIDZND2="47245@x" Pin0InfoVect0LinkObjId="SW-304665_0" Pin0InfoVect1LinkObjId="g_1c49f80_0" Pin0InfoVect2LinkObjId="SW-304666_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c49250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-319 4380,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5a620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-318 4419,-339 4380,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47245@0" ObjectIDZND0="g_1c49250@0" ObjectIDZND1="47244@x" ObjectIDZND2="g_1c49f80@0" Pin0InfoVect0LinkObjId="g_1c49250_0" Pin0InfoVect1LinkObjId="SW-304665_0" Pin0InfoVect2LinkObjId="g_1c49f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-318 4419,-339 4380,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5a880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-339 4339,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1c49250@0" ObjectIDND1="47245@x" ObjectIDZND0="47244@x" ObjectIDZND1="g_1c49f80@0" Pin0InfoVect0LinkObjId="SW-304665_0" Pin0InfoVect1LinkObjId="g_1c49f80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c49250_0" Pin1InfoVect1LinkObjId="SW-304666_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-339 4339,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cdfe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce0090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-407 4494,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-407 4494,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce02f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-446 4494,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-446 4494,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce0550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-184 4494,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1c8e0c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8e0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-184 4494,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-237 4494,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c8e0c0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1c8d390@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c8d390_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8e0c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-237 4494,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce0a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-340 4494,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c8e0c0@0" ObjectIDND1="g_1c8d390@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c8e0c0_0" Pin1InfoVect1LinkObjId="g_1c8d390_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-340 4494,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce0c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-320 4535,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c8d390@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1c8e0c0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c8e0c0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8d390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-320 4535,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-319 4574,-340 4535,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1c8d390@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1c8e0c0@0" Pin0InfoVect0LinkObjId="g_1c8d390_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1c8e0c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-319 4574,-340 4535,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce1130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-340 4494,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1c8d390@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1c8e0c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c8e0c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c8d390_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-340 4494,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cec580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c769b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-405 4644,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-405 4644,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c76c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-444 4644,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-444 4644,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c76e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-182 4644,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1ceaf50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ceaf50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-182 4644,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c770d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-235 4644,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1ceaf50@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1cea220@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cea220_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ceaf50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-235 4644,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c77330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-338 4644,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1ceaf50@0" ObjectIDND1="g_1cea220@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ceaf50_0" Pin1InfoVect1LinkObjId="g_1cea220_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-338 4644,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c77590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-318 4685,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cea220@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1ceaf50@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1ceaf50_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cea220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-318 4685,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c777f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-317 4724,-338 4685,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1cea220@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1ceaf50@0" Pin0InfoVect0LinkObjId="g_1cea220_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1ceaf50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-317 4724,-338 4685,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c77a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-338 4644,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1cea220@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1ceaf50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1ceaf50_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cea220_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-338 4644,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c94520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c94780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-405 4783,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-405 4783,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c949e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-444 4783,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-444 4783,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c94c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-182 4783,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1cbce00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cbce00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-182 4783,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c94ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-235 4783,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cbce00@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1cbc0d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cbc0d0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cbce00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-235 4783,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c95100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-338 4783,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1cbce00@0" ObjectIDND1="g_1cbc0d0@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cbce00_0" Pin1InfoVect1LinkObjId="g_1cbc0d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-338 4783,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c95360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4824,-318 4824,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cbc0d0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1cbce00@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cbce00_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cbc0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4824,-318 4824,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c955c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4863,-317 4863,-338 4824,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1cbc0d0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1cbce00@0" Pin0InfoVect0LinkObjId="g_1cbc0d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1cbce00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4863,-317 4863,-338 4824,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c95820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4824,-338 4783,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1cbc0d0@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1cbce00@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cbce00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cbc0d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4824,-338 4783,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c98640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,57 4018,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1c978b0@0" Pin0InfoVect0LinkObjId="g_1c978b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,57 4018,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6f9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,5 3977,5 3977,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="47241@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-304661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,5 3977,5 3977,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c704c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-5 4018,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="reactance" ObjectIDND0="47241@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-5 4018,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c70720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,5 4018,15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="reactance" ObjectIDND0="0@x" ObjectIDND1="47241@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-304661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,5 4018,15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c70980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-466 3683,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47248@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-466 3683,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c711b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-464 3855,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47252@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-464 3855,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c719e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-464 4018,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47236@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-464 4018,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c72210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-464 4176,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47256@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-464 4176,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c72a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-462 4339,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47243@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-462 4339,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c73270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-463 4494,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-463 4494,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c73aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-461 4644,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-461 4644,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c742d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-461 4783,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47225@0" Pin0InfoVect0LinkObjId="g_1c2e2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-461 4783,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c636f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4296,-14 4296,-21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="breaker" ObjectIDND0="g_1c62ca0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c62ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4296,-14 4296,-21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c63950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4296,-60 4296,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47246@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4296,-60 4296,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c63bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4296,-96 4296,-107 4339,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="47246@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304667_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4296,-96 4296,-107 4339,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c32180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-728 4632,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-728 4632,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c353e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-688 4632,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-688 4632,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c3a410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-726 4745,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-726 4745,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b9d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-686 4745,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-686 4745,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b9d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-658 4632,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-658 4632,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b9d9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-656 4745,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-656 4745,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ba3120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-729 5075,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-729 5075,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b5cd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-689 5075,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-689 5075,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b61d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5188,-727 5188,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5188,-727 5188,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b65070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5188,-687 5188,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5188,-687 5188,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b652d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-659 5075,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-659 5075,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b65530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5188,-657 5188,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5188,-657 5188,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be8fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-795 4632,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b66000@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b66000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-795 4632,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be9210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5188,-744 5188,-820 4632,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5188,-744 5188,-820 4632,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be9e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-758 5075,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1be9470@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1be9470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-758 5075,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bea0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-743 4745,-783 5075,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-743 4745,-783 5075,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bea350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-811 5075,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1be9470@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1be9470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-811 5075,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bea5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-848 4632,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1b66000@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b66000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-848 4632,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1beda00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-968 4586,-968 4586,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-968 4586,-968 4586,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bef600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-1025 4632,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1bee440@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bee440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-1025 4632,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bef860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-1047 4631,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-1047 4631,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bf1640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5076,-1005 5076,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1beec20@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1beec20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5076,-1005 5076,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bf18a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-1034 5075,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1beec20@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1beec20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-1034 5075,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-183 4018,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ccc9b0@0" ObjectIDZND0="47239@1" Pin0InfoVect0LinkObjId="SW-304659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ccc9b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-183 4018,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-66 4018,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c98830@0" ObjectIDZND0="0@x" ObjectIDZND1="47241@x" ObjectIDZND2="47240@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-304661_0" Pin0InfoVect2LinkObjId="SW-304660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c98830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-66 4018,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be4ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-37 3977,-51 4018,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_1c98830@0" ObjectIDZND1="47240@x" ObjectIDZND2="47239@x" Pin0InfoVect0LinkObjId="g_1c98830_0" Pin0InfoVect1LinkObjId="SW-304660_0" Pin0InfoVect2LinkObjId="SW-304659_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-37 3977,-51 4018,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be55c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-66 4018,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1c98830@0" ObjectIDND1="47240@x" ObjectIDND2="47239@x" ObjectIDZND0="0@x" ObjectIDZND1="47241@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-304661_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c98830_0" Pin1InfoVect1LinkObjId="SW-304660_0" Pin1InfoVect2LinkObjId="SW-304659_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-66 4018,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be5820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-51 4018,-41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1c98830@0" ObjectIDND2="47240@x" ObjectIDZND0="47241@1" Pin0InfoVect0LinkObjId="SW-304661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1c98830_0" Pin1InfoVect2LinkObjId="SW-304660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-51 4018,-41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be5a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4046,-86 4018,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="47240@0" ObjectIDZND0="47239@x" ObjectIDZND1="g_1c98830@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-304659_0" Pin0InfoVect1LinkObjId="g_1c98830_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4046,-86 4018,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be6570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-93 4018,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="47239@0" ObjectIDZND0="47240@x" ObjectIDZND1="g_1c98830@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-304660_0" Pin0InfoVect1LinkObjId="g_1c98830_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-93 4018,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcc340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-86 4018,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="47240@x" ObjectIDND1="47239@x" ObjectIDZND0="g_1c98830@0" ObjectIDZND1="0@x" ObjectIDZND2="47241@x" Pin0InfoVect0LinkObjId="g_1c98830_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-304661_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-304660_0" Pin1InfoVect1LinkObjId="SW-304659_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-86 4018,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-240 3683,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cd4920@1" ObjectIDZND0="47249@x" ObjectIDZND1="g_1c58bb0@0" ObjectIDZND2="47250@x" Pin0InfoVect0LinkObjId="SW-304671_0" Pin0InfoVect1LinkObjId="g_1c58bb0_0" Pin0InfoVect2LinkObjId="SW-304672_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cd4920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-240 3683,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcd070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-339 3683,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1cd4920@0" ObjectIDND1="g_1c58bb0@0" ObjectIDND2="47250@x" ObjectIDZND0="47249@0" Pin0InfoVect0LinkObjId="SW-304671_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cd4920_0" Pin1InfoVect1LinkObjId="g_1c58bb0_0" Pin1InfoVect2LinkObjId="SW-304672_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-339 3683,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcd2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-323 3724,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c58bb0@0" ObjectIDZND0="g_1cd4920@0" ObjectIDZND1="47249@x" ObjectIDZND2="47250@x" Pin0InfoVect0LinkObjId="g_1cd4920_0" Pin0InfoVect1LinkObjId="SW-304671_0" Pin0InfoVect2LinkObjId="SW-304672_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c58bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-323 3724,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcdda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3763,-323 3763,-339 3724,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47250@0" ObjectIDZND0="g_1c58bb0@0" ObjectIDZND1="g_1cd4920@0" ObjectIDZND2="47249@x" Pin0InfoVect0LinkObjId="g_1c58bb0_0" Pin0InfoVect1LinkObjId="g_1cd4920_0" Pin0InfoVect2LinkObjId="SW-304671_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3763,-323 3763,-339 3724,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bce000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-339 3683,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1c58bb0@0" ObjectIDND1="47250@x" ObjectIDZND0="g_1cd4920@0" ObjectIDZND1="47249@x" Pin0InfoVect0LinkObjId="g_1cd4920_0" Pin0InfoVect1LinkObjId="SW-304671_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c58bb0_0" Pin1InfoVect1LinkObjId="SW-304672_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-339 3683,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b26a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-111 4064,-111 4064,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-111 4064,-111 4064,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b2f370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3783,-730 3783,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b2e860@0" ObjectIDZND0="47231@0" Pin0InfoVect0LinkObjId="SW-304648_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2e860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3783,-730 3783,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b2f5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3783,-778 3783,-785 3894,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="47231@1" ObjectIDZND0="47261@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304648_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3783,-778 3783,-785 3894,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-873 3897,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="47227@0" ObjectIDZND0="48136@0" Pin0InfoVect0LinkObjId="g_1b95210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-873 3897,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8efa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3876,-919 3897,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47229@0" ObjectIDZND0="47227@x" ObjectIDZND1="47228@x" Pin0InfoVect0LinkObjId="SW-304642_0" Pin0InfoVect1LinkObjId="SW-304643_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3876,-919 3897,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8fa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-902 3897,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47227@1" ObjectIDZND0="47229@x" ObjectIDZND1="47228@x" Pin0InfoVect0LinkObjId="SW-304644_0" Pin0InfoVect1LinkObjId="SW-304643_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-304642_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-902 3897,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8fcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-919 3897,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47229@x" ObjectIDND1="47227@x" ObjectIDZND0="47228@0" Pin0InfoVect0LinkObjId="SW-304643_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-304644_0" Pin1InfoVect1LinkObjId="SW-304642_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-919 3897,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b91df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-719 3897,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47261@1" ObjectIDZND0="g_1b22d10@0" ObjectIDZND1="47234@x" Pin0InfoVect0LinkObjId="g_1b22d10_0" Pin0InfoVect1LinkObjId="SW-304650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2f5d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-719 3897,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b91fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-638 3897,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1b22d10@0" ObjectIDND1="47261@x" ObjectIDZND0="47234@0" Pin0InfoVect0LinkObjId="SW-304650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b22d10_0" Pin1InfoVect1LinkObjId="g_1b2f5d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-638 3897,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b94fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-799 3897,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="47261@0" ObjectIDZND0="48655@0" Pin0InfoVect0LinkObjId="SW-319186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2f5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-799 3897,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b95210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-840 3897,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48655@1" ObjectIDZND0="48136@0" Pin0InfoVect0LinkObjId="g_1b8ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319186_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-840 3897,-856 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="3683" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="3855" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="4018" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="4176" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="4339" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="4494" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="4644" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47225" cx="4783" cy="-486" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5075" cy="-636" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5188" cy="-636" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4632" cy="-635" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4745" cy="-635" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48136" cx="3897" cy="-856" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48136" cx="3897" cy="-856" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-303561" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3451.000000 -1114.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47041" ObjectName="DYN-CX_SDJ"/>
     <cge:Meas_Ref ObjectId="303561"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d2a4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -577.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1056.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1a7be00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3282.000000 -1169.500000) translate(0,16)">三道景风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3632.000000 -99.000000) translate(0,15)">  三道景Ⅰ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3632.000000 -99.000000) translate(0,33)">(1~4号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d3c6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -622.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d056e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -477.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d05850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -773.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4ef70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -763.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4ef70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -763.000000) translate(0,27)">SZZ11-100000/115GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4ef70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -763.000000) translate(0,42)">115±8x1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4ef70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -763.000000) translate(0,57)">Ud%=10.5 YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cb24c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -763.000000) translate(0,15)">35kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba4b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -93.000000) translate(0,15)">  三道景Ⅱ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba4b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -93.000000) translate(0,33)">(5~8号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba5600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -91.000000) translate(0,15)">  三道景Ⅲ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba5600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -91.000000) translate(0,33)">(9~12号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba6280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3927.000000 109.000000) translate(0,15)">35kV1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba6280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3927.000000 109.000000) translate(0,33)">     (-20~20MVar)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c63e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 18.000000) translate(0,15)">35kV1号站用变兼接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c654f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -83.000000) translate(0,12)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c65c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -84.000000) translate(0,12)">储能(预留)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c665d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 -84.000000) translate(0,12)">预留滤波装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c690d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4592.000000 -710.000000) translate(0,15)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b9de30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5072.000000 -623.000000) translate(0,15)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba01a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5033.000000 -711.000000) translate(0,15)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b65790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -708.000000) translate(0,15)">403</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b65dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5147.000000 -708.000000) translate(0,15)">404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bedc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.000000 -947.000000) translate(0,15)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bee1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4412.000000 -998.000000) translate(0,15)">35kV1号站用变兼接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf1b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -1114.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bda6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4907.000000 -1166.000000) translate(0,15)">1.电源35kV北城变10kV北赵线053断路器;</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bda6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4907.000000 -1166.000000) translate(0,33)">2.接于10kV牛拖箐支线T水箐支线;</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bda6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4907.000000 -1166.000000) translate(0,51)">3.隔离点载10kV牛拖箐支线A001隔离开关。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1be0440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -939.000000) translate(0,15)">10kV2号备用变(施工变)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.605263 -892.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3906.605263 -977.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3822.605263 -944.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3840.605263 -1042.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b294d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3906.789474 -581.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b29750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4383.000000 -604.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b29990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -433.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b29bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3769.000000 -311.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b29e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3864.000000 -429.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2a050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4027.000000 -429.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2a290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4108.000000 -302.000000) translate(0,12)">38537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2a4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -121.000000) translate(0,12)">3856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2a710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4042.000000 -81.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2cf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -30.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2d440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -304.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -429.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2d8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -304.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2db00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.000000 -427.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2de40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4428.000000 -302.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b320e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -764.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b88fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -89.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1b89380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.605263 -1172.000000) translate(0,14)">110kV启景线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1b900d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3454.000000 -1070.000000) translate(0,16)">AGC/AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1edbb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3248.000000 -233.000000) translate(0,15)">6384366   6384199</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3125" y="-595"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(170,85,127)" stroke-width="1" width="12" x="3797" y="-771"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1cd2ac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 -687.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 62.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19cde60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -575.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d20c30">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.605263 -1109.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b22d10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.789474 -643.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2e490">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -580.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c58bb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -269.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cd4920">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3678.000000 -182.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c92db0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -267.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c5f0a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 -180.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ccbe40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -267.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ccc9b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -178.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc0600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 -267.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d251d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -180.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c49250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4373.000000 -265.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c49f80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -178.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c8d390">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -266.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c8e0c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -179.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cea220">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4678.000000 -264.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ceaf50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 -177.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cbc0d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -264.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cbce00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4778.000000 -177.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c978b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 96.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c98830">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -59.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b66000">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4627.000000 -790.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be9470">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5070.000000 -753.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bee440">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4640.000000 -1020.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1beec20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5084.000000 -1000.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-304378" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.605263 -926.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304378" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47227"/>
     <cge:Term_Ref ObjectID="44111"/>
    <cge:TPSR_Ref TObjectID="47227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-304379" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.605263 -926.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47227"/>
     <cge:Term_Ref ObjectID="44111"/>
    <cge:TPSR_Ref TObjectID="47227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-304375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.605263 -926.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47227"/>
     <cge:Term_Ref ObjectID="44111"/>
    <cge:TPSR_Ref TObjectID="47227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-304401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.789474 -594.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47232"/>
     <cge:Term_Ref ObjectID="44121"/>
    <cge:TPSR_Ref TObjectID="47232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-304402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.789474 -594.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47232"/>
     <cge:Term_Ref ObjectID="44121"/>
    <cge:TPSR_Ref TObjectID="47232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-304398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.789474 -594.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47232"/>
     <cge:Term_Ref ObjectID="44121"/>
    <cge:TPSR_Ref TObjectID="47232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-304423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -48.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47247"/>
     <cge:Term_Ref ObjectID="44568"/>
    <cge:TPSR_Ref TObjectID="47247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-304424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -48.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47247"/>
     <cge:Term_Ref ObjectID="44568"/>
    <cge:TPSR_Ref TObjectID="47247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-304420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -48.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304420" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47247"/>
     <cge:Term_Ref ObjectID="44568"/>
    <cge:TPSR_Ref TObjectID="47247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-304429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47251"/>
     <cge:Term_Ref ObjectID="44576"/>
    <cge:TPSR_Ref TObjectID="47251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-304430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47251"/>
     <cge:Term_Ref ObjectID="44576"/>
    <cge:TPSR_Ref TObjectID="47251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-304426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -50.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47251"/>
     <cge:Term_Ref ObjectID="44576"/>
    <cge:TPSR_Ref TObjectID="47251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-304435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47255"/>
     <cge:Term_Ref ObjectID="44660"/>
    <cge:TPSR_Ref TObjectID="47255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-304436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47255"/>
     <cge:Term_Ref ObjectID="44660"/>
    <cge:TPSR_Ref TObjectID="47255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-304432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304432" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47255"/>
     <cge:Term_Ref ObjectID="44660"/>
    <cge:TPSR_Ref TObjectID="47255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-304417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -45.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47242"/>
     <cge:Term_Ref ObjectID="44147"/>
    <cge:TPSR_Ref TObjectID="47242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-304418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -45.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47242"/>
     <cge:Term_Ref ObjectID="44147"/>
    <cge:TPSR_Ref TObjectID="47242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-304414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -45.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47242"/>
     <cge:Term_Ref ObjectID="44147"/>
    <cge:TPSR_Ref TObjectID="47242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-304411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -12.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47235"/>
     <cge:Term_Ref ObjectID="44127"/>
    <cge:TPSR_Ref TObjectID="47235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-304412" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -12.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304412" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47235"/>
     <cge:Term_Ref ObjectID="44127"/>
    <cge:TPSR_Ref TObjectID="47235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-304408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -12.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47235"/>
     <cge:Term_Ref ObjectID="44127"/>
    <cge:TPSR_Ref TObjectID="47235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-304381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -588.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47225"/>
     <cge:Term_Ref ObjectID="44108"/>
    <cge:TPSR_Ref TObjectID="47225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-304382" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -588.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304382" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47225"/>
     <cge:Term_Ref ObjectID="44108"/>
    <cge:TPSR_Ref TObjectID="47225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-304383" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -588.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47225"/>
     <cge:Term_Ref ObjectID="44108"/>
    <cge:TPSR_Ref TObjectID="47225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-304384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -588.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47225"/>
     <cge:Term_Ref ObjectID="44108"/>
    <cge:TPSR_Ref TObjectID="47225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-304388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -588.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="304388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47225"/>
     <cge:Term_Ref ObjectID="44108"/>
    <cge:TPSR_Ref TObjectID="47225"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="165" x="3247" y="-1179"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/></g>
   <g href="AVC三道景.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3435" y="-1086"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1775720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3963.000000 930.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c55ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 915.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdd8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 900.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdda60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3565.000000 48.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cde1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3554.000000 33.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cde330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 18.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b20cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 576.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b20e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 562.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b20f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3533.000000 543.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 591.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 527.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b855c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 594.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b857d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 579.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b859e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 564.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SDJ.CX_SDJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-486 4855,-486 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47225" ObjectName="BS-CX_SDJ.CX_SDJ_3IM"/>
    <cge:TPSR_Ref TObjectID="47225"/></metadata>
   <polyline fill="none" opacity="0" points="3589,-486 4855,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-635 4813,-635 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4565,-635 4813,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-636 5256,-636 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5008,-636 5256,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SDJ.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-856 3903,-856 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48136" ObjectName="BS-CX_SDJ.XM"/>
    <cge:TPSR_Ref TObjectID="48136"/></metadata>
   <polyline fill="none" opacity="0" points="3892,-856 3903,-856 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3213.000000 -1099.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-309734" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -1007.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309734" ObjectName="CX_SDJ:1011-P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-309735" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -972.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309735" ObjectName="CX_SDJ:1011-Q"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-304644">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3897.605263 -904.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47229" ObjectName="SW-CX_SDJ.CX_SDJ_10117SW"/>
     <cge:Meas_Ref ObjectId="304644"/>
    <cge:TPSR_Ref TObjectID="47229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304643">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.605263 -930.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47228" ObjectName="SW-CX_SDJ.CX_SDJ_1011SW"/>
     <cge:Meas_Ref ObjectId="304643"/>
    <cge:TPSR_Ref TObjectID="47228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304645">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3899.605263 -1001.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47230" ObjectName="SW-CX_SDJ.CX_SDJ_10167SW"/>
     <cge:Meas_Ref ObjectId="304645"/>
    <cge:TPSR_Ref TObjectID="47230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.789474 -594.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47234" ObjectName="SW-CX_SDJ.CX_SDJ_301XC1"/>
     <cge:Meas_Ref ObjectId="304650"/>
    <cge:TPSR_Ref TObjectID="47234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.789474 -523.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47233" ObjectName="SW-CX_SDJ.CX_SDJ_301XC"/>
     <cge:Meas_Ref ObjectId="304650"/>
    <cge:TPSR_Ref TObjectID="47233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304685">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 -628.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47260" ObjectName="SW-CX_SDJ.CX_SDJ_3901XC1"/>
     <cge:Meas_Ref ObjectId="304685"/>
    <cge:TPSR_Ref TObjectID="47260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304685">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 -535.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47259" ObjectName="SW-CX_SDJ.CX_SDJ_3901XC"/>
     <cge:Meas_Ref ObjectId="304685"/>
    <cge:TPSR_Ref TObjectID="47259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -442.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47248" ObjectName="SW-CX_SDJ.CX_SDJ_381XC"/>
     <cge:Meas_Ref ObjectId="304671"/>
    <cge:TPSR_Ref TObjectID="47248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -372.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47249" ObjectName="SW-CX_SDJ.CX_SDJ_381XC1"/>
     <cge:Meas_Ref ObjectId="304671"/>
    <cge:TPSR_Ref TObjectID="47249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -440.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47252" ObjectName="SW-CX_SDJ.CX_SDJ_382XC"/>
     <cge:Meas_Ref ObjectId="304676"/>
    <cge:TPSR_Ref TObjectID="47252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -370.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47253" ObjectName="SW-CX_SDJ.CX_SDJ_382XC1"/>
     <cge:Meas_Ref ObjectId="304676"/>
    <cge:TPSR_Ref TObjectID="47253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 -440.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47236" ObjectName="SW-CX_SDJ.CX_SDJ_385XC"/>
     <cge:Meas_Ref ObjectId="304657"/>
    <cge:TPSR_Ref TObjectID="47236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 -370.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47237" ObjectName="SW-CX_SDJ.CX_SDJ_385XC1"/>
     <cge:Meas_Ref ObjectId="304657"/>
    <cge:TPSR_Ref TObjectID="47237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304681">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -440.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47256" ObjectName="SW-CX_SDJ.CX_SDJ_383XC"/>
     <cge:Meas_Ref ObjectId="304681"/>
    <cge:TPSR_Ref TObjectID="47256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304681">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -370.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47257" ObjectName="SW-CX_SDJ.CX_SDJ_383XC1"/>
     <cge:Meas_Ref ObjectId="304681"/>
    <cge:TPSR_Ref TObjectID="47257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -438.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47243" ObjectName="SW-CX_SDJ.CX_SDJ_384XC"/>
     <cge:Meas_Ref ObjectId="304665"/>
    <cge:TPSR_Ref TObjectID="47243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -368.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47244" ObjectName="SW-CX_SDJ.CX_SDJ_384XC1"/>
     <cge:Meas_Ref ObjectId="304665"/>
    <cge:TPSR_Ref TObjectID="47244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 -439.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 -369.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 -437.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 -367.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.000000 -437.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.000000 -367.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -721.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -651.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -719.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -649.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.000000 -722.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.000000 -652.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.000000 -720.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.000000 -650.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4577.000000 -904.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -79.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47240" ObjectName="SW-CX_SDJ.CX_SDJ_38567SW"/>
     <cge:Meas_Ref ObjectId="304660"/>
    <cge:TPSR_Ref TObjectID="47240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304672">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3754.000000 -272.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47250" ObjectName="SW-CX_SDJ.CX_SDJ_38167SW"/>
     <cge:Meas_Ref ObjectId="304672"/>
    <cge:TPSR_Ref TObjectID="47250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304677">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -269.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47254" ObjectName="SW-CX_SDJ.CX_SDJ_38267SW"/>
     <cge:Meas_Ref ObjectId="304677"/>
    <cge:TPSR_Ref TObjectID="47254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -267.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47238" ObjectName="SW-CX_SDJ.CX_SDJ_38537SW"/>
     <cge:Meas_Ref ObjectId="304658"/>
    <cge:TPSR_Ref TObjectID="47238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304682">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -269.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47258" ObjectName="SW-CX_SDJ.CX_SDJ_38367SW"/>
     <cge:Meas_Ref ObjectId="304682"/>
    <cge:TPSR_Ref TObjectID="47258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -267.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47245" ObjectName="SW-CX_SDJ.CX_SDJ_38467SW"/>
     <cge:Meas_Ref ObjectId="304666"/>
    <cge:TPSR_Ref TObjectID="47245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4565.000000 -268.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 -266.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 -266.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304667">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.000000 -55.000000)" xlink:href="#switch2:shape29_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47246" ObjectName="SW-CX_SDJ.CX_SDJ_3010SW"/>
     <cge:Meas_Ref ObjectId="304667"/>
    <cge:TPSR_Ref TObjectID="47246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -88.000000)" xlink:href="#switch2:shape29_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47239" ObjectName="SW-CX_SDJ.CX_SDJ_3856SW"/>
     <cge:Meas_Ref ObjectId="304659"/>
    <cge:TPSR_Ref TObjectID="47239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -0.000000)" xlink:href="#switch2:shape29_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47241" ObjectName="SW-CX_SDJ.CX_SDJ_386SW"/>
     <cge:Meas_Ref ObjectId="304661"/>
    <cge:TPSR_Ref TObjectID="47241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-304648">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -737.000000)" xlink:href="#switch2:shape29_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47231" ObjectName="SW-CX_SDJ.CX_SDJ_1010SW"/>
     <cge:Meas_Ref ObjectId="304648"/>
    <cge:TPSR_Ref TObjectID="47231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319186">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.642292 3881.605263 -803.308300)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48655" ObjectName="SW-CX_SDJ.XB"/>
     <cge:Meas_Ref ObjectId="319186"/>
    <cge:TPSR_Ref TObjectID="48655"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="165" x="3247" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="165" x="3247" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3198" y="-1196"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3435,-1086 3432,-1089 3432,-1035 3435,-1038 3435,-1086" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3435,-1086 3432,-1089 3581,-1089 3578,-1086 3435,-1086" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3435,-1038 3432,-1035 3581,-1035 3578,-1038 3435,-1038" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3578,-1086 3581,-1089 3581,-1035 3578,-1038 3578,-1086" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3435" y="-1086"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3435" y="-1086"/>
    </a>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SDJ.CX_SDJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="44674"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.789474 -714.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.789474 -714.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="47261" ObjectName="TF-CX_SDJ.CX_SDJ_1T"/>
    <cge:TPSR_Ref TObjectID="47261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -27.000000)" xlink:href="#transformer2:shape72_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -27.000000)" xlink:href="#transformer2:shape72_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4617.000000 -888.000000)" xlink:href="#transformer2:shape72_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4617.000000 -888.000000)" xlink:href="#transformer2:shape72_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -860.000000)" xlink:href="#transformer2:shape75_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -860.000000)" xlink:href="#transformer2:shape75_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SDJ"/>
</svg>