<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-16" aopId="256" id="thSvg" viewBox="3117 -1254 2220 1255">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline fill="none" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 "/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline fill="none" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 "/>
    <polyline fill="none" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 "/>
    <polyline fill="none" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline fill="none" points="5,36 0,46 10,46 5,36 "/>
    <polyline fill="none" points="5,24 0,14 10,14 5,24 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
    <circle cx="24" cy="8" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" r="8.5" stroke-width="1"/>
    <circle cx="34" cy="8" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape95">
    <ellipse cx="20" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="34" x2="29" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="34" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="31" x2="29" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="21" x2="21" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="9" x2="0" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="6" x2="3" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="7" x2="2" y1="4" y2="4"/>
    <polyline fill="none" points="21,19 5,19 5,6 "/>
    <ellipse cx="31" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="20" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="31" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline fill="none" points="1,13 9,1 17,13 "/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline fill="none" points="41,15 41,40 70,40 "/>
    <circle cx="42" cy="16" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1265" width="2230" x="3112" y="-1259"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 1230.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 1215.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3754.000000 1200.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 1230.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.000000 1215.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3884.000000 1200.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 1004.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.000000 989.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4391.000000 974.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5249.000000 906.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5238.000000 891.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5263.000000 876.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5003.000000 870.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4992.000000 855.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 840.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5249.000000 806.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5238.000000 791.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5263.000000 776.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5249.000000 567.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5238.000000 552.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5263.000000 537.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4814.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 198.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 183.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 168.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 206.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 191.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 176.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3429.000000 67.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3418.000000 52.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3443.000000 37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4304.000000 318.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 303.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 288.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -16.000000 7.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 69.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 3.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 65.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4523.000000 50.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -8.000000 2.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 64.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 49.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 874.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 859.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 844.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -75.000000 289.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 1217.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 1201.666667) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 1186.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 1171.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 693.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3530.000000 736.000000) translate(0,12)">温度1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.000000 721.833333) translate(0,12)">温度2（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.000000 707.666667) translate(0,12)">温度3（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4270.000000 653.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 696.000000) translate(0,12)">温度1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 681.833333) translate(0,12)">温度2（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 667.666667) translate(0,12)">温度3（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 1049.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5106.000000 1033.666667) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5131.000000 1018.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 1003.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5090.000000 662.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.000000 646.666667) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5104.000000 631.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.000000 616.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3524.000000 855.850000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3524.000000 840.700000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3530.000000 825.550000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3516.000000 809.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3524.000000 871.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3530.000000 794.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 846.850000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 831.700000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 816.550000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.000000 800.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 862.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 785.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.000000 514.850000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.000000 499.700000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 484.550000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 468.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.000000 530.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 453.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3473.000000 508.250000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3473.000000 492.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3479.000000 476.750000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3465.000000 461.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3473.000000 523.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 458.250000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 442.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5239.000000 426.750000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5225.000000 411.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 473.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 1238.850000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 1223.700000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 1208.550000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4789.000000 1192.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 1254.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 1177.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5249.000000 969.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5238.000000 954.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5263.000000 939.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-30285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -345.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5121" ObjectName="SW-CX_SY.CX_SY_012BK"/>
     <cge:Meas_Ref ObjectId="30285"/>
    <cge:TPSR_Ref TObjectID="5121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4923.000000 -869.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5078" ObjectName="SW-CX_SY.CX_SY_312BK"/>
     <cge:Meas_Ref ObjectId="30242"/>
    <cge:TPSR_Ref TObjectID="5078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30228">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -809.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5064" ObjectName="SW-CX_SY.CX_SY_112BK"/>
     <cge:Meas_Ref ObjectId="30228"/>
    <cge:TPSR_Ref TObjectID="5064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30257">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 -540.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5093" ObjectName="SW-CX_SY.CX_SY_362BK"/>
     <cge:Meas_Ref ObjectId="30257"/>
    <cge:TPSR_Ref TObjectID="5093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30309">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5019.166667 -306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5145" ObjectName="SW-CX_SY.CX_SY_072BK"/>
     <cge:Meas_Ref ObjectId="30309"/>
    <cge:TPSR_Ref TObjectID="5145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30312">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5094.166667 -306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5148" ObjectName="SW-CX_SY.CX_SY_073BK"/>
     <cge:Meas_Ref ObjectId="30312"/>
    <cge:TPSR_Ref TObjectID="5148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5172.166667 -308.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5151" ObjectName="SW-CX_SY.CX_SY_074BK"/>
     <cge:Meas_Ref ObjectId="30315"/>
    <cge:TPSR_Ref TObjectID="5151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30297">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3577.166667 -308.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5133" ObjectName="SW-CX_SY.CX_SY_043BK"/>
     <cge:Meas_Ref ObjectId="30297"/>
    <cge:TPSR_Ref TObjectID="5133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30294">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.166667 -309.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5130" ObjectName="SW-CX_SY.CX_SY_042BK"/>
     <cge:Meas_Ref ObjectId="30294"/>
    <cge:TPSR_Ref TObjectID="5130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.166667 -309.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5127" ObjectName="SW-CX_SY.CX_SY_041BK"/>
     <cge:Meas_Ref ObjectId="30291"/>
    <cge:TPSR_Ref TObjectID="5127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.166667 -306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5136" ObjectName="SW-CX_SY.CX_SY_044BK"/>
     <cge:Meas_Ref ObjectId="30300"/>
    <cge:TPSR_Ref TObjectID="5136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30189">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -980.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5025" ObjectName="SW-CX_SY.CX_SY_101BK"/>
     <cge:Meas_Ref ObjectId="30189"/>
    <cge:TPSR_Ref TObjectID="5025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30216">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.000000 -957.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5052" ObjectName="SW-CX_SY.CX_SY_132BK"/>
     <cge:Meas_Ref ObjectId="30216"/>
    <cge:TPSR_Ref TObjectID="5052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30202">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 -1001.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5038" ObjectName="SW-CX_SY.CX_SY_102BK"/>
     <cge:Meas_Ref ObjectId="30202"/>
    <cge:TPSR_Ref TObjectID="5038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30222">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4320.000000 -958.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5058" ObjectName="SW-CX_SY.CX_SY_133BK"/>
     <cge:Meas_Ref ObjectId="30222"/>
    <cge:TPSR_Ref TObjectID="5058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.152815 4944.000000 -598.471850)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5046" ObjectName="SW-CX_SY.CX_SY_302BK"/>
     <cge:Meas_Ref ObjectId="30210"/>
    <cge:TPSR_Ref TObjectID="5046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30253">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4941.000000 -778.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5089" ObjectName="SW-CX_SY.CX_SY_361BK"/>
     <cge:Meas_Ref ObjectId="30253"/>
    <cge:TPSR_Ref TObjectID="5089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.166667 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5124" ObjectName="SW-CX_SY.CX_SY_039BK"/>
     <cge:Meas_Ref ObjectId="30288"/>
    <cge:TPSR_Ref TObjectID="5124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30199">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5035" ObjectName="SW-CX_SY.CX_SY_001BK"/>
     <cge:Meas_Ref ObjectId="30199"/>
    <cge:TPSR_Ref TObjectID="5035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30213">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 -305.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5049" ObjectName="SW-CX_SY.CX_SY_002BK"/>
     <cge:Meas_Ref ObjectId="30213"/>
    <cge:TPSR_Ref TObjectID="5049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.166667 -305.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5139" ObjectName="SW-CX_SY.CX_SY_069BK"/>
     <cge:Meas_Ref ObjectId="30303"/>
    <cge:TPSR_Ref TObjectID="5139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.166667 -306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5142" ObjectName="SW-CX_SY.CX_SY_071BK"/>
     <cge:Meas_Ref ObjectId="30306"/>
    <cge:TPSR_Ref TObjectID="5142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30249">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4942.000000 -939.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5085" ObjectName="SW-CX_SY.CX_SY_332BK"/>
     <cge:Meas_Ref ObjectId="30249"/>
    <cge:TPSR_Ref TObjectID="5085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30245">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4941.000000 -889.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5081" ObjectName="SW-CX_SY.CX_SY_331BK"/>
     <cge:Meas_Ref ObjectId="30245"/>
    <cge:TPSR_Ref TObjectID="5081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30196">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.152815 4936.000000 -1016.471850)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5032" ObjectName="SW-CX_SY.CX_SY_301BK"/>
     <cge:Meas_Ref ObjectId="30196"/>
    <cge:TPSR_Ref TObjectID="5032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5103" ObjectName="SW-CX_SY.CX_SY_032BK"/>
     <cge:Meas_Ref ObjectId="30267"/>
    <cge:TPSR_Ref TObjectID="5103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30273">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5109" ObjectName="SW-CX_SY.CX_SY_062BK"/>
     <cge:Meas_Ref ObjectId="30273"/>
    <cge:TPSR_Ref TObjectID="5109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30279">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5115" ObjectName="SW-CX_SY.CX_SY_063BK"/>
     <cge:Meas_Ref ObjectId="30279"/>
    <cge:TPSR_Ref TObjectID="5115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5097" ObjectName="SW-CX_SY.CX_SY_033BK"/>
     <cge:Meas_Ref ObjectId="30261"/>
    <cge:TPSR_Ref TObjectID="5097"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SY.CX_SY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="7314"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 -599.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="7316"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 -599.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="7318"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 -599.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5157" ObjectName="TF-CX_SY.CX_SY_1T"/>
    <cge:TPSR_Ref TObjectID="5157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SY.CX_SY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="7321"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -659.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="7323"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -659.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="7325"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -659.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5158" ObjectName="TF-CX_SY.CX_SY_2T"/>
    <cge:TPSR_Ref TObjectID="5158"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-CX_SY.CX_SY_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-896 4070,-896 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10708" ObjectName="BS-CX_SY.CX_SY_1IM"/>
    <cge:TPSR_Ref TObjectID="10708"/></metadata>
   <polyline fill="none" opacity="0" points="3517,-896 4070,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_SY.CX_SY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3489,-422 4308,-422 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5023" ObjectName="BS-CX_SY.CX_SY_9IM"/>
    <cge:TPSR_Ref TObjectID="5023"/></metadata>
   <polyline fill="none" opacity="0" points="3489,-422 4308,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_SY.CX_SY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-1153 4862,-856 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5021" ObjectName="BS-CX_SY.CX_SY_3IM"/>
    <cge:TPSR_Ref TObjectID="5021"/></metadata>
   <polyline fill="none" opacity="0" points="4862,-1153 4862,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_SY.CX_SY_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4863,-836 4863,-480 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5022" ObjectName="BS-CX_SY.CX_SY_3IIM"/>
    <cge:TPSR_Ref TObjectID="5022"/></metadata>
   <polyline fill="none" opacity="0" points="4863,-836 4863,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_SY.CX_SY_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-897 4724,-897 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10707" ObjectName="BS-CX_SY.CX_SY_1IIM"/>
    <cge:TPSR_Ref TObjectID="10707"/></metadata>
   <polyline fill="none" opacity="0" points="4103,-897 4724,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_SY.CX_SY_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-423 5205,-423 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5024" ObjectName="BS-CX_SY.CX_SY_9IIM"/>
    <cge:TPSR_Ref TObjectID="5024"/></metadata>
   <polyline fill="none" opacity="0" points="4364,-423 5205,-423 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_SY.CX_SY_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 -90.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11726" ObjectName="CB-CX_SY.CX_SY_1C"/>
    <cge:TPSR_Ref TObjectID="11726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_SY.CX_SY_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 -91.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11727" ObjectName="CB-CX_SY.CX_SY_2C"/>
    <cge:TPSR_Ref TObjectID="11727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_SY.CX_SY_3C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -90.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11728" ObjectName="CB-CX_SY.CX_SY_3C"/>
    <cge:TPSR_Ref TObjectID="11728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_SY.CX_SY_4C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -90.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11729" ObjectName="CB-CX_SY.CX_SY_4C"/>
    <cge:TPSR_Ref TObjectID="11729"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SY.CX_SY_1TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16899"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4192.000000 -231.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4192.000000 -231.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12089" ObjectName="TF-CX_SY.CX_SY_1TZyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SY.CX_SY_2TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16903"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4432.000000 -234.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4432.000000 -234.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12090" ObjectName="TF-CX_SY.CX_SY_2TZyb"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d3b1a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -600.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d4a010">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -656.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d40dc0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -660.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3db53a0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 5023.000000 -243.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3db64e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5055.000000 -115.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dbe8c0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 5098.000000 -244.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dbfec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5130.000000 -116.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc5990">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 5176.000000 -247.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc6f90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5208.000000 -119.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dcacd0">
    <use class="BV-110KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3588.000000 -1048.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dcefe0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3562.000000 -1080.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ddeee0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 3581.000000 -246.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a937d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3614.000000 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a9bbb0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 3652.000000 -247.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a9cf50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 -119.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aa2a80">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 3718.000000 -247.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aa3e20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -119.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aaeab0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 3503.000000 -244.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aafe50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3535.000000 -116.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ab12e0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3764.000000 -865.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac9060">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3906.000000 -1073.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3af60b0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4310.000000 -1074.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b034f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -1089.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b083c0">
    <use class="BV-110KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4643.000000 -1052.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b0c6d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4618.000000 -1084.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b194b0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4963.500000 -722.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b1d3c0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5140.500000 -733.500000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b2c0d0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 3787.000000 -251.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b2d470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3819.000000 -123.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b3b0d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4216.000000 -357.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b3bbb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4202.000000 -243.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b3c5d0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4215.000000 -502.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b3fdc0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4168.500000 -501.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b41290">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4226.500000 -588.500000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b46260">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4456.000000 -357.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b46d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 -241.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b47c20">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4455.000000 -503.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b4b410">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4408.500000 -502.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b4c8e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4466.500000 -588.500000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b5d5c0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 4881.000000 -243.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b5ebc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 -115.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b66fa0">
    <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -1.000000 4954.000000 -244.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b685a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -116.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b69c90">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4960.500000 -1099.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b85e50">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5137.500000 -1110.500000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bac8c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 -263.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bc30e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -262.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bd6b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -262.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3beef60">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5149.500000 -965.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bfa580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -262.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c219b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4220.000000 -865.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c22470">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -669.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c2fb30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -553.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c44860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 -1053.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c45340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5069.000000 -912.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c45f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 -676.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c46cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5068.000000 -751.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c47a20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5069.000000 -870.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c48790">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5070.000000 -513.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c49500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -551.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c4a270">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3695.000000 -551.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c4afe0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 -1086.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c4bd50">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 -598.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3226.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-29994" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3633.000000 -736.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29994" ObjectName="CX_SY:CX_SY_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-29995" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3633.000000 -721.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29995" ObjectName="CX_SY:CX_SY_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-29996" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3633.000000 -706.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29996" ObjectName="CX_SY:CX_SY_1T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-57379" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4364.000000 -667.333333) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57379" ObjectName="CX_SY:CX_SY_2T_Tmp3"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-30017" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4364.000000 -681.666667) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30017" ObjectName="CX_SY:CX_SY_2T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-30016" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4364.000000 -696.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30016" ObjectName="CX_SY:CX_SY_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62647" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3275.538462 -935.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62647" ObjectName="CX_SY:CX_SY_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-29979" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3796.000000 -1230.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5025"/>
     <cge:Term_Ref ObjectID="7049"/>
    <cge:TPSR_Ref TObjectID="5025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-29980" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3796.000000 -1230.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5025"/>
     <cge:Term_Ref ObjectID="7049"/>
    <cge:TPSR_Ref TObjectID="5025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29976" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3796.000000 -1230.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5025"/>
     <cge:Term_Ref ObjectID="7049"/>
    <cge:TPSR_Ref TObjectID="5025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30105" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4356.000000 -318.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5121"/>
     <cge:Term_Ref ObjectID="7241"/>
    <cge:TPSR_Ref TObjectID="5121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30106" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4356.000000 -318.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5121"/>
     <cge:Term_Ref ObjectID="7241"/>
    <cge:TPSR_Ref TObjectID="5121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30102" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4356.000000 -318.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5121"/>
     <cge:Term_Ref ObjectID="7241"/>
    <cge:TPSR_Ref TObjectID="5121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30071" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5064.000000 -870.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5078"/>
     <cge:Term_Ref ObjectID="7155"/>
    <cge:TPSR_Ref TObjectID="5078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30072" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5064.000000 -870.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5078"/>
     <cge:Term_Ref ObjectID="7155"/>
    <cge:TPSR_Ref TObjectID="5078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30068" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5064.000000 -870.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5078"/>
     <cge:Term_Ref ObjectID="7155"/>
    <cge:TPSR_Ref TObjectID="5078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-57383" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4002.000000 -874.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5064"/>
     <cge:Term_Ref ObjectID="7127"/>
    <cge:TPSR_Ref TObjectID="5064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-57384" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4002.000000 -874.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5064"/>
     <cge:Term_Ref ObjectID="7127"/>
    <cge:TPSR_Ref TObjectID="5064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-57380" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4002.000000 -874.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5064"/>
     <cge:Term_Ref ObjectID="7127"/>
    <cge:TPSR_Ref TObjectID="5064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30091" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -567.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5093"/>
     <cge:Term_Ref ObjectID="7185"/>
    <cge:TPSR_Ref TObjectID="5093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30092" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -567.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5093"/>
     <cge:Term_Ref ObjectID="7185"/>
    <cge:TPSR_Ref TObjectID="5093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30089" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -567.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5093"/>
     <cge:Term_Ref ObjectID="7185"/>
    <cge:TPSR_Ref TObjectID="5093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30145" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5011.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5145"/>
     <cge:Term_Ref ObjectID="7289"/>
    <cge:TPSR_Ref TObjectID="5145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30146" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5011.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5145"/>
     <cge:Term_Ref ObjectID="7289"/>
    <cge:TPSR_Ref TObjectID="5145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30143" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5011.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5145"/>
     <cge:Term_Ref ObjectID="7289"/>
    <cge:TPSR_Ref TObjectID="5145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30150" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5086.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5148"/>
     <cge:Term_Ref ObjectID="7295"/>
    <cge:TPSR_Ref TObjectID="5148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30151" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5086.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5148"/>
     <cge:Term_Ref ObjectID="7295"/>
    <cge:TPSR_Ref TObjectID="5148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30148" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5086.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5148"/>
     <cge:Term_Ref ObjectID="7295"/>
    <cge:TPSR_Ref TObjectID="5148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30155" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5164.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5151"/>
     <cge:Term_Ref ObjectID="7301"/>
    <cge:TPSR_Ref TObjectID="5151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30156" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5164.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5151"/>
     <cge:Term_Ref ObjectID="7301"/>
    <cge:TPSR_Ref TObjectID="5151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30153" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5164.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5151"/>
     <cge:Term_Ref ObjectID="7301"/>
    <cge:TPSR_Ref TObjectID="5151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30125" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3571.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5133"/>
     <cge:Term_Ref ObjectID="7265"/>
    <cge:TPSR_Ref TObjectID="5133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30126" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3571.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5133"/>
     <cge:Term_Ref ObjectID="7265"/>
    <cge:TPSR_Ref TObjectID="5133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30123" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3571.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5133"/>
     <cge:Term_Ref ObjectID="7265"/>
    <cge:TPSR_Ref TObjectID="5133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30120" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3638.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5130"/>
     <cge:Term_Ref ObjectID="7259"/>
    <cge:TPSR_Ref TObjectID="5130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30121" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3638.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5130"/>
     <cge:Term_Ref ObjectID="7259"/>
    <cge:TPSR_Ref TObjectID="5130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30118" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3638.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5130"/>
     <cge:Term_Ref ObjectID="7259"/>
    <cge:TPSR_Ref TObjectID="5130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30115" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5127"/>
     <cge:Term_Ref ObjectID="7253"/>
    <cge:TPSR_Ref TObjectID="5127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30116" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5127"/>
     <cge:Term_Ref ObjectID="7253"/>
    <cge:TPSR_Ref TObjectID="5127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30113" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5127"/>
     <cge:Term_Ref ObjectID="7253"/>
    <cge:TPSR_Ref TObjectID="5127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30130" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3491.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5136"/>
     <cge:Term_Ref ObjectID="7271"/>
    <cge:TPSR_Ref TObjectID="5136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30131" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3491.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5136"/>
     <cge:Term_Ref ObjectID="7271"/>
    <cge:TPSR_Ref TObjectID="5136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30128" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3491.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5136"/>
     <cge:Term_Ref ObjectID="7271"/>
    <cge:TPSR_Ref TObjectID="5136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30001" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4160.000000 -1217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5038"/>
     <cge:Term_Ref ObjectID="7075"/>
    <cge:TPSR_Ref TObjectID="5038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30002" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4160.000000 -1217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5038"/>
     <cge:Term_Ref ObjectID="7075"/>
    <cge:TPSR_Ref TObjectID="5038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29998" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4160.000000 -1217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5038"/>
     <cge:Term_Ref ObjectID="7075"/>
    <cge:TPSR_Ref TObjectID="5038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-30003" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4160.000000 -1217.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5038"/>
     <cge:Term_Ref ObjectID="7075"/>
    <cge:TPSR_Ref TObjectID="5038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30007" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5139.000000 -663.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5046"/>
     <cge:Term_Ref ObjectID="7091"/>
    <cge:TPSR_Ref TObjectID="5046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30008" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5139.000000 -663.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30008" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5046"/>
     <cge:Term_Ref ObjectID="7091"/>
    <cge:TPSR_Ref TObjectID="5046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30004" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5139.000000 -663.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5046"/>
     <cge:Term_Ref ObjectID="7091"/>
    <cge:TPSR_Ref TObjectID="5046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-30009" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5139.000000 -663.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5046"/>
     <cge:Term_Ref ObjectID="7091"/>
    <cge:TPSR_Ref TObjectID="5046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30086" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -806.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5089"/>
     <cge:Term_Ref ObjectID="7177"/>
    <cge:TPSR_Ref TObjectID="5089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30087" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -806.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5089"/>
     <cge:Term_Ref ObjectID="7177"/>
    <cge:TPSR_Ref TObjectID="5089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30084" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -806.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5089"/>
     <cge:Term_Ref ObjectID="7177"/>
    <cge:TPSR_Ref TObjectID="5089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30110" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3775.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5124"/>
     <cge:Term_Ref ObjectID="7247"/>
    <cge:TPSR_Ref TObjectID="5124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30111" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3775.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5124"/>
     <cge:Term_Ref ObjectID="7247"/>
    <cge:TPSR_Ref TObjectID="5124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30108" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3775.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5124"/>
     <cge:Term_Ref ObjectID="7247"/>
    <cge:TPSR_Ref TObjectID="5124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-29991" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4031.000000 -205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5035"/>
     <cge:Term_Ref ObjectID="7069"/>
    <cge:TPSR_Ref TObjectID="5035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-29992" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4031.000000 -205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5035"/>
     <cge:Term_Ref ObjectID="7069"/>
    <cge:TPSR_Ref TObjectID="5035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29988" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4031.000000 -205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5035"/>
     <cge:Term_Ref ObjectID="7069"/>
    <cge:TPSR_Ref TObjectID="5035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30013" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4646.000000 -196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5049"/>
     <cge:Term_Ref ObjectID="7097"/>
    <cge:TPSR_Ref TObjectID="5049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30014" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4646.000000 -196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5049"/>
     <cge:Term_Ref ObjectID="7097"/>
    <cge:TPSR_Ref TObjectID="5049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30010" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4646.000000 -196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5049"/>
     <cge:Term_Ref ObjectID="7097"/>
    <cge:TPSR_Ref TObjectID="5049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30135" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4879.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5139"/>
     <cge:Term_Ref ObjectID="7277"/>
    <cge:TPSR_Ref TObjectID="5139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30136" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4879.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5139"/>
     <cge:Term_Ref ObjectID="7277"/>
    <cge:TPSR_Ref TObjectID="5139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30133" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4879.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5139"/>
     <cge:Term_Ref ObjectID="7277"/>
    <cge:TPSR_Ref TObjectID="5139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30140" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4941.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5142"/>
     <cge:Term_Ref ObjectID="7283"/>
    <cge:TPSR_Ref TObjectID="5142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30141" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4941.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5142"/>
     <cge:Term_Ref ObjectID="7283"/>
    <cge:TPSR_Ref TObjectID="5142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30138" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4941.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5142"/>
     <cge:Term_Ref ObjectID="7283"/>
    <cge:TPSR_Ref TObjectID="5142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30081" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -969.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5085"/>
     <cge:Term_Ref ObjectID="7169"/>
    <cge:TPSR_Ref TObjectID="5085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30082" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -969.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5085"/>
     <cge:Term_Ref ObjectID="7169"/>
    <cge:TPSR_Ref TObjectID="5085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30079" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -969.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5085"/>
     <cge:Term_Ref ObjectID="7169"/>
    <cge:TPSR_Ref TObjectID="5085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-30076" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -906.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5081"/>
     <cge:Term_Ref ObjectID="7161"/>
    <cge:TPSR_Ref TObjectID="5081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30077" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -906.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5081"/>
     <cge:Term_Ref ObjectID="7161"/>
    <cge:TPSR_Ref TObjectID="5081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30074" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5306.000000 -906.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5081"/>
     <cge:Term_Ref ObjectID="7161"/>
    <cge:TPSR_Ref TObjectID="5081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-29985" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5166.000000 -1048.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5032"/>
     <cge:Term_Ref ObjectID="7063"/>
    <cge:TPSR_Ref TObjectID="5032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-29986" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5166.000000 -1048.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5032"/>
     <cge:Term_Ref ObjectID="7063"/>
    <cge:TPSR_Ref TObjectID="5032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29982" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5166.000000 -1048.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5032"/>
     <cge:Term_Ref ObjectID="7063"/>
    <cge:TPSR_Ref TObjectID="5032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-29987" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5166.000000 -1048.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5032"/>
     <cge:Term_Ref ObjectID="7063"/>
    <cge:TPSR_Ref TObjectID="5032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30095" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3957.000000 -61.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5097"/>
     <cge:Term_Ref ObjectID="7193"/>
    <cge:TPSR_Ref TObjectID="5097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30094" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3957.000000 -61.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5097"/>
     <cge:Term_Ref ObjectID="7193"/>
    <cge:TPSR_Ref TObjectID="5097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30097" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4151.000000 -61.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5103"/>
     <cge:Term_Ref ObjectID="7205"/>
    <cge:TPSR_Ref TObjectID="5103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30096" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4151.000000 -61.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5103"/>
     <cge:Term_Ref ObjectID="7205"/>
    <cge:TPSR_Ref TObjectID="5103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30099" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4565.000000 -61.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5109"/>
     <cge:Term_Ref ObjectID="7217"/>
    <cge:TPSR_Ref TObjectID="5109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30098" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4565.000000 -61.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5109"/>
     <cge:Term_Ref ObjectID="7217"/>
    <cge:TPSR_Ref TObjectID="5109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-30101" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4756.000000 -61.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5115"/>
     <cge:Term_Ref ObjectID="7229"/>
    <cge:TPSR_Ref TObjectID="5115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-30100" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4756.000000 -61.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5115"/>
     <cge:Term_Ref ObjectID="7229"/>
    <cge:TPSR_Ref TObjectID="5115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-30018" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4364.000000 -653.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5158"/>
     <cge:Term_Ref ObjectID="7320"/>
    <cge:TPSR_Ref TObjectID="5158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-30037" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4860.000000 -1253.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5021"/>
     <cge:Term_Ref ObjectID="7045"/>
    <cge:TPSR_Ref TObjectID="5021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-30038" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4860.000000 -1253.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5021"/>
     <cge:Term_Ref ObjectID="7045"/>
    <cge:TPSR_Ref TObjectID="5021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-30039" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4860.000000 -1253.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5021"/>
     <cge:Term_Ref ObjectID="7045"/>
    <cge:TPSR_Ref TObjectID="5021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-30043" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4860.000000 -1253.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5021"/>
     <cge:Term_Ref ObjectID="7045"/>
    <cge:TPSR_Ref TObjectID="5021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-30040" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4860.000000 -1253.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5021"/>
     <cge:Term_Ref ObjectID="7045"/>
    <cge:TPSR_Ref TObjectID="5021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-30036" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4860.000000 -1253.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30036" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5021"/>
     <cge:Term_Ref ObjectID="7045"/>
    <cge:TPSR_Ref TObjectID="5021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-30053" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3526.000000 -521.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5023"/>
     <cge:Term_Ref ObjectID="7047"/>
    <cge:TPSR_Ref TObjectID="5023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-30054" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3526.000000 -521.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5023"/>
     <cge:Term_Ref ObjectID="7047"/>
    <cge:TPSR_Ref TObjectID="5023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-30055" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3526.000000 -521.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5023"/>
     <cge:Term_Ref ObjectID="7047"/>
    <cge:TPSR_Ref TObjectID="5023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-30059" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3526.000000 -521.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5023"/>
     <cge:Term_Ref ObjectID="7047"/>
    <cge:TPSR_Ref TObjectID="5023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-30056" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3526.000000 -521.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5023"/>
     <cge:Term_Ref ObjectID="7047"/>
    <cge:TPSR_Ref TObjectID="5023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-30061" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5296.000000 -471.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5024"/>
     <cge:Term_Ref ObjectID="7048"/>
    <cge:TPSR_Ref TObjectID="5024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-30062" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5296.000000 -471.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5024"/>
     <cge:Term_Ref ObjectID="7048"/>
    <cge:TPSR_Ref TObjectID="5024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-30063" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5296.000000 -471.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5024"/>
     <cge:Term_Ref ObjectID="7048"/>
    <cge:TPSR_Ref TObjectID="5024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-30067" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5296.000000 -471.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5024"/>
     <cge:Term_Ref ObjectID="7048"/>
    <cge:TPSR_Ref TObjectID="5024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-30064" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5296.000000 -471.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5024"/>
     <cge:Term_Ref ObjectID="7048"/>
    <cge:TPSR_Ref TObjectID="5024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-30045" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -529.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5022"/>
     <cge:Term_Ref ObjectID="7046"/>
    <cge:TPSR_Ref TObjectID="5022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-30046" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -529.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5022"/>
     <cge:Term_Ref ObjectID="7046"/>
    <cge:TPSR_Ref TObjectID="5022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-30047" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -529.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30047" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5022"/>
     <cge:Term_Ref ObjectID="7046"/>
    <cge:TPSR_Ref TObjectID="5022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-30051" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -529.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5022"/>
     <cge:Term_Ref ObjectID="7046"/>
    <cge:TPSR_Ref TObjectID="5022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-30048" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -529.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5022"/>
     <cge:Term_Ref ObjectID="7046"/>
    <cge:TPSR_Ref TObjectID="5022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-30044" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -529.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5022"/>
     <cge:Term_Ref ObjectID="7046"/>
    <cge:TPSR_Ref TObjectID="5022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-57387" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3578.000000 -870.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10708"/>
     <cge:Term_Ref ObjectID="14867"/>
    <cge:TPSR_Ref TObjectID="10708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-57388" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3578.000000 -870.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10708"/>
     <cge:Term_Ref ObjectID="14867"/>
    <cge:TPSR_Ref TObjectID="10708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-57389" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3578.000000 -870.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10708"/>
     <cge:Term_Ref ObjectID="14867"/>
    <cge:TPSR_Ref TObjectID="10708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57393" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3578.000000 -870.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10708"/>
     <cge:Term_Ref ObjectID="14867"/>
    <cge:TPSR_Ref TObjectID="10708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-57390" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3578.000000 -870.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10708"/>
     <cge:Term_Ref ObjectID="14867"/>
    <cge:TPSR_Ref TObjectID="10708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57386" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3578.000000 -870.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10708"/>
     <cge:Term_Ref ObjectID="14867"/>
    <cge:TPSR_Ref TObjectID="10708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-57395" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4671.000000 -863.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10707"/>
     <cge:Term_Ref ObjectID="14866"/>
    <cge:TPSR_Ref TObjectID="10707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-57396" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4671.000000 -863.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10707"/>
     <cge:Term_Ref ObjectID="14866"/>
    <cge:TPSR_Ref TObjectID="10707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-57397" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4671.000000 -863.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10707"/>
     <cge:Term_Ref ObjectID="14866"/>
    <cge:TPSR_Ref TObjectID="10707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57401" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4671.000000 -863.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10707"/>
     <cge:Term_Ref ObjectID="14866"/>
    <cge:TPSR_Ref TObjectID="10707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-57398" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4671.000000 -863.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10707"/>
     <cge:Term_Ref ObjectID="14866"/>
    <cge:TPSR_Ref TObjectID="10707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57394" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4671.000000 -863.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10707"/>
     <cge:Term_Ref ObjectID="14866"/>
    <cge:TPSR_Ref TObjectID="10707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-29997" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3633.000000 -692.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5157"/>
     <cge:Term_Ref ObjectID="7317"/>
    <cge:TPSR_Ref TObjectID="5157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-30031" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4345.000000 -1233.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5058"/>
     <cge:Term_Ref ObjectID="7115"/>
    <cge:TPSR_Ref TObjectID="5058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-30032" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4345.000000 -1233.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5058"/>
     <cge:Term_Ref ObjectID="7115"/>
    <cge:TPSR_Ref TObjectID="5058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-30028" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4345.000000 -1233.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5058"/>
     <cge:Term_Ref ObjectID="7115"/>
    <cge:TPSR_Ref TObjectID="5058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-30023" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3935.000000 -1237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30023" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5052"/>
     <cge:Term_Ref ObjectID="7103"/>
    <cge:TPSR_Ref TObjectID="5052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-30024" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3935.000000 -1237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5052"/>
     <cge:Term_Ref ObjectID="7103"/>
    <cge:TPSR_Ref TObjectID="5052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-30020" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3935.000000 -1237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5052"/>
     <cge:Term_Ref ObjectID="7103"/>
    <cge:TPSR_Ref TObjectID="5052"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3238" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <polygon fill="rgb(255,255,255)" points="3408,-1183 3405,-1186 3405,-1128 3408,-1131 3408,-1183" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3408,-1183 3405,-1186 3473,-1186 3470,-1183 3408,-1183" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3408,-1131 3405,-1128 3473,-1128 3470,-1131 3408,-1131" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3470,-1183 3473,-1186 3473,-1128 3470,-1131 3470,-1183" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="52" stroke="rgb(255,255,255)" width="62" x="3408" y="-1183"/>
     <rect height="52" qtmmishow="hidden" stroke="rgb(0,0,0)" width="62" x="3408" y="-1183"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <rect height="22" qtmmishow="hidden" width="22" x="5217" y="-534"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="5217" y="-534"/></g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/></g>
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/></g>
   <g href="AVC上营站.svg" style="fill-opacity:0"><rect height="52" qtmmishow="hidden" stroke="rgb(0,0,0)" width="62" x="3408" y="-1183"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="5217" y="-534"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3c42000">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5148.000000 -805.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c426f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5154.000000 -966.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c430d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5154.000000 -567.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_YL1Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5144.000000 -1126.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12110" ObjectName="EC-CX_SY.CX_SY_YL1Ld"/>
    <cge:TPSR_Ref TObjectID="12110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_332Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5227.000000 -940.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12102" ObjectName="EC-CX_SY.CX_SY_332Ld"/>
    <cge:TPSR_Ref TObjectID="12102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_331Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5226.000000 -890.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12103" ObjectName="EC-CX_SY.CX_SY_331Ld"/>
    <cge:TPSR_Ref TObjectID="12103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_361Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5226.000000 -779.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12104" ObjectName="EC-CX_SY.CX_SY_361Ld"/>
    <cge:TPSR_Ref TObjectID="12104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_362Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5228.000000 -541.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12105" ObjectName="EC-CX_SY.CX_SY_362Ld"/>
    <cge:TPSR_Ref TObjectID="12105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_YL2Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5145.000000 -489.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12111" ObjectName="EC-CX_SY.CX_SY_YL2Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4493.000000 -1145.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3665.000000 -1144.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_044Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.000000 -75.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12100" ObjectName="EC-CX_SY.CX_SY_044Ld"/>
    <cge:TPSR_Ref TObjectID="12100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_043Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3577.000000 -77.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12099" ObjectName="EC-CX_SY.CX_SY_043Ld"/>
    <cge:TPSR_Ref TObjectID="12099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_042Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -78.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12098" ObjectName="EC-CX_SY.CX_SY_042Ld"/>
    <cge:TPSR_Ref TObjectID="12098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_041Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -78.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12097" ObjectName="EC-CX_SY.CX_SY_041Ld"/>
    <cge:TPSR_Ref TObjectID="12097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_039Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 -82.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12096" ObjectName="EC-CX_SY.CX_SY_039Ld"/>
    <cge:TPSR_Ref TObjectID="12096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_YL5Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -105.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12113" ObjectName="EC-CX_SY.CX_SY_YL5Ld"/>
    <cge:TPSR_Ref TObjectID="12113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_069Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -74.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12091" ObjectName="EC-CX_SY.CX_SY_069Ld"/>
    <cge:TPSR_Ref TObjectID="12091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_071Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -75.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12092" ObjectName="EC-CX_SY.CX_SY_071Ld"/>
    <cge:TPSR_Ref TObjectID="12092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_072Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5019.000000 -74.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12093" ObjectName="EC-CX_SY.CX_SY_072Ld"/>
    <cge:TPSR_Ref TObjectID="12093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_073Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5094.000000 -75.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12094" ObjectName="EC-CX_SY.CX_SY_073Ld"/>
    <cge:TPSR_Ref TObjectID="12094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_074Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5172.000000 -78.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12095" ObjectName="EC-CX_SY.CX_SY_074Ld"/>
    <cge:TPSR_Ref TObjectID="12095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SY.CX_SY_YL6Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4814.000000 -106.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12115" ObjectName="EC-CX_SY.CX_SY_YL6Ld"/>
    <cge:TPSR_Ref TObjectID="12115"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_30faa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-670 3596,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3c4bd50@0" ObjectIDND1="5157@x" ObjectIDND2="5031@x" ObjectIDZND0="g_2d3b1a0@0" Pin0InfoVect0LinkObjId="g_2d3b1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c4bd50_0" Pin1InfoVect1LinkObjId="g_3dd4b60_0" Pin1InfoVect2LinkObjId="SW-30195_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-670 3596,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d305e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-610 3568,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3663bf0@0" ObjectIDZND0="5031@0" Pin0InfoVect0LinkObjId="SW-30195_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3663bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-610 3568,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cce600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-660 3568,-670 3596,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="5031@1" ObjectIDZND0="g_2d3b1a0@0" ObjectIDZND1="g_3c4bd50@0" ObjectIDZND2="5157@x" Pin0InfoVect0LinkObjId="g_2d3b1a0_0" Pin0InfoVect1LinkObjId="g_3c4bd50_0" Pin0InfoVect2LinkObjId="g_3dd4b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-660 3568,-670 3596,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d2ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-928 3552,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10708@0" ObjectIDND1="5069@x" ObjectIDZND0="5070@1" Pin0InfoVect0LinkObjId="SW-30233_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-57017_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-928 3552,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cce9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3516,-928 3503,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5070@0" ObjectIDZND0="g_30f7880@0" Pin0InfoVect0LinkObjId="g_30f7880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30233_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3516,-928 3503,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d2cb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-895 3569,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10708@0" ObjectIDZND0="5069@x" ObjectIDZND1="5070@x" Pin0InfoVect0LinkObjId="SW-57017_0" Pin0InfoVect1LinkObjId="SW-30233_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-895 3569,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3a90520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-895 4045,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10708@0" ObjectIDZND0="5065@1" Pin0InfoVect0LinkObjId="SW-30229_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-895 4045,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d3fbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-766 4045,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5067@0" ObjectIDZND0="g_2d3fe20@0" Pin0InfoVect0LinkObjId="g_2d3fe20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30231_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-766 4045,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31ad470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4124,-896 4124,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10707@0" ObjectIDZND0="5066@1" Pin0InfoVect0LinkObjId="SW-30230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3adbf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4124,-896 4124,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31afd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4124,-767 4124,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5068@0" ObjectIDZND0="g_31affa0@0" Pin0InfoVect0LinkObjId="g_31affa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4124,-767 4124,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da1160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4046,-819 4070,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5065@x" ObjectIDND1="5067@x" ObjectIDZND0="5064@1" Pin0InfoVect0LinkObjId="SW-30228_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30229_0" Pin1InfoVect1LinkObjId="SW-30231_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4046,-819 4070,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da13c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-819 4124,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5064@0" ObjectIDZND0="5066@x" ObjectIDZND1="5068@x" Pin0InfoVect0LinkObjId="SW-30230_0" Pin0InfoVect1LinkObjId="SW-30232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30228_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-819 4124,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da1620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3684,-559 3699,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5157@x" ObjectIDND1="5037@x" ObjectIDZND0="g_3c4a270@0" Pin0InfoVect0LinkObjId="g_3c4a270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3dd4b60_0" Pin1InfoVect1LinkObjId="SW-30201_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3684,-559 3699,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3da7ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-550 4894,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5022@0" ObjectIDZND0="5094@0" Pin0InfoVect0LinkObjId="SW-30258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-550 4894,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3da8250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4952,-550 4930,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5093@1" ObjectIDZND0="5094@1" Pin0InfoVect0LinkObjId="SW-30258_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4952,-550 4930,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3daafc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-421 4278,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5023@0" ObjectIDZND0="5122@1" Pin0InfoVect0LinkObjId="SW-30286_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-421 4278,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dab220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-366 4278,-355 4324,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5122@0" ObjectIDZND0="5121@1" Pin0InfoVect0LinkObjId="SW-30285_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-366 4278,-355 4324,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dadd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-422 4396,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5024@0" ObjectIDZND0="5123@1" Pin0InfoVect0LinkObjId="SW-30287_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-422 4396,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dadf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-367 4396,-355 4351,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5123@0" ObjectIDZND0="5121@0" Pin0InfoVect0LinkObjId="SW-30285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-367 4396,-355 4351,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db5dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-313 5028,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5145@0" ObjectIDZND0="g_3db53a0@1" Pin0InfoVect0LinkObjId="g_3db53a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-313 5028,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db6020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-248 5028,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3db53a0@0" ObjectIDZND0="5147@1" Pin0InfoVect0LinkObjId="SW-30311_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3db53a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-248 5028,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db6280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-187 5062,-187 5062,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12093@x" ObjectIDND1="5147@x" ObjectIDZND0="g_3db64e0@0" Pin0InfoVect0LinkObjId="g_3db64e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_072Ld_0" Pin1InfoVect1LinkObjId="SW-30311_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-187 5062,-187 5062,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db7250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-422 5028,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5024@0" ObjectIDZND0="5146@1" Pin0InfoVect0LinkObjId="SW-30310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-422 5028,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db74b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-364 5028,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5146@0" ObjectIDZND0="5145@1" Pin0InfoVect0LinkObjId="SW-30309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-364 5028,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dbf2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-314 5104,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5148@0" ObjectIDZND0="g_3dbe8c0@1" Pin0InfoVect0LinkObjId="g_3dbe8c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30312_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-314 5104,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dbf540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-249 5103,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3dbe8c0@0" ObjectIDZND0="5150@1" Pin0InfoVect0LinkObjId="SW-30314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dbe8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-249 5103,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dbf7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-188 5137,-188 5137,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12094@x" ObjectIDND1="5150@x" ObjectIDZND0="g_3dbfec0@0" Pin0InfoVect0LinkObjId="g_3dbfec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_073Ld_0" Pin1InfoVect1LinkObjId="SW-30314_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-188 5137,-188 5137,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dbfa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-202 5103,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5150@0" ObjectIDZND0="g_3dbfec0@0" ObjectIDZND1="12094@x" Pin0InfoVect0LinkObjId="g_3dbfec0_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-202 5103,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dbfc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-188 5103,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3dbfec0@0" ObjectIDND1="5150@x" ObjectIDZND0="12094@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3dbfec0_0" Pin1InfoVect1LinkObjId="SW-30314_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-188 5103,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc0c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-422 5103,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5024@0" ObjectIDZND0="5149@1" Pin0InfoVect0LinkObjId="SW-30313_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-422 5103,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc0e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-365 5103,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5149@0" ObjectIDZND0="5148@1" Pin0InfoVect0LinkObjId="SW-30312_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30313_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-365 5103,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc63b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-316 5181,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5151@0" ObjectIDZND0="g_3dc5990@1" Pin0InfoVect0LinkObjId="g_3dc5990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-316 5181,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc6610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-252 5181,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3dc5990@0" ObjectIDZND0="5153@1" Pin0InfoVect0LinkObjId="SW-30317_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dc5990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-252 5181,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc6870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-191 5215,-191 5215,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12095@x" ObjectIDND1="5153@x" ObjectIDZND0="g_3dc6f90@0" Pin0InfoVect0LinkObjId="g_3dc6f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_074Ld_0" Pin1InfoVect1LinkObjId="SW-30317_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-191 5215,-191 5215,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc6ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-205 5181,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5153@0" ObjectIDZND0="g_3dc6f90@0" ObjectIDZND1="12095@x" Pin0InfoVect0LinkObjId="g_3dc6f90_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_074Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-205 5181,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc6d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-191 5181,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3dc6f90@0" ObjectIDND1="5153@x" ObjectIDZND0="12095@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3dc6f90_0" Pin1InfoVect1LinkObjId="SW-30317_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-191 5181,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc7d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-422 5181,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5024@0" ObjectIDZND0="5152@1" Pin0InfoVect0LinkObjId="SW-30316_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-422 5181,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc7f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-370 5181,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5152@0" ObjectIDZND0="5151@1" Pin0InfoVect0LinkObjId="SW-30315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30316_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-370 5181,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dcaa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-1007 3569,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3dcacd0@0" ObjectIDND1="5069@x" ObjectIDND2="10576@x" ObjectIDZND0="g_3dcefe0@0" Pin0InfoVect0LinkObjId="g_3dcefe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dcacd0_0" Pin1InfoVect1LinkObjId="SW-57017_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-1007 3569,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dcc270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-1008 3593,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3dcefe0@0" ObjectIDND1="5069@x" ObjectIDND2="10576@x" ObjectIDZND0="g_3dcacd0@0" Pin0InfoVect0LinkObjId="g_3dcacd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dcefe0_0" Pin1InfoVect1LinkObjId="SW-57017_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-1008 3593,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dced80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-927 3569,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10708@0" ObjectIDND1="5070@x" ObjectIDZND0="5069@0" Pin0InfoVect0LinkObjId="SW-57017_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-30233_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-927 3569,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd2800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-1008 3553,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3dcacd0@0" ObjectIDND1="g_3dcefe0@0" ObjectIDND2="5069@x" ObjectIDZND0="10576@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dcacd0_0" Pin1InfoVect1LinkObjId="g_3dcefe0_0" Pin1InfoVect2LinkObjId="SW-57017_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-1008 3553,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd2a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-1008 3504,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10576@0" ObjectIDZND0="g_3dd2cc0@0" Pin0InfoVect0LinkObjId="g_3dd2cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-1008 3504,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd3750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-982 3569,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5069@1" ObjectIDZND0="g_3dcacd0@0" ObjectIDZND1="g_3dcefe0@0" ObjectIDZND2="10576@x" Pin0InfoVect0LinkObjId="g_3dcacd0_0" Pin0InfoVect1LinkObjId="g_3dcefe0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-982 3569,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd39b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-714 4135,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_2d40dc0@0" ObjectIDZND0="g_2d4a010@0" ObjectIDZND1="5045@x" ObjectIDZND2="5158@x" Pin0InfoVect0LinkObjId="g_2d4a010_0" Pin0InfoVect1LinkObjId="SW-30209_0" Pin0InfoVect2LinkObjId="g_3c23350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d40dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-714 4135,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd3c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-668 4108,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3dd40d0@0" ObjectIDZND0="5045@0" Pin0InfoVect0LinkObjId="SW-30209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dd40d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-668 4108,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd3e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-717 4108,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="5045@1" ObjectIDZND0="g_2d4a010@0" ObjectIDZND1="g_2d40dc0@0" ObjectIDZND2="5158@x" Pin0InfoVect0LinkObjId="g_2d4a010_0" Pin0InfoVect1LinkObjId="g_2d40dc0_0" Pin0InfoVect2LinkObjId="g_3c23350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-717 4108,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd4b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3619,-670 3687,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_3c4bd50@0" ObjectIDND1="g_2d3b1a0@0" ObjectIDND2="5031@x" ObjectIDZND0="5157@x" Pin0InfoVect0LinkObjId="g_3dd4dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c4bd50_0" Pin1InfoVect1LinkObjId="g_2d3b1a0_0" Pin1InfoVect2LinkObjId="SW-30195_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3619,-670 3687,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd4dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3619,-653 3619,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3c4bd50@0" ObjectIDZND0="5157@x" ObjectIDZND1="g_2d3b1a0@0" ObjectIDZND2="5031@x" Pin0InfoVect0LinkObjId="g_3dd4b60_0" Pin0InfoVect1LinkObjId="g_2d3b1a0_0" Pin0InfoVect2LinkObjId="SW-30195_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c4bd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3619,-653 3619,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd5020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3619,-670 3596,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3c4bd50@0" ObjectIDND1="5157@x" ObjectIDZND0="g_2d3b1a0@0" ObjectIDZND1="5031@x" Pin0InfoVect0LinkObjId="g_2d3b1a0_0" Pin0InfoVect1LinkObjId="SW-30195_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c4bd50_0" Pin1InfoVect1LinkObjId="g_3dd4b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3619,-670 3596,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ddf900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-251 3586,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ddeee0@0" ObjectIDZND0="5135@x" Pin0InfoVect0LinkObjId="SW-30299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ddeee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-251 3586,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ddfb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-190 3621,-190 3621,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12099@x" ObjectIDND1="5135@x" ObjectIDZND0="g_3a937d0@0" Pin0InfoVect0LinkObjId="g_3a937d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_043Ld_0" Pin1InfoVect1LinkObjId="SW-30299_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-190 3621,-190 3621,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ddfdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-204 3586,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5135@x" ObjectIDZND0="g_3a937d0@0" ObjectIDZND1="12099@x" Pin0InfoVect0LinkObjId="g_3a937d0_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-204 3586,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-190 3586,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3a937d0@0" ObjectIDND1="5135@x" ObjectIDZND0="12099@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a937d0_0" Pin1InfoVect1LinkObjId="SW-30299_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-190 3586,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a94540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-422 3586,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5023@0" ObjectIDZND0="5134@x" Pin0InfoVect0LinkObjId="SW-30298_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-422 3586,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a947a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-367 3586,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5134@x" ObjectIDZND0="5133@1" Pin0InfoVect0LinkObjId="SW-30297_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-367 3586,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a9c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-252 3657,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3a9bbb0@0" ObjectIDZND0="5132@x" Pin0InfoVect0LinkObjId="SW-30296_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a9bbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-252 3657,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a9c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-191 3691,-191 3691,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12098@x" ObjectIDND1="5132@x" ObjectIDZND0="g_3a9cf50@0" Pin0InfoVect0LinkObjId="g_3a9cf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_042Ld_0" Pin1InfoVect1LinkObjId="SW-30296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-191 3691,-191 3691,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a9ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-205 3657,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5132@x" ObjectIDZND0="g_3a9cf50@0" ObjectIDZND1="12098@x" Pin0InfoVect0LinkObjId="g_3a9cf50_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_042Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-205 3657,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a9ccf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-191 3657,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3a9cf50@0" ObjectIDND1="5132@x" ObjectIDZND0="12098@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_042Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a9cf50_0" Pin1InfoVect1LinkObjId="SW-30296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-191 3657,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a9dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-421 3657,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5023@0" ObjectIDZND0="5131@x" Pin0InfoVect0LinkObjId="SW-30295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-421 3657,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a9df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-368 3657,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5131@x" ObjectIDZND0="5130@1" Pin0InfoVect0LinkObjId="SW-30294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-368 3657,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa34a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-252 3723,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3aa2a80@0" ObjectIDZND0="5129@x" Pin0InfoVect0LinkObjId="SW-30293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3aa2a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-252 3723,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa3700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-191 3757,-191 3757,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12097@x" ObjectIDND1="5129@x" ObjectIDZND0="g_3aa3e20@0" Pin0InfoVect0LinkObjId="g_3aa3e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_041Ld_0" Pin1InfoVect1LinkObjId="SW-30293_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-191 3757,-191 3757,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa3960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-205 3723,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5129@x" ObjectIDZND0="g_3aa3e20@0" ObjectIDZND1="12097@x" Pin0InfoVect0LinkObjId="g_3aa3e20_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_041Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-205 3723,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa3bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-191 3723,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3aa3e20@0" ObjectIDND1="5129@x" ObjectIDZND0="12097@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_041Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3aa3e20_0" Pin1InfoVect1LinkObjId="SW-30293_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-191 3723,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa4b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-421 3723,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5023@0" ObjectIDZND0="5128@x" Pin0InfoVect0LinkObjId="SW-30292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-421 3723,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aa4df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-368 3723,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5128@x" ObjectIDZND0="5127@1" Pin0InfoVect0LinkObjId="SW-30291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-368 3723,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aaf4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-249 3508,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3aaeab0@0" ObjectIDZND0="5138@x" Pin0InfoVect0LinkObjId="SW-30302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3aaeab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-249 3508,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aaf730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-188 3542,-188 3542,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12100@x" ObjectIDND1="5138@x" ObjectIDZND0="g_3aafe50@0" Pin0InfoVect0LinkObjId="g_3aafe50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_044Ld_0" Pin1InfoVect1LinkObjId="SW-30302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-188 3542,-188 3542,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aaf990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-202 3508,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5138@x" ObjectIDZND0="g_3aafe50@0" ObjectIDZND1="12100@x" Pin0InfoVect0LinkObjId="g_3aafe50_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-202 3508,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aafbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-188 3508,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3aafe50@0" ObjectIDND1="5138@x" ObjectIDZND0="12100@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3aafe50_0" Pin1InfoVect1LinkObjId="SW-30302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-188 3508,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ab0bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-421 3508,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5023@0" ObjectIDZND0="5137@x" Pin0InfoVect0LinkObjId="SW-30301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-421 3508,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ab0e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-365 3508,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5137@x" ObjectIDZND0="5136@1" Pin0InfoVect0LinkObjId="SW-30300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-365 3508,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ab1080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-895 3656,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="10708@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2d3b1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-895 3656,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ab4b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3739,-1108 3727,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5030@0" ObjectIDZND0="g_3ab4da0@0" Pin0InfoVect0LinkObjId="g_3ab4da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3739,-1108 3727,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3abab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-1040 3726,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5029@0" ObjectIDZND0="g_3abadf0@0" Pin0InfoVect0LinkObjId="g_3abadf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-1040 3726,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3abb880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-1040 3776,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5025@x" ObjectIDND1="5027@x" ObjectIDZND0="5029@1" Pin0InfoVect0LinkObjId="SW-30193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30189_0" Pin1InfoVect1LinkObjId="SW-30191_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-1040 3776,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3abbae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-1040 3793,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5025@x" ObjectIDND1="5029@x" ObjectIDZND0="5027@0" Pin0InfoVect0LinkObjId="SW-30191_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30189_0" Pin1InfoVect1LinkObjId="SW-30193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-1040 3793,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ac0840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-967 3727,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5028@0" ObjectIDZND0="g_3ac0aa0@0" Pin0InfoVect0LinkObjId="g_3ac0aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-967 3727,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ac1530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-967 3776,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5025@x" ObjectIDND1="5026@x" ObjectIDZND0="5028@1" Pin0InfoVect0LinkObjId="SW-30192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30189_0" Pin1InfoVect1LinkObjId="SW-30190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-967 3776,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ac4040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-895 3926,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10708@0" ObjectIDZND0="5053@0" Pin0InfoVect0LinkObjId="SW-30217_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-895 3926,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ac42a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-946 3926,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5053@1" ObjectIDZND0="5052@x" ObjectIDZND1="5055@x" Pin0InfoVect0LinkObjId="SW-30216_0" Pin0InfoVect1LinkObjId="SW-30219_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30217_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-946 3926,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ac6550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-955 3926,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5053@x" ObjectIDND1="5055@x" ObjectIDZND0="5052@0" Pin0InfoVect0LinkObjId="SW-30216_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30217_0" Pin1InfoVect1LinkObjId="SW-30219_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-955 3926,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3aca600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1113 3901,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5054@x" ObjectIDND1="5057@x" ObjectIDND2="g_3c4afe0@0" ObjectIDZND0="g_3ac9060@0" Pin0InfoVect0LinkObjId="g_3ac9060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30218_0" Pin1InfoVect1LinkObjId="SW-30221_0" Pin1InfoVect2LinkObjId="g_3c4afe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1113 3901,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3acd310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1059 3909,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3c4afe0@0" ObjectIDND1="g_3ac9060@0" ObjectIDND2="9184@1" ObjectIDZND0="5057@1" Pin0InfoVect0LinkObjId="SW-30221_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c4afe0_0" Pin1InfoVect1LinkObjId="g_3ac9060_0" Pin1InfoVect2LinkObjId="g_3b99b10_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1059 3909,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3acd570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-1059 3860,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5057@0" ObjectIDZND0="g_3acd7d0@0" Pin0InfoVect0LinkObjId="g_3acd7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30221_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-1059 3860,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ace260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1050 3926,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5054@1" ObjectIDZND0="g_3c4afe0@0" ObjectIDZND1="g_3ac9060@0" ObjectIDZND2="9184@1" Pin0InfoVect0LinkObjId="g_3c4afe0_0" Pin0InfoVect1LinkObjId="g_3ac9060_0" Pin0InfoVect2LinkObjId="g_3b99b10_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1050 3926,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad0f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1002 3909,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5052@x" ObjectIDND1="5054@x" ObjectIDZND0="5056@1" Pin0InfoVect0LinkObjId="SW-30220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30216_0" Pin1InfoVect1LinkObjId="SW-30218_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1002 3909,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad11d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-1002 3860,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5056@0" ObjectIDZND0="g_3ad1430@0" Pin0InfoVect0LinkObjId="g_3ad1430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-1002 3860,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad1ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-992 3926,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5052@1" ObjectIDZND0="5054@x" ObjectIDZND1="5056@x" Pin0InfoVect0LinkObjId="SW-30218_0" Pin0InfoVect1LinkObjId="SW-30220_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30216_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-992 3926,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad2120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1002 3926,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5052@x" ObjectIDND1="5056@x" ObjectIDZND0="5054@0" Pin0InfoVect0LinkObjId="SW-30218_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30216_0" Pin1InfoVect1LinkObjId="SW-30220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1002 3926,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad4e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-955 3909,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5052@x" ObjectIDND1="5053@x" ObjectIDZND0="5055@1" Pin0InfoVect0LinkObjId="SW-30219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30216_0" Pin1InfoVect1LinkObjId="SW-30217_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-955 3909,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad5090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-955 3860,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5055@0" ObjectIDZND0="g_3ad52f0@0" Pin0InfoVect0LinkObjId="g_3ad52f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30219_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-955 3860,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad5d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1094 3954,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5054@x" ObjectIDND1="5057@x" ObjectIDND2="g_3ac9060@0" ObjectIDZND0="g_3c4afe0@0" Pin0InfoVect0LinkObjId="g_3c4afe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30218_0" Pin1InfoVect1LinkObjId="SW-30221_0" Pin1InfoVect2LinkObjId="g_3ac9060_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1094 3954,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad5fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1059 3926,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5054@x" ObjectIDND1="5057@x" ObjectIDZND0="g_3c4afe0@0" ObjectIDZND1="g_3ac9060@0" ObjectIDZND2="9184@1" Pin0InfoVect0LinkObjId="g_3c4afe0_0" Pin0InfoVect1LinkObjId="g_3ac9060_0" Pin0InfoVect2LinkObjId="g_3b99b10_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30218_0" Pin1InfoVect1LinkObjId="SW-30221_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1059 3926,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad6240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1094 3926,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="5054@x" ObjectIDND1="5057@x" ObjectIDND2="g_3c4afe0@0" ObjectIDZND0="g_3ac9060@0" ObjectIDZND1="9184@1" Pin0InfoVect0LinkObjId="g_3ac9060_0" Pin0InfoVect1LinkObjId="g_3b99b10_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30218_0" Pin1InfoVect1LinkObjId="SW-30221_0" Pin1InfoVect2LinkObjId="g_3c4afe0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1094 3926,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad64a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-834 4045,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5065@0" ObjectIDZND0="5064@x" ObjectIDZND1="5067@x" Pin0InfoVect0LinkObjId="SW-30228_0" Pin0InfoVect1LinkObjId="SW-30231_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30229_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-834 4045,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad6700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-819 4045,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5064@x" ObjectIDND1="5065@x" ObjectIDZND0="5067@1" Pin0InfoVect0LinkObjId="SW-30231_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30228_0" Pin1InfoVect1LinkObjId="SW-30229_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-819 4045,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad6960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4124,-803 4124,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5068@1" ObjectIDZND0="5064@x" ObjectIDZND1="5066@x" Pin0InfoVect0LinkObjId="SW-30228_0" Pin0InfoVect1LinkObjId="SW-30230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4124,-803 4124,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ad6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4124,-819 4124,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5064@x" ObjectIDND1="5068@x" ObjectIDZND0="5066@0" Pin0InfoVect0LinkObjId="SW-30230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30228_0" Pin1InfoVect1LinkObjId="SW-30232_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4124,-819 4124,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3adbf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4125,-917 4125,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5040@0" ObjectIDZND0="10707@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4125,-917 4125,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3adc1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-895 4045,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10708@0" ObjectIDZND0="5039@0" Pin0InfoVect0LinkObjId="SW-30203_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-895 4045,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3adeef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-976 3990,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5042@0" ObjectIDZND0="g_3adf150@0" Pin0InfoVect0LinkObjId="g_3adf150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-976 3990,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3adfbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-976 4036,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="5038@x" ObjectIDND1="5040@x" ObjectIDND2="5039@x" ObjectIDZND0="5042@1" Pin0InfoVect0LinkObjId="SW-30206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30202_0" Pin1InfoVect1LinkObjId="SW-30204_0" Pin1InfoVect2LinkObjId="SW-30203_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-976 4036,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3adfe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-952 4045,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="5039@1" ObjectIDZND0="5038@x" ObjectIDZND1="5040@x" ObjectIDZND2="5042@x" Pin0InfoVect0LinkObjId="SW-30202_0" Pin0InfoVect1LinkObjId="SW-30204_0" Pin0InfoVect2LinkObjId="SW-30206_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-952 4045,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ae67a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4057,-1054 4044,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5043@0" ObjectIDZND0="g_3ae6a00@0" Pin0InfoVect0LinkObjId="g_3ae6a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4057,-1054 4044,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ae7490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1054 4093,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5038@x" ObjectIDND1="5041@x" ObjectIDZND0="5043@1" Pin0InfoVect0LinkObjId="SW-30207_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30202_0" Pin1InfoVect1LinkObjId="SW-30205_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1054 4093,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ae76f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-991 4125,-991 4125,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="5039@x" ObjectIDND1="5042@x" ObjectIDND2="5038@x" ObjectIDZND0="5040@1" Pin0InfoVect0LinkObjId="SW-30204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30203_0" Pin1InfoVect1LinkObjId="SW-30206_0" Pin1InfoVect2LinkObjId="SW-30202_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-991 4125,-991 4125,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ae7950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-976 4045,-991 4110,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5039@x" ObjectIDND1="5042@x" ObjectIDZND0="5038@x" ObjectIDZND1="5040@x" Pin0InfoVect0LinkObjId="SW-30202_0" Pin0InfoVect1LinkObjId="SW-30204_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30203_0" Pin1InfoVect1LinkObjId="SW-30206_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-976 4045,-991 4110,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ae7bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1008 4110,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="5038@0" ObjectIDZND0="5039@x" ObjectIDZND1="5042@x" ObjectIDZND2="5040@x" Pin0InfoVect0LinkObjId="SW-30203_0" Pin0InfoVect1LinkObjId="SW-30206_0" Pin0InfoVect2LinkObjId="SW-30204_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1008 4110,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ae7e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1054 4110,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5041@x" ObjectIDND1="5043@x" ObjectIDZND0="5038@1" Pin0InfoVect0LinkObjId="SW-30202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30205_0" Pin1InfoVect1LinkObjId="SW-30207_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1054 4110,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3aea920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1073 4110,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5041@0" ObjectIDZND0="5038@x" ObjectIDZND1="5043@x" Pin0InfoVect0LinkObjId="SW-30202_0" Pin0InfoVect1LinkObjId="SW-30207_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1073 4110,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3aed630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4058,-1128 4045,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5044@0" ObjectIDZND0="g_3aed890@0" Pin0InfoVect0LinkObjId="g_3aed890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4058,-1128 4045,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3aee320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1128 4094,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c219b0@0" ObjectIDND1="5158@x" ObjectIDND2="5041@x" ObjectIDZND0="5044@1" Pin0InfoVect0LinkObjId="SW-30208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c219b0_0" Pin1InfoVect1LinkObjId="g_3c23350_0" Pin1InfoVect2LinkObjId="SW-30205_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1128 4094,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3aee580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1128 4110,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c219b0@0" ObjectIDND1="5158@x" ObjectIDND2="5044@x" ObjectIDZND0="5041@1" Pin0InfoVect0LinkObjId="SW-30205_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c219b0_0" Pin1InfoVect1LinkObjId="g_3c23350_0" Pin1InfoVect2LinkObjId="SW-30208_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1128 4110,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3af1090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-896 4329,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10707@0" ObjectIDZND0="5059@0" Pin0InfoVect0LinkObjId="SW-30223_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3adbf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-896 4329,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3af12f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-947 4329,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5059@1" ObjectIDZND0="5058@x" ObjectIDZND1="5061@x" Pin0InfoVect0LinkObjId="SW-30222_0" Pin0InfoVect1LinkObjId="SW-30225_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30223_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-947 4329,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3af35a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-956 4329,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5059@x" ObjectIDND1="5061@x" ObjectIDZND0="5058@0" Pin0InfoVect0LinkObjId="SW-30222_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30223_0" Pin1InfoVect1LinkObjId="SW-30225_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-956 4329,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3af7650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1114 4305,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5060@x" ObjectIDND1="5063@x" ObjectIDND2="g_3b034f0@0" ObjectIDZND0="g_3af60b0@0" Pin0InfoVect0LinkObjId="g_3af60b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30224_0" Pin1InfoVect1LinkObjId="SW-30227_0" Pin1InfoVect2LinkObjId="g_3b034f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1114 4305,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3afa360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1060 4312,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3b034f0@0" ObjectIDND1="g_3af60b0@0" ObjectIDND2="9183@1" ObjectIDZND0="5063@1" Pin0InfoVect0LinkObjId="SW-30227_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b034f0_0" Pin1InfoVect1LinkObjId="g_3af60b0_0" Pin1InfoVect2LinkObjId="g_3b99d00_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1060 4312,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3afa5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-1060 4263,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5063@0" ObjectIDZND0="g_3afa820@0" Pin0InfoVect0LinkObjId="g_3afa820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30227_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4276,-1060 4263,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3afb2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1051 4329,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5060@1" ObjectIDZND0="g_3b034f0@0" ObjectIDZND1="g_3af60b0@0" ObjectIDZND2="9183@1" Pin0InfoVect0LinkObjId="g_3b034f0_0" Pin0InfoVect1LinkObjId="g_3af60b0_0" Pin0InfoVect2LinkObjId="g_3b99d00_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1051 4329,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3afdfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1003 4312,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5058@x" ObjectIDND1="5060@x" ObjectIDZND0="5062@1" Pin0InfoVect0LinkObjId="SW-30226_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30222_0" Pin1InfoVect1LinkObjId="SW-30224_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1003 4312,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3afe220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-1003 4263,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5062@0" ObjectIDZND0="g_3afe480@0" Pin0InfoVect0LinkObjId="g_3afe480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4276,-1003 4263,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3afef10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-993 4329,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5058@1" ObjectIDZND0="5060@x" ObjectIDZND1="5062@x" Pin0InfoVect0LinkObjId="SW-30224_0" Pin0InfoVect1LinkObjId="SW-30226_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30222_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-993 4329,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3aff170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1003 4329,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5058@x" ObjectIDND1="5062@x" ObjectIDZND0="5060@0" Pin0InfoVect0LinkObjId="SW-30224_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30222_0" Pin1InfoVect1LinkObjId="SW-30226_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1003 4329,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b01e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-957 4312,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5058@x" ObjectIDND1="5059@x" ObjectIDZND0="5061@1" Pin0InfoVect0LinkObjId="SW-30225_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30222_0" Pin1InfoVect1LinkObjId="SW-30223_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-957 4312,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b020e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-957 4263,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5061@0" ObjectIDZND0="g_3b02340@0" Pin0InfoVect0LinkObjId="g_3b02340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30225_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4276,-957 4263,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b02dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1095 4355,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5060@x" ObjectIDND1="5063@x" ObjectIDND2="g_3af60b0@0" ObjectIDZND0="g_3b034f0@0" Pin0InfoVect0LinkObjId="g_3b034f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30224_0" Pin1InfoVect1LinkObjId="SW-30227_0" Pin1InfoVect2LinkObjId="g_3af60b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1095 4355,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b03030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1060 4329,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="5060@x" ObjectIDND1="5063@x" ObjectIDZND0="g_3b034f0@0" ObjectIDZND1="g_3af60b0@0" ObjectIDZND2="9183@1" Pin0InfoVect0LinkObjId="g_3b034f0_0" Pin0InfoVect1LinkObjId="g_3af60b0_0" Pin0InfoVect2LinkObjId="g_3b99d00_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30224_0" Pin1InfoVect1LinkObjId="SW-30227_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1060 4329,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b03290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1095 4329,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="5060@x" ObjectIDND1="5063@x" ObjectIDND2="g_3b034f0@0" ObjectIDZND0="g_3af60b0@0" ObjectIDZND1="9183@1" Pin0InfoVect0LinkObjId="g_3af60b0_0" Pin0InfoVect1LinkObjId="g_3b99d00_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30224_0" Pin1InfoVect1LinkObjId="SW-30227_0" Pin1InfoVect2LinkObjId="g_3b034f0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1095 4329,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b042a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-896 4484,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="10707@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2d3b1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3adbf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-896 4484,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b06fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-932 4608,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10707@0" ObjectIDND1="5071@x" ObjectIDZND0="5072@1" Pin0InfoVect0LinkObjId="SW-30236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3adbf80_0" Pin1InfoVect1LinkObjId="SW-30235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-932 4608,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b07210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4572,-932 4559,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5072@0" ObjectIDZND0="g_3b07470@0" Pin0InfoVect0LinkObjId="g_3b07470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4572,-932 4559,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b07f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-896 4625,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10707@0" ObjectIDZND0="5071@x" ObjectIDZND1="5072@x" Pin0InfoVect0LinkObjId="SW-30235_0" Pin0InfoVect1LinkObjId="SW-30236_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3adbf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-896 4625,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b08160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-1011 4625,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3b083c0@0" ObjectIDND1="5071@x" ObjectIDND2="5073@x" ObjectIDZND0="g_3b0c6d0@0" Pin0InfoVect0LinkObjId="g_3b0c6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b083c0_0" Pin1InfoVect1LinkObjId="SW-30235_0" Pin1InfoVect2LinkObjId="SW-30237_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-1011 4625,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b09960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-1012 4648,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3b0c6d0@0" ObjectIDND1="5071@x" ObjectIDND2="5073@x" ObjectIDZND0="g_3b083c0@0" Pin0InfoVect0LinkObjId="g_3b083c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b0c6d0_0" Pin1InfoVect1LinkObjId="SW-30235_0" Pin1InfoVect2LinkObjId="SW-30237_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-1012 4648,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b0c470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-931 4625,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10707@0" ObjectIDND1="5072@x" ObjectIDZND0="5071@0" Pin0InfoVect0LinkObjId="SW-30235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3adbf80_0" Pin1InfoVect1LinkObjId="SW-30236_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-931 4625,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b0fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-1012 4608,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3b083c0@0" ObjectIDND1="g_3b0c6d0@0" ObjectIDND2="5071@x" ObjectIDZND0="5073@1" Pin0InfoVect0LinkObjId="SW-30237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b083c0_0" Pin1InfoVect1LinkObjId="g_3b0c6d0_0" Pin1InfoVect2LinkObjId="SW-30235_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-1012 4608,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b10150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4572,-1012 4559,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5073@0" ObjectIDZND0="g_3b103b0@0" Pin0InfoVect0LinkObjId="g_3b103b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4572,-1012 4559,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b10e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-986 4625,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5071@1" ObjectIDZND0="g_3b083c0@0" ObjectIDZND1="g_3b0c6d0@0" ObjectIDZND2="5073@x" Pin0InfoVect0LinkObjId="g_3b083c0_0" Pin0InfoVect1LinkObjId="g_3b0c6d0_0" Pin0InfoVect2LinkObjId="SW-30237_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-986 4625,-1011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b110a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-498 5118,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="5022@0" ObjectIDZND0="12111@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_YL2Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-498 5118,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b11300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-550 4979,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5095@0" ObjectIDZND0="5093@0" Pin0InfoVect0LinkObjId="SW-30257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-550 4979,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b11560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-550 5051,-521 5074,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12105@x" ObjectIDND1="5096@x" ObjectIDND2="5095@x" ObjectIDZND0="g_3c48790@0" Pin0InfoVect0LinkObjId="g_3c48790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_362Ld_0" Pin1InfoVect1LinkObjId="SW-30260_0" Pin1InfoVect2LinkObjId="SW-30259_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-550 5051,-521 5074,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b117c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-550 5036,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c48790@0" ObjectIDND1="12105@x" ObjectIDND2="5096@x" ObjectIDZND0="5095@1" Pin0InfoVect0LinkObjId="SW-30259_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c48790_0" Pin1InfoVect1LinkObjId="EC-CX_SY.CX_SY_362Ld_0" Pin1InfoVect2LinkObjId="SW-30260_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-550 5036,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b18d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-610 4895,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5022@0" ObjectIDZND0="5047@x" Pin0InfoVect0LinkObjId="SW-30211_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-610 4895,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b18ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4931,-610 4953,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5047@x" ObjectIDZND0="5046@1" Pin0InfoVect0LinkObjId="SW-30210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30211_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4931,-610 4953,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b19250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4980,-610 5001,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5046@0" ObjectIDZND0="5048@x" Pin0InfoVect0LinkObjId="SW-30212_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4980,-610 5001,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1c7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-713 4895,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5022@0" ObjectIDZND0="5075@x" Pin0InfoVect0LinkObjId="SW-30239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-713 4895,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1ca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-713 5115,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b194b0@1" ObjectIDZND0="g_3b1d3c0@0" Pin0InfoVect0LinkObjId="g_3b1d3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b194b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-713 5115,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1cca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-713 4947,-684 4970,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3b194b0@0" ObjectIDND1="5075@x" ObjectIDZND0="g_3c45f40@0" Pin0InfoVect0LinkObjId="g_3c45f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b194b0_0" Pin1InfoVect1LinkObjId="SW-30239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-713 4947,-684 4970,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4931,-713 4947,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5075@x" ObjectIDZND0="g_3c45f40@0" ObjectIDZND1="g_3b194b0@0" Pin0InfoVect0LinkObjId="g_3c45f40_0" Pin0InfoVect1LinkObjId="g_3b194b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4931,-713 4947,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-713 4968,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3c45f40@0" ObjectIDND1="5075@x" ObjectIDZND0="g_3b194b0@0" Pin0InfoVect0LinkObjId="g_3b194b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c45f40_0" Pin1InfoVect1LinkObjId="SW-30239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-713 4968,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b26bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-788 4892,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5022@0" ObjectIDZND0="5090@0" Pin0InfoVect0LinkObjId="SW-30254_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-788 4892,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b26e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-788 4928,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5089@1" ObjectIDZND0="5090@1" Pin0InfoVect0LinkObjId="SW-30254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-788 4928,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b270b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4998,-788 4977,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5091@0" ObjectIDZND0="5089@0" Pin0InfoVect0LinkObjId="SW-30253_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4998,-788 4977,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b27310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-788 5049,-759 5072,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12104@x" ObjectIDND1="5092@x" ObjectIDND2="5091@x" ObjectIDZND0="g_3c46cb0@0" Pin0InfoVect0LinkObjId="g_3c46cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_361Ld_0" Pin1InfoVect1LinkObjId="SW-30256_0" Pin1InfoVect2LinkObjId="SW-30255_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-788 5049,-759 5072,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b27570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-788 5034,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c46cb0@0" ObjectIDND1="12104@x" ObjectIDND2="5092@x" ObjectIDZND0="5091@1" Pin0InfoVect0LinkObjId="SW-30255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c46cb0_0" Pin1InfoVect1LinkObjId="EC-CX_SY.CX_SY_361Ld_0" Pin1InfoVect2LinkObjId="SW-30256_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-788 5034,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-256 3792,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3b2c0d0@0" ObjectIDZND0="5126@x" Pin0InfoVect0LinkObjId="SW-30290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b2c0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-256 3792,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2cd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-195 3826,-195 3826,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12096@x" ObjectIDND1="5126@x" ObjectIDZND0="g_3b2d470@0" Pin0InfoVect0LinkObjId="g_3b2d470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_039Ld_0" Pin1InfoVect1LinkObjId="SW-30290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-195 3826,-195 3826,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2cfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-209 3792,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5126@x" ObjectIDZND0="g_3b2d470@0" ObjectIDZND1="12096@x" Pin0InfoVect0LinkObjId="g_3b2d470_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_039Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-209 3792,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2d210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-195 3792,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3b2d470@0" ObjectIDND1="5126@x" ObjectIDZND0="12096@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_039Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b2d470_0" Pin1InfoVect1LinkObjId="SW-30290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-195 3792,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-421 3792,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5023@0" ObjectIDZND0="5125@x" Pin0InfoVect0LinkObjId="SW-30289_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-421 3792,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-372 3792,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5125@x" ObjectIDZND0="5124@1" Pin0InfoVect0LinkObjId="SW-30288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-372 3792,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b30f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-421 3870,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="5023@0" ObjectIDZND0="12113@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_YL5Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-421 3870,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b33a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4041,-405 4041,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5036@1" ObjectIDZND0="5023@0" Pin0InfoVect0LinkObjId="g_3bbb7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4041,-405 4041,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-421 4207,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5023@0" ObjectIDZND0="5154@1" Pin0InfoVect0LinkObjId="SW-30318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-421 4207,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3b950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-367 4207,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5154@0" ObjectIDZND0="g_3b3b0d0@0" Pin0InfoVect0LinkObjId="g_3b3b0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-367 4207,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-421 4206,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="5023@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b33a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-421 4206,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3fb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-538 4206,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b3c5d0@1" ObjectIDZND0="g_3b41290@0" Pin0InfoVect0LinkObjId="g_3b41290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b3c5d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-538 4206,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b40b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-486 4175,-486 4175,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b3c5d0@0" ObjectIDZND0="g_3b3fdc0@0" Pin0InfoVect0LinkObjId="g_3b3fdc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b3c5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-486 4175,-486 4175,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b40dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-471 4206,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDZND0="g_3b3fdc0@0" ObjectIDZND1="g_3b3c5d0@0" Pin0InfoVect0LinkObjId="g_3b3fdc0_0" Pin0InfoVect1LinkObjId="g_3b3c5d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-471 4206,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b41030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-486 4206,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b3fdc0@0" ObjectIDZND0="g_3b3c5d0@0" Pin0InfoVect0LinkObjId="g_3b3c5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b3fdc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-486 4206,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b46000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4447,-422 4447,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5024@0" ObjectIDZND0="5155@1" Pin0InfoVect0LinkObjId="SW-30319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4447,-422 4447,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b46ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4447,-367 4447,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5155@0" ObjectIDZND0="g_3b46260@0" Pin0InfoVect0LinkObjId="g_3b46260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4447,-367 4447,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b47760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4447,-321 4447,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b46260@1" ObjectIDZND0="g_3b46d40@1" Pin0InfoVect0LinkObjId="g_3b46d40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b46260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4447,-321 4447,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b479c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4447,-246 4447,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3b46d40@0" ObjectIDZND0="12090@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b46d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4447,-246 4447,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b4af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-422 4446,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="5024@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4446,-422 4446,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b4b1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-538 4446,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b47c20@1" ObjectIDZND0="g_3b4c8e0@0" Pin0InfoVect0LinkObjId="g_3b4c8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b47c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4446,-538 4446,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b4c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-487 4415,-487 4415,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b47c20@0" ObjectIDZND0="g_3b4b410@0" Pin0InfoVect0LinkObjId="g_3b4b410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b47c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-487 4415,-487 4415,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b4c420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-471 4446,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDZND0="g_3b4b410@0" ObjectIDZND1="g_3b47c20@0" Pin0InfoVect0LinkObjId="g_3b4b410_0" Pin0InfoVect1LinkObjId="g_3b47c20_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4446,-471 4446,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b4c680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-487 4446,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b4b410@0" ObjectIDZND0="g_3b47c20@0" Pin0InfoVect0LinkObjId="g_3b47c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b4b410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4446,-487 4446,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b55f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-404 4624,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5050@1" ObjectIDZND0="5024@0" Pin0InfoVect0LinkObjId="g_3bbe960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30214_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-404 4624,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b561b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4823,-422 4823,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="5024@0" ObjectIDZND0="12115@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_YL6Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4823,-422 4823,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5dfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-313 4886,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5139@0" ObjectIDZND0="g_3b5d5c0@1" Pin0InfoVect0LinkObjId="g_3b5d5c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-313 4886,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5e240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-248 4886,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3b5d5c0@0" ObjectIDZND0="5141@1" Pin0InfoVect0LinkObjId="SW-30305_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b5d5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-248 4886,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5e4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-187 4920,-187 4920,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12091@x" ObjectIDND1="5141@x" ObjectIDZND0="g_3b5ebc0@0" Pin0InfoVect0LinkObjId="g_3b5ebc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_069Ld_0" Pin1InfoVect1LinkObjId="SW-30305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-187 4920,-187 4920,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5e700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-201 4886,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5141@0" ObjectIDZND0="g_3b5ebc0@0" ObjectIDZND1="12091@x" Pin0InfoVect0LinkObjId="g_3b5ebc0_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_069Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-201 4886,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5e960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-187 4886,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3b5ebc0@0" ObjectIDND1="5141@x" ObjectIDZND0="12091@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_069Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b5ebc0_0" Pin1InfoVect1LinkObjId="SW-30305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-187 4886,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5f930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-422 4886,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5024@0" ObjectIDZND0="5140@1" Pin0InfoVect0LinkObjId="SW-30304_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-422 4886,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5fb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-364 4886,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5140@0" ObjectIDZND0="5139@1" Pin0InfoVect0LinkObjId="SW-30303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-364 4886,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b679c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-314 4959,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5142@0" ObjectIDZND0="g_3b66fa0@1" Pin0InfoVect0LinkObjId="g_3b66fa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-314 4959,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b67c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-249 4959,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3b66fa0@0" ObjectIDZND0="5144@1" Pin0InfoVect0LinkObjId="SW-30308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b66fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-249 4959,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b67e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-188 4993,-188 4993,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="12092@x" ObjectIDND1="5144@x" ObjectIDZND0="g_3b685a0@0" Pin0InfoVect0LinkObjId="g_3b685a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_071Ld_0" Pin1InfoVect1LinkObjId="SW-30308_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-188 4993,-188 4993,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b680e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-202 4959,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5144@0" ObjectIDZND0="g_3b685a0@0" ObjectIDZND1="12092@x" Pin0InfoVect0LinkObjId="g_3b685a0_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-202 4959,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b68340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-188 4959,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3b685a0@0" ObjectIDND1="5144@x" ObjectIDZND0="12092@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b685a0_0" Pin1InfoVect1LinkObjId="SW-30308_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-188 4959,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b69310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-422 4959,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5024@0" ObjectIDZND0="5143@1" Pin0InfoVect0LinkObjId="SW-30307_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-422 4959,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b69570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-365 4959,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5143@0" ObjectIDZND0="5142@1" Pin0InfoVect0LinkObjId="SW-30306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-365 4959,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b697d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-201 5028,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="5147@0" ObjectIDZND0="g_3db64e0@0" ObjectIDZND1="12093@x" Pin0InfoVect0LinkObjId="g_3db64e0_0" Pin0InfoVect1LinkObjId="EC-CX_SY.CX_SY_072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30311_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-201 5028,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b69a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-101 5028,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="12093@0" ObjectIDZND0="g_3db64e0@0" ObjectIDZND1="5147@x" Pin0InfoVect0LinkObjId="g_3db64e0_0" Pin0InfoVect1LinkObjId="SW-30311_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_072Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-101 5028,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6cfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4861,-1090 4892,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5021@0" ObjectIDZND0="5074@x" Pin0InfoVect0LinkObjId="SW-30238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b85270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4861,-1090 4892,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-1090 5112,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b69c90@1" ObjectIDZND0="g_3b85e50@0" Pin0InfoVect0LinkObjId="g_3b85e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b69c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-1090 5112,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-1090 4944,-1061 4967,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3b69c90@0" ObjectIDND1="5074@x" ObjectIDZND0="g_3c44860@0" Pin0InfoVect0LinkObjId="g_3c44860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b69c90_0" Pin1InfoVect1LinkObjId="SW-30238_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-1090 4944,-1061 4967,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6d6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-1090 4944,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5074@x" ObjectIDZND0="g_3c44860@0" ObjectIDZND1="g_3b69c90@0" Pin0InfoVect0LinkObjId="g_3c44860_0" Pin0InfoVect1LinkObjId="g_3b69c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30238_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-1090 4944,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6d940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-1090 4965,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3c44860@0" ObjectIDND1="5074@x" ObjectIDZND0="g_3b69c90@0" Pin0InfoVect0LinkObjId="g_3b69c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c44860_0" Pin1InfoVect1LinkObjId="SW-30238_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-1090 4965,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4861,-1135 5117,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="5021@0" ObjectIDZND0="12110@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_YL1Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b85270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4861,-1135 5117,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b75170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4861,-949 4893,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5021@0" ObjectIDZND0="5086@0" Pin0InfoVect0LinkObjId="SW-30250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b85270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4861,-949 4893,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b753d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-949 4929,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5085@1" ObjectIDZND0="5086@1" Pin0InfoVect0LinkObjId="SW-30250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-949 4929,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b75630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-949 4978,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5087@0" ObjectIDZND0="5085@0" Pin0InfoVect0LinkObjId="SW-30249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-949 4978,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b75890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-949 5050,-920 5073,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12102@x" ObjectIDND1="5088@x" ObjectIDND2="5087@x" ObjectIDZND0="g_3c45340@0" Pin0InfoVect0LinkObjId="g_3c45340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_332Ld_0" Pin1InfoVect1LinkObjId="SW-30252_0" Pin1InfoVect2LinkObjId="SW-30251_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-949 5050,-920 5073,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b75af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-949 5035,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c45340@0" ObjectIDND1="12102@x" ObjectIDND2="5088@x" ObjectIDZND0="5087@1" Pin0InfoVect0LinkObjId="SW-30251_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c45340_0" Pin1InfoVect1LinkObjId="EC-CX_SY.CX_SY_332Ld_0" Pin1InfoVect2LinkObjId="SW-30252_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-949 5035,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7d0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4861,-899 4892,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5021@0" ObjectIDZND0="5082@0" Pin0InfoVect0LinkObjId="SW-30246_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b85270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4861,-899 4892,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7d320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-899 4928,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5081@1" ObjectIDZND0="5082@1" Pin0InfoVect0LinkObjId="SW-30246_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-899 4928,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4998,-899 4977,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5083@0" ObjectIDZND0="5081@0" Pin0InfoVect0LinkObjId="SW-30245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4998,-899 4977,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-899 5199,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3c47a20@0" ObjectIDND1="5083@x" ObjectIDZND0="12103@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_331Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c47a20_0" Pin1InfoVect1LinkObjId="SW-30247_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-899 5199,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b84db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-1028 4972,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5034@x" ObjectIDZND0="5032@0" Pin0InfoVect0LinkObjId="SW-30196_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-1028 4972,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b85010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4945,-1028 4923,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5032@1" ObjectIDZND0="5033@x" Pin0InfoVect0LinkObjId="SW-30197_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4945,-1028 4923,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b85270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-1028 4861,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5033@x" ObjectIDZND0="5021@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-1028 4861,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b854d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-814 4879,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5022@0" ObjectIDZND0="5080@0" Pin0InfoVect0LinkObjId="SW-30244_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-814 4879,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b85730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4861,-877 4881,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5021@0" ObjectIDZND0="5079@0" Pin0InfoVect0LinkObjId="SW-30243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b85270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4861,-877 4881,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b85990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-877 4932,-877 4932,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5079@1" ObjectIDZND0="5078@0" Pin0InfoVect0LinkObjId="SW-30242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-877 4932,-877 4932,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b85bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4932,-833 4932,-814 4915,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5078@1" ObjectIDZND0="5080@1" Pin0InfoVect0LinkObjId="SW-30244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4932,-833 4932,-814 4915,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b914d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3684,-559 3684,-466 4012,-466 4012,-224 4041,-224 4041,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_3c4a270@0" ObjectIDND1="5157@x" ObjectIDZND0="5037@0" Pin0InfoVect0LinkObjId="SW-30201_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c4a270_0" Pin1InfoVect1LinkObjId="g_3dd4b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3684,-559 3684,-466 4012,-466 4012,-224 4041,-224 4041,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b91750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3684,-604 3684,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5157@2" ObjectIDZND0="g_3c4a270@0" ObjectIDZND1="5037@x" Pin0InfoVect0LinkObjId="g_3c4a270_0" Pin0InfoVect1LinkObjId="SW-30201_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dd4b60_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3684,-604 3684,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b919b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-713 4083,-732 4108,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="g_2d4a010@0" ObjectIDZND0="g_2d40dc0@0" ObjectIDZND1="5158@x" ObjectIDZND2="5045@x" Pin0InfoVect0LinkObjId="g_2d40dc0_0" Pin0InfoVect1LinkObjId="g_3c23350_0" Pin0InfoVect2LinkObjId="SW-30209_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d4a010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-713 4083,-732 4108,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b91c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-732 4135,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="g_2d4a010@0" ObjectIDND1="5045@x" ObjectIDZND0="g_2d40dc0@0" ObjectIDZND1="5158@x" Pin0InfoVect0LinkObjId="g_2d40dc0_0" Pin0InfoVect1LinkObjId="g_3c23350_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d4a010_0" Pin1InfoVect1LinkObjId="SW-30209_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-732 4135,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b91e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-732 4135,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5158@x" ObjectIDZND0="g_2d4a010@0" ObjectIDZND1="5045@x" ObjectIDZND2="g_2d40dc0@0" Pin0InfoVect0LinkObjId="g_2d4a010_0" Pin0InfoVect1LinkObjId="SW-30209_0" Pin0InfoVect2LinkObjId="g_2d40dc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c23350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-732 4135,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b920d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-878 5049,-878 5049,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3c47a20@0" ObjectIDZND0="12103@x" ObjectIDZND1="5083@x" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_331Ld_0" Pin0InfoVect1LinkObjId="SW-30247_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c47a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-878 5049,-878 5049,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b92330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-899 5034,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3c47a20@0" ObjectIDND1="12103@x" ObjectIDZND0="5083@1" Pin0InfoVect0LinkObjId="SW-30247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c47a20_0" Pin1InfoVect1LinkObjId="EC-CX_SY.CX_SY_331Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-899 5034,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b95080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-895 3793,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10708@0" ObjectIDZND0="5026@0" Pin0InfoVect0LinkObjId="SW-30190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-895 3793,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b952e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-948 3793,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5026@1" ObjectIDZND0="5025@x" ObjectIDZND1="5028@x" Pin0InfoVect0LinkObjId="SW-30189_0" Pin0InfoVect1LinkObjId="SW-30192_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-948 3793,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b95540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-967 3793,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5026@x" ObjectIDND1="5028@x" ObjectIDZND0="5025@0" Pin0InfoVect0LinkObjId="SW-30189_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30190_0" Pin1InfoVect1LinkObjId="SW-30192_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-967 3793,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b957a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-1015 3793,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5025@1" ObjectIDZND0="5027@x" ObjectIDZND1="5029@x" Pin0InfoVect0LinkObjId="SW-30191_0" Pin0InfoVect1LinkObjId="SW-30193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30189_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-1015 3793,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b99b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1113 3926,-1165 3926,-1167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="5054@x" ObjectIDND1="5057@x" ObjectIDND2="g_3c4afe0@0" ObjectIDZND0="9184@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30218_0" Pin1InfoVect1LinkObjId="SW-30221_0" Pin1InfoVect2LinkObjId="g_3c4afe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1113 3926,-1165 3926,-1167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b99d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1113 4329,-1164 4329,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="5060@x" ObjectIDND1="5063@x" ObjectIDND2="g_3b034f0@0" ObjectIDZND0="9183@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-30224_0" Pin1InfoVect1LinkObjId="SW-30227_0" Pin1InfoVect2LinkObjId="g_3b034f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4329,-1113 4329,-1164 4329,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bad2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-335 4135,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5103@0" ObjectIDZND0="g_3bac8c0@1" Pin0InfoVect0LinkObjId="g_3bac8c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30267_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-335 4135,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bad540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-91 4135,-76 4160,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="5108@0" Pin0InfoVect0LinkObjId="SW-30272_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-91 4135,-76 4160,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bb2d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-211 4135,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5106@1" ObjectIDZND0="11727@x" ObjectIDZND1="5105@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_2C_0" Pin0InfoVect1LinkObjId="SW-30269_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-211 4135,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bb5a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-211 4135,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5105@x" ObjectIDND1="5106@x" ObjectIDZND0="11727@0" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_2C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30269_0" Pin1InfoVect1LinkObjId="SW-30270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-211 4135,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bb5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-261 4135,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5107@1" ObjectIDZND0="g_3bac8c0@0" ObjectIDZND1="5105@x" Pin0InfoVect0LinkObjId="g_3bac8c0_0" Pin0InfoVect1LinkObjId="SW-30269_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-261 4135,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bb5ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-261 4135,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5105@x" ObjectIDND1="5107@x" ObjectIDZND0="g_3bac8c0@0" Pin0InfoVect0LinkObjId="g_3bac8c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30269_0" Pin1InfoVect1LinkObjId="SW-30271_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-261 4135,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bb89e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-261 4135,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3bac8c0@0" ObjectIDND1="5107@x" ObjectIDZND0="5105@1" Pin0InfoVect0LinkObjId="SW-30269_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bac8c0_0" Pin1InfoVect1LinkObjId="SW-30271_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-261 4135,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bb8c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-218 4135,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5105@0" ObjectIDZND0="11727@x" ObjectIDZND1="5106@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_2C_0" Pin0InfoVect1LinkObjId="SW-30270_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-218 4135,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bb9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4086,-261 4072,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5107@0" ObjectIDZND0="g_3bb8ea0@0" Pin0InfoVect0LinkObjId="g_3bb8ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4086,-261 4072,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bba620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4075,-211 4086,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3bb9b90@0" ObjectIDZND0="5106@0" Pin0InfoVect0LinkObjId="SW-30270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bb9b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4075,-211 4086,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbb310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4196,-76 4211,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5108@1" ObjectIDZND0="g_3bba880@0" Pin0InfoVect0LinkObjId="g_3bba880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4196,-76 4211,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbb570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-363 4135,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5103@1" ObjectIDZND0="5104@0" Pin0InfoVect0LinkObjId="SW-30268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-363 4135,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbb7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-412 4135,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5104@1" ObjectIDZND0="5023@0" Pin0InfoVect0LinkObjId="g_3b33a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-412 4135,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbc2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4041,-283 4041,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5037@1" ObjectIDZND0="5035@0" Pin0InfoVect0LinkObjId="SW-30199_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30201_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4041,-283 4041,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbc490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4041,-341 4041,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5035@1" ObjectIDZND0="5036@0" Pin0InfoVect0LinkObjId="SW-30200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30199_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4041,-341 4041,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbdba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-321 4207,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b3b0d0@1" ObjectIDZND0="g_3b3bbb0@1" Pin0InfoVect0LinkObjId="g_3b3bbb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b3b0d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-321 4207,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbdd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-248 4207,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3b3bbb0@0" ObjectIDZND0="12089@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b3bbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-248 4207,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbe960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-412 4565,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5110@1" ObjectIDZND0="5024@0" Pin0InfoVect0LinkObjId="g_3b55f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-412 4565,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bc4590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4516,-260 4502,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5113@0" ObjectIDZND0="g_3bc3b00@0" Pin0InfoVect0LinkObjId="g_3bc3b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4516,-260 4502,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bc5280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-362 4565,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5109@1" ObjectIDZND0="5110@0" Pin0InfoVect0LinkObjId="SW-30274_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30273_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-362 4565,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bc54e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-334 4565,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5109@0" ObjectIDZND0="g_3bc30e0@1" Pin0InfoVect0LinkObjId="g_3bc30e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-334 4565,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bc5740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-90 4565,-75 4590,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="5114@0" Pin0InfoVect0LinkObjId="SW-30278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-90 4565,-75 4590,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bcaf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-210 4516,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3bc47f0@0" ObjectIDZND0="5112@0" Pin0InfoVect0LinkObjId="SW-30276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bc47f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-210 4516,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bcb160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4552,-210 4565,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5112@1" ObjectIDZND0="11728@x" ObjectIDZND1="5111@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_3C_0" Pin0InfoVect1LinkObjId="SW-30275_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30276_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4552,-210 4565,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bcb3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-210 4565,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5111@x" ObjectIDND1="5112@x" ObjectIDZND0="11728@0" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_3C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30275_0" Pin1InfoVect1LinkObjId="SW-30276_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-210 4565,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bcb620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4552,-260 4565,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5113@1" ObjectIDZND0="g_3bc30e0@0" ObjectIDZND1="5111@x" Pin0InfoVect0LinkObjId="g_3bc30e0_0" Pin0InfoVect1LinkObjId="SW-30275_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4552,-260 4565,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bcb880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-260 4565,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5111@x" ObjectIDND1="5113@x" ObjectIDZND0="g_3bc30e0@0" Pin0InfoVect0LinkObjId="g_3bc30e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30275_0" Pin1InfoVect1LinkObjId="SW-30277_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-260 4565,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bce390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-260 4565,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3bc30e0@0" ObjectIDND1="5113@x" ObjectIDZND0="5111@1" Pin0InfoVect0LinkObjId="SW-30275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bc30e0_0" Pin1InfoVect1LinkObjId="SW-30277_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-260 4565,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bce5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-217 4565,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5111@0" ObjectIDZND0="11728@x" ObjectIDZND1="5112@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_3C_0" Pin0InfoVect1LinkObjId="SW-30276_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-217 4565,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd1d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-75 4641,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5114@1" ObjectIDZND0="g_3bd1300@0" Pin0InfoVect0LinkObjId="g_3bd1300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-75 4641,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd1ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-412 4754,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5116@1" ObjectIDZND0="5024@0" Pin0InfoVect0LinkObjId="g_3b55f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-412 4754,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd8000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-260 4691,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5119@0" ObjectIDZND0="g_3bd7570@0" Pin0InfoVect0LinkObjId="g_3bd7570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30283_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-260 4691,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd8cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-362 4754,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5115@1" ObjectIDZND0="5116@0" Pin0InfoVect0LinkObjId="SW-30280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-362 4754,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-335 4754,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5115@0" ObjectIDZND0="g_3bd6b50@1" Pin0InfoVect0LinkObjId="g_3bd6b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-335 4754,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd91b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-90 4754,-75 4779,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="5120@0" Pin0InfoVect0LinkObjId="SW-30284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-90 4754,-75 4779,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bde970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4694,-210 4705,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3bd8260@0" ObjectIDZND0="5118@0" Pin0InfoVect0LinkObjId="SW-30282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bd8260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4694,-210 4705,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bdebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-210 4754,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5118@1" ObjectIDZND0="11729@x" ObjectIDZND1="5117@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_4C_0" Pin0InfoVect1LinkObjId="SW-30281_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-210 4754,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bdee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-210 4754,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5117@x" ObjectIDND1="5118@x" ObjectIDZND0="11729@0" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_4C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30281_0" Pin1InfoVect1LinkObjId="SW-30282_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-210 4754,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bdf090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-260 4754,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5119@1" ObjectIDZND0="g_3bd6b50@0" ObjectIDZND1="5117@x" Pin0InfoVect0LinkObjId="g_3bd6b50_0" Pin0InfoVect1LinkObjId="SW-30281_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-260 4754,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bdf2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-260 4754,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5117@x" ObjectIDND1="5119@x" ObjectIDZND0="g_3bd6b50@0" Pin0InfoVect0LinkObjId="g_3bd6b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30281_0" Pin1InfoVect1LinkObjId="SW-30283_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-260 4754,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3be1e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-260 4754,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3bd6b50@0" ObjectIDND1="5119@x" ObjectIDZND0="5117@1" Pin0InfoVect0LinkObjId="SW-30281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bd6b50_0" Pin1InfoVect1LinkObjId="SW-30283_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-260 4754,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3be2060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-217 4754,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5117@0" ObjectIDZND0="11729@x" ObjectIDZND1="5118@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_4C_0" Pin0InfoVect1LinkObjId="SW-30282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-217 4754,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3be5800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4815,-75 4830,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5120@1" ObjectIDZND0="g_3be4d70@0" Pin0InfoVect0LinkObjId="g_3be4d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4815,-75 4830,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3be6bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-282 4624,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5051@1" ObjectIDZND0="5049@0" Pin0InfoVect0LinkObjId="SW-30213_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30215_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-282 4624,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3be6dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-340 4624,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5049@1" ObjectIDZND0="5050@0" Pin0InfoVect0LinkObjId="SW-30214_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-340 4624,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf1e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5057,-949 5050,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="12102@x" ObjectIDND1="5088@x" ObjectIDZND0="g_3c45340@0" ObjectIDZND1="5087@x" Pin0InfoVect0LinkObjId="g_3c45340_0" Pin0InfoVect1LinkObjId="SW-30251_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_332Ld_0" Pin1InfoVect1LinkObjId="SW-30252_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5057,-949 5050,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf20b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-949 5057,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="12102@0" ObjectIDZND0="g_3c45340@0" ObjectIDZND1="5087@x" ObjectIDZND2="5088@x" Pin0InfoVect0LinkObjId="g_3c45340_0" Pin0InfoVect1LinkObjId="SW-30251_0" Pin0InfoVect2LinkObjId="SW-30252_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_332Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5200,-949 5057,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf2310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5057,-949 5057,-974 5067,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="12102@x" ObjectIDND1="g_3c45340@0" ObjectIDND2="5087@x" ObjectIDZND0="5088@0" Pin0InfoVect0LinkObjId="SW-30252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_332Ld_0" Pin1InfoVect1LinkObjId="g_3c45340_0" Pin1InfoVect2LinkObjId="SW-30251_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5057,-949 5057,-974 5067,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf2570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5159,-974 5144,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3c426f0@0" ObjectIDZND0="g_3beef60@0" Pin0InfoVect0LinkObjId="g_3beef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c426f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5159,-974 5144,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf27d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5113,-974 5103,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3beef60@1" ObjectIDZND0="5088@1" Pin0InfoVect0LinkObjId="SW-30252_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3beef60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5113,-974 5103,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf2a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-788 5055,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3c46cb0@0" ObjectIDND1="5091@x" ObjectIDZND0="12104@x" ObjectIDZND1="5092@x" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_361Ld_0" Pin0InfoVect1LinkObjId="SW-30256_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c46cb0_0" Pin1InfoVect1LinkObjId="SW-30255_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-788 5055,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf2c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-788 5199,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3c46cb0@0" ObjectIDND1="5091@x" ObjectIDND2="5092@x" ObjectIDZND0="12104@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_361Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c46cb0_0" Pin1InfoVect1LinkObjId="SW-30255_0" Pin1InfoVect2LinkObjId="SW-30256_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-788 5199,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf2ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-550 5058,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3c48790@0" ObjectIDND1="5095@x" ObjectIDZND0="12105@x" ObjectIDZND1="5096@x" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_362Ld_0" Pin0InfoVect1LinkObjId="SW-30260_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c48790_0" Pin1InfoVect1LinkObjId="SW-30259_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-550 5058,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf3150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-550 5201,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3c48790@0" ObjectIDND1="5095@x" ObjectIDND2="5096@x" ObjectIDZND0="12105@0" Pin0InfoVect0LinkObjId="EC-CX_SY.CX_SY_362Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c48790_0" Pin1InfoVect1LinkObjId="SW-30259_0" Pin1InfoVect2LinkObjId="SW-30260_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-550 5201,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bf5de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-412 3957,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5098@1" ObjectIDZND0="5023@0" Pin0InfoVect0LinkObjId="g_3b33a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-412 3957,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfba30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-260 3894,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5101@0" ObjectIDZND0="g_3bfafa0@0" Pin0InfoVect0LinkObjId="g_3bfafa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-260 3894,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfc720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-362 3957,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5097@1" ObjectIDZND0="5098@0" Pin0InfoVect0LinkObjId="SW-30262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30261_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-362 3957,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfc980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-334 3957,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="5097@0" ObjectIDZND0="g_3bfa580@1" Pin0InfoVect0LinkObjId="g_3bfa580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-334 3957,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfcbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-90 3957,-75 3982,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="5102@0" Pin0InfoVect0LinkObjId="SW-30266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-90 3957,-75 3982,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c023a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-210 3908,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3bfbc90@0" ObjectIDZND0="5100@0" Pin0InfoVect0LinkObjId="SW-30264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bfbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-210 3908,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c02600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-210 3957,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5100@1" ObjectIDZND0="11726@x" ObjectIDZND1="5099@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_1C_0" Pin0InfoVect1LinkObjId="SW-30263_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-210 3957,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c02860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-210 3957,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="5099@x" ObjectIDND1="5100@x" ObjectIDZND0="11726@0" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30263_0" Pin1InfoVect1LinkObjId="SW-30264_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-210 3957,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c02ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-260 3957,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5101@1" ObjectIDZND0="g_3bfa580@0" ObjectIDZND1="5099@x" Pin0InfoVect0LinkObjId="g_3bfa580_0" Pin0InfoVect1LinkObjId="SW-30263_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-260 3957,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c02d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-260 3957,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5099@x" ObjectIDND1="5101@x" ObjectIDZND0="g_3bfa580@0" Pin0InfoVect0LinkObjId="g_3bfa580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-30263_0" Pin1InfoVect1LinkObjId="SW-30265_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-260 3957,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c05830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-260 3957,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3bfa580@0" ObjectIDND1="5101@x" ObjectIDZND0="5099@1" Pin0InfoVect0LinkObjId="SW-30263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bfa580_0" Pin1InfoVect1LinkObjId="SW-30265_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-260 3957,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c05a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-217 3957,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="5099@0" ObjectIDZND0="11726@x" ObjectIDZND1="5100@x" Pin0InfoVect0LinkObjId="CB-CX_SY.CX_SY_1C_0" Pin0InfoVect1LinkObjId="SW-30264_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-217 3957,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c09230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-75 4033,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5102@1" ObjectIDZND0="g_3c087a0@0" Pin0InfoVect0LinkObjId="g_3c087a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-75 4033,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c230f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-872 4207,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_3c219b0@0" ObjectIDZND0="5041@x" ObjectIDZND1="5044@x" ObjectIDZND2="5158@x" Pin0InfoVect0LinkObjId="SW-30205_0" Pin0InfoVect1LinkObjId="SW-30208_0" Pin0InfoVect2LinkObjId="g_3c23350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c219b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-872 4207,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c23350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4409,-672 4409,-708 4261,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="g_3c22470@0" ObjectIDND1="5158@x" ObjectIDND2="g_3c22470@0" ObjectIDZND0="5158@0" Pin0InfoVect0LinkObjId="g_3c235b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c22470_0" Pin1InfoVect1LinkObjId="g_3c235b0_0" Pin1InfoVect2LinkObjId="g_3c22470_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4409,-672 4409,-708 4261,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c235b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-559 4664,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_3c49500@0" ObjectIDZND0="5158@x" ObjectIDZND1="5051@x" Pin0InfoVect0LinkObjId="g_3c23350_0" Pin0InfoVect1LinkObjId="SW-30215_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c49500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-559 4664,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c23810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-668 4205,-604 4664,-604 4664,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5158@2" ObjectIDZND0="g_3c49500@0" ObjectIDZND1="5051@x" Pin0InfoVect0LinkObjId="g_3c49500_0" Pin0InfoVect1LinkObjId="SW-30215_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c23350_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-668 4205,-604 4664,-604 4664,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c23a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-559 4664,-211 4624,-211 4624,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5158@x" ObjectIDND1="g_3c49500@0" ObjectIDZND0="5051@0" Pin0InfoVect0LinkObjId="SW-30215_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c23350_0" Pin1InfoVect1LinkObjId="g_3c49500_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-559 4664,-211 4624,-211 4624,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c29570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-550 5058,-575 5083,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="12105@x" ObjectIDND1="g_3c48790@0" ObjectIDND2="5095@x" ObjectIDZND0="5096@0" Pin0InfoVect0LinkObjId="SW-30260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_362Ld_0" Pin1InfoVect1LinkObjId="g_3c48790_0" Pin1InfoVect2LinkObjId="SW-30259_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-550 5058,-575 5083,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c297d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-575 5159,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="5096@1" ObjectIDZND0="g_3c430d0@0" Pin0InfoVect0LinkObjId="g_3c430d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-575 5159,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c29a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-788 5055,-813 5084,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="12104@x" ObjectIDND1="g_3c46cb0@0" ObjectIDND2="5091@x" ObjectIDZND0="5092@0" Pin0InfoVect0LinkObjId="SW-30256_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SY.CX_SY_361Ld_0" Pin1InfoVect1LinkObjId="g_3c46cb0_0" Pin1InfoVect2LinkObjId="SW-30255_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-788 5055,-813 5084,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c29c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-813 5153,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="5092@1" ObjectIDZND0="g_3c42000@0" Pin0InfoVect0LinkObjId="g_3c42000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-813 5153,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c30600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-607 3885,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_3c2fb30@0" ObjectIDZND0="5157@x" ObjectIDZND1="5034@x" Pin0InfoVect0LinkObjId="g_3dd4b60_0" Pin0InfoVect1LinkObjId="SW-30198_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c2fb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-607 3885,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c30860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5029,-1028 5068,-1028 5068,-991 4786,-991 4786,-626 3885,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="5034@x" ObjectIDZND0="g_3c2fb30@0" ObjectIDZND1="5157@x" Pin0InfoVect0LinkObjId="g_3c2fb30_0" Pin0InfoVect1LinkObjId="g_3dd4b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5029,-1028 5068,-1028 5068,-991 4786,-991 4786,-626 3885,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c30ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-626 3788,-626 3788,-645 3743,-645 3741,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="g_3c2fb30@0" ObjectIDND1="5034@x" ObjectIDZND0="5157@0" Pin0InfoVect0LinkObjId="g_3dd4b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c2fb30_0" Pin1InfoVect1LinkObjId="SW-30198_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-626 3788,-626 3788,-645 3743,-645 3741,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c41a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-871 4206,-1151 4110,-1151 4110,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c219b0@0" ObjectIDND1="5158@x" ObjectIDZND0="5041@x" ObjectIDZND1="5044@x" Pin0InfoVect0LinkObjId="SW-30205_0" Pin0InfoVect1LinkObjId="SW-30208_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c219b0_0" Pin1InfoVect1LinkObjId="g_3c23350_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-871 4206,-1151 4110,-1151 4110,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c41c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-747 4206,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5158@1" ObjectIDZND0="5041@x" ObjectIDZND1="5044@x" ObjectIDZND2="g_3c219b0@0" Pin0InfoVect0LinkObjId="SW-30205_0" Pin0InfoVect1LinkObjId="SW-30208_0" Pin0InfoVect2LinkObjId="g_3c219b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c23350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-747 4206,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c41df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4409,-675 4419,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5158@x" ObjectIDND1="5158@x" ObjectIDND2="g_3c22470@0" ObjectIDZND0="g_3c22470@0" Pin0InfoVect0LinkObjId="g_3c22470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c23350_0" Pin1InfoVect1LinkObjId="g_3c23350_0" Pin1InfoVect2LinkObjId="g_3c22470_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4409,-675 4419,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c4cfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-871 3686,-1187 3793,-1187 3793,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5157@x" ObjectIDND1="g_3ab12e0@0" ObjectIDZND0="5027@x" ObjectIDZND1="5030@x" Pin0InfoVect0LinkObjId="SW-30191_0" Pin0InfoVect1LinkObjId="SW-30194_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3dd4b60_0" Pin1InfoVect1LinkObjId="g_3ab12e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-871 3686,-1187 3793,-1187 3793,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c4d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-687 3686,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5157@1" ObjectIDZND0="5027@x" ObjectIDZND1="5030@x" ObjectIDZND2="g_3ab12e0@0" Pin0InfoVect0LinkObjId="SW-30191_0" Pin0InfoVect1LinkObjId="SW-30194_0" Pin0InfoVect2LinkObjId="g_3ab12e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dd4b60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-687 3686,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c4d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3686,-872 3704,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="5157@x" ObjectIDND1="5027@x" ObjectIDND2="5030@x" ObjectIDZND0="g_3ab12e0@0" Pin0InfoVect0LinkObjId="g_3ab12e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dd4b60_0" Pin1InfoVect1LinkObjId="SW-30191_0" Pin1InfoVect2LinkObjId="SW-30194_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3686,-872 3704,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c4d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-1108 3793,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5030@1" ObjectIDZND0="5157@x" ObjectIDZND1="g_3ab12e0@0" ObjectIDZND2="5027@x" Pin0InfoVect0LinkObjId="g_3dd4b60_0" Pin0InfoVect1LinkObjId="g_3ab12e0_0" Pin0InfoVect2LinkObjId="SW-30191_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-1108 3793,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c4d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-1108 3793,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="5157@x" ObjectIDND1="g_3ab12e0@0" ObjectIDND2="5030@x" ObjectIDZND0="5027@1" Pin0InfoVect0LinkObjId="SW-30191_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dd4b60_0" Pin1InfoVect1LinkObjId="g_3ab12e0_0" Pin1InfoVect2LinkObjId="SW-30194_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-1108 3793,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c4eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-302 3508,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3aaeab0@1" ObjectIDZND0="5136@0" Pin0InfoVect0LinkObjId="SW-30300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3aaeab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-302 3508,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c4ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-304 3586,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3ddeee0@1" ObjectIDZND0="5133@0" Pin0InfoVect0LinkObjId="SW-30297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ddeee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-304 3586,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c4eee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-305 3657,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3a9bbb0@1" ObjectIDZND0="5130@0" Pin0InfoVect0LinkObjId="SW-30294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a9bbb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-305 3657,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c4f0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-305 3723,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3aa2a80@1" ObjectIDZND0="5127@0" Pin0InfoVect0LinkObjId="SW-30291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3aa2a80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-305 3723,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c4f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-309 3792,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3b2c0d0@1" ObjectIDZND0="5124@0" Pin0InfoVect0LinkObjId="SW-30288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b2c0d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-309 3792,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-610 5070,-610 5070,-645 4409,-645 4409,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="5048@x" ObjectIDZND0="5158@x" ObjectIDZND1="g_3c22470@0" ObjectIDZND2="5158@x" Pin0InfoVect0LinkObjId="g_3c23350_0" Pin0InfoVect1LinkObjId="g_3c22470_0" Pin0InfoVect2LinkObjId="g_3c23350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-30212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-610 5070,-610 5070,-645 4409,-645 4409,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8d780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4409,-674 4409,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5158@x" ObjectIDND1="g_3c22470@0" ObjectIDND2="5048@x" ObjectIDZND0="5158@x" ObjectIDZND1="g_3c22470@0" ObjectIDZND2="5048@x" Pin0InfoVect0LinkObjId="g_3c23350_0" Pin0InfoVect1LinkObjId="g_3c22470_0" Pin0InfoVect2LinkObjId="SW-30212_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c23350_0" Pin1InfoVect1LinkObjId="g_3c22470_0" Pin1InfoVect2LinkObjId="SW-30212_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4409,-674 4409,-675 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-16" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3424.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16" ObjectName="DYN-CX_SY"/>
     <cge:Meas_Ref ObjectId="16"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="5228" cy="-522" fill="rgb(0,0,0)" rx="10" ry="10.5" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="CX_SY" flowDrawDirect="1" flowShape="0" id="AC-110kV.lushangIhui_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1167 3926,-1231 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9184" ObjectName="AC-110kV.lushangIhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3926,-1167 3926,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="CX_SY" flowDrawDirect="1" flowShape="0" id="AC-110kV.lushangIIhui_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4329,-1170 4329,-1235 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9183" ObjectName="AC-110kV.lushangIIhui_line"/>
    <cge:TPSR_Ref TObjectID="9183_SS-16"/></metadata>
   <polyline fill="none" opacity="0" points="4329,-1170 4329,-1235 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-30195">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3559.000000 -619.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5031" ObjectName="SW-CX_SY.CX_SY_1010SW"/>
     <cge:Meas_Ref ObjectId="30195"/>
    <cge:TPSR_Ref TObjectID="5031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 -809.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5080" ObjectName="SW-CX_SY.CX_SY_3122SW"/>
     <cge:Meas_Ref ObjectId="30244"/>
    <cge:TPSR_Ref TObjectID="5080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30233">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3511.000000 -923.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5070" ObjectName="SW-CX_SY.CX_SY_19010SW"/>
     <cge:Meas_Ref ObjectId="30233"/>
    <cge:TPSR_Ref TObjectID="5070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30229">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4036.000000 -829.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5065" ObjectName="SW-CX_SY.CX_SY_1121SW"/>
     <cge:Meas_Ref ObjectId="30229"/>
    <cge:TPSR_Ref TObjectID="5065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30231">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4036.000000 -761.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5067" ObjectName="SW-CX_SY.CX_SY_11217SW"/>
     <cge:Meas_Ref ObjectId="30231"/>
    <cge:TPSR_Ref TObjectID="5067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30230">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4115.000000 -830.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5066" ObjectName="SW-CX_SY.CX_SY_1122SW"/>
     <cge:Meas_Ref ObjectId="30230"/>
    <cge:TPSR_Ref TObjectID="5066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30232">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4115.000000 -762.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5068" ObjectName="SW-CX_SY.CX_SY_11227SW"/>
     <cge:Meas_Ref ObjectId="30232"/>
    <cge:TPSR_Ref TObjectID="5068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30258">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 -545.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5094" ObjectName="SW-CX_SY.CX_SY_3622SW"/>
     <cge:Meas_Ref ObjectId="30258"/>
    <cge:TPSR_Ref TObjectID="5094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30259">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.000000 -545.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5095" ObjectName="SW-CX_SY.CX_SY_3626SW"/>
     <cge:Meas_Ref ObjectId="30259"/>
    <cge:TPSR_Ref TObjectID="5095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5122" ObjectName="SW-CX_SY.CX_SY_0121SW"/>
     <cge:Meas_Ref ObjectId="30286"/>
    <cge:TPSR_Ref TObjectID="5122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30287">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5123" ObjectName="SW-CX_SY.CX_SY_0122SW"/>
     <cge:Meas_Ref ObjectId="30287"/>
    <cge:TPSR_Ref TObjectID="5123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30310">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5020.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5146" ObjectName="SW-CX_SY.CX_SY_0722SW"/>
     <cge:Meas_Ref ObjectId="30310"/>
    <cge:TPSR_Ref TObjectID="5146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30311">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5020.000000 -196.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5147" ObjectName="SW-CX_SY.CX_SY_0726SW"/>
     <cge:Meas_Ref ObjectId="30311"/>
    <cge:TPSR_Ref TObjectID="5147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30313">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5095.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5149" ObjectName="SW-CX_SY.CX_SY_0732SW"/>
     <cge:Meas_Ref ObjectId="30313"/>
    <cge:TPSR_Ref TObjectID="5149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5095.000000 -197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5150" ObjectName="SW-CX_SY.CX_SY_0736SW"/>
     <cge:Meas_Ref ObjectId="30314"/>
    <cge:TPSR_Ref TObjectID="5150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5173.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5153" ObjectName="SW-CX_SY.CX_SY_0746SW"/>
     <cge:Meas_Ref ObjectId="30317"/>
    <cge:TPSR_Ref TObjectID="5153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5173.000000 -365.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5152" ObjectName="SW-CX_SY.CX_SY_0742SW"/>
     <cge:Meas_Ref ObjectId="30316"/>
    <cge:TPSR_Ref TObjectID="5152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57017">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3560.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5069" ObjectName="SW-CX_SY.CX_SY_1901SW"/>
     <cge:Meas_Ref ObjectId="57017"/>
    <cge:TPSR_Ref TObjectID="5069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -1003.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10576" ObjectName="SW-CX_SY.CX_SY_19017SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -872.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5079" ObjectName="SW-CX_SY.CX_SY_3121SW"/>
     <cge:Meas_Ref ObjectId="30243"/>
    <cge:TPSR_Ref TObjectID="5079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30194">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3734.000000 -1103.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5030" ObjectName="SW-CX_SY.CX_SY_10167SW"/>
     <cge:Meas_Ref ObjectId="30194"/>
    <cge:TPSR_Ref TObjectID="5030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30191">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -1054.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5027" ObjectName="SW-CX_SY.CX_SY_1016SW"/>
     <cge:Meas_Ref ObjectId="30191"/>
    <cge:TPSR_Ref TObjectID="5027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30193">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3735.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5029" ObjectName="SW-CX_SY.CX_SY_10160SW"/>
     <cge:Meas_Ref ObjectId="30193"/>
    <cge:TPSR_Ref TObjectID="5029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30192">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3735.000000 -962.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5028" ObjectName="SW-CX_SY.CX_SY_10117SW"/>
     <cge:Meas_Ref ObjectId="30192"/>
    <cge:TPSR_Ref TObjectID="5028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30217">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.000000 -905.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5053" ObjectName="SW-CX_SY.CX_SY_1321SW"/>
     <cge:Meas_Ref ObjectId="30217"/>
    <cge:TPSR_Ref TObjectID="5053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30218">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.000000 -1009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5054" ObjectName="SW-CX_SY.CX_SY_1326SW"/>
     <cge:Meas_Ref ObjectId="30218"/>
    <cge:TPSR_Ref TObjectID="5054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30221">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -1054.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5057" ObjectName="SW-CX_SY.CX_SY_13267SW"/>
     <cge:Meas_Ref ObjectId="30221"/>
    <cge:TPSR_Ref TObjectID="5057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30220">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5056" ObjectName="SW-CX_SY.CX_SY_13260SW"/>
     <cge:Meas_Ref ObjectId="30220"/>
    <cge:TPSR_Ref TObjectID="5056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30219">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -950.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5055" ObjectName="SW-CX_SY.CX_SY_13217SW"/>
     <cge:Meas_Ref ObjectId="30219"/>
    <cge:TPSR_Ref TObjectID="5055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30203">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4036.000000 -911.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5039" ObjectName="SW-CX_SY.CX_SY_1021SW"/>
     <cge:Meas_Ref ObjectId="30203"/>
    <cge:TPSR_Ref TObjectID="5039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30204">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 -912.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5040" ObjectName="SW-CX_SY.CX_SY_1022SW"/>
     <cge:Meas_Ref ObjectId="30204"/>
    <cge:TPSR_Ref TObjectID="5040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30206">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3995.000000 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5042" ObjectName="SW-CX_SY.CX_SY_10217SW"/>
     <cge:Meas_Ref ObjectId="30206"/>
    <cge:TPSR_Ref TObjectID="5042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30207">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -1049.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5043" ObjectName="SW-CX_SY.CX_SY_10260SW"/>
     <cge:Meas_Ref ObjectId="30207"/>
    <cge:TPSR_Ref TObjectID="5043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30205">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 -1068.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5041" ObjectName="SW-CX_SY.CX_SY_1026SW"/>
     <cge:Meas_Ref ObjectId="30205"/>
    <cge:TPSR_Ref TObjectID="5041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30208">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -1123.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5044" ObjectName="SW-CX_SY.CX_SY_10267SW"/>
     <cge:Meas_Ref ObjectId="30208"/>
    <cge:TPSR_Ref TObjectID="5044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30223">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4320.000000 -906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5059" ObjectName="SW-CX_SY.CX_SY_1332SW"/>
     <cge:Meas_Ref ObjectId="30223"/>
    <cge:TPSR_Ref TObjectID="5059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30224">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4320.000000 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5060" ObjectName="SW-CX_SY.CX_SY_1336SW"/>
     <cge:Meas_Ref ObjectId="30224"/>
    <cge:TPSR_Ref TObjectID="5060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30227">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -1055.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5063" ObjectName="SW-CX_SY.CX_SY_13367SW"/>
     <cge:Meas_Ref ObjectId="30227"/>
    <cge:TPSR_Ref TObjectID="5063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30226">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -998.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5062" ObjectName="SW-CX_SY.CX_SY_13360SW"/>
     <cge:Meas_Ref ObjectId="30226"/>
    <cge:TPSR_Ref TObjectID="5062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30225">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5061" ObjectName="SW-CX_SY.CX_SY_13327SW"/>
     <cge:Meas_Ref ObjectId="30225"/>
    <cge:TPSR_Ref TObjectID="5061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30236">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -927.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5072" ObjectName="SW-CX_SY.CX_SY_19020SW"/>
     <cge:Meas_Ref ObjectId="30236"/>
    <cge:TPSR_Ref TObjectID="5072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30235">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -945.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5071" ObjectName="SW-CX_SY.CX_SY_1902SW"/>
     <cge:Meas_Ref ObjectId="30235"/>
    <cge:TPSR_Ref TObjectID="5071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30237">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5073" ObjectName="SW-CX_SY.CX_SY_19027SW"/>
     <cge:Meas_Ref ObjectId="30237"/>
    <cge:TPSR_Ref TObjectID="5073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30254">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -783.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5090" ObjectName="SW-CX_SY.CX_SY_3612SW"/>
     <cge:Meas_Ref ObjectId="30254"/>
    <cge:TPSR_Ref TObjectID="5090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30255">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4993.000000 -783.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5091" ObjectName="SW-CX_SY.CX_SY_3616SW"/>
     <cge:Meas_Ref ObjectId="30255"/>
    <cge:TPSR_Ref TObjectID="5091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -364.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5036" ObjectName="SW-CX_SY.CX_SY_0011SW"/>
     <cge:Meas_Ref ObjectId="30200"/>
    <cge:TPSR_Ref TObjectID="5036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5037" ObjectName="SW-CX_SY.CX_SY_0016SW"/>
     <cge:Meas_Ref ObjectId="30201"/>
    <cge:TPSR_Ref TObjectID="5037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5154" ObjectName="SW-CX_SY.CX_SY_0311SW"/>
     <cge:Meas_Ref ObjectId="30318"/>
    <cge:TPSR_Ref TObjectID="5154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.326087 -0.000000 0.000000 -3.214286 4201.000000 -431.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5076" ObjectName="SW-CX_SY.CX_SY_0901SW"/>
     <cge:Meas_Ref ObjectId="30240"/>
    <cge:TPSR_Ref TObjectID="5076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30319">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5155" ObjectName="SW-CX_SY.CX_SY_0612SW"/>
     <cge:Meas_Ref ObjectId="30319"/>
    <cge:TPSR_Ref TObjectID="5155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.326087 -0.000000 0.000000 -3.214286 4441.000000 -431.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5077" ObjectName="SW-CX_SY.CX_SY_0902SW"/>
     <cge:Meas_Ref ObjectId="30241"/>
    <cge:TPSR_Ref TObjectID="5077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30214">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5050" ObjectName="SW-CX_SY.CX_SY_0022SW"/>
     <cge:Meas_Ref ObjectId="30214"/>
    <cge:TPSR_Ref TObjectID="5050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30215">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5051" ObjectName="SW-CX_SY.CX_SY_0026SW"/>
     <cge:Meas_Ref ObjectId="30215"/>
    <cge:TPSR_Ref TObjectID="5051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4878.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5140" ObjectName="SW-CX_SY.CX_SY_0692SW"/>
     <cge:Meas_Ref ObjectId="30304"/>
    <cge:TPSR_Ref TObjectID="5140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4878.000000 -196.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5141" ObjectName="SW-CX_SY.CX_SY_0696SW"/>
     <cge:Meas_Ref ObjectId="30305"/>
    <cge:TPSR_Ref TObjectID="5141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4951.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5143" ObjectName="SW-CX_SY.CX_SY_0712SW"/>
     <cge:Meas_Ref ObjectId="30307"/>
    <cge:TPSR_Ref TObjectID="5143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4951.000000 -197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5144" ObjectName="SW-CX_SY.CX_SY_0716SW"/>
     <cge:Meas_Ref ObjectId="30308"/>
    <cge:TPSR_Ref TObjectID="5144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5086" ObjectName="SW-CX_SY.CX_SY_3321SW"/>
     <cge:Meas_Ref ObjectId="30250"/>
    <cge:TPSR_Ref TObjectID="5086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30251">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5087" ObjectName="SW-CX_SY.CX_SY_3326SW"/>
     <cge:Meas_Ref ObjectId="30251"/>
    <cge:TPSR_Ref TObjectID="5087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30246">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -894.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5082" ObjectName="SW-CX_SY.CX_SY_3311SW"/>
     <cge:Meas_Ref ObjectId="30246"/>
    <cge:TPSR_Ref TObjectID="5082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30247">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4993.000000 -894.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5083" ObjectName="SW-CX_SY.CX_SY_3316SW"/>
     <cge:Meas_Ref ObjectId="30247"/>
    <cge:TPSR_Ref TObjectID="5083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30190">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -907.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5026" ObjectName="SW-CX_SY.CX_SY_1011SW"/>
     <cge:Meas_Ref ObjectId="30190"/>
    <cge:TPSR_Ref TObjectID="5026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30209">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -676.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5045" ObjectName="SW-CX_SY.CX_SY_1020SW"/>
     <cge:Meas_Ref ObjectId="30209"/>
    <cge:TPSR_Ref TObjectID="5045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30268">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5104" ObjectName="SW-CX_SY.CX_SY_0321SW"/>
     <cge:Meas_Ref ObjectId="30268"/>
    <cge:TPSR_Ref TObjectID="5104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5107" ObjectName="SW-CX_SY.CX_SY_03260SW"/>
     <cge:Meas_Ref ObjectId="30271"/>
    <cge:TPSR_Ref TObjectID="5107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -206.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5106" ObjectName="SW-CX_SY.CX_SY_03267SW"/>
     <cge:Meas_Ref ObjectId="30270"/>
    <cge:TPSR_Ref TObjectID="5106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -71.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5108" ObjectName="SW-CX_SY.CX_SY_03200SW"/>
     <cge:Meas_Ref ObjectId="30272"/>
    <cge:TPSR_Ref TObjectID="5108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5105" ObjectName="SW-CX_SY.CX_SY_0326SW"/>
     <cge:Meas_Ref ObjectId="30269"/>
    <cge:TPSR_Ref TObjectID="5105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30274">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5110" ObjectName="SW-CX_SY.CX_SY_0622SW"/>
     <cge:Meas_Ref ObjectId="30274"/>
    <cge:TPSR_Ref TObjectID="5110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30277">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -255.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5113" ObjectName="SW-CX_SY.CX_SY_06260SW"/>
     <cge:Meas_Ref ObjectId="30277"/>
    <cge:TPSR_Ref TObjectID="5113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30276">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5112" ObjectName="SW-CX_SY.CX_SY_06267SW"/>
     <cge:Meas_Ref ObjectId="30276"/>
    <cge:TPSR_Ref TObjectID="5112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30275">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -212.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5111" ObjectName="SW-CX_SY.CX_SY_0626SW"/>
     <cge:Meas_Ref ObjectId="30275"/>
    <cge:TPSR_Ref TObjectID="5111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -70.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5114" ObjectName="SW-CX_SY.CX_SY_06200SW"/>
     <cge:Meas_Ref ObjectId="30278"/>
    <cge:TPSR_Ref TObjectID="5114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5116" ObjectName="SW-CX_SY.CX_SY_0632SW"/>
     <cge:Meas_Ref ObjectId="30280"/>
    <cge:TPSR_Ref TObjectID="5116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -255.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5119" ObjectName="SW-CX_SY.CX_SY_06360SW"/>
     <cge:Meas_Ref ObjectId="30283"/>
    <cge:TPSR_Ref TObjectID="5119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5118" ObjectName="SW-CX_SY.CX_SY_06367SW"/>
     <cge:Meas_Ref ObjectId="30282"/>
    <cge:TPSR_Ref TObjectID="5118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -212.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5117" ObjectName="SW-CX_SY.CX_SY_0636SW"/>
     <cge:Meas_Ref ObjectId="30281"/>
    <cge:TPSR_Ref TObjectID="5117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30284">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -70.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5120" ObjectName="SW-CX_SY.CX_SY_06300SW"/>
     <cge:Meas_Ref ObjectId="30284"/>
    <cge:TPSR_Ref TObjectID="5120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30252">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.000000 -969.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5088" ObjectName="SW-CX_SY.CX_SY_3329SW"/>
     <cge:Meas_Ref ObjectId="30252"/>
    <cge:TPSR_Ref TObjectID="5088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5098" ObjectName="SW-CX_SY.CX_SY_0331SW"/>
     <cge:Meas_Ref ObjectId="30262"/>
    <cge:TPSR_Ref TObjectID="5098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -255.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5101" ObjectName="SW-CX_SY.CX_SY_03360SW"/>
     <cge:Meas_Ref ObjectId="30265"/>
    <cge:TPSR_Ref TObjectID="5101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5100" ObjectName="SW-CX_SY.CX_SY_03367SW"/>
     <cge:Meas_Ref ObjectId="30264"/>
    <cge:TPSR_Ref TObjectID="5100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -212.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5099" ObjectName="SW-CX_SY.CX_SY_0336SW"/>
     <cge:Meas_Ref ObjectId="30263"/>
    <cge:TPSR_Ref TObjectID="5099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -70.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5102" ObjectName="SW-CX_SY.CX_SY_03300SW"/>
     <cge:Meas_Ref ObjectId="30266"/>
    <cge:TPSR_Ref TObjectID="5102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30260">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5133.000000 -569.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5096" ObjectName="SW-CX_SY.CX_SY_3629SW"/>
     <cge:Meas_Ref ObjectId="30260"/>
    <cge:TPSR_Ref TObjectID="5096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5134.000000 -807.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5092" ObjectName="SW-CX_SY.CX_SY_3619SW"/>
     <cge:Meas_Ref ObjectId="30256"/>
    <cge:TPSR_Ref TObjectID="5092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5128" ObjectName="SW-CX_SY.CX_SY_0411SW"/>
     <cge:Meas_Ref ObjectId="30292"/>
    <cge:TPSR_Ref TObjectID="5128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5137" ObjectName="SW-CX_SY.CX_SY_0441SW"/>
     <cge:Meas_Ref ObjectId="30301"/>
    <cge:TPSR_Ref TObjectID="5137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30298">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3577.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5134" ObjectName="SW-CX_SY.CX_SY_0431SW"/>
     <cge:Meas_Ref ObjectId="30298"/>
    <cge:TPSR_Ref TObjectID="5134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5131" ObjectName="SW-CX_SY.CX_SY_0421SW"/>
     <cge:Meas_Ref ObjectId="30295"/>
    <cge:TPSR_Ref TObjectID="5131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30289">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 -367.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5125" ObjectName="SW-CX_SY.CX_SY_0391SW"/>
     <cge:Meas_Ref ObjectId="30289"/>
    <cge:TPSR_Ref TObjectID="5125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3499.000000 -197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5138" ObjectName="SW-CX_SY.CX_SY_0446SW"/>
     <cge:Meas_Ref ObjectId="30302"/>
    <cge:TPSR_Ref TObjectID="5138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30299">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3577.000000 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5135" ObjectName="SW-CX_SY.CX_SY_0436SW"/>
     <cge:Meas_Ref ObjectId="30299"/>
    <cge:TPSR_Ref TObjectID="5135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30296">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5132" ObjectName="SW-CX_SY.CX_SY_0426SW"/>
     <cge:Meas_Ref ObjectId="30296"/>
    <cge:TPSR_Ref TObjectID="5132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30293">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5129" ObjectName="SW-CX_SY.CX_SY_0416SW"/>
     <cge:Meas_Ref ObjectId="30293"/>
    <cge:TPSR_Ref TObjectID="5129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30290">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 -204.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5126" ObjectName="SW-CX_SY.CX_SY_0396SW"/>
     <cge:Meas_Ref ObjectId="30290"/>
    <cge:TPSR_Ref TObjectID="5126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 -708.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5075" ObjectName="SW-CX_SY.CX_SY_3902SW"/>
     <cge:Meas_Ref ObjectId="30239"/>
    <cge:TPSR_Ref TObjectID="5075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30211">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 -605.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5047" ObjectName="SW-CX_SY.CX_SY_3022SW"/>
     <cge:Meas_Ref ObjectId="30211"/>
    <cge:TPSR_Ref TObjectID="5047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30212">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -605.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5048" ObjectName="SW-CX_SY.CX_SY_3026SW"/>
     <cge:Meas_Ref ObjectId="30212"/>
    <cge:TPSR_Ref TObjectID="5048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30238">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -1085.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5074" ObjectName="SW-CX_SY.CX_SY_3901SW"/>
     <cge:Meas_Ref ObjectId="30238"/>
    <cge:TPSR_Ref TObjectID="5074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30197">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -1023.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5033" ObjectName="SW-CX_SY.CX_SY_3011SW"/>
     <cge:Meas_Ref ObjectId="30197"/>
    <cge:TPSR_Ref TObjectID="5033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-30198">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4988.000000 -1023.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5034" ObjectName="SW-CX_SY.CX_SY_3016SW"/>
     <cge:Meas_Ref ObjectId="30198"/>
    <cge:TPSR_Ref TObjectID="5034"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -977.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -977.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -977.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -977.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -977.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -977.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -977.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3808.000000 -1009.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3800.000000 -937.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3739.000000 -993.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3738.000000 -1066.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3737.000000 -1134.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3523.000000 -649.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3533.000000 -1117.000000) translate(0,15)">110kVI段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3533.000000 -1117.000000) translate(0,33)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -1166.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -1166.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -1166.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -1166.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -1166.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -442.000000) translate(0,12)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3473.000000 -440.000000) translate(0,12)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4870.000000 -488.000000) translate(0,12)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4869.000000 -1159.000000) translate(0,12)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -1186.000000) translate(0,15)">禄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -1186.000000) translate(0,33)">上</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -1186.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -1186.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -1186.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3935.000000 -986.000000) translate(0,12)">132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -935.000000) translate(0,12)">1321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3871.000000 -1053.000000) translate(0,12)">13267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3871.000000 -1028.000000) translate(0,12)">13260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3871.000000 -982.000000) translate(0,12)">13217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3933.000000 -1039.000000) translate(0,12)">1326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4119.000000 -1029.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4117.000000 -1098.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4056.000000 -1154.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4055.000000 -1080.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4132.000000 -942.000000) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4052.000000 -941.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -1002.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4072.000000 -843.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4052.000000 -859.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4131.000000 -860.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4052.000000 -791.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4131.000000 -792.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4093.000000 -649.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -767.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3725.000000 -619.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4338.000000 -987.000000) translate(0,12)">133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4336.000000 -936.000000) translate(0,12)">1332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4336.000000 -1040.000000) translate(0,12)">1336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4274.000000 -1053.000000) translate(0,12)">13367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4274.000000 -1029.000000) translate(0,12)">13360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4274.000000 -983.000000) translate(0,12)">13327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4632.000000 -975.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4570.000000 -958.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4570.000000 -1038.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3518.000000 -335.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -390.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -227.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3480.000000 -167.000000) translate(0,15)">入</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3480.000000 -167.000000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3480.000000 -167.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3480.000000 -167.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3480.000000 -167.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3557.000000 -167.000000) translate(0,15)">棠</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3557.000000 -167.000000) translate(0,33)">海</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3557.000000 -167.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3557.000000 -167.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3557.000000 -167.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -167.000000) translate(0,15)">棠</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -167.000000) translate(0,33)">海</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -167.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -167.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -167.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3698.000000 -131.000000) translate(0,15)">官</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3698.000000 -131.000000) translate(0,33)">洼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3698.000000 -131.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3766.000000 -131.000000) translate(0,15)">科</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3766.000000 -131.000000) translate(0,33)">甲</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3766.000000 -131.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3840.000000 -167.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3840.000000 -167.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3840.000000 -167.000000) translate(0,51)">5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3840.000000 -167.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3840.000000 -167.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -239.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -239.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -239.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -239.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -239.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.000000 -230.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.000000 -230.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.000000 -230.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.000000 -230.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4412.000000 -230.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -342.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4793.000000 -186.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4793.000000 -186.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4793.000000 -186.000000) translate(0,51)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4793.000000 -186.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -131.000000) translate(0,15)">横</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -131.000000) translate(0,33)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -131.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4932.000000 -134.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4932.000000 -134.000000) translate(0,33)">洼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4932.000000 -134.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5002.000000 -167.000000) translate(0,15)">入</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5002.000000 -167.000000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5002.000000 -167.000000) translate(0,51)">III</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5002.000000 -167.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5002.000000 -167.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5076.000000 -134.000000) translate(0,15)">沙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5076.000000 -134.000000) translate(0,33)">湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5076.000000 -134.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -167.000000) translate(0,15)">入</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -167.000000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -167.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -167.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -167.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3597.000000 -337.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3594.000000 -392.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3594.000000 -229.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3667.000000 -338.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3664.000000 -393.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3664.000000 -230.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3733.000000 -338.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3730.000000 -393.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3730.000000 -230.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3802.000000 -342.000000) translate(0,12)">039</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3799.000000 -397.000000) translate(0,12)">0391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3799.000000 -234.000000) translate(0,12)">0396</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3966.000000 -355.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3964.000000 -401.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3988.000000 -101.000000) translate(0,12)">03300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3905.000000 -283.000000) translate(0,12)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3905.000000 -234.000000) translate(0,12)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3964.000000 -242.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4050.000000 -335.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4048.000000 -394.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4048.000000 -283.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4144.000000 -356.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -401.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -243.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4083.000000 -287.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4083.000000 -237.000000) translate(0,12)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4169.000000 -102.000000) translate(0,12)">03200</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4214.000000 -392.000000) translate(0,12)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4325.000000 -379.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4285.000000 -391.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4403.000000 -392.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4454.000000 -392.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4574.000000 -355.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4572.000000 -242.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4513.000000 -286.000000) translate(0,12)">06260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4513.000000 -236.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4597.000000 -101.000000) translate(0,12)">06200</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -335.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -393.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -271.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4783.000000 -101.000000) translate(0,12)">06300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -401.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4702.000000 -286.000000) translate(0,12)">06360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4702.000000 -236.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -242.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4763.000000 -355.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4896.000000 -334.000000) translate(0,12)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -389.000000) translate(0,12)">0692</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -226.000000) translate(0,12)">0696</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4969.000000 -335.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4966.000000 -390.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4966.000000 -227.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5038.000000 -334.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5035.000000 -389.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5035.000000 -226.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5113.000000 -335.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5110.000000 -390.000000) translate(0,12)">0732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5110.000000 -227.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5193.000000 -337.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5190.000000 -394.000000) translate(0,12)">0742</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5188.000000 -230.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5143.000000 -1128.000000) translate(0,15)">预留1回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5124.000000 -1069.000000) translate(0,15)">35kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5190.000000 -982.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5151.000000 -944.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5134.000000 -926.000000) translate(0,15)">（至中村）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5151.000000 -892.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -868.000000) translate(0,15)">分</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -868.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5134.000000 -765.000000) translate(0,15)">（至城北）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5151.000000 -783.000000) translate(0,15)">上城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5183.000000 -818.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5123.000000 -693.000000) translate(0,15)">35kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5134.000000 -527.000000) translate(0,15)">（至大德）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5151.000000 -545.000000) translate(0,15)">上大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5195.000000 -585.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5143.000000 -490.000000) translate(0,15)">预留2回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4894.000000 -1117.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4946.000000 -1053.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4889.000000 -1055.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4995.000000 -1055.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4952.000000 -973.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5000.000000 -975.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4895.000000 -975.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5071.000000 -998.000000) translate(0,12)">3329</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4951.000000 -923.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4894.000000 -925.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5000.000000 -925.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4941.000000 -854.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4882.000000 -871.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4881.000000 -840.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4951.000000 -812.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4894.000000 -810.000000) translate(0,12)">3612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5000.000000 -814.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4896.000000 -740.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4954.000000 -635.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4896.000000 -637.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5002.000000 -637.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4953.000000 -574.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4895.000000 -576.000000) translate(0,12)">3622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5001.000000 -576.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4218.000000 -461.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4458.000000 -461.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3576.000000 -971.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3518.000000 -954.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1167.500000) translate(0,16)">上营变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -1034.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3800.000000 -1084.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -1176.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -1176.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -1176.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -1176.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -1176.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4587.000000 -1121.000000) translate(0,15)">110kVII段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4587.000000 -1121.000000) translate(0,33)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -591.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -591.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -591.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -591.000000) translate(0,69)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -591.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -591.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4240.000000 -591.000000) translate(0,123)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -595.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -595.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -595.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -595.000000) translate(0,69)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -595.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -595.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -595.000000) translate(0,123)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4572.000000 -401.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3514.000000 -889.000000) translate(0,12)">110kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4620.000000 -890.000000) translate(0,12)">110kVIIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -1191.000000) translate(0,15)">禄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -1191.000000) translate(0,33)">上</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -1191.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -1191.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -1191.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,33)">SFSZ10-50000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,51)">110±8×1.25%/37±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,69)">50/50/50MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,105)">Uk1-2%=10.44</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,123)">Uk1-3%=18.27</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3692.000000 -850.000000) translate(0,141)">Uk2-3%=6.52</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,33)">SFSZ10-50000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,51)">110±8×1.25%/37±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,69)">50/50/50MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,105)">Uk1-2%=10.35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,123)">Uk1-3%=18.20</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -857.000000) translate(0,141)">Uk2-3%=6.57</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,42)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -190.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,42)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -192.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,42)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -192.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,42)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4706.000000 -191.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4066.000000 -811.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3420.000000 -1163.000000) translate(0,16)">AVC</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3663bf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -593.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30f7880">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3508.333333 -922.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3fe20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 -736.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31affa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -737.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dd2cc0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3509.333333 -1002.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dd40d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4102.000000 -673.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ab4da0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3732.333333 -1102.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3abadf0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3731.333333 -1034.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac0aa0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3732.333333 -961.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3acd7d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3865.000000 -1053.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ad1430">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3865.000000 -996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ad52f0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3865.000000 -949.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3adf150">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3995.333333 -970.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ae6a00">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4049.333333 -1048.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aed890">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4050.333333 -1122.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3afa820">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4268.000000 -1054.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3afe480">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4268.000000 -997.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b02340">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4268.000000 -951.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b07470">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4564.333333 -926.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b103b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4564.333333 -1006.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bb8ea0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4077.000000 -255.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bb9b90">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4080.000000 -205.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bba880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -70.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bc3b00">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4507.000000 -254.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bc47f0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4510.000000 -204.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bd1300">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4636.000000 -69.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bd7570">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4696.000000 -254.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bd8260">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4699.000000 -204.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3be4d70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -69.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bfafa0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3899.000000 -254.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bfbc90">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3902.000000 -204.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c087a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.000000 -69.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="CX_SY"/>
</svg>