<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-143" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 2022 1227">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="64" x2="64" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_36d24d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape147">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.05405"/>
    <polyline points="58,100 64,100 " stroke-width="1.05405"/>
    <polyline points="64,100 64,93 " stroke-width="1.05405"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <circle cx="15" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="10" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="15" y1="50" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="45" y2="50"/>
   </symbol>
   <symbol id="voltageTransformer:shape78">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="40" x2="40" y1="14" y2="1"/>
    <polyline points="6,39 6,23 29,23 " stroke-width="0.587413"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="41" x2="41" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="38" x2="41" y1="43" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="38" x2="41" y1="40" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="30" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="30" x2="30" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="48" x2="45" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="45" x2="45" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="45" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="30" y2="33"/>
    <circle cx="30" cy="41" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="45" cy="30" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="40" cy="41" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="38" x2="35" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="35" x2="35" y1="23" y2="26"/>
    <circle cx="25" cy="30" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="35" y1="21" y2="23"/>
    <circle cx="35" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="23" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="47" x2="47" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="47" y1="7" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="23" x2="23" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="30" x2="30" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="30" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="23" y1="8" y2="8"/>
   </symbol>
   <symbol id="voltageTransformer:shape53">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="70" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="26" x2="23" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="29" x2="26" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="26" x2="26" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="41" x2="38" y1="78" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="44" x2="41" y1="75" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="41" x2="41" y1="78" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="68" x2="56" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="58" x2="66" y1="96" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="60" x2="64" y1="99" y2="99"/>
    <rect height="27" stroke-width="0.416667" width="14" x="34" y="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="58"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="72" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="78" y2="78"/>
    <ellipse cx="40" cy="78" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="26" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="26" cy="78" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="30" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="81" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="28" y1="81" y2="77"/>
    <circle cx="40" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="41" x2="38" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="44" x2="41" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="41" x2="41" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="62" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="62" x2="62" y1="78" y2="93"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3312d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_330d980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3309b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_330af30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3307540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33061a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_330edd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_32fe2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_32fe570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_32fe570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3300420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3300420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32fb7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32fb7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_330cdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32fc2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3315fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_32ff4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_32ee020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32ed830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32d9320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3302de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_32ec870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32e6d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32e7910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32fa050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_32f01e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33150d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_330fb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_32dbf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_32ed490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3312580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3301fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_32eccc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_38440d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1237" width="2032" x="-335" y="-962"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.000000 -187.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3374900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 301.000000 -202.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a9ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -217.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336c9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 638.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336cee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.000000 623.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336d120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 608.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336d450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 324.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336d6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 796.000000 309.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3318f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 294.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_386bc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.000000 515.000000) translate(0,12)">绕温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_386c780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.000000 530.000000) translate(0,12)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_386cce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.000000 545.000000) translate(0,12)">档位(档)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38710d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 214.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3871330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 229.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3871570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 199.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38717b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 243.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="732,89 730,87 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="737,78 740,78 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="738,108 763,108 763,137 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1385,-673 1410,-673 1410,-642 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(170,85,127)" stroke-width="1" width="12" x="643" y="-461"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-92022">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.166667 -674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19635" ObjectName="SW-CX_MBT.CX_MBT_1011SW"/>
     <cge:Meas_Ref ObjectId="92022"/>
    <cge:TPSR_Ref TObjectID="19635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92023">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.166667 -522.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19636" ObjectName="SW-CX_MBT.CX_MBT_1016SW"/>
     <cge:Meas_Ref ObjectId="92023"/>
    <cge:TPSR_Ref TObjectID="19636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92033">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.166667 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19644" ObjectName="SW-CX_MBT.CX_MBT_3016SW"/>
     <cge:Meas_Ref ObjectId="92033"/>
    <cge:TPSR_Ref TObjectID="19644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92041">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.000000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19649" ObjectName="SW-CX_MBT.CX_MBT_3616SW"/>
     <cge:Meas_Ref ObjectId="92041"/>
    <cge:TPSR_Ref TObjectID="19649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92032">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.166667 -193.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19643" ObjectName="SW-CX_MBT.CX_MBT_3011SW"/>
     <cge:Meas_Ref ObjectId="92032"/>
    <cge:TPSR_Ref TObjectID="19643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92040">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19648" ObjectName="SW-CX_MBT.CX_MBT_3611SW"/>
     <cge:Meas_Ref ObjectId="92040"/>
    <cge:TPSR_Ref TObjectID="19648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92028">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.000000 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19641" ObjectName="SW-CX_MBT.CX_MBT_1010SW"/>
     <cge:Meas_Ref ObjectId="92028"/>
    <cge:TPSR_Ref TObjectID="19641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92002">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -181.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19631" ObjectName="SW-CX_MBT.CX_MBT_3901SW"/>
     <cge:Meas_Ref ObjectId="92002"/>
    <cge:TPSR_Ref TObjectID="19631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.000000 -227.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19632" ObjectName="SW-CX_MBT.CX_MBT_39017SW"/>
     <cge:Meas_Ref ObjectId="92003"/>
    <cge:TPSR_Ref TObjectID="19632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92050">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 545.000000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19654" ObjectName="SW-CX_MBT.CX_MBT_3626SW"/>
     <cge:Meas_Ref ObjectId="92050"/>
    <cge:TPSR_Ref TObjectID="19654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92049">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 545.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19653" ObjectName="SW-CX_MBT.CX_MBT_3621SW"/>
     <cge:Meas_Ref ObjectId="92049"/>
    <cge:TPSR_Ref TObjectID="19653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19684" ObjectName="SW-CX_MBT.CX_MBT_3636SW"/>
     <cge:Meas_Ref ObjectId="92096"/>
    <cge:TPSR_Ref TObjectID="19684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92095">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19683" ObjectName="SW-CX_MBT.CX_MBT_3631SW"/>
     <cge:Meas_Ref ObjectId="92095"/>
    <cge:TPSR_Ref TObjectID="19683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92099">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19687" ObjectName="SW-CX_MBT.CX_MBT_3010SW"/>
     <cge:Meas_Ref ObjectId="92099"/>
    <cge:TPSR_Ref TObjectID="19687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92081">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19673" ObjectName="SW-CX_MBT.CX_MBT_3646SW"/>
     <cge:Meas_Ref ObjectId="92081"/>
    <cge:TPSR_Ref TObjectID="19673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19668" ObjectName="SW-CX_MBT.CX_MBT_3641SW"/>
     <cge:Meas_Ref ObjectId="92076"/>
    <cge:TPSR_Ref TObjectID="19668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92059">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.000000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19659" ObjectName="SW-CX_MBT.CX_MBT_3666SW"/>
     <cge:Meas_Ref ObjectId="92059"/>
    <cge:TPSR_Ref TObjectID="19659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19658" ObjectName="SW-CX_MBT.CX_MBT_3661SW"/>
     <cge:Meas_Ref ObjectId="92058"/>
    <cge:TPSR_Ref TObjectID="19658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92068">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19664" ObjectName="SW-CX_MBT.CX_MBT_3676SW"/>
     <cge:Meas_Ref ObjectId="92068"/>
    <cge:TPSR_Ref TObjectID="19664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19663" ObjectName="SW-CX_MBT.CX_MBT_3671SW"/>
     <cge:Meas_Ref ObjectId="92067"/>
    <cge:TPSR_Ref TObjectID="19663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 -11.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19669" ObjectName="SW-CX_MBT.CX_MBT_3643SW"/>
     <cge:Meas_Ref ObjectId="92077"/>
    <cge:TPSR_Ref TObjectID="19669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92090">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1504.000000 85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19680" ObjectName="SW-CX_MBT.CX_MBT_3686SW"/>
     <cge:Meas_Ref ObjectId="92090"/>
    <cge:TPSR_Ref TObjectID="19680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92086">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1504.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19676" ObjectName="SW-CX_MBT.CX_MBT_3681SW"/>
     <cge:Meas_Ref ObjectId="92086"/>
    <cge:TPSR_Ref TObjectID="19676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1504.000000 -11.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19677" ObjectName="SW-CX_MBT.CX_MBT_3683SW"/>
     <cge:Meas_Ref ObjectId="92087"/>
    <cge:TPSR_Ref TObjectID="19677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1333.000000 -653.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92025">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.166667 -723.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19638" ObjectName="SW-CX_MBT.CX_MBT_10117SW"/>
     <cge:Meas_Ref ObjectId="92025"/>
    <cge:TPSR_Ref TObjectID="19638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92024">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.166667 -656.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19637" ObjectName="SW-CX_MBT.CX_MBT_10110SW"/>
     <cge:Meas_Ref ObjectId="92024"/>
    <cge:TPSR_Ref TObjectID="19637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92026">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.166667 -583.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19639" ObjectName="SW-CX_MBT.CX_MBT_10160SW"/>
     <cge:Meas_Ref ObjectId="92026"/>
    <cge:TPSR_Ref TObjectID="19639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92027">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.166667 -501.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19640" ObjectName="SW-CX_MBT.CX_MBT_10167SW"/>
     <cge:Meas_Ref ObjectId="92027"/>
    <cge:TPSR_Ref TObjectID="19640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92034">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.166667 -243.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19645" ObjectName="SW-CX_MBT.CX_MBT_30117SW"/>
     <cge:Meas_Ref ObjectId="92034"/>
    <cge:TPSR_Ref TObjectID="19645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92042">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -97.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19650" ObjectName="SW-CX_MBT.CX_MBT_36117SW"/>
     <cge:Meas_Ref ObjectId="92042"/>
    <cge:TPSR_Ref TObjectID="19650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92043">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 11.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19651" ObjectName="SW-CX_MBT.CX_MBT_36167SW"/>
     <cge:Meas_Ref ObjectId="92043"/>
    <cge:TPSR_Ref TObjectID="19651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92051">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 489.000000 -97.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19655" ObjectName="SW-CX_MBT.CX_MBT_36217SW"/>
     <cge:Meas_Ref ObjectId="92051"/>
    <cge:TPSR_Ref TObjectID="19655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92052">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 489.000000 11.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19656" ObjectName="SW-CX_MBT.CX_MBT_36267SW"/>
     <cge:Meas_Ref ObjectId="92052"/>
    <cge:TPSR_Ref TObjectID="19656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92097">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 672.000000 -97.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19685" ObjectName="SW-CX_MBT.CX_MBT_36317SW"/>
     <cge:Meas_Ref ObjectId="92097"/>
    <cge:TPSR_Ref TObjectID="19685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92098">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 672.000000 11.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19686" ObjectName="SW-CX_MBT.CX_MBT_36367SW"/>
     <cge:Meas_Ref ObjectId="92098"/>
    <cge:TPSR_Ref TObjectID="19686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92078">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -97.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19670" ObjectName="SW-CX_MBT.CX_MBT_36417SW"/>
     <cge:Meas_Ref ObjectId="92078"/>
    <cge:TPSR_Ref TObjectID="19670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92079">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 5.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19671" ObjectName="SW-CX_MBT.CX_MBT_36437SW"/>
     <cge:Meas_Ref ObjectId="92079"/>
    <cge:TPSR_Ref TObjectID="19671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92082">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 94.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19674" ObjectName="SW-CX_MBT.CX_MBT_36567SW"/>
     <cge:Meas_Ref ObjectId="92082"/>
    <cge:TPSR_Ref TObjectID="19674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92061">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1048.000000 11.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19661" ObjectName="SW-CX_MBT.CX_MBT_36667SW"/>
     <cge:Meas_Ref ObjectId="92061"/>
    <cge:TPSR_Ref TObjectID="19661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92060">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1048.000000 -97.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19660" ObjectName="SW-CX_MBT.CX_MBT_36617SW"/>
     <cge:Meas_Ref ObjectId="92060"/>
    <cge:TPSR_Ref TObjectID="19660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 -97.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19665" ObjectName="SW-CX_MBT.CX_MBT_36717SW"/>
     <cge:Meas_Ref ObjectId="92069"/>
    <cge:TPSR_Ref TObjectID="19665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 11.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19666" ObjectName="SW-CX_MBT.CX_MBT_36767SW"/>
     <cge:Meas_Ref ObjectId="92070"/>
    <cge:TPSR_Ref TObjectID="19666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92088">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1448.000000 -97.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19678" ObjectName="SW-CX_MBT.CX_MBT_36817SW"/>
     <cge:Meas_Ref ObjectId="92088"/>
    <cge:TPSR_Ref TObjectID="19678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92089">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1448.000000 5.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19679" ObjectName="SW-CX_MBT.CX_MBT_36837SW"/>
     <cge:Meas_Ref ObjectId="92089"/>
    <cge:TPSR_Ref TObjectID="19679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92091">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1448.000000 94.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19681" ObjectName="SW-CX_MBT.CX_MBT_36867SW"/>
     <cge:Meas_Ref ObjectId="92091"/>
    <cge:TPSR_Ref TObjectID="19681"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_MBT.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.000000 102.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43306" ObjectName="SM-CX_MBT.P1"/>
    <cge:TPSR_Ref TObjectID="43306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_MBT.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 102.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43307" ObjectName="SM-CX_MBT.P2"/>
    <cge:TPSR_Ref TObjectID="43307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_MBT.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1108.000000 102.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43308" ObjectName="SM-CX_MBT.P3"/>
    <cge:TPSR_Ref TObjectID="43308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_MBT.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 102.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43309" ObjectName="SM-CX_MBT.P4"/>
    <cge:TPSR_Ref TObjectID="43309"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BDS" endPointId="0" endStationName="CX_MBT" flowDrawDirect="1" flowShape="0" id="AC-110kV.baotian_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="732,-889 717,-816 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34026" ObjectName="AC-110kV.baotian_line"/>
    <cge:TPSR_Ref TObjectID="34026_SS-143"/></metadata>
   <polyline fill="none" opacity="0" points="732,-889 717,-816 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_30246c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 601.000000 -408.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3329980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 337.000000 -226.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331cd60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -96.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331d790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 12.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37e19d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 -96.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37135b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 12.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3364e10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 -96.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3365610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 12.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_333dfd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 698.000000 154.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331c260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 -96.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3331960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 95.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3333bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 6.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_375aa20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1025.000000 -96.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37028e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1025.000000 12.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_373dc50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 -96.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3339690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 12.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d4430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 -96.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d4c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 95.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d6ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 6.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36fbb90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 -630.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_370fdf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.166667 -242.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3710880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.166667 -722.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3711310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.166667 -655.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3711da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.166667 -582.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3712830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.166667 -500.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_36a9f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,17 367,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3366760@0" ObjectIDZND0="19649@x" ObjectIDZND1="19651@x" ObjectIDZND2="43306@x" Pin0InfoVect0LinkObjId="SW-92041_0" Pin0InfoVect1LinkObjId="SW-92043_0" Pin0InfoVect2LinkObjId="SM-CX_MBT.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3366760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="400,17 367,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32dcec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-334 713,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="19644@x" ObjectIDND1="g_36eda50@0" ObjectIDZND0="19642@1" Pin0InfoVect0LinkObjId="SW-92031_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92033_0" Pin1InfoVect1LinkObjId="g_36eda50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-334 713,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32dd120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="665,-787 713,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_373f850@0" ObjectIDZND0="19635@x" ObjectIDZND1="19638@x" ObjectIDZND2="g_36ad260@0" Pin0InfoVect0LinkObjId="SW-92022_0" Pin0InfoVect1LinkObjId="SW-92025_0" Pin0InfoVect2LinkObjId="g_36ad260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_373f850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="665,-787 713,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3363010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-405 713,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="19646@1" ObjectIDZND0="19644@1" Pin0InfoVect0LinkObjId="SW-92033_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36faa80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-405 713,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33064b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,-728 630,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3710880@0" ObjectIDZND0="19638@0" Pin0InfoVect0LinkObjId="SW-92025_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3710880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="617,-728 630,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33066a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,-661 630,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3711310@0" ObjectIDZND0="19637@0" Pin0InfoVect0LinkObjId="SW-92024_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3711310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="617,-661 630,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3306890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,-588 630,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3711da0@0" ObjectIDZND0="19639@0" Pin0InfoVect0LinkObjId="SW-92026_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3711da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="617,-588 630,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3306a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,-506 630,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3712830@0" ObjectIDZND0="19640@0" Pin0InfoVect0LinkObjId="SW-92027_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3712830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="617,-506 630,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3306cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="297,-102 307,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_331cd60@0" ObjectIDZND0="19650@0" Pin0InfoVect0LinkObjId="SW-92042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331cd60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="297,-102 307,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3306ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="297,6 307,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_331d790@0" ObjectIDZND0="19651@0" Pin0InfoVect0LinkObjId="SW-92043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331d790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="297,6 307,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36dd890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-155 367,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19648@1" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36ddac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-155 367,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ddac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-198 713,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19643@0" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92032_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-198 713,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ddcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,-248 798,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_370fdf0@0" ObjectIDZND0="19645@1" Pin0InfoVect0LinkObjId="SW-92034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_370fdf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="811,-248 798,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36ddf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="666,-661 713,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19637@1" ObjectIDZND0="19634@x" ObjectIDZND1="19635@x" Pin0InfoVect0LinkObjId="SW-92021_0" Pin0InfoVect1LinkObjId="SW-92022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="666,-661 713,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36de1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-679 713,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19635@0" ObjectIDZND0="19634@x" ObjectIDZND1="19637@x" Pin0InfoVect0LinkObjId="SW-92021_0" Pin0InfoVect1LinkObjId="SW-92024_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92022_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-679 713,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36de410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-661 713,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19635@x" ObjectIDND1="19637@x" ObjectIDZND0="19634@1" Pin0InfoVect0LinkObjId="SW-92021_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92022_0" Pin1InfoVect1LinkObjId="SW-92024_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-661 713,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36de670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="666,-588 713,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="breaker" ObjectIDND0="19639@1" ObjectIDZND0="47818@0" ObjectIDZND1="19634@x" Pin0InfoVect0LinkObjId="g_3783870_0" Pin0InfoVect1LinkObjId="SW-92021_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92026_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="666,-588 713,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3783870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-604 713,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="19634@0" ObjectIDZND0="47818@0" ObjectIDZND1="19639@x" Pin0InfoVect0LinkObjId="g_36de670_0" Pin0InfoVect1LinkObjId="SW-92026_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92021_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-604 713,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3783ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-588 713,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="19634@x" ObjectIDND1="19639@x" ObjectIDZND0="47818@0" Pin0InfoVect0LinkObjId="g_36de670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92021_0" Pin1InfoVect1LinkObjId="SW-92026_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-588 713,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3783d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="666,-506 713,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="19640@1" ObjectIDZND0="19636@x" ObjectIDZND1="19646@x" Pin0InfoVect0LinkObjId="SW-92023_0" Pin0InfoVect1LinkObjId="g_36faa80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92027_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="666,-506 713,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3783f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-102 367,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19647@x" ObjectIDND1="19650@x" ObjectIDZND0="19648@0" Pin0InfoVect0LinkObjId="SW-92040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92039_0" Pin1InfoVect1LinkObjId="SW-92042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-102 367,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37841f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,6 367,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19651@1" ObjectIDZND0="g_3366760@0" ObjectIDZND1="43306@x" ObjectIDZND2="19649@x" Pin0InfoVect0LinkObjId="g_3366760_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P1_0" Pin0InfoVect2LinkObjId="SW-92041_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="343,6 367,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36fa890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-487 713,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19646@0" ObjectIDZND0="19636@x" ObjectIDZND1="19640@x" Pin0InfoVect0LinkObjId="SW-92023_0" Pin0InfoVect1LinkObjId="SW-92027_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36faa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-487 713,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36faa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-527 713,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="19636@0" ObjectIDZND0="19646@x" ObjectIDZND1="19640@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-92027_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92023_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-527 713,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37159b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="666,-728 713,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="19638@1" ObjectIDZND0="19635@x" ObjectIDZND1="g_373f850@0" ObjectIDZND2="34026@1" Pin0InfoVect0LinkObjId="SW-92022_0" Pin0InfoVect1LinkObjId="g_373f850_0" Pin0InfoVect2LinkObjId="g_36acda0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92025_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="666,-728 713,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3715bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-728 713,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_373f850@0" ObjectIDND1="34026@1" ObjectIDND2="g_36ad260@0" ObjectIDZND0="19635@1" Pin0InfoVect0LinkObjId="SW-92022_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_373f850_0" Pin1InfoVect1LinkObjId="g_36acda0_1" Pin1InfoVect2LinkObjId="g_36ad260_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-728 713,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3337690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-248 713,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19645@0" ObjectIDZND0="19642@x" ObjectIDZND1="19643@x" Pin0InfoVect0LinkObjId="SW-92031_0" Pin0InfoVect1LinkObjId="SW-92032_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="762,-248 713,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33378f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-779 727,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="19635@x" ObjectIDND1="19638@x" ObjectIDND2="g_373f850@0" ObjectIDZND0="g_36ad260@0" Pin0InfoVect0LinkObjId="g_36ad260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-92022_0" Pin1InfoVect1LinkObjId="SW-92025_0" Pin1InfoVect2LinkObjId="g_373f850_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-779 727,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36acb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-779 713,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" EndDevType1="powerLine" ObjectIDND0="19635@x" ObjectIDND1="19638@x" ObjectIDND2="g_36ad260@0" ObjectIDZND0="g_373f850@0" ObjectIDZND1="34026@1" Pin0InfoVect0LinkObjId="g_373f850_0" Pin0InfoVect1LinkObjId="g_36acda0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-92022_0" Pin1InfoVect1LinkObjId="SW-92025_0" Pin1InfoVect2LinkObjId="g_36ad260_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-779 713,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36acda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-787 713,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_373f850@0" ObjectIDND1="19635@x" ObjectIDND2="19638@x" ObjectIDZND0="34026@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_373f850_0" Pin1InfoVect1LinkObjId="SW-92022_0" Pin1InfoVect2LinkObjId="SW-92025_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-787 713,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36ad000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-728 713,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="19635@x" ObjectIDND1="19638@x" ObjectIDZND0="g_373f850@0" ObjectIDZND1="34026@1" ObjectIDZND2="g_36ad260@0" Pin0InfoVect0LinkObjId="g_373f850_0" Pin0InfoVect1LinkObjId="g_36acda0_1" Pin0InfoVect2LinkObjId="g_36ad260_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92022_0" Pin1InfoVect1LinkObjId="SW-92025_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="713,-728 713,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3025150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-431 607,-431 607,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19641@0" ObjectIDZND0="g_30246c0@0" Pin0InfoVect0LinkObjId="g_30246c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92028_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="593,-431 607,-431 607,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30253b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="711,-467 593,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="19646@x" ObjectIDZND0="19641@1" Pin0InfoVect0LinkObjId="SW-92028_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36faa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="711,-467 593,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b8fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-234 713,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19643@1" ObjectIDZND0="19642@x" ObjectIDZND1="19645@x" Pin0InfoVect0LinkObjId="SW-92031_0" Pin0InfoVect1LinkObjId="SW-92034_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92032_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-234 713,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b9230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-277 713,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19642@0" ObjectIDZND0="19643@x" ObjectIDZND1="19645@x" Pin0InfoVect0LinkObjId="SW-92032_0" Pin0InfoVect1LinkObjId="SW-92034_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92031_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-277 713,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b9490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-348 713,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="19644@0" ObjectIDZND0="19642@x" ObjectIDZND1="g_36eda50@0" Pin0InfoVect0LinkObjId="SW-92031_0" Pin0InfoVect1LinkObjId="g_36eda50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92033_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-348 713,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b96f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-334 764,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="19642@x" ObjectIDND1="19644@x" ObjectIDZND0="g_36eda50@0" Pin0InfoVect0LinkObjId="g_36eda50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92031_0" Pin1InfoVect1LinkObjId="SW-92033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-334 764,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32da560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-186 288,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19631@0" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="288,-186 288,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32da7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-232 333,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3329980@0" ObjectIDZND0="19632@1" Pin0InfoVect0LinkObjId="SW-92003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3329980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="341,-232 333,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32daa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-241 288,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3341ee0@0" ObjectIDZND0="19631@x" ObjectIDZND1="19632@x" Pin0InfoVect0LinkObjId="SW-92002_0" Pin0InfoVect1LinkObjId="SW-92003_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3341ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="288,-241 288,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="297,-232 288,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="19632@0" ObjectIDZND0="g_3341ee0@0" ObjectIDZND1="19631@x" Pin0InfoVect0LinkObjId="g_3341ee0_0" Pin0InfoVect1LinkObjId="SW-92002_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="297,-232 288,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-232 288,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3341ee0@0" ObjectIDND1="19632@x" ObjectIDZND0="19631@1" Pin0InfoVect0LinkObjId="SW-92002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3341ee0_0" Pin1InfoVect1LinkObjId="SW-92003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="288,-232 288,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3367b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,-102 367,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19650@1" ObjectIDZND0="19647@x" ObjectIDZND1="19648@x" Pin0InfoVect0LinkObjId="SW-92039_0" Pin0InfoVect1LinkObjId="SW-92040_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="343,-102 367,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3367da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-102 367,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19648@x" ObjectIDND1="19650@x" ObjectIDZND0="19647@1" Pin0InfoVect0LinkObjId="SW-92039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92040_0" Pin1InfoVect1LinkObjId="SW-92042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-102 367,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3368000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-61 367,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19647@0" ObjectIDZND0="19649@1" Pin0InfoVect0LinkObjId="SW-92041_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92039_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-61 367,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33173a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-9 367,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19649@0" ObjectIDZND0="g_3366760@0" ObjectIDZND1="43306@x" ObjectIDZND2="19651@x" Pin0InfoVect0LinkObjId="g_3366760_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P1_0" Pin0InfoVect2LinkObjId="SW-92043_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="367,-9 367,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33186f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,6 367,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="19649@x" ObjectIDND1="19651@x" ObjectIDZND0="g_3366760@0" ObjectIDZND1="43306@x" Pin0InfoVect0LinkObjId="g_3366760_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92041_0" Pin1InfoVect1LinkObjId="SW-92043_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="367,6 367,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3318950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,17 367,81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_3366760@0" ObjectIDND1="19649@x" ObjectIDND2="19651@x" ObjectIDZND0="43306@0" Pin0InfoVect0LinkObjId="SM-CX_MBT.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3366760_0" Pin1InfoVect1LinkObjId="SW-92041_0" Pin1InfoVect2LinkObjId="SW-92043_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,17 367,81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3318bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,17 554,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3713f80@0" ObjectIDZND0="19654@x" ObjectIDZND1="19656@x" ObjectIDZND2="43307@x" Pin0InfoVect0LinkObjId="SW-92050_0" Pin0InfoVect1LinkObjId="SW-92052_0" Pin0InfoVect2LinkObjId="SM-CX_MBT.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3713f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,17 554,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37e02b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,-102 494,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_37e19d0@0" ObjectIDZND0="19655@0" Pin0InfoVect0LinkObjId="SW-92051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37e19d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="484,-102 494,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37e04a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,6 494,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_37135b0@0" ObjectIDZND0="19656@0" Pin0InfoVect0LinkObjId="SW-92052_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37135b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="484,6 494,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37e0690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-155 554,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19653@1" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92049_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-155 554,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37e0880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-102 554,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19652@x" ObjectIDND1="19655@x" ObjectIDZND0="19653@0" Pin0InfoVect0LinkObjId="SW-92049_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92048_0" Pin1InfoVect1LinkObjId="SW-92051_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-102 554,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37e0ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,6 554,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19656@1" ObjectIDZND0="g_3713f80@0" ObjectIDZND1="43307@x" ObjectIDZND2="19654@x" Pin0InfoVect0LinkObjId="g_3713f80_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P2_0" Pin0InfoVect2LinkObjId="SW-92050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92052_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="530,6 554,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375c700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-102 554,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19655@1" ObjectIDZND0="19652@x" ObjectIDZND1="19653@x" Pin0InfoVect0LinkObjId="SW-92048_0" Pin0InfoVect1LinkObjId="SW-92049_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="530,-102 554,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375c960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-102 554,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19653@x" ObjectIDND1="19655@x" ObjectIDZND0="19652@1" Pin0InfoVect0LinkObjId="SW-92048_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92049_0" Pin1InfoVect1LinkObjId="SW-92051_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-102 554,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-61 554,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19652@0" ObjectIDZND0="19654@1" Pin0InfoVect0LinkObjId="SW-92050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92048_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-61 554,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-9 554,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19654@0" ObjectIDZND0="g_3713f80@0" ObjectIDZND1="43307@x" ObjectIDZND2="19656@x" Pin0InfoVect0LinkObjId="g_3713f80_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P2_0" Pin0InfoVect2LinkObjId="SW-92052_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="554,-9 554,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,6 554,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="19654@x" ObjectIDND1="19656@x" ObjectIDZND0="g_3713f80@0" ObjectIDZND1="43307@x" Pin0InfoVect0LinkObjId="g_3713f80_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92050_0" Pin1InfoVect1LinkObjId="SW-92052_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="554,6 554,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,17 554,81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_3713f80@0" ObjectIDND1="19654@x" ObjectIDND2="19656@x" ObjectIDZND0="43307@0" Pin0InfoVect0LinkObjId="SM-CX_MBT.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3713f80_0" Pin1InfoVect1LinkObjId="SW-92050_0" Pin1InfoVect2LinkObjId="SW-92052_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,17 554,81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331e9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,17 737,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_334ae90@0" ObjectIDZND0="19684@x" ObjectIDZND1="19686@x" ObjectIDZND2="19687@x" Pin0InfoVect0LinkObjId="SW-92096_0" Pin0InfoVect1LinkObjId="SW-92098_0" Pin0InfoVect2LinkObjId="SW-92099_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_334ae90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="770,17 737,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,-102 677,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3364e10@0" ObjectIDZND0="19685@0" Pin0InfoVect0LinkObjId="SW-92097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3364e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="667,-102 677,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e12c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,6 677,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3365610@0" ObjectIDZND0="19686@0" Pin0InfoVect0LinkObjId="SW-92098_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3365610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="667,6 677,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e14b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-155 737,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19683@1" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-155 737,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3363cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-102 737,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19682@x" ObjectIDND1="19685@x" ObjectIDZND0="19683@0" Pin0InfoVect0LinkObjId="SW-92095_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92094_0" Pin1InfoVect1LinkObjId="SW-92097_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-102 737,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3363f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,6 737,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19686@1" ObjectIDZND0="g_334ae90@0" ObjectIDZND1="19687@x" ObjectIDZND2="19684@x" Pin0InfoVect0LinkObjId="g_334ae90_0" Pin0InfoVect1LinkObjId="SW-92099_0" Pin0InfoVect2LinkObjId="SW-92096_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92098_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="713,6 737,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334bc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-102 737,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19685@1" ObjectIDZND0="19682@x" ObjectIDZND1="19683@x" Pin0InfoVect0LinkObjId="SW-92094_0" Pin0InfoVect1LinkObjId="SW-92095_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92097_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="713,-102 737,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-102 737,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19683@x" ObjectIDND1="19685@x" ObjectIDZND0="19682@1" Pin0InfoVect0LinkObjId="SW-92094_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92095_0" Pin1InfoVect1LinkObjId="SW-92097_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-102 737,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334c0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-61 737,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19682@0" ObjectIDZND0="19684@1" Pin0InfoVect0LinkObjId="SW-92096_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-61 737,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-9 737,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19684@0" ObjectIDZND0="g_334ae90@0" ObjectIDZND1="19687@x" ObjectIDZND2="19686@x" Pin0InfoVect0LinkObjId="g_334ae90_0" Pin0InfoVect1LinkObjId="SW-92099_0" Pin0InfoVect2LinkObjId="SW-92098_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="737,-9 737,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,6 737,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="19684@x" ObjectIDND1="19686@x" ObjectIDZND0="g_334ae90@0" ObjectIDZND1="19687@x" Pin0InfoVect0LinkObjId="g_334ae90_0" Pin0InfoVect1LinkObjId="SW-92099_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92096_0" Pin1InfoVect1LinkObjId="SW-92098_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="737,6 737,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334c7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,17 737,84 704,84 704,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_334ae90@0" ObjectIDND1="19684@x" ObjectIDND2="19686@x" ObjectIDZND0="19687@1" Pin0InfoVect0LinkObjId="SW-92099_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_334ae90_0" Pin1InfoVect1LinkObjId="SW-92096_0" Pin1InfoVect2LinkObjId="SW-92098_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,17 737,84 704,84 704,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ae110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,136 704,126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_333dfd0@0" ObjectIDZND0="19687@0" Pin0InfoVect0LinkObjId="SW-92099_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_333dfd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="704,136 704,126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="850,-102 860,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_331c260@0" ObjectIDZND0="19670@0" Pin0InfoVect0LinkObjId="SW-92078_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331c260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="850,-102 860,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331aaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="850,89 860,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3331960@0" ObjectIDZND0="19674@0" Pin0InfoVect0LinkObjId="SW-92082_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3331960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="850,89 860,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331ad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,-155 920,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19668@1" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,-155 920,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,-102 920,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19667@x" ObjectIDND1="19670@x" ObjectIDZND0="19668@0" Pin0InfoVect0LinkObjId="SW-92076_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92075_0" Pin1InfoVect1LinkObjId="SW-92078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,-102 920,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,89 920,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19674@1" ObjectIDZND0="19672@x" ObjectIDZND1="19673@x" Pin0InfoVect0LinkObjId="SW-92080_0" Pin0InfoVect1LinkObjId="SW-92081_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92082_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,89 920,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33330c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-102 920,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19670@1" ObjectIDZND0="19667@x" ObjectIDZND1="19668@x" Pin0InfoVect0LinkObjId="SW-92075_0" Pin0InfoVect1LinkObjId="SW-92076_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-102 920,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3333320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,-102 920,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19668@x" ObjectIDND1="19670@x" ObjectIDZND0="19667@1" Pin0InfoVect0LinkObjId="SW-92075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92076_0" Pin1InfoVect1LinkObjId="SW-92078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,-102 920,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3740ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="850,0 860,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3333bf0@0" ObjectIDZND0="19671@0" Pin0InfoVect0LinkObjId="SW-92079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3333bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="850,0 860,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3741db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,0 920,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19671@1" ObjectIDZND0="g_3332350@0" ObjectIDZND1="19669@x" ObjectIDZND2="19673@x" Pin0InfoVect0LinkObjId="g_3332350_0" Pin0InfoVect1LinkObjId="SW-92077_0" Pin0InfoVect2LinkObjId="SW-92081_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="896,0 920,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3742840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,0 920,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3332350@0" ObjectIDND1="19669@x" ObjectIDND2="19671@x" ObjectIDZND0="19673@1" Pin0InfoVect0LinkObjId="SW-92081_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3332350_0" Pin1InfoVect1LinkObjId="SW-92077_0" Pin1InfoVect2LinkObjId="SW-92079_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,0 920,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36cffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,80 920,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19673@0" ObjectIDZND0="19672@x" ObjectIDZND1="19674@x" Pin0InfoVect0LinkObjId="SW-92080_0" Pin0InfoVect1LinkObjId="SW-92082_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="920,80 920,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d0190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,100 920,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19672@1" ObjectIDZND0="19673@x" ObjectIDZND1="19674@x" Pin0InfoVect0LinkObjId="SW-92081_0" Pin0InfoVect1LinkObjId="SW-92082_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="920,100 920,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d0fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="932,137 920,137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_36d0380@0" ObjectIDZND0="19672@x" ObjectIDZND1="g_36f6ea0@0" Pin0InfoVect0LinkObjId="SW-92080_0" Pin0InfoVect1LinkObjId="g_36f6ea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d0380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="932,137 920,137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d1ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,139 920,127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_36d0380@0" ObjectIDND1="g_36f6ea0@0" ObjectIDZND0="19672@0" Pin0InfoVect0LinkObjId="SW-92080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36d0380_0" Pin1InfoVect1LinkObjId="g_36f6ea0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,139 920,127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e4fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,199 920,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_36d1d20@0" ObjectIDZND0="g_36f6ea0@0" Pin0InfoVect0LinkObjId="g_36f6ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d1d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,199 920,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e51d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,148 920,139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_36f6ea0@1" ObjectIDZND0="g_36d0380@0" ObjectIDZND1="19672@x" Pin0InfoVect0LinkObjId="g_36d0380_0" Pin0InfoVect1LinkObjId="SW-92080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36f6ea0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="920,148 920,139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e6820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1146,17 1113,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_37032f0@0" ObjectIDZND0="19659@x" ObjectIDZND1="19661@x" ObjectIDZND2="43308@x" Pin0InfoVect0LinkObjId="SW-92059_0" Pin0InfoVect1LinkObjId="SW-92061_0" Pin0InfoVect2LinkObjId="SM-CX_MBT.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37032f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1146,17 1113,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37592c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-102 1053,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_375aa20@0" ObjectIDZND0="19660@0" Pin0InfoVect0LinkObjId="SW-92060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_375aa20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-102 1053,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37594b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,6 1053,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_37028e0@0" ObjectIDZND0="19661@0" Pin0InfoVect0LinkObjId="SW-92061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37028e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,6 1053,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37596a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-155 1113,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19658@1" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-155 1113,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3759890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-102 1113,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19657@x" ObjectIDND1="19660@x" ObjectIDZND0="19658@0" Pin0InfoVect0LinkObjId="SW-92058_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92057_0" Pin1InfoVect1LinkObjId="SW-92060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-102 1113,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3759ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1089,6 1113,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19661@1" ObjectIDZND0="g_37032f0@0" ObjectIDZND1="43308@x" ObjectIDZND2="19659@x" Pin0InfoVect0LinkObjId="g_37032f0_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P3_0" Pin0InfoVect2LinkObjId="SW-92059_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92061_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1089,6 1113,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3704060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1089,-102 1113,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19660@1" ObjectIDZND0="19657@x" ObjectIDZND1="19658@x" Pin0InfoVect0LinkObjId="SW-92057_0" Pin0InfoVect1LinkObjId="SW-92058_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1089,-102 1113,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37042c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-102 1113,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19658@x" ObjectIDND1="19660@x" ObjectIDZND0="19657@1" Pin0InfoVect0LinkObjId="SW-92057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92058_0" Pin1InfoVect1LinkObjId="SW-92060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-102 1113,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3704520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-61 1113,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19657@0" ObjectIDZND0="19659@1" Pin0InfoVect0LinkObjId="SW-92059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-61 1113,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3704780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-9 1113,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19659@0" ObjectIDZND0="g_37032f0@0" ObjectIDZND1="43308@x" ObjectIDZND2="19661@x" Pin0InfoVect0LinkObjId="g_37032f0_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P3_0" Pin0InfoVect2LinkObjId="SW-92061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-9 1113,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37049e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,6 1113,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="19659@x" ObjectIDND1="19661@x" ObjectIDZND0="g_37032f0@0" ObjectIDZND1="43308@x" Pin0InfoVect0LinkObjId="g_37032f0_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92059_0" Pin1InfoVect1LinkObjId="SW-92061_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1113,6 1113,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3704c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,17 1113,81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="generator" ObjectIDND0="19659@x" ObjectIDND1="19661@x" ObjectIDND2="g_37032f0@0" ObjectIDZND0="43308@0" Pin0InfoVect0LinkObjId="SM-CX_MBT.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-92059_0" Pin1InfoVect1LinkObjId="SW-92061_0" Pin1InfoVect2LinkObjId="g_37032f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1113,17 1113,81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f82310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1350,17 1317,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_333a090@0" ObjectIDZND0="19664@x" ObjectIDZND1="19666@x" ObjectIDZND2="43309@x" Pin0InfoVect0LinkObjId="SW-92068_0" Pin0InfoVect1LinkObjId="SW-92070_0" Pin0InfoVect2LinkObjId="SM-CX_MBT.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_333a090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1350,17 1317,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373c530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-102 1257,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_373dc50@0" ObjectIDZND0="19665@0" Pin0InfoVect0LinkObjId="SW-92069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_373dc50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-102 1257,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,6 1257,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3339690@0" ObjectIDZND0="19666@0" Pin0InfoVect0LinkObjId="SW-92070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3339690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,6 1257,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373c910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-155 1317,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19663@1" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92067_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-155 1317,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-102 1317,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19662@x" ObjectIDND1="19665@x" ObjectIDZND0="19663@0" Pin0InfoVect0LinkObjId="SW-92067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92066_0" Pin1InfoVect1LinkObjId="SW-92069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-102 1317,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1293,6 1317,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19666@1" ObjectIDZND0="g_333a090@0" ObjectIDZND1="43309@x" ObjectIDZND2="19664@x" Pin0InfoVect0LinkObjId="g_333a090_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P4_0" Pin0InfoVect2LinkObjId="SW-92068_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1293,6 1317,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1293,-102 1317,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19665@1" ObjectIDZND0="19662@x" ObjectIDZND1="19663@x" Pin0InfoVect0LinkObjId="SW-92066_0" Pin0InfoVect1LinkObjId="SW-92067_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1293,-102 1317,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-102 1317,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19663@x" ObjectIDND1="19665@x" ObjectIDZND0="19662@1" Pin0InfoVect0LinkObjId="SW-92066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92067_0" Pin1InfoVect1LinkObjId="SW-92069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-102 1317,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-61 1317,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19662@0" ObjectIDZND0="19664@1" Pin0InfoVect0LinkObjId="SW-92068_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-61 1317,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333b520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-9 1317,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19664@0" ObjectIDZND0="g_333a090@0" ObjectIDZND1="43309@x" ObjectIDZND2="19666@x" Pin0InfoVect0LinkObjId="g_333a090_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P4_0" Pin0InfoVect2LinkObjId="SW-92070_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92068_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-9 1317,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333b780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,6 1317,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="19664@x" ObjectIDND1="19666@x" ObjectIDZND0="g_333a090@0" ObjectIDZND1="43309@x" Pin0InfoVect0LinkObjId="g_333a090_0" Pin0InfoVect1LinkObjId="SM-CX_MBT.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92068_0" Pin1InfoVect1LinkObjId="SW-92070_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1317,6 1317,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333b9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,17 1317,81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_333a090@0" ObjectIDND1="19664@x" ObjectIDND2="19666@x" ObjectIDZND0="43309@0" Pin0InfoVect0LinkObjId="SM-CX_MBT.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_333a090_0" Pin1InfoVect1LinkObjId="SW-92068_0" Pin1InfoVect2LinkObjId="SW-92070_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,17 1317,81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33511b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,-9 920,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3332350@0" ObjectIDZND0="19673@x" ObjectIDZND1="19671@x" ObjectIDZND2="19669@x" Pin0InfoVect0LinkObjId="SW-92081_0" Pin0InfoVect1LinkObjId="SW-92079_0" Pin0InfoVect2LinkObjId="SW-92077_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3332350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="978,-9 920,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33513a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,-9 920,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3332350@0" ObjectIDND1="19669@x" ObjectIDZND0="19673@x" ObjectIDZND1="19671@x" Pin0InfoVect0LinkObjId="SW-92081_0" Pin0InfoVect1LinkObjId="SW-92079_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3332350_0" Pin1InfoVect1LinkObjId="SW-92077_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="920,-9 920,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3351590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,-9 920,-16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3332350@0" ObjectIDND1="19673@x" ObjectIDND2="19671@x" ObjectIDZND0="19669@0" Pin0InfoVect0LinkObjId="SW-92077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3332350_0" Pin1InfoVect1LinkObjId="SW-92081_0" Pin1InfoVect2LinkObjId="SW-92079_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,-9 920,-16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33517a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,-52 920,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19669@1" ObjectIDZND0="19667@0" Pin0InfoVect0LinkObjId="SW-92075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="920,-52 920,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3708b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,-102 1453,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36d4430@0" ObjectIDZND0="19678@0" Pin0InfoVect0LinkObjId="SW-92088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d4430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1443,-102 1453,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d2cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,89 1453,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36d4c10@0" ObjectIDZND0="19681@0" Pin0InfoVect0LinkObjId="SW-92091_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d4c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1443,89 1453,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d2f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,-155 1513,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19676@1" ObjectIDZND0="19630@0" Pin0InfoVect0LinkObjId="g_36dd890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-155 1513,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d3180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,-102 1513,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19675@x" ObjectIDND1="19678@x" ObjectIDZND0="19676@0" Pin0InfoVect0LinkObjId="SW-92086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92085_0" Pin1InfoVect1LinkObjId="SW-92088_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-102 1513,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d33e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,89 1513,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19681@1" ObjectIDZND0="g_2f85e50@0" ObjectIDZND1="g_36f79e0@0" ObjectIDZND2="19680@x" Pin0InfoVect0LinkObjId="g_2f85e50_0" Pin0InfoVect1LinkObjId="g_36f79e0_0" Pin0InfoVect2LinkObjId="SW-92090_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92091_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1489,89 1513,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d62d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,-102 1513,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19678@1" ObjectIDZND0="19675@x" ObjectIDZND1="19676@x" Pin0InfoVect0LinkObjId="SW-92085_0" Pin0InfoVect1LinkObjId="SW-92086_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1489,-102 1513,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d6530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,-102 1513,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19676@x" ObjectIDND1="19678@x" ObjectIDZND0="19675@1" Pin0InfoVect0LinkObjId="SW-92085_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92086_0" Pin1InfoVect1LinkObjId="SW-92088_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-102 1513,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d6790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,0 1453,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36d6ee0@0" ObjectIDZND0="19679@0" Pin0InfoVect0LinkObjId="SW-92089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d6ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1443,0 1453,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f85560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,0 1513,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19679@1" ObjectIDZND0="g_36d55e0@0" ObjectIDZND1="19677@x" ObjectIDZND2="19680@x" Pin0InfoVect0LinkObjId="g_36d55e0_0" Pin0InfoVect1LinkObjId="SW-92087_0" Pin0InfoVect2LinkObjId="SW-92090_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1489,0 1513,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f85790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,0 1513,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_36d55e0@0" ObjectIDND1="19677@x" ObjectIDND2="19679@x" ObjectIDZND0="19680@1" Pin0InfoVect0LinkObjId="SW-92090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36d55e0_0" Pin1InfoVect1LinkObjId="SW-92087_0" Pin1InfoVect2LinkObjId="SW-92089_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,0 1513,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f85c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,80 1513,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19680@0" ObjectIDZND0="g_2f85e50@0" ObjectIDZND1="g_36f79e0@0" ObjectIDZND2="19681@x" Pin0InfoVect0LinkObjId="g_2f85e50_0" Pin0InfoVect1LinkObjId="g_36f79e0_0" Pin0InfoVect2LinkObjId="SW-92091_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1513,80 1513,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f86bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1525,137 1513,137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f85e50@0" ObjectIDZND0="g_36f79e0@0" ObjectIDZND1="19680@x" ObjectIDZND2="19681@x" Pin0InfoVect0LinkObjId="g_36f79e0_0" Pin0InfoVect1LinkObjId="SW-92090_0" Pin0InfoVect2LinkObjId="SW-92091_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f85e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1525,137 1513,137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f86e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,199 1513,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_36f5e50@0" ObjectIDZND0="g_36f79e0@0" Pin0InfoVect0LinkObjId="g_36f79e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36f5e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,199 1513,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f87080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,148 1513,139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_36f79e0@1" ObjectIDZND0="g_2f85e50@0" ObjectIDZND1="19680@x" ObjectIDZND2="19681@x" Pin0InfoVect0LinkObjId="g_2f85e50_0" Pin0InfoVect1LinkObjId="SW-92090_0" Pin0InfoVect2LinkObjId="SW-92091_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36f79e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1513,148 1513,139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f2a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1571,-9 1513,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_36d55e0@0" ObjectIDZND0="19680@x" ObjectIDZND1="19679@x" ObjectIDZND2="19677@x" Pin0InfoVect0LinkObjId="SW-92090_0" Pin0InfoVect1LinkObjId="SW-92089_0" Pin0InfoVect2LinkObjId="SW-92087_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d55e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1571,-9 1513,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f2c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,-9 1513,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36d55e0@0" ObjectIDND1="19677@x" ObjectIDZND0="19680@x" ObjectIDZND1="19679@x" Pin0InfoVect0LinkObjId="SW-92090_0" Pin0InfoVect1LinkObjId="SW-92089_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36d55e0_0" Pin1InfoVect1LinkObjId="SW-92087_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-9 1513,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f2df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,-9 1513,-16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_36d55e0@0" ObjectIDND1="19680@x" ObjectIDND2="19679@x" ObjectIDZND0="19677@0" Pin0InfoVect0LinkObjId="SW-92087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36d55e0_0" Pin1InfoVect1LinkObjId="SW-92090_0" Pin1InfoVect2LinkObjId="SW-92089_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-9 1513,-16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f2fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,-52 1513,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19677@1" ObjectIDZND0="19675@0" Pin0InfoVect0LinkObjId="SW-92085_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-52 1513,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f5bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1513,89 1513,139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="19680@x" ObjectIDND1="19681@x" ObjectIDZND0="g_2f85e50@0" ObjectIDZND1="g_36f79e0@0" Pin0InfoVect0LinkObjId="g_2f85e50_0" Pin0InfoVect1LinkObjId="g_36f79e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-92090_0" Pin1InfoVect1LinkObjId="SW-92091_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1513,89 1513,139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_374fba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-714 1385,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-714 1385,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_374fe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-714 1512,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-714 1512,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36fc5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1342,-648 1342,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36fbb90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36fbb90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1342,-648 1342,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36fc840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1342,-694 1342,-703 1385,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1342,-694 1342,-703 1385,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36ff850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-492 1385,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-492 1385,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36ffab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-561 1385,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-561 1385,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36c97b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-492 1512,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-492 1512,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36c9a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-561 1512,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-561 1512,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37750b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-563 713,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19636@1" ObjectIDZND0="47818@0" Pin0InfoVect0LinkObjId="g_36de670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-92023_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-563 713,-579 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.166667 -638.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19634"/>
     <cge:Term_Ref ObjectID="27386"/>
    <cge:TPSR_Ref TObjectID="19634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.166667 -638.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19634"/>
     <cge:Term_Ref ObjectID="27386"/>
    <cge:TPSR_Ref TObjectID="19634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92386" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.166667 -638.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19634"/>
     <cge:Term_Ref ObjectID="27386"/>
    <cge:TPSR_Ref TObjectID="19634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-91371" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 865.166667 -325.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="91371" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19642"/>
     <cge:Term_Ref ObjectID="27402"/>
    <cge:TPSR_Ref TObjectID="19642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-91372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 865.166667 -325.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="91372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19642"/>
     <cge:Term_Ref ObjectID="27402"/>
    <cge:TPSR_Ref TObjectID="19642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-91370" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 865.166667 -325.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="91370" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19642"/>
     <cge:Term_Ref ObjectID="27402"/>
    <cge:TPSR_Ref TObjectID="19642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 188.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19647"/>
     <cge:Term_Ref ObjectID="27410"/>
    <cge:TPSR_Ref TObjectID="19647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 188.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19647"/>
     <cge:Term_Ref ObjectID="27410"/>
    <cge:TPSR_Ref TObjectID="19647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 188.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19647"/>
     <cge:Term_Ref ObjectID="27410"/>
    <cge:TPSR_Ref TObjectID="19647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.000000 178.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19652"/>
     <cge:Term_Ref ObjectID="27420"/>
    <cge:TPSR_Ref TObjectID="19652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.000000 178.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19652"/>
     <cge:Term_Ref ObjectID="27420"/>
    <cge:TPSR_Ref TObjectID="19652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.000000 178.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19652"/>
     <cge:Term_Ref ObjectID="27420"/>
    <cge:TPSR_Ref TObjectID="19652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 193.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19682"/>
     <cge:Term_Ref ObjectID="27480"/>
    <cge:TPSR_Ref TObjectID="19682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 193.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19682"/>
     <cge:Term_Ref ObjectID="27480"/>
    <cge:TPSR_Ref TObjectID="19682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 193.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19682"/>
     <cge:Term_Ref ObjectID="27480"/>
    <cge:TPSR_Ref TObjectID="19682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 177.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19667"/>
     <cge:Term_Ref ObjectID="27450"/>
    <cge:TPSR_Ref TObjectID="19667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 177.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19667"/>
     <cge:Term_Ref ObjectID="27450"/>
    <cge:TPSR_Ref TObjectID="19667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 177.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19667"/>
     <cge:Term_Ref ObjectID="27450"/>
    <cge:TPSR_Ref TObjectID="19667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19657"/>
     <cge:Term_Ref ObjectID="27430"/>
    <cge:TPSR_Ref TObjectID="19657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19657"/>
     <cge:Term_Ref ObjectID="27430"/>
    <cge:TPSR_Ref TObjectID="19657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19657"/>
     <cge:Term_Ref ObjectID="27430"/>
    <cge:TPSR_Ref TObjectID="19657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 177.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19662"/>
     <cge:Term_Ref ObjectID="27440"/>
    <cge:TPSR_Ref TObjectID="19662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 177.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19662"/>
     <cge:Term_Ref ObjectID="27440"/>
    <cge:TPSR_Ref TObjectID="19662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 177.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19662"/>
     <cge:Term_Ref ObjectID="27440"/>
    <cge:TPSR_Ref TObjectID="19662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-92414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19675"/>
     <cge:Term_Ref ObjectID="27466"/>
    <cge:TPSR_Ref TObjectID="19675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-92415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19675"/>
     <cge:Term_Ref ObjectID="27466"/>
    <cge:TPSR_Ref TObjectID="19675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-92413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19675"/>
     <cge:Term_Ref ObjectID="27466"/>
    <cge:TPSR_Ref TObjectID="19675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-92373" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -244.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92373" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19630"/>
     <cge:Term_Ref ObjectID="27381"/>
    <cge:TPSR_Ref TObjectID="19630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-92374" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -244.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19630"/>
     <cge:Term_Ref ObjectID="27381"/>
    <cge:TPSR_Ref TObjectID="19630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-92375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -244.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19630"/>
     <cge:Term_Ref ObjectID="27381"/>
    <cge:TPSR_Ref TObjectID="19630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-92376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -244.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19630"/>
     <cge:Term_Ref ObjectID="27381"/>
    <cge:TPSR_Ref TObjectID="19630"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-116,-818 -119,-821 -119,-767 -116,-770 -116,-818" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-116,-818 -119,-821 30,-821 27,-818 -116,-818" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-116,-770 -119,-767 30,-767 27,-770 -116,-770" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="27,-818 30,-821 30,-767 27,-770 27,-818" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-116" y="-818"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-116" y="-818"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
   <g href="AVC茅稗田.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-116" y="-818"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="737" y1="84" y2="84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="741" x2="739" y1="89" y2="91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="737" y1="78" y2="78"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="763" x2="738" y1="113" y2="138"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.25" x1="761" x2="764" y1="143" y2="143"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="759" x2="767" y1="140" y2="140"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="769" x2="757" y1="138" y2="138"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="738" x2="738" y1="123" y2="165"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="741" y1="110" y2="114"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="732" y1="109" y2="114"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="737" y1="108" y2="103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="741" y1="85" y2="89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="732" y1="84" y2="89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="737" x2="737" y1="83" y2="78"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.713213" x1="619" x2="619" y1="-432" y2="-448"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.458987" x1="619" x2="619" y1="-467" y2="-451"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="649" x2="649" y1="-467" y2="-443"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.25" x1="650" x2="647" y1="-423" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.39375" x1="652" x2="645" y1="-425" y2="-425"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.305149" x1="649" x2="649" y1="-429" y2="-439"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.611465" x1="643" x2="654" y1="-429" y2="-429"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.658501" x1="607" x2="619" y1="-431" y2="-431"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1410" x2="1385" y1="-667" y2="-641"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="1412" x2="1408" y1="-636" y2="-636"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="1414" x2="1406" y1="-639" y2="-639"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="1404" x2="1416" y1="-642" y2="-642"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-92031">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.166667 -268.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19642" ObjectName="SW-CX_MBT.CX_MBT_301BK"/>
     <cge:Meas_Ref ObjectId="92031"/>
    <cge:TPSR_Ref TObjectID="19642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92039">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.000000 -52.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19647" ObjectName="SW-CX_MBT.CX_MBT_361BK"/>
     <cge:Meas_Ref ObjectId="92039"/>
    <cge:TPSR_Ref TObjectID="19647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92021">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.166667 -596.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19634" ObjectName="SW-CX_MBT.CX_MBT_101BK"/>
     <cge:Meas_Ref ObjectId="92021"/>
    <cge:TPSR_Ref TObjectID="19634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92048">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 545.000000 -52.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19652" ObjectName="SW-CX_MBT.CX_MBT_362BK"/>
     <cge:Meas_Ref ObjectId="92048"/>
    <cge:TPSR_Ref TObjectID="19652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -52.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19682" ObjectName="SW-CX_MBT.CX_MBT_363BK"/>
     <cge:Meas_Ref ObjectId="92094"/>
    <cge:TPSR_Ref TObjectID="19682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 -52.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19667" ObjectName="SW-CX_MBT.CX_MBT_364BK"/>
     <cge:Meas_Ref ObjectId="92075"/>
    <cge:TPSR_Ref TObjectID="19667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92080">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 135.357708)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19672" ObjectName="SW-CX_MBT.CX_MBT_365BK"/>
     <cge:Meas_Ref ObjectId="92080"/>
    <cge:TPSR_Ref TObjectID="19672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.000000 -52.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19657" ObjectName="SW-CX_MBT.CX_MBT_366BK"/>
     <cge:Meas_Ref ObjectId="92057"/>
    <cge:TPSR_Ref TObjectID="19657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -52.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19662" ObjectName="SW-CX_MBT.CX_MBT_367BK"/>
     <cge:Meas_Ref ObjectId="92066"/>
    <cge:TPSR_Ref TObjectID="19662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-92085">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1504.000000 -52.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19675" ObjectName="SW-CX_MBT.CX_MBT_368BK"/>
     <cge:Meas_Ref ObjectId="92085"/>
    <cge:TPSR_Ref TObjectID="19675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1376.000000 -526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_MBT.CX_MBT_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,-172 1692,-172 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19630" ObjectName="BS-CX_MBT.CX_MBT_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="19630"/></metadata>
   <polyline fill="none" opacity="0" points="187,-172 1692,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-492 1618,-492 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1299,-492 1618,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_MBT.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-579 727,-579 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47818" ObjectName="BS-CX_MBT.XM"/>
    <cge:TPSR_Ref TObjectID="47818"/></metadata>
   <polyline fill="none" opacity="0" points="692,-579 727,-579 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_36ad260">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.166667 -772.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36eda50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.166667 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3366760">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 393.000000 75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3713f80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 580.000000 75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334ae90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3332350">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.000000 49.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d0380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 928.000000 145.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d1d20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 229.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37032f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_333a090">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1343.000000 75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d55e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1564.000000 49.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f85e50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1521.000000 145.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f5e50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 229.000000)" xlink:href="#lightningRod:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f6ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 195.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f79e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1500.000000 195.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1385" cy="-492" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1512" cy="-492" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="367" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="288" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="554" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="737" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="920" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="1113" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="1317" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="1513" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19630" cx="713" cy="-172" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47818" cx="713" cy="-579" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47818" cx="713" cy="-579" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33253a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 232.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36add70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 319.000000 128.000000) translate(0,15)">茅稗田Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3363270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -489.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3363270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -489.000000) translate(0,33)">SZ11-90000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3363270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -489.000000) translate(0,51)">115±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3363270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -489.000000) translate(0,69)">90000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3363270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -489.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3363270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -489.000000) translate(0,105)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3846dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -892.000000) translate(0,15)">保</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3846dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -892.000000) translate(0,33)">田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3846dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -892.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3784450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.500000 -754.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b38f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.166667 -698.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b3b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.500000 -686.000000) translate(0,12)">10110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b3d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -625.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b3fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.166667 -552.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b41f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 -532.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b4430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.166667 -372.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b4670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 724.166667 -298.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36aaea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -272.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ab0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.166667 -221.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ab320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 -144.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ab560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 305.000000 -126.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ab7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 -82.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ab9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 -37.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36abc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 305.000000 -18.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36abe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -399.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3374ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -192.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3374ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 -614.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3319db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -455.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f8ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36f94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_36f98a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">茅稗田升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37dfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 136.000000) translate(0,15)">茅稗田Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e0ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 561.000000 -144.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e10d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -126.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e1310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.000000 -82.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e1550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 561.000000 -37.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e1790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -19.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36e0bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 169.000000) translate(0,15)">35kV1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3364150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 744.000000 -144.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3364510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -126.000000) translate(0,12)">36317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3364750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -82.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3364990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 744.000000 -37.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3364bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -18.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ae370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 105.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331b470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -144.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331b960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 858.000000 -126.000000) translate(0,12)">36417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331bba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -82.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331bde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 52.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 858.000000 64.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3741250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 858.000000 -25.000000) translate(0,12)">36437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 106.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36e53c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.500000 252.000000) translate(0,15)">(-20~20MVar)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36e6360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 320.000000 147.000000) translate(0,15)">(1~8、10、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36e6360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 320.000000 147.000000) translate(0,33)">12~15号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36e6600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 155.000000) translate(0,15)">(9、11、16~27号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37588b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1062.000000 131.000000) translate(0,15)">凉风坳Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3759cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1120.000000 -144.000000) translate(0,12)">3661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375a120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1051.000000 -126.000000) translate(0,12)">36617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375a360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1122.000000 -82.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1120.000000 -37.000000) translate(0,12)">3666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375a7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1051.000000 -19.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3705030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1062.500000 150.000000) translate(0,15)">(1~12号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373c040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 134.000000) translate(0,15)">凉风坳Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373cf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -144.000000) translate(0,12)">3671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373d350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -126.000000) translate(0,12)">36717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -82.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -37.000000) translate(0,12)">3676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373da10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -19.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333bdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1259.000000 157.000000) translate(0,15)">(13~25号风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3350540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -44.000000) translate(0,12)">3643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33519d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1426.500000 232.000000) translate(0,15)">2号静止无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d3640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.000000 -144.000000) translate(0,12)">3681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d3b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -126.000000) translate(0,12)">36817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d3d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -82.000000) translate(0,12)">368</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d3fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.000000 52.000000) translate(0,12)">3686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d41f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 64.000000) translate(0,12)">36867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d69f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -25.000000) translate(0,12)">36837</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f872e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.500000 252.000000) translate(0,15)">(16.74MVar)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f2520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.000000 -44.000000) translate(0,12)">3683</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_36f65b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 212.000000) translate(0,12)">FC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36fcaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1301.000000 -680.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36fff10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -729.000000) translate(0,15)">1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 -709.000000) translate(0,15)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 -729.000000) translate(0,15)">备用所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -803.000000) translate(0,15)">1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.500000 -821.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1467.000000 -802.000000) translate(0,15)">麻街分支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3701880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1467.000000 -820.000000) translate(0,15)">10kV北赵线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36c9c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1302.000000 -488.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_386b3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 293.000000 -209.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_386b8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -256.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3871e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -213.000000 6.000000) translate(0,16)">6384199</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3873300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -213.000000 31.000000) translate(0,16)">6384366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3873990" transform="matrix(1.000000 0.000000 -0.000000 1.000000 -298.000000 -620.000000) translate(0,15)">风场实时功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3874330" transform="matrix(1.000000 0.000000 -0.000000 1.000000 -321.000000 -579.000000) translate(0,15)">110kV保田线AB线电压Uab（AVC）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_3875090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -329.000000 -519.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_3875090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -329.000000 -519.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_3875090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -329.000000 -519.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_3875090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -329.000000 -519.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_3875090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -329.000000 -519.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_37ca830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -90.000000 -804.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="736" cy="108" fill="none" fillStyle="0" r="15" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="736" cy="86" fill="none" fillStyle="0" r="15" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_373f850">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.166667 -780.000000)" xlink:href="#voltageTransformer:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3341ee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 247.000000 -236.000000)" xlink:href="#voltageTransformer:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-92390" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -544.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92390" ObjectName="CX_MBT:CX_MBT_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-92391" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -526.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92391" ObjectName="CX_MBT:CX_MBT_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-92392" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -510.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92392" ObjectName="CX_MBT:CX_MBT_1T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116490" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -210.000000 -746.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116490" ObjectName="CX_MBT:CX_MBT_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116491" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -213.000000 -703.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116491" ObjectName="CX_MBT:CX_MBT_ZJ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-251300" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -159.461538 -620.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="251300" ObjectName="CX_MBT:CX_MBT_GG_P1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93019" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 4.538462 -579.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93019" ObjectName="CX_MBT:CX_MBT_BTX_Uab"/>
    </metadata>
   </g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="738,151 732,138 745,138 738,151 738,150 738,151 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="364,34 370,34 367,42 364,34 364,34 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="551,34 557,34 554,42 551,34 551,34 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="734,34 740,34 737,42 734,34 734,34 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="917,36 923,36 920,28 917,36 917,36 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="917,10 923,10 920,18 917,10 917,10 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1110,34 1116,34 1113,42 1110,34 1110,34 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1314,34 1320,34 1317,42 1314,34 1314,34 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1510,36 1516,36 1513,28 1510,36 1510,36 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1510,10 1516,10 1513,18 1510,10 1510,10 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-739 1379,-752 1392,-752 1385,-739 1385,-740 1385,-739 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-739 1506,-752 1519,-752 1512,-739 1512,-740 1512,-739 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-623 1379,-636 1392,-636 1385,-623 1385,-624 1385,-623 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-623 1506,-636 1519,-636 1512,-623 1512,-624 1512,-623 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-600 1379,-587 1392,-587 1385,-600 1385,-599 1385,-600 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1512,-600 1506,-587 1519,-587 1512,-600 1512,-599 1512,-600 " stroke="rgb(60,120,255)"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-88803" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -30.000000 -880.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19177" ObjectName="DYN-CX_MBT"/>
     <cge:Meas_Ref ObjectId="88803"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_MBT.CX_MBT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="27496"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.969697 -0.000000 0.000000 -0.948723 676.166667 -400.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.969697 -0.000000 0.000000 -0.948723 676.166667 -400.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19646" ObjectName="TF-CX_MBT.CX_MBT_1T"/>
    <cge:TPSR_Ref TObjectID="19646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -615.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -615.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.000000 -658.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.000000 -658.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_MBT"/>
</svg>