<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-39" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1199 2131 1203">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="5" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="26" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="lightningRod:shape84">
    <polyline points="36,57 55,57 55,48 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.48213" x1="55" x2="55" y1="48" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.48212" x1="51" x2="59" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.48212" x1="49" x2="61" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.64705" x1="53" x2="57" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.07498" x1="55" x2="55" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.48215" x1="55" x2="53" y1="28" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.48215" x1="54" x2="57" y1="28" y2="33"/>
    <rect height="21" stroke-width="1.64706" width="12" x="49" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.441313" x1="36" x2="36" y1="63" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="26" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="26" x2="46" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.556491" x1="12" x2="12" y1="19" y2="12"/>
    <polyline arcFlag="1" points="12,19 11,19 11,19 10,19 9,19 9,20 8,20 7,21 7,21 7,22 6,23 6,23 6,24 6,25 6,26 6,26 7,27 7,28 7,28 8,29 9,29 9,30 10,30 11,30 11,30 12,30 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="12,30 11,30 11,30 10,30 9,30 9,31 8,31 7,32 7,32 7,33 6,34 6,34 6,35 6,36 6,37 6,37 7,38 7,39 7,39 8,40 9,40 9,41 10,41 11,41 11,41 12,41 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="12,41 11,41 11,41 10,41 9,41 9,42 8,42 7,43 7,43 7,44 6,45 6,45 6,46 6,47 6,48 6,48 7,49 7,50 7,50 8,51 9,51 9,52 10,52 11,52 11,52 12,52 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.397493" x1="12" x2="12" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="12" x2="36" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.601018" x1="8" x2="62" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="12" x2="36" y1="12" y2="12"/>
    <rect height="19" stroke-width="0.312745" width="11" x="30" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="8" x2="8" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="50" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="61" x2="61" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="36" x2="36" y1="15" y2="2"/>
   </symbol>
   <symbol id="lightningRod:shape61">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="6" x2="6" y1="65" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="6" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="6" x2="8" y1="34" y2="36"/>
    <rect height="19" stroke-width="0.75" width="8" x="2" y="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="20" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.606897" x1="6" x2="29" y1="65" y2="65"/>
    <rect height="27" stroke-width="0.416667" width="14" x="22" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="75" y2="22"/>
    <circle cx="36" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="29" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="29" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="36" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape33">
    <ellipse cx="7" cy="23" fillStyle="0" rx="6.5" ry="6" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="8" x2="8" y1="29" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="28" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="30" x2="29" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="29" y1="34" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="8" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="29" x2="29" y1="16" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="34" x2="24" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="31" x2="27" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="30" x2="28" y1="2" y2="2"/>
    <rect height="14" stroke-width="0.571429" width="6" x="26" y="16"/>
    <circle cx="14" cy="17" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <circle cx="7" cy="13" fillStyle="0" r="6.5" stroke-width="0.45993"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline points="64,100 64,93 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d79770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d7a180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d7ab00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d7b7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d7c9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d7d5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d7e1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d7ebd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d7f440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d7fe20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d80910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1d80f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d82900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d83510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d83ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d847d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d85fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d86cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1589b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d882c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d89470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d89df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8a8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14c1da0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8d2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d8dc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d9d6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8f030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d90070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d8c9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1213" width="2141" x="3111" y="-1204"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b33960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 726.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b34c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 741.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b35880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 726.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b35b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 741.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b426c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.000000 1064.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b43c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3921.000000 1079.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b44560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 1048.000000) translate(0,12)">Ia(A)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b44d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3937.000000 1019.000000) translate(0,12)">Cos：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b45940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 1033.000000) translate(0,12)">Ic(A)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 0.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b46c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 922.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b46f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 937.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b47140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 907.000000) translate(0,12)">Ia(A)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b47380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 818.000000) translate(0,12)">Cos：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b475c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 892.000000) translate(0,12)">Ub(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b47e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 877.000000) translate(0,12)">Ua(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b48050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 863.000000) translate(0,12)">Uc(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b48290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 848.000000) translate(0,12)">Uab(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b484d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 833.000000) translate(0,12)">F(Hz)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 0.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b49240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 922.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b49510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4533.000000 937.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b49750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4533.000000 907.000000) translate(0,12)">Ia(A)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b49990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 818.000000) translate(0,12)">Cos：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b49bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 892.000000) translate(0,12)">Ub(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b49e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 877.000000) translate(0,12)">Ua(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4a050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 863.000000) translate(0,12)">Uc(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4a290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 848.000000) translate(0,12)">Uab(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4a4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 833.000000) translate(0,12)">F(Hz)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 -11.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4a800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 559.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4aad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 574.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4ad10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 544.000000) translate(0,12)">Ia(A)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4af50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 455.000000) translate(0,12)">Cos：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 529.000000) translate(0,12)">Ub(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4b3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 514.000000) translate(0,12)">Ua(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4b610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 500.000000) translate(0,12)">Uc(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4b850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 485.000000) translate(0,12)">Uab(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4ba90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.000000 470.000000) translate(0,12)">F(Hz)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 -0.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4bdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 570.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4c090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 585.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4c2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 555.000000) translate(0,12)">Ia(A)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4c510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 466.000000) translate(0,12)">Cos：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4c750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 540.000000) translate(0,12)">Ub(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4c990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 525.000000) translate(0,12)">Ua(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4cbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 511.000000) translate(0,12)">Uc(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4ce10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4521.000000 496.000000) translate(0,12)">Uab(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4d050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 481.000000) translate(0,12)">F(Hz)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 146.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4d380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 585.500000) translate(0,12)">Uab(kV)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.000000 146.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4d710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 585.500000) translate(0,12)">Uab(kV)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4daa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 306.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4dd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 321.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4df40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 290.000000) translate(0,12)">Ia(A)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4e270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 335.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4e4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3614.000000 319.000000) translate(0,12)">Ia(A)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4e800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 316.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4ea60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 331.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4eca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 300.000000) translate(0,12)">Ia(A)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4efd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 356.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4f230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 371.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4f470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 340.000000) translate(0,12)">Ia(A)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4f7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 286.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4fa00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 301.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4fc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 270.000000) translate(0,12)">Ia(A)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b4ff70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 324.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b501d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 339.000000) translate(0,12)">P(MW)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b50410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 308.000000) translate(0,12)">Ia(A)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b50740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.000000 339.000000) translate(0,12)">Q(MVar)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b509a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 323.000000) translate(0,12)">Ia(A)：</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-596"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1076"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1196"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47425">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 -357.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8593" ObjectName="SW-CX_XL.CX_XL_032XC"/>
     <cge:Meas_Ref ObjectId="47425"/>
    <cge:TPSR_Ref TObjectID="8593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47425">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 -277.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8594" ObjectName="SW-CX_XL.CX_XL_032XC1"/>
     <cge:Meas_Ref ObjectId="47425"/>
    <cge:TPSR_Ref TObjectID="8594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 -374.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8590" ObjectName="SW-CX_XL.CX_XL_012XC"/>
     <cge:Meas_Ref ObjectId="37984"/>
    <cge:TPSR_Ref TObjectID="8590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 -289.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8591" ObjectName="SW-CX_XL.CX_XL_012XC1"/>
     <cge:Meas_Ref ObjectId="37984"/>
    <cge:TPSR_Ref TObjectID="8591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38055">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -352.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8597" ObjectName="SW-CX_XL.CX_XL_034XC"/>
     <cge:Meas_Ref ObjectId="38055"/>
    <cge:TPSR_Ref TObjectID="8597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38055">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -273.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8598" ObjectName="SW-CX_XL.CX_XL_034XC1"/>
     <cge:Meas_Ref ObjectId="38055"/>
    <cge:TPSR_Ref TObjectID="8598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4217.000000 -224.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6088" ObjectName="SW-CX_XL.CX_XL_03467SW"/>
     <cge:Meas_Ref ObjectId="38056"/>
    <cge:TPSR_Ref TObjectID="6088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38040">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -352.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8595" ObjectName="SW-CX_XL.CX_XL_033XC"/>
     <cge:Meas_Ref ObjectId="38040"/>
    <cge:TPSR_Ref TObjectID="8595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38040">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -273.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8596" ObjectName="SW-CX_XL.CX_XL_033XC1"/>
     <cge:Meas_Ref ObjectId="38040"/>
    <cge:TPSR_Ref TObjectID="8596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38041">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4054.000000 -224.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6086" ObjectName="SW-CX_XL.CX_XL_03367SW"/>
     <cge:Meas_Ref ObjectId="38041"/>
    <cge:TPSR_Ref TObjectID="6086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47468">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -350.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8599" ObjectName="SW-CX_XL.CX_XL_035XC"/>
     <cge:Meas_Ref ObjectId="47468"/>
    <cge:TPSR_Ref TObjectID="8599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47468">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -275.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8600" ObjectName="SW-CX_XL.CX_XL_035XC1"/>
     <cge:Meas_Ref ObjectId="47468"/>
    <cge:TPSR_Ref TObjectID="8600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47469">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4657.000000 -222.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10598" ObjectName="SW-CX_XL.CX_XL_03567SW"/>
     <cge:Meas_Ref ObjectId="47469"/>
    <cge:TPSR_Ref TObjectID="10598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 -353.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8601" ObjectName="SW-CX_XL.CX_XL_036XC"/>
     <cge:Meas_Ref ObjectId="47483"/>
    <cge:TPSR_Ref TObjectID="8601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 -274.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8602" ObjectName="SW-CX_XL.CX_XL_036XC1"/>
     <cge:Meas_Ref ObjectId="47483"/>
    <cge:TPSR_Ref TObjectID="8602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38086">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4828.000000 -225.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6091" ObjectName="SW-CX_XL.CX_XL_03667SW"/>
     <cge:Meas_Ref ObjectId="38086"/>
    <cge:TPSR_Ref TObjectID="6091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4651.000000 -4.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4519.000000 -4.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4392.000000 -4.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -32.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.000000 -356.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8603" ObjectName="SW-CX_XL.CX_XL_037XC"/>
     <cge:Meas_Ref ObjectId="47497"/>
    <cge:TPSR_Ref TObjectID="8603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.000000 -276.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8604" ObjectName="SW-CX_XL.CX_XL_037XC1"/>
     <cge:Meas_Ref ObjectId="47497"/>
    <cge:TPSR_Ref TObjectID="8604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -543.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8586" ObjectName="SW-CX_XL.CX_XL_001XC"/>
     <cge:Meas_Ref ObjectId="47372"/>
    <cge:TPSR_Ref TObjectID="8586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -540.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8588" ObjectName="SW-CX_XL.CX_XL_002XC"/>
     <cge:Meas_Ref ObjectId="47387"/>
    <cge:TPSR_Ref TObjectID="8588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -460.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8587" ObjectName="SW-CX_XL.CX_XL_001XC1"/>
     <cge:Meas_Ref ObjectId="47372"/>
    <cge:TPSR_Ref TObjectID="8587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -461.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8589" ObjectName="SW-CX_XL.CX_XL_002XC1"/>
     <cge:Meas_Ref ObjectId="47387"/>
    <cge:TPSR_Ref TObjectID="8589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37903">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4126.000000 -583.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6058" ObjectName="SW-CX_XL.CX_XL_00167SW"/>
     <cge:Meas_Ref ObjectId="37903"/>
    <cge:TPSR_Ref TObjectID="6058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37941">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4732.000000 -583.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6064" ObjectName="SW-CX_XL.CX_XL_00267SW"/>
     <cge:Meas_Ref ObjectId="37941"/>
    <cge:TPSR_Ref TObjectID="6064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37890">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -774.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6056" ObjectName="SW-CX_XL.CX_XL_1016SW"/>
     <cge:Meas_Ref ObjectId="37890"/>
    <cge:TPSR_Ref TObjectID="6056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37930">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 -774.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6062" ObjectName="SW-CX_XL.CX_XL_1026SW"/>
     <cge:Meas_Ref ObjectId="37930"/>
    <cge:TPSR_Ref TObjectID="6062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37888">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -877.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6054" ObjectName="SW-CX_XL.CX_XL_1011SW"/>
     <cge:Meas_Ref ObjectId="37888"/>
    <cge:TPSR_Ref TObjectID="6054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37928">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 -878.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6060" ObjectName="SW-CX_XL.CX_XL_1021SW"/>
     <cge:Meas_Ref ObjectId="37928"/>
    <cge:TPSR_Ref TObjectID="6060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37997">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -948.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6073" ObjectName="SW-CX_XL.CX_XL_1311SW"/>
     <cge:Meas_Ref ObjectId="37997"/>
    <cge:TPSR_Ref TObjectID="6073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37994">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -1056.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6070" ObjectName="SW-CX_XL.CX_XL_1316SW"/>
     <cge:Meas_Ref ObjectId="37994"/>
    <cge:TPSR_Ref TObjectID="6070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37989">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6067" ObjectName="SW-CX_XL.CX_XL_1901SW"/>
     <cge:Meas_Ref ObjectId="37989"/>
    <cge:TPSR_Ref TObjectID="6067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37889">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4126.000000 -867.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6055" ObjectName="SW-CX_XL.CX_XL_10117SW"/>
     <cge:Meas_Ref ObjectId="37889"/>
    <cge:TPSR_Ref TObjectID="6055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37995">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4126.000000 -1043.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6071" ObjectName="SW-CX_XL.CX_XL_13160SW"/>
     <cge:Meas_Ref ObjectId="37995"/>
    <cge:TPSR_Ref TObjectID="6071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37996">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4126.000000 -1098.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6072" ObjectName="SW-CX_XL.CX_XL_13167SW"/>
     <cge:Meas_Ref ObjectId="37996"/>
    <cge:TPSR_Ref TObjectID="6072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47414">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4127.000000 -989.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8592" ObjectName="SW-CX_XL.CX_XL_13117SW"/>
     <cge:Meas_Ref ObjectId="47414"/>
    <cge:TPSR_Ref TObjectID="8592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37990">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4431.000000 -905.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6068" ObjectName="SW-CX_XL.CX_XL_19010SW"/>
     <cge:Meas_Ref ObjectId="37990"/>
    <cge:TPSR_Ref TObjectID="6068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37991">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4431.000000 -841.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6069" ObjectName="SW-CX_XL.CX_XL_19017SW"/>
     <cge:Meas_Ref ObjectId="37991"/>
    <cge:TPSR_Ref TObjectID="6069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37929">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4734.000000 -867.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6061" ObjectName="SW-CX_XL.CX_XL_10217SW"/>
     <cge:Meas_Ref ObjectId="37929"/>
    <cge:TPSR_Ref TObjectID="6061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57026">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3560.000000 -319.500000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10597" ObjectName="SW-CX_XL.CX_XL_0311XC"/>
     <cge:Meas_Ref ObjectId="57026"/>
    <cge:TPSR_Ref TObjectID="10597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38021">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3771.000000 -246.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6083" ObjectName="SW-CX_XL.CX_XL_03267SW"/>
     <cge:Meas_Ref ObjectId="38021"/>
    <cge:TPSR_Ref TObjectID="6083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 -319.500000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8606" ObjectName="SW-CX_XL.CX_XL_0901XC"/>
     <cge:Meas_Ref ObjectId="37968"/>
    <cge:TPSR_Ref TObjectID="8606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37987">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -319.500000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10595" ObjectName="SW-CX_XL.CX_XL_0122XC"/>
     <cge:Meas_Ref ObjectId="37987"/>
    <cge:TPSR_Ref TObjectID="10595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37970">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -310.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8607" ObjectName="SW-CX_XL.CX_XL_0902XC"/>
     <cge:Meas_Ref ObjectId="37970"/>
    <cge:TPSR_Ref TObjectID="8607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5129.000000 -247.034591)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6093" ObjectName="SW-CX_XL.CX_XL_03767SW"/>
     <cge:Meas_Ref ObjectId="38101"/>
    <cge:TPSR_Ref TObjectID="6093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5193.000000 -314.500000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10600" ObjectName="SW-CX_XL.CX_XL_0381XC"/>
     <cge:Meas_Ref ObjectId="38105"/>
    <cge:TPSR_Ref TObjectID="10600"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XL.CX_XL_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-420 4343,-420 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6051" ObjectName="BS-CX_XL.CX_XL_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="6051"/></metadata>
   <polyline fill="none" opacity="0" points="3525,-420 4343,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XL.CX_XL_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-420 5233,-420 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6052" ObjectName="BS-CX_XL.CX_XL_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="6052"/></metadata>
   <polyline fill="none" opacity="0" points="4383,-420 5233,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4340,-7 4340,-65 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4340,-7 4340,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XL.CX_XL_1M">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3930,-952 4780,-952 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10603" ObjectName="BS-CX_XL.CX_XL_1M"/>
    <cge:TPSR_Ref TObjectID="10603"/></metadata>
   <polyline fill="none" opacity="0" points="3930,-952 4780,-952 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14df1d0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4272.500000 -240.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_146ca00" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4109.500000 -240.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1591590" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4712.500000 -238.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f67a0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4883.500000 -241.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14cb9b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -109.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a9640" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4191.500000 -599.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1538ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4787.500000 -599.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f4d40" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4181.500000 -883.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149b260" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4181.500000 -1059.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1551660" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4181.500000 -1114.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f8d80" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4183.500000 -1005.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ec560" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4486.500000 -921.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ef9b0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4486.500000 -857.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a7ac0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4789.500000 -883.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_154c6b0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3826.500000 -261.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14cd9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5184.500000 -263.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b39040" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 -709.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b39790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -708.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_155e6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-420 3711,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6051@0" ObjectIDZND0="8593@0" Pin0InfoVect0LinkObjId="SW-47425_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-420 3711,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14fc200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-207 3711,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1590580@1" ObjectIDZND0="g_12b81f0@1" Pin0InfoVect0LinkObjId="g_12b81f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1590580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-207 3711,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14fc3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-146 3711,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12b81f0@0" ObjectIDZND0="g_13004b0@1" Pin0InfoVect0LinkObjId="g_13004b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b81f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-146 3711,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15ac5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-420 4303,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6051@0" ObjectIDZND0="8590@0" Pin0InfoVect0LinkObjId="SW-37984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-420 4303,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15d1d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-420 4159,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6051@0" ObjectIDZND0="8597@0" Pin0InfoVect0LinkObjId="SW-38055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-420 4159,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_153fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4190,-250 4159,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6088@1" ObjectIDZND0="8598@x" ObjectIDZND1="g_1540550@0" ObjectIDZND2="g_1b3dec0@0" Pin0InfoVect0LinkObjId="SW-38055_0" Pin0InfoVect1LinkObjId="g_1540550_0" Pin0InfoVect2LinkObjId="g_1b3dec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4190,-250 4159,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_153ff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-279 4159,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8598@1" ObjectIDZND0="6088@x" ObjectIDZND1="g_1540550@0" ObjectIDZND2="g_1b3dec0@0" Pin0InfoVect0LinkObjId="SW-38056_0" Pin0InfoVect1LinkObjId="g_1540550_0" Pin0InfoVect2LinkObjId="g_1b3dec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-279 4159,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1540170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-218 4191,-219 4159,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1b3dec0@0" ObjectIDZND0="8598@x" ObjectIDZND1="6088@x" ObjectIDZND2="g_1540550@0" Pin0InfoVect0LinkObjId="SW-38055_0" Pin0InfoVect1LinkObjId="SW-38056_0" Pin0InfoVect2LinkObjId="g_1540550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3dec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-218 4191,-219 4159,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1540360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-250 4159,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8598@x" ObjectIDND1="6088@x" ObjectIDZND0="g_1540550@0" ObjectIDZND1="g_1b3dec0@0" Pin0InfoVect0LinkObjId="g_1540550_0" Pin0InfoVect1LinkObjId="g_1b3dec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38055_0" Pin1InfoVect1LinkObjId="SW-38056_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-250 4159,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1540f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-219 4159,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8598@x" ObjectIDND1="6088@x" ObjectIDND2="g_1b3dec0@0" ObjectIDZND0="g_1540550@0" Pin0InfoVect0LinkObjId="g_1540550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-38055_0" Pin1InfoVect1LinkObjId="SW-38056_0" Pin1InfoVect2LinkObjId="g_1b3dec0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-219 4159,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1541130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-133 4159,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1540550@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1540550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-133 4159,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ff8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-420 3996,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6051@0" ObjectIDZND0="8595@0" Pin0InfoVect0LinkObjId="SW-38040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-420 3996,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15827f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-250 3996,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6086@1" ObjectIDZND0="8596@x" ObjectIDZND1="g_1582fb0@0" ObjectIDZND2="g_1b3ec30@0" Pin0InfoVect0LinkObjId="SW-38040_0" Pin0InfoVect1LinkObjId="g_1582fb0_0" Pin0InfoVect2LinkObjId="g_1b3ec30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38041_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-250 3996,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15829e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-279 3996,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8596@1" ObjectIDZND0="6086@x" ObjectIDZND1="g_1582fb0@0" ObjectIDZND2="g_1b3ec30@0" Pin0InfoVect0LinkObjId="SW-38041_0" Pin0InfoVect1LinkObjId="g_1582fb0_0" Pin0InfoVect2LinkObjId="g_1b3ec30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-279 3996,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1582bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4028,-218 4028,-219 3996,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1b3ec30@0" ObjectIDZND0="8596@x" ObjectIDZND1="6086@x" ObjectIDZND2="g_1582fb0@0" Pin0InfoVect0LinkObjId="SW-38040_0" Pin0InfoVect1LinkObjId="SW-38041_0" Pin0InfoVect2LinkObjId="g_1582fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4028,-218 4028,-219 3996,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1582dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-250 3996,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8596@x" ObjectIDND1="6086@x" ObjectIDZND0="g_1582fb0@0" ObjectIDZND1="g_1b3ec30@0" Pin0InfoVect0LinkObjId="g_1582fb0_0" Pin0InfoVect1LinkObjId="g_1b3ec30_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38040_0" Pin1InfoVect1LinkObjId="SW-38041_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-250 3996,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15839a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-219 3996,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8596@x" ObjectIDND1="6086@x" ObjectIDND2="g_1b3ec30@0" ObjectIDZND0="g_1582fb0@0" Pin0InfoVect0LinkObjId="g_1582fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-38040_0" Pin1InfoVect1LinkObjId="SW-38041_0" Pin1InfoVect2LinkObjId="g_1b3ec30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-219 3996,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1583b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-133 3996,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1582fb0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1582fb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-133 3996,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15bba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-420 4599,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6052@0" ObjectIDZND0="8599@0" Pin0InfoVect0LinkObjId="SW-47468_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1466c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-420 4599,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1591e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-248 4599,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10598@1" ObjectIDZND0="g_1b3c3e0@0" ObjectIDZND1="g_15924e0@0" ObjectIDZND2="8600@x" Pin0InfoVect0LinkObjId="g_1b3c3e0_0" Pin0InfoVect1LinkObjId="g_15924e0_0" Pin0InfoVect2LinkObjId="SW-47468_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-248 4599,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15920a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-217 4599,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1b3c3e0@0" ObjectIDZND0="10598@x" ObjectIDZND1="8600@x" ObjectIDZND2="g_15924e0@0" Pin0InfoVect0LinkObjId="SW-47469_0" Pin0InfoVect1LinkObjId="SW-47468_0" Pin0InfoVect2LinkObjId="g_15924e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-217 4599,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15922c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-248 4599,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10598@x" ObjectIDND1="8600@x" ObjectIDZND0="g_1b3c3e0@0" ObjectIDZND1="g_15924e0@0" Pin0InfoVect0LinkObjId="g_1b3c3e0_0" Pin0InfoVect1LinkObjId="g_15924e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47469_0" Pin1InfoVect1LinkObjId="SW-47468_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-248 4599,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1593030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-217 4599,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10598@x" ObjectIDND1="8600@x" ObjectIDND2="g_1b3c3e0@0" ObjectIDZND0="g_15924e0@0" Pin0InfoVect0LinkObjId="g_15924e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47469_0" Pin1InfoVect1LinkObjId="SW-47468_0" Pin1InfoVect2LinkObjId="g_1b3c3e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-217 4599,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_150d380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-131 4599,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_15924e0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15924e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-131 4599,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15b25c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-420 4770,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6052@0" ObjectIDZND0="8601@0" Pin0InfoVect0LinkObjId="SW-47483_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1466c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-420 4770,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f7090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4801,-251 4770,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6091@1" ObjectIDZND0="8602@x" ObjectIDZND1="g_14e8160@0" ObjectIDZND2="g_1b3d150@0" Pin0InfoVect0LinkObjId="SW-47483_0" Pin0InfoVect1LinkObjId="g_14e8160_0" Pin0InfoVect2LinkObjId="g_1b3d150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-251 4770,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f72b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-280 4770,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8602@1" ObjectIDZND0="6091@x" ObjectIDZND1="g_14e8160@0" ObjectIDZND2="g_1b3d150@0" Pin0InfoVect0LinkObjId="SW-38086_0" Pin0InfoVect1LinkObjId="g_14e8160_0" Pin0InfoVect2LinkObjId="g_1b3d150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47483_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-280 4770,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e7d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4803,-219 4802,-220 4770,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1b3d150@0" ObjectIDZND0="8602@x" ObjectIDZND1="6091@x" ObjectIDZND2="g_14e8160@0" Pin0InfoVect0LinkObjId="SW-47483_0" Pin0InfoVect1LinkObjId="SW-38086_0" Pin0InfoVect2LinkObjId="g_14e8160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3d150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4803,-219 4802,-220 4770,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e7f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-251 4770,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8602@x" ObjectIDND1="6091@x" ObjectIDZND0="g_14e8160@0" ObjectIDZND1="g_1b3d150@0" Pin0InfoVect0LinkObjId="g_14e8160_0" Pin0InfoVect1LinkObjId="g_1b3d150_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47483_0" Pin1InfoVect1LinkObjId="SW-38086_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-251 4770,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e8b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-220 4770,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8602@x" ObjectIDND1="6091@x" ObjectIDND2="g_1b3d150@0" ObjectIDZND0="g_14e8160@0" Pin0InfoVect0LinkObjId="g_14e8160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47483_0" Pin1InfoVect1LinkObjId="SW-38086_0" Pin1InfoVect2LinkObjId="g_1b3d150_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-220 4770,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_150cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-30 4626,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1493850@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1493850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-30 4626,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_150d120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-30 4770,-30 4770,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14e8160@1" Pin0InfoVect0LinkObjId="g_14e8160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-30 4770,-30 4770,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_157e490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-30 4555,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1493850@0" Pin0InfoVect0LinkObjId="g_1493850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-30 4555,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1580150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-30 4492,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4472,-30 4492,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15752f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-31 4421,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-31 4421,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1575550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-90 4421,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_14cb9b0@0" Pin0InfoVect0LinkObjId="g_14cb9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-90 4421,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1573e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-420 5072,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6052@0" ObjectIDZND0="8603@0" Pin0InfoVect0LinkObjId="SW-47497_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1466c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-420 5072,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15441f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-206 5072,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1503720@1" ObjectIDZND0="g_15777c0@1" Pin0InfoVect0LinkObjId="g_15777c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1503720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-206 5072,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1544450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-145 5072,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15777c0@0" ObjectIDZND0="g_14709a0@1" Pin0InfoVect0LinkObjId="g_14709a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15777c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-145 5072,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15446b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-233 5200,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1544f00@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1544f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5200,-233 5200,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a93e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-420 4067,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6051@0" ObjectIDZND0="8587@1" Pin0InfoVect0LinkObjId="SW-47372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-420 4067,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15001d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-609 4761,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6064@0" ObjectIDZND0="g_1538ce0@0" Pin0InfoVect0LinkObjId="g_1538ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-609 4761,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-609 4674,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="6064@1" ObjectIDZND0="8588@x" ObjectIDZND1="g_1b3b670@0" ObjectIDZND2="6095@x" Pin0InfoVect0LinkObjId="SW-47387_0" Pin0InfoVect1LinkObjId="g_1b3b670_0" Pin0InfoVect2LinkObjId="g_146db40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37941_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-609 4674,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146d1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-567 4067,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="8586@0" ObjectIDZND0="6058@x" ObjectIDZND1="g_1b3a1e0@0" ObjectIDZND2="6094@x" Pin0InfoVect0LinkObjId="SW-37903_0" Pin0InfoVect1LinkObjId="g_1b3a1e0_0" Pin0InfoVect2LinkObjId="g_1b3b410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-567 4067,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146d420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-564 4674,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="8588@0" ObjectIDZND0="6064@x" ObjectIDZND1="g_1b3b670@0" ObjectIDZND2="6095@x" Pin0InfoVect0LinkObjId="SW-37941_0" Pin0InfoVect1LinkObjId="g_1b3b670_0" Pin0InfoVect2LinkObjId="g_146db40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-564 4674,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146d680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-655 4674,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_1b3b670@0" ObjectIDZND0="8588@x" ObjectIDZND1="6064@x" ObjectIDZND2="6095@x" Pin0InfoVect0LinkObjId="SW-47387_0" Pin0InfoVect1LinkObjId="SW-37941_0" Pin0InfoVect2LinkObjId="g_146db40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3b670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-655 4674,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-609 4674,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="8588@x" ObjectIDND1="6064@x" ObjectIDZND0="g_1b3b670@0" ObjectIDZND1="6095@x" Pin0InfoVect0LinkObjId="g_1b3b670_0" Pin0InfoVect1LinkObjId="g_146db40_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47387_0" Pin1InfoVect1LinkObjId="SW-37941_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-609 4674,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146db40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-655 4674,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="8588@x" ObjectIDND1="6064@x" ObjectIDND2="g_1b3b670@0" ObjectIDZND0="6095@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47387_0" Pin1InfoVect1LinkObjId="SW-37941_0" Pin1InfoVect2LinkObjId="g_1b3b670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-655 4674,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d5af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-769 4068,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="6094@0" ObjectIDZND0="6056@0" Pin0InfoVect0LinkObjId="SW-37890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3b410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-769 4068,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d5d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-832 4068,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6056@1" ObjectIDZND0="6053@1" Pin0InfoVect0LinkObjId="SW-37886_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-832 4068,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d5f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-935 4068,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6054@1" ObjectIDZND0="10603@0" Pin0InfoVect0LinkObjId="g_14d66b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37888_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-935 4068,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d61f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-769 4675,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="6095@0" ObjectIDZND0="6062@0" Pin0InfoVect0LinkObjId="SW-37930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_146db40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-769 4675,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d6450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-832 4675,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6062@1" ObjectIDZND0="6059@1" Pin0InfoVect0LinkObjId="SW-37927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-832 4675,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d66b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-936 4675,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6060@1" ObjectIDZND0="10603@0" Pin0InfoVect0LinkObjId="g_14d5f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-936 4675,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1523590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-952 4067,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10603@0" ObjectIDZND0="6073@0" Pin0InfoVect0LinkObjId="SW-37997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d5f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-952 4067,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15279e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-281 4599,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8600@1" ObjectIDZND0="g_1b3c3e0@0" ObjectIDZND1="g_15924e0@0" ObjectIDZND2="10598@x" Pin0InfoVect0LinkObjId="g_1b3c3e0_0" Pin0InfoVect1LinkObjId="g_15924e0_0" Pin0InfoVect2LinkObjId="SW-47469_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-281 4599,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1527c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-30 4339,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-30 4339,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f1060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-30 4421,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-30 4421,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f12c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-30 4401,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-30 4401,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f4b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3567,-234 3567,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_12fe180@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12fe180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3567,-234 3567,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1498580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-893 4156,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6055@0" ObjectIDZND0="g_14f4d40@0" Pin0InfoVect0LinkObjId="g_14f4d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37889_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-893 4156,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_149b000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-893 4068,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6055@1" ObjectIDZND0="6053@x" ObjectIDZND1="6054@x" Pin0InfoVect0LinkObjId="SW-37886_0" Pin0InfoVect1LinkObjId="SW-37888_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37889_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-893 4068,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_154eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-1069 4155,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6071@0" ObjectIDZND0="g_149b260@0" Pin0InfoVect0LinkObjId="g_149b260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-1069 4155,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1551400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-1069 4067,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6071@1" ObjectIDZND0="6075@x" ObjectIDZND1="6070@x" Pin0InfoVect0LinkObjId="SW-38002_0" Pin0InfoVect1LinkObjId="SW-37994_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-1069 4067,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1552090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-1124 4155,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6072@0" ObjectIDZND0="g_1551660@0" Pin0InfoVect0LinkObjId="g_1551660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-1124 4155,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14f8b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-1124 4067,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6072@1" ObjectIDZND0="6070@x" ObjectIDZND1="g_1b3fe60@0" Pin0InfoVect0LinkObjId="SW-37994_0" Pin0InfoVect1LinkObjId="g_1b3fe60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-1124 4067,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ef750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-931 4372,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="6068@1" ObjectIDZND0="6067@x" ObjectIDZND1="10603@0" Pin0InfoVect0LinkObjId="SW-37989_0" Pin0InfoVect1LinkObjId="g_14d5f90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-931 4372,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14a7860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-867 4372,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="6069@1" ObjectIDZND0="6067@x" ObjectIDZND1="g_1526300@0" Pin0InfoVect0LinkObjId="SW-37989_0" Pin0InfoVect1LinkObjId="g_1526300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-867 4372,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15b50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-893 4675,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6061@1" ObjectIDZND0="6059@x" ObjectIDZND1="6060@x" Pin0InfoVect0LinkObjId="SW-37927_0" Pin0InfoVect1LinkObjId="SW-37928_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-893 4675,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-882 4068,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6053@0" ObjectIDZND0="6055@x" ObjectIDZND1="6054@x" Pin0InfoVect0LinkObjId="SW-37889_0" Pin0InfoVect1LinkObjId="SW-37888_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37886_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-882 4068,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152cc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-893 4068,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6053@x" ObjectIDND1="6055@x" ObjectIDZND0="6054@0" Pin0InfoVect0LinkObjId="SW-37888_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37886_0" Pin1InfoVect1LinkObjId="SW-37889_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-893 4068,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152ceb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1059 4067,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6075@0" ObjectIDZND0="6071@x" ObjectIDZND1="6070@x" Pin0InfoVect0LinkObjId="SW-37995_0" Pin0InfoVect1LinkObjId="SW-37994_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1059 4067,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152d110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1069 4067,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6075@x" ObjectIDND1="6071@x" ObjectIDZND0="6070@0" Pin0InfoVect0LinkObjId="SW-37994_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38002_0" Pin1InfoVect1LinkObjId="SW-37995_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1069 4067,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152d370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1114 4067,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6070@1" ObjectIDZND0="6072@x" ObjectIDZND1="g_1b3fe60@0" Pin0InfoVect0LinkObjId="SW-37996_0" Pin0InfoVect1LinkObjId="g_1b3fe60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37994_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1114 4067,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-919 4371,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="6067@1" ObjectIDZND0="6068@x" ObjectIDZND1="10603@0" Pin0InfoVect0LinkObjId="SW-37990_0" Pin0InfoVect1LinkObjId="g_14d5f90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-919 4371,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152d830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-931 4371,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="6067@x" ObjectIDND1="6068@x" ObjectIDZND0="10603@0" Pin0InfoVect0LinkObjId="g_14d5f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37989_0" Pin1InfoVect1LinkObjId="SW-37990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-931 4371,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152da90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-867 4371,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="6069@x" ObjectIDND1="g_1526300@0" ObjectIDZND0="6067@0" Pin0InfoVect0LinkObjId="SW-37989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37991_0" Pin1InfoVect1LinkObjId="g_1526300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-867 4371,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152dcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-883 4675,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6059@0" ObjectIDZND0="6061@x" ObjectIDZND1="6060@x" Pin0InfoVect0LinkObjId="SW-37929_0" Pin0InfoVect1LinkObjId="SW-37928_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-883 4675,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_152df50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-893 4675,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6059@x" ObjectIDND1="6061@x" ObjectIDZND0="6060@0" Pin0InfoVect0LinkObjId="SW-37928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37927_0" Pin1InfoVect1LinkObjId="SW-37929_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-893 4675,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_154d0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-272 3781,-271 3800,-271 3806,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="6083@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38021_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-272 3781,-271 3800,-271 3806,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_156d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-272 3712,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6083@1" ObjectIDZND0="8594@x" ObjectIDZND1="g_1590580@0" Pin0InfoVect0LinkObjId="SW-47425_0" Pin0InfoVect1LinkObjId="g_1590580_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38021_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-272 3712,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_156d750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-283 3711,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8594@1" ObjectIDZND0="6083@x" ObjectIDZND1="g_1590580@0" Pin0InfoVect0LinkObjId="SW-38021_0" Pin0InfoVect1LinkObjId="g_1590580_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47425_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-283 3711,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_156d9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-272 3711,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8594@x" ObjectIDND1="6083@x" ObjectIDZND0="g_1590580@0" Pin0InfoVect0LinkObjId="g_1590580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47425_0" Pin1InfoVect1LinkObjId="SW-38021_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-272 3711,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1571500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-609 4135,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_15a9640@0" ObjectIDZND0="6058@0" Pin0InfoVect0LinkObjId="SW-37903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15a9640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-609 4135,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1571760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-609 4067,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="6058@1" ObjectIDZND0="8586@x" ObjectIDZND1="g_1b3a1e0@0" ObjectIDZND2="6094@x" Pin0InfoVect0LinkObjId="SW-47372_0" Pin0InfoVect1LinkObjId="g_1b3a1e0_0" Pin0InfoVect2LinkObjId="g_1b3b410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37903_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-609 4067,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ce3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-273 5072,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6093@1" ObjectIDZND0="8604@x" ObjectIDZND1="g_1503720@0" Pin0InfoVect0LinkObjId="SW-47497_0" Pin0InfoVect1LinkObjId="g_1503720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-273 5072,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1462ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-420 5200,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6052@0" ObjectIDZND0="10600@0" Pin0InfoVect0LinkObjId="SW-38105_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1466c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5200,-420 5200,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1463120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-308 5200,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="10600@1" ObjectIDZND0="g_1544f00@0" Pin0InfoVect0LinkObjId="g_1544f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5200,-308 5200,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1463f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-301 3711,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8594@0" ObjectIDZND0="6082@0" Pin0InfoVect0LinkObjId="SW-47429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-301 3711,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1464150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3711,-346 3711,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6082@1" ObjectIDZND0="8593@1" Pin0InfoVect0LinkObjId="SW-47425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3711,-346 3711,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1464340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-297 4159,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8598@0" ObjectIDZND0="6087@0" Pin0InfoVect0LinkObjId="SW-47457_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38055_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-297 4159,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1464530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-339 4159,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6087@1" ObjectIDZND0="8597@1" Pin0InfoVect0LinkObjId="SW-38055_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47457_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-339 4159,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1464760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-297 3996,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8596@0" ObjectIDZND0="6085@0" Pin0InfoVect0LinkObjId="SW-47444_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-297 3996,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1464990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-342 3996,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6085@1" ObjectIDZND0="8595@1" Pin0InfoVect0LinkObjId="SW-38040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-342 3996,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1464bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-299 4599,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8600@0" ObjectIDZND0="6089@0" Pin0InfoVect0LinkObjId="SW-47472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-299 4599,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1464df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-339 4599,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6089@1" ObjectIDZND0="8599@1" Pin0InfoVect0LinkObjId="SW-47468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-339 4599,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1465020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-484 4067,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8587@0" ObjectIDZND0="6057@0" Pin0InfoVect0LinkObjId="SW-47375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-484 4067,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1465250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-530 4067,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6057@1" ObjectIDZND0="8586@1" Pin0InfoVect0LinkObjId="SW-47372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47375_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-530 4067,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14654b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-528 4674,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6063@1" ObjectIDZND0="8588@1" Pin0InfoVect0LinkObjId="SW-47387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-528 4674,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1465710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1025 4067,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6075@1" ObjectIDZND0="6073@x" ObjectIDZND1="8592@x" Pin0InfoVect0LinkObjId="SW-37997_0" Pin0InfoVect1LinkObjId="SW-47414_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38002_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1025 4067,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1465970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1015 4067,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6075@x" ObjectIDND1="8592@x" ObjectIDZND0="6073@1" Pin0InfoVect0LinkObjId="SW-37997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38002_0" Pin1InfoVect1LinkObjId="SW-47414_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1015 4067,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1465bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1015 4100,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6073@x" ObjectIDND1="6075@x" ObjectIDZND0="8592@1" Pin0InfoVect0LinkObjId="SW-47414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37997_0" Pin1InfoVect1LinkObjId="SW-38002_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1015 4100,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1465e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-1015 4157,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8592@0" ObjectIDZND0="g_14f8d80@0" Pin0InfoVect0LinkObjId="g_14f8d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-1015 4157,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1466090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-420 4924,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6052@0" ObjectIDZND0="8607@0" Pin0InfoVect0LinkObjId="SW-37970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1466c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-420 4924,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14662f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-304 4924,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8607@1" ObjectIDZND0="g_15759f0@0" Pin0InfoVect0LinkObjId="g_15759f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-304 4924,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1466550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-298 4770,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8602@0" ObjectIDZND0="6090@0" Pin0InfoVect0LinkObjId="SW-47486_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-298 4770,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14667b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-342 4770,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6090@1" ObjectIDZND0="8601@1" Pin0InfoVect0LinkObjId="SW-47483_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-342 4770,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1466a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-501 4674,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6063@0" ObjectIDZND0="8589@0" Pin0InfoVect0LinkObjId="SW-47387_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-501 4674,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1466c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-467 4674,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8589@1" ObjectIDZND0="6052@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-467 4674,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d7ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-300 5072,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8604@0" ObjectIDZND0="6092@0" Pin0InfoVect0LinkObjId="SW-47501_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-300 5072,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d8120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-341 5072,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6092@1" ObjectIDZND0="8603@1" Pin0InfoVect0LinkObjId="SW-47497_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47501_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-341 5072,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1483d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-313 4303,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8591@0" ObjectIDZND0="8605@0" Pin0InfoVect0LinkObjId="SW-47404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37984_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-313 4303,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1483f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-361 4303,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8605@1" ObjectIDZND0="8590@1" Pin0InfoVect0LinkObjId="SW-37984_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-361 4303,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4462,-420 4462,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6052@0" ObjectIDZND0="10595@0" Pin0InfoVect0LinkObjId="SW-37987_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1466c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4462,-420 4462,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2f4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4462,-313 4462,-271 4303,-271 4303,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="10595@1" ObjectIDZND0="8591@1" Pin0InfoVect0LinkObjId="SW-37984_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37987_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4462,-313 4462,-271 4303,-271 4303,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3567,-420 3567,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6051@0" ObjectIDZND0="10597@0" Pin0InfoVect0LinkObjId="SW-57026_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3567,-420 3567,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3567,-313 3567,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="10597@1" ObjectIDZND0="g_12fe180@0" Pin0InfoVect0LinkObjId="g_12fe180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57026_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3567,-313 3567,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2fb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-420 3845,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6051@0" ObjectIDZND0="8606@0" Pin0InfoVect0LinkObjId="SW-37968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-420 3845,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2fdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-313 3845,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8606@1" ObjectIDZND0="g_1b50be0@0" Pin0InfoVect0LinkObjId="g_1b50be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-313 3845,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b3af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-656 4067,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_1b3a1e0@0" ObjectIDZND0="6058@x" ObjectIDZND1="8586@x" ObjectIDZND2="6094@x" Pin0InfoVect0LinkObjId="SW-37903_0" Pin0InfoVect1LinkObjId="SW-47372_0" Pin0InfoVect2LinkObjId="g_1b3b410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3a1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-656 4067,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b3b1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-609 4067,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="6058@x" ObjectIDND1="8586@x" ObjectIDZND0="g_1b3a1e0@0" ObjectIDZND1="6094@x" Pin0InfoVect0LinkObjId="g_1b3a1e0_0" Pin0InfoVect1LinkObjId="g_1b3b410_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37903_0" Pin1InfoVect1LinkObjId="SW-47372_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-609 4067,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b3b410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-656 4067,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="6058@x" ObjectIDND1="8586@x" ObjectIDND2="g_1b3a1e0@0" ObjectIDZND0="6094@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37903_0" Pin1InfoVect1LinkObjId="SW-47372_0" Pin1InfoVect2LinkObjId="g_1b3a1e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-656 4067,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b3f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1174 4090,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="6070@x" ObjectIDND1="6072@x" ObjectIDND2="g_1b3fe60@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37994_0" Pin1InfoVect1LinkObjId="SW-37996_0" Pin1InfoVect2LinkObjId="g_1b3fe60_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1174 4090,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b3fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1174 4042,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="6070@x" ObjectIDND1="6072@x" ObjectIDND2="g_1b3fe60@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37994_0" Pin1InfoVect1LinkObjId="SW-37996_0" Pin1InfoVect2LinkObjId="g_1b3fe60_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1174 4042,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b41700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-1151 4067,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b3fe60@0" ObjectIDZND0="6070@x" ObjectIDZND1="6072@x" Pin0InfoVect0LinkObjId="SW-37994_0" Pin0InfoVect1LinkObjId="SW-37996_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3fe60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-1151 4067,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b41960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-827 4371,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1526300@0" ObjectIDZND0="6067@x" ObjectIDZND1="6069@x" Pin0InfoVect0LinkObjId="SW-37989_0" Pin0InfoVect1LinkObjId="SW-37991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1526300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-827 4371,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b522d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1124 4067,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6070@x" ObjectIDND1="6072@x" ObjectIDZND0="g_1b3fe60@0" Pin0InfoVect0LinkObjId="g_1b3fe60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37994_0" Pin1InfoVect1LinkObjId="SW-37996_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1124 4067,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b52530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1174 4067,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="6070@x" ObjectIDZND1="6072@x" ObjectIDZND2="g_1b3fe60@0" Pin0InfoVect0LinkObjId="SW-37994_0" Pin0InfoVect1LinkObjId="SW-37996_0" Pin0InfoVect2LinkObjId="g_1b3fe60_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1174 4067,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b6d280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4440,-931 4460,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6068@0" ObjectIDZND0="g_14ec560@0" Pin0InfoVect0LinkObjId="g_14ec560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4440,-931 4460,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b6d4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4440,-867 4460,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6069@0" ObjectIDZND0="g_14ef9b0@0" Pin0InfoVect0LinkObjId="g_14ef9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37991_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4440,-867 4460,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b6d740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-893 4763,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6061@0" ObjectIDZND0="g_14a7ac0@0" Pin0InfoVect0LinkObjId="g_14a7ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-893 4763,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6d9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-250 4083,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6086@0" ObjectIDZND0="g_146ca00@0" Pin0InfoVect0LinkObjId="g_146ca00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-250 4083,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6dc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4226,-250 4246,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6088@0" ObjectIDZND0="g_14df1d0@0" Pin0InfoVect0LinkObjId="g_14df1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4226,-250 4246,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6de60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4666,-248 4686,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10598@0" ObjectIDZND0="g_1591590@0" Pin0InfoVect0LinkObjId="g_1591590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4666,-248 4686,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6e0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-251 4857,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6091@0" ObjectIDZND0="g_15f67a0@0" Pin0InfoVect0LinkObjId="g_15f67a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-251 4857,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6e320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5138,-273 5158,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6093@0" ObjectIDZND0="g_14cd9c0@0" Pin0InfoVect0LinkObjId="g_14cd9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5138,-273 5158,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-282 5072,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8604@1" ObjectIDZND0="6093@x" ObjectIDZND1="g_1503720@0" Pin0InfoVect0LinkObjId="SW-38101_0" Pin0InfoVect1LinkObjId="g_1503720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47497_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-282 5072,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6f070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-273 5072,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6093@x" ObjectIDND1="8604@x" ObjectIDZND0="g_1503720@0" Pin0InfoVect0LinkObjId="g_1503720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38101_0" Pin1InfoVect1LinkObjId="SW-47497_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-273 5072,-259 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6051" cx="3711" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6051" cx="4303" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6051" cx="4159" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6051" cx="3996" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6052" cx="4599" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6052" cx="4770" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6052" cx="5072" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6051" cx="4067" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10603" cx="4068" cy="-952" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10603" cx="4675" cy="-952" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10603" cx="4068" cy="-952" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4339" cy="-30" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10603" cx="4371" cy="-952" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6052" cx="5200" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6052" cx="4924" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6052" cx="4674" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6052" cx="4462" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6051" cx="3567" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6051" cx="3845" cy="-420" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37308" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3425.000000 -1080.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5890" ObjectName="DYN-CX_XL"/>
     <cge:Meas_Ref ObjectId="37308"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1595060" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 3785.000000 -141.341463) translate(0,15)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1590c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -58.000000) translate(0,15)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14fc5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -95.000000) translate(0,15)">10kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14fc5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -95.000000) translate(0,33)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1541320" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4133.000000 -92.341463) translate(0,15)">备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1583d80" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 3961.000000 -93.341463) translate(0,15)">一车间线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_150d570" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4608.000000 -119.341463) translate(0,15)">二车间线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14e8d50" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4781.000000 -126.341463) translate(0,15)">兴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14e8d50" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4781.000000 -126.341463) translate(0,33)">棱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14e8d50" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4781.000000 -126.341463) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1577180" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4865.000000 -141.341463) translate(0,15)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1504370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -58.000000) translate(0,15)">2号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1544910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5172.000000 -95.000000) translate(0,15)">10kV2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1544910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5172.000000 -95.000000) translate(0,33)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1522b80" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4071.000000 -1171.341463) translate(0,15)">狮兴I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1525d10" transform="matrix(1.000000 -0.000000 -0.000000 1.024390 4014.000000 -1198.341463) translate(0,15)">110kV勐武线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1530800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4029.500000 -873.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1530bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.500000 -924.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1530f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -821.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15311b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -919.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14600d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.500000 -735.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1460620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4737.000000 -735.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14609d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -521.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1460c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.000000 -635.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1460e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -908.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1461090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -922.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14613d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -860.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1463380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.000000 -355.500000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1463870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -50.000000) translate(0,15)">110kV武定变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1463870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -50.000000) translate(0,33)"> 10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14d8380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -575.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_152f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1054.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_152f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1054.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_152f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1054.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_152f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1054.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_152f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1054.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_152f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1054.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_152f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1054.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_14b9fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -1160.500000) translate(0,16)">兴棱变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bacf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4029.500000 -1047.000000) translate(0,12)">131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bb1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -1041.000000) translate(0,12)">13117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bb400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.500000 -999.000000) translate(0,12)">1311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bb640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.500000 -1094.000000) translate(0,12)">13160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bb880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.500000 -1150.000000) translate(0,12)">13167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bbac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -1110.000000) translate(0,12)">1316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bc840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -919.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bca80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4634.500000 -929.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bccc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4634.000000 -826.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bcf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.500000 -875.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bd140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -635.000000) translate(0,12)">00267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bd380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -522.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bd5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -979.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147f2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -440.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147fd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4383.000000 -440.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1480280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3574.000000 -356.500000) translate(0,12)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1480500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3741.000000 -298.000000) translate(0,12)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1480740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -276.000000) translate(0,12)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1480980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4005.000000 -336.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1480bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3720.000000 -340.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1480e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -276.000000) translate(0,12)">03467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1481310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -333.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1481590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -355.500000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14817d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4474.000000 -355.500000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1481a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -333.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1481d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -274.000000) translate(0,12)">03567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14821b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -277.000000) translate(0,12)">03667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14823f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4779.000000 -336.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1482630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5099.000000 -299.000000) translate(0,12)">03767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1482870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5081.000000 -335.000000) translate(0,12)">037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1482ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4885.000000 -347.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1482cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5212.000000 -346.000000) translate(0,12)">0381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1483210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -56.000000) translate(0,12)">4546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1483490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4495.000000 -56.000000) translate(0,12)">4542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14836d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -56.000000) translate(0,12)">4541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1483910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4428.000000 -79.000000) translate(0,12)">45417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1483b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4448.000000 -54.000000) translate(0,12)">454</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b41bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -767.000000) translate(0,12)">110kV母线TV</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4067,-749 4108,-749 4108,-735 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4675,-750 4716,-750 4716,-736 " stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1526300">
    <use class="BV-110KV" transform="matrix(2.451613 -0.000000 0.000000 -2.384615 4335.000000 -775.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_12fe180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -229.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b81f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -141.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13004b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -63.000000)" xlink:href="#lightningRod:shape84"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1590580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -202.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1540550">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -128.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1582fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -128.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15924e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -126.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e8160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4765.000000 -130.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1493850">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4613.000000 -25.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15759f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 -151.000000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15777c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.000000 -140.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14709a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5036.000000 -62.000000)" xlink:href="#lightningRod:shape84"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1503720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 -201.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1544f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5195.000000 -228.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3a1e0">
    <use class="BV-10KV" transform="matrix(0.765625 -0.000000 0.000000 -0.933333 4093.000000 -648.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3b670">
    <use class="BV-10KV" transform="matrix(0.765625 -0.000000 0.000000 -0.933333 4702.000000 -647.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3c3e0">
    <use class="BV-10KV" transform="matrix(0.765625 -0.000000 0.000000 -0.933333 4628.000000 -209.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3d150">
    <use class="BV-10KV" transform="matrix(0.765625 -0.000000 0.000000 -0.933333 4799.000000 -213.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3dec0">
    <use class="BV-10KV" transform="matrix(0.765625 -0.000000 0.000000 -0.933333 4187.000000 -212.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3ec30">
    <use class="BV-10KV" transform="matrix(0.765625 -0.000000 0.000000 -0.933333 4024.000000 -212.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3fe60">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4005.500000 -1159.500000)" xlink:href="#lightningRod:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b50be0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -146.000000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37829" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -1079.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37829" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6075"/>
     <cge:Term_Ref ObjectID="8822"/>
    <cge:TPSR_Ref TObjectID="6075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37830" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -1079.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6075"/>
     <cge:Term_Ref ObjectID="8822"/>
    <cge:TPSR_Ref TObjectID="6075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37826" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -1079.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6075"/>
     <cge:Term_Ref ObjectID="8822"/>
    <cge:TPSR_Ref TObjectID="6075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-37828" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -1079.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6075"/>
     <cge:Term_Ref ObjectID="8822"/>
    <cge:TPSR_Ref TObjectID="6075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-37831" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -1079.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6075"/>
     <cge:Term_Ref ObjectID="8822"/>
    <cge:TPSR_Ref TObjectID="6075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37796" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37797" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37787" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-37789" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-37790" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-37791" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37792" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-37795" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-37798" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -937.000000) translate(0,132)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6059"/>
     <cge:Term_Ref ObjectID="8792"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47326" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47326" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47327" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47317" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-47319" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-47320" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-47321" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47322" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47325" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-47328" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -585.000000) translate(0,132)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6063"/>
     <cge:Term_Ref ObjectID="12118"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37771" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37772" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37762" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-37764" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-37765" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-37766" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37767" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-37770" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-37773" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -937.000000) translate(0,132)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6053"/>
     <cge:Term_Ref ObjectID="8778"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47314" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47315" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47305" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-47307" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-47308" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-47309" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47310" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47313" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-47316" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -585.000000) translate(0,132)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6057"/>
     <cge:Term_Ref ObjectID="12116"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47533" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -320.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6085"/>
     <cge:Term_Ref ObjectID="12122"/>
    <cge:TPSR_Ref TObjectID="6085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47344" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -320.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47344" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6085"/>
     <cge:Term_Ref ObjectID="12122"/>
    <cge:TPSR_Ref TObjectID="6085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47341" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -320.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6085"/>
     <cge:Term_Ref ObjectID="12122"/>
    <cge:TPSR_Ref TObjectID="6085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47349" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -329.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47349" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6087"/>
     <cge:Term_Ref ObjectID="12124"/>
    <cge:TPSR_Ref TObjectID="6087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47350" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -329.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47350" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6087"/>
     <cge:Term_Ref ObjectID="12124"/>
    <cge:TPSR_Ref TObjectID="6087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47346" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -329.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47346" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6087"/>
     <cge:Term_Ref ObjectID="12124"/>
    <cge:TPSR_Ref TObjectID="6087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47332" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -370.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8605"/>
     <cge:Term_Ref ObjectID="12132"/>
    <cge:TPSR_Ref TObjectID="8605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47333" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -370.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8605"/>
     <cge:Term_Ref ObjectID="12132"/>
    <cge:TPSR_Ref TObjectID="8605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47329" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -370.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8605"/>
     <cge:Term_Ref ObjectID="12132"/>
    <cge:TPSR_Ref TObjectID="8605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47355" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -301.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6089"/>
     <cge:Term_Ref ObjectID="12126"/>
    <cge:TPSR_Ref TObjectID="6089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47356" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -301.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6089"/>
     <cge:Term_Ref ObjectID="12126"/>
    <cge:TPSR_Ref TObjectID="6089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47352" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -301.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47352" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6089"/>
     <cge:Term_Ref ObjectID="12126"/>
    <cge:TPSR_Ref TObjectID="6089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47361" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -338.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47361" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6090"/>
     <cge:Term_Ref ObjectID="12128"/>
    <cge:TPSR_Ref TObjectID="6090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47362" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -338.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47362" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6090"/>
     <cge:Term_Ref ObjectID="12128"/>
    <cge:TPSR_Ref TObjectID="6090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47358" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -338.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47358" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6090"/>
     <cge:Term_Ref ObjectID="12128"/>
    <cge:TPSR_Ref TObjectID="6090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47339" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.000000 -335.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6082"/>
     <cge:Term_Ref ObjectID="12120"/>
    <cge:TPSR_Ref TObjectID="6082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47335" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.000000 -335.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6082"/>
     <cge:Term_Ref ObjectID="12120"/>
    <cge:TPSR_Ref TObjectID="6082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47368" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5026.000000 -339.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6092"/>
     <cge:Term_Ref ObjectID="12130"/>
    <cge:TPSR_Ref TObjectID="6092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47364" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5026.000000 -339.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47364" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6092"/>
     <cge:Term_Ref ObjectID="12130"/>
    <cge:TPSR_Ref TObjectID="6092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37817" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3696.000000 -439.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6051"/>
     <cge:Term_Ref ObjectID="8770"/>
    <cge:TPSR_Ref TObjectID="6051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37820" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -439.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6052"/>
     <cge:Term_Ref ObjectID="8771"/>
    <cge:TPSR_Ref TObjectID="6052"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3247" y="-1171"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3198" y="-1188"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4102" cy="-749" fill="none" fillStyle="0" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4710" cy="-750" fill="none" fillStyle="0" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_XL.CX_XL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="12106"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4033.000000 -687.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4033.000000 -687.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6094" ObjectName="TF-CX_XL.CX_XL_1T"/>
    <cge:TPSR_Ref TObjectID="6094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XL.CX_XL_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="12110"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4641.000000 -688.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4641.000000 -688.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6095" ObjectName="TF-CX_XL.CX_XL_2T"/>
    <cge:TPSR_Ref TObjectID="6095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5185.000000 -197.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5185.000000 -197.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3552.000000 -196.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3552.000000 -196.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3247" y="-1171"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3247" y="-1171"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3198" y="-1188"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3198" y="-1188"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47404">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8605" ObjectName="SW-CX_XL.CX_XL_012BK"/>
     <cge:Meas_Ref ObjectId="47404"/>
    <cge:TPSR_Ref TObjectID="8605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 -311.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6082" ObjectName="SW-CX_XL.CX_XL_032BK"/>
     <cge:Meas_Ref ObjectId="47429"/>
    <cge:TPSR_Ref TObjectID="6082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47457">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -304.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6087" ObjectName="SW-CX_XL.CX_XL_034BK"/>
     <cge:Meas_Ref ObjectId="47457"/>
    <cge:TPSR_Ref TObjectID="6087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47444">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -307.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6085" ObjectName="SW-CX_XL.CX_XL_033BK"/>
     <cge:Meas_Ref ObjectId="47444"/>
    <cge:TPSR_Ref TObjectID="6085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -304.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6089" ObjectName="SW-CX_XL.CX_XL_035BK"/>
     <cge:Meas_Ref ObjectId="47472"/>
    <cge:TPSR_Ref TObjectID="6089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47486">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4761.000000 -307.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6090" ObjectName="SW-CX_XL.CX_XL_036BK"/>
     <cge:Meas_Ref ObjectId="47486"/>
    <cge:TPSR_Ref TObjectID="6090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4481.000000 -21.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47375">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -495.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6057" ObjectName="SW-CX_XL.CX_XL_001BK"/>
     <cge:Meas_Ref ObjectId="47375"/>
    <cge:TPSR_Ref TObjectID="6057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47390">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6063" ObjectName="SW-CX_XL.CX_XL_002BK"/>
     <cge:Meas_Ref ObjectId="47390"/>
    <cge:TPSR_Ref TObjectID="6063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37886">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 -844.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6053" ObjectName="SW-CX_XL.CX_XL_101BK"/>
     <cge:Meas_Ref ObjectId="37886"/>
    <cge:TPSR_Ref TObjectID="6053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37927">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -845.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6059" ObjectName="SW-CX_XL.CX_XL_102BK"/>
     <cge:Meas_Ref ObjectId="37927"/>
    <cge:TPSR_Ref TObjectID="6059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38002">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -1021.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6075" ObjectName="SW-CX_XL.CX_XL_131BK"/>
     <cge:Meas_Ref ObjectId="38002"/>
    <cge:TPSR_Ref TObjectID="6075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47501">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5063.000000 -306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6092" ObjectName="SW-CX_XL.CX_XL_037BK"/>
     <cge:Meas_Ref ObjectId="47501"/>
    <cge:TPSR_Ref TObjectID="6092"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3235.000000 -1112.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-37761" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4292.000000 -741.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37761" ObjectName="CX_XL:CX_XL_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-37825" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -726.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37825" ObjectName="CX_XL:CX_XL_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-37824" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.000000 -726.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37824" ObjectName="CX_XL:CX_XL_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-37786" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -741.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37786" ObjectName="CX_XL:CX_XL_2T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62654" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3245.538462 -1013.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62654" ObjectName="CX_XL:CX_XL_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-61622" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3245.538462 -971.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="61622" ObjectName="CX_XL:CX_XL_sumQ"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XL"/>
</svg>