<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-189" aopId="3940866" id="thSvg" product="E8000V2" version="1.0" viewBox="498 -934 2208 1202">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="28" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape34">
    <ellipse cx="15" cy="26" fillStyle="0" rx="6" ry="6.5" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="35" x2="37" y1="28" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="34" x2="35" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="35" x2="35" y1="13" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="35" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="35" x2="35" y1="32" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="30" x2="40" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="33" x2="38" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="34" x2="37" y1="46" y2="46"/>
    <rect height="14" stroke-width="0.571429" width="6" x="33" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="15" y1="19" y2="5"/>
    <circle cx="7" cy="30" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <circle cx="14" cy="34" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <circle cx="21" cy="29" fillStyle="0" r="6.5" stroke-width="0.45993"/>
   </symbol>
   <symbol id="lightningRod:shape35">
    <circle cx="15" cy="21" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="36" x2="37" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="35" x2="36" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="36" x2="36" y1="34" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="36" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="36" x2="36" y1="16" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="31" x2="41" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="34" x2="38" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="35" x2="37" y1="2" y2="2"/>
    <rect height="14" stroke-width="0.571429" width="6" x="33" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="15" y1="28" y2="43"/>
    <circle cx="7" cy="17" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <ellipse cx="14" cy="13" fillStyle="0" rx="6.5" ry="6" stroke-width="0.45993"/>
    <ellipse cx="21" cy="18" fillStyle="0" rx="6.5" ry="6" stroke-width="0.45993"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape16_0">
    <circle cx="29" cy="25" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="23" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="23" x2="15" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="15" x2="23" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape16_1">
    <ellipse cx="60" cy="25" fillStyle="0" rx="24.5" ry="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="74" x2="66" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="66" x2="58" y1="25" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="58" x2="66" y1="17" y2="25"/>
   </symbol>
   <symbol id="transformer2:shape77_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 6,61 6,32 " stroke-width="1"/>
    <circle cx="31" cy="64" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="19" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="31"/>
    <polyline DF8003:Layer="PUBLIC" points="31,18 25,31 37,31 31,18 31,19 31,18 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape77_1">
    <circle cx="31" cy="86" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="90" y2="90"/>
   </symbol>
   <symbol id="transformer2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="51" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="55" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape18_1">
    <circle cx="30" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="33" y1="76" y2="74"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,76 24,78 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,87 34,87 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="87"/>
   </symbol>
   <symbol id="voltageTransformer:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b7ce40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b7d7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b7e190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b7f0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b80370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b80f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b81b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b82560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b82dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b837b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b843a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1b84d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b86940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b87550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b87f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b88810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b8a000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b8ad00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b8b5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b8bfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b8d190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b8db10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b8e600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b93980" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b90310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b916d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b924b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b956d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b96c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1212" width="2218" x="493" y="-939"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-658 1108,-671 1120,-671 1114,-658 1114,-659 1114,-658 " stroke="rgb(60,120,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2500.633028 -674.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 -675.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141476">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 80.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25267" ObjectName="SW-YR_LTS.YR_LTS_323BK"/>
     <cge:Meas_Ref ObjectId="141476"/>
    <cge:TPSR_Ref TObjectID="25267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 -696.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.000000 -707.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24965" ObjectName="SW-YR_ZH.YR_ZH_391BK"/>
     <cge:Meas_Ref ObjectId="138802"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2133.000000 -702.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24971" ObjectName="SW-YR_ZH.YR_ZH_392BK"/>
     <cge:Meas_Ref ObjectId="138856"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1962.000000 -398.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25119" ObjectName="SW-YR_TPL.YR_TPL_381BK"/>
     <cge:Meas_Ref ObjectId="140279"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25124" ObjectName="SW-YR_TPL.YR_TPL_382BK"/>
     <cge:Meas_Ref ObjectId="140331"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141453">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 95.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25260" ObjectName="SW-YR_LTS.YR_LTS_322BK"/>
     <cge:Meas_Ref ObjectId="141453"/>
    <cge:TPSR_Ref TObjectID="25260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -389.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25128" ObjectName="SW-YR_TPL.YR_TPL_383BK"/>
     <cge:Meas_Ref ObjectId="140382"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -84.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -697.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.976744 1502.000000 -70.813953)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 -698.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 87.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25256" ObjectName="SW-YR_LTS.YR_LTS_321BK"/>
     <cge:Meas_Ref ObjectId="141435"/>
    <cge:TPSR_Ref TObjectID="25256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2092.000000 -76.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1116.000000 200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1035.000000 91.360657)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1154.000000 82.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140978">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1236.000000 -268.639343)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25191" ObjectName="SW-YR_BB.YR_BB_341BK"/>
     <cge:Meas_Ref ObjectId="140978"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.500000 -573.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183002">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.705882 788.000000 -52.227579)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27848" ObjectName="SW-YR_YJ.YR_YJ_351BK"/>
     <cge:Meas_Ref ObjectId="183002"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183092">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.705882 751.000000 96.411765)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27850" ObjectName="SW-YR_YJ.YR_YJ_353BK"/>
     <cge:Meas_Ref ObjectId="183092"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2547.633028 -268.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_18a42a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2471.633028 -483.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1272,-279 1300,-279 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1272,-279 1300,-279 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.566667 -0.000000 0.000000 -0.560000 2246.000000 -33.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.566667 -0.000000 0.000000 -0.560000 2246.000000 -33.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.566667 -0.560000 -0.000000 1991.500000 -151.500000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -0.566667 -0.560000 -0.000000 1991.500000 -151.500000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.467633 -0.000000 0.000000 -0.460000 1552.956522 -623.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.467633 -0.000000 0.000000 -0.460000 1552.956522 -623.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 947.000000 -757.639343)" xlink:href="#transformer2:shape77_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 947.000000 -757.639343)" xlink:href="#transformer2:shape77_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 730.000000 249.000000)" xlink:href="#transformer2:shape18_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 730.000000 249.000000)" xlink:href="#transformer2:shape18_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1082.000000 -657.639343)" xlink:href="#transformer2:shape77_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1082.000000 -657.639343)" xlink:href="#transformer2:shape77_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_139aae0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2345.000000 -525.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_139b010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2324.000000 -526.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_139b9e0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2411.500000 -79.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_139bfd0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2352.000000 -82.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1323ba0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2351.500000 -37.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13bc110">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1888.000000 -602.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13bcf20">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1950.000000 -620.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1312570">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1949.500000 -599.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a9510">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2031.000000 -361.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a9db0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2017.500000 -340.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_137c140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2004.000000 -258.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_137e460">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1986.000000 -242.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b53e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1888.500000 -23.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b5c60">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1890.000000 -44.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b63a0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1836.000000 -13.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131f1c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1746.000000 -165.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131f900">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1725.500000 -166.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13203e0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1644.000000 -241.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13467a0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1494.500000 -644.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1332c60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.976744 1497.000000 12.209302)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a75e0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -0.976744 1431.500000 -55.651163)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a7e60">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 0.976744 1452.000000 -56.162791)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_152a400">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1332.000000 -621.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d66e0">
    <use class="BV-35KV" transform="matrix(-0.734375 -0.000000 0.000000 -1.000000 1311.265625 -165.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17daad0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.933333 2196.000000 -604.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e8470">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2083.000000 -566.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e8950">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2083.000000 -587.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f40c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2045.000000 -113.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f5330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 -105.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f5e10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1892.000000 -144.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fcf30">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1460.000000 -636.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fdeb0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1555.000000 -541.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1802940">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1612.000000 -542.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1805a10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1683.000000 -607.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1807360">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1293.000000 -560.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a2f60">
    <use class="BV-0KV" transform="matrix(-0.833333 -0.000000 -0.000000 0.536585 1052.000000 -172.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a3460">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 0.650794 1018.000000 -197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181c180">
    <use class="BV-0KV" transform="matrix(0.833333 0.000000 0.000000 -0.536585 1033.750000 -175.960600)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181c7c0">
    <use class="BV-35KV" transform="matrix(-0.833333 -0.000000 -0.000000 0.536585 1170.000000 -174.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181d040">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 0.650794 1129.000000 -199.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181e050">
    <use class="BV-35KV" transform="matrix(0.833333 0.000000 0.000000 -0.536585 1151.750000 -177.960600)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_182d8d0">
    <use class="BV-0KV" transform="matrix(0.734375 -0.000000 0.000000 -1.000000 1034.734375 -11.639343)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_182e2b0">
    <use class="BV-0KV" transform="matrix(0.734375 -0.000000 0.000000 -1.000000 1155.734375 -17.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_182f2c0">
    <use class="BV-0KV" transform="matrix(-0.636364 -0.000000 -0.000000 0.507937 1029.000000 -63.639343)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1830270">
    <use class="BV-0KV" transform="matrix(-0.636364 -0.000000 -0.000000 0.507937 1148.000000 -67.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1833770">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1964.000000 -325.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_184d260">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1173.500000 -333.139343)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_184dae0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1193.000000 -353.639343)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_184e480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -246.639343)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_186d3c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 974.000000 -418.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_186df70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -497.000000)" xlink:href="#lightningRod:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_187a770">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 876.500000 -200.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_187aff0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 891.000000 -221.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_187c680">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 761.000000 -203.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_188f300">
    <use class="BV-35KV" transform="matrix(-0.734375 -0.000000 0.000000 -1.000000 713.265625 144.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1892310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 162.000000)" xlink:href="#lightningRod:shape35"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1893c10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 869.000000 105.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18a3cd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2470.633028 -526.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18a6e30">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2416.000000 -100.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18ac210">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1138.000000 -554.639343)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b23b0">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1119.000000 -537.639343)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b4340">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2621.500000 -424.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b4b60">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2631.000000 -446.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 618.000000 -854.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="188" x="630" y="-914"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="188" x="630" y="-914"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="581" y="-930"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="581" y="-930"/></g>
  </g><g id="MotifButton_Layer">
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="188" x="630" y="-914"/></g>
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="581" y="-930"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(213,0,0)" stroke-width="1" width="360" x="499" y="-933"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1200" stroke="rgb(213,0,0)" stroke-width="1" width="2207" x="499" y="-933"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(213,0,0)" stroke-width="1" width="360" x="499" y="-813"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_LTS.YR_LTS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1262,173 2404,173 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25315" ObjectName="BS-YR_LTS.YR_LTS_3IM"/>
    <cge:TPSR_Ref TObjectID="25315"/></metadata>
   <polyline fill="none" opacity="0" points="1262,173 2404,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_ZH.YR_ZH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2179,-817 1853,-817 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24962" ObjectName="BS-YR_ZH.YR_ZH_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2179,-817 1853,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1751,-816 1223,-816 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1751,-816 1223,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_TPL.YR_TPL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-488 2030,-488 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25116" ObjectName="BS-YR_TPL.YR_TPL_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1605,-488 2030,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_MH.YR_MH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2180,-300 2180,-196 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25019" ObjectName="BS-YR_MH.YR_MH_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2180,-300 2180,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,21 1694,21 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1628,21 1694,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-208 1415,-208 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1540,-208 1415,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1265,-442 1365,-442 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1265,-442 1365,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2179,-135 2179,-35 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2179,-135 2179,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1112,149 1218,149 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1112,149 1218,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,150 1075,150 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="979,150 1075,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_BB.YR_BB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-335 1367,-226 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25188" ObjectName="BS-YR_BB.YR_BB_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1367,-335 1367,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_YJ.YR_YJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-10 876,-10 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24878" ObjectName="BS-YR_YJ.YR_YJ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="732,-10 876,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2330,-818 2539,-818 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2330,-818 2539,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2527,-247 2590,-247 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2527,-247 2590,-247 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13bb7e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1809.000000 -638.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_137b6b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1746.000000 -296.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12fb8f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.409091 2109.000000 -299.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135f880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1561.000000 -318.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135ab00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1758.000000 -130.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_134e8d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1567.000000 -202.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1342ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1556.000000 -55.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a6930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.976744 1405.000000 -21.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_152dda0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1386.000000 -514.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d5cb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1221.000000 -12.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d9980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 2050.000000 -634.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e5c00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1896.000000 7.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f8cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1950.000000 -138.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1850a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1088.000000 -348.639343)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_187b730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 882.000000 -142.639343)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_188c090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.347826 -0.000000 0.000000 -1.583333 636.000000 120.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18a22d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2622.000000 -377.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18a6610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2424.000000 -17.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18c3990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1044.000000 -621.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1318" cy="-442" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1440" cy="-207" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1511" cy="-207" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1660" cy="21" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25188" cx="1367" cy="-279" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25019" cx="2180" cy="-249" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2179" cy="-86" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="2142" cy="-817" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="1900" cy="-817" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="1971" cy="-488" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="1843" cy="-488" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="1660" cy="-488" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="1843" cy="173" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="1318" cy="173" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1025" cy="149" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1144" cy="148" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1125" cy="148" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1061" cy="149" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="761" cy="-10" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="836" cy="-10" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="2362" cy="173" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2557" cy="-247" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2362" cy="-818" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2510" cy="-818" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1475" cy="-207" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1318" cy="-816" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1475" cy="-816" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1674" cy="-816" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24878" cx="798" cy="-10" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="1345" x2="1345" y1="-286" y2="-286"/>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1109.000000 -701.567915)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -761.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -761.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -761.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -761.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -761.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -761.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -761.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_13f0b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 670.000000 -903.500000) translate(0,16)">35kV网络图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1130d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -751.000000) translate(0,16)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1130d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -751.000000) translate(0,36)">兴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1130d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -751.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -471.000000) translate(0,16)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -471.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -471.000000) translate(0,56)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -471.000000) translate(0,76)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -471.000000) translate(0,96)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_10b2c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -300.000000) translate(0,17)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_10b2c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -300.000000) translate(0,38)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_10b2c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -300.000000) translate(0,59)">猛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_10b2c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -300.000000) translate(0,80)">虎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_10b2c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -300.000000) translate(0,101)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_113be00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1865.000000 -276.000000) translate(0,16)">龙他猛线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13dd770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 -768.000000) translate(0,16)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13dd770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 -768.000000) translate(0,36)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13dd770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 -768.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_110ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1936.000000 -458.000000) translate(0,16)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_110ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1936.000000 -458.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_110ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1936.000000 -458.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13d7c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2337.000000 -433.000000) translate(0,16)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13d7c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2337.000000 -433.000000) translate(0,36)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13d7c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2337.000000 -433.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1330160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 -574.000000) translate(0,16)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1330160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 -574.000000) translate(0,36)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1330160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 -574.000000) translate(0,56)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1330160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 -574.000000) translate(0,76)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1330160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 -574.000000) translate(0,96)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,16)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,36)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,56)">盛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,96)">至</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,116)">220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,136)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,156)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,176)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13f6a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -377.000000) translate(0,196)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_12e4bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2515.000000 -556.000000) translate(0,16)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_12e4bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2515.000000 -556.000000) translate(0,36)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_12e4bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2515.000000 -556.000000) translate(0,56)">盛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_12e4bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2515.000000 -556.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12e4d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1340.000000 -843.000000) translate(0,17)">110kV万马变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1361800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -501.000000) translate(0,17)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1361970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -556.000000) translate(0,17)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12e3f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1264.000000 -437.000000) translate(0,17)">35kV永兴变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12e40f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 661.000000 -35.000000) translate(0,17)">35kV宜就变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12c2e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -332.000000) translate(0,17)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12c2e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -332.000000) translate(0,38)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12c2e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -332.000000) translate(0,59)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12c2e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -332.000000) translate(0,80)">别</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12c2e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -332.000000) translate(0,101)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13c0b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1250.000000 -48.000000) translate(0,17)">32167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132cdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1596.000000 -657.000000) translate(0,17)">2号站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132cdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1596.000000 -657.000000) translate(0,38)">用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132d070" transform="matrix(1.000000 -0.000000 -0.000000 0.976744 1520.000000 -174.348837) translate(0,17)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132c2a0" transform="matrix(1.000000 -0.000000 -0.000000 0.976744 1525.000000 -103.953488) translate(0,17)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132c550" transform="matrix(1.000000 -0.000000 -0.000000 0.976744 1519.000000 -53.232558) translate(0,17)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132c6c0" transform="matrix(1.000000 -0.000000 -0.000000 0.976744 1470.000000 19.209302) translate(0,17)">万马电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12e38f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 28.000000) translate(0,17)">红石岩电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12e3ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1665.000000 -173.000000) translate(0,17)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132e640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1670.000000 -116.000000) translate(0,17)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132e7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 -33.000000) translate(0,17)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132e920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.000000 -206.000000) translate(0,17)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132ea90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 -363.000000) translate(0,17)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132ec00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1787.000000 16.000000) translate(0,17)">3226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132eeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1798.000000 62.000000) translate(0,17)">322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_132f020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1789.000000 104.000000) translate(0,17)">3221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a3d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 -337.000000) translate(0,17)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a3ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1771.000000 -514.000000) translate(0,17)">35kV他普里电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2056.000000 -241.000000) translate(0,17)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1770.000000 182.000000) translate(0,17)">35kV龙头山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -421.000000) translate(0,17)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a44f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.000000 -421.000000) translate(0,17)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -431.000000) translate(0,17)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a47d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -463.000000) translate(0,17)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -369.000000) translate(0,17)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.000000 -463.000000) translate(0,17)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.000000 -369.000000) translate(0,17)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1980.000000 -473.000000) translate(0,17)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a4f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1982.000000 -390.000000) translate(0,17)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13a5070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -843.000000) translate(0,17)">35kV中和变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143d250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -672.000000) translate(0,17)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143d460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2073.000000 -673.000000) translate(0,17)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1913.000000 -740.000000) translate(0,17)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143d740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2158.000000 -735.000000) translate(0,17)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 -786.000000) translate(0,17)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143da20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -693.000000) translate(0,17)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143db90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2152.000000 -781.000000) translate(0,17)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143dd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2152.000000 -683.000000) translate(0,17)">3926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_143de70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -53.000000) translate(0,13)">32367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143dfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2305.000000 1.000000) translate(0,17)">3236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143e240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2310.000000 51.000000) translate(0,17)">323</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2304.000000 103.000000) translate(0,17)">3231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143e520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 1.000000) translate(0,17)">3216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 57.000000) translate(0,17)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 100.000000) translate(0,17)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_143e970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1584.000000 -89.000000) translate(0,17)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143eae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1727.000000 -124.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143ec50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -249.000000) translate(0,9)">3619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143ef00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1773.000000 -201.000000) translate(0,9)">36197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -244.000000) translate(0,9)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -244.000000) translate(0,20)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -244.000000) translate(0,31)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -244.000000) translate(0,42)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -244.000000) translate(0,53)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -244.000000) translate(0,64)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_143f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1949.000000 -244.000000) translate(0,75)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_1317780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2242.000000 -29.000000) translate(0,9)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17db9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -843.000000) translate(0,17)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dc380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -730.000000) translate(0,17)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -678.000000) translate(0,17)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dcb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -776.000000) translate(0,17)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dcd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 -729.000000) translate(0,17)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dcf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -677.000000) translate(0,17)">3746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dd440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -775.000000) translate(0,17)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dd680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1683.000000 -728.000000) translate(0,17)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17dd8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 -676.000000) translate(0,17)">3756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17ddb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 -774.000000) translate(0,17)">3751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e25c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2450.000000 -846.000000) translate(0,17)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e2bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -710.000000) translate(0,17)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e2e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2377.000000 -768.000000) translate(0,17)">3632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e3070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2375.000000 -646.000000) translate(0,17)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e6d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 933.000000 -606.000000) translate(0,17)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e7380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 924.000000 -550.000000) translate(0,17)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,16)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,36)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,56)">仁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,76)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,96)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,116)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,136)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,156)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,176)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,196)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e75c0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 1000.971014 -490.000000) translate(0,216)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -761.000000) translate(0,17)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -761.000000) translate(0,38)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -761.000000) translate(0,59)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -761.000000) translate(0,80)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -761.000000) translate(0,101)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -761.000000) translate(0,122)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17e81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -761.000000) translate(0,143)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e9d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 -135.000000) translate(0,16)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e9d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 -135.000000) translate(0,36)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e9d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 -135.000000) translate(0,56)">田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e9d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 -135.000000) translate(0,76)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17e9d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 -135.000000) translate(0,96)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_17f9780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1959.000000 -126.000000) translate(0,9)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17f9db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2098.000000 -117.000000) translate(0,17)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17f9ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1987.000000 -81.000000) translate(0,17)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_17fa230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2060.000000 -69.000000) translate(0,13)">龙他猛线T田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_17fa230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2060.000000 -69.000000) translate(0,29)">房变电站支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_180adb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1668.000000 -294.000000) translate(0,17)">N29</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_180b7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1795.000000 -259.000000) translate(0,17)">N39</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1819440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -274.000000) translate(0,14)">班别T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18253a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.000000 60.000000) translate(0,17)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18259d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1158.000000 51.000000) translate(0,17)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1825c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1029.000000 108.000000) translate(0,17)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1825e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1153.000000 107.000000) translate(0,17)">3542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1826090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 8.000000) translate(0,17)">3523</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18262d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1153.000000 3.000000) translate(0,17)">3543</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1826510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1074.000000 181.000000) translate(0,17)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1826750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 183.000000) translate(0,17)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1826990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 178.000000) translate(0,17)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1826bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 223.000000) translate(0,17)">110kV莲池变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18274c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 949.000000 156.000000) translate(0,17)">35kV I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1827930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1154.000000 153.000000) translate(0,17)">35kV II母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1827b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1033.000000 -219.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1828680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -185.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1828990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1335.000000 36.000000) translate(0,17)">莲</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1828990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1335.000000 36.000000) translate(0,38)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1828990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1335.000000 36.000000) translate(0,59)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_182d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1033.000000 -103.000000) translate(0,17)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_182d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1153.000000 -105.000000) translate(0,17)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1832520" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 940.971014 -783.000000) translate(0,16)">至0.4kV系统</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,42)">仁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,57)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,72)">施</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,87)">工</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,102)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,117)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -516.000000) translate(0,132)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1850440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1024.000000 -336.000000) translate(0,17)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1853de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1319.000000 -309.000000) translate(0,17)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1854410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1182.000000 -311.000000) translate(0,17)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1854650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -310.000000) translate(0,17)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1878000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -179.000000) translate(0,17)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_187d430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -104.000000) translate(0,17)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_188b630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 46.000000) translate(0,17)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_188bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.000000 50.000000) translate(0,17)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_188ecd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 79.000000) translate(0,17)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1899620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2530.000000 -703.000000) translate(0,17)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1899c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2523.000000 -761.000000) translate(0,17)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1899e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2519.000000 -649.000000) translate(0,17)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_189a0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2323.000000 -845.000000) translate(0,17)">110kV永仁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_189f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2526.000000 -239.000000) translate(0,17)">盛源变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18a3220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2569.000000 -299.000000) translate(0,17)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18a3850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2562.000000 -357.000000) translate(0,17)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18a3a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2594.000000 -417.000000) translate(0,17)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18a5bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2436.000000 -479.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18a61e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -220.000000) translate(0,16)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18a61e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -220.000000) translate(0,36)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18a61e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -220.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_18a7470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -86.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18a8700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2029.000000 -564.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18a8b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1941.000000 -593.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18a8d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2040.000000 -383.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18aac60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -529.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18ab240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -533.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2112.000000 -480.000000) translate(0,16)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2112.000000 -480.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2112.000000 -480.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1810.000000 -205.000000) translate(0,16)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1810.000000 -205.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1810.000000 -205.000000) translate(0,56)">猛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1810.000000 -205.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1801.000000 -465.000000) translate(0,16)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1801.000000 -465.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1801.000000 -465.000000) translate(0,56)">猛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ab910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1801.000000 -465.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -472.000000) translate(0,16)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -472.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -472.000000) translate(0,56)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -472.000000) translate(0,76)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -472.000000) translate(0,96)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 -308.000000) translate(0,16)">万他马红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -776.000000) translate(0,16)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -776.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -776.000000) translate(0,56)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -776.000000) translate(0,76)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18abfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -776.000000) translate(0,96)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18afa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.799862 -725.000000) translate(0,15)">至0.4kV系统</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -624.000000) translate(0,12)">35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -624.000000) translate(0,27)">仁和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -624.000000) translate(0,42)">施工</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18b18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -624.000000) translate(0,57)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bb9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 -766.000000) translate(0,16)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bb9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 -766.000000) translate(0,36)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bb9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 -766.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -771.000000) translate(0,16)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -771.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -771.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2323.000000 -737.000000) translate(0,16)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2323.000000 -737.000000) translate(0,36)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2323.000000 -737.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2475.000000 -747.000000) translate(0,16)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2475.000000 -747.000000) translate(0,36)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2475.000000 -747.000000) translate(0,56)">盛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bc490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2475.000000 -747.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18bc6e0" transform="matrix(1.000000 -0.000000 -0.000000 0.976744 1435.000000 -235.790698) translate(0,17)">万马电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18bdf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 -535.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18be4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 -578.000000) translate(0,17)">万中线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18be710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2048.000000 -299.000000) translate(0,17)">36110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18be950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1869.000000 -208.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18beb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1935.000000 -41.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18bedd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.000000 5.000000) translate(0,17)">32267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bf010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 48.000000) translate(0,16)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bf010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 48.000000) translate(0,36)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bf010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 48.000000) translate(0,56)">猛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18bf010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 48.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18bf260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -234.000000) translate(0,17)">莲龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18bf490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -11.000000) translate(0,17)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_181b4f0" transform="matrix(1.000000 -0.000000 -0.000000 0.976744 1385.000000 -181.348837) translate(0,17)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1385c30" transform="matrix(1.000000 -0.000000 -0.000000 0.976744 1346.000000 -88.348837) translate(0,17)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1385e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1189.000000 -375.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13860b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -271.000000) translate(0,17)">N89</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13862f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -660.000000) translate(0,17)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c6120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 -200.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c6750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 226.000000) translate(0,17)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c8120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -305.000000) translate(0,17)">连宜T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c89a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 26.000000) translate(0,17)">连</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c89a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 26.000000) translate(0,38)">宜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c89a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 26.000000) translate(0,59)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c89a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 26.000000) translate(0,80)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c8c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.000000 34.000000) translate(0,17)">莲</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c8c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.000000 34.000000) translate(0,38)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c8c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.000000 34.000000) translate(0,59)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c8e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2624.000000 -470.000000) translate(0,17)">线路TV</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2500.633028 -612.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2500.633028 -732.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 -613.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 -733.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141473">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2375.000000 -21.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25266" ObjectName="SW-YR_LTS.YR_LTS_32367SW"/>
     <cge:Meas_Ref ObjectId="141473"/>
    <cge:TPSR_Ref TObjectID="25266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25264" ObjectName="SW-YR_LTS.YR_LTS_3231SW"/>
     <cge:Meas_Ref ObjectId="141471"/>
    <cge:TPSR_Ref TObjectID="25264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141472">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 31.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25265" ObjectName="SW-YR_LTS.YR_LTS_3236SW"/>
     <cge:Meas_Ref ObjectId="141472"/>
    <cge:TPSR_Ref TObjectID="25265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138805">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.000000 -661.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24967" ObjectName="SW-YR_ZH.YR_ZH_3916SW"/>
     <cge:Meas_Ref ObjectId="138805"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.000000 -752.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24966" ObjectName="SW-YR_ZH.YR_ZH_3911SW"/>
     <cge:Meas_Ref ObjectId="138804"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138808">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1841.000000 -642.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24970" ObjectName="SW-YR_ZH.YR_ZH_39167SW"/>
     <cge:Meas_Ref ObjectId="138808"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2133.000000 -747.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24972" ObjectName="SW-YR_ZH.YR_ZH_3921SW"/>
     <cge:Meas_Ref ObjectId="138858"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2133.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24973" ObjectName="SW-YR_ZH.YR_ZH_3926SW"/>
     <cge:Meas_Ref ObjectId="138859"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1962.000000 -438.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25120" ObjectName="SW-YR_TPL.YR_TPL_3811SW"/>
     <cge:Meas_Ref ObjectId="140281"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1962.000000 -356.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25121" ObjectName="SW-YR_TPL.YR_TPL_3816SW"/>
     <cge:Meas_Ref ObjectId="140282"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 -432.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25125" ObjectName="SW-YR_TPL.YR_TPL_3821SW"/>
     <cge:Meas_Ref ObjectId="140333"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 -334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25126" ObjectName="SW-YR_TPL.YR_TPL_3826SW"/>
     <cge:Meas_Ref ObjectId="140334"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -300.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25127" ObjectName="SW-YR_TPL.YR_TPL_38267SW"/>
     <cge:Meas_Ref ObjectId="140335"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139440">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2109.000000 -255.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25023" ObjectName="SW-YR_MH.YR_MH_36110SW"/>
     <cge:Meas_Ref ObjectId="139440"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2049.000000 -244.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25022" ObjectName="SW-YR_MH.YR_MH_3611SW"/>
     <cge:Meas_Ref ObjectId="139439"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25261" ObjectName="SW-YR_LTS.YR_LTS_3221SW"/>
     <cge:Meas_Ref ObjectId="141454"/>
    <cge:TPSR_Ref TObjectID="25261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 52.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25262" ObjectName="SW-YR_LTS.YR_LTS_3226SW"/>
     <cge:Meas_Ref ObjectId="141455"/>
    <cge:TPSR_Ref TObjectID="25262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25130" ObjectName="SW-YR_TPL.YR_TPL_3836SW"/>
     <cge:Meas_Ref ObjectId="140385"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1593.000000 -322.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25131" ObjectName="SW-YR_TPL.YR_TPL_38367SW"/>
     <cge:Meas_Ref ObjectId="140386"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1759.000000 -174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 -225.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -206.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -135.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 2.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1588.000000 -59.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -742.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -644.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.976744 1502.000000 -140.162791)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.976744 1502.000000 -19.046512)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.976744 1431.000000 -141.139535)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.976744 1406.000000 -54.209302)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 -743.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 -645.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1332.000000 -518.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141436">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25257" ObjectName="SW-YR_LTS.YR_LTS_3211SW"/>
     <cge:Meas_Ref ObjectId="141436"/>
    <cge:TPSR_Ref TObjectID="25257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 36.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25258" ObjectName="SW-YR_LTS.YR_LTS_3216SW"/>
     <cge:Meas_Ref ObjectId="141437"/>
    <cge:TPSR_Ref TObjectID="25258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.000000 -17.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25259" ObjectName="SW-YR_LTS.YR_LTS_32167SW"/>
     <cge:Meas_Ref ObjectId="141438"/>
    <cge:TPSR_Ref TObjectID="25259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2082.000000 -638.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24976" ObjectName="SW-YR_ZH.YR_ZH_39267SW"/>
     <cge:Meas_Ref ObjectId="138862"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 -643.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 -741.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1846.000000 3.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25263" ObjectName="SW-YR_LTS.YR_LTS_32267SW"/>
     <cge:Meas_Ref ObjectId="141456"/>
    <cge:TPSR_Ref TObjectID="25263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2063.000000 -77.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2137.000000 -77.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1987.000000 -81.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1947.000000 -93.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1537.000000 -541.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1594.000000 -542.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1275.000000 -560.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1066.000000 158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1130.000000 160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1030.000000 42.360657)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1030.000000 140.360657)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1149.000000 34.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1149.000000 134.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1030.000000 -73.639343)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1149.000000 -77.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25129" ObjectName="SW-YR_TPL.YR_TPL_3831SW"/>
     <cge:Meas_Ref ObjectId="140384"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140981">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.000000 -293.639343)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25194" ObjectName="SW-YR_BB.YR_BB_34167SW"/>
     <cge:Meas_Ref ObjectId="140981"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140979">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1295.000000 -273.639343)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25192" ObjectName="SW-YR_BB.YR_BB_3411SW"/>
     <cge:Meas_Ref ObjectId="140979"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140980">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1176.000000 -273.639343)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25193" ObjectName="SW-YR_BB.YR_BB_3416SW"/>
     <cge:Meas_Ref ObjectId="140980"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.500000 -521.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.000000 -146.639343)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27849" ObjectName="SW-YR_YJ.YR_YJ_35167SW"/>
     <cge:Meas_Ref ObjectId="183006"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138151">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.645570 826.000000 83.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24884" ObjectName="SW-YR_YJ.YR_YJ_3901SW"/>
     <cge:Meas_Ref ObjectId="138151"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.000000 115.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27851" ObjectName="SW-YR_YJ.YR_YJ_3536SW"/>
     <cge:Meas_Ref ObjectId="183096"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2547.633028 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2568.000000 -381.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -625.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_1374370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-653 2510,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-653 2510,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1374560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-709 2510,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-709 2510,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_139a700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-738 2362,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-738 2362,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_139a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-683 2362,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-683 2362,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_139b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2333,-531 2333,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_139b010@0" ObjectIDZND0="g_139aae0@0" Pin0InfoVect0LinkObjId="g_139aae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_139b010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2333,-531 2333,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_139b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2406,-88 2421,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_139b9e0@0" ObjectIDZND0="g_18a6e30@0" Pin0InfoVect0LinkObjId="g_18a6e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_139b9e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2406,-88 2421,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1362de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,71 2362,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25267@0" ObjectIDZND0="25264@1" Pin0InfoVect0LinkObjId="SW-141471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,71 2362,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1362fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,26 2362,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25265@0" ObjectIDZND0="25267@1" Pin0InfoVect0LinkObjId="SW-141476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,26 2362,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1364800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,-704 1674,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1674,-704 1674,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13649f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,-746 1674,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1674,-746 1674,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1352c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-702 1900,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24967@1" ObjectIDZND0="24965@0" Pin0InfoVect0LinkObjId="SW-138802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138805_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-702 1900,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1352e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-742 1900,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24965@1" ObjectIDZND0="24966@0" Pin0InfoVect0LinkObjId="SW-138804_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-742 1900,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13bb3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-647 1882,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24967@x" ObjectIDND1="g_13bc110@0" ObjectIDND2="g_1312570@0" ObjectIDZND0="24970@1" Pin0InfoVect0LinkObjId="SW-138808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138805_0" Pin1InfoVect1LinkObjId="g_13bc110_0" Pin1InfoVect2LinkObjId="g_1312570_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-647 1882,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13bb5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1846,-647 1832,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24970@0" ObjectIDZND0="g_13bb7e0@0" Pin0InfoVect0LinkObjId="g_13bb7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1846,-647 1832,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13bcd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1884,-608 1900,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_13bc110@0" ObjectIDZND0="24970@x" ObjectIDZND1="24967@x" ObjectIDZND2="g_1312570@0" Pin0InfoVect0LinkObjId="SW-138808_0" Pin0InfoVect1LinkObjId="SW-138805_0" Pin0InfoVect2LinkObjId="g_1312570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13bc110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1884,-608 1900,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1312350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1944,-608 1955,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1312570@0" ObjectIDZND0="g_13bcf20@0" Pin0InfoVect0LinkObjId="g_13bcf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1312570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1944,-608 1955,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1312d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-608 1913,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_13bc110@0" ObjectIDND1="24970@x" ObjectIDND2="24967@x" ObjectIDZND0="g_1312570@1" Pin0InfoVect0LinkObjId="g_1312570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13bc110_0" Pin1InfoVect1LinkObjId="SW-138808_0" Pin1InfoVect2LinkObjId="SW-138805_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-608 1913,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a9050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-710 2142,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24971@0" ObjectIDZND0="24973@1" Pin0InfoVect0LinkObjId="SW-138859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-710 2142,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a92b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-752 2142,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24972@0" ObjectIDZND0="24971@1" Pin0InfoVect0LinkObjId="SW-138856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-752 2142,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13a9b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2012,-349 2036,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13a9db0@0" ObjectIDZND0="g_13a9510@0" Pin0InfoVect0LinkObjId="g_13a9510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13a9db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2012,-349 2036,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_137b450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1784,-305 1769,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25127@0" ObjectIDZND0="g_137b6b0@0" Pin0InfoVect0LinkObjId="g_137b6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1784,-305 1769,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_137e240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1977,-206 1977,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_137e460@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_137e460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1977,-206 1977,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12f8c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1977,-249 1977,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25127@x" ObjectIDND1="25126@x" ObjectIDND2="g_13b53e0@0" ObjectIDZND0="g_137e460@0" Pin0InfoVect0LinkObjId="g_137e460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="SW-140334_0" Pin1InfoVect2LinkObjId="g_13b53e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1977,-249 1977,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12fb690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2118,-296 2118,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25023@1" ObjectIDZND0="g_12fb8f0@0" Pin0InfoVect0LinkObjId="g_12fb8f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2118,-296 2118,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12cf340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1832,-19 1843,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_13b63a0@0" ObjectIDZND0="25262@x" ObjectIDZND1="25263@x" ObjectIDZND2="g_13b53e0@0" Pin0InfoVect0LinkObjId="SW-141455_0" Pin0InfoVect1LinkObjId="SW-141456_0" Pin0InfoVect2LinkObjId="g_13b53e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1832,-19 1843,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a20f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,47 1843,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25262@0" ObjectIDZND0="25260@1" Pin0InfoVect0LinkObjId="SW-141453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141455_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,47 1843,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a2350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,86 1843,98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25260@0" ObjectIDZND0="25261@1" Pin0InfoVect0LinkObjId="SW-141454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,86 1843,98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_135f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1598,-327 1584,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25131@0" ObjectIDZND0="g_135f880@0" Pin0InfoVect0LinkObjId="g_135f880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1598,-327 1584,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131ef60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1661,-230 1680,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_13203e0@0" ObjectIDND1="0@x" ObjectIDND2="g_13467a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13203e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_13467a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1661,-230 1680,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1320180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1734,-171 1734,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_131f900@0" ObjectIDZND0="g_131f1c0@0" Pin0InfoVect0LinkObjId="g_131f1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_131f900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1734,-171 1734,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_134e410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-211 1640,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_13203e0@0" ObjectIDND1="0@x" ObjectIDND2="g_13467a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13203e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_13467a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-211 1640,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_134e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1604,-211 1590,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_134e8d0@0" Pin0InfoVect0LinkObjId="g_134e8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1604,-211 1590,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1299cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-140 1660,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-140 1660,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_133fec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-3 1660,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-3 1660,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13425f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-64 1629,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-64 1629,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1342850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1593,-64 1579,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1342ab0@0" Pin0InfoVect0LinkObjId="g_1342ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1593,-64 1579,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12dcee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-85 1660,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-85 1660,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12dd140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-64 1660,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-64 1660,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1346080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-705 1475,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-705 1475,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13462e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-747 1475,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-747 1475,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1346540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,-635 1555,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_13467a0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13467a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1530,-635 1555,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1332540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,-145 1511,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1511,-145 1511,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13327a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,-79 1511,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1511,-79 1511,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1332a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,-24 1511,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1332c60@0" Pin0InfoVect0LinkObjId="g_1332c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1511,-24 1511,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12a7380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1415,-59 1415,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_12a6930@0" Pin0InfoVect0LinkObjId="g_12a6930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1415,-59 1415,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12a85a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-61 1440,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12a75e0@0" ObjectIDZND0="g_12a7e60@0" Pin0InfoVect0LinkObjId="g_12a7e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12a75e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-61 1440,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1529ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-706 1318,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-706 1318,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1529f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-748 1318,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-748 1318,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152a1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-627 1336,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_152a400@0" Pin0InfoVect0LinkObjId="g_152a400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-627 1336,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152b1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-650 1318,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_152a400@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_152a400_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-650 1318,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-523 1337,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_152a400@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_152a400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-523 1337,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_152db40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-523 1393,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_152dda0@0" Pin0InfoVect0LinkObjId="g_152dda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-523 1393,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17cc950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-474 1318,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-474 1318,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17d3240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,51 1318,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25256@1" ObjectIDZND0="25258@0" Pin0InfoVect0LinkObjId="SW-141437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,51 1318,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17d34a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,98 1318,78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25257@1" ObjectIDZND0="25256@0" Pin0InfoVect0LinkObjId="SW-141435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,98 1318,78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17d5a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-22 1244,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25259@0" ObjectIDZND0="g_17d5cb0@0" Pin0InfoVect0LinkObjId="g_17d5cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-22 1244,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17d9720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-643 2123,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24973@x" ObjectIDND1="g_17daad0@0" ObjectIDND2="g_17e8950@0" ObjectIDZND0="24976@1" Pin0InfoVect0LinkObjId="SW-138862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138859_0" Pin1InfoVect1LinkObjId="g_17daad0_0" Pin1InfoVect2LinkObjId="g_17e8950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-643 2123,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17da3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2087,-643 2073,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24976@0" ObjectIDZND0="g_17d9980@0" Pin0InfoVect0LinkObjId="g_17d9980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2087,-643 2073,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17da610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-654 2142,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24973@0" ObjectIDZND0="24976@x" ObjectIDZND1="g_17daad0@0" ObjectIDZND2="g_17e8950@0" Pin0InfoVect0LinkObjId="SW-138862_0" Pin0InfoVect1LinkObjId="g_17daad0_0" Pin0InfoVect2LinkObjId="g_17e8950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-654 2142,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17da870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-612 2200,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24976@x" ObjectIDND1="24973@x" ObjectIDND2="g_17e8950@0" ObjectIDZND0="g_17daad0@0" Pin0InfoVect0LinkObjId="g_17daad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138862_0" Pin1InfoVect1LinkObjId="SW-138859_0" Pin1InfoVect2LinkObjId="g_17e8950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-612 2200,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17db780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-643 2142,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24976@x" ObjectIDND1="24973@x" ObjectIDZND0="g_17daad0@0" ObjectIDZND1="g_17e8950@0" ObjectIDZND2="g_1833770@0" Pin0InfoVect0LinkObjId="g_17daad0_0" Pin0InfoVect1LinkObjId="g_17e8950_0" Pin0InfoVect2LinkObjId="g_1833770_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138862_0" Pin1InfoVect1LinkObjId="SW-138859_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-643 2142,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e5280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-19 1843,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_13b63a0@0" ObjectIDND1="g_13b53e0@0" ObjectIDND2="25127@x" ObjectIDZND0="25262@x" ObjectIDZND1="25263@x" Pin0InfoVect0LinkObjId="SW-141455_0" Pin0InfoVect1LinkObjId="SW-141456_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13b63a0_0" Pin1InfoVect1LinkObjId="g_13b53e0_0" Pin1InfoVect2LinkObjId="SW-140335_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-19 1843,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e54e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-2 1843,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_13b63a0@0" ObjectIDND1="g_13b53e0@0" ObjectIDND2="25127@x" ObjectIDZND0="25262@1" Pin0InfoVect0LinkObjId="SW-141455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13b63a0_0" Pin1InfoVect1LinkObjId="g_13b53e0_0" Pin1InfoVect2LinkObjId="SW-140335_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-2 1843,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e5740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-2 1851,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_13b63a0@0" ObjectIDND1="g_13b53e0@0" ObjectIDND2="25127@x" ObjectIDZND0="25263@0" Pin0InfoVect0LinkObjId="SW-141456_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13b63a0_0" Pin1InfoVect1LinkObjId="g_13b53e0_0" Pin1InfoVect2LinkObjId="SW-140335_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-2 1851,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e59a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1887,-2 1903,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25263@1" ObjectIDZND0="g_17e5c00@0" Pin0InfoVect0LinkObjId="g_17e5c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1887,-2 1903,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e6630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-32 1843,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13b53e0@0" ObjectIDND1="25127@x" ObjectIDND2="25126@x" ObjectIDZND0="g_13b63a0@0" ObjectIDZND1="25262@x" ObjectIDZND2="25263@x" Pin0InfoVect0LinkObjId="g_13b63a0_0" Pin0InfoVect1LinkObjId="SW-141455_0" Pin0InfoVect2LinkObjId="SW-141456_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13b53e0_0" Pin1InfoVect1LinkObjId="SW-140335_0" Pin1InfoVect2LinkObjId="SW-140334_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-32 1843,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e6890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-32 1852,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_13b63a0@0" ObjectIDND1="25262@x" ObjectIDND2="25263@x" ObjectIDZND0="g_13b53e0@1" Pin0InfoVect0LinkObjId="g_13b53e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13b63a0_0" Pin1InfoVect1LinkObjId="SW-141455_0" Pin1InfoVect2LinkObjId="SW-141456_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-32 1852,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e6af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1883,-32 1895,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13b53e0@0" ObjectIDZND0="g_13b5c60@0" Pin0InfoVect0LinkObjId="g_13b5c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b53e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1883,-32 1895,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,-578 2088,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_17e8470@0" ObjectIDZND0="g_17e8950@0" Pin0InfoVect0LinkObjId="g_17e8950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17e8470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2078,-578 2088,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e91b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1977,-249 1843,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_137e460@0" ObjectIDND1="g_137c140@0" ObjectIDND2="25022@x" ObjectIDZND0="25127@x" ObjectIDZND1="25126@x" ObjectIDZND2="g_13b53e0@0" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="SW-140334_0" Pin0InfoVect2LinkObjId="g_13b53e0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_137e460_0" Pin1InfoVect1LinkObjId="g_137c140_0" Pin1InfoVect2LinkObjId="SW-139439_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1977,-249 1843,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e9410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1499,-635 1475,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_13467a0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_17fcf30@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_17fcf30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13467a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-635 1475,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e9670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,-327 1660,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25131@1" ObjectIDZND0="25130@x" ObjectIDZND1="0@x" ObjectIDZND2="g_13467a0@0" Pin0InfoVect0LinkObjId="SW-140385_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_13467a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1634,-327 1660,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e98d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-327 1660,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25131@x" ObjectIDND1="0@x" ObjectIDND2="g_13467a0@0" ObjectIDZND0="25130@0" Pin0InfoVect0LinkObjId="SW-140385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_13467a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-327 1660,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f4e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2333,-562 2333,-579 2362,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_139b010@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1323ba0@0" ObjectIDZND2="25266@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1323ba0_0" Pin0InfoVect2LinkObjId="SW-141473_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_139b010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2333,-562 2333,-579 2362,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f50d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-579 2362,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_139b010@0" ObjectIDND1="g_1323ba0@0" ObjectIDND2="25266@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_139b010_0" Pin1InfoVect1LinkObjId="g_1323ba0_0" Pin1InfoVect2LinkObjId="SW-141473_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-579 2362,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f5bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-141 1904,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_17f5330@1" ObjectIDZND0="g_17f5e10@0" Pin0InfoVect0LinkObjId="g_17f5e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f5330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-141 1904,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fc6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2141,-86 2128,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2141,-86 2128,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fc8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2101,-86 2084,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2101,-86 2084,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17fcad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-110 1904,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_17f5330@0" ObjectIDZND0="25127@x" ObjectIDZND1="25126@x" ObjectIDZND2="g_137e460@0" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="SW-140334_0" Pin0InfoVect2LinkObjId="g_137e460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f5330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-110 1904,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fcd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1956,-134 1956,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_17f8cf0@0" Pin0InfoVect0LinkObjId="g_17f8cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1956,-134 1956,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17fd9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1456,-642 1475,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_17fcf30@0" ObjectIDZND0="0@x" ObjectIDZND1="g_13467a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13467a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17fcf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1456,-642 1475,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fdc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1543,-547 1543,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_17fdeb0@0" Pin0InfoVect0LinkObjId="g_17fdeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1543,-547 1543,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1800f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-649 1475,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_17fcf30@0" ObjectIDZND1="g_13467a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_17fcf30_0" Pin0InfoVect1LinkObjId="g_13467a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-649 1475,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18011e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-642 1475,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_17fcf30@0" ObjectIDZND0="g_13467a0@0" ObjectIDZND1="0@x" ObjectIDZND2="25131@x" Pin0InfoVect0LinkObjId="g_13467a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-140386_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_17fcf30_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-642 1475,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18026e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-548 1600,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1802940@0" Pin0InfoVect0LinkObjId="g_1802940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-548 1600,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1806780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-647 1900,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24970@x" ObjectIDND1="g_13bc110@0" ObjectIDND2="g_1312570@0" ObjectIDZND0="24967@0" Pin0InfoVect0LinkObjId="SW-138805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138808_0" Pin1InfoVect1LinkObjId="g_13bc110_0" Pin1InfoVect2LinkObjId="g_1312570_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-647 1900,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18069e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-608 1900,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_13bc110@0" ObjectIDND1="g_1312570@0" ObjectIDND2="0@x" ObjectIDZND0="24970@x" ObjectIDZND1="24967@x" Pin0InfoVect0LinkObjId="SW-138808_0" Pin0InfoVect1LinkObjId="SW-138805_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13bc110_0" Pin1InfoVect1LinkObjId="g_1312570_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-608 1900,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1806c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1687,-615 1674,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1805a10@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_13bc110@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_13bc110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1805a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1687,-615 1674,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1806ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,-648 1674,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1805a10@0" ObjectIDZND1="0@x" ObjectIDZND2="g_13bc110@0" Pin0InfoVect0LinkObjId="g_1805a10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_13bc110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1674,-648 1674,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1807100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1281,-566 1281,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1807360@0" Pin0InfoVect0LinkObjId="g_1807360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1281,-566 1281,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1807aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-610 1280,-618 1318,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_152a400@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_152a400_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-610 1280,-618 1318,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_180a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-627 1318,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_152a400@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_152a400_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-627 1318,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_180a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-523 1318,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_152a400@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_152a400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-523 1318,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_180ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-618 1318,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_152a400@0" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_152a400_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-618 1318,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1812370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,199 1061,209 1080,209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,199 1061,209 1080,209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18125d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,209 1125,209 1125,201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1107,209 1125,209 1125,201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18145e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,99 1025,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,99 1025,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1816d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,55 1025,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,55 1025,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_181bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-178 1043,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_181c180@0" ObjectIDZND0="g_13a2f60@0" Pin0InfoVect0LinkObjId="g_13a2f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_181c180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-178 1043,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181ddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1161,-180 1161,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_181e050@0" ObjectIDZND0="g_181c7c0@0" Pin0InfoVect0LinkObjId="g_181c7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_181e050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1161,-180 1161,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1824ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,93 1144,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,93 1144,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1825140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,46 1144,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,46 1144,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_182f060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,-23 1144,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_182e2b0@0" ObjectIDZND0="g_1830270@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1830270_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_182e2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1159,-23 1144,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1830010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-61 1025,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_182f2c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_182f2c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-61 1025,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1830fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-64 1144,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1830270@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1830270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-64 1144,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1831220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-149 1144,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_181c7c0@0" ObjectIDND1="g_181d040@0" ObjectIDND2="25259@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_181c7c0_0" Pin1InfoVect1LinkObjId="g_181d040_0" Pin1InfoVect2LinkObjId="SW-141438_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-149 1144,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1831480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-424 1660,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25128@1" ObjectIDZND0="25129@0" Pin0InfoVect0LinkObjId="SW-140384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-424 1660,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18316e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-377 1660,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25130@1" ObjectIDZND0="25128@0" Pin0InfoVect0LinkObjId="SW-140382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-377 1660,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1831940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-375 1843,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25126@1" ObjectIDZND0="25124@0" Pin0InfoVect0LinkObjId="SW-140331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-375 1843,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1831ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-422 1843,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25124@1" ObjectIDZND0="25125@0" Pin0InfoVect0LinkObjId="SW-140333_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-422 1843,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1831e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1820,-305 1843,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25127@1" ObjectIDZND0="g_137e460@0" ObjectIDZND1="g_137c140@0" ObjectIDZND2="25022@x" Pin0InfoVect0LinkObjId="g_137e460_0" Pin0InfoVect1LinkObjId="g_137c140_0" Pin0InfoVect2LinkObjId="SW-139439_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1820,-305 1843,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1832060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-443 1971,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25120@0" ObjectIDZND0="25119@1" Pin0InfoVect0LinkObjId="SW-140279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-443 1971,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18322c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-397 1971,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25121@1" ObjectIDZND0="25119@0" Pin0InfoVect0LinkObjId="SW-140279_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-397 1971,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18341e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1960,-331 1971,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1833770@0" ObjectIDZND0="g_17daad0@0" ObjectIDZND1="24976@x" ObjectIDZND2="24973@x" Pin0InfoVect0LinkObjId="g_17daad0_0" Pin0InfoVect1LinkObjId="SW-138862_0" Pin0InfoVect2LinkObjId="SW-138859_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1833770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1960,-331 1971,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1834440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-578 2142,-331 1971,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_17daad0@0" ObjectIDND1="24976@x" ObjectIDND2="24973@x" ObjectIDZND0="g_1833770@0" ObjectIDZND1="g_13a9db0@0" ObjectIDZND2="25121@x" Pin0InfoVect0LinkObjId="g_1833770_0" Pin0InfoVect1LinkObjId="g_13a9db0_0" Pin0InfoVect2LinkObjId="SW-140282_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17daad0_0" Pin1InfoVect1LinkObjId="SW-138862_0" Pin1InfoVect2LinkObjId="SW-138859_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-578 2142,-331 1971,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18346a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-249 1843,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_137e460@0" ObjectIDND1="g_137c140@0" ObjectIDND2="25022@x" ObjectIDZND0="25127@x" ObjectIDZND1="25126@x" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="SW-140334_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_137e460_0" Pin1InfoVect1LinkObjId="g_137c140_0" Pin1InfoVect2LinkObjId="SW-139439_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-249 1843,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1834900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-305 1843,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25127@x" ObjectIDND1="g_137e460@0" ObjectIDND2="g_137c140@0" ObjectIDZND0="25126@0" Pin0InfoVect0LinkObjId="SW-140334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="g_137e460_0" Pin1InfoVect2LinkObjId="g_137c140_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-305 1843,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1834b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-149 1121,-149 1121,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_181c7c0@0" ObjectIDND1="0@x" ObjectIDND2="25259@x" ObjectIDZND0="g_181d040@0" Pin0InfoVect0LinkObjId="g_181d040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_181c7c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-141438_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-149 1121,-149 1121,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1834dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-149 1162,-149 1162,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_181d040@0" ObjectIDND1="0@x" ObjectIDND2="25259@x" ObjectIDZND0="g_181c7c0@1" Pin0InfoVect0LinkObjId="g_181c7c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_181d040_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-141438_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-149 1162,-149 1162,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1835020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1010,-159 1010,-147 1044,-147 1044,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13a3460@0" ObjectIDZND0="g_13a2f60@1" Pin0InfoVect0LinkObjId="g_13a2f60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13a3460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1010,-159 1010,-147 1044,-147 1044,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1842f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-612 2142,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_17daad0@0" ObjectIDND1="24976@x" ObjectIDND2="24973@x" ObjectIDZND0="g_17e8950@0" ObjectIDZND1="g_1833770@0" ObjectIDZND2="g_13a9db0@0" Pin0InfoVect0LinkObjId="g_17e8950_0" Pin0InfoVect1LinkObjId="g_1833770_0" Pin0InfoVect2LinkObjId="g_13a9db0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17daad0_0" Pin1InfoVect1LinkObjId="SW-138862_0" Pin1InfoVect2LinkObjId="SW-138859_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-612 2142,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1843160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-578 2119,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_17daad0@0" ObjectIDND1="24976@x" ObjectIDND2="24973@x" ObjectIDZND0="g_17e8950@1" Pin0InfoVect0LinkObjId="g_17e8950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17daad0_0" Pin1InfoVect1LinkObjId="SW-138862_0" Pin1InfoVect2LinkObjId="SW-138859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-578 2119,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18433c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-349 1981,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1833770@0" ObjectIDND1="g_17daad0@0" ObjectIDND2="24976@x" ObjectIDZND0="g_13a9db0@1" Pin0InfoVect0LinkObjId="g_13a9db0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1833770_0" Pin1InfoVect1LinkObjId="g_17daad0_0" Pin1InfoVect2LinkObjId="SW-138862_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-349 1981,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1843eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-331 1971,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1833770@0" ObjectIDND1="g_17daad0@0" ObjectIDND2="24976@x" ObjectIDZND0="g_13a9db0@0" ObjectIDZND1="25121@x" Pin0InfoVect0LinkObjId="g_13a9db0_0" Pin0InfoVect1LinkObjId="SW-140282_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1833770_0" Pin1InfoVect1LinkObjId="g_17daad0_0" Pin1InfoVect2LinkObjId="SW-138862_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-331 1971,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1844110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-349 1971,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_13a9db0@0" ObjectIDND1="g_1833770@0" ObjectIDND2="g_17daad0@0" ObjectIDZND0="25121@0" Pin0InfoVect0LinkObjId="SW-140282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13a9db0_0" Pin1InfoVect1LinkObjId="g_1833770_0" Pin1InfoVect2LinkObjId="g_17daad0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-349 1971,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1847400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-249 1843,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25127@x" ObjectIDND1="25126@x" ObjectIDND2="g_137e460@0" ObjectIDZND0="g_13b53e0@0" ObjectIDZND1="g_13b63a0@0" ObjectIDZND2="25262@x" Pin0InfoVect0LinkObjId="g_13b53e0_0" Pin0InfoVect1LinkObjId="g_13b63a0_0" Pin0InfoVect2LinkObjId="SW-141455_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="SW-140334_0" Pin1InfoVect2LinkObjId="g_137e460_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-249 1843,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1847660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-86 1843,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25127@x" ObjectIDND1="25126@x" ObjectIDND2="g_137e460@0" ObjectIDZND0="g_13b53e0@0" ObjectIDZND1="g_13b63a0@0" ObjectIDZND2="25262@x" Pin0InfoVect0LinkObjId="g_13b53e0_0" Pin0InfoVect1LinkObjId="g_13b63a0_0" Pin0InfoVect2LinkObjId="SW-141455_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="SW-140334_0" Pin1InfoVect2LinkObjId="g_137e460_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-86 1843,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18478c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1956,-98 1956,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_17f5330@0" ObjectIDZND2="25127@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_17f5330_0" Pin0InfoVect2LinkObjId="SW-140335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1956,-98 1956,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18483b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-86 1956,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_17f5330@0" ObjectIDZND2="25127@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_17f5330_0" Pin0InfoVect2LinkObjId="SW-140335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-86 1956,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1848610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2051,-117 2051,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17f40c0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f40c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-117 2051,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1849100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2067,-86 2051,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_17f40c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_17f40c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2067,-86 2051,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1849360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2051,-86 2028,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_17f40c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17f40c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-86 2028,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1849e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-86 1843,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_17f5330@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="25127@x" ObjectIDZND1="25126@x" ObjectIDZND2="g_137e460@0" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="SW-140334_0" Pin0InfoVect2LinkObjId="g_137e460_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17f5330_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-86 1843,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1956,-86 1904,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_17f5330@0" ObjectIDZND1="25127@x" ObjectIDZND2="25126@x" Pin0InfoVect0LinkObjId="g_17f5330_0" Pin0InfoVect1LinkObjId="SW-140335_0" Pin0InfoVect2LinkObjId="SW-140334_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1956,-86 1904,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184a310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2011,-263 2011,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_137c140@0" ObjectIDZND0="25127@x" ObjectIDZND1="25126@x" ObjectIDZND2="g_13b53e0@0" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="SW-140334_0" Pin0InfoVect2LinkObjId="g_13b53e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_137c140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2011,-263 2011,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1977,-249 2011,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25127@x" ObjectIDND1="25126@x" ObjectIDND2="g_13b53e0@0" ObjectIDZND0="g_137c140@0" ObjectIDZND1="25022@x" Pin0InfoVect0LinkObjId="g_137c140_0" Pin0InfoVect1LinkObjId="SW-139439_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="SW-140334_0" Pin1InfoVect2LinkObjId="g_13b53e0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1977,-249 2011,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2011,-249 2054,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_137c140@0" ObjectIDND1="25127@x" ObjectIDND2="25126@x" ObjectIDZND0="25022@0" Pin0InfoVect0LinkObjId="SW-139439_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_137c140_0" Pin1InfoVect1LinkObjId="SW-140335_0" Pin1InfoVect2LinkObjId="SW-140334_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2011,-249 2054,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_184b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1415,-94 1415,-127 1440,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_12a75e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_12a75e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1415,-94 1415,-127 1440,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_184bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-146 1440,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_12a75e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_12a75e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-146 1440,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_184c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-127 1440,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_12a75e0@1" Pin0InfoVect0LinkObjId="g_12a75e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-127 1440,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184c2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1298,-22 1318,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25259@1" ObjectIDZND0="25258@x" ObjectIDZND1="g_181c7c0@0" ObjectIDZND2="g_181d040@0" Pin0InfoVect0LinkObjId="SW-141437_0" Pin0InfoVect1LinkObjId="g_181c7c0_0" Pin0InfoVect2LinkObjId="g_181d040_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1298,-22 1318,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184cda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-5 1318,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25258@1" ObjectIDZND0="25259@x" ObjectIDZND1="g_181c7c0@0" ObjectIDZND2="g_181d040@0" Pin0InfoVect0LinkObjId="SW-141438_0" Pin0InfoVect1LinkObjId="g_181c7c0_0" Pin0InfoVect2LinkObjId="g_181d040_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-5 1318,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184d000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-22 1318,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25259@x" ObjectIDND1="25258@x" ObjectIDZND0="g_181c7c0@0" ObjectIDZND1="g_181d040@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_181c7c0_0" Pin0InfoVect1LinkObjId="g_181d040_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141438_0" Pin1InfoVect1LinkObjId="SW-141437_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-22 1318,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184e220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-342 1198,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_184d260@0" ObjectIDZND0="g_184dae0@0" Pin0InfoVect0LinkObjId="g_184dae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_184d260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-342 1198,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184fd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1137,-342 1137,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_184d260@1" ObjectIDZND0="g_184e480@0" ObjectIDZND1="25193@x" ObjectIDZND2="25194@x" Pin0InfoVect0LinkObjId="g_184e480_0" Pin0InfoVect1LinkObjId="SW-140980_0" Pin0InfoVect2LinkObjId="SW-140981_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_184d260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1137,-342 1137,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_184ff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1137,-279 1137,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="25193@x" ObjectIDND1="25194@x" ObjectIDND2="0@x" ObjectIDZND0="g_184e480@0" Pin0InfoVect0LinkObjId="g_184e480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140980_0" Pin1InfoVect1LinkObjId="SW-140981_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1137,-279 1137,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18501e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1137,-279 1181,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_184e480@0" ObjectIDND1="25194@x" ObjectIDND2="0@x" ObjectIDZND0="25193@0" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_184e480_0" Pin1InfoVect1LinkObjId="SW-140981_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1137,-279 1181,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1853920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1094,-335 1094,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25194@1" ObjectIDZND0="g_1850a70@0" Pin0InfoVect0LinkObjId="g_1850a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1094,-335 1094,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1853b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1094,-279 1137,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25194@x" ObjectIDND1="0@x" ObjectIDND2="g_18b23b0@0" ObjectIDZND0="g_184e480@0" ObjectIDZND1="25193@x" ObjectIDZND2="g_184d260@0" Pin0InfoVect0LinkObjId="g_184e480_0" Pin0InfoVect1LinkObjId="SW-140980_0" Pin0InfoVect2LinkObjId="g_184d260_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140981_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_18b23b0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1094,-279 1137,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1858810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1217,-279 1245,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25193@1" ObjectIDZND0="25191@1" Pin0InfoVect0LinkObjId="SW-140978_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1217,-279 1245,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-18 1025,-18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_182d8d0@0" ObjectIDZND0="g_182f2c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_182f2c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_182d8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-18 1025,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185c0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-35 1025,-18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_182f2c0@0" ObjectIDZND0="g_182d8d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_182d8d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_182f2c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-35 1025,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-18 1025,1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_182d8d0@0" ObjectIDND1="g_182f2c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_182d8d0_0" Pin1InfoVect1LinkObjId="g_182f2c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-18 1025,1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1094,-299 1094,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25194@0" ObjectIDZND0="g_184e480@0" ObjectIDZND1="25193@x" ObjectIDZND2="g_184d260@0" Pin0InfoVect0LinkObjId="g_184e480_0" Pin0InfoVect1LinkObjId="SW-140980_0" Pin0InfoVect2LinkObjId="g_184d260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140981_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1094,-299 1094,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185d940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-23 1144,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_182e2b0@0" ObjectIDND1="0@x" ObjectIDZND0="g_1830270@0" Pin0InfoVect0LinkObjId="g_1830270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_182e2b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-23 1144,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-7 1144,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_182e2b0@0" ObjectIDZND1="g_1830270@0" Pin0InfoVect0LinkObjId="g_182e2b0_0" Pin0InfoVect1LinkObjId="g_1830270_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-7 1144,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185de00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1336,-279 1367,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25192@1" ObjectIDZND0="25188@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1336,-279 1367,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185e060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-181 1440,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-181 1440,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185e890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,-180 1511,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1511,-180 1511,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185f950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-284 1475,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="busSection" ObjectIDND0="25131@x" ObjectIDND1="25130@x" ObjectIDND2="g_13203e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="SW-140385_0" Pin1InfoVect2LinkObjId="g_13203e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-284 1475,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185fbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-247 1640,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_13467a0@0" ObjectIDND2="0@x" ObjectIDZND0="g_13203e0@0" Pin0InfoVect0LinkObjId="g_13203e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13467a0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-247 1640,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18606a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-284 1660,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_13467a0@0" ObjectIDND2="0@x" ObjectIDZND0="25131@x" ObjectIDZND1="25130@x" ObjectIDZND2="g_13203e0@0" Pin0InfoVect0LinkObjId="SW-140386_0" Pin0InfoVect1LinkObjId="SW-140385_0" Pin0InfoVect2LinkObjId="g_13203e0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13467a0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-284 1660,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1860900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-284 1660,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_13467a0@0" ObjectIDND2="0@x" ObjectIDZND0="25131@x" ObjectIDZND1="25130@x" Pin0InfoVect0LinkObjId="SW-140386_0" Pin0InfoVect1LinkObjId="SW-140385_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13467a0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-284 1660,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1860b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1768,-179 1768,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_135ab00@0" Pin0InfoVect0LinkObjId="g_135ab00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1768,-179 1768,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1861c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-247 1660,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_13203e0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_13467a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13467a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13203e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-247 1660,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1862710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-230 1660,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_13203e0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_13467a0@0" Pin0InfoVect0LinkObjId="g_13203e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_13467a0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-230 1660,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1863200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-175 1660,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_13203e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13203e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-175 1660,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1863460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-211 1660,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_13203e0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_13467a0@0" Pin0InfoVect0LinkObjId="g_13203e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_13467a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-211 1660,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18636c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1734,-202 1734,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_131f900@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_131f900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1734,-202 1734,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18641b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1714,-230 1734,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_131f900@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_131f900_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1714,-230 1734,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1864410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1734,-230 1768,-230 1768,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_131f900@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_131f900_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1734,-230 1768,-230 1768,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1864f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-190 1318,-205 1144,-205 1144,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25259@x" ObjectIDND1="25258@x" ObjectIDND2="g_17d66e0@0" ObjectIDZND0="g_181c7c0@0" ObjectIDZND1="g_181d040@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_181c7c0_0" Pin0InfoVect1LinkObjId="g_181d040_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141438_0" Pin1InfoVect1LinkObjId="SW-141437_0" Pin1InfoVect2LinkObjId="g_17d66e0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-190 1318,-205 1144,-205 1144,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1865170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1308,-171 1318,-171 1318,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_17d66e0@0" ObjectIDZND0="25259@x" ObjectIDZND1="25258@x" ObjectIDZND2="g_181c7c0@0" Pin0InfoVect0LinkObjId="SW-141438_0" Pin0InfoVect1LinkObjId="SW-141437_0" Pin0InfoVect2LinkObjId="g_181c7c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17d66e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1308,-171 1318,-171 1318,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18659a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2118,-249 2180,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="25023@x" ObjectIDND1="25022@x" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_1866490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-139440_0" Pin1InfoVect1LinkObjId="SW-139439_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2118,-249 2180,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1866490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2118,-260 2118,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="25023@0" ObjectIDZND0="25019@0" ObjectIDZND1="25022@x" Pin0InfoVect0LinkObjId="g_18659a0_0" Pin0InfoVect1LinkObjId="SW-139439_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2118,-260 2118,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18666f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2118,-249 2090,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25019@0" ObjectIDND1="25023@x" ObjectIDZND0="25022@1" Pin0InfoVect0LinkObjId="SW-139439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18659a0_0" Pin1InfoVect1LinkObjId="SW-139440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2118,-249 2090,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1866f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2158,-86 2179,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2158,-86 2179,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1867750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-788 2142,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24972@1" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_18679b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-788 2142,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18679b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-793 1900,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24966@1" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_1867750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-793 1900,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18687b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-479 1971,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25120@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_1868fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-479 1971,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1868fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,-473 1843,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25125@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_18687b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140333_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,-473 1843,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1869810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1660,-475 1660,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25129@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_18687b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-475 1660,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_186a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,125 2362,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25264@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_186a2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,125 2362,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_186a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1843,134 1843,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25261@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_186a040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1843,134 1843,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_186a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,134 1318,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25257@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_186a040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,134 1318,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_186b300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,135 1025,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,135 1025,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_186bb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,129 1144,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,129 1144,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_186c360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,165 1125,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,165 1125,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_186cb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,163 1061,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,163 1061,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18760a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-581 979,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-581 979,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1876300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-502 1019,-492 979,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_186df70@0" ObjectIDZND0="g_186d3c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_186d3c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_186df70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-502 1019,-492 979,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1876df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-468 979,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_186d3c0@1" ObjectIDZND0="g_186df70@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_186df70_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_186d3c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="979,-468 979,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1877050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-492 979,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_186df70@0" ObjectIDND1="g_186d3c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_186df70_0" Pin1InfoVect1LinkObjId="g_186d3c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-492 979,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1877b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-115 1025,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_184e480@0" ObjectIDZND1="25193@x" ObjectIDZND2="g_184d260@0" Pin0InfoVect0LinkObjId="g_184e480_0" Pin0InfoVect1LinkObjId="SW-140980_0" Pin0InfoVect2LinkObjId="g_184d260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-115 1025,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1877da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-279 1094,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_18b23b0@0" ObjectIDND2="g_186d3c0@0" ObjectIDZND0="g_184e480@0" ObjectIDZND1="25193@x" ObjectIDZND2="g_184d260@0" Pin0InfoVect0LinkObjId="g_184e480_0" Pin0InfoVect1LinkObjId="SW-140980_0" Pin0InfoVect2LinkObjId="g_184d260_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_18b23b0_0" Pin1InfoVect2LinkObjId="g_186d3c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-279 1094,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_187c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="871,-209 896,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_187a770@0" ObjectIDZND0="g_187aff0@0" Pin0InfoVect0LinkObjId="g_187aff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_187a770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="871,-209 896,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_187c420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-152 889,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27849@1" ObjectIDZND0="g_187b730@0" Pin0InfoVect0LinkObjId="g_187b730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-152 889,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_188bea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,110 660,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27851@0" ObjectIDZND0="g_188c090@0" Pin0InfoVect0LinkObjId="g_188c090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,110 660,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1895250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-279 979,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_184e480@0" ObjectIDND2="25193@x" ObjectIDZND0="g_18b23b0@0" ObjectIDZND1="g_186d3c0@0" ObjectIDZND2="27849@x" Pin0InfoVect0LinkObjId="g_18b23b0_0" Pin0InfoVect1LinkObjId="g_186d3c0_0" Pin0InfoVect2LinkObjId="SW-183006_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_184e480_0" Pin1InfoVect2LinkObjId="SW-140980_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-279 979,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18954b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-57 798,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="27848@1" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_1895710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183002_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="798,-57 798,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1895710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,26 761,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="27850@0" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_18954b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,26 761,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1895f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,34 836,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24884@1" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_18954b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138151_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,34 836,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1896770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,98 836,98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1893c10@0" ObjectIDZND0="24884@x" ObjectIDZND1="g_1892310@0" Pin0InfoVect0LinkObjId="SW-138151_0" Pin0InfoVect1LinkObjId="g_1892310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1893c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="873,98 836,98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1897260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,80 836,98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24884@0" ObjectIDZND0="g_1893c10@0" ObjectIDZND1="g_1892310@0" Pin0InfoVect0LinkObjId="g_1893c10_0" Pin0InfoVect1LinkObjId="g_1892310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="836,80 836,98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18974c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,98 836,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1893c10@0" ObjectIDND1="24884@x" ObjectIDZND0="g_1892310@0" Pin0InfoVect0LinkObjId="g_1892310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1893c10_0" Pin1InfoVect1LinkObjId="SW-138151_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,98 836,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1897720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,110 761,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="27851@1" ObjectIDZND0="27850@x" ObjectIDZND1="g_188f300@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-183092_0" Pin0InfoVect1LinkObjId="g_188f300_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="710,110 761,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1898210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,91 761,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="27850@1" ObjectIDZND0="27851@x" ObjectIDZND1="g_188f300@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-183096_0" Pin0InfoVect1LinkObjId="g_188f300_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="761,91 761,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1898470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,137 761,137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="transformer2" ObjectIDND0="g_188f300@0" ObjectIDZND0="27851@x" ObjectIDZND1="27850@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-183096_0" Pin0InfoVect1LinkObjId="SW-183092_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_188f300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="710,137 761,137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1898f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,110 761,137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="27851@x" ObjectIDND1="27850@x" ObjectIDZND0="g_188f300@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_188f300_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183096_0" Pin1InfoVect1LinkObjId="SW-183092_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="761,110 761,137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18991c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,137 761,156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="transformer2" ObjectIDND0="g_188f300@0" ObjectIDND1="27851@x" ObjectIDND2="27850@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_188f300_0" Pin1InfoVect1LinkObjId="SW-183096_0" Pin1InfoVect2LinkObjId="SW-183092_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,137 761,156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_189ea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2557,-330 2557,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2557,-330 2557,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_189ec60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-142 2510,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="g_18a3cd0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_18b4340@0" Pin0InfoVect0LinkObjId="g_18a3cd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_18b4340_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-142 2510,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_189f0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2557,-276 2557,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2557,-276 2557,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a2070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2609,-386 2629,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_18a22d0@0" Pin0InfoVect0LinkObjId="g_18a22d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2609,-386 2629,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a2d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2573,-386 2557,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_18b4340@0" ObjectIDZND2="g_18a3cd0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_18b4340_0" Pin0InfoVect2LinkObjId="g_18a3cd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2573,-386 2557,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a2fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2557,-386 2557,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_18b4340@0" ObjectIDND2="g_18a3cd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_18b4340_0" Pin1InfoVect2LinkObjId="g_18a3cd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2557,-386 2557,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a5230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2480,-531 2480,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_18a3cd0@0" ObjectIDZND0="g_18a42a0@0" Pin0InfoVect0LinkObjId="g_18a42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18a3cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2480,-531 2480,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a5490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2480,-562 2480,-578 2510,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18a3cd0@1" ObjectIDZND0="g_18b4340@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_18b4340_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18a3cd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2480,-562 2480,-578 2510,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a56f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-442 2510,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_18b4340@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_18a3cd0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_18a3cd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18b4340_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-442 2510,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a5950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-578 2510,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_18b4340@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18b4340_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-578 2510,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a6420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2294,-47 2315,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1323ba0@1" Pin0InfoVect0LinkObjId="g_1323ba0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2294,-47 2315,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18a7aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2348,-88 2375,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_139bfd0@0" ObjectIDZND0="g_139b9e0@1" Pin0InfoVect0LinkObjId="g_139b9e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_139bfd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2348,-88 2375,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a7c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2346,-46 2362,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1323ba0@0" ObjectIDZND0="g_139b010@0" ObjectIDZND1="0@x" ObjectIDZND2="25266@x" Pin0InfoVect0LinkObjId="g_139b010_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-141473_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1323ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2346,-46 2362,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a7e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-579 2362,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_139b010@0" ObjectIDND1="0@x" ObjectIDZND0="g_1323ba0@0" ObjectIDZND1="25266@x" ObjectIDZND2="25265@x" Pin0InfoVect0LinkObjId="g_1323ba0_0" Pin0InfoVect1LinkObjId="SW-141473_0" Pin0InfoVect2LinkObjId="SW-141472_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_139b010_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-579 2362,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a8070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2380,-26 2362,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25266@0" ObjectIDZND0="g_1323ba0@0" ObjectIDZND1="g_139b010@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1323ba0_0" Pin0InfoVect1LinkObjId="g_139b010_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2380,-26 2362,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a82a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-46 2362,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1323ba0@0" ObjectIDND1="g_139b010@0" ObjectIDND2="0@x" ObjectIDZND0="25266@x" ObjectIDZND1="25265@x" Pin0InfoVect0LinkObjId="SW-141473_0" Pin0InfoVect1LinkObjId="SW-141472_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1323ba0_0" Pin1InfoVect1LinkObjId="g_139b010_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-46 2362,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-26 2362,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25266@x" ObjectIDND1="g_1323ba0@0" ObjectIDND2="g_139b010@0" ObjectIDZND0="25265@1" Pin0InfoVect0LinkObjId="SW-141472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="g_1323ba0_0" Pin1InfoVect2LinkObjId="g_139b010_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-26 2362,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1542,-591 1542,-602 1475,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_13467a0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_17fcf30@0" Pin0InfoVect0LinkObjId="g_13467a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_17fcf30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1542,-591 1542,-602 1475,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-635 1475,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13467a0@0" ObjectIDND1="0@x" ObjectIDND2="g_17fcf30@0" ObjectIDZND0="0@x" ObjectIDZND1="25131@x" ObjectIDZND2="25130@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-140386_0" Pin0InfoVect2LinkObjId="SW-140385_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13467a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_17fcf30_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-635 1475,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a9b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-602 1475,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_13467a0@0" ObjectIDND2="0@x" ObjectIDZND0="25131@x" ObjectIDZND1="25130@x" ObjectIDZND2="g_13203e0@0" Pin0InfoVect0LinkObjId="SW-140386_0" Pin0InfoVect1LinkObjId="SW-140385_0" Pin0InfoVect2LinkObjId="g_13203e0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13467a0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-602 1475,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a9d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1599,-592 1599,-602 1674,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_13bc110@0" ObjectIDZND1="24970@x" ObjectIDZND2="24967@x" Pin0InfoVect0LinkObjId="g_13bc110_0" Pin0InfoVect1LinkObjId="SW-138808_0" Pin0InfoVect2LinkObjId="SW-138805_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1599,-592 1599,-602 1674,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18aa7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-608 1900,-552 1674,-552 1674,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_13bc110@0" ObjectIDND1="24970@x" ObjectIDND2="24967@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1805a10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1805a10_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13bc110_0" Pin1InfoVect1LinkObjId="SW-138808_0" Pin1InfoVect2LinkObjId="SW-138805_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-608 1900,-552 1674,-552 1674,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18aaa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,-602 1674,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_13bc110@0" ObjectIDND2="24970@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1805a10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1805a10_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13bc110_0" Pin1InfoVect2LinkObjId="SW-138808_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1674,-602 1674,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18accc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-609 1114,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="load" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-609 1114,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b1dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-546 1114,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_18ac210@0" ObjectIDZND0="g_18b23b0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_18b23b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18ac210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-546 1114,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b1fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-533 1114,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_18b23b0@1" ObjectIDZND0="g_18ac210@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_18ac210_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b23b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-533 1114,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b21a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-546 1114,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_18ac210@0" ObjectIDND1="g_18b23b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18ac210_0" Pin1InfoVect1LinkObjId="g_18b23b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-546 1114,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b33d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-395 1113,-395 1113,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_186d3c0@0" ObjectIDND1="0@x" ObjectIDND2="g_184e480@0" ObjectIDZND0="g_18b23b0@0" Pin0InfoVect0LinkObjId="g_18b23b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_186d3c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_184e480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-395 1113,-395 1113,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b3e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-395 979,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_18b23b0@0" ObjectIDND1="0@x" ObjectIDND2="g_184e480@0" ObjectIDZND0="g_186d3c0@0" Pin0InfoVect0LinkObjId="g_186d3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18b23b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_184e480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-395 979,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b40e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-280 979,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_184e480@0" ObjectIDND2="25193@x" ObjectIDZND0="g_18b23b0@0" ObjectIDZND1="g_186d3c0@0" Pin0InfoVect0LinkObjId="g_18b23b0_0" Pin0InfoVect1LinkObjId="g_186d3c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_184e480_0" Pin1InfoVect2LinkObjId="SW-140980_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="979,-280 979,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18b52a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2636,-434 2616,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_18b4b60@0" ObjectIDZND0="g_18b4340@0" Pin0InfoVect0LinkObjId="g_18b4340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b4b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2636,-434 2616,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18b5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2585,-433 2557,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18b4340@1" ObjectIDZND0="g_18a3cd0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_18a3cd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b4340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2585,-433 2557,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18b5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-442 2557,-442 2557,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18a3cd0@0" ObjectIDND1="0@x" ObjectIDZND0="g_18b4340@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_18b4340_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18a3cd0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-442 2557,-442 2557,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18b59c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2557,-433 2557,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_18b4340@0" ObjectIDND1="g_18a3cd0@0" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18b4340_0" Pin1InfoVect1LinkObjId="g_18a3cd0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2557,-433 2557,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18ba3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-774 2362,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-774 2362,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18ba610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2510,-773 2510,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2510,-773 2510,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18bc910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-784 1318,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-784 1318,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18bcff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-783 1475,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-783 1475,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18bd700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,-782 1674,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1674,-782 1674,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18c3730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1031,-630 1051,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_18c3990@0" Pin0InfoVect0LinkObjId="g_18c3990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1031,-630 1051,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18c4420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="995,-630 979,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="995,-630 979,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18c4f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-658 979,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="979,-658 979,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18c5170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-630 979,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-630 979,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c53d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-152 798,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27849@0" ObjectIDZND0="27848@x" ObjectIDZND1="0@x" ObjectIDZND2="g_184e480@0" Pin0InfoVect0LinkObjId="SW-183002_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_184e480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="827,-152 798,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c5ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-122 798,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27848@0" ObjectIDZND0="27849@x" ObjectIDZND1="0@x" ObjectIDZND2="g_184e480@0" Pin0InfoVect0LinkObjId="SW-183006_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_184e480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="798,-122 798,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c6e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-209 798,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_187c680@0" ObjectIDZND0="27849@x" ObjectIDZND1="27848@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-183006_0" Pin0InfoVect1LinkObjId="SW-183002_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_187c680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="757,-209 798,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c7a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-151 798,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27849@x" ObjectIDND1="27848@x" ObjectIDZND0="0@x" ObjectIDZND1="g_184e480@0" ObjectIDZND2="25193@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_184e480_0" Pin0InfoVect2LinkObjId="SW-140980_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183006_0" Pin1InfoVect1LinkObjId="SW-183002_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="798,-151 798,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c7c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-209 798,-279 979,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27849@x" ObjectIDND1="27848@x" ObjectIDND2="g_187c680@0" ObjectIDZND0="0@x" ObjectIDZND1="g_184e480@0" ObjectIDZND2="25193@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_184e480_0" Pin0InfoVect2LinkObjId="SW-140980_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-183006_0" Pin1InfoVect1LinkObjId="SW-183002_0" Pin1InfoVect2LinkObjId="g_187c680_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="798,-209 798,-279 979,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c7ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-209 798,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_187a770@1" ObjectIDZND0="27849@x" ObjectIDZND1="27848@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-183006_0" Pin0InfoVect1LinkObjId="SW-183002_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_187a770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="840,-209 798,-209 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>