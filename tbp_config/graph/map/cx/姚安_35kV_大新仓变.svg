<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-220" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-197 -1109 2506 1235">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape170">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="16,30 7,30 " stroke-width="1"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="15,10 6,10 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1087"/>
    <polyline points="58,100 64,100 " stroke-width="1.1087"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1087"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="voltageTransformer:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="27" x2="28" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="22" x2="21" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="28" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
    <circle cx="23" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="7" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e888c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e89a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e8a3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e8b0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e8c320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e8cf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e8d9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e8e460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_15e0c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_15e0c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e914e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e914e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e933e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e933e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1e943f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e96080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e96cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e97bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e98490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e99c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9a930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9b1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e9b9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9ca90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9df00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e9e8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e9fcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ea08b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ea1900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ea2550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1eb0860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ea3e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ea5540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ea6990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1245" width="2516" x="-202" y="-1114"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="651" x2="651" y1="-934" y2="-924"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="648" x2="654" y1="-934" y2="-934"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="650" x2="652" y1="-938" y2="-938"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="649" x2="653" y1="-936" y2="-936"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="656" x2="646" y1="-912" y2="-921"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="646" x2="646" y1="-921" y2="-924"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="651" x2="632" y1="-905" y2="-905"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="651" x2="651" y1="-905" y2="-911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="874" x2="926" y1="-590" y2="-590"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="854" x2="854" y1="-76" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="854" x2="760" y1="62" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="760" x2="760" y1="59" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="802" x2="802" y1="47" y2="47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="554" x2="554" y1="-556" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="551" x2="557" y1="-556" y2="-556"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="553" x2="555" y1="-560" y2="-560"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="552" x2="556" y1="-558" y2="-558"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="559" x2="549" y1="-534" y2="-543"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="549" x2="549" y1="-543" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="554" x2="535" y1="-527" y2="-527"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="554" x2="554" y1="-527" y2="-533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2052" x2="2052" y1="-380" y2="-96"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2167" x2="2167" y1="-381" y2="-97"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2024" x2="2024" y1="-558" y2="-548"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2021" x2="2027" y1="-558" y2="-558"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2023" x2="2025" y1="-562" y2="-562"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2022" x2="2026" y1="-560" y2="-560"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2029" x2="2019" y1="-536" y2="-545"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2019" x2="2019" y1="-545" y2="-548"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2024" x2="2005" y1="-529" y2="-529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2024" x2="2024" y1="-529" y2="-535"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-148332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -828.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25933" ObjectName="SW-YA_DXC.YA_DXC_342BK"/>
     <cge:Meas_Ref ObjectId="148332"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148356">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.000000 -825.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25938" ObjectName="SW-YA_DXC.YA_DXC_343BK"/>
     <cge:Meas_Ref ObjectId="148356"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 -823.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25943" ObjectName="SW-YA_DXC.YA_DXC_341BK"/>
     <cge:Meas_Ref ObjectId="148380"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 -617.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25948" ObjectName="SW-YA_DXC.YA_DXC_301BK"/>
     <cge:Meas_Ref ObjectId="148404"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 -429.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25954" ObjectName="SW-YA_DXC.YA_DXC_001BK"/>
     <cge:Meas_Ref ObjectId="148483"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1611.000000 -629.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25950" ObjectName="SW-YA_DXC.YA_DXC_302BK"/>
     <cge:Meas_Ref ObjectId="148442"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148487">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1611.000000 -422.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25956" ObjectName="SW-YA_DXC.YA_DXC_002BK"/>
     <cge:Meas_Ref ObjectId="148487"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148545">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.371429 -269.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25968" ObjectName="SW-YA_DXC.YA_DXC_042BK"/>
     <cge:Meas_Ref ObjectId="148545"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148528">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.371429 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25965" ObjectName="SW-YA_DXC.YA_DXC_043BK"/>
     <cge:Meas_Ref ObjectId="148528"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.371429 -274.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25971" ObjectName="SW-YA_DXC.YA_DXC_044BK"/>
     <cge:Meas_Ref ObjectId="148562"/>
    <cge:TPSR_Ref TObjectID="25971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243481">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.371429 -276.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40887" ObjectName="SW-YA_DXC.YA_DXC_045BK"/>
     <cge:Meas_Ref ObjectId="243481"/>
    <cge:TPSR_Ref TObjectID="40887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.371429 -269.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25962" ObjectName="SW-YA_DXC.YA_DXC_047BK"/>
     <cge:Meas_Ref ObjectId="148511"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1676.371429 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25959" ObjectName="SW-YA_DXC.YA_DXC_048BK"/>
     <cge:Meas_Ref ObjectId="148494"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.371429 -276.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40902" ObjectName="SW-YA_DXC.YA_DXC_049BK"/>
     <cge:Meas_Ref ObjectId="243598"/>
    <cge:TPSR_Ref TObjectID="40902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185363">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 -444.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40871" ObjectName="SW-YA_DXC.YA_DXC_012BK"/>
     <cge:Meas_Ref ObjectId="185363"/>
    <cge:TPSR_Ref TObjectID="40871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-266304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 235.371429 -268.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43014" ObjectName="SW-YA_DXC.YA_DXC_041BK"/>
     <cge:Meas_Ref ObjectId="266304"/>
    <cge:TPSR_Ref TObjectID="43014"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13d7920">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 640.000000 -928.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13acbf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1394.000000 -900.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_142dbb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1813.000000 -876.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c9ea0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 543.000000 -550.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1272380">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2013.000000 -552.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YA" endPointId="0" endStationName="YA_DXC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yaoxin" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="858,-1021 858,-981 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34312" ObjectName="AC-35kV.LN_yaoxin"/>
    <cge:TPSR_Ref TObjectID="34312_SS-220"/></metadata>
   <polyline fill="none" opacity="0" points="858,-1021 858,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_DXC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xidalianTdxc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1273,-1009 1273,-978 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34317" ObjectName="AC-35kV.LN_xidalianTdxc"/>
    <cge:TPSR_Ref TObjectID="34317_SS-220"/></metadata>
   <polyline fill="none" opacity="0" points="1273,-1009 1273,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_DXC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dacangnanTdxc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1689,-1019 1689,-980 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34314" ObjectName="AC-35kV.LN_dacangnanTdxc"/>
    <cge:TPSR_Ref TObjectID="34314_SS-220"/></metadata>
   <polyline fill="none" opacity="0" points="1689,-1019 1689,-980 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.371429 -21.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34265" ObjectName="EC-YA_DXC.042Ld"/>
    <cge:TPSR_Ref TObjectID="34265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 594.371429 -24.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34264" ObjectName="EC-YA_DXC.043Ld"/>
    <cge:TPSR_Ref TObjectID="34264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.371429 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.047Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1497.371429 -21.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34263" ObjectName="EC-YA_DXC.047Ld"/>
    <cge:TPSR_Ref TObjectID="34263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.048Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.371429 -24.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34262" ObjectName="EC-YA_DXC.048Ld"/>
    <cge:TPSR_Ref TObjectID="34262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.371429 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2047.371429 -76.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2162.371429 -77.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DXC.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 239.371429 -20.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46581" ObjectName="EC-YA_DXC.041Ld"/>
    <cge:TPSR_Ref TObjectID="46581"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13d67e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 -754.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c3eb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 -758.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1320b80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 869.000000 -70.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_13cc9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-746 1273,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25934@0" Pin0InfoVect0LinkObjId="SW-148334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-746 1273,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1395600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-820 1273,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25934@1" ObjectIDZND0="25933@0" Pin0InfoVect0LinkObjId="SW-148332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-820 1273,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f4080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-863 1273,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25933@1" ObjectIDZND0="25936@0" Pin0InfoVect0LinkObjId="SW-148336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-863 1273,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13600b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-746 1689,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25939@0" Pin0InfoVect0LinkObjId="SW-148358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-746 1689,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13602a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-823 1689,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25939@1" ObjectIDZND0="25938@0" Pin0InfoVect0LinkObjId="SW-148356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-823 1689,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1360490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-860 1689,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25938@1" ObjectIDZND0="25941@0" Pin0InfoVect0LinkObjId="SW-148360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-860 1689,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f8780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-746 858,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25944@0" Pin0InfoVect0LinkObjId="SW-148382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="858,-746 858,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f8970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-821 858,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25944@1" ObjectIDZND0="25943@0" Pin0InfoVect0LinkObjId="SW-148380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="858,-821 858,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f8b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-858 858,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25943@1" ObjectIDZND0="25946@0" Pin0InfoVect0LinkObjId="SW-148384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="858,-858 858,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d6f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-782 587,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25953@0" ObjectIDZND0="g_13d67e0@0" Pin0InfoVect0LinkObjId="g_13d67e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-782 587,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d7730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-881 631,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_13d7140@0" ObjectIDZND0="g_13d7920@0" Pin0InfoVect0LinkObjId="g_13d7920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d7140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-881 631,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1416940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-835 670,-835 670,-854 669,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_13d7140@0" ObjectIDND1="25953@x" ObjectIDND2="25952@x" ObjectIDZND0="g_1415f30@0" Pin0InfoVect0LinkObjId="g_1415f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13d7140_0" Pin1InfoVect1LinkObjId="SW-148480_0" Pin1InfoVect2LinkObjId="SW-148479_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-835 670,-835 670,-854 669,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1417100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-835 631,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1415f30@0" ObjectIDND1="25953@x" ObjectIDND2="25952@x" ObjectIDZND0="g_13d7140@1" Pin0InfoVect0LinkObjId="g_13d7140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1415f30_0" Pin1InfoVect1LinkObjId="SW-148480_0" Pin1InfoVect2LinkObjId="SW-148479_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-835 631,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13eb130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-906 1689,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="25941@1" ObjectIDZND0="25942@x" ObjectIDZND1="34314@1" ObjectIDZND2="25940@x" Pin0InfoVect0LinkObjId="SW-148371_0" Pin0InfoVect1LinkObjId="g_13eb320_1" Pin0InfoVect2LinkObjId="SW-148359_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-906 1689,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13eb320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-942 1689,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="25941@x" ObjectIDND1="25942@x" ObjectIDND2="25940@x" ObjectIDZND0="34314@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148360_0" Pin1InfoVect1LinkObjId="SW-148371_0" Pin1InfoVect2LinkObjId="SW-148359_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-942 1689,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13eb510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1713,-942 1689,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="25942@0" ObjectIDZND0="25941@x" ObjectIDZND1="34314@1" ObjectIDZND2="25940@x" Pin0InfoVect0LinkObjId="SW-148360_0" Pin0InfoVect1LinkObjId="g_13eb320_1" Pin0InfoVect2LinkObjId="SW-148359_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1713,-942 1689,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e1c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-746 926,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25949@1" Pin0InfoVect0LinkObjId="SW-148406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-746 926,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e1e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-665 926,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25949@0" ObjectIDZND0="25948@1" Pin0InfoVect0LinkObjId="SW-148404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-665 926,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e2080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-625 926,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25948@0" ObjectIDZND0="25973@1" Pin0InfoVect0LinkObjId="g_14b1350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-625 926,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e22a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-403 926,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25955@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13ad1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-403 926,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e24c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-437 926,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25954@0" ObjectIDZND0="25955@1" Pin0InfoVect0LinkObjId="SW-148485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-437 926,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13abbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-746 1620,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25951@1" Pin0InfoVect0LinkObjId="SW-148444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-746 1620,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13abdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-688 1620,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25951@0" ObjectIDZND0="25950@1" Pin0InfoVect0LinkObjId="SW-148442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-688 1620,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ac010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-637 1620,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25950@0" ObjectIDZND0="25974@1" Pin0InfoVect0LinkObjId="g_14b3030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-637 1620,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ad1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-350 759,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25972@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13e22a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,-350 759,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13940c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="947,-351 947,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40888@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13e22a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="947,-351 947,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cc640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-352 1109,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40905@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13e22a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-352 1109,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1395290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-346 415,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25970@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13e22a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148548_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-346 415,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e0a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-922 1785,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_142e7e0@0" ObjectIDZND0="25942@x" ObjectIDZND1="g_142dbb0@0" Pin0InfoVect0LinkObjId="SW-148371_0" Pin0InfoVect1LinkObjId="g_142dbb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_142e7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-922 1785,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e14e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1749,-942 1785,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="25942@1" ObjectIDZND0="g_142e7e0@0" ObjectIDZND1="g_142dbb0@0" Pin0InfoVect0LinkObjId="g_142e7e0_0" Pin0InfoVect1LinkObjId="g_142dbb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1749,-942 1785,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e1700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-942 1821,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_142e7e0@0" ObjectIDND1="25942@x" ObjectIDZND0="g_142dbb0@0" Pin0InfoVect0LinkObjId="g_142dbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_142e7e0_0" Pin1InfoVect1LinkObjId="SW-148371_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-942 1821,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c1f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-878 948,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_135db80@0" ObjectIDZND0="25947@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_135db80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-878 948,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c2cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-964 858,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="25947@x" ObjectIDND1="g_13c21f0@0" ObjectIDND2="25945@x" ObjectIDZND0="34312@1" Pin0InfoVect0LinkObjId="g_13c2f20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13c21f0_0" Pin1InfoVect2LinkObjId="SW-148383_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="858,-964 858,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c2f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-949 904,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13c21f0@0" ObjectIDZND0="34312@1" ObjectIDZND1="25945@x" ObjectIDZND2="25946@x" Pin0InfoVect0LinkObjId="g_13c2cc0_1" Pin0InfoVect1LinkObjId="SW-148383_0" Pin0InfoVect2LinkObjId="SW-148384_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13c21f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="904,-949 904,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c39f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-964 904,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34312@1" ObjectIDND1="25945@x" ObjectIDND2="25946@x" ObjectIDZND0="25947@x" ObjectIDZND1="g_13c21f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13c21f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13c2cc0_1" Pin1InfoVect1LinkObjId="SW-148383_0" Pin1InfoVect2LinkObjId="SW-148384_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="858,-964 904,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c3c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-939 948,-964 904,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25947@1" ObjectIDZND0="34312@1" ObjectIDZND1="25945@x" ObjectIDZND2="25946@x" Pin0InfoVect0LinkObjId="g_13c2cc0_1" Pin0InfoVect1LinkObjId="SW-148383_0" Pin0InfoVect2LinkObjId="SW-148384_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="948,-939 948,-964 904,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e5620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="644,-764 631,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="40867@0" ObjectIDZND0="25930@0" ObjectIDZND1="25952@x" Pin0InfoVect0LinkObjId="g_1307160_0" Pin0InfoVect1LinkObjId="SW-148479_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="644,-764 631,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e6110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-746 631,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25930@0" ObjectIDZND0="40867@x" ObjectIDZND1="25952@x" Pin0InfoVect0LinkObjId="SW-243386_0" Pin0InfoVect1LinkObjId="SW-148479_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="631,-746 631,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e6370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-764 631,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="40867@x" ObjectIDND1="25930@0" ObjectIDZND0="25952@0" Pin0InfoVect0LinkObjId="SW-148479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-243386_0" Pin1InfoVect1LinkObjId="g_13e5620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-764 631,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e65d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="694,-764 680,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_13c3eb0@0" ObjectIDZND0="40867@1" Pin0InfoVect0LinkObjId="SW-243386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13c3eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="694,-764 680,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e7560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-954 1371,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_13e6830@0" ObjectIDZND0="25937@x" ObjectIDZND1="g_13acbf0@0" Pin0InfoVect0LinkObjId="SW-148347_0" Pin0InfoVect1LinkObjId="g_13acbf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e6830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-954 1371,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e8050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,-966 1371,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="25937@1" ObjectIDZND0="g_13e6830@0" ObjectIDZND1="g_13acbf0@0" Pin0InfoVect0LinkObjId="g_13e6830_0" Pin0InfoVect1LinkObjId="g_13acbf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1349,-966 1371,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e82b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-966 1402,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_13e6830@0" ObjectIDND1="25937@x" ObjectIDZND0="g_13acbf0@0" Pin0InfoVect0LinkObjId="g_13acbf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13e6830_0" Pin1InfoVect1LinkObjId="SW-148347_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-966 1402,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_132c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-481 926,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40864@1" ObjectIDZND0="25954@1" Pin0InfoVect0LinkObjId="SW-148483_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148485_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-481 926,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_132e4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-430 1620,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25956@0" ObjectIDZND0="25957@1" Pin0InfoVect0LinkObjId="SW-148489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-430 1620,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1307160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1939,-786 1939,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25930@0" Pin0InfoVect0LinkObjId="g_13e5620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d7920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1939,-786 1939,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1307990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1939,-838 1939,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_13d7920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d7920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1939,-838 1939,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a3af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-304 416,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25968@1" ObjectIDZND0="25970@1" Pin0InfoVect0LinkObjId="SW-148548_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148545_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-304 416,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a3d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-129 416,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25969@1" ObjectIDZND0="g_13c7050@1" Pin0InfoVect0LinkObjId="g_13c7050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-129 416,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a4c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-69 416,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34265@x" ObjectIDND1="g_130a770@0" ObjectIDZND0="25969@0" Pin0InfoVect0LinkObjId="SW-148547_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.042Ld_0" Pin1InfoVect1LinkObjId="g_130a770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-69 416,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127f210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-257 416,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40876@1" ObjectIDZND0="25968@0" Pin0InfoVect0LinkObjId="SW-148545_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148548_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-257 416,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13532e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="379,-215 416,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_127f470@0" ObjectIDZND0="g_13c7050@0" ObjectIDZND1="40876@x" ObjectIDZND2="40877@x" Pin0InfoVect0LinkObjId="g_13c7050_0" Pin0InfoVect1LinkObjId="SW-148548_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127f470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="379,-215 416,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1353540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-201 416,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13c7050@0" ObjectIDZND0="g_127f470@0" ObjectIDZND1="40876@x" ObjectIDZND2="40877@x" Pin0InfoVect0LinkObjId="g_127f470_0" Pin0InfoVect1LinkObjId="SW-148548_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13c7050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="416,-201 416,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1356a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-42 416,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34265@0" ObjectIDZND0="25969@x" ObjectIDZND1="g_130a770@0" Pin0InfoVect0LinkObjId="SW-148547_0" Pin0InfoVect1LinkObjId="g_130a770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.042Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="416,-42 416,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1356cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-76 416,-76 416,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_130a770@0" ObjectIDZND0="34265@x" ObjectIDZND1="25969@x" Pin0InfoVect0LinkObjId="EC-YA_DXC.042Ld_0" Pin0InfoVect1LinkObjId="SW-148547_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_130a770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="460,-76 416,-76 416,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cfac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-307 599,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25965@1" ObjectIDZND0="25966@1" Pin0InfoVect0LinkObjId="SW-148530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-307 599,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cfcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-132 599,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25967@1" ObjectIDZND0="g_132f980@1" Pin0InfoVect0LinkObjId="g_132f980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148531_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-132 599,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d0bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-72 599,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34264@x" ObjectIDND1="g_1356f50@0" ObjectIDZND0="25967@0" Pin0InfoVect0LinkObjId="SW-148531_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.043Ld_0" Pin1InfoVect1LinkObjId="g_1356f50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-72 599,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_134f810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-262 599,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40878@1" ObjectIDZND0="25965@0" Pin0InfoVect0LinkObjId="SW-148528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-262 599,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13507a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="562,-218 599,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_134fa70@0" ObjectIDZND0="g_132f980@0" ObjectIDZND1="40879@x" ObjectIDZND2="40878@x" Pin0InfoVect0LinkObjId="g_132f980_0" Pin0InfoVect1LinkObjId="SW-243419_0" Pin0InfoVect2LinkObjId="SW-148530_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_134fa70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="562,-218 599,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1350a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-204 599,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_132f980@0" ObjectIDZND0="g_134fa70@0" ObjectIDZND1="40879@x" ObjectIDZND2="40878@x" Pin0InfoVect0LinkObjId="g_134fa70_0" Pin0InfoVect1LinkObjId="SW-243419_0" Pin0InfoVect2LinkObjId="SW-148530_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_132f980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="599,-204 599,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fb670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-45 599,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34264@0" ObjectIDZND0="25967@x" ObjectIDZND1="g_1356f50@0" Pin0InfoVect0LinkObjId="SW-148531_0" Pin0InfoVect1LinkObjId="g_1356f50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.043Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="599,-45 599,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fb8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-79 599,-79 599,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1356f50@0" ObjectIDZND0="34264@x" ObjectIDZND1="25967@x" Pin0InfoVect0LinkObjId="EC-YA_DXC.043Ld_0" Pin0InfoVect1LinkObjId="SW-148531_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1356f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="643,-79 599,-79 599,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fbb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-309 759,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25971@1" ObjectIDZND0="25972@1" Pin0InfoVect0LinkObjId="SW-148564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,-309 759,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fbd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-134 759,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40881@1" ObjectIDZND0="g_13008c0@1" Pin0InfoVect0LinkObjId="g_13008c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,-134 759,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1390af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-264 759,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40880@1" ObjectIDZND0="25971@0" Pin0InfoVect0LinkObjId="SW-148562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148564_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,-264 759,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1391a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="722,-220 759,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1390d50@0" ObjectIDZND0="g_13008c0@0" ObjectIDZND1="40880@0" ObjectIDZND2="40882@x" Pin0InfoVect0LinkObjId="g_13008c0_0" Pin0InfoVect1LinkObjId="SW-148564_0" Pin0InfoVect2LinkObjId="SW-243436_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1390d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="722,-220 759,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1391ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-206 759,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13008c0@0" ObjectIDZND0="g_1390d50@0" ObjectIDZND1="40880@0" ObjectIDZND2="40882@x" Pin0InfoVect0LinkObjId="g_1390d50_0" Pin0InfoVect1LinkObjId="SW-148564_0" Pin0InfoVect2LinkObjId="SW-243436_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13008c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="759,-206 759,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1321610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-76 759,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="40883@0" ObjectIDZND0="40912@x" ObjectIDZND1="40881@x" Pin0InfoVect0LinkObjId="CB-YA_DXC.YA_DXC_Cb1_0" Pin0InfoVect1LinkObjId="SW-243435_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="796,-76 759,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1321870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-47 759,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40912@0" ObjectIDZND0="40883@x" ObjectIDZND1="40881@x" Pin0InfoVect0LinkObjId="SW-243437_0" Pin0InfoVect1LinkObjId="SW-243435_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YA_DXC.YA_DXC_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="759,-47 759,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1321ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-76 759,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="40912@x" ObjectIDND1="40883@x" ObjectIDZND0="40881@0" Pin0InfoVect0LinkObjId="SW-243435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-YA_DXC.YA_DXC_Cb1_0" Pin1InfoVect1LinkObjId="SW-243437_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,-76 759,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1321d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,-76 832,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1320b80@0" ObjectIDZND0="40883@1" Pin0InfoVect0LinkObjId="SW-243437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1320b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-76 832,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ae740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-311 948,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40887@1" ObjectIDZND0="40888@1" Pin0InfoVect0LinkObjId="SW-243450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243481_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-311 948,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ae9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-136 948,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40886@1" ObjectIDZND0="g_13b4bc0@1" Pin0InfoVect0LinkObjId="g_13b4bc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-136 948,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13af810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-76 948,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_13ada70@0" ObjectIDZND0="40886@0" Pin0InfoVect0LinkObjId="SW-243451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13d7920_0" Pin1InfoVect1LinkObjId="g_13ada70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-76 948,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1312710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-264 948,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40889@1" ObjectIDZND0="40887@0" Pin0InfoVect0LinkObjId="SW-243481_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-264 948,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13136a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-222 948,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1312970@0" ObjectIDZND0="g_13b4bc0@0" ObjectIDZND1="40889@x" ObjectIDZND2="40885@x" Pin0InfoVect0LinkObjId="g_13b4bc0_0" Pin0InfoVect1LinkObjId="SW-243450_0" Pin0InfoVect2LinkObjId="SW-243452_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1312970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="911,-222 948,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1313900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-208 948,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13b4bc0@0" ObjectIDZND0="g_1312970@0" ObjectIDZND1="40889@x" ObjectIDZND2="40885@x" Pin0InfoVect0LinkObjId="g_1312970_0" Pin0InfoVect1LinkObjId="SW-243450_0" Pin0InfoVect2LinkObjId="SW-243452_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b4bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="948,-208 948,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137a660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-49 948,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="40886@x" ObjectIDZND1="g_13ada70@0" Pin0InfoVect0LinkObjId="SW-243451_0" Pin0InfoVect1LinkObjId="g_13ada70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d7920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="948,-49 948,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-83 948,-83 948,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_13ada70@0" ObjectIDZND0="0@x" ObjectIDZND1="40886@x" Pin0InfoVect0LinkObjId="g_13d7920_0" Pin0InfoVect1LinkObjId="SW-243451_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13ada70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="992,-83 948,-83 948,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1381270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-317 1109,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_137aaf0@0" ObjectIDZND0="40905@1" Pin0InfoVect0LinkObjId="SW-243555_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_137aaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-317 1109,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13814d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-269 1109,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40906@1" ObjectIDZND0="g_137aaf0@1" Pin0InfoVect0LinkObjId="g_137aaf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-269 1109,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1384250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-126 1109,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1381730@1" Pin0InfoVect0LinkObjId="g_1381730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d7920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-126 1109,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13851e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-222 1109,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_13844b0@0" ObjectIDZND0="g_1381730@0" ObjectIDZND1="40906@x" Pin0InfoVect0LinkObjId="g_1381730_0" Pin0InfoVect1LinkObjId="SW-243555_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13844b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-222 1109,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1385440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-203 1109,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1381730@0" ObjectIDZND0="g_13844b0@0" ObjectIDZND1="40906@x" Pin0InfoVect0LinkObjId="g_13844b0_0" Pin0InfoVect1LinkObjId="SW-243555_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1381730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-203 1109,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13856a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-222 1109,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1381730@0" ObjectIDND1="g_13844b0@0" ObjectIDZND0="40906@0" Pin0InfoVect0LinkObjId="SW-243555_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1381730_0" Pin1InfoVect1LinkObjId="g_13844b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-222 1109,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12cca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-497 534,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="40868@0" ObjectIDZND0="g_12c9ea0@0" Pin0InfoVect0LinkObjId="g_12c9ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-497 534,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d2bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-397 534,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25958@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13e22a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-397 534,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d3690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-469 534,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12d2e10@0" ObjectIDZND0="40868@1" Pin0InfoVect0LinkObjId="SW-148491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d2e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-469 534,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d4620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="511,-425 534,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_12d38f0@0" ObjectIDZND0="25958@x" ObjectIDZND1="g_12d2e10@0" Pin0InfoVect0LinkObjId="SW-148491_0" Pin0InfoVect1LinkObjId="g_12d2e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d38f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="511,-425 534,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d5110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-414 534,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25958@1" ObjectIDZND0="g_12d38f0@0" ObjectIDZND1="g_12d2e10@0" Pin0InfoVect0LinkObjId="g_12d38f0_0" Pin0InfoVect1LinkObjId="g_12d2e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="534,-414 534,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d5370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-425 534,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_12d38f0@0" ObjectIDND1="25958@x" ObjectIDZND0="g_12d2e10@1" Pin0InfoVect0LinkObjId="g_12d2e10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_12d38f0_0" Pin1InfoVect1LinkObjId="SW-148491_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-425 534,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d5d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1684,-349 1684,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25960@0" ObjectIDZND0="40907@0" Pin0InfoVect0LinkObjId="g_12d5f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1684,-349 1684,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d5f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1871,-351 1871,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40903@0" ObjectIDZND0="40907@0" Pin0InfoVect0LinkObjId="g_12d5d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1871,-351 1871,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d61c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-346 1501,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25963@0" ObjectIDZND0="40907@0" Pin0InfoVect0LinkObjId="g_12d5d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-346 1501,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d7000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-304 1502,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25962@1" ObjectIDZND0="25963@1" Pin0InfoVect0LinkObjId="SW-148513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-304 1502,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d7260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-129 1502,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25964@1" ObjectIDZND0="g_12dcc60@1" Pin0InfoVect0LinkObjId="g_12dcc60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-129 1502,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d8130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-69 1502,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34263@x" ObjectIDND1="g_12d63f0@0" ObjectIDZND0="25964@0" Pin0InfoVect0LinkObjId="SW-148514_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.047Ld_0" Pin1InfoVect1LinkObjId="g_12d63f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-69 1502,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b9450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-257 1502,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40890@1" ObjectIDZND0="25962@0" Pin0InfoVect0LinkObjId="SW-148511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-257 1502,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bd6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-42 1502,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34263@0" ObjectIDZND0="25964@x" ObjectIDZND1="g_12d63f0@0" Pin0InfoVect0LinkObjId="SW-148514_0" Pin0InfoVect1LinkObjId="g_12d63f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.047Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-42 1502,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bd930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1546,-76 1502,-76 1502,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_12d63f0@0" ObjectIDZND0="25964@x" ObjectIDZND1="34263@x" Pin0InfoVect0LinkObjId="SW-148514_0" Pin0InfoVect1LinkObjId="EC-YA_DXC.047Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d63f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1546,-76 1502,-76 1502,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12be8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-307 1685,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25959@1" ObjectIDZND0="25960@1" Pin0InfoVect0LinkObjId="SW-148496_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-307 1685,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12beb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-132 1685,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25961@1" ObjectIDZND0="g_12c4520@1" Pin0InfoVect0LinkObjId="g_12c4520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148497_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-132 1685,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bf9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-72 1685,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34262@x" ObjectIDND1="g_12bdb90@0" ObjectIDZND0="25961@0" Pin0InfoVect0LinkObjId="SW-148497_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.048Ld_0" Pin1InfoVect1LinkObjId="g_12bdb90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-72 1685,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12adb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-262 1685,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40892@1" ObjectIDZND0="25959@0" Pin0InfoVect0LinkObjId="SW-148494_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-262 1685,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b1e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-45 1685,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34262@0" ObjectIDZND0="25961@x" ObjectIDZND1="g_12bdb90@0" Pin0InfoVect0LinkObjId="SW-148497_0" Pin0InfoVect1LinkObjId="g_12bdb90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.048Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-45 1685,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b2070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-79 1685,-79 1685,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_12bdb90@0" ObjectIDZND0="25961@x" ObjectIDZND1="34262@x" Pin0InfoVect0LinkObjId="SW-148497_0" Pin0InfoVect1LinkObjId="EC-YA_DXC.048Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12bdb90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-79 1685,-79 1685,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b3000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-311 1872,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40902@1" ObjectIDZND0="40903@1" Pin0InfoVect0LinkObjId="SW-243599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-311 1872,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b3260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-136 1872,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40901@1" ObjectIDZND0="g_12524f0@1" Pin0InfoVect0LinkObjId="g_12524f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-136 1872,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b40d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-76 1872,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_12b22d0@0" ObjectIDZND0="40901@0" Pin0InfoVect0LinkObjId="SW-243600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13d7920_0" Pin1InfoVect1LinkObjId="g_12b22d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-76 1872,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1259ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-264 1872,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40904@1" ObjectIDZND0="40902@0" Pin0InfoVect0LinkObjId="SW-243598_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-264 1872,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125e160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-49 1872,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="40901@x" ObjectIDZND1="g_12b22d0@0" Pin0InfoVect0LinkObjId="SW-243600_0" Pin0InfoVect1LinkObjId="g_12b22d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d7920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-49 1872,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1916,-83 1872,-83 1872,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_12b22d0@0" ObjectIDZND0="40901@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-243600_0" Pin0InfoVect1LinkObjId="g_13d7920_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b22d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1916,-83 1872,-83 1872,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125e620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-397 1620,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25957@0" ObjectIDZND0="40907@0" Pin0InfoVect0LinkObjId="g_12d5d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-397 1620,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e7240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1465,-215 1502,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12b96b0@0" ObjectIDZND0="g_12dcc60@0" ObjectIDZND1="40890@x" ObjectIDZND2="40891@x" Pin0InfoVect0LinkObjId="g_12dcc60_0" Pin0InfoVect1LinkObjId="SW-148513_0" Pin0InfoVect2LinkObjId="SW-243482_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b96b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1465,-215 1502,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e7d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-215 1502,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_12b96b0@0" ObjectIDND1="40890@x" ObjectIDND2="40891@x" ObjectIDZND0="g_12dcc60@0" Pin0InfoVect0LinkObjId="g_12dcc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12b96b0_0" Pin1InfoVect1LinkObjId="SW-148513_0" Pin1InfoVect2LinkObjId="SW-243482_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-215 1502,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e7f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1648,-218 1685,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12addf0@0" ObjectIDZND0="g_12c4520@0" ObjectIDZND1="40892@x" ObjectIDZND2="40893@x" Pin0InfoVect0LinkObjId="g_12c4520_0" Pin0InfoVect1LinkObjId="SW-148496_0" Pin0InfoVect2LinkObjId="SW-243497_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12addf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1648,-218 1685,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e8a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-218 1685,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_12addf0@0" ObjectIDND1="40892@x" ObjectIDND2="40893@x" ObjectIDZND0="g_12c4520@0" Pin0InfoVect0LinkObjId="g_12c4520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12addf0_0" Pin1InfoVect1LinkObjId="SW-148496_0" Pin1InfoVect2LinkObjId="SW-243497_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-218 1685,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e8ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1835,-222 1872,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_125a140@0" ObjectIDZND0="g_12524f0@0" ObjectIDZND1="40904@x" ObjectIDZND2="40900@x" Pin0InfoVect0LinkObjId="g_12524f0_0" Pin0InfoVect1LinkObjId="SW-243599_0" Pin0InfoVect2LinkObjId="SW-243601_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_125a140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1835,-222 1872,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e99f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-222 1872,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_125a140@0" ObjectIDND1="40904@x" ObjectIDND2="40900@x" ObjectIDZND0="g_12524f0@0" Pin0InfoVect0LinkObjId="g_12524f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_125a140_0" Pin1InfoVect1LinkObjId="SW-243599_0" Pin1InfoVect2LinkObjId="SW-243601_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-222 1872,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f16d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2004,-399 2004,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40869@0" ObjectIDZND0="40907@0" Pin0InfoVect0LinkObjId="g_12d5d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2004,-399 2004,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f21b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2004,-471 2004,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12f1930@0" ObjectIDZND0="40870@1" Pin0InfoVect0LinkObjId="SW-243400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f1930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2004,-471 2004,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f3140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1981,-427 2004,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_12f2410@0" ObjectIDZND0="40869@x" ObjectIDZND1="g_12f1930@0" Pin0InfoVect0LinkObjId="SW-243400_0" Pin0InfoVect1LinkObjId="g_12f1930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f2410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1981,-427 2004,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f33a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2004,-416 2004,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40869@1" ObjectIDZND0="g_12f2410@0" ObjectIDZND1="g_12f1930@0" Pin0InfoVect0LinkObjId="g_12f2410_0" Pin0InfoVect1LinkObjId="g_12f1930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2004,-416 2004,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f3600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2004,-427 2004,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="40869@x" ObjectIDND1="g_12f2410@0" ObjectIDZND0="g_12f1930@1" Pin0InfoVect0LinkObjId="g_12f1930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-243400_0" Pin1InfoVect1LinkObjId="g_12f2410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2004,-427 2004,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a8310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-454 1261,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40871@1" ObjectIDZND0="40913@1" Pin0InfoVect0LinkObjId="SW-185367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-454 1261,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a8570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1340,-454 1318,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40914@0" ObjectIDZND0="40871@0" Pin0InfoVect0LinkObjId="SW-185363_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1340,-454 1318,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ae6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1420,-418 1420,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40873@1" ObjectIDZND0="40874@1" Pin0InfoVect0LinkObjId="SW-185368_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185368_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1420,-418 1420,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ae930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1420,-401 1420,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40873@0" ObjectIDZND0="40907@0" Pin0InfoVect0LinkObjId="g_12d5d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1420,-401 1420,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14af160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-454 1420,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40914@1" ObjectIDZND0="40874@0" Pin0InfoVect0LinkObjId="SW-185368_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185367_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-454 1420,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14af3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1179,-381 1179,-454 1244,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25931@0" ObjectIDZND0="40913@0" Pin0InfoVect0LinkObjId="SW-185367_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e22a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1179,-381 1179,-454 1244,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b1350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="906,-509 926,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_14b08d0@0" ObjectIDZND0="25973@x" ObjectIDZND1="40864@x" Pin0InfoVect0LinkObjId="g_13e2080_0" Pin0InfoVect1LinkObjId="SW-148485_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14b08d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="906,-509 926,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b1e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-527 926,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25973@0" ObjectIDZND0="g_14b08d0@0" ObjectIDZND1="40864@x" Pin0InfoVect0LinkObjId="g_14b08d0_0" Pin0InfoVect1LinkObjId="SW-148485_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e2080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="926,-527 926,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b20a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-509 926,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_14b08d0@0" ObjectIDND1="25973@x" ObjectIDZND0="40864@0" Pin0InfoVect0LinkObjId="SW-148485_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14b08d0_0" Pin1InfoVect1LinkObjId="g_13e2080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-509 926,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b3030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1597,-511 1620,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_14b2300@0" ObjectIDZND0="25974@x" ObjectIDZND1="40865@x" Pin0InfoVect0LinkObjId="g_13ac010_0" Pin0InfoVect1LinkObjId="SW-148489_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14b2300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1597,-511 1620,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-531 1620,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25974@0" ObjectIDZND0="g_14b2300@0" ObjectIDZND1="40865@x" Pin0InfoVect0LinkObjId="g_14b2300_0" Pin0InfoVect1LinkObjId="SW-148489_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13ac010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-531 1620,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-511 1620,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_14b2300@0" ObjectIDND1="25974@x" ObjectIDZND0="40865@0" Pin0InfoVect0LinkObjId="SW-148489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14b2300_0" Pin1InfoVect1LinkObjId="g_13ac010_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-511 1620,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1267910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-348 599,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25966@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13e22a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-348 599,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1268a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-242 1502,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40890@0" ObjectIDZND0="g_12b96b0@0" ObjectIDZND1="g_12dcc60@0" ObjectIDZND2="40891@x" Pin0InfoVect0LinkObjId="g_12b96b0_0" Pin0InfoVect1LinkObjId="g_12dcc60_0" Pin0InfoVect2LinkObjId="SW-243482_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-242 1502,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1268ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-224 1502,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40890@x" ObjectIDND1="40891@x" ObjectIDZND0="g_12b96b0@0" ObjectIDZND1="g_12dcc60@0" Pin0InfoVect0LinkObjId="g_12b96b0_0" Pin0InfoVect1LinkObjId="g_12dcc60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148513_0" Pin1InfoVect1LinkObjId="SW-243482_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-224 1502,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1269770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-245 1685,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40892@0" ObjectIDZND0="g_12addf0@0" ObjectIDZND1="g_12c4520@0" ObjectIDZND2="40893@x" Pin0InfoVect0LinkObjId="g_12addf0_0" Pin0InfoVect1LinkObjId="g_12c4520_0" Pin0InfoVect2LinkObjId="SW-243497_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-245 1685,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12699b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-226 1685,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40892@x" ObjectIDND1="40893@x" ObjectIDZND0="g_12addf0@0" ObjectIDZND1="g_12c4520@0" Pin0InfoVect0LinkObjId="g_12addf0_0" Pin0InfoVect1LinkObjId="g_12c4520_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148496_0" Pin1InfoVect1LinkObjId="SW-243497_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-226 1685,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-215 416,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_127f470@0" ObjectIDND1="g_13c7050@0" ObjectIDZND0="40876@x" ObjectIDZND1="40877@x" Pin0InfoVect0LinkObjId="SW-148548_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_127f470_0" Pin1InfoVect1LinkObjId="g_13c7050_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="416,-215 416,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-224 416,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_127f470@0" ObjectIDND1="g_13c7050@0" ObjectIDND2="40877@x" ObjectIDZND0="40876@0" Pin0InfoVect0LinkObjId="SW-148548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_127f470_0" Pin1InfoVect1LinkObjId="g_13c7050_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-224 416,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126e780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-220 759,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1390d50@0" ObjectIDND1="g_13008c0@0" ObjectIDZND0="40880@x" ObjectIDZND1="40882@x" Pin0InfoVect0LinkObjId="SW-148564_0" Pin0InfoVect1LinkObjId="SW-243436_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1390d50_0" Pin1InfoVect1LinkObjId="g_13008c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="759,-220 759,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-231 759,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1390d50@0" ObjectIDND1="g_13008c0@0" ObjectIDND2="40882@x" ObjectIDZND0="40880@0" Pin0InfoVect0LinkObjId="SW-148564_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1390d50_0" Pin1InfoVect1LinkObjId="g_13008c0_0" Pin1InfoVect2LinkObjId="SW-243436_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,-231 759,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126f4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-222 948,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1312970@0" ObjectIDND1="g_13b4bc0@0" ObjectIDZND0="40889@x" ObjectIDZND1="40885@x" Pin0InfoVect0LinkObjId="SW-243450_0" Pin0InfoVect1LinkObjId="SW-243452_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1312970_0" Pin1InfoVect1LinkObjId="g_13b4bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="948,-222 948,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126f730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-232 948,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1312970@0" ObjectIDND1="g_13b4bc0@0" ObjectIDND2="40885@x" ObjectIDZND0="40889@0" Pin0InfoVect0LinkObjId="SW-243450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1312970_0" Pin1InfoVect1LinkObjId="g_13b4bc0_0" Pin1InfoVect2LinkObjId="SW-243452_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-232 948,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1271ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-477 1620,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40865@1" ObjectIDZND0="25956@1" Pin0InfoVect0LinkObjId="SW-148487_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148489_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-477 1620,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1272120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2004,-499 2004,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="40870@0" ObjectIDZND0="g_1272380@0" Pin0InfoVect0LinkObjId="g_1272380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2004,-499 2004,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12740d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-818 587,-827 631,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25953@1" ObjectIDZND0="25952@x" ObjectIDZND1="g_1415f30@0" ObjectIDZND2="g_13d7140@0" Pin0InfoVect0LinkObjId="SW-148479_0" Pin0InfoVect1LinkObjId="g_1415f30_0" Pin0InfoVect2LinkObjId="g_13d7140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,-818 587,-827 631,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1274bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-814 631,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25952@1" ObjectIDZND0="25953@x" ObjectIDZND1="g_1415f30@0" ObjectIDZND2="g_13d7140@0" Pin0InfoVect0LinkObjId="SW-148480_0" Pin0InfoVect1LinkObjId="g_1415f30_0" Pin0InfoVect2LinkObjId="g_13d7140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="631,-814 631,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1274e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-827 631,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25953@x" ObjectIDND1="25952@x" ObjectIDZND0="g_1415f30@0" ObjectIDZND1="g_13d7140@0" Pin0InfoVect0LinkObjId="g_1415f30_0" Pin0InfoVect1LinkObjId="g_13d7140_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148480_0" Pin1InfoVect1LinkObjId="SW-148479_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="631,-827 631,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1275080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="825,-942 825,-952 858,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25945@0" ObjectIDZND0="25946@x" ObjectIDZND1="25947@x" ObjectIDZND2="g_13c21f0@0" Pin0InfoVect0LinkObjId="SW-148384_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_13c21f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="825,-942 825,-952 858,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1275b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-903 858,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25946@1" ObjectIDZND0="25945@x" ObjectIDZND1="25947@x" ObjectIDZND2="g_13c21f0@0" Pin0InfoVect0LinkObjId="SW-148383_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_13c21f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="858,-903 858,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1275dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-952 858,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25945@x" ObjectIDND1="25946@x" ObjectIDZND0="25947@x" ObjectIDZND1="g_13c21f0@0" ObjectIDZND2="34312@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13c21f0_0" Pin0InfoVect2LinkObjId="g_13c2cc0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148383_0" Pin1InfoVect1LinkObjId="SW-148384_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="858,-952 858,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1276030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-966 1273,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25937@0" ObjectIDZND0="34317@1" ObjectIDZND1="25936@x" ObjectIDZND2="25935@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-148336_0" Pin0InfoVect2LinkObjId="SW-148335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148347_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-966 1273,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1276d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-978 1273,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34317@1" ObjectIDZND0="25937@x" ObjectIDZND1="25936@x" ObjectIDZND2="25935@x" Pin0InfoVect0LinkObjId="SW-148347_0" Pin0InfoVect1LinkObjId="SW-148336_0" Pin0InfoVect2LinkObjId="SW-148335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1276030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-978 1273,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1276fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-966 1273,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="25937@x" ObjectIDND1="34317@1" ObjectIDND2="25935@x" ObjectIDZND0="25936@1" Pin0InfoVect0LinkObjId="SW-148336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148347_0" Pin1InfoVect1LinkObjId="g_1276030_1" Pin1InfoVect2LinkObjId="SW-148335_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-966 1273,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1277200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-966 1238,-966 1238,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="25937@x" ObjectIDND1="34317@1" ObjectIDND2="25936@x" ObjectIDZND0="25935@0" Pin0InfoVect0LinkObjId="SW-148335_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148347_0" Pin1InfoVect1LinkObjId="g_1276030_1" Pin1InfoVect2LinkObjId="SW-148336_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-966 1238,-966 1238,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1277460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1656,-928 1656,-941 1690,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="25940@0" ObjectIDZND0="25941@x" ObjectIDZND1="25942@x" ObjectIDZND2="34314@1" Pin0InfoVect0LinkObjId="SW-148360_0" Pin0InfoVect1LinkObjId="SW-148371_0" Pin0InfoVect2LinkObjId="g_13eb320_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1656,-928 1656,-941 1690,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12776c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-217 647,-231 599,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40879@0" ObjectIDZND0="g_134fa70@0" ObjectIDZND1="g_132f980@0" ObjectIDZND2="40878@x" Pin0InfoVect0LinkObjId="g_134fa70_0" Pin0InfoVect1LinkObjId="g_132f980_0" Pin0InfoVect2LinkObjId="SW-148530_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="647,-217 647,-231 599,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12781b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-218 599,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_134fa70@0" ObjectIDND1="g_132f980@0" ObjectIDZND0="40879@x" ObjectIDZND1="40878@x" Pin0InfoVect0LinkObjId="SW-243419_0" Pin0InfoVect1LinkObjId="SW-148530_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_134fa70_0" Pin1InfoVect1LinkObjId="g_132f980_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="599,-218 599,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1278410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-231 599,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="40879@x" ObjectIDND1="g_134fa70@0" ObjectIDND2="g_132f980@0" ObjectIDZND0="40878@0" Pin0InfoVect0LinkObjId="SW-148530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-243419_0" Pin1InfoVect1LinkObjId="g_134fa70_0" Pin1InfoVect2LinkObjId="g_132f980_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="599,-231 599,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1278670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="464,-212 464,-224 416,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40877@0" ObjectIDZND0="g_127f470@0" ObjectIDZND1="g_13c7050@0" ObjectIDZND2="40876@x" Pin0InfoVect0LinkObjId="g_127f470_0" Pin0InfoVect1LinkObjId="g_13c7050_0" Pin0InfoVect2LinkObjId="SW-148548_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="464,-212 464,-224 416,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12788d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="810,-217 810,-218 810,-231 759,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40882@0" ObjectIDZND0="g_1390d50@0" ObjectIDZND1="g_13008c0@0" ObjectIDZND2="40880@x" Pin0InfoVect0LinkObjId="g_1390d50_0" Pin0InfoVect1LinkObjId="g_13008c0_0" Pin0InfoVect2LinkObjId="SW-148564_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="810,-217 810,-218 810,-231 759,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1278b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="996,-215 996,-232 948,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40885@0" ObjectIDZND0="g_1312970@0" ObjectIDZND1="g_13b4bc0@0" ObjectIDZND2="40889@x" Pin0InfoVect0LinkObjId="g_1312970_0" Pin0InfoVect1LinkObjId="g_13b4bc0_0" Pin0InfoVect2LinkObjId="SW-243450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="996,-215 996,-232 948,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1278da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1872,-249 1872,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40904@0" ObjectIDZND0="g_125a140@0" ObjectIDZND1="g_12524f0@0" ObjectIDZND2="40900@x" Pin0InfoVect0LinkObjId="g_125a140_0" Pin0InfoVect1LinkObjId="g_12524f0_0" Pin0InfoVect2LinkObjId="SW-243601_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1872,-249 1872,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1279000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1550,-207 1550,-224 1502,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="40891@0" ObjectIDZND0="40890@x" ObjectIDZND1="g_12b96b0@0" ObjectIDZND2="g_12dcc60@0" Pin0InfoVect0LinkObjId="SW-148513_0" Pin0InfoVect1LinkObjId="g_12b96b0_0" Pin0InfoVect2LinkObjId="g_12dcc60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1550,-207 1550,-224 1502,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1279260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,-207 1733,-224 1685,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="40893@0" ObjectIDZND0="40892@x" ObjectIDZND1="g_12addf0@0" ObjectIDZND2="g_12c4520@0" Pin0InfoVect0LinkObjId="SW-148496_0" Pin0InfoVect1LinkObjId="g_12addf0_0" Pin0InfoVect2LinkObjId="g_12c4520_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1733,-207 1733,-224 1685,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12794c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1920,-207 1920,-224 1872,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="40900@0" ObjectIDZND0="g_125a140@0" ObjectIDZND1="g_12524f0@0" ObjectIDZND2="40904@x" Pin0InfoVect0LinkObjId="g_125a140_0" Pin0InfoVect1LinkObjId="g_12524f0_0" Pin0InfoVect2LinkObjId="SW-243599_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1920,-207 1920,-224 1872,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1282900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-344 244,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43015@0" ObjectIDZND0="25931@0" Pin0InfoVect0LinkObjId="g_13e22a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-266306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="244,-344 244,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1283580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-303 244,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43014@1" ObjectIDZND0="43015@1" Pin0InfoVect0LinkObjId="SW-266306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-266304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="244,-303 244,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12837b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-128 244,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43016@1" ObjectIDZND0="g_12890f0@1" Pin0InfoVect0LinkObjId="g_12890f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-266307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="244,-128 244,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12845c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-68 244,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="46581@x" ObjectIDND1="g_1282af0@0" ObjectIDZND0="43016@0" Pin0InfoVect0LinkObjId="SW-266307_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_DXC.041Ld_0" Pin1InfoVect1LinkObjId="g_1282af0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="244,-68 244,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12901d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-256 244,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43017@1" ObjectIDZND0="43014@0" Pin0InfoVect0LinkObjId="SW-266304_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-266306_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="244,-256 244,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1291160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="207,-214 244,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1290430@0" ObjectIDZND0="g_12890f0@0" ObjectIDZND1="43017@x" ObjectIDZND2="43018@x" Pin0InfoVect0LinkObjId="g_12890f0_0" Pin0InfoVect1LinkObjId="SW-266306_0" Pin0InfoVect2LinkObjId="SW-266342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1290430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="207,-214 244,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12913c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-200 244,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12890f0@0" ObjectIDZND0="g_1290430@0" ObjectIDZND1="43017@x" ObjectIDZND2="43018@x" Pin0InfoVect0LinkObjId="g_1290430_0" Pin0InfoVect1LinkObjId="SW-266306_0" Pin0InfoVect2LinkObjId="SW-266342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12890f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="244,-200 244,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1294910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-41 244,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46581@0" ObjectIDZND0="43016@x" ObjectIDZND1="g_1282af0@0" Pin0InfoVect0LinkObjId="SW-266307_0" Pin0InfoVect1LinkObjId="g_1282af0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_DXC.041Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="244,-41 244,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1294b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-75 244,-75 244,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1282af0@0" ObjectIDZND0="43016@x" ObjectIDZND1="46581@x" Pin0InfoVect0LinkObjId="SW-266307_0" Pin0InfoVect1LinkObjId="EC-YA_DXC.041Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1282af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="288,-75 244,-75 244,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1295800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-214 244,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1290430@0" ObjectIDND1="g_12890f0@0" ObjectIDZND0="43017@0" ObjectIDZND1="43018@x" Pin0InfoVect0LinkObjId="SW-266306_0" Pin0InfoVect1LinkObjId="SW-266342_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1290430_0" Pin1InfoVect1LinkObjId="g_12890f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="244,-214 244,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12959f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="244,-223 244,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1290430@0" ObjectIDND1="g_12890f0@0" ObjectIDND2="43018@x" ObjectIDZND0="43017@0" Pin0InfoVect0LinkObjId="SW-266306_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1290430_0" Pin1InfoVect1LinkObjId="g_12890f0_0" Pin1InfoVect2LinkObjId="SW-266342_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="244,-223 244,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1295be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="292,-211 292,-223 244,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43018@0" ObjectIDZND0="g_1290430@0" ObjectIDZND1="g_12890f0@0" ObjectIDZND2="43017@0" Pin0InfoVect0LinkObjId="g_1290430_0" Pin0InfoVect1LinkObjId="g_12890f0_0" Pin0InfoVect2LinkObjId="SW-266306_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-266342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="292,-211 292,-223 244,-223 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1273" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1689" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="858" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="631" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1620" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1939" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="926" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40907" cx="1684" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40907" cx="1871" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40907" cx="1501" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40907" cx="1620" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40907" cx="2004" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40907" cx="1420" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="926" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="759" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="947" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1109" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="415" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="534" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="1179" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="599" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25931" cx="244" cy="-381" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-147909" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 177.500000 -926.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25866" ObjectName="DYN-YA_DXC"/>
     <cge:Meas_Ref ObjectId="147909"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1454510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">大新仓变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ac230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -536.000000) translate(0,12)">SZ11-10000/35GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ac230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -536.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ac230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -536.000000) translate(0,42)">yn,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ac230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -536.000000) translate(0,57)">Ud=7.36%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_123f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1441.000000 -587.000000) translate(0,12)">SZ9-4000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_123f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1441.000000 -587.000000) translate(0,27)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_123f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1441.000000 -587.000000) translate(0,42)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_123f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1441.000000 -587.000000) translate(0,57)">Ud=7.58%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_90f760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1328eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cc160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -28.600000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cc160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -28.600000) translate(0,27)">SH15-M-50/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cc830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -391.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1395480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 936.000000 -457.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -690.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ac7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 936.000000 -646.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ac9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1628.000000 -713.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14111c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -658.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14113a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -754.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d8c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1598.000000 -916.000000) translate(0,12)">34367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d8fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -812.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -895.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1726.000000 -968.000000) translate(0,12)">3433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1699.000000 -854.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.000000 -804.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 639.000000 -803.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1281.000000 -803.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -940.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1281.000000 -911.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135e3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1315.000000 -993.000000) translate(0,12)">3423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135e5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -857.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135e7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 865.000000 -810.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135e990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -928.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 865.000000 -892.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135ed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -852.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_142bbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -1017.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1394670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -1054.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_136ca90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -197.000000 -55.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_136db60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -65.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_136db60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -65.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_136de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -95.000000) translate(0,17)">5817032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_132e730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -453.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1307bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1889.000000 -821.000000) translate(0,12)">3441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1308220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1902.000000 -992.600000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1308220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1902.000000 -992.600000) translate(0,27)">S11-RL-50/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1308a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 604.000000 -974.600000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1308a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 604.000000 -974.600000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c7da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -298.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c83d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -118.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13306d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.000000 -301.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1330d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -121.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 769.000000 -303.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138ada0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -123.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b1b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.571429 -24.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -305.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b5f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 -125.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12d55d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -585.000000) translate(0,12)">10kVⅠ母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12dd9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1512.000000 -298.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ddea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1509.000000 -118.000000) translate(0,12)">0476</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a6ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -301.000000) translate(0,12)">048</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a7520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 -121.000000) translate(0,12)">0486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_124f730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1852.571429 -24.000000) translate(0,12)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1253240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 -305.000000) translate(0,12)">049</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1253870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1879.000000 -125.000000) translate(0,12)">0496</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12f3860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1950.000000 -593.000000) translate(0,12)">10kVⅡ母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12f4e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2037.571429 -50.000000) translate(0,12)">预留2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12f58b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2153.571429 -50.000000) translate(0,12)">预留3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14af620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 -478.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14afc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 972.000000 -924.000000) translate(0,12)">3413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14afe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 679.000000 -785.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b00d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 970.000000 -604.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b0650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1654.000000 -609.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b3fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 394.000000 -19.000000) translate(0,12)">光禄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 473.000000 -205.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 586.000000 -20.000000) translate(0,12)">东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b5270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -205.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b54f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 816.000000 -205.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b5730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 -102.000000) translate(0,12)">04467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b5970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 72.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b60a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -205.000000) translate(0,12)">04560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b64f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 -478.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 -430.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1559.000000 -195.000000) translate(0,12)">04760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 -17.000000) translate(0,12)">工业园区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b7970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1668.000000 -21.000000) translate(0,12)">左门线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1742.000000 -197.000000) translate(0,12)">04860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b8470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1929.000000 -201.000000) translate(0,12)">04960</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b86b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 -371.000000) translate(0,12)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b88f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -1109.000000) translate(0,12)">35kV姚新线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b9130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -1104.000000) translate(0,12)">35kV西大连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b9cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 -1102.000000) translate(0,12)">35kV大仓南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_126a230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -1036.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_126b190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -630.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_126bc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -460.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_126bfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2018.000000 -462.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_126f990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -309.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_126fe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -41.000000 -119.000000) translate(0,17)">4665</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1289e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -297.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128a470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 -117.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 171.000000 -11.000000) translate(0,12)">10kV工业园区II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1298940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -195.000000) translate(0,12)">04160</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="1" width="5" x="648" y="-924"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(0,255,0)" stroke-width="1" width="5" x="551" y="-546"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(0,255,0)" stroke-width="1" width="5" x="2021" y="-548"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_DXC.YA_DXC_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-746 2014,-746 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25930" ObjectName="BS-YA_DXC.YA_DXC_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   <polyline fill="none" opacity="0" points="574,-746 2014,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_DXC.YA_DXC_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="207,-381 1214,-381 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25931" ObjectName="BS-YA_DXC.YA_DXC_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   <polyline fill="none" opacity="0" points="207,-381 1214,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_DXC.YA_DXC_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1354,-381 2300,-381 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40907" ObjectName="BS-YA_DXC.YA_DXC_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="40907"/></metadata>
   <polyline fill="none" opacity="0" points="1354,-381 2300,-381 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_DXC.YA_DXC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36698"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1585.000000 -527.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1585.000000 -527.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25974" ObjectName="TF-YA_DXC.YA_DXC_2T"/>
    <cge:TPSR_Ref TObjectID="25974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_DXC.YA_DXC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36694"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 891.000000 -523.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 891.000000 -523.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25973" ObjectName="TF-YA_DXC.YA_DXC_1T"/>
    <cge:TPSR_Ref TObjectID="25973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1124.000000 -131.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1124.000000 -131.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1924.000000 -859.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1924.000000 -859.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -994.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -813.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217886" ObjectName="YA_DXC:YA_DXC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219738" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -772.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219738" ObjectName="YA_DXC:YA_DXC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -46.000000 -894.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217886" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -854.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217886" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="176" y="-1024"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="176" y="-1024"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="176" y="-1063"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="176" y="-1063"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="867" y="-852"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="867" y="-852"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1283" y="-857"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1283" y="-857"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1699" y="-854"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1699" y="-854"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="970" y="-604"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="970" y="-604"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1654" y="-609"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1654" y="-609"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1292" y="-478"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1292" y="-478"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="426" y="-298"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="426" y="-298"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="609" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="609" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="769" y="-303"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="769" y="-303"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="958" y="-305"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="958" y="-305"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1512" y="-298"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1512" y="-298"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1695" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1695" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1882" y="-305"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1882" y="-305"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="111,-1052 108,-1055 108,-1000 111,-1003 111,-1052" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="111,-1052 108,-1055 167,-1055 164,-1052 111,-1052" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="111,-1003 108,-1000 167,-1000 164,-1003 111,-1003" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="164,-1052 167,-1055 167,-1000 164,-1003 164,-1052" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="49" stroke="rgb(255,255,255)" width="53" x="111" y="-1052"/>
     <rect fill="none" height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="111" y="-1052"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-162" y="-630"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-162" y="-630"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="254" y="-297"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="254" y="-297"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135f110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 803.000000 1073.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135f2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 1058.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135f4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 1043.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135f670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.000000 566.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135f810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.000000 581.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135fbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1668.000000 568.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135fdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1668.000000 583.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142ae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 776.000000 668.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142b030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 653.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142b1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 638.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142b590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.000000 473.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142b770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 771.000000 458.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142b950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 796.000000 443.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136a620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 1069.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136aab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1203.000000 1054.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136acf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 1039.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136b110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1632.000000 1070.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136b3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1621.000000 1055.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136b610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1646.000000 1040.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136ba30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 673.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136bcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1470.000000 658.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136bf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1495.000000 643.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136c350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1486.000000 464.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136c610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1475.000000 449.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136c850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1500.000000 434.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c87f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 346.000000 -4.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 -19.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c8cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 360.000000 -34.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1331120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.000000 -3.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13313e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 -18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1331620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -33.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 878.000000 -2.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 -17.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_130cab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 892.000000 -32.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12de2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12de580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12de7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a7940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.000000 -2.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a7c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 -17.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 -32.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1253c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1816.000000 -5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1253f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1805.000000 -20.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1254190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1830.000000 -35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12626e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 539.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1262970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 524.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1262bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 509.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1279850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 221.000000 480.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1279d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 221.000000 451.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127a250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 213.000000 421.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127a7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.000000 406.000000) translate(0,12)">F  ：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127fd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 221.000000 465.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127ff60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 221.000000 436.000000) translate(0,12)">3U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1280290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2169.000000 475.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1280510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2169.000000 446.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1280750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2161.000000 416.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1280990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2205.000000 401.000000) translate(0,12)">F  ：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1280bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2169.000000 460.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1280e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2169.000000 431.000000) translate(0,12)">3U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1281140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.000000 843.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12813c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.000000 814.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1281600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2015.000000 784.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1281840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2059.000000 769.000000) translate(0,12)">F  ：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1281a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.000000 828.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1281cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.000000 799.000000) translate(0,12)">3U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1282450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.000000 -111.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12826c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 -95.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1297690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.000000 -12.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1297ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 183.000000 -27.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1297f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 208.000000 -42.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YA_DXC.YA_DXC_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 732.000000 61.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40912" ObjectName="CB-YA_DXC.YA_DXC_Cb1"/>
    <cge:TPSR_Ref TObjectID="40912"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -1073.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25943"/>
     <cge:Term_Ref ObjectID="36632"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -1073.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25943"/>
     <cge:Term_Ref ObjectID="36632"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -1073.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25943"/>
     <cge:Term_Ref ObjectID="36632"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -1069.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25933"/>
     <cge:Term_Ref ObjectID="36612"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -1069.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25933"/>
     <cge:Term_Ref ObjectID="36612"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -1069.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25933"/>
     <cge:Term_Ref ObjectID="36612"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1691.000000 -1069.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25938"/>
     <cge:Term_Ref ObjectID="36622"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1691.000000 -1069.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25938"/>
     <cge:Term_Ref ObjectID="36622"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1691.000000 -1069.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25938"/>
     <cge:Term_Ref ObjectID="36622"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -667.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25948"/>
     <cge:Term_Ref ObjectID="36642"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -667.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25948"/>
     <cge:Term_Ref ObjectID="36642"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -667.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25948"/>
     <cge:Term_Ref ObjectID="36642"/>
    <cge:TPSR_Ref TObjectID="25948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 -672.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25950"/>
     <cge:Term_Ref ObjectID="36646"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 -672.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25950"/>
     <cge:Term_Ref ObjectID="36646"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 -672.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25950"/>
     <cge:Term_Ref ObjectID="36646"/>
    <cge:TPSR_Ref TObjectID="25950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 839.000000 -471.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25954"/>
     <cge:Term_Ref ObjectID="36654"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 839.000000 -471.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25954"/>
     <cge:Term_Ref ObjectID="36654"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 839.000000 -471.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25954"/>
     <cge:Term_Ref ObjectID="36654"/>
    <cge:TPSR_Ref TObjectID="25954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1539.000000 -466.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25956"/>
     <cge:Term_Ref ObjectID="36658"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1539.000000 -466.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25956"/>
     <cge:Term_Ref ObjectID="36658"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1539.000000 -466.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25956"/>
     <cge:Term_Ref ObjectID="36658"/>
    <cge:TPSR_Ref TObjectID="25956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243659" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -537.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40871"/>
     <cge:Term_Ref ObjectID="61879"/>
    <cge:TPSR_Ref TObjectID="40871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243660" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -537.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40871"/>
     <cge:Term_Ref ObjectID="61879"/>
    <cge:TPSR_Ref TObjectID="40871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243665" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -537.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40871"/>
     <cge:Term_Ref ObjectID="61879"/>
    <cge:TPSR_Ref TObjectID="40871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25968"/>
     <cge:Term_Ref ObjectID="36682"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25968"/>
     <cge:Term_Ref ObjectID="36682"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25968"/>
     <cge:Term_Ref ObjectID="36682"/>
    <cge:TPSR_Ref TObjectID="25968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 3.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25965"/>
     <cge:Term_Ref ObjectID="36676"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148302" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 3.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25965"/>
     <cge:Term_Ref ObjectID="36676"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 3.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25965"/>
     <cge:Term_Ref ObjectID="36676"/>
    <cge:TPSR_Ref TObjectID="25965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 3.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40887"/>
     <cge:Term_Ref ObjectID="61907"/>
    <cge:TPSR_Ref TObjectID="40887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 3.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40887"/>
     <cge:Term_Ref ObjectID="61907"/>
    <cge:TPSR_Ref TObjectID="40887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 3.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40887"/>
     <cge:Term_Ref ObjectID="61907"/>
    <cge:TPSR_Ref TObjectID="40887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25962"/>
     <cge:Term_Ref ObjectID="36670"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25962"/>
     <cge:Term_Ref ObjectID="36670"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25962"/>
     <cge:Term_Ref ObjectID="36670"/>
    <cge:TPSR_Ref TObjectID="25962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-148291" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 1.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25959"/>
     <cge:Term_Ref ObjectID="36664"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 1.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25959"/>
     <cge:Term_Ref ObjectID="36664"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148289" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 1.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148289" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25959"/>
     <cge:Term_Ref ObjectID="36664"/>
    <cge:TPSR_Ref TObjectID="25959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243682" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1885.000000 2.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40902"/>
     <cge:Term_Ref ObjectID="61935"/>
    <cge:TPSR_Ref TObjectID="40902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243681" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1885.000000 2.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40902"/>
     <cge:Term_Ref ObjectID="61935"/>
    <cge:TPSR_Ref TObjectID="40902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243675" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1885.000000 2.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40902"/>
     <cge:Term_Ref ObjectID="61935"/>
    <cge:TPSR_Ref TObjectID="40902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-148312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 96.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25971"/>
     <cge:Term_Ref ObjectID="36688"/>
    <cge:TPSR_Ref TObjectID="25971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-148309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 96.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25971"/>
     <cge:Term_Ref ObjectID="36688"/>
    <cge:TPSR_Ref TObjectID="25971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-148274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1055.000000 -579.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25973"/>
     <cge:Term_Ref ObjectID="36695"/>
    <cge:TPSR_Ref TObjectID="25973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-148275" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1055.000000 -579.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25973"/>
     <cge:Term_Ref ObjectID="36695"/>
    <cge:TPSR_Ref TObjectID="25973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-148282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1758.000000 -580.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25974"/>
     <cge:Term_Ref ObjectID="36699"/>
    <cge:TPSR_Ref TObjectID="25974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-148283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1758.000000 -580.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25974"/>
     <cge:Term_Ref ObjectID="36699"/>
    <cge:TPSR_Ref TObjectID="25974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-148284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -477.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-148285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -477.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-148286" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -477.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-243649" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -477.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-148287" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -477.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-243650" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -477.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243650" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25931"/>
     <cge:Term_Ref ObjectID="36609"/>
    <cge:TPSR_Ref TObjectID="25931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-243651" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40907"/>
     <cge:Term_Ref ObjectID="61945"/>
    <cge:TPSR_Ref TObjectID="40907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-243652" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40907"/>
     <cge:Term_Ref ObjectID="61945"/>
    <cge:TPSR_Ref TObjectID="40907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-243653" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243653" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40907"/>
     <cge:Term_Ref ObjectID="61945"/>
    <cge:TPSR_Ref TObjectID="40907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-243657" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -473.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40907"/>
     <cge:Term_Ref ObjectID="61945"/>
    <cge:TPSR_Ref TObjectID="40907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-243654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -473.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40907"/>
     <cge:Term_Ref ObjectID="61945"/>
    <cge:TPSR_Ref TObjectID="40907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-243658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -473.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40907"/>
     <cge:Term_Ref ObjectID="61945"/>
    <cge:TPSR_Ref TObjectID="40907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-148263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2113.000000 -842.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-148264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2113.000000 -842.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-148265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2113.000000 -842.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-243645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2113.000000 -842.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-148266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2113.000000 -842.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="148266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-243646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2113.000000 -842.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25930"/>
     <cge:Term_Ref ObjectID="36608"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-266294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 15.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="266294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43014"/>
     <cge:Term_Ref ObjectID="18958"/>
    <cge:TPSR_Ref TObjectID="43014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-266295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 15.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="266295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43014"/>
     <cge:Term_Ref ObjectID="18958"/>
    <cge:TPSR_Ref TObjectID="43014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-266301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 15.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="266301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43014"/>
     <cge:Term_Ref ObjectID="18958"/>
    <cge:TPSR_Ref TObjectID="43014"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-148334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25934" ObjectName="SW-YA_DXC.YA_DXC_3421SW"/>
     <cge:Meas_Ref ObjectId="148334"/>
    <cge:TPSR_Ref TObjectID="25934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -881.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25936" ObjectName="SW-YA_DXC.YA_DXC_3426SW"/>
     <cge:Meas_Ref ObjectId="148336"/>
    <cge:TPSR_Ref TObjectID="25936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148347">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25937" ObjectName="SW-YA_DXC.YA_DXC_3423SW"/>
     <cge:Meas_Ref ObjectId="148347"/>
    <cge:TPSR_Ref TObjectID="25937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148358">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.000000 -782.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25939" ObjectName="SW-YA_DXC.YA_DXC_3431SW"/>
     <cge:Meas_Ref ObjectId="148358"/>
    <cge:TPSR_Ref TObjectID="25939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.000000 -865.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25941" ObjectName="SW-YA_DXC.YA_DXC_3436SW"/>
     <cge:Meas_Ref ObjectId="148360"/>
    <cge:TPSR_Ref TObjectID="25941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148359">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1647.000000 -877.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25940" ObjectName="SW-YA_DXC.YA_DXC_34367SW"/>
     <cge:Meas_Ref ObjectId="148359"/>
    <cge:TPSR_Ref TObjectID="25940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148371">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1708.000000 -937.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25942" ObjectName="SW-YA_DXC.YA_DXC_3433SW"/>
     <cge:Meas_Ref ObjectId="148371"/>
    <cge:TPSR_Ref TObjectID="25942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 -780.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25944" ObjectName="SW-YA_DXC.YA_DXC_3411SW"/>
     <cge:Meas_Ref ObjectId="148382"/>
    <cge:TPSR_Ref TObjectID="25944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 -862.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25946" ObjectName="SW-YA_DXC.YA_DXC_3416SW"/>
     <cge:Meas_Ref ObjectId="148384"/>
    <cge:TPSR_Ref TObjectID="25946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148383">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 -891.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25945" ObjectName="SW-YA_DXC.YA_DXC_34167SW"/>
     <cge:Meas_Ref ObjectId="148383"/>
    <cge:TPSR_Ref TObjectID="25945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148479">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -773.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25952" ObjectName="SW-YA_DXC.YA_DXC_3901SW"/>
     <cge:Meas_Ref ObjectId="148479"/>
    <cge:TPSR_Ref TObjectID="25952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148480">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -777.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25953" ObjectName="SW-YA_DXC.YA_DXC_39017SW"/>
     <cge:Meas_Ref ObjectId="148480"/>
    <cge:TPSR_Ref TObjectID="25953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 -902.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25935" ObjectName="SW-YA_DXC.YA_DXC_34267SW"/>
     <cge:Meas_Ref ObjectId="148335"/>
    <cge:TPSR_Ref TObjectID="25935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25949" ObjectName="SW-YA_DXC.YA_DXC_3011SW"/>
     <cge:Meas_Ref ObjectId="148406"/>
    <cge:TPSR_Ref TObjectID="25949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1611.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25951" ObjectName="SW-YA_DXC.YA_DXC_3021SW"/>
     <cge:Meas_Ref ObjectId="148444"/>
    <cge:TPSR_Ref TObjectID="25951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 -889.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25947" ObjectName="SW-YA_DXC.YA_DXC_3413SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="25947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 639.000000 -759.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40867" ObjectName="SW-YA_DXC.YA_DXC_39010SW"/>
     <cge:Meas_Ref ObjectId="243386"/>
    <cge:TPSR_Ref TObjectID="40867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.000000 -474.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40864" ObjectName="SW-YA_DXC.YA_DXC_011XC1"/>
     <cge:Meas_Ref ObjectId="148485"/>
    <cge:TPSR_Ref TObjectID="40864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.000000 -396.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25955" ObjectName="SW-YA_DXC.YA_DXC_011XC"/>
     <cge:Meas_Ref ObjectId="148485"/>
    <cge:TPSR_Ref TObjectID="25955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1610.000000 -470.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40865" ObjectName="SW-YA_DXC.YA_DXC_002XC1"/>
     <cge:Meas_Ref ObjectId="148489"/>
    <cge:TPSR_Ref TObjectID="40865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1610.000000 -392.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25957" ObjectName="SW-YA_DXC.YA_DXC_002XC"/>
     <cge:Meas_Ref ObjectId="148489"/>
    <cge:TPSR_Ref TObjectID="25957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1934.000000 -785.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.371429 -88.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25969" ObjectName="SW-YA_DXC.YA_DXC_0426SW"/>
     <cge:Meas_Ref ObjectId="148547"/>
    <cge:TPSR_Ref TObjectID="25969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148548">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 -321.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25970" ObjectName="SW-YA_DXC.YA_DXC_042XC"/>
     <cge:Meas_Ref ObjectId="148548"/>
    <cge:TPSR_Ref TObjectID="25970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148548">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 -235.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40876" ObjectName="SW-YA_DXC.YA_DXC_042XC1"/>
     <cge:Meas_Ref ObjectId="148548"/>
    <cge:TPSR_Ref TObjectID="40876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.000000 -161.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40877" ObjectName="SW-YA_DXC.YA_DXC_04260SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="40877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148531">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.371429 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25967" ObjectName="SW-YA_DXC.YA_DXC_0436SW"/>
     <cge:Meas_Ref ObjectId="148531"/>
    <cge:TPSR_Ref TObjectID="25967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.000000 -324.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25966" ObjectName="SW-YA_DXC.YA_DXC_043XC"/>
     <cge:Meas_Ref ObjectId="148530"/>
    <cge:TPSR_Ref TObjectID="25966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40878" ObjectName="SW-YA_DXC.YA_DXC_043XC1"/>
     <cge:Meas_Ref ObjectId="148530"/>
    <cge:TPSR_Ref TObjectID="40878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243419">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -166.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40879" ObjectName="SW-YA_DXC.YA_DXC_04360SW"/>
     <cge:Meas_Ref ObjectId="243419"/>
    <cge:TPSR_Ref TObjectID="40879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.371429 -93.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40881" ObjectName="SW-YA_DXC.YA_DXC_0446SW"/>
     <cge:Meas_Ref ObjectId="243435"/>
    <cge:TPSR_Ref TObjectID="40881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 749.000000 -326.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25972" ObjectName="SW-YA_DXC.YA_DXC_044XC"/>
     <cge:Meas_Ref ObjectId="148564"/>
    <cge:TPSR_Ref TObjectID="25972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 749.000000 -240.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40880" ObjectName="SW-YA_DXC.YA_DXC_044XC1"/>
     <cge:Meas_Ref ObjectId="148564"/>
    <cge:TPSR_Ref TObjectID="40880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243436">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.000000 -166.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40882" ObjectName="SW-YA_DXC.YA_DXC_04460SW"/>
     <cge:Meas_Ref ObjectId="243436"/>
    <cge:TPSR_Ref TObjectID="40882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243437">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.000000 -71.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40883" ObjectName="SW-YA_DXC.YA_DXC_04467SW"/>
     <cge:Meas_Ref ObjectId="243437"/>
    <cge:TPSR_Ref TObjectID="40883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.371429 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40886" ObjectName="SW-YA_DXC.YA_DXC_0456SW"/>
     <cge:Meas_Ref ObjectId="243451"/>
    <cge:TPSR_Ref TObjectID="40886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 -328.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40888" ObjectName="SW-YA_DXC.YA_DXC_045XC"/>
     <cge:Meas_Ref ObjectId="243450"/>
    <cge:TPSR_Ref TObjectID="40888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 -242.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40889" ObjectName="SW-YA_DXC.YA_DXC_045XC1"/>
     <cge:Meas_Ref ObjectId="243450"/>
    <cge:TPSR_Ref TObjectID="40889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 -164.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40885" ObjectName="SW-YA_DXC.YA_DXC_04560SW"/>
     <cge:Meas_Ref ObjectId="243452"/>
    <cge:TPSR_Ref TObjectID="40885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243555">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -328.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40905" ObjectName="SW-YA_DXC.YA_DXC_0461XC"/>
     <cge:Meas_Ref ObjectId="243555"/>
    <cge:TPSR_Ref TObjectID="40905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243555">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -245.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40906" ObjectName="SW-YA_DXC.YA_DXC_0461XC1"/>
     <cge:Meas_Ref ObjectId="243555"/>
    <cge:TPSR_Ref TObjectID="40906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 -473.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40868" ObjectName="SW-YA_DXC.YA_DXC_0901XC1"/>
     <cge:Meas_Ref ObjectId="148491"/>
    <cge:TPSR_Ref TObjectID="40868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 -390.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25958" ObjectName="SW-YA_DXC.YA_DXC_0901XC"/>
     <cge:Meas_Ref ObjectId="148491"/>
    <cge:TPSR_Ref TObjectID="25958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.371429 -88.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25964" ObjectName="SW-YA_DXC.YA_DXC_0476SW"/>
     <cge:Meas_Ref ObjectId="148514"/>
    <cge:TPSR_Ref TObjectID="25964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1492.000000 -321.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25963" ObjectName="SW-YA_DXC.YA_DXC_047XC"/>
     <cge:Meas_Ref ObjectId="148513"/>
    <cge:TPSR_Ref TObjectID="25963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1492.000000 -235.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40890" ObjectName="SW-YA_DXC.YA_DXC_047XC1"/>
     <cge:Meas_Ref ObjectId="148513"/>
    <cge:TPSR_Ref TObjectID="40890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1541.000000 -156.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40891" ObjectName="SW-YA_DXC.YA_DXC_04760SW"/>
     <cge:Meas_Ref ObjectId="243482"/>
    <cge:TPSR_Ref TObjectID="40891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1676.371429 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25961" ObjectName="SW-YA_DXC.YA_DXC_0486SW"/>
     <cge:Meas_Ref ObjectId="148497"/>
    <cge:TPSR_Ref TObjectID="25961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 -324.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25960" ObjectName="SW-YA_DXC.YA_DXC_048XC"/>
     <cge:Meas_Ref ObjectId="148496"/>
    <cge:TPSR_Ref TObjectID="25960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40892" ObjectName="SW-YA_DXC.YA_DXC_048XC1"/>
     <cge:Meas_Ref ObjectId="148496"/>
    <cge:TPSR_Ref TObjectID="40892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.000000 -156.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40893" ObjectName="SW-YA_DXC.YA_DXC_04860SW"/>
     <cge:Meas_Ref ObjectId="243497"/>
    <cge:TPSR_Ref TObjectID="40893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243600">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.371429 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40901" ObjectName="SW-YA_DXC.YA_DXC_0496SW"/>
     <cge:Meas_Ref ObjectId="243600"/>
    <cge:TPSR_Ref TObjectID="40901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.000000 -328.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40903" ObjectName="SW-YA_DXC.YA_DXC_049XC"/>
     <cge:Meas_Ref ObjectId="243599"/>
    <cge:TPSR_Ref TObjectID="40903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1862.000000 -242.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40904" ObjectName="SW-YA_DXC.YA_DXC_049XC1"/>
     <cge:Meas_Ref ObjectId="243599"/>
    <cge:TPSR_Ref TObjectID="40904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243601">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1911.000000 -156.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40900" ObjectName="SW-YA_DXC.YA_DXC_04960SW"/>
     <cge:Meas_Ref ObjectId="243601"/>
    <cge:TPSR_Ref TObjectID="40900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1994.000000 -475.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40870" ObjectName="SW-YA_DXC.YA_DXC_0902XC1"/>
     <cge:Meas_Ref ObjectId="243400"/>
    <cge:TPSR_Ref TObjectID="40870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1994.000000 -392.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40869" ObjectName="SW-YA_DXC.YA_DXC_0902XC"/>
     <cge:Meas_Ref ObjectId="243400"/>
    <cge:TPSR_Ref TObjectID="40869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185367">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.000000 -445.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40913" ObjectName="SW-YA_DXC.YA_DXC_012XC"/>
     <cge:Meas_Ref ObjectId="185367"/>
    <cge:TPSR_Ref TObjectID="40913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185367">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 -445.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40914" ObjectName="SW-YA_DXC.YA_DXC_012XC1"/>
     <cge:Meas_Ref ObjectId="185367"/>
    <cge:TPSR_Ref TObjectID="40914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185368">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.000000 -430.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40874" ObjectName="SW-YA_DXC.YA_DXC_0122XC1"/>
     <cge:Meas_Ref ObjectId="185368"/>
    <cge:TPSR_Ref TObjectID="40874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185368">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.000000 -394.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40873" ObjectName="SW-YA_DXC.YA_DXC_0122XC"/>
     <cge:Meas_Ref ObjectId="185368"/>
    <cge:TPSR_Ref TObjectID="40873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-266307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 235.371429 -87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43016" ObjectName="SW-YA_DXC.YA_DXC_0416SW"/>
     <cge:Meas_Ref ObjectId="266307"/>
    <cge:TPSR_Ref TObjectID="43016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-266306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 234.000000 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43015" ObjectName="SW-YA_DXC.YA_DXC_041XC"/>
     <cge:Meas_Ref ObjectId="266306"/>
    <cge:TPSR_Ref TObjectID="43015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-266306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 234.000000 -234.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43017" ObjectName="SW-YA_DXC.YA_DXC_041XC1"/>
     <cge:Meas_Ref ObjectId="266306"/>
    <cge:TPSR_Ref TObjectID="43017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-266342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 -160.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43018" ObjectName="SW-YA_DXC.YA_DXC_04160SW"/>
     <cge:Meas_Ref ObjectId="266342"/>
    <cge:TPSR_Ref TObjectID="43018"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="176" y="-1024"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="176" y="-1063"/></g>
   <g href="35kV大新仓变35kV姚新线341断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="867" y="-852"/></g>
   <g href="35kV大新仓变35kV西大连线342断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1283" y="-857"/></g>
   <g href="35kV大新仓变35kV大仓南线343断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1699" y="-854"/></g>
   <g href="35kV大新仓变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="970" y="-604"/></g>
   <g href="35kV大新仓变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1654" y="-609"/></g>
   <g href="35kV大新仓变10kV分段及备自投012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1292" y="-478"/></g>
   <g href="35kV大新仓变10kV光禄线042断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="426" y="-298"/></g>
   <g href="35kV大新仓变10kV东线043断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="609" y="-301"/></g>
   <g href="35kV大新仓变10kV电容器044断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="769" y="-303"/></g>
   <g href="35kV大新仓变10kV备用一线045断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="958" y="-305"/></g>
   <g href="35kV大新仓变10kV姚安工业园区线047断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1512" y="-298"/></g>
   <g href="35kV大新仓变10kV左门线048断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1695" y="-301"/></g>
   <g href="35kV大新仓变10kV备用二线049断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1882" y="-305"/></g>
   <g href="AVC大新仓站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="111" y="-1052"/></g>
   <g href="35kV大新仓变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-162" y="-630"/></g>
   <g href="35kV大新仓变10kV工业园区Ⅱ回线041断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="254" y="-297"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13d7140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -845.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1415f30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.000000 -848.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_142e7e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 -868.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135db80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.000000 -842.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c21f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.000000 -895.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13e6830">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1364.000000 -900.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1309b20">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 821.500000 -596.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_130a770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.371429 -22.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c7050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.371429 -143.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127f470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.371429 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1356f50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.371429 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132f980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 594.371429 -146.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_134fa70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.371429 -164.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13008c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.371429 -148.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1390d50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.371429 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13ada70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.371429 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b4bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.371429 -150.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1312970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 904.371429 -168.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_137aaf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 -281.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1381730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.371429 -145.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13844b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.371429 -168.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d2e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 525.000000 -433.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d38f0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 457.500000 -432.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d63f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1539.371429 -22.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12dcc60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1497.371429 -143.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b96b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.371429 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12bdb90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.371429 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c4520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.371429 -146.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12addf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1641.371429 -164.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b22d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1909.371429 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12524f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.371429 -150.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125a140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1828.371429 -168.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f1930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1995.000000 -435.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f2410">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1927.500000 -434.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b08d0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 852.000000 -515.628571)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b2300">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1543.000000 -517.628571)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1282af0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 281.371429 -21.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12890f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 239.371429 -142.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1290430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.371429 -160.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_DXC"/>
</svg>