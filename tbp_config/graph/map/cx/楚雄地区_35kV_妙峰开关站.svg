<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-260" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="382 -764 1758 1324">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="7" y1="5" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="21" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <rect height="13" stroke-width="0.424575" width="29" x="15" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1d11e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape32_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="transformer2:shape32_1">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a491c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a49e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a91b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a92710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a93880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a94390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a94dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a95710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa3670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b67610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b68050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b62fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b645c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1334" width="1768" x="377" y="-769"/>
  </g><g id="CircleFilled_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="2058" cy="-542" fill="none" fillStyle="0" rx="13" ry="12.5" stroke="rgb(60,120,255)" stroke-width="0.265306"/>
   <circle DF8003:Layer="PUBLIC" cx="2058" cy="-526" fill="none" fillStyle="0" r="13" stroke="rgb(60,120,255)" stroke-width="0.265306"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1491" x2="1491" y1="-268" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1499" x2="1506" y1="-231" y2="-231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1500" x2="1499" y1="-227" y2="-231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1505" x2="1506" y1="-227" y2="-231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1489" x2="1491" y1="-241" y2="-238"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1489" x2="1491" y1="-235" y2="-238"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1491" x2="1494" y1="-238" y2="-238"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1501" x2="1503" y1="-247" y2="-244"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1501" x2="1503" y1="-241" y2="-244"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1503" x2="1505" y1="-244" y2="-244"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1487" x2="1495" y1="-252" y2="-259"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1495" x2="1495" y1="-259" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1491" x2="1491" y1="-238" y2="-251"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1487" x2="1487" y1="-252" y2="-250"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1503" x2="1503" y1="-244" y2="-268"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1505" x2="1513" y1="-231" y2="-231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1513" x2="1513" y1="-231" y2="-268"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1491" x2="1515" y1="-268" y2="-268"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.475079" x1="1523" x2="1523" y1="-272" y2="-263"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.18239" x1="1530" x2="1530" y1="-267" y2="-269"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.257389" x1="1526" x2="1526" y1="-265" y2="-270"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.294231" x1="1523" x2="1513" y1="-268" y2="-268"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1137" x2="1137" y1="-175" y2="-172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1134" x2="1137" y1="-177" y2="-175"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1140" x2="1137" y1="-177" y2="-175"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1128" x2="1128" y1="-168" y2="-165"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1125" x2="1128" y1="-170" y2="-168"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1131" x2="1128" y1="-170" y2="-168"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1153" x2="1153" y1="-168" y2="-179"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1149" x2="1149" y1="-189" y2="-192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1159" x2="1149" y1="-180" y2="-189"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1114" x2="1118" y1="-174" y2="-173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1114" x2="1118" y1="-178" y2="-179"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1118" x2="1118" y1="-179" y2="-173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1155" x2="1151" y1="-204" y2="-204"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1154" x2="1152" y1="-206" y2="-206"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1156" x2="1150" y1="-202" y2="-202"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1153" x2="1153" y1="-202" y2="-192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1127" x2="1127" y1="-181" y2="-178"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1124" x2="1127" y1="-183" y2="-181"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1130" x2="1127" y1="-183" y2="-181"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1137" x2="1137" y1="-175" y2="-197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1127" x2="1127" y1="-181" y2="-197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1128" x2="1153" y1="-168" y2="-168"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1159" x2="1159" y1="-180" y2="-177"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1153" x2="1127" y1="-197" y2="-197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1680" x2="1680" y1="387" y2="405"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1879" x2="1835" y1="-546" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1835" x2="1835" y1="-546" y2="-533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1836" x2="1836" y1="-502" y2="-486"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2.5" x1="1879" x2="1879" y1="-182" y2="-284"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2.5" x1="2058" x2="2058" y1="-183" y2="-282"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.265306" x1="2054" x2="2058" y1="-528" y2="-524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.265306" x1="2058" x2="2062" y1="-524" y2="-528"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.265306" x1="2058" x2="2058" y1="-520" y2="-524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(255,0,0)" stroke-dasharray="10 5 " stroke-width="1" x1="1840" x2="1840" y1="-236" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(255,0,0)" stroke-dasharray="10 5 " stroke-width="1" x1="1840" x2="2097" y1="-337" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(255,0,0)" stroke-dasharray="10 5 " stroke-width="1" x1="2097" x2="1840" y1="-236" y2="-236"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(255,0,0)" stroke-dasharray="10 5 " stroke-width="1" x1="2097" x2="2097" y1="-337" y2="-236"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(255,0,0)" stroke-dasharray="10 5 " stroke-width="1" x1="1885" x2="2051" y1="-295" y2="-295"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-508 1430,-521 1418,-521 1424,-508 1424,-509 1424,-508 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-410 1418,-398 1430,-398 1424,-410 1424,-409 1424,-410 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,202 1053,189 1041,189 1047,202 1047,201 1047,202 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,213 1053,226 1041,226 1047,213 1047,214 1047,213 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,381 1053,368 1041,368 1047,381 1047,380 1047,381 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,392 1053,405 1041,405 1047,392 1047,393 1047,392 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,283 1275,270 1263,270 1269,283 1269,282 1269,283 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,282 1493,269 1481,269 1487,282 1487,281 1487,282 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,293 1729,306 1717,306 1723,293 1723,294 1723,293 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,269 1729,256 1717,256 1723,269 1723,268 1723,269 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,412 1729,399 1717,399 1723,412 1723,411 1723,412 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-587 1885,-600 1873,-600 1879,-587 1879,-588 1879,-587 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-467 1873,-455 1885,-455 1879,-467 1879,-466 1879,-467 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-481 1885,-493 1873,-493 1879,-481 1879,-482 1879,-481 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-585 2064,-598 2052,-598 2058,-585 2058,-586 2058,-585 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-465 2052,-453 2064,-453 2058,-465 2058,-464 2058,-465 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-479 2064,-491 2052,-491 2058,-479 2058,-480 2058,-479 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-549 2054,-540 2063,-540 2058,-549 2058,-548 2058,-549 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1966,-288 1972,-300 1960,-300 1966,-288 1966,-289 1966,-288 " stroke="rgb(60,120,255)"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.424575" width="29" x="1439" y="-340"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(60,120,255)" stroke-width="1" width="4" x="1489" y="-261"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="5" x="1151" y="-192"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" fillStyle="1" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="9" x="1675" y="405"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" fillStyle="1" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="9" x="1831" y="-486"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196839">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1415.000000 -30.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30149" ObjectName="SW-CX_MF.CX_MF_3711SW"/>
     <cge:Meas_Ref ObjectId="196839"/>
    <cge:TPSR_Ref TObjectID="30149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.631933 12.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30166" ObjectName="SW-CX_MF.CX_MF_3901SW"/>
     <cge:Meas_Ref ObjectId="196856"/>
    <cge:TPSR_Ref TObjectID="30166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196844">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.532773 286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30154" ObjectName="SW-CX_MF.CX_MF_3766SW"/>
     <cge:Meas_Ref ObjectId="196844"/>
    <cge:TPSR_Ref TObjectID="30154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196841">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.532773 84.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30151" ObjectName="SW-CX_MF.CX_MF_3721SW"/>
     <cge:Meas_Ref ObjectId="196841"/>
    <cge:TPSR_Ref TObjectID="30151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196845">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.532773 307.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30155" ObjectName="SW-CX_MF.CX_MF_37667SW"/>
     <cge:Meas_Ref ObjectId="196845"/>
    <cge:TPSR_Ref TObjectID="30155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1057.631933 -43.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30167" ObjectName="SW-CX_MF.CX_MF_39017SW"/>
     <cge:Meas_Ref ObjectId="196857"/>
    <cge:TPSR_Ref TObjectID="30167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196842">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.532773 111.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30152" ObjectName="SW-CX_MF.CX_MF_37217SW"/>
     <cge:Meas_Ref ObjectId="196842"/>
    <cge:TPSR_Ref TObjectID="30152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196847">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.000000 101.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30157" ObjectName="SW-CX_MF.CX_MF_3741SW"/>
     <cge:Meas_Ref ObjectId="196847"/>
    <cge:TPSR_Ref TObjectID="30157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196848">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 131.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30158" ObjectName="SW-CX_MF.CX_MF_37417SW"/>
     <cge:Meas_Ref ObjectId="196848"/>
    <cge:TPSR_Ref TObjectID="30158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196850">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 101.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30160" ObjectName="SW-CX_MF.CX_MF_3751SW"/>
     <cge:Meas_Ref ObjectId="196850"/>
    <cge:TPSR_Ref TObjectID="30160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196851">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 130.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30161" ObjectName="SW-CX_MF.CX_MF_37517SW"/>
     <cge:Meas_Ref ObjectId="196851"/>
    <cge:TPSR_Ref TObjectID="30161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196853">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1714.000000 100.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30163" ObjectName="SW-CX_MF.CX_MF_3731SW"/>
     <cge:Meas_Ref ObjectId="196853"/>
    <cge:TPSR_Ref TObjectID="30163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196854">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 129.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30164" ObjectName="SW-CX_MF.CX_MF_37317SW"/>
     <cge:Meas_Ref ObjectId="196854"/>
    <cge:TPSR_Ref TObjectID="30164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196855">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.739130 1672.000000 391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30165" ObjectName="SW-CX_MF.CX_MF_3010SW"/>
     <cge:Meas_Ref ObjectId="196855"/>
    <cge:TPSR_Ref TObjectID="30165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.739130 1828.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_MF.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 316.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43355" ObjectName="SM-CX_MF.P1"/>
    <cge:TPSR_Ref TObjectID="43355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_MF.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1482.000000 315.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43356" ObjectName="SM-CX_MF.P2"/>
    <cge:TPSR_Ref TObjectID="43356"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BDS" endPointId="0" endStationName="CX_MF" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_baomiao" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1424,-671 1424,-614 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42320" ObjectName="AC-35kV.LN_baomiao"/>
    <cge:TPSR_Ref TObjectID="42320_SS-260"/></metadata>
   <polyline fill="none" opacity="0" points="1424,-671 1424,-614 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a91470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.532773 308.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab5e10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.631933 -42.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b57bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.532773 112.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c4a110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1193.000000 132.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1651310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1411.000000 131.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1630820" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1647.000000 130.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a5bcd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1674.000000 443.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b76480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1830.000000 -448.000000)" xlink:href="#earth:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1d88250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-35 1424,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30149@0" ObjectIDZND0="30146@0" Pin0InfoVect0LinkObjId="g_1a5ecb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196839_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-35 1424,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db6ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-71 1424,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30149@1" ObjectIDZND0="30148@0" Pin0InfoVect0LinkObjId="SW-196838_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196839_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-71 1424,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db8760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1485,-333 1424,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="g_1ad3420@0" ObjectIDZND0="g_1ad3c30@0" ObjectIDZND1="42320@1" ObjectIDZND2="g_1aac4c0@0" Pin0InfoVect0LinkObjId="g_1ad3c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_1aac4c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad3420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1485,-333 1424,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad0030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,20 1114,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30146@0" ObjectIDZND0="30166@0" Pin0InfoVect0LinkObjId="SW-196856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d88250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,20 1114,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad0290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-66 1173,-66 1173,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="30167@x" ObjectIDND1="30166@x" ObjectIDND2="g_1b60800@0" ObjectIDZND0="g_1633b40@0" Pin0InfoVect0LinkObjId="g_1633b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196857_0" Pin1InfoVect1LinkObjId="SW-196856_0" Pin1InfoVect2LinkObjId="g_1b60800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-66 1173,-66 1173,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aab230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-572 1424,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_1ad3c30@0" ObjectIDZND0="g_1ad3420@0" ObjectIDZND1="g_1aac4c0@0" ObjectIDZND2="g_1620cb0@0" Pin0InfoVect0LinkObjId="g_1ad3420_0" Pin0InfoVect1LinkObjId="g_1aac4c0_0" Pin0InfoVect2LinkObjId="g_1620cb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad3c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-572 1424,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aab490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-615 1424,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="42320@1" ObjectIDZND0="g_1ad3c30@0" ObjectIDZND1="g_1ad3420@0" ObjectIDZND2="g_1aac4c0@0" Pin0InfoVect0LinkObjId="g_1ad3c30_0" Pin0InfoVect1LinkObjId="g_1ad3420_0" Pin0InfoVect2LinkObjId="g_1aac4c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-615 1424,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b60400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-572 1424,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_1ad3c30@0" ObjectIDND1="42320@1" ObjectIDZND0="g_1ad3420@0" ObjectIDZND1="g_1aac4c0@0" ObjectIDZND2="g_1620cb0@0" Pin0InfoVect0LinkObjId="g_1ad3420_0" Pin0InfoVect1LinkObjId="g_1aac4c0_0" Pin0InfoVect2LinkObjId="g_1620cb0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ad3c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-572 1424,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16338e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-140 1114,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1b60800@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-140 1114,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a5ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,43 1047,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30151@1" ObjectIDZND0="30146@0" Pin0InfoVect0LinkObjId="g_1d88250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1047,43 1047,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,177 1047,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1a579b0@0" ObjectIDZND0="30150@x" ObjectIDZND1="30154@x" Pin0InfoVect0LinkObjId="SW-196840_0" Pin0InfoVect1LinkObjId="SW-196844_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a579b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1074,177 1047,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,281 1047,302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30154@0" ObjectIDZND0="30155@x" ObjectIDZND1="30153@x" Pin0InfoVect0LinkObjId="SW-196845_0" Pin0InfoVect1LinkObjId="SW-196843_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1047,281 1047,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,302 1002,302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a91470@0" ObjectIDZND0="30155@0" Pin0InfoVect0LinkObjId="SW-196845_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a91470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,302 1002,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0c030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,302 1047,302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30155@1" ObjectIDZND0="30154@x" ObjectIDZND1="30153@x" Pin0InfoVect0LinkObjId="SW-196844_0" Pin0InfoVect1LinkObjId="SW-196843_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196845_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1038,302 1047,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_163d650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-237 1403,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_1ad3420@0" ObjectIDND1="g_1ad3c30@0" ObjectIDND2="42320@1" ObjectIDZND0="g_1aac4c0@0" Pin0InfoVect0LinkObjId="g_1aac4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ad3420_0" Pin1InfoVect1LinkObjId="g_1ad3c30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-237 1403,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16207f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-333 1424,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_1ad3420@0" ObjectIDND1="g_1ad3c30@0" ObjectIDND2="42320@1" ObjectIDZND0="g_1aac4c0@0" ObjectIDZND1="g_1620cb0@0" ObjectIDZND2="30148@x" Pin0InfoVect0LinkObjId="g_1aac4c0_0" Pin0InfoVect1LinkObjId="g_1620cb0_0" Pin0InfoVect2LinkObjId="SW-196838_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ad3420_0" Pin1InfoVect1LinkObjId="g_1ad3c30_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-333 1424,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1620a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-237 1423,-237 1423,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="breaker" ObjectIDND0="g_1ad3420@0" ObjectIDND1="g_1ad3c30@0" ObjectIDND2="42320@1" ObjectIDZND0="30148@1" Pin0InfoVect0LinkObjId="SW-196838_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ad3420_0" Pin1InfoVect1LinkObjId="g_1ad3c30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-237 1423,-237 1423,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16212c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-237 1483,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1620cb0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1620cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-237 1483,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1621520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1446,-237 1423,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_1620cb0@1" ObjectIDZND0="g_1ad3420@0" ObjectIDZND1="g_1ad3c30@0" ObjectIDZND2="42320@1" Pin0InfoVect0LinkObjId="g_1ad3420_0" Pin0InfoVect1LinkObjId="g_1ad3c30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1620cb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1446,-237 1423,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abb040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-48 1063,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ab5e10@0" ObjectIDZND0="30167@0" Pin0InfoVect0LinkObjId="SW-196857_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ab5e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-48 1063,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abb2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1099,-48 1111,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30167@1" ObjectIDZND0="30166@x" ObjectIDZND1="g_1633b40@0" ObjectIDZND2="g_1b60800@0" Pin0InfoVect0LinkObjId="SW-196856_0" Pin0InfoVect1LinkObjId="g_1633b40_0" Pin0InfoVect2LinkObjId="g_1b60800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196857_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1099,-48 1111,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abb9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-29 1114,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30166@1" ObjectIDZND0="30167@x" ObjectIDZND1="g_1633b40@0" ObjectIDZND2="g_1b60800@0" Pin0InfoVect0LinkObjId="SW-196857_0" Pin0InfoVect1LinkObjId="g_1633b40_0" Pin0InfoVect2LinkObjId="g_1b60800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-29 1114,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abbbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-48 1114,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="30167@x" ObjectIDND1="30166@x" ObjectIDZND0="g_1633b40@0" ObjectIDZND1="g_1b60800@0" Pin0InfoVect0LinkObjId="g_1633b40_0" Pin0InfoVect1LinkObjId="g_1b60800_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196857_0" Pin1InfoVect1LinkObjId="SW-196856_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-48 1114,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abbdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-76 1114,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1633b40@0" ObjectIDND1="30167@x" ObjectIDND2="30166@x" ObjectIDZND0="g_1b60800@1" Pin0InfoVect0LinkObjId="g_1b60800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1633b40_0" Pin1InfoVect1LinkObjId="SW-196857_0" Pin1InfoVect2LinkObjId="SW-196856_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-76 1114,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abbfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-66 1114,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1633b40@0" ObjectIDND1="30167@x" ObjectIDND2="30166@x" ObjectIDZND0="g_1b60800@0" Pin0InfoVect0LinkObjId="g_1b60800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1633b40_0" Pin1InfoVect1LinkObjId="SW-196857_0" Pin1InfoVect2LinkObjId="SW-196856_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-66 1114,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abc1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-76 1114,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_1b60800@0" ObjectIDND1="g_1633b40@0" ObjectIDND2="30167@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b60800_0" Pin1InfoVect1LinkObjId="g_1633b40_0" Pin1InfoVect2LinkObjId="SW-196857_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-76 1114,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b58640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,106 1002,106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b57bb0@0" ObjectIDZND0="30152@0" Pin0InfoVect0LinkObjId="SW-196842_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b57bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,106 1002,106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a5fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,106 1047,106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="30152@1" ObjectIDZND0="30150@x" ObjectIDZND1="30151@x" Pin0InfoVect0LinkObjId="SW-196840_0" Pin0InfoVect1LinkObjId="SW-196841_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196842_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1038,106 1047,106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a60770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,132 1047,106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30150@1" ObjectIDZND0="30152@x" ObjectIDZND1="30151@x" Pin0InfoVect0LinkObjId="SW-196842_0" Pin0InfoVect1LinkObjId="SW-196841_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1047,132 1047,106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a60960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,106 1047,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="30150@x" ObjectIDND1="30152@x" ObjectIDZND0="30151@0" Pin0InfoVect0LinkObjId="SW-196841_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196840_0" Pin1InfoVect1LinkObjId="SW-196842_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1047,106 1047,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a60b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,159 1047,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="30150@0" ObjectIDZND0="g_1a579b0@0" ObjectIDZND1="30154@x" Pin0InfoVect0LinkObjId="g_1a579b0_0" Pin0InfoVect1LinkObjId="SW-196844_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1047,159 1047,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a60d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,177 1047,245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1a579b0@0" ObjectIDND1="30150@x" ObjectIDZND0="30154@1" Pin0InfoVect0LinkObjId="SW-196844_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a579b0_0" Pin1InfoVect1LinkObjId="SW-196840_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1047,177 1047,245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a60f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,325 1047,302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30153@1" ObjectIDZND0="30155@x" ObjectIDZND1="30154@x" Pin0InfoVect0LinkObjId="SW-196845_0" Pin0InfoVect1LinkObjId="SW-196844_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196843_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1047,325 1047,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fdc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,352 1047,419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="30153@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196843_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1047,352 1047,419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d11100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,466 1047,486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1d11880@0" Pin0InfoVect0LinkObjId="g_1d11880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1047,466 1047,486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8a260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,60 1269,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30157@1" ObjectIDZND0="30146@0" Pin0InfoVect0LinkObjId="g_1d88250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196847_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,60 1269,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a553f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,227 1269,227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_1d8a660@0" ObjectIDZND0="30156@x" ObjectIDZND1="43355@x" Pin0InfoVect0LinkObjId="SW-196846_0" Pin0InfoVect1LinkObjId="SM-CX_MF.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d8a660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1296,227 1269,227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4ab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1211,126 1224,126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c4a110@0" ObjectIDZND0="30158@0" Pin0InfoVect0LinkObjId="SW-196848_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c4a110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1211,126 1224,126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4ad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,126 1269,126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30158@1" ObjectIDZND0="30157@x" ObjectIDZND1="30156@x" Pin0InfoVect0LinkObjId="SW-196847_0" Pin0InfoVect1LinkObjId="SW-196846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196848_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1260,126 1269,126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4b4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,189 1269,227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="30156@0" ObjectIDZND0="g_1d8a660@0" ObjectIDZND1="43355@x" Pin0InfoVect0LinkObjId="g_1d8a660_0" Pin0InfoVect1LinkObjId="SM-CX_MF.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1269,189 1269,227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4b6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,227 1269,295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_1d8a660@0" ObjectIDND1="30156@x" ObjectIDZND0="43355@0" Pin0InfoVect0LinkObjId="SM-CX_MF.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d8a660_0" Pin1InfoVect1LinkObjId="SW-196846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,227 1269,295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a50630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,126 1269,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="30158@x" ObjectIDND1="30156@x" ObjectIDZND0="30157@0" Pin0InfoVect0LinkObjId="SW-196847_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196848_0" Pin1InfoVect1LinkObjId="SW-196846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,126 1269,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a50890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,161 1269,126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30156@1" ObjectIDZND0="30158@x" ObjectIDZND1="30157@x" Pin0InfoVect0LinkObjId="SW-196848_0" Pin0InfoVect1LinkObjId="SW-196847_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196846_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1269,161 1269,126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6ded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,59 1487,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30160@1" ObjectIDZND0="30146@0" Pin0InfoVect0LinkObjId="g_1d88250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,59 1487,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6f000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1514,226 1487,226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_1a6e2d0@0" ObjectIDZND0="30159@x" ObjectIDZND1="43356@x" Pin0InfoVect0LinkObjId="SW-196849_0" Pin0InfoVect1LinkObjId="SM-CX_MF.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6e2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1514,226 1487,226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1353090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1429,125 1442,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1651310@0" ObjectIDZND0="30161@0" Pin0InfoVect0LinkObjId="SW-196851_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1651310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1429,125 1442,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13532f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,125 1487,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30161@1" ObjectIDZND0="30160@x" ObjectIDZND1="30159@x" Pin0InfoVect0LinkObjId="SW-196850_0" Pin0InfoVect1LinkObjId="SW-196849_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1478,125 1487,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1353a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,188 1487,226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="30159@0" ObjectIDZND0="g_1a6e2d0@0" ObjectIDZND1="43356@x" Pin0InfoVect0LinkObjId="g_1a6e2d0_0" Pin0InfoVect1LinkObjId="SM-CX_MF.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1487,188 1487,226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1353bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,226 1487,294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="30159@x" ObjectIDND1="g_1a6e2d0@0" ObjectIDZND0="43356@0" Pin0InfoVect0LinkObjId="SM-CX_MF.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196849_0" Pin1InfoVect1LinkObjId="g_1a6e2d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,226 1487,294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1353de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,125 1487,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="30161@x" ObjectIDND1="30159@x" ObjectIDZND0="30160@0" Pin0InfoVect0LinkObjId="SW-196850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196851_0" Pin1InfoVect1LinkObjId="SW-196849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,125 1487,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1353fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,157 1487,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30159@1" ObjectIDZND0="30160@x" ObjectIDZND1="30161@x" Pin0InfoVect0LinkObjId="SW-196850_0" Pin0InfoVect1LinkObjId="SW-196851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196849_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1487,157 1487,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1354200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1487,155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db5900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,58 1723,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30163@1" ObjectIDZND0="30146@0" Pin0InfoVect0LinkObjId="g_1d88250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196853_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1723,58 1723,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a21cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1750,225 1723,225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="g_1db5b60@0" ObjectIDZND0="30162@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-196852_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1db5b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1750,225 1723,225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1631270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1665,124 1678,124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1630820@0" ObjectIDZND0="30164@0" Pin0InfoVect0LinkObjId="SW-196854_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1630820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1665,124 1678,124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16314d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1714,124 1723,124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30164@1" ObjectIDZND0="30163@x" ObjectIDZND1="30162@x" Pin0InfoVect0LinkObjId="SW-196853_0" Pin0InfoVect1LinkObjId="SW-196852_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1714,124 1723,124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1631c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,187 1723,225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="30162@0" ObjectIDZND0="g_1db5b60@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1db5b60_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196852_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1723,187 1723,225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1631e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,225 1723,334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="30162@x" ObjectIDND1="g_1db5b60@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196852_0" Pin1InfoVect1LinkObjId="g_1db5b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1723,225 1723,334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1632000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,124 1723,95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="30164@x" ObjectIDND1="30162@x" ObjectIDZND0="30163@0" Pin0InfoVect0LinkObjId="SW-196853_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196854_0" Pin1InfoVect1LinkObjId="SW-196852_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1723,124 1723,95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16321f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,156 1723,124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30162@1" ObjectIDZND0="30163@x" ObjectIDZND1="30164@x" Pin0InfoVect0LinkObjId="SW-196853_0" Pin0InfoVect1LinkObjId="SW-196854_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196852_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1723,156 1723,124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1632420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1723,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a58b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1723,376 1723,447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1723,376 1723,447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a28390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1679,431 1679,425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_1a5bcd0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a5bcd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1679,431 1679,425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b76e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1835,-460 1835,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_1b76480@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b76480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1835,-460 1835,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b77560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-557 1879,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-557 1879,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a65690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1834,-413 1834,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1a65b50@0" ObjectIDZND2="g_1a65b50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1a65b50_0" Pin0InfoVect2LinkObjId="g_1a65b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1834,-413 1834,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a658f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1834,-433 1879,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="g_1a65b50@0" ObjectIDND2="g_1a65b50@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1a65b50_0" Pin1InfoVect2LinkObjId="g_1a65b50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1834,-433 1879,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a66880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1797,-413 1797,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="g_1a65b50@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1a65b50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1a65b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a65b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1797,-413 1797,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a67370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-515 1879,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1a65b50@0" ObjectIDZND2="g_1a65b50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1a65b50_0" Pin0InfoVect2LinkObjId="g_1a65b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-515 1879,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a675d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-433 1879,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1a65b50@0" ObjectIDND2="g_1a65b50@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1a65b50_0" Pin1InfoVect2LinkObjId="g_1a65b50_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-433 1879,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a680c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1834,-433 1797,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1a65b50@0" ObjectIDZND1="g_1a65b50@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a65b50_0" Pin0InfoVect1LinkObjId="g_1a65b50_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1834,-433 1797,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a68320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1797,-433 1796,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="transformer2" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="transformer2" ObjectIDND0="g_1a65b50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1a65b50@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a65b50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a65b50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1797,-433 1796,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a18960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-555 2058,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2058,-555 2058,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a1b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,-411 2013,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="0@1" ObjectIDZND0="g_1a1b7e0@0" ObjectIDZND1="g_1a1b7e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a1b7e0_0" Pin0InfoVect1LinkObjId="g_1a1b7e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2013,-411 2013,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a1b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,-431 2058,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1a1b7e0@0" ObjectIDND2="g_1a1b7e0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1a1b7e0_0" Pin1InfoVect2LinkObjId="g_1a1b7e0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2013,-431 2058,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1284780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1976,-411 1976,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="g_1a1b7e0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1a1b7e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1a1b7e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a1b7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1976,-411 1976,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12849e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-513 2058,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_1a1b7e0@0" ObjectIDZND2="g_1a1b7e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1a1b7e0_0" Pin0InfoVect2LinkObjId="g_1a1b7e0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2058,-513 2058,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1284c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-431 2058,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1a1b7e0@0" ObjectIDND2="g_1a1b7e0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1a1b7e0_0" Pin1InfoVect2LinkObjId="g_1a1b7e0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2058,-431 2058,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1284ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,-431 1976,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="0@x" ObjectIDZND0="g_1a1b7e0@0" ObjectIDZND1="g_1a1b7e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a1b7e0_0" Pin0InfoVect1LinkObjId="g_1a1b7e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2013,-431 1976,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1285100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1976,-431 1975,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_1a1b7e0@0" ObjectIDND1="0@x" ObjectIDZND0="g_1a1b7e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1a1b7e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a1b7e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1976,-431 1975,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aaf640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1722,345 1679,345 1679,361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="30165@1" Pin0InfoVect0LinkObjId="SW-196855_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1722,345 1679,345 1679,361 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-196814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -80.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30146"/>
     <cge:Term_Ref ObjectID="42829"/>
    <cge:TPSR_Ref TObjectID="30146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-196815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -80.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30146"/>
     <cge:Term_Ref ObjectID="42829"/>
    <cge:TPSR_Ref TObjectID="30146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-196816" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -80.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196816" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30146"/>
     <cge:Term_Ref ObjectID="42829"/>
    <cge:TPSR_Ref TObjectID="30146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-196820" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -80.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30146"/>
     <cge:Term_Ref ObjectID="42829"/>
    <cge:TPSR_Ref TObjectID="30146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-196817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -80.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30146"/>
     <cge:Term_Ref ObjectID="42829"/>
    <cge:TPSR_Ref TObjectID="30146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196810" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30148"/>
     <cge:Term_Ref ObjectID="42832"/>
    <cge:TPSR_Ref TObjectID="30148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30148"/>
     <cge:Term_Ref ObjectID="42832"/>
    <cge:TPSR_Ref TObjectID="30148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30148"/>
     <cge:Term_Ref ObjectID="42832"/>
    <cge:TPSR_Ref TObjectID="30148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196835" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1126.000000 134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196835" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30150"/>
     <cge:Term_Ref ObjectID="42836"/>
    <cge:TPSR_Ref TObjectID="30150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1126.000000 134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30150"/>
     <cge:Term_Ref ObjectID="42836"/>
    <cge:TPSR_Ref TObjectID="30150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1271.000000 390.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30156"/>
     <cge:Term_Ref ObjectID="42848"/>
    <cge:TPSR_Ref TObjectID="30156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1271.000000 390.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30156"/>
     <cge:Term_Ref ObjectID="42848"/>
    <cge:TPSR_Ref TObjectID="30156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196822" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1271.000000 390.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196822" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30156"/>
     <cge:Term_Ref ObjectID="42848"/>
    <cge:TPSR_Ref TObjectID="30156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 390.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30159"/>
     <cge:Term_Ref ObjectID="42854"/>
    <cge:TPSR_Ref TObjectID="30159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196828" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 390.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30159"/>
     <cge:Term_Ref ObjectID="42854"/>
    <cge:TPSR_Ref TObjectID="30159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196826" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 390.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30159"/>
     <cge:Term_Ref ObjectID="42854"/>
    <cge:TPSR_Ref TObjectID="30159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 143.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30162"/>
     <cge:Term_Ref ObjectID="42860"/>
    <cge:TPSR_Ref TObjectID="30162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196832" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 143.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30162"/>
     <cge:Term_Ref ObjectID="42860"/>
    <cge:TPSR_Ref TObjectID="30162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 143.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30162"/>
     <cge:Term_Ref ObjectID="42860"/>
    <cge:TPSR_Ref TObjectID="30162"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="485" y="-747"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="485" y="-747"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="436" y="-764"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="436" y="-764"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="713,-622 710,-625 710,-571 713,-574 713,-622" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="713,-622 710,-625 859,-625 856,-622 713,-622" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="713,-574 710,-571 859,-571 856,-574 713,-574" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="856,-622 859,-625 859,-571 856,-574 856,-622" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="713" y="-622"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="713" y="-622"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="485" y="-747"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="436" y="-764"/></g>
   <g href="AVC妙峰光伏.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="713" y="-622"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad40d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.000000 36.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad4340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 895.000000 23.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad4580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 78.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad47c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 50.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db68b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 64.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab0aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1087.000000 -149.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab0d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.000000 -134.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab2160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 159.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab2390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1474.000000 144.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d30d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 129.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d3400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 -390.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d3660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1202.000000 -405.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d38a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 -420.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d3bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1757.000000 -143.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d3e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1746.000000 -158.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d4070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.000000 -173.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Rectangle_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(60,120,255)" stroke-width="2.28848" width="13" x="1872" y="-309"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(60,120,255)" stroke-width="2.28848" width="13" x="2051" y="-307"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ad3420">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 -325.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.540000 -0.000000 0.000000 -0.588889 1033.452773 469.078431)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.540000 -0.000000 0.000000 -0.588889 1033.452773 469.078431)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1710.000000 381.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1710.000000 381.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1866.000000 -510.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1866.000000 -510.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.622951 -0.677419 -0.000000 1843.500000 -378.500000)" xlink:href="#transformer2:shape32_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.622951 -0.677419 -0.000000 1843.500000 -378.500000)" xlink:href="#transformer2:shape32_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.622951 -0.677419 -0.000000 2022.500000 -376.500000)" xlink:href="#transformer2:shape32_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.622951 -0.677419 -0.000000 2022.500000 -376.500000)" xlink:href="#transformer2:shape32_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 473.000000 -688.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 502.538462 -555.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 501.538462 -514.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-196189" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 -656.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29874" ObjectName="DYN-CX_MF"/>
     <cge:Meas_Ref ObjectId="196189"/>
    </metadata>
   </g>
  </g><g id="Circle_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="1502" cy="-229" fill="none" rx="8" ry="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1490" cy="-237" fill="none" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1502" cy="-244" fill="none" r="8" stroke="rgb(60,120,255)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1137" cy="-175" fill="none" rx="8" ry="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1128" cy="-167" fill="none" rx="8" ry="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1117" cy="-175" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1127" cy="-181" fill="none" rx="8" ry="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196838">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1415.000000 -109.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30148" ObjectName="SW-CX_MF.CX_MF_371BK"/>
     <cge:Meas_Ref ObjectId="196838"/>
    <cge:TPSR_Ref TObjectID="30148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196840">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.532773 167.357708)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30150" ObjectName="SW-CX_MF.CX_MF_372BK"/>
     <cge:Meas_Ref ObjectId="196840"/>
    <cge:TPSR_Ref TObjectID="30150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196843">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.532773 360.357708)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30153" ObjectName="SW-CX_MF.CX_MF_376BK"/>
     <cge:Meas_Ref ObjectId="196843"/>
    <cge:TPSR_Ref TObjectID="30153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196846">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.000000 197.357708)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30156" ObjectName="SW-CX_MF.CX_MF_374BK"/>
     <cge:Meas_Ref ObjectId="196846"/>
    <cge:TPSR_Ref TObjectID="30156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196849">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 192.357708)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30159" ObjectName="SW-CX_MF.CX_MF_375BK"/>
     <cge:Meas_Ref ObjectId="196849"/>
    <cge:TPSR_Ref TObjectID="30159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196852">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1714.000000 191.357708)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30162" ObjectName="SW-CX_MF.CX_MF_373BK"/>
     <cge:Meas_Ref ObjectId="196852"/>
    <cge:TPSR_Ref TObjectID="30162"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1aac4c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1344.000000 -229.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad3c30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1464.000000 -564.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b60800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.631933 -104.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1633b40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1165.631933 -86.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a579b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.532773 231.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1620cb0">
    <use class="BV-35KV" transform="matrix(-0.000000 0.750000 0.717188 0.000000 1442.641406 -243.687500)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d11880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.532773 516.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8a660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 281.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a6e2d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.000000 280.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1db5b60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1743.000000 279.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a65b50">
    <use class="BV-0KV" transform="matrix(-0.000000 0.707692 0.812500 0.000000 1790.406250 -416.646154)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a1b7e0">
    <use class="BV-0KV" transform="matrix(-0.000000 0.707692 0.812500 0.000000 1969.406250 -414.646154)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f34db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -157.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1652840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -595.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1652840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -595.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1652840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -595.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1652840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -595.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1652840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -595.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1652840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -595.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1652840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -595.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="21" graphid="g_1ca8120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -736.500000) translate(0,17)">妙峰开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d88480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -663.000000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d88480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -663.000000) translate(0,44)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d88480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -663.000000) translate(0,68)">保</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d88480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -663.000000) translate(0,92)">妙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d88480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -663.000000) translate(0,116)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0c290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.532773 137.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0c780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.532773 53.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.532773 255.000000) translate(0,12)">3766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_163c4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.532773 282.000000) translate(0,12)">37667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a53e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.500000 -3.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abb500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -69.500000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.500000 -246.000000) translate(0,15)">35kV母线电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.500000 -246.000000) translate(0,33)"> 压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a60140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.532773 86.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d11360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 972.000000 524.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d11360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 972.000000 524.000000) translate(0,33)">      ±3MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d12250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 434.000000) translate(0,12)">3000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a55650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1227.000000 167.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a55b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 70.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4afe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.000000 106.000000) translate(0,12)">37417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6f260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1445.000000 166.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6f750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1443.000000 69.000000) translate(0,12)">3751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1353550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 105.000000) translate(0,12)">37517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1355490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.500000 356.000000) translate(0,15)">(1-9号方阵)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a8d9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 333.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a8ddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.500000 353.000000) translate(0,15)">(10-18号方阵)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a8e020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1449.000000 330.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a21f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 165.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a22440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 68.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1631730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1657.000000 104.000000) translate(0,12)">37317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a58f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1738.000000 349.000000) translate(0,12)">700kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a285f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1665.500000 460.000000) translate(0,15)">35kV1号站用变及</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a285f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1665.500000 460.000000) translate(0,33)">小电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b77070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1791.000000 -528.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b77750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1845.000000 -651.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a18bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2005.000000 -667.000000) translate(0,15)">10kV仓海线妙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a18bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2005.000000 -667.000000) translate(0,33)">峰山移民支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1287c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1759.500000 -580.000000) translate(0,12)">35kV1号站用变及</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1287c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1759.500000 -580.000000) translate(0,26)">小电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1a6ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2076.500000 -543.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a6c110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1924.000000 -171.000000) translate(0,15)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1a6ca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1957.500000 -285.000000) translate(0,8)">ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aaed60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -139.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aaf1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1435.000000 -60.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aaf400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1636.000000 367.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aaf830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 331.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d42b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -18.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d44f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.532773 205.000000) translate(0,15)">6226678</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1ac0ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 740.000000 -607.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1879,-526 1899,-526 1899,-511 1879,-493 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2058,-524 2078,-524 2078,-509 2058,-491 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="30146" cx="1424" cy="20" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30146" cx="1114" cy="20" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30146" cx="1047" cy="20" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30146" cx="1269" cy="20" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30146" cx="1487" cy="20" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30146" cx="1723" cy="20" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_MF.CX_MF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,20 1826,20 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30146" ObjectName="BS-CX_MF.CX_MF_3IM"/>
    <cge:TPSR_Ref TObjectID="30146"/></metadata>
   <polyline fill="none" opacity="0" points="930,20 1826,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1791,-183 2134,-183 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1791,-183 2134,-183 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_MF"/>
</svg>