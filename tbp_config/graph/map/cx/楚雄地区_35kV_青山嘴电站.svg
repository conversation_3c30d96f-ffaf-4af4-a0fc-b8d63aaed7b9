<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-62" aopId="2884350" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1199 2243 1120">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="44" x2="44" y1="31" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="44" x2="44" y1="79" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="53" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="53" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="19" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="19" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="86" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="86" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="2" y2="12"/>
    <polyline points="44,79 46,79 47,78 49,78 51,77 52,76 54,75 55,73 55,71 56,70 57,68 57,66 57,64 56,62 55,61 55,59 54,58 52,56 51,55 49,54 47,54 46,53 44,53 42,53 40,54 38,54 37,55 35,56 34,58 33,59 32,61 31,62 31,64 31,66 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="31" x2="43" y1="66" y2="66"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="8" x2="8" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape22_0">
    <circle cx="29" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="29" x2="36" y1="44" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="21" x2="29" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="29" x2="29" y1="35" y2="44"/>
   </symbol>
   <symbol id="transformer:shape22_1">
    <circle cx="59" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="60" x2="67" y1="66" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="52" x2="60" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="60" x2="60" y1="57" y2="66"/>
   </symbol>
   <symbol id="transformer:shape22-2">
    <circle cx="58" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="62" y1="28" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="62" y1="28" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="62" y1="37" y2="20"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape55_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,44 6,44 6,73 " stroke-width="1"/>
    <circle cx="31" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="31,87 25,74 37,74 31,87 31,86 31,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="49" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="44" y2="39"/>
   </symbol>
   <symbol id="transformer2:shape55_1">
    <circle cx="31" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="20" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape120">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="31" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="31" y1="37" y2="35"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="28" y2="19"/>
    <ellipse cx="31" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="18" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="18" y1="37" y2="35"/>
    <ellipse cx="31" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="15" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="15" y1="19" y2="19"/>
    <ellipse cx="18" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="18" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a7f880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a80a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a813c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a82090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a832f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a83f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a84970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a85430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a869d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a869d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a883b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a883b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a89ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a89ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2a8ab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8c720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a8d310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a8e090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a8e7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8fee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a90a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a91180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a91940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a92a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a933a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a93e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a94850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a95cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a96860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a97890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a984d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa6ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a99aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a9a800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a9bd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1130" width="2253" x="3111" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5357" x2="5357" y1="-504" y2="-464"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.493242 -393.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6592" ObjectName="SW-CX_QSZ.CX_QSZ_066BK"/>
     <cge:Meas_Ref ObjectId="40511"/>
    <cge:TPSR_Ref TObjectID="6592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.116729 -342.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6600" ObjectName="SW-CX_QSZ.CX_QSZ_064BK"/>
     <cge:Meas_Ref ObjectId="40526"/>
    <cge:TPSR_Ref TObjectID="6600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40518">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.953278 -391.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6595" ObjectName="SW-CX_QSZ.CX_QSZ_065BK"/>
     <cge:Meas_Ref ObjectId="40518"/>
    <cge:TPSR_Ref TObjectID="6595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.819387 -391.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6609" ObjectName="SW-CX_QSZ.CX_QSZ_063BK"/>
     <cge:Meas_Ref ObjectId="40547"/>
    <cge:TPSR_Ref TObjectID="6609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40533">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.745431 -391.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6603" ObjectName="SW-CX_QSZ.CX_QSZ_062BK"/>
     <cge:Meas_Ref ObjectId="40533"/>
    <cge:TPSR_Ref TObjectID="6603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40540">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.535305 -397.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6606" ObjectName="SW-CX_QSZ.CX_QSZ_061BK"/>
     <cge:Meas_Ref ObjectId="40540"/>
    <cge:TPSR_Ref TObjectID="6606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.235448 -919.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6622" ObjectName="SW-CX_QSZ.CX_QSZ_362BK"/>
     <cge:Meas_Ref ObjectId="40605"/>
    <cge:TPSR_Ref TObjectID="6622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40503">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -917.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6588" ObjectName="SW-CX_QSZ.CX_QSZ_361BK"/>
     <cge:Meas_Ref ObjectId="40503"/>
    <cge:TPSR_Ref TObjectID="6588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40554">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.444434 -745.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6612" ObjectName="SW-CX_QSZ.CX_QSZ_301BK"/>
     <cge:Meas_Ref ObjectId="40554"/>
    <cge:TPSR_Ref TObjectID="6612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40558">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.444434 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6614" ObjectName="SW-CX_QSZ.CX_QSZ_001BK"/>
     <cge:Meas_Ref ObjectId="40558"/>
    <cge:TPSR_Ref TObjectID="6614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.444434 -577.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28377" ObjectName="SW-CX_QSZ.CX_QSZ_002BK"/>
     <cge:Meas_Ref ObjectId="187320"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187291">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.444434 -742.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28375" ObjectName="SW-CX_QSZ.CX_QSZ_302BK"/>
     <cge:Meas_Ref ObjectId="187291"/>
    <cge:TPSR_Ref TObjectID="28375"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 -549.000000)" xlink:href="#transformer:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 -549.000000)" xlink:href="#transformer:shape22_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 -549.000000)" xlink:href="#transformer:shape22-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_QSZ.CX_QSZ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3520,-861 4912,-861 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6586" ObjectName="BS-CX_QSZ.CX_QSZ_3IM"/>
    <cge:TPSR_Ref TObjectID="6586"/></metadata>
   <polyline fill="none" opacity="0" points="3520,-861 4912,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QSZ.CX_QSZ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3537,-497 5015,-497 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6587" ObjectName="BS-CX_QSZ.CX_QSZ_9IM"/>
    <cge:TPSR_Ref TObjectID="6587"/></metadata>
   <polyline fill="none" opacity="0" points="3537,-497 5015,-497 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_QSZ.CX_QSZ_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -104.000000)" xlink:href="#capacitor:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40692" ObjectName="CB-CX_QSZ.CX_QSZ_1C"/>
    <cge:TPSR_Ref TObjectID="40692"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_QSZ.CX_QSZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9410"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4068.000000 -643.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4068.000000 -643.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6624" ObjectName="TF-CX_QSZ.CX_QSZ_1T"/>
    <cge:TPSR_Ref TObjectID="6624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_QSZ.CX_QSZ_Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9414"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4652.000000 -783.000000)" xlink:href="#transformer2:shape55_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4652.000000 -783.000000)" xlink:href="#transformer2:shape55_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6625" ObjectName="TF-CX_QSZ.CX_QSZ_Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_QSZ.CX_QSZ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40300"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4799.000000 -640.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4799.000000 -640.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28373" ObjectName="TF-CX_QSZ.CX_QSZ_2T"/>
    <cge:TPSR_Ref TObjectID="28373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4931.000000 -324.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4931.000000 -324.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2160c50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.717830 -265.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2134c60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3830.953278 -200.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21690f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.819387 -218.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2140800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3923.000000 -1125.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20fd3c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4249.000000 -1123.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2024de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4568.764552 -963.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20cf7f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.764552 -962.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2065530">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.452961 -583.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2066020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.452961 -582.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20dfb10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 -790.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e0740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.962136 -429.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe3fb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -1042.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe43d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4188.000000 -1040.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe4800">
    <use class="BV-10KV" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3597.000000 -257.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe4d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -273.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe52b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5084.000000 -206.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe57f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -255.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2017c80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 -255.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20181f0">
    <use class="BV-10KV" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4716.000000 -261.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2031610">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5145.819387 -182.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2113de0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5110.000000 -441.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2114ac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -634.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb9040">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4480.500000 -542.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-40494" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -683.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40494" ObjectName="CX_QSZ:CX_QSZ_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3226.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-40480" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -781.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40480" ObjectName="CX_QSZ:CX_QSZ_Zyb_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-40481" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -764.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40481" ObjectName="CX_QSZ:CX_QSZ_Zyb_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-40482" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -747.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40482" ObjectName="CX_QSZ:CX_QSZ_Zyb_Ic"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-40617" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4967.000000 -287.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40617" ObjectName="CX_QSZ:CX_QSZ_Zyb_Ua1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-40618" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4967.000000 -270.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40618" ObjectName="CX_QSZ:CX_QSZ_Zyb_Ia1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200695" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3265.538462 -985.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200695" ObjectName="CX_QSZ:CX_QSZ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200696" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3265.538462 -942.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200696" ObjectName="CX_QSZ:CX_QSZ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -978.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6622"/>
     <cge:Term_Ref ObjectID="9404"/>
    <cge:TPSR_Ref TObjectID="6622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40500" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -978.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40500" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6622"/>
     <cge:Term_Ref ObjectID="9404"/>
    <cge:TPSR_Ref TObjectID="6622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -978.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6622"/>
     <cge:Term_Ref ObjectID="9404"/>
    <cge:TPSR_Ref TObjectID="6622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -978.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6622"/>
     <cge:Term_Ref ObjectID="9404"/>
    <cge:TPSR_Ref TObjectID="6622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -978.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6622"/>
     <cge:Term_Ref ObjectID="9404"/>
    <cge:TPSR_Ref TObjectID="6622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.444434 -637.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6614"/>
     <cge:Term_Ref ObjectID="9388"/>
    <cge:TPSR_Ref TObjectID="6614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.444434 -637.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6614"/>
     <cge:Term_Ref ObjectID="9388"/>
    <cge:TPSR_Ref TObjectID="6614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.444434 -637.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6614"/>
     <cge:Term_Ref ObjectID="9388"/>
    <cge:TPSR_Ref TObjectID="6614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-40619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.444434 -637.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6614"/>
     <cge:Term_Ref ObjectID="9388"/>
    <cge:TPSR_Ref TObjectID="6614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.444434 -637.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6614"/>
     <cge:Term_Ref ObjectID="9388"/>
    <cge:TPSR_Ref TObjectID="6614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.444434 -637.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6614"/>
     <cge:Term_Ref ObjectID="9388"/>
    <cge:TPSR_Ref TObjectID="6614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.257794 -177.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6592"/>
     <cge:Term_Ref ObjectID="9344"/>
    <cge:TPSR_Ref TObjectID="6592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.257794 -177.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6592"/>
     <cge:Term_Ref ObjectID="9344"/>
    <cge:TPSR_Ref TObjectID="6592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.257794 -177.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6592"/>
     <cge:Term_Ref ObjectID="9344"/>
    <cge:TPSR_Ref TObjectID="6592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.257794 -177.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6592"/>
     <cge:Term_Ref ObjectID="9344"/>
    <cge:TPSR_Ref TObjectID="6592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.257794 -177.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6592"/>
     <cge:Term_Ref ObjectID="9344"/>
    <cge:TPSR_Ref TObjectID="6592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5104.881281 -154.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6600"/>
     <cge:Term_Ref ObjectID="9360"/>
    <cge:TPSR_Ref TObjectID="6600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5104.881281 -154.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6600"/>
     <cge:Term_Ref ObjectID="9360"/>
    <cge:TPSR_Ref TObjectID="6600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5104.881281 -154.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6600"/>
     <cge:Term_Ref ObjectID="9360"/>
    <cge:TPSR_Ref TObjectID="6600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5104.881281 -154.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6600"/>
     <cge:Term_Ref ObjectID="9360"/>
    <cge:TPSR_Ref TObjectID="6600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5104.881281 -154.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6600"/>
     <cge:Term_Ref ObjectID="9360"/>
    <cge:TPSR_Ref TObjectID="6600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.819387 -175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6609"/>
     <cge:Term_Ref ObjectID="9378"/>
    <cge:TPSR_Ref TObjectID="6609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.819387 -175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6609"/>
     <cge:Term_Ref ObjectID="9378"/>
    <cge:TPSR_Ref TObjectID="6609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.819387 -175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6609"/>
     <cge:Term_Ref ObjectID="9378"/>
    <cge:TPSR_Ref TObjectID="6609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.819387 -175.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6609"/>
     <cge:Term_Ref ObjectID="9378"/>
    <cge:TPSR_Ref TObjectID="6609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.819387 -175.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6609"/>
     <cge:Term_Ref ObjectID="9378"/>
    <cge:TPSR_Ref TObjectID="6609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4566.009983 -177.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6603"/>
     <cge:Term_Ref ObjectID="9366"/>
    <cge:TPSR_Ref TObjectID="6603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4566.009983 -177.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6603"/>
     <cge:Term_Ref ObjectID="9366"/>
    <cge:TPSR_Ref TObjectID="6603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4566.009983 -177.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6603"/>
     <cge:Term_Ref ObjectID="9366"/>
    <cge:TPSR_Ref TObjectID="6603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4566.009983 -177.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6603"/>
     <cge:Term_Ref ObjectID="9366"/>
    <cge:TPSR_Ref TObjectID="6603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4566.009983 -177.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6603"/>
     <cge:Term_Ref ObjectID="9366"/>
    <cge:TPSR_Ref TObjectID="6603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.299857 -177.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6606"/>
     <cge:Term_Ref ObjectID="9372"/>
    <cge:TPSR_Ref TObjectID="6606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.299857 -177.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6606"/>
     <cge:Term_Ref ObjectID="9372"/>
    <cge:TPSR_Ref TObjectID="6606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.299857 -177.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6606"/>
     <cge:Term_Ref ObjectID="9372"/>
    <cge:TPSR_Ref TObjectID="6606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.299857 -177.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6606"/>
     <cge:Term_Ref ObjectID="9372"/>
    <cge:TPSR_Ref TObjectID="6606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.299857 -177.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6606"/>
     <cge:Term_Ref ObjectID="9372"/>
    <cge:TPSR_Ref TObjectID="6606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-40493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.444434 -702.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6624"/>
     <cge:Term_Ref ObjectID="9411"/>
    <cge:TPSR_Ref TObjectID="6624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-40483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -942.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6586"/>
     <cge:Term_Ref ObjectID="9334"/>
    <cge:TPSR_Ref TObjectID="6586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-40484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -942.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6586"/>
     <cge:Term_Ref ObjectID="9334"/>
    <cge:TPSR_Ref TObjectID="6586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-40485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -942.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6586"/>
     <cge:Term_Ref ObjectID="9334"/>
    <cge:TPSR_Ref TObjectID="6586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-40491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -942.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6586"/>
     <cge:Term_Ref ObjectID="9334"/>
    <cge:TPSR_Ref TObjectID="6586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-40486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -942.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6586"/>
     <cge:Term_Ref ObjectID="9334"/>
    <cge:TPSR_Ref TObjectID="6586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-40487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -581.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6587"/>
     <cge:Term_Ref ObjectID="9335"/>
    <cge:TPSR_Ref TObjectID="6587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-40488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -581.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6587"/>
     <cge:Term_Ref ObjectID="9335"/>
    <cge:TPSR_Ref TObjectID="6587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-40489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -581.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6587"/>
     <cge:Term_Ref ObjectID="9335"/>
    <cge:TPSR_Ref TObjectID="6587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-40492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -581.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6587"/>
     <cge:Term_Ref ObjectID="9335"/>
    <cge:TPSR_Ref TObjectID="6587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-40490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -581.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6587"/>
     <cge:Term_Ref ObjectID="9335"/>
    <cge:TPSR_Ref TObjectID="6587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -982.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6588"/>
     <cge:Term_Ref ObjectID="9336"/>
    <cge:TPSR_Ref TObjectID="6588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -982.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6588"/>
     <cge:Term_Ref ObjectID="9336"/>
    <cge:TPSR_Ref TObjectID="6588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -982.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6588"/>
     <cge:Term_Ref ObjectID="9336"/>
    <cge:TPSR_Ref TObjectID="6588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-40444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -982.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6588"/>
     <cge:Term_Ref ObjectID="9336"/>
    <cge:TPSR_Ref TObjectID="6588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -982.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6588"/>
     <cge:Term_Ref ObjectID="9336"/>
    <cge:TPSR_Ref TObjectID="6588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -798.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6612"/>
     <cge:Term_Ref ObjectID="9384"/>
    <cge:TPSR_Ref TObjectID="6612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -798.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6612"/>
     <cge:Term_Ref ObjectID="9384"/>
    <cge:TPSR_Ref TObjectID="6612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -798.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6612"/>
     <cge:Term_Ref ObjectID="9384"/>
    <cge:TPSR_Ref TObjectID="6612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -798.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6612"/>
     <cge:Term_Ref ObjectID="9384"/>
    <cge:TPSR_Ref TObjectID="6612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4952.000000 -805.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28375"/>
     <cge:Term_Ref ObjectID="40304"/>
    <cge:TPSR_Ref TObjectID="28375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4952.000000 -805.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28375"/>
     <cge:Term_Ref ObjectID="40304"/>
    <cge:TPSR_Ref TObjectID="28375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187293" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4952.000000 -805.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28375"/>
     <cge:Term_Ref ObjectID="40304"/>
    <cge:TPSR_Ref TObjectID="28375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-187298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4952.000000 -805.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28375"/>
     <cge:Term_Ref ObjectID="40304"/>
    <cge:TPSR_Ref TObjectID="28375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-241864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-241868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-241869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-241865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-187305" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -680.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28377"/>
     <cge:Term_Ref ObjectID="40308"/>
    <cge:TPSR_Ref TObjectID="28377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -174.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6595"/>
     <cge:Term_Ref ObjectID="9350"/>
    <cge:TPSR_Ref TObjectID="6595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -174.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6595"/>
     <cge:Term_Ref ObjectID="9350"/>
    <cge:TPSR_Ref TObjectID="6595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-187307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -730.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28373"/>
     <cge:Term_Ref ObjectID="40301"/>
    <cge:TPSR_Ref TObjectID="28373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-187317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -730.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28373"/>
     <cge:Term_Ref ObjectID="40301"/>
    <cge:TPSR_Ref TObjectID="28373"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3238" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3238" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3190" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3190" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3898" y="-948"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3898" y="-948"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4224" y="-946"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4224" y="-946"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="3641" y="-422"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="3641" y="-422"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="3909" y="-420"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="3909" y="-420"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="5128" y="-371"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="5128" y="-371"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="4350" y="-420"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="4350" y="-420"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4565" y="-420"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4565" y="-420"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4760" y="-426"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4760" y="-426"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3393" y="-1156"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3393" y="-1156"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3393" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3393" y="-1191"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="83" x="3139" y="-768"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="83" x="3139" y="-768"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="99" x="3971" y="-701"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="99" x="3971" y="-701"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="98" x="4869" y="-732"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="98" x="4869" y="-732"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3528" y="-1181"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3528" y="-1181"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3133" y="-733"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3133" y="-733"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3238" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3190" y="-1194"/></g>
   <g href="35kV青山嘴变35kV坝后电站线362断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3898" y="-948"/></g>
   <g href="35kV青山嘴变35kV青山嘴线361断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4224" y="-946"/></g>
   <g href="35kV青山嘴变10kV杨排线066断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="3641" y="-422"/></g>
   <g href="35kV青山嘴变10kV电容器065断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="3909" y="-420"/></g>
   <g href="35kV青山嘴变10kV云南交投线064断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="5128" y="-371"/></g>
   <g href="35kV青山嘴变10kV右岸施工线063断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="4350" y="-420"/></g>
   <g href="35kV青山嘴变10kV生活区线062断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4565" y="-420"/></g>
   <g href="35kV青山嘴变10kV石料厂线061断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4760" y="-426"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3393" y="-1156"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3393" y="-1191"/></g>
   <g href="35kV青山嘴变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="83" x="3139" y="-768"/></g>
   <g href="35kV青山嘴变35kV1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="99" x="3971" y="-701"/></g>
   <g href="35kV青山嘴变35kV2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="98" x="4869" y="-732"/></g>
   <g href="AVC青山嘴站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3528" y="-1181"/></g>
   <g href="35kV青山嘴变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3133" y="-733"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="1" width="42" x="4374" y="-665"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3527" y="-1181"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2111420">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4609.000000 -1074.000000)" xlink:href="#voltageTransformer:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2115670">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3871.000000 -742.000000)" xlink:href="#voltageTransformer:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_QSZ.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -215.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34454" ObjectName="EC-CX_QSZ.066Ld"/>
    <cge:TPSR_Ref TObjectID="34454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QSZ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -214.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34455" ObjectName="EC-CX_QSZ.063Ld"/>
    <cge:TPSR_Ref TObjectID="34455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QSZ.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -211.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34456" ObjectName="EC-CX_QSZ.062Ld"/>
    <cge:TPSR_Ref TObjectID="34456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QSZ.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.000000 -211.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34457" ObjectName="EC-CX_QSZ.061Ld"/>
    <cge:TPSR_Ref TObjectID="34457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QSZ.362Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -1127.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41848" ObjectName="EC-CX_QSZ.362Ld"/>
    <cge:TPSR_Ref TObjectID="41848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_QSZ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -189.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34458" ObjectName="EC-CX_QSZ.064Ld"/>
    <cge:TPSR_Ref TObjectID="34458"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_20b0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-386 3632,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6594@1" ObjectIDZND0="6592@0" Pin0InfoVect0LinkObjId="SW-40511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-386 3632,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20aec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-323 3603,-323 3603,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6594@x" ObjectIDND1="34454@x" ObjectIDZND0="g_1fe4800@0" Pin0InfoVect0LinkObjId="g_1fe4800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40514_0" Pin1InfoVect1LinkObjId="EC-CX_QSZ.066Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-323 3603,-323 3603,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20aee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-350 3632,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6594@0" ObjectIDZND0="g_1fe4800@0" ObjectIDZND1="34454@x" Pin0InfoVect0LinkObjId="g_1fe4800_0" Pin0InfoVect1LinkObjId="EC-CX_QSZ.066Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-350 3632,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20af020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-323 3632,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1fe4800@0" ObjectIDND1="6594@x" ObjectIDZND0="34454@0" Pin0InfoVect0LinkObjId="EC-CX_QSZ.066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fe4800_0" Pin1InfoVect1LinkObjId="SW-40514_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-323 3632,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2093760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-335 5120,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6602@1" ObjectIDZND0="6600@0" Pin0InfoVect0LinkObjId="SW-40526_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-335 5120,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2093950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-377 5120,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6600@1" ObjectIDZND0="6601@0" Pin0InfoVect0LinkObjId="SW-40528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-377 5120,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2093b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-272 5091,-272 5091,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="6602@x" ObjectIDND1="g_2031610@0" ObjectIDND2="34458@x" ObjectIDZND0="g_1fe52b0@0" Pin0InfoVect0LinkObjId="g_1fe52b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40529_0" Pin1InfoVect1LinkObjId="g_2031610_0" Pin1InfoVect2LinkObjId="EC-CX_QSZ.064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-272 5091,-272 5091,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2093d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-299 5120,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="6602@0" ObjectIDZND0="g_1fe52b0@0" ObjectIDZND1="g_2031610@0" ObjectIDZND2="34458@x" Pin0InfoVect0LinkObjId="g_1fe52b0_0" Pin0InfoVect1LinkObjId="g_2031610_0" Pin0InfoVect2LinkObjId="EC-CX_QSZ.064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-299 5120,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2093f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-272 5120,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_1fe52b0@0" ObjectIDND1="6602@x" ObjectIDND2="g_2031610@0" ObjectIDZND0="34458@0" Pin0InfoVect0LinkObjId="EC-CX_QSZ.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fe52b0_0" Pin1InfoVect1LinkObjId="SW-40529_0" Pin1InfoVect2LinkObjId="g_2031610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-272 5120,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2160490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-384 3901,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6596@1" ObjectIDZND0="6595@0" Pin0InfoVect0LinkObjId="SW-40518_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-384 3901,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2160680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-426 3901,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6595@1" ObjectIDZND0="6597@0" Pin0InfoVect0LinkObjId="SW-40521_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40518_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-426 3901,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2160870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-339 3872,-339 3872,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2160c50@0" ObjectIDND1="6596@x" ObjectIDZND0="g_1fe4d70@0" Pin0InfoVect0LinkObjId="g_1fe4d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2160c50_0" Pin1InfoVect1LinkObjId="SW-40520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-339 3872,-339 3872,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2160a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-348 3901,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6596@0" ObjectIDZND0="g_2160c50@0" ObjectIDZND1="g_1fe4d70@0" Pin0InfoVect0LinkObjId="g_2160c50_0" Pin0InfoVect1LinkObjId="g_1fe4d70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-348 3901,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2161260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-339 3901,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fe4d70@0" ObjectIDND1="6596@x" ObjectIDZND0="g_2160c50@0" Pin0InfoVect0LinkObjId="g_2160c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fe4d70_0" Pin1InfoVect1LinkObjId="SW-40520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-339 3901,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2134880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-270 3901,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2160c50@1" ObjectIDZND0="6598@1" Pin0InfoVect0LinkObjId="SW-40524_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2160c50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-270 3901,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2134a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-218 3901,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="6598@0" ObjectIDZND0="40692@0" Pin0InfoVect0LinkObjId="CB-CX_QSZ.CX_QSZ_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-218 3901,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2102080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-206 3984,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6599@1" ObjectIDZND0="g_21028a0@0" Pin0InfoVect0LinkObjId="g_21028a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-206 3984,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21302f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-384 4342,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6611@1" ObjectIDZND0="6609@0" Pin0InfoVect0LinkObjId="SW-40547_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-384 4342,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21304e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-426 4342,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6609@1" ObjectIDZND0="6610@0" Pin0InfoVect0LinkObjId="SW-40549_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-426 4342,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21306d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-321 4313,-321 4313,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_21690f0@0" ObjectIDND1="34455@x" ObjectIDND2="6611@x" ObjectIDZND0="g_1fe57f0@0" Pin0InfoVect0LinkObjId="g_1fe57f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21690f0_0" Pin1InfoVect1LinkObjId="EC-CX_QSZ.063Ld_0" Pin1InfoVect2LinkObjId="SW-40550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-321 4313,-321 4313,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21308c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-348 4342,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="6611@0" ObjectIDZND0="g_21690f0@0" ObjectIDZND1="34455@x" ObjectIDZND2="g_1fe57f0@0" Pin0InfoVect0LinkObjId="g_21690f0_0" Pin0InfoVect1LinkObjId="EC-CX_QSZ.063Ld_0" Pin0InfoVect2LinkObjId="g_1fe57f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-348 4342,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208de40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-384 4558,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6605@1" ObjectIDZND0="6603@0" Pin0InfoVect0LinkObjId="SW-40533_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40536_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-384 4558,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208e030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-426 4558,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6603@1" ObjectIDZND0="6604@0" Pin0InfoVect0LinkObjId="SW-40535_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40533_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-426 4558,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208e220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-321 4558,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2017c80@0" ObjectIDND1="6605@x" ObjectIDZND0="34456@0" Pin0InfoVect0LinkObjId="EC-CX_QSZ.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2017c80_0" Pin1InfoVect1LinkObjId="SW-40536_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-321 4558,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4752,-390 4752,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6608@1" ObjectIDZND0="6606@0" Pin0InfoVect0LinkObjId="SW-40540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40543_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4752,-390 4752,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20900d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4752,-432 4752,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6606@1" ObjectIDZND0="6607@0" Pin0InfoVect0LinkObjId="SW-40542_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4752,-432 4752,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2167760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4752,-327 4752,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_20181f0@0" ObjectIDND1="6608@x" ObjectIDZND0="34457@0" Pin0InfoVect0LinkObjId="EC-CX_QSZ.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20181f0_0" Pin1InfoVect1LinkObjId="SW-40543_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4752,-327 4752,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2168f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-308 4341,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_21690f0@0" ObjectIDND1="g_1fe57f0@0" ObjectIDND2="6611@x" ObjectIDZND0="34455@0" Pin0InfoVect0LinkObjId="EC-CX_QSZ.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21690f0_0" Pin1InfoVect1LinkObjId="g_1fe57f0_0" Pin1InfoVect2LinkObjId="SW-40550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-308 4341,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2063700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-313 4527,-321 4558,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2017c80@0" ObjectIDZND0="6605@x" ObjectIDZND1="34456@x" Pin0InfoVect0LinkObjId="SW-40536_0" Pin0InfoVect1LinkObjId="EC-CX_QSZ.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2017c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-313 4527,-321 4558,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20638f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-321 4558,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2017c80@0" ObjectIDND1="34456@x" ObjectIDZND0="6605@0" Pin0InfoVect0LinkObjId="SW-40536_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2017c80_0" Pin1InfoVect1LinkObjId="EC-CX_QSZ.062Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-321 4558,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2063ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-319 4722,-327 4752,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_20181f0@0" ObjectIDZND0="6608@x" ObjectIDZND1="34457@x" Pin0InfoVect0LinkObjId="SW-40543_0" Pin0InfoVect1LinkObjId="EC-CX_QSZ.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20181f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-319 4722,-327 4752,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2064490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4752,-327 4752,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_20181f0@0" ObjectIDND1="34457@x" ObjectIDZND0="6608@0" Pin0InfoVect0LinkObjId="SW-40543_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20181f0_0" Pin1InfoVect1LinkObjId="EC-CX_QSZ.061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4752,-327 4752,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2064680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-290 4377,-308 4342,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_21690f0@0" ObjectIDZND0="g_1fe57f0@0" ObjectIDZND1="6611@x" ObjectIDZND2="34455@x" Pin0InfoVect0LinkObjId="g_1fe57f0_0" Pin0InfoVect1LinkObjId="SW-40550_0" Pin0InfoVect2LinkObjId="EC-CX_QSZ.063Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21690f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-290 4377,-308 4342,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2064870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-308 4342,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_21690f0@0" ObjectIDND1="34455@x" ObjectIDZND0="g_1fe57f0@0" ObjectIDZND1="6611@x" Pin0InfoVect0LinkObjId="g_1fe57f0_0" Pin0InfoVect1LinkObjId="SW-40550_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21690f0_0" Pin1InfoVect1LinkObjId="EC-CX_QSZ.063Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-308 4342,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2096770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-861 3889,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6586@0" ObjectIDZND0="6623@0" Pin0InfoVect0LinkObjId="SW-40606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-861 3889,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20db8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-914 3889,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6623@1" ObjectIDZND0="6622@0" Pin0InfoVect0LinkObjId="SW-40605_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-914 3889,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20dd620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-954 3889,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6622@1" ObjectIDZND0="6620@0" Pin0InfoVect0LinkObjId="SW-40603_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-954 3889,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213fcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3834,-1024 3807,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6621@0" ObjectIDZND0="g_213fed0@0" Pin0InfoVect0LinkObjId="g_213fed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3834,-1024 3807,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20aae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-861 4215,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6586@0" ObjectIDZND0="6589@0" Pin0InfoVect0LinkObjId="SW-40504_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-861 4215,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20acca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-912 4215,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6589@1" ObjectIDZND0="6588@0" Pin0InfoVect0LinkObjId="SW-40503_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40504_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-912 4215,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fc210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-952 4215,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6588@1" ObjectIDZND0="6590@0" Pin0InfoVect0LinkObjId="SW-40505_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40503_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-952 4215,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fc430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-1006 4215,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="6590@1" ObjectIDZND0="g_20fd3c0@0" ObjectIDZND1="g_1fe43d0@0" ObjectIDZND2="18034@1" Pin0InfoVect0LinkObjId="g_20fd3c0_0" Pin0InfoVect1LinkObjId="g_1fe43d0_0" Pin0InfoVect2LinkObjId="g_20fe4f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40505_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-1006 4215,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fcf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-1038 4259,-1038 4259,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6590@x" ObjectIDND1="6591@x" ObjectIDND2="g_1fe43d0@0" ObjectIDZND0="g_20fd3c0@0" Pin0InfoVect0LinkObjId="g_20fd3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40505_0" Pin1InfoVect1LinkObjId="SW-40506_0" Pin1InfoVect2LinkObjId="g_1fe43d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-1038 4259,-1038 4259,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fd1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-1022 4215,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="6590@x" ObjectIDND1="6591@x" ObjectIDZND0="g_20fd3c0@0" ObjectIDZND1="g_1fe43d0@0" ObjectIDZND2="18034@1" Pin0InfoVect0LinkObjId="g_20fd3c0_0" Pin0InfoVect1LinkObjId="g_1fe43d0_0" Pin0InfoVect2LinkObjId="g_20fe4f0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40505_0" Pin1InfoVect1LinkObjId="SW-40506_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-1022 4215,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fe0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-1110 4195,-1110 4195,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6590@x" ObjectIDND1="6591@x" ObjectIDND2="g_20fd3c0@0" ObjectIDZND0="g_1fe43d0@0" Pin0InfoVect0LinkObjId="g_1fe43d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40505_0" Pin1InfoVect1LinkObjId="SW-40506_0" Pin1InfoVect2LinkObjId="g_20fd3c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-1110 4195,-1110 4195,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fe2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-1038 4215,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="6590@x" ObjectIDND1="6591@x" ObjectIDND2="g_20fd3c0@0" ObjectIDZND0="g_1fe43d0@0" ObjectIDZND1="18034@1" Pin0InfoVect0LinkObjId="g_1fe43d0_0" Pin0InfoVect1LinkObjId="g_20fe4f0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40505_0" Pin1InfoVect1LinkObjId="SW-40506_0" Pin1InfoVect2LinkObjId="g_20fd3c0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-1038 4215,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fe4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-1110 4215,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="6590@x" ObjectIDND1="6591@x" ObjectIDND2="g_20fd3c0@0" ObjectIDZND0="18034@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40505_0" Pin1InfoVect1LinkObjId="SW-40506_0" Pin1InfoVect2LinkObjId="g_20fd3c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-1110 4215,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2036ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-861 4103,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6586@0" ObjectIDZND0="6613@1" Pin0InfoVect0LinkObjId="SW-40556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-861 4103,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20370e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-806 4103,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6613@0" ObjectIDZND0="6612@1" Pin0InfoVect0LinkObjId="SW-40554_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-806 4103,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2037300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-753 4103,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6612@0" ObjectIDZND0="6624@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40554_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-753 4103,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2039150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-647 4103,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6624@0" ObjectIDZND0="6614@1" Pin0InfoVect0LinkObjId="SW-40558_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2037300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-647 4103,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2039370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-497 4103,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6587@0" ObjectIDZND0="6615@0" Pin0InfoVect0LinkObjId="SW-40559_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-497 4103,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2039590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-563 4104,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6615@1" ObjectIDZND0="6614@0" Pin0InfoVect0LinkObjId="SW-40558_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-563 4104,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2021ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-861 4578,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6586@0" ObjectIDZND0="6616@0" Pin0InfoVect0LinkObjId="SW-40570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-861 4578,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2021ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-922 4578,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6616@1" ObjectIDZND0="g_20cf7f0@0" ObjectIDZND1="g_2024de0@0" ObjectIDZND2="6617@x" Pin0InfoVect0LinkObjId="g_20cf7f0_0" Pin0InfoVect1LinkObjId="g_2024de0_0" Pin0InfoVect2LinkObjId="SW-40571_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-922 4578,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2024070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-943 4598,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_20cf7f0@0" ObjectIDND1="g_2024de0@0" ObjectIDND2="6616@x" ObjectIDZND0="6617@0" Pin0InfoVect0LinkObjId="SW-40571_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20cf7f0_0" Pin1InfoVect1LinkObjId="g_2024de0_0" Pin1InfoVect2LinkObjId="SW-40570_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-943 4598,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2024290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4634,-943 4657,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6617@1" ObjectIDZND0="g_20244b0@0" Pin0InfoVect0LinkObjId="g_20244b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40571_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4634,-943 4657,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-999 4578,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2024de0@1" ObjectIDZND0="g_2111420@0" Pin0InfoVect0LinkObjId="g_2111420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2024de0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-999 4578,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cf190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-954 4539,-954 4539,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6616@x" ObjectIDND1="6617@x" ObjectIDND2="g_2024de0@0" ObjectIDZND0="g_20cf7f0@0" Pin0InfoVect0LinkObjId="g_20cf7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40570_0" Pin1InfoVect1LinkObjId="SW-40571_0" Pin1InfoVect2LinkObjId="g_2024de0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-954 4539,-954 4539,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cf3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-943 4578,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6616@x" ObjectIDND1="6617@x" ObjectIDZND0="g_20cf7f0@0" ObjectIDZND1="g_2024de0@0" Pin0InfoVect0LinkObjId="g_20cf7f0_0" Pin0InfoVect1LinkObjId="g_2024de0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40570_0" Pin1InfoVect1LinkObjId="SW-40571_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-943 4578,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cf5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-954 4578,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6616@x" ObjectIDND1="6617@x" ObjectIDND2="g_20cf7f0@0" ObjectIDZND0="g_2024de0@0" Pin0InfoVect0LinkObjId="g_2024de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40570_0" Pin1InfoVect1LinkObjId="SW-40571_0" Pin1InfoVect2LinkObjId="g_20cf7f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-954 4578,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2089e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-497 3901,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6587@0" ObjectIDZND0="6597@1" Pin0InfoVect0LinkObjId="SW-40521_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-497 3901,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-497 4342,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6587@0" ObjectIDZND0="6610@1" Pin0InfoVect0LinkObjId="SW-40549_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-497 4342,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208c4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-497 4558,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6587@0" ObjectIDZND0="6604@1" Pin0InfoVect0LinkObjId="SW-40535_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-497 4558,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_209bb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4752,-497 4752,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6587@0" ObjectIDZND0="6607@1" Pin0InfoVect0LinkObjId="SW-40542_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4752,-497 4752,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2065900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-619 3839,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2065530@1" ObjectIDZND0="g_2114ac0@1" Pin0InfoVect0LinkObjId="g_2114ac0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2065530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-619 3839,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2065b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-574 3800,-574 3800,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2065530@0" ObjectIDND1="6618@x" ObjectIDZND0="g_2066020@0" Pin0InfoVect0LinkObjId="g_2066020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2065530_0" Pin1InfoVect1LinkObjId="SW-40573_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-574 3800,-574 3800,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2065dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-574 3839,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2066020@0" ObjectIDND1="6618@x" ObjectIDZND0="g_2065530@0" Pin0InfoVect0LinkObjId="g_2065530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2066020_0" Pin1InfoVect1LinkObjId="SW-40573_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-574 3839,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2066400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-497 3839,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6587@0" ObjectIDZND0="6618@0" Pin0InfoVect0LinkObjId="SW-40573_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-497 3839,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2066660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-551 3839,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6618@1" ObjectIDZND0="g_2065530@0" ObjectIDZND1="g_2066020@0" Pin0InfoVect0LinkObjId="g_2065530_0" Pin0InfoVect1LinkObjId="g_2066020_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40573_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-551 3839,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e02b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-861 4621,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="6586@0" ObjectIDZND0="g_20dfb10@0" Pin0InfoVect0LinkObjId="g_20dfb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-861 4621,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e04e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-795 4620,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_20dfb10@1" ObjectIDZND0="6625@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dfb10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-795 4620,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e0cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-497 4947,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="6587@0" ObjectIDZND0="g_20e0740@0" Pin0InfoVect0LinkObjId="g_20e0740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-497 4947,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e0f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4946,-434 4946,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_20e0740@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e0740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4946,-434 4946,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20e1170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-89 3985,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2102270@0" Pin0InfoVect0LinkObjId="g_2102270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-89 3985,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fe3dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="3951,-210 3951,-94 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-210 3951,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-1022 4193,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_20fd3c0@0" ObjectIDND1="g_1fe43d0@0" ObjectIDND2="18034@1" ObjectIDZND0="6591@1" Pin0InfoVect0LinkObjId="SW-40506_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20fd3c0_0" Pin1InfoVect1LinkObjId="g_1fe43d0_0" Pin1InfoVect2LinkObjId="g_20fe4f0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-1022 4193,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-1022 4134,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6591@0" ObjectIDZND0="g_20fc650@0" Pin0InfoVect0LinkObjId="g_20fc650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-1022 4134,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2020d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-480 3632,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6593@1" ObjectIDZND0="6587@0" Pin0InfoVect0LinkObjId="g_1fba380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-480 3632,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2020fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-444 3632,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6593@0" ObjectIDZND0="6592@1" Pin0InfoVect0LinkObjId="SW-40511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-444 3632,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ffbc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-750 4835,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28375@0" ObjectIDZND0="28373@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-750 4835,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffbe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-644 4834,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28373@0" ObjectIDZND0="28377@1" Pin0InfoVect0LinkObjId="SW-187320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ffbc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-644 4834,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ffcbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-861 4835,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6586@0" ObjectIDZND0="28376@0" Pin0InfoVect0LinkObjId="SW-187300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-861 4835,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2002a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-804 4835,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28376@1" ObjectIDZND0="28375@x" ObjectIDZND1="28374@x" Pin0InfoVect0LinkObjId="SW-187291_0" Pin0InfoVect1LinkObjId="SW-187306_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-804 4835,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2002c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-786 4835,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28376@x" ObjectIDND1="28374@x" ObjectIDZND0="28375@1" Pin0InfoVect0LinkObjId="SW-187291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187300_0" Pin1InfoVect1LinkObjId="SW-187306_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-786 4835,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2002ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-765 4782,-786 4835,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28374@0" ObjectIDZND0="28376@x" ObjectIDZND1="28375@x" Pin0InfoVect0LinkObjId="SW-187300_0" Pin0InfoVect1LinkObjId="SW-187291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-765 4782,-786 4835,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2003140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-729 4782,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28374@1" ObjectIDZND0="g_20033a0@0" Pin0InfoVect0LinkObjId="g_20033a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187306_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-729 4782,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202e140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-534 5120,-560 4834,-560 4834,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28378@0" ObjectIDZND0="28377@0" Pin0InfoVect0LinkObjId="SW-187320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-534 5120,-560 4834,-560 4834,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20323f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5156,-254 5156,-272 5120,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2031610@0" ObjectIDZND0="g_1fe52b0@0" ObjectIDZND1="6602@x" ObjectIDZND2="34458@x" Pin0InfoVect0LinkObjId="g_1fe52b0_0" Pin0InfoVect1LinkObjId="SW-40529_0" Pin0InfoVect2LinkObjId="EC-CX_QSZ.064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2031610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5156,-254 5156,-272 5120,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2114860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-496 5120,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28378@1" ObjectIDZND0="g_2113de0@1" Pin0InfoVect0LinkObjId="g_2113de0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-496 5120,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2118270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-700 3840,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2115670@0" ObjectIDZND0="g_2114ac0@0" Pin0InfoVect0LinkObjId="g_2114ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2115670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-700 3840,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fb8de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-595 4475,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-595 4475,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fba380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-595 4394,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="6587@0" Pin0InfoVect0LinkObjId="g_2020d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-595 4394,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-497 4394,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6587@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2020d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-497 4394,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbb0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-595 4394,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" ObjectIDND0="0@x" ObjectIDND1="6587@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_2020d70_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-595 4394,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fbb330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4422,-549 4422,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1fb9040@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb9040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4422,-549 4422,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20bb180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-446 5120,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2113de0@0" ObjectIDZND0="6601@1" Pin0InfoVect0LinkObjId="SW-40528_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2113de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-446 5120,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2075180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-195 3838,-206 3928,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2134c60@0" ObjectIDZND0="6599@0" Pin0InfoVect0LinkObjId="SW-40525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2134c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-195 3838,-206 3928,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20753e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-105 3901,-89 3928,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-105 3901,-89 3928,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff1af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-1024 3889,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6621@1" ObjectIDZND0="6620@x" ObjectIDZND1="g_2140800@0" ObjectIDZND2="g_1fe3fb0@0" Pin0InfoVect0LinkObjId="SW-40603_0" Pin0InfoVect1LinkObjId="g_2140800_0" Pin0InfoVect2LinkObjId="g_1fe3fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-1024 3889,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff2340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-1024 3889,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6621@x" ObjectIDND1="g_2140800@0" ObjectIDND2="g_1fe3fb0@0" ObjectIDZND0="6620@1" Pin0InfoVect0LinkObjId="SW-40603_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40604_0" Pin1InfoVect1LinkObjId="g_2140800_0" Pin1InfoVect2LinkObjId="g_1fe3fb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-1024 3889,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff2530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-1053 3933,-1032 3889,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2140800@0" ObjectIDZND0="6621@x" ObjectIDZND1="6620@x" ObjectIDZND2="g_1fe3fb0@0" Pin0InfoVect0LinkObjId="SW-40604_0" Pin0InfoVect1LinkObjId="SW-40603_0" Pin0InfoVect2LinkObjId="g_1fe3fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2140800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-1053 3933,-1032 3889,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff2fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-1032 3889,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2140800@0" ObjectIDND1="g_1fe3fb0@0" ObjectIDND2="41848@x" ObjectIDZND0="6621@x" ObjectIDZND1="6620@x" Pin0InfoVect0LinkObjId="SW-40604_0" Pin0InfoVect1LinkObjId="SW-40603_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2140800_0" Pin1InfoVect1LinkObjId="g_1fe3fb0_0" Pin1InfoVect2LinkObjId="EC-CX_QSZ.362Ld_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-1032 3889,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff3200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-1100 3869,-1108 3889,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fe3fb0@0" ObjectIDZND0="41848@x" ObjectIDZND1="g_2140800@0" ObjectIDZND2="6621@x" Pin0InfoVect0LinkObjId="EC-CX_QSZ.362Ld_0" Pin0InfoVect1LinkObjId="g_2140800_0" Pin0InfoVect2LinkObjId="SW-40604_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe3fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-1100 3869,-1108 3889,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff3cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-1132 3889,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="41848@0" ObjectIDZND0="g_1fe3fb0@0" ObjectIDZND1="g_2140800@0" ObjectIDZND2="6621@x" Pin0InfoVect0LinkObjId="g_1fe3fb0_0" Pin0InfoVect1LinkObjId="g_2140800_0" Pin0InfoVect2LinkObjId="SW-40604_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_QSZ.362Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-1132 3889,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff3f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-1108 3889,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1fe3fb0@0" ObjectIDND1="41848@x" ObjectIDZND0="g_2140800@0" ObjectIDZND1="6621@x" ObjectIDZND2="6620@x" Pin0InfoVect0LinkObjId="g_2140800_0" Pin0InfoVect1LinkObjId="SW-40604_0" Pin0InfoVect2LinkObjId="SW-40603_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fe3fb0_0" Pin1InfoVect1LinkObjId="EC-CX_QSZ.362Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-1108 3889,-1032 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37331" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5913" ObjectName="DYN-CX_QSZ"/>
     <cge:Meas_Ref ObjectId="37331"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f8d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 176.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fa180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 161.500000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fae440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 147.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1faeaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 132.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1faeec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 118.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 -801.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb10f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 176.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb1340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 161.500000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb1550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 147.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb1790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 132.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb19d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 118.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb1d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 638.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb1f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 623.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb21c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 608.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb2400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 578.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2121200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.000000 563.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2121440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 593.000000) translate(0,12)">Ib(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21219b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 158.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2121c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 173.000000) translate(0,12)">Q(KVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2121f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 702.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2123030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 687.000000) translate(0,12)">油温（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2124680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 288.500000) translate(0,12)">Ua(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2124c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 270.500000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2124fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 940.500000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21254f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 925.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 910.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21259b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 895.500000) translate(0,12)">Uo(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3598.000000 880.500000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 580.500000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2126190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 565.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21263d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 550.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201c460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3630.000000 535.500000) translate(0,12)">Uo(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201c660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3616.000000 520.500000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa8f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 785.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa9820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4188.000000 770.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa9aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 754.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa9ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 800.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20344e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 793.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2034780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 778.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20349c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 762.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2034c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4898.000000 808.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210fe90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 746.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2110130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 762.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2110370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 780.500000) translate(0,12)">Ua(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b3860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 175.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b3e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 160.500000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b40d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 146.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b4310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 131.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b4550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 117.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b4880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 175.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b4af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 160.500000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b4d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 146.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b4f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 131.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b51b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 117.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b54e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 175.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b5750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 160.500000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b5990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 146.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b5bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 131.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b5e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 117.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 -807.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b6140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 176.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b63f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 161.500000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b6630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 147.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b6870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 132.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b6ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 118.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b6de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5048.000000 154.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b7050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5037.000000 139.500000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b7290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 125.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b74d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 110.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b7710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.000000 96.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2076e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 731.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2077070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 716.000000) translate(0,12)">油温（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff0650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 647.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff0b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 573.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff0d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4899.000000 677.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff0fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.000000 632.500000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff11f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.000000 617.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff1430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.000000 602.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff1670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 586.500000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff18b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 662.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DG" endPointId="0" endStationName="CX_QSZ" flowDrawDirect="1" flowShape="0" id="AC-35kV.qingshanzui_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4215,-1123 4215,-1150 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18034" ObjectName="AC-35kV.qingshanzui_line"/>
    <cge:TPSR_Ref TObjectID="18034_SS-62"/></metadata>
   <polyline fill="none" opacity="0" points="4215,-1123 4215,-1150 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6586" cx="4621" cy="-861" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="4947" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6586" cx="4578" cy="-861" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="3901" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="4342" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="4558" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="4752" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="3839" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="3632" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="4103" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6586" cx="4103" cy="-861" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6586" cx="4835" cy="-861" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6587" cx="4394" cy="-497" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6586" cx="3889" cy="-861" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6586" cx="4215" cy="-861" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6602" ObjectName="SW-CX_QSZ.CX_QSZ_0646SW"/>
     <cge:Meas_Ref ObjectId="40529"/>
    <cge:TPSR_Ref TObjectID="6602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40520">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6596" ObjectName="SW-CX_QSZ.CX_QSZ_0652SW"/>
     <cge:Meas_Ref ObjectId="40520"/>
    <cge:TPSR_Ref TObjectID="6596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40524">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6598" ObjectName="SW-CX_QSZ.CX_QSZ_0656SW"/>
     <cge:Meas_Ref ObjectId="40524"/>
    <cge:TPSR_Ref TObjectID="6598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.000000 -201.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6599" ObjectName="SW-CX_QSZ.CX_QSZ_06567SW"/>
     <cge:Meas_Ref ObjectId="40525"/>
    <cge:TPSR_Ref TObjectID="6599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.000000 -84.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40550">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6611" ObjectName="SW-CX_QSZ.CX_QSZ_0636SW"/>
     <cge:Meas_Ref ObjectId="40550"/>
    <cge:TPSR_Ref TObjectID="6611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40536">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6605" ObjectName="SW-CX_QSZ.CX_QSZ_0626SW"/>
     <cge:Meas_Ref ObjectId="40536"/>
    <cge:TPSR_Ref TObjectID="6605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40543">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4744.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6608" ObjectName="SW-CX_QSZ.CX_QSZ_0616SW"/>
     <cge:Meas_Ref ObjectId="40543"/>
    <cge:TPSR_Ref TObjectID="6608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -873.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6623" ObjectName="SW-CX_QSZ.CX_QSZ_3621SW"/>
     <cge:Meas_Ref ObjectId="40606"/>
    <cge:TPSR_Ref TObjectID="6623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -967.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6620" ObjectName="SW-CX_QSZ.CX_QSZ_3626SW"/>
     <cge:Meas_Ref ObjectId="40603"/>
    <cge:TPSR_Ref TObjectID="6620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 -1019.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6621" ObjectName="SW-CX_QSZ.CX_QSZ_36267SW"/>
     <cge:Meas_Ref ObjectId="40604"/>
    <cge:TPSR_Ref TObjectID="6621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40504">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -871.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6589" ObjectName="SW-CX_QSZ.CX_QSZ_3611SW"/>
     <cge:Meas_Ref ObjectId="40504"/>
    <cge:TPSR_Ref TObjectID="6589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40505">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -965.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6590" ObjectName="SW-CX_QSZ.CX_QSZ_3616SW"/>
     <cge:Meas_Ref ObjectId="40505"/>
    <cge:TPSR_Ref TObjectID="6590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40570">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -881.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6616" ObjectName="SW-CX_QSZ.CX_QSZ_3901SW"/>
     <cge:Meas_Ref ObjectId="40570"/>
    <cge:TPSR_Ref TObjectID="6616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40571">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -938.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6617" ObjectName="SW-CX_QSZ.CX_QSZ_39017SW"/>
     <cge:Meas_Ref ObjectId="40571"/>
    <cge:TPSR_Ref TObjectID="6617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40528">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6601" ObjectName="SW-CX_QSZ.CX_QSZ_0641SW"/>
     <cge:Meas_Ref ObjectId="40528"/>
    <cge:TPSR_Ref TObjectID="6601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40521">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 -437.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6597" ObjectName="SW-CX_QSZ.CX_QSZ_0651SW"/>
     <cge:Meas_Ref ObjectId="40521"/>
    <cge:TPSR_Ref TObjectID="6597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40549">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 -437.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6610" ObjectName="SW-CX_QSZ.CX_QSZ_0631SW"/>
     <cge:Meas_Ref ObjectId="40549"/>
    <cge:TPSR_Ref TObjectID="6610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40535">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -437.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6604" ObjectName="SW-CX_QSZ.CX_QSZ_0621SW"/>
     <cge:Meas_Ref ObjectId="40535"/>
    <cge:TPSR_Ref TObjectID="6604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40542">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4744.000000 -443.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6607" ObjectName="SW-CX_QSZ.CX_QSZ_0611SW"/>
     <cge:Meas_Ref ObjectId="40542"/>
    <cge:TPSR_Ref TObjectID="6607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40573">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3831.000000 -510.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6618" ObjectName="SW-CX_QSZ.CX_QSZ_0901SW"/>
     <cge:Meas_Ref ObjectId="40573"/>
    <cge:TPSR_Ref TObjectID="6618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -439.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6593" ObjectName="SW-CX_QSZ.CX_QSZ_0661SW"/>
     <cge:Meas_Ref ObjectId="40513"/>
    <cge:TPSR_Ref TObjectID="6593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6594" ObjectName="SW-CX_QSZ.CX_QSZ_0666SW"/>
     <cge:Meas_Ref ObjectId="40514"/>
    <cge:TPSR_Ref TObjectID="6594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40559">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -522.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6615" ObjectName="SW-CX_QSZ.CX_QSZ_0011SW"/>
     <cge:Meas_Ref ObjectId="40559"/>
    <cge:TPSR_Ref TObjectID="6615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40556">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -801.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6613" ObjectName="SW-CX_QSZ.CX_QSZ_3011SW"/>
     <cge:Meas_Ref ObjectId="40556"/>
    <cge:TPSR_Ref TObjectID="6613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40506">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -1017.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6591" ObjectName="SW-CX_QSZ.CX_QSZ_36167SW"/>
     <cge:Meas_Ref ObjectId="40506"/>
    <cge:TPSR_Ref TObjectID="6591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187300">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 -799.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28376" ObjectName="SW-CX_QSZ.CX_QSZ_3021SW"/>
     <cge:Meas_Ref ObjectId="187300"/>
    <cge:TPSR_Ref TObjectID="28376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187306">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.000000 -724.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28374" ObjectName="SW-CX_QSZ.CX_QSZ_30217SW"/>
     <cge:Meas_Ref ObjectId="187306"/>
    <cge:TPSR_Ref TObjectID="28374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -491.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28378" ObjectName="SW-CX_QSZ.CX_QSZ_0022SW"/>
     <cge:Meas_Ref ObjectId="187322"/>
    <cge:TPSR_Ref TObjectID="28378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 -590.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2191d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4893.000000 -318.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea8a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -203.000000) translate(0,15)">杨排线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d7e6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -180.000000) translate(0,15)">云南交投线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2158a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4297.000000 -203.000000) translate(0,15)">右岸施工线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20902c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -203.000000) translate(0,15)">生活区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc73f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -203.000000) translate(0,15)">石料厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_209df40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3844.000000 -1187.000000) translate(0,15)">至35kV坝后电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2064100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.000000 -1187.000000) translate(0,15)">至110kV东瓜变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20955e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -1152.000000) translate(0,15)">坝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20955e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -1152.000000) translate(0,33)">后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20955e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -1152.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20955e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -1152.000000) translate(0,69)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20955e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -1152.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2095890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4223.000000 -1151.000000) translate(0,15)">青</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2095890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4223.000000 -1151.000000) translate(0,33)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2095890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4223.000000 -1151.000000) translate(0,51)">嘴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2095890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4223.000000 -1151.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2095ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -759.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2095d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -1131.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20668c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -948.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2066e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3896.000000 -903.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2067280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3896.000000 -997.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20676b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -1015.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20678f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -946.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2067d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -901.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2067f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -995.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20681a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.000000 -911.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20683e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4596.000000 -969.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20686a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3845.452961 -540.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20688e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.444434 -774.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2068b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4109.444434 -831.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2068d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.444434 -609.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2068fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.444434 -549.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20691e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -887.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2069420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -523.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2069660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.257794 -422.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20de4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.257794 -469.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20de710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.257794 -375.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20de950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.953278 -420.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20deb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.953278 -467.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20deff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.953278 -373.000000) translate(0,12)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20df230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.953278 -243.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20df470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.881281 -371.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20df6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5126.881281 -418.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e13d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5126.881281 -324.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.953278 -229.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.819387 -420.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.819387 -467.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.819387 -373.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e21c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.509983 -420.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.509983 -467.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.509983 -373.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4760.299857 -426.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe1480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4758.299857 -473.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe1690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4758.299857 -379.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2018730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.500000 -1166.500000) translate(0,16)">青山嘴变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20192a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_201c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_201c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_201c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_201c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_201c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_201c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_201c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201cc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -1013.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffb550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4844.000000 -771.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffba40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4844.000000 -606.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ffc060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -230.000000) translate(0,16)">4779</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2030b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4842.000000 -829.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2031190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -754.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20313d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -521.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2032650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 -708.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2033880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 -690.000000) translate(0,12)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2035be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3404.000000 -1148.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_20364b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3404.000000 -1183.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_21105b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3144.000000 -766.000000) translate(0,17)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21184d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -672.000000) translate(0,15)">SZ11-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21184d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -672.000000) translate(0,33)">35±3×2.5%/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fbb6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4358.000000 -686.000000) translate(0,15)">10kV信号发生源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_20b7950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -196.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_20b7950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -196.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20b9df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -183.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_206d2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -701.000000) translate(0,15)">35kV1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_206f990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -673.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2071260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4869.000000 -732.000000) translate(0,15)">35kV2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2075640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3803.000000 -128.000000) translate(0,12)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2077510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3543.500000 -1169.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1fef1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -732.000000) translate(0,20)">隔刀远控</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2102270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 -80.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21028a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -197.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213fed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -1015.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20fc650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.033333 -0.000000 0.000000 -1.000000 4107.000000 -1013.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20244b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.000000 -934.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20033a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -690.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_QSZ"/>
</svg>