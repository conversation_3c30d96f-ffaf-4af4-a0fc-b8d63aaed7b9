<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-311" aopId="1835526" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1166 2046 1443">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape137">
    <ellipse cx="18" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="17" y2="19"/>
    <ellipse cx="28" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="19" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="19" y2="22"/>
    <ellipse cx="17" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="13" y2="16"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="6" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape73">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="22" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="28" y1="32" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="51" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="67" x2="59" y1="30" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="59" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49cbf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49cc8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49cd270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49cdf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49cf150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49cfd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49d0910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_49d1340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_49d2950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_49d2950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49d42c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49d42c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49d5e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49d5e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_49d6ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49d8760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_49d93b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_49da290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49dac00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49dc4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49dccd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49dd2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49ddcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49dee30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49df7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49e02a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_49e0c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_49e2050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_49e2950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_49e3860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49e42d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_49f2700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_49e5880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_49e64c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_49e7770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1453" width="2056" x="11" y="-1171"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.000000 -175.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 538.910714 -38.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279509">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.000000 -476.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44399" ObjectName="SW-CX_KQ.CX_KQ_301BK"/>
     <cge:Meas_Ref ObjectId="279509"/>
    <cge:TPSR_Ref TObjectID="44399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -446.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44404" ObjectName="SW-CX_KQ.CX_KQ_363BK"/>
     <cge:Meas_Ref ObjectId="279562"/>
    <cge:TPSR_Ref TObjectID="44404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1671.000000 -176.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1671.000000 -477.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44418" ObjectName="SW-CX_KQ.CX_KQ_302BK"/>
     <cge:Meas_Ref ObjectId="279608"/>
    <cge:TPSR_Ref TObjectID="44418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -458.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44423" ObjectName="SW-CX_KQ.CX_KQ_365BK"/>
     <cge:Meas_Ref ObjectId="279661"/>
    <cge:TPSR_Ref TObjectID="44423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 -463.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44425" ObjectName="SW-CX_KQ.CX_KQ_364BK"/>
     <cge:Meas_Ref ObjectId="279671"/>
    <cge:TPSR_Ref TObjectID="44425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279571">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -752.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44406" ObjectName="SW-CX_KQ.CX_KQ_312BK"/>
     <cge:Meas_Ref ObjectId="279571"/>
    <cge:TPSR_Ref TObjectID="44406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1786.000000 -768.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44411" ObjectName="SW-CX_KQ.CX_KQ_372BK"/>
     <cge:Meas_Ref ObjectId="279584"/>
    <cge:TPSR_Ref TObjectID="44411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279485">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 -771.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44392" ObjectName="SW-CX_KQ.CX_KQ_371BK"/>
     <cge:Meas_Ref ObjectId="279485"/>
    <cge:TPSR_Ref TObjectID="44392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.910714 -38.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.910714 -39.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1385.910714 -37.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1716.910714 -39.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1883.910714 -37.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_49b9780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -294.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43e0be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1262.000000 -289.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_LC" endPointId="0" endStationName="CX_KQ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_kuqu2" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1795,-1066 1795,-1150 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="44583" ObjectName="AC-35kV.LN_kuqu2"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1795,-1066 1795,-1150 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_KQ.CX_KQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="21786"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 -312.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 -312.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="44428" ObjectName="TF-CX_KQ.CX_KQ_1T"/>
    <cge:TPSR_Ref TObjectID="44428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_KQ.CX_KQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="21790"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1649.000000 -313.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1649.000000 -313.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="44429" ObjectName="TF-CX_KQ.CX_KQ_2T"/>
    <cge:TPSR_Ref TObjectID="44429"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_72d4c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-210 757,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-210 757,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_72d4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-173 757,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-173 757,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_253c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-73 548,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,-73 548,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_253c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-36 548,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,-36 548,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_434ed00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-413 757,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_434f1c0@0" ObjectIDZND0="44428@1" Pin0InfoVect0LinkObjId="g_4017910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_434f1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-413 757,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_434ef60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1671,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1671,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40176b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-238 757,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_4016c90@0" Pin0InfoVect0LinkObjId="g_4016c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-238 757,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4017910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-306 757,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_4016c90@1" ObjectIDZND0="44428@0" Pin0InfoVect0LinkObjId="g_434ed00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4016c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-306 757,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e47400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-620 572,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44431@0" ObjectIDZND0="44405@1" Pin0InfoVect0LinkObjId="SW-279563_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ffea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="572,-620 572,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_72d0770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="541,-510 572,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44465@0" ObjectIDZND0="44405@x" ObjectIDZND1="44404@x" Pin0InfoVect0LinkObjId="SW-279563_0" Pin0InfoVect1LinkObjId="SW-279562_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="541,-510 572,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_72d1260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-549 572,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44405@0" ObjectIDZND0="44465@x" ObjectIDZND1="44404@x" Pin0InfoVect0LinkObjId="SW-280081_0" Pin0InfoVect1LinkObjId="SW-279562_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="572,-549 572,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_72d14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-510 572,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44465@x" ObjectIDND1="44405@x" ObjectIDZND0="44404@1" Pin0InfoVect0LinkObjId="SW-279562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-280081_0" Pin1InfoVect1LinkObjId="SW-279563_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="572,-510 572,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fface0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="640,-406 572,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_72d1720@0" ObjectIDZND0="44404@x" ObjectIDZND1="g_2518780@0" Pin0InfoVect0LinkObjId="SW-279562_0" Pin0InfoVect1LinkObjId="g_2518780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_72d1720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="640,-406 572,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ffb7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-454 572,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="44404@0" ObjectIDZND0="g_72d1720@0" ObjectIDZND1="g_2518780@0" Pin0InfoVect0LinkObjId="g_72d1720_0" Pin0InfoVect1LinkObjId="g_2518780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="572,-454 572,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ffba30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-406 572,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_72d1720@0" ObjectIDND1="44404@x" ObjectIDZND0="g_2518780@0" Pin0InfoVect0LinkObjId="g_2518780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_72d1720_0" Pin1InfoVect1LinkObjId="SW-279562_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="572,-406 572,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ffea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-596 757,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44400@1" ObjectIDZND0="44431@0" Pin0InfoVect0LinkObjId="g_4a4f6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-596 757,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_8841aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-532 757,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44401@0" ObjectIDZND0="44399@x" ObjectIDZND1="44400@x" Pin0InfoVect0LinkObjId="SW-279509_0" Pin0InfoVect1LinkObjId="SW-279510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-532 757,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_8842590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-511 757,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44399@1" ObjectIDZND0="44401@x" ObjectIDZND1="44400@x" Pin0InfoVect0LinkObjId="SW-279511_0" Pin0InfoVect1LinkObjId="SW-279510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="757,-511 757,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_88427f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-532 757,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="44401@x" ObjectIDND1="44399@x" ObjectIDZND0="44400@0" Pin0InfoVect0LinkObjId="SW-279510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279511_0" Pin1InfoVect1LinkObjId="SW-279509_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-532 757,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_88437c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="781,-476 757,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_8842a50@0" ObjectIDZND0="g_434f1c0@0" ObjectIDZND1="44399@x" Pin0InfoVect0LinkObjId="g_434f1c0_0" Pin0InfoVect1LinkObjId="SW-279509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_8842a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="781,-476 757,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_88442b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-466 757,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_434f1c0@1" ObjectIDZND0="g_8842a50@0" ObjectIDZND1="44399@x" Pin0InfoVect0LinkObjId="g_8842a50_0" Pin0InfoVect1LinkObjId="SW-279509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_434f1c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="757,-466 757,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_8844510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-476 757,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_8842a50@0" ObjectIDND1="g_434f1c0@0" ObjectIDZND0="44399@0" Pin0InfoVect0LinkObjId="SW-279509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_8842a50_0" Pin1InfoVect1LinkObjId="g_434f1c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-476 757,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_8844770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-156 757,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-156 757,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_49b5430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-620 928,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44431@0" ObjectIDZND0="44402@1" Pin0InfoVect0LinkObjId="SW-279556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ffea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-620 928,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_49b87d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="906,-527 928,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44403@0" ObjectIDZND0="44402@x" ObjectIDZND1="g_88449d0@0" Pin0InfoVect0LinkObjId="SW-279556_0" Pin0InfoVect1LinkObjId="g_88449d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="906,-527 928,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_49b92c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-562 928,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44402@0" ObjectIDZND0="44403@x" ObjectIDZND1="g_88449d0@0" Pin0InfoVect0LinkObjId="SW-279557_0" Pin0InfoVect1LinkObjId="g_88449d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="928,-562 928,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_49b9520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-527 928,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="44403@x" ObjectIDND1="44402@x" ObjectIDZND0="g_88449d0@0" Pin0InfoVect0LinkObjId="g_88449d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279557_0" Pin1InfoVect1LinkObjId="SW-279556_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-527 928,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43d9350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="964,-422 928,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="g_43d85e0@0" ObjectIDZND0="g_88449d0@0" ObjectIDZND1="g_49b9780@0" Pin0InfoVect0LinkObjId="g_88449d0_0" Pin0InfoVect1LinkObjId="g_49b9780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43d85e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="964,-422 928,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43d9e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-466 928,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="g_88449d0@1" ObjectIDZND0="g_43d85e0@0" ObjectIDZND1="g_49b9780@0" Pin0InfoVect0LinkObjId="g_43d85e0_0" Pin0InfoVect1LinkObjId="g_49b9780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_88449d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="928,-466 928,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43da0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-422 928,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_43d85e0@0" ObjectIDND1="g_88449d0@0" ObjectIDZND0="g_49b9780@0" Pin0InfoVect0LinkObjId="g_49b9780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_43d85e0_0" Pin1InfoVect1LinkObjId="g_88449d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-422 928,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43e04c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-522 1302,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44422@0" ObjectIDZND0="44421@x" ObjectIDZND1="g_43da300@0" Pin0InfoVect0LinkObjId="SW-279655_0" Pin0InfoVect1LinkObjId="g_43da300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-522 1302,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43e0720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1302,-557 1302,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44421@0" ObjectIDZND0="44422@x" ObjectIDZND1="g_43da300@0" Pin0InfoVect0LinkObjId="SW-279656_0" Pin0InfoVect1LinkObjId="g_43da300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1302,-557 1302,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43e0980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1302,-522 1302,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="44421@x" ObjectIDND1="44422@x" ObjectIDZND0="g_43da300@0" Pin0InfoVect0LinkObjId="g_43da300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279655_0" Pin1InfoVect1LinkObjId="SW-279656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1302,-522 1302,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43e31e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-417 1302,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="g_43e2470@0" ObjectIDZND0="g_43da300@0" ObjectIDZND1="g_43e0be0@0" Pin0InfoVect0LinkObjId="g_43da300_0" Pin0InfoVect1LinkObjId="g_43e0be0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43e2470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-417 1302,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43e3440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1302,-461 1302,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="g_43da300@1" ObjectIDZND0="g_43e2470@0" ObjectIDZND1="g_43e0be0@0" Pin0InfoVect0LinkObjId="g_43e2470_0" Pin0InfoVect1LinkObjId="g_43e0be0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43da300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1302,-461 1302,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43e36a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1302,-417 1302,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_43da300@0" ObjectIDND1="g_43e2470@0" ObjectIDZND0="g_43e0be0@0" Pin0InfoVect0LinkObjId="g_43e0be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_43da300_0" Pin1InfoVect1LinkObjId="g_43e2470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1302,-417 1302,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c66230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-211 1680,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-211 1680,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c66490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-174 1680,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-174 1680,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4c6c800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-414 1680,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_4c6ca60@0" ObjectIDZND0="44429@1" Pin0InfoVect0LinkObjId="g_4c6e100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4c6ca60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-414 1680,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c6dea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-239 1680,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_4c6d480@0" Pin0InfoVect0LinkObjId="g_4c6d480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-239 1680,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c6e100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-309 1680,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_4c6d480@1" ObjectIDZND0="44429@0" Pin0InfoVect0LinkObjId="g_4c6c800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4c6d480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-309 1680,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4c73cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1642,-533 1680,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44420@0" ObjectIDZND0="44418@x" ObjectIDZND1="44419@x" Pin0InfoVect0LinkObjId="SW-279608_0" Pin0InfoVect1LinkObjId="SW-279609_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1642,-533 1680,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4c73f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-512 1680,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44418@1" ObjectIDZND0="44420@x" ObjectIDZND1="44419@x" Pin0InfoVect0LinkObjId="SW-279610_0" Pin0InfoVect1LinkObjId="SW-279609_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279608_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-512 1680,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4c741b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-533 1680,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="44418@x" ObjectIDND1="44420@x" ObjectIDZND0="44419@0" Pin0InfoVect0LinkObjId="SW-279609_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279608_0" Pin1InfoVect1LinkObjId="SW-279610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-533 1680,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4c75180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1704,-477 1680,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_4c74410@0" ObjectIDZND0="g_4c6ca60@0" ObjectIDZND1="44418@x" Pin0InfoVect0LinkObjId="g_4c6ca60_0" Pin0InfoVect1LinkObjId="SW-279608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4c74410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-477 1680,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4c753e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-467 1680,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_4c6ca60@1" ObjectIDZND0="g_4c74410@0" ObjectIDZND1="44418@x" Pin0InfoVect0LinkObjId="g_4c74410_0" Pin0InfoVect1LinkObjId="SW-279608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4c6ca60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-467 1680,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4c75640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-477 1680,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_4c6ca60@0" ObjectIDND1="g_4c74410@0" ObjectIDZND0="44418@0" Pin0InfoVect0LinkObjId="SW-279608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4c6ca60_0" Pin1InfoVect1LinkObjId="g_4c74410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-477 1680,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45476d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1842,-522 1873,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44466@0" ObjectIDZND0="44424@x" ObjectIDZND1="44423@x" Pin0InfoVect0LinkObjId="SW-279662_0" Pin0InfoVect1LinkObjId="SW-279661_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1842,-522 1873,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4547930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-561 1873,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44424@0" ObjectIDZND0="44423@x" ObjectIDZND1="44466@x" Pin0InfoVect0LinkObjId="SW-279661_0" Pin0InfoVect1LinkObjId="SW-280082_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-561 1873,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4547b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-522 1873,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44424@x" ObjectIDND1="44466@x" ObjectIDZND0="44423@1" Pin0InfoVect0LinkObjId="SW-279661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279662_0" Pin1InfoVect1LinkObjId="SW-280082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-522 1873,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4548ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1941,-418 1873,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_4547df0@0" ObjectIDZND0="44423@x" ObjectIDZND1="g_25182d0@0" Pin0InfoVect0LinkObjId="SW-279661_0" Pin0InfoVect1LinkObjId="g_25182d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4547df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1941,-418 1873,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4548e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-466 1873,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="44423@0" ObjectIDZND0="g_4547df0@0" ObjectIDZND1="g_25182d0@0" Pin0InfoVect0LinkObjId="g_4547df0_0" Pin0InfoVect1LinkObjId="g_25182d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-466 1873,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4549060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-418 1873,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="44423@x" ObjectIDND1="g_4547df0@0" ObjectIDZND0="g_25182d0@0" Pin0InfoVect0LinkObjId="g_25182d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279661_0" Pin1InfoVect1LinkObjId="g_4547df0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-418 1873,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_454fd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-597 1680,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44419@1" ObjectIDZND0="44432@0" Pin0InfoVect0LinkObjId="g_45505a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-597 1680,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45505a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-597 1873,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44424@1" ObjectIDZND0="44432@0" Pin0InfoVect0LinkObjId="g_454fd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-597 1873,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4550dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1680,-157 1680,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-157 1680,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4551030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1302,-593 1302,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44421@1" ObjectIDZND0="44432@0" Pin0InfoVect0LinkObjId="g_454fd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279655_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1302,-593 1302,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4556700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-622 1478,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44432@0" ObjectIDZND0="44426@1" Pin0InfoVect0LinkObjId="SW-279672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_454fd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-622 1478,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4556960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-471 1478,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="44425@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-471 1478,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4559d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1434,-530 1478,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44427@0" ObjectIDZND0="44426@x" ObjectIDZND1="44425@x" Pin0InfoVect0LinkObjId="SW-279672_0" Pin0InfoVect1LinkObjId="SW-279671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1434,-530 1478,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_455a840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-561 1478,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44426@0" ObjectIDZND0="44427@x" ObjectIDZND1="44425@x" Pin0InfoVect0LinkObjId="SW-279673_0" Pin0InfoVect1LinkObjId="SW-279671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-561 1478,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_455aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-530 1478,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44427@x" ObjectIDND1="44426@x" ObjectIDZND0="44425@1" Pin0InfoVect0LinkObjId="SW-279671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279673_0" Pin1InfoVect1LinkObjId="SW-279672_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-530 1478,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43724e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-665 1248,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44408@0" ObjectIDZND0="44432@0" Pin0InfoVect0LinkObjId="g_454fd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279573_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-665 1248,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4372740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-620 1005,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44431@0" ObjectIDZND0="44407@0" Pin0InfoVect0LinkObjId="SW-279572_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ffea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-620 1005,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43729a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-729 1005,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44409@0" ObjectIDZND0="44407@x" ObjectIDZND1="44406@x" Pin0InfoVect0LinkObjId="SW-279572_0" Pin0InfoVect1LinkObjId="SW-279571_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="977,-729 1005,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4373490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-698 1005,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44407@1" ObjectIDZND0="44409@x" ObjectIDZND1="44406@x" Pin0InfoVect0LinkObjId="SW-279574_0" Pin0InfoVect1LinkObjId="SW-279571_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-698 1005,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43736f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-729 1005,-762 1116,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44409@x" ObjectIDND1="44407@x" ObjectIDZND0="44406@1" Pin0InfoVect0LinkObjId="SW-279571_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279574_0" Pin1InfoVect1LinkObjId="SW-279572_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-729 1005,-762 1116,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4373950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-725 1248,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44410@0" ObjectIDZND0="44406@x" ObjectIDZND1="44408@x" Pin0InfoVect0LinkObjId="SW-279571_0" Pin0InfoVect1LinkObjId="SW-279573_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279575_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-725 1248,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4374440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1143,-762 1248,-762 1248,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44406@0" ObjectIDZND0="44410@x" ObjectIDZND1="44408@x" Pin0InfoVect0LinkObjId="SW-279575_0" Pin0InfoVect1LinkObjId="SW-279573_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279571_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1143,-762 1248,-762 1248,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43746a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-725 1248,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="44410@x" ObjectIDND1="44406@x" ObjectIDZND0="44408@1" Pin0InfoVect0LinkObjId="SW-279573_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279575_0" Pin1InfoVect1LinkObjId="SW-279571_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-725 1248,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43876e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-622 1795,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44432@0" ObjectIDZND0="44412@0" Pin0InfoVect0LinkObjId="SW-279585_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_454fd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-622 1795,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438a9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-838 1795,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44416@0" ObjectIDZND0="44411@x" ObjectIDZND1="44413@x" Pin0InfoVect0LinkObjId="SW-279584_0" Pin0InfoVect1LinkObjId="SW-279586_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279589_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-838 1795,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438b4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-803 1795,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44411@1" ObjectIDZND0="44416@x" ObjectIDZND1="44413@x" Pin0InfoVect0LinkObjId="SW-279589_0" Pin0InfoVect1LinkObjId="SW-279586_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-803 1795,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438b710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-838 1795,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="44416@x" ObjectIDND1="44411@x" ObjectIDZND0="44413@0" Pin0InfoVect0LinkObjId="SW-279586_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279589_0" Pin1InfoVect1LinkObjId="SW-279584_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-838 1795,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438b970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1765,-741 1795,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44415@0" ObjectIDZND0="44411@x" ObjectIDZND1="g_43855b0@0" ObjectIDZND2="44412@x" Pin0InfoVect0LinkObjId="SW-279584_0" Pin0InfoVect1LinkObjId="g_43855b0_0" Pin0InfoVect2LinkObjId="SW-279585_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279588_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1765,-741 1795,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438c460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-741 1795,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="44415@x" ObjectIDND1="g_43855b0@0" ObjectIDND2="44412@x" ObjectIDZND0="44411@0" Pin0InfoVect0LinkObjId="SW-279584_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-279588_0" Pin1InfoVect1LinkObjId="g_43855b0_0" Pin1InfoVect2LinkObjId="SW-279585_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-741 1795,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438c6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1833,-713 1795,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_43855b0@0" ObjectIDZND0="44412@x" ObjectIDZND1="44415@x" ObjectIDZND2="44411@x" Pin0InfoVect0LinkObjId="SW-279585_0" Pin0InfoVect1LinkObjId="SW-279588_0" Pin0InfoVect2LinkObjId="SW-279584_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43855b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1833,-713 1795,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-697 1795,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="44412@1" ObjectIDZND0="g_43855b0@0" ObjectIDZND1="44415@x" ObjectIDZND2="44411@x" Pin0InfoVect0LinkObjId="g_43855b0_0" Pin0InfoVect1LinkObjId="SW-279588_0" Pin0InfoVect2LinkObjId="SW-279584_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279585_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-697 1795,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-713 1795,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_43855b0@0" ObjectIDND1="44412@x" ObjectIDZND0="44415@x" ObjectIDZND1="44411@x" Pin0InfoVect0LinkObjId="SW-279588_0" Pin0InfoVect1LinkObjId="SW-279584_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_43855b0_0" Pin1InfoVect1LinkObjId="SW-279585_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-713 1795,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438d670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1832,-1037 1795,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4386360@0" ObjectIDZND0="44583@1" ObjectIDZND1="44417@x" ObjectIDZND2="44414@x" Pin0InfoVect0LinkObjId="g_438e3c0_1" Pin0InfoVect1LinkObjId="SW-279590_0" Pin0InfoVect2LinkObjId="SW-279587_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4386360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1832,-1037 1795,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_438e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-1037 1795,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_4386360@0" ObjectIDND1="44417@x" ObjectIDND2="44414@x" ObjectIDZND0="44583@0" Pin0InfoVect0LinkObjId="g_438d670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4386360_0" Pin1InfoVect1LinkObjId="SW-279590_0" Pin1InfoVect2LinkObjId="SW-279587_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-1037 1795,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-841 642,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44397@0" ObjectIDZND0="44392@x" ObjectIDZND1="44394@x" Pin0InfoVect0LinkObjId="SW-279485_0" Pin0InfoVect1LinkObjId="SW-279487_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="607,-841 642,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-806 642,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44392@1" ObjectIDZND0="44397@x" ObjectIDZND1="44394@x" Pin0InfoVect0LinkObjId="SW-279490_0" Pin0InfoVect1LinkObjId="SW-279487_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279485_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="642,-806 642,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4cec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-841 642,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="44392@x" ObjectIDND1="44397@x" ObjectIDZND0="44394@0" Pin0InfoVect0LinkObjId="SW-279487_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279485_0" Pin1InfoVect1LinkObjId="SW-279490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-841 642,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4d120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-744 642,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="44396@0" ObjectIDZND0="44392@x" ObjectIDZND1="44393@x" ObjectIDZND2="g_4a48dd0@0" Pin0InfoVect0LinkObjId="SW-279485_0" Pin0InfoVect1LinkObjId="SW-279486_0" Pin0InfoVect2LinkObjId="g_4a48dd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="612,-744 642,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4d380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-744 642,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="44396@x" ObjectIDND1="44393@x" ObjectIDND2="g_4a48dd0@0" ObjectIDZND0="44392@0" Pin0InfoVect0LinkObjId="SW-279485_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-279489_0" Pin1InfoVect1LinkObjId="SW-279486_0" Pin1InfoVect2LinkObjId="g_4a48dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-744 642,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4d5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="680,-716 642,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_4a48dd0@0" ObjectIDZND0="44393@x" ObjectIDZND1="44392@x" ObjectIDZND2="44396@x" Pin0InfoVect0LinkObjId="SW-279486_0" Pin0InfoVect1LinkObjId="SW-279485_0" Pin0InfoVect2LinkObjId="SW-279489_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a48dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="680,-716 642,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4d840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-700 642,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="44393@1" ObjectIDZND0="g_4a48dd0@0" ObjectIDZND1="44392@x" ObjectIDZND2="44396@x" Pin0InfoVect0LinkObjId="g_4a48dd0_0" Pin0InfoVect1LinkObjId="SW-279485_0" Pin0InfoVect2LinkObjId="SW-279489_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="642,-700 642,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-716 642,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44393@x" ObjectIDND1="g_4a48dd0@0" ObjectIDZND0="44392@x" ObjectIDZND1="44396@x" Pin0InfoVect0LinkObjId="SW-279485_0" Pin0InfoVect1LinkObjId="SW-279489_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279486_0" Pin1InfoVect1LinkObjId="g_4a48dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="642,-716 642,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4a4f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-664 642,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44393@0" ObjectIDZND0="44431@0" Pin0InfoVect0LinkObjId="g_3ffea60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279486_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-664 642,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a504b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-101 548,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,-101 548,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a53e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="585,12 548,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_253c830@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_4a564c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4a564c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_253c830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="585,12 548,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a54900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,-19 548,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_253c830@0" ObjectIDZND1="0@x" ObjectIDZND2="g_4a564c0@0" Pin0InfoVect0LinkObjId="g_253c830_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4a564c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="548,-19 548,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a54b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,43 548,43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_253c830@0" ObjectIDZND1="0@x" ObjectIDZND2="g_4a564c0@0" Pin0InfoVect0LinkObjId="g_253c830_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4a564c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="513,43 548,43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a55650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,13 548,43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_253c830@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_4a564c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4a564c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_253c830_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="548,13 548,43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a56f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,43 548,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_253c830@0" ObjectIDND2="0@x" ObjectIDZND0="g_4a564c0@0" Pin0InfoVect0LinkObjId="g_4a564c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_253c830_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,43 548,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a57160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="548,103 548,139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_4a564c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a564c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="548,103 548,139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a59430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-73 726,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-73 726,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a59690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-36 726,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-36 726,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a635d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,12 726,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_4a598f0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_4a64b60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4a64b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a598f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="763,12 726,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a63830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-19 726,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_4a598f0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_4a64b60@0" Pin0InfoVect0LinkObjId="g_4a598f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4a64b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="726,-19 726,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a63a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,43 726,43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4a598f0@0" ObjectIDZND2="g_4a64b60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4a598f0_0" Pin0InfoVect2LinkObjId="g_4a64b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="691,43 726,43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a63cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,13 726,43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_4a598f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4a64b60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4a64b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4a598f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,13 726,43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a655a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,43 726,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_4a598f0@0" ObjectIDND2="0@x" ObjectIDZND0="g_4a64b60@0" Pin0InfoVect0LinkObjId="g_4a64b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4a598f0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,43 726,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a65800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,103 726,139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_4a64b60@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a64b60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,103 726,139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4a66b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-101 726,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-101 726,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24bda70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="884,-131 884,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="884,-131 884,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24bea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="884,30 884,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4a673b0@0" ObjectIDZND0="g_24bfc10@0" Pin0InfoVect0LinkObjId="g_24bfc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a673b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="884,30 884,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24bec60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-46 884,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_24bdcd0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4a673b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4a673b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24bdcd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="923,-46 884,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24bf750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="884,-80 884,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_24bdcd0@0" ObjectIDZND1="g_4a673b0@0" Pin0InfoVect0LinkObjId="g_24bdcd0_0" Pin0InfoVect1LinkObjId="g_4a673b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="884,-80 884,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24bf9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="884,-46 884,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24bdcd0@0" ObjectIDND1="0@x" ObjectIDZND0="g_4a673b0@1" Pin0InfoVect0LinkObjId="g_4a673b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24bdcd0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="884,-46 884,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24c3a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,-74 1045,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1045,-74 1045,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24c3c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,-37 1045,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1045,-37 1045,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cdbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,11 1045,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_24c3ed0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_24cf140@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24cf140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c3ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1082,11 1045,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cde10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,-20 1045,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_24c3ed0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_24cf140@0" Pin0InfoVect0LinkObjId="g_24c3ed0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24cf140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1045,-20 1045,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ce070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1010,42 1045,42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24c3ed0@0" ObjectIDZND2="g_24cf140@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24c3ed0_0" Pin0InfoVect2LinkObjId="g_24cf140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1010,42 1045,42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ce2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,12 1045,42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24c3ed0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24cf140@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24cf140_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24c3ed0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1045,12 1045,42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cfb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,42 1045,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24c3ed0@0" ObjectIDND2="0@x" ObjectIDZND0="g_24cf140@0" Pin0InfoVect0LinkObjId="g_24cf140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24c3ed0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1045,42 1045,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cfde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,102 1045,138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_24cf140@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cf140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1045,102 1045,138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d31d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1395,-72 1395,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1395,-72 1395,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d3430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1395,-35 1395,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1395,-35 1395,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24dd370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1432,13 1395,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_24d3690@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_24de900@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24de900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24d3690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1432,13 1395,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24dd5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1395,-18 1395,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_24d3690@0" ObjectIDZND1="0@x" ObjectIDZND2="g_24de900@0" Pin0InfoVect0LinkObjId="g_24d3690_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24de900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1395,-18 1395,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24dd830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,44 1395,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24d3690@0" ObjectIDZND2="g_24de900@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24d3690_0" Pin0InfoVect2LinkObjId="g_24de900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1360,44 1395,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24dda90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1395,14 1395,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24d3690@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24de900@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24de900_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24d3690_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1395,14 1395,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24df340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1395,44 1395,75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24d3690@0" ObjectIDND2="0@x" ObjectIDZND0="g_24de900@0" Pin0InfoVect0LinkObjId="g_24de900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24d3690_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1395,44 1395,75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24df5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1395,104 1395,140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_24de900@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24de900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1395,104 1395,140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24e2990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-74 1726,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-74 1726,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24e2bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-37 1726,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-37 1726,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ecb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1763,11 1726,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_24e2e50@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_24ee0c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24ee0c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24e2e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1763,11 1726,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ecd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-20 1726,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_24e2e50@0" ObjectIDZND1="0@x" ObjectIDZND2="g_24ee0c0@0" Pin0InfoVect0LinkObjId="g_24e2e50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24ee0c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-20 1726,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ecff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1691,42 1726,42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24e2e50@0" ObjectIDZND2="g_24ee0c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24e2e50_0" Pin0InfoVect2LinkObjId="g_24ee0c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1691,42 1726,42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ed250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,12 1726,42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24e2e50@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24ee0c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24ee0c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24e2e50_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1726,12 1726,42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24eeb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,42 1726,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24e2e50@0" ObjectIDND2="0@x" ObjectIDZND0="g_24ee0c0@0" Pin0InfoVect0LinkObjId="g_24ee0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24e2e50_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,42 1726,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24eed60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,102 1726,138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_24ee0c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24ee0c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,102 1726,138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24f2150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,-72 1893,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1893,-72 1893,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24f23b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,-35 1893,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1893,-35 1893,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fc2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1930,13 1893,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_24f2610@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_24fd880@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24fd880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24f2610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1930,13 1893,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fc550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,-18 1893,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_24f2610@0" ObjectIDZND1="0@x" ObjectIDZND2="g_24fd880@0" Pin0InfoVect0LinkObjId="g_24f2610_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24fd880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1893,-18 1893,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fc7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1858,44 1893,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24f2610@0" ObjectIDZND2="g_24fd880@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24f2610_0" Pin0InfoVect2LinkObjId="g_24fd880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1858,44 1893,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,14 1893,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24f2610@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24fd880@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24fd880_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24f2610_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1893,14 1893,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fe2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,44 1893,75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24f2610@0" ObjectIDND2="0@x" ObjectIDZND0="g_24fd880@0" Pin0InfoVect0LinkObjId="g_24fd880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24f2610_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1893,44 1893,75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24fe520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,104 1893,140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_24fd880@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24fd880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1893,104 1893,140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25038e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-132 1537,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-132 1537,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2504870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,30 1537,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_24ffe70@0" ObjectIDZND0="g_25051f0@0" Pin0InfoVect0LinkObjId="g_25051f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24ffe70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,30 1537,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2504ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1576,-46 1537,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2503b40@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24ffe70@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24ffe70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2503b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1576,-46 1537,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2504d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-80 1537,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2503b40@0" ObjectIDZND1="g_24ffe70@0" Pin0InfoVect0LinkObjId="g_2503b40_0" Pin0InfoVect1LinkObjId="g_24ffe70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-80 1537,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2504f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-46 1537,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2503b40@0" ObjectIDZND0="g_24ffe70@1" Pin0InfoVect0LinkObjId="g_24ffe70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2503b40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-46 1537,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2507de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,-102 1045,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1045,-102 1045,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2508610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1395,-100 1395,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1395,-100 1395,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2508e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-102 1726,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-102 1726,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2509670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1893,-100 1893,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1893,-100 1893,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251ea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-939 642,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="44395@0" ObjectIDZND0="44394@1" Pin0InfoVect0LinkObjId="SW-279487_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279488_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-939 642,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251ec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,-1001 642,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44398@0" ObjectIDZND0="44395@x" ObjectIDZND1="g_4a49b80@0" Pin0InfoVect0LinkObjId="SW-279488_0" Pin0InfoVect1LinkObjId="g_4a49b80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="602,-1001 642,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-1001 642,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="44398@x" ObjectIDND1="g_4a49b80@0" ObjectIDZND0="44395@1" Pin0InfoVect0LinkObjId="SW-279488_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279491_0" Pin1InfoVect1LinkObjId="g_4a49b80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-1001 642,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251f7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="679,-1040 642,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4a49b80@0" ObjectIDZND0="44398@x" ObjectIDZND1="44395@x" Pin0InfoVect0LinkObjId="SW-279491_0" Pin0InfoVect1LinkObjId="SW-279488_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a49b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="679,-1040 642,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2520260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-1107 642,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_4a49b80@0" ObjectIDZND1="44398@x" ObjectIDZND2="44395@x" Pin0InfoVect0LinkObjId="g_4a49b80_0" Pin0InfoVect1LinkObjId="SW-279491_0" Pin0InfoVect2LinkObjId="SW-279488_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="642,-1107 642,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25204a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-1040 642,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4a49b80@0" ObjectIDZND0="44398@x" ObjectIDZND1="44395@x" Pin0InfoVect0LinkObjId="SW-279491_0" Pin0InfoVect1LinkObjId="SW-279488_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4a49b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="642,-1040 642,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d6c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-908 1795,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="44413@1" ObjectIDZND0="44414@0" Pin0InfoVect0LinkObjId="SW-279587_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279586_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-908 1795,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d6f700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1773,-1010 1795,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="44417@0" ObjectIDZND0="44414@x" ObjectIDZND1="g_4386360@0" ObjectIDZND2="44583@1" Pin0InfoVect0LinkObjId="SW-279587_0" Pin0InfoVect1LinkObjId="g_4386360_0" Pin0InfoVect2LinkObjId="g_438d670_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1773,-1010 1795,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d711e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-981 1795,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="44414@1" ObjectIDZND0="44417@x" ObjectIDZND1="g_4386360@0" ObjectIDZND2="44583@1" Pin0InfoVect0LinkObjId="SW-279590_0" Pin0InfoVect1LinkObjId="g_4386360_0" Pin0InfoVect2LinkObjId="g_438d670_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-279587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-981 1795,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d724e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-1010 1795,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="44417@x" ObjectIDND1="44414@x" ObjectIDZND0="g_4386360@0" ObjectIDZND1="44583@1" Pin0InfoVect0LinkObjId="g_4386360_0" Pin0InfoVect1LinkObjId="g_438d670_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-279590_0" Pin1InfoVect1LinkObjId="SW-279587_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-1010 1795,-1037 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="44431" cx="572" cy="-620" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44431" cx="757" cy="-620" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44431" cx="928" cy="-620" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44432" cx="1680" cy="-622" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44432" cx="1873" cy="-622" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44432" cx="1302" cy="-622" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44432" cx="1478" cy="-622" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44431" cx="1005" cy="-620" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44432" cx="1248" cy="-622" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44432" cx="1795" cy="-622" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44431" cx="642" cy="-620" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="757" cy="-131" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="548" cy="-131" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="726" cy="-131" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="884" cy="-131" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1680" cy="-132" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1537" cy="-132" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1045" cy="-131" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1395" cy="-132" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1726" cy="-132" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1893" cy="-132" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-277444" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -923.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44191" ObjectName="DYN-CX_KQ"/>
     <cge:Meas_Ref ObjectId="277444"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_8847ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1f59600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">库区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f5a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -280.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f5a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -280.000000) translate(0,33)">SZ20-12500/35/NX2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f5a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -280.000000) translate(0,51)">(35000±3x2.5%)/6300V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f5a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -280.000000) translate(0,69)">Ud1 =8.21%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f5a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -280.000000) translate(0,87)">12.5MVA/12.5MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f5a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -280.000000) translate(0,105)">ONAN,Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_49c3480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 880.500000 -280.000000) translate(0,15)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_49c3480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 880.500000 -280.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4500670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -600.000000) translate(0,15)">35kVIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_434ae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 506.000000 195.000000) translate(0,12)">厂区Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4017b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -116.000000) translate(0,12)">6kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40189f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1947.000000 -123.000000) translate(0,12)">6kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4018c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.357143 -344.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e43b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 117.000000 -625.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250d170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 -364.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250d770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1708.000000 -365.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250d980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 651.000000 -800.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250dbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -691.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250de00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 581.000000 -770.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250e040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -867.000000) translate(0,12)">37130</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250e280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 649.000000 -900.000000) translate(0,12)">3713</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250e4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 -1027.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250e700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -974.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250e940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 581.000000 -475.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250eb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.000000 -574.000000) translate(0,12)">3633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250edc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1117.000000 -786.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250f000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -687.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250f240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -690.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250f480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 946.000000 -755.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250f6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -751.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250f900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1804.000000 -797.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250fb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -692.000000) translate(0,12)">3722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250fd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1734.000000 -767.000000) translate(0,12)">37227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250ffc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -864.000000) translate(0,12)">37230</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2510200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1802.000000 -897.000000) translate(0,12)">3723</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2510440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1741.000000 -1036.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2510680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1746.000000 -965.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25108c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1979.000000 -609.000000) translate(0,12)">35kVIIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2510e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -505.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25110d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.000000 -598.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2511310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -558.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2511550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -506.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2511790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1629.000000 -597.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25119d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -559.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2511c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 935.000000 -587.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -553.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25123c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1309.000000 -582.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1236.000000 -548.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1487.000000 -492.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 -586.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -556.000000) translate(0,12)">36427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 -487.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2514890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.500000 -267.000000) translate(0,15)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2514890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.500000 -267.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2514b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1876.357143 -290.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2514d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -202.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2514f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -204.000000) translate(0,12)">602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1913.000000 -70.000000) translate(0,12)">608</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25154d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1739.000000 -62.000000) translate(0,12)">607</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1411.000000 -68.000000) translate(0,12)">606</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.000000 -66.000000) translate(0,12)">605</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -67.000000) translate(0,12)">604</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -71.000000) translate(0,12)">603</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 188.000000) translate(0,12)">厂区Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1016.000000 176.000000) translate(0,12)">备用Ⅰ线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 179.000000) translate(0,12)">备用Ⅱ线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1696.000000 180.000000) translate(0,12)">厂区Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2517110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1868.000000 184.000000) translate(0,12)">厂区Ⅳ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25176f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.500000 121.000000) translate(0,15)">6kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25176f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.500000 121.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25178c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.500000 122.000000) translate(0,15)">6kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25178c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.500000 122.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.500000 -1058.000000) translate(0,15)">库</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.500000 -1058.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.500000 -1058.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.500000 -1058.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.500000 -1058.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -290.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -290.000000) translate(0,33)">SZ20-12500/35/NX2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -290.000000) translate(0,51)">(35000±3x2.5%)/6300V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -290.000000) translate(0,69)">Ud1 =8.21%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -290.000000) translate(0,87)">12.5MVA/12.5MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2517d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -290.000000) translate(0,105)">ONAN,Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2518080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1409.000000 -339.000000) translate(0,12)">35kV备用Ⅰ线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1831.000000 9.000000) translate(0,12)">60867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1670.000000 10.000000) translate(0,12)">60767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1334.000000 9.000000) translate(0,12)">60667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251dcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 995.000000 7.000000) translate(0,12)">60567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 661.000000 9.000000) translate(0,12)">60467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 482.000000 9.000000) translate(0,12)">60367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 509.000000 -536.000000) translate(0,12)">36337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1880.000000 -586.000000) translate(0,12)">3655</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1799.000000 -548.000000) translate(0,12)">36557</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2520700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.500000 -1149.000000) translate(0,15)">至110kV哨房变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25217c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1764.000000 -1166.000000) translate(0,15)">库</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25217c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1764.000000 -1166.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25217c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1764.000000 -1166.000000) translate(0,51)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25217c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1764.000000 -1166.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25217c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1764.000000 -1166.000000) translate(0,87)">线</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 747.000000 -149.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 747.000000 -214.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 537.910714 -77.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 537.910714 -12.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279563">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -544.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44405" ObjectName="SW-CX_KQ.CX_KQ_3633SW"/>
     <cge:Meas_Ref ObjectId="279563"/>
    <cge:TPSR_Ref TObjectID="44405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280081">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 490.000000 -503.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44465" ObjectName="SW-CX_KQ.CX_KQ_36337SW"/>
     <cge:Meas_Ref ObjectId="280081"/>
    <cge:TPSR_Ref TObjectID="44465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279510">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.000000 -555.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44400" ObjectName="SW-CX_KQ.CX_KQ_3011SW"/>
     <cge:Meas_Ref ObjectId="279510"/>
    <cge:TPSR_Ref TObjectID="44400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279511">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 668.000000 -525.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44401" ObjectName="SW-CX_KQ.CX_KQ_30117SW"/>
     <cge:Meas_Ref ObjectId="279511"/>
    <cge:TPSR_Ref TObjectID="44401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279556">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44402" ObjectName="SW-CX_KQ.CX_KQ_3901SW"/>
     <cge:Meas_Ref ObjectId="279556"/>
    <cge:TPSR_Ref TObjectID="44402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -520.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44403" ObjectName="SW-CX_KQ.CX_KQ_39017SW"/>
     <cge:Meas_Ref ObjectId="279557"/>
    <cge:TPSR_Ref TObjectID="44403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1293.000000 -552.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44421" ObjectName="SW-CX_KQ.CX_KQ_3902SW"/>
     <cge:Meas_Ref ObjectId="279655"/>
    <cge:TPSR_Ref TObjectID="44421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 -515.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44422" ObjectName="SW-CX_KQ.CX_KQ_39027SW"/>
     <cge:Meas_Ref ObjectId="279656"/>
    <cge:TPSR_Ref TObjectID="44422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1670.000000 -150.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1670.000000 -215.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1671.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44419" ObjectName="SW-CX_KQ.CX_KQ_3021SW"/>
     <cge:Meas_Ref ObjectId="279609"/>
    <cge:TPSR_Ref TObjectID="44419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1591.000000 -526.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44420" ObjectName="SW-CX_KQ.CX_KQ_30217SW"/>
     <cge:Meas_Ref ObjectId="279610"/>
    <cge:TPSR_Ref TObjectID="44420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44424" ObjectName="SW-CX_KQ.CX_KQ_3655SW"/>
     <cge:Meas_Ref ObjectId="279662"/>
    <cge:TPSR_Ref TObjectID="44424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280082">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1791.000000 -515.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44466" ObjectName="SW-CX_KQ.CX_KQ_36557SW"/>
     <cge:Meas_Ref ObjectId="280082"/>
    <cge:TPSR_Ref TObjectID="44466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279672">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44426" ObjectName="SW-CX_KQ.CX_KQ_3642SW"/>
     <cge:Meas_Ref ObjectId="279672"/>
    <cge:TPSR_Ref TObjectID="44426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279673">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.000000 -523.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44427" ObjectName="SW-CX_KQ.CX_KQ_36427SW"/>
     <cge:Meas_Ref ObjectId="279673"/>
    <cge:TPSR_Ref TObjectID="44427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 -722.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44409" ObjectName="SW-CX_KQ.CX_KQ_31217SW"/>
     <cge:Meas_Ref ObjectId="279574"/>
    <cge:TPSR_Ref TObjectID="44409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279575">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1164.000000 -718.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44410" ObjectName="SW-CX_KQ.CX_KQ_31227SW"/>
     <cge:Meas_Ref ObjectId="279575"/>
    <cge:TPSR_Ref TObjectID="44410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279572">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44407" ObjectName="SW-CX_KQ.CX_KQ_3121SW"/>
     <cge:Meas_Ref ObjectId="279572"/>
    <cge:TPSR_Ref TObjectID="44407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279573">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.000000 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44408" ObjectName="SW-CX_KQ.CX_KQ_3122SW"/>
     <cge:Meas_Ref ObjectId="279573"/>
    <cge:TPSR_Ref TObjectID="44408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279585">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1786.000000 -656.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44412" ObjectName="SW-CX_KQ.CX_KQ_3721SW"/>
     <cge:Meas_Ref ObjectId="279585"/>
    <cge:TPSR_Ref TObjectID="44412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279586">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1786.000000 -867.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44413" ObjectName="SW-CX_KQ.CX_KQ_3723SW"/>
     <cge:Meas_Ref ObjectId="279586"/>
    <cge:TPSR_Ref TObjectID="44413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279587">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1786.000000 -940.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44414" ObjectName="SW-CX_KQ.CX_KQ_3726SW"/>
     <cge:Meas_Ref ObjectId="279587"/>
    <cge:TPSR_Ref TObjectID="44414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1714.000000 -734.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44415" ObjectName="SW-CX_KQ.CX_KQ_37217SW"/>
     <cge:Meas_Ref ObjectId="279588"/>
    <cge:TPSR_Ref TObjectID="44415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 -831.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44416" ObjectName="SW-CX_KQ.CX_KQ_37230SW"/>
     <cge:Meas_Ref ObjectId="279589"/>
    <cge:TPSR_Ref TObjectID="44416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.000000 -1003.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44417" ObjectName="SW-CX_KQ.CX_KQ_37267SW"/>
     <cge:Meas_Ref ObjectId="279590"/>
    <cge:TPSR_Ref TObjectID="44417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279486">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 -659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44393" ObjectName="SW-CX_KQ.CX_KQ_3711SW"/>
     <cge:Meas_Ref ObjectId="279486"/>
    <cge:TPSR_Ref TObjectID="44393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279487">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 -870.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44394" ObjectName="SW-CX_KQ.CX_KQ_3713SW"/>
     <cge:Meas_Ref ObjectId="279487"/>
    <cge:TPSR_Ref TObjectID="44394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279488">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 -934.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44395" ObjectName="SW-CX_KQ.CX_KQ_3716SW"/>
     <cge:Meas_Ref ObjectId="279488"/>
    <cge:TPSR_Ref TObjectID="44395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279489">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 -737.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44396" ObjectName="SW-CX_KQ.CX_KQ_37117SW"/>
     <cge:Meas_Ref ObjectId="279489"/>
    <cge:TPSR_Ref TObjectID="44396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279490">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -834.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44397" ObjectName="SW-CX_KQ.CX_KQ_37130SW"/>
     <cge:Meas_Ref ObjectId="279490"/>
    <cge:TPSR_Ref TObjectID="44397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-279491">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -994.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44398" ObjectName="SW-CX_KQ.CX_KQ_37167SW"/>
     <cge:Meas_Ref ObjectId="279491"/>
    <cge:TPSR_Ref TObjectID="44398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 50.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.910714 -77.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.910714 -12.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 640.000000 50.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 893.754735 -104.500000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.910714 -78.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.910714 -13.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 959.000000 49.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1384.910714 -76.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1384.910714 -11.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 51.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1715.910714 -78.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1715.910714 -13.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1640.000000 49.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1882.910714 -76.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1882.910714 -11.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1807.000000 51.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1546.754735 -104.500000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 542.910714 160.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.910714 160.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1039.910714 159.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.910714 161.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1720.910714 159.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1887.910714 161.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4019ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 -232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_401aad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 452.000000 -262.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_401b340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 -247.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4392950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 840.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4392bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 718.000000 810.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4392e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 825.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4393160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 433.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4394530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 448.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4394fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 177.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4395540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 390.000000 162.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4395aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 223.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4395d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 208.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4395f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 193.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4396570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1960.000000 179.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4396820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1944.000000 164.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4396a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 225.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4396ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 210.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4396ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 195.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4397210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c4a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.000000 -262.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c4c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -247.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c4f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 945.000000 -232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c51a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 959.000000 -262.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c53e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 -247.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c5710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 222.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c5970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 192.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c5bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 207.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c5ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.000000 245.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c6140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1758.000000 215.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c6380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1734.000000 230.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c66b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.000000 -232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c6910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 -262.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1291.000000 -247.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c6e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.000000 501.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c70e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.000000 471.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c7320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.000000 486.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c7650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1705.000000 598.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c78b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1719.000000 568.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c7af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1695.000000 583.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c7e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1710.000000 415.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c8080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1710.000000 430.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c83b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.000000 661.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c8620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 646.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c8860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 707.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c8aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 692.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49c8ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 677.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2509f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 -232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250a5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1626.000000 -262.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250a820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1602.000000 -247.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250ab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1833.000000 -232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250adb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1847.000000 -262.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250aff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1823.000000 -247.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250b320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1849.000000 859.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250b580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.000000 829.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250b7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1839.000000 844.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250baf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 853.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250bd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.000000 823.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250bf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1058.000000 838.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250c2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 465.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 446.000000 435.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250c760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 450.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250ca90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1943.000000 498.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250ccf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 468.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250cf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1933.000000 483.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 676.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25137c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1963.000000 661.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1973.000000 722.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1973.000000 707.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1973.000000 692.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25141b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 560.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2514410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1510.000000 530.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2514650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1486.000000 545.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_253c830">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 66.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_434f1c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -408.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4016c90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_72d1720">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 646.557507 -410.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_8842a50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 -468.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_88449d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -461.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43d85e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 960.000000 -414.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43da300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1293.000000 -456.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43e2470">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1334.000000 -409.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4c6ca60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 -409.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4c6d480">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 -249.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4c74410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1700.000000 -469.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4547df0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1947.557507 -422.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43855b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1839.557507 -717.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4386360">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1838.557507 -1041.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a48dd0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 686.557507 -720.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a49b80">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 685.557507 -1044.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a564c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 557.000000 108.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a598f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 66.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a64b60">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 734.000000 108.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4a673b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 892.557507 -7.500000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24bdcd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.000000 8.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24bfc10">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 903.000000 71.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c3ed0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.000000 65.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cf140">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1053.000000 107.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d3690">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 67.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24de900">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1403.000000 109.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24e2e50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 65.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ee0c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1734.000000 107.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f2610">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1923.000000 67.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fd880">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1901.000000 109.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ffe70">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1545.557507 -7.500000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2503b40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1569.000000 8.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25051f0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1556.000000 72.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25182d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1859.000000 -339.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2518780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 -327.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_KQ.CX_KQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="443,-620 1060,-620 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="44431" ObjectName="BS-CX_KQ.CX_KQ_3IM"/>
    <cge:TPSR_Ref TObjectID="44431"/></metadata>
   <polyline fill="none" opacity="0" points="443,-620 1060,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="475,-131 1168,-131 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="475,-131 1168,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-132 1951,-132 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1318,-132 1951,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_KQ.CX_KQ_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,-622 1994,-622 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="44432" ObjectName="BS-CX_KQ.CX_KQ_3IIM"/>
    <cge:TPSR_Ref TObjectID="44432"/></metadata>
   <polyline fill="none" opacity="0" points="1212,-622 1994,-622 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-279726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -705.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44431"/>
     <cge:Term_Ref ObjectID="21792"/>
    <cge:TPSR_Ref TObjectID="44431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-279727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -705.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44431"/>
     <cge:Term_Ref ObjectID="21792"/>
    <cge:TPSR_Ref TObjectID="44431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-279728" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -705.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44431"/>
     <cge:Term_Ref ObjectID="21792"/>
    <cge:TPSR_Ref TObjectID="44431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-279732" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -705.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44431"/>
     <cge:Term_Ref ObjectID="21792"/>
    <cge:TPSR_Ref TObjectID="44431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-279729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -705.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44431"/>
     <cge:Term_Ref ObjectID="21792"/>
    <cge:TPSR_Ref TObjectID="44431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-279778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -720.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44432"/>
     <cge:Term_Ref ObjectID="21793"/>
    <cge:TPSR_Ref TObjectID="44432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-279779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -720.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44432"/>
     <cge:Term_Ref ObjectID="21793"/>
    <cge:TPSR_Ref TObjectID="44432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-279780" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -720.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44432"/>
     <cge:Term_Ref ObjectID="21793"/>
    <cge:TPSR_Ref TObjectID="44432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-279784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -720.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44432"/>
     <cge:Term_Ref ObjectID="21793"/>
    <cge:TPSR_Ref TObjectID="44432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-279781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -720.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44432"/>
     <cge:Term_Ref ObjectID="21793"/>
    <cge:TPSR_Ref TObjectID="44432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -560.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44425"/>
     <cge:Term_Ref ObjectID="21778"/>
    <cge:TPSR_Ref TObjectID="44425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279799" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -560.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44425"/>
     <cge:Term_Ref ObjectID="21778"/>
    <cge:TPSR_Ref TObjectID="44425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -560.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44425"/>
     <cge:Term_Ref ObjectID="21778"/>
    <cge:TPSR_Ref TObjectID="44425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -597.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44418"/>
     <cge:Term_Ref ObjectID="21764"/>
    <cge:TPSR_Ref TObjectID="44418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -597.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44418"/>
     <cge:Term_Ref ObjectID="21764"/>
    <cge:TPSR_Ref TObjectID="44418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -597.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44418"/>
     <cge:Term_Ref ObjectID="21764"/>
    <cge:TPSR_Ref TObjectID="44418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1998.000000 -501.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44423"/>
     <cge:Term_Ref ObjectID="21774"/>
    <cge:TPSR_Ref TObjectID="44423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1998.000000 -501.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44423"/>
     <cge:Term_Ref ObjectID="21774"/>
    <cge:TPSR_Ref TObjectID="44423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1998.000000 -501.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44423"/>
     <cge:Term_Ref ObjectID="21774"/>
    <cge:TPSR_Ref TObjectID="44423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -501.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44399"/>
     <cge:Term_Ref ObjectID="21726"/>
    <cge:TPSR_Ref TObjectID="44399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -501.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44399"/>
     <cge:Term_Ref ObjectID="21726"/>
    <cge:TPSR_Ref TObjectID="44399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -501.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44399"/>
     <cge:Term_Ref ObjectID="21726"/>
    <cge:TPSR_Ref TObjectID="44399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -461.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44404"/>
     <cge:Term_Ref ObjectID="21736"/>
    <cge:TPSR_Ref TObjectID="44404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -461.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44404"/>
     <cge:Term_Ref ObjectID="21736"/>
    <cge:TPSR_Ref TObjectID="44404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -461.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44404"/>
     <cge:Term_Ref ObjectID="21736"/>
    <cge:TPSR_Ref TObjectID="44404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -838.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44392"/>
     <cge:Term_Ref ObjectID="21712"/>
    <cge:TPSR_Ref TObjectID="44392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -838.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44392"/>
     <cge:Term_Ref ObjectID="21712"/>
    <cge:TPSR_Ref TObjectID="44392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279703" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -838.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44392"/>
     <cge:Term_Ref ObjectID="21712"/>
    <cge:TPSR_Ref TObjectID="44392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -852.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44406"/>
     <cge:Term_Ref ObjectID="21740"/>
    <cge:TPSR_Ref TObjectID="44406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -852.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44406"/>
     <cge:Term_Ref ObjectID="21740"/>
    <cge:TPSR_Ref TObjectID="44406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -852.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44406"/>
     <cge:Term_Ref ObjectID="21740"/>
    <cge:TPSR_Ref TObjectID="44406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-279752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -858.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44411"/>
     <cge:Term_Ref ObjectID="21750"/>
    <cge:TPSR_Ref TObjectID="44411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-279753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -858.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44411"/>
     <cge:Term_Ref ObjectID="21750"/>
    <cge:TPSR_Ref TObjectID="44411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-279755" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -858.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279755" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44411"/>
     <cge:Term_Ref ObjectID="21750"/>
    <cge:TPSR_Ref TObjectID="44411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-279774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1792.000000 -428.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44429"/>
     <cge:Term_Ref ObjectID="21788"/>
    <cge:TPSR_Ref TObjectID="44429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-279775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1792.000000 -428.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44429"/>
     <cge:Term_Ref ObjectID="21788"/>
    <cge:TPSR_Ref TObjectID="44429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-279722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -448.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44428"/>
     <cge:Term_Ref ObjectID="21784"/>
    <cge:TPSR_Ref TObjectID="44428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-279723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -448.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="279723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44428"/>
     <cge:Term_Ref ObjectID="21784"/>
    <cge:TPSR_Ref TObjectID="44428"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_KQ"/>
</svg>