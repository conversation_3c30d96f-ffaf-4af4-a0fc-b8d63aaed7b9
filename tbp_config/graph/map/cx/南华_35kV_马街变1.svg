<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-199" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="1353 -1598 2423 1371">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="7" stroke-width="0.153636"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="20" x2="20" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="14" y1="16" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.15789"/>
    <polyline points="58,100 64,100 " stroke-width="1.15789"/>
    <polyline points="64,100 64,93 " stroke-width="1.15789"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2672160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2672fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26739b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2674170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26752a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2675ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2676600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2676ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26786e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26786e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2679b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2679b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_267bf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267db90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_267e780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_267f210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_267fb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2681210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2681c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2682390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2682b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26835f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2683f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2684a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2685420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2686a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_26873a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2688390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2697850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_268a650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_268ad00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_268bf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1381" width="2433" x="1348" y="-1603"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2849" x2="2849" y1="-490" y2="-490"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2849" x2="2849" y1="-488" y2="-488"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2837" x2="2837" y1="-507" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2858" x2="2858" y1="-378" y2="-378"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2847" x2="2847" y1="-488" y2="-488"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2848" x2="2848" y1="-490" y2="-490"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-131728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2131.338841 -581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24159" ObjectName="SW-NH_MJ.NH_MJ_071BK"/>
     <cge:Meas_Ref ObjectId="131728"/>
    <cge:TPSR_Ref TObjectID="24159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131739">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2540.500771 -1017.558877)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24162" ObjectName="SW-NH_MJ.NH_MJ_301BK"/>
     <cge:Meas_Ref ObjectId="131739"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131745">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2540.500771 -804.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24164" ObjectName="SW-NH_MJ.NH_MJ_001BK"/>
     <cge:Meas_Ref ObjectId="131745"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2932.582268 -1014.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24166" ObjectName="SW-NH_MJ.NH_MJ_302BK"/>
     <cge:Meas_Ref ObjectId="131761"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2931.582268 -805.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24168" ObjectName="SW-NH_MJ.NH_MJ_002BK"/>
     <cge:Meas_Ref ObjectId="131767"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2389.215639 -543.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24141" ObjectName="SW-NH_MJ.NH_MJ_072BK"/>
     <cge:Meas_Ref ObjectId="131662"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131706">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3451.456461 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24153" ObjectName="SW-NH_MJ.NH_MJ_076BK"/>
     <cge:Meas_Ref ObjectId="131706"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193510">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3275.000000 -1226.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29423" ObjectName="SW-NH_MJ.NH_MJ_372BK"/>
     <cge:Meas_Ref ObjectId="193510"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131648">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2720.000000 -1234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24137" ObjectName="SW-NH_MJ.NH_MJ_371BK"/>
     <cge:Meas_Ref ObjectId="131648"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131684">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2840.320934 -541.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24147" ObjectName="SW-NH_MJ.NH_MJ_074BK"/>
     <cge:Meas_Ref ObjectId="131684"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.914456 -531.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24144" ObjectName="SW-NH_MJ.NH_MJ_073BK"/>
     <cge:Meas_Ref ObjectId="131673"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3253.329751 -542.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24150" ObjectName="SW-NH_MJ.NH_MJ_075BK"/>
     <cge:Meas_Ref ObjectId="131695"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131717">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3060.329751 -547.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24156" ObjectName="SW-NH_MJ.NH_MJ_077BK"/>
     <cge:Meas_Ref ObjectId="131717"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ff87a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3018.000000 -340.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_MJ" endPointId="0" endStationName="NH_HTP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_mahong" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2729,-1497 2729,-1564 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34577" ObjectName="AC-35kV.LN_mahong"/>
    <cge:TPSR_Ref TObjectID="34577_SS-199"/></metadata>
   <polyline fill="none" opacity="0" points="2729,-1497 2729,-1564 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_MJ" endPointId="0" endStationName="CX_BJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_bama" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3284,-1540 3284,-1577 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34579" ObjectName="AC-35kV.LN_bama"/>
    <cge:TPSR_Ref TObjectID="34579_SS-199"/></metadata>
   <polyline fill="none" opacity="0" points="3284,-1540 3284,-1577 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-NH_MJ.NH_MJ_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34122"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2629.621739 -1345.558877)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2629.621739 -1345.558877)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24182" ObjectName="TF-NH_MJ.NH_MJ_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_MJ.NH_MJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34118"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 2908.500000 -893.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 2908.500000 -893.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24181" ObjectName="TF-NH_MJ.NH_MJ_2T"/>
    <cge:TPSR_Ref TObjectID="24181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_MJ.NH_MJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34110"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 2517.500000 -900.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 2517.500000 -900.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24174" ObjectName="TF-NH_MJ.NH_MJ_1T"/>
    <cge:TPSR_Ref TObjectID="24174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2265.000000 -572.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2265.000000 -572.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c77fc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2837.274456 -1407.900000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c08170">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2438.548972 -343.897163)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd5fd0">
    <use class="BV-10KV" transform="matrix(1.440000 -0.000000 -0.000000 -1.500000 3613.294322 -437.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdc080">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3689.562904 -479.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c8b4e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.229570 -488.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fcbdb0">
    <use class="BV-35KV" transform="matrix(0.857143 -0.000000 -0.000000 -1.000000 3165.000000 -936.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c6e610">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3287.333333 -951.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c6f380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3167.000000 -972.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fe58d0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 3348.000000 -1459.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c8fb70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2388.000000 -440.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c90550">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3450.000000 -480.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc76a0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2886.247789 -343.897163)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fba210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2839.000000 -428.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2052f30">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2683.247789 -334.062677)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2055450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.000000 -411.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205e6b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3252.000000 -480.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204a500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3059.000000 -412.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204c4f0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3107.247789 -335.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204e440">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3508.247789 -343.649789)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff99e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3300.247789 -340.649789)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ffb430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3235.976118 -1374.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c68d90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2770.976118 -1410.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21496f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2608.000000 -1351.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_214d850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.688889 3279.000000 -1470.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1448.500000 -1327.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1450.000000 -1328.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208961" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -1232.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208961" ObjectName="NH_MJ:NH_MJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208961" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 -1191.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208961" ObjectName="NH_MJ:NH_MJ_sumP"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="2942" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="3230" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="3284" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2941" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2398" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3460" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3633" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3262" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3069" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="2729" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2140" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2250" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2550" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2645" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2849" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="2550" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-131037" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.000000 -1295.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24024" ObjectName="DYN-NH_MJ"/>
     <cge:Meas_Ref ObjectId="131037"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20295f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2012.000000 -713.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_203e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_206a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2018d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2227.000000 -1140.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1fe3cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -1373.000000) translate(0,16)">马街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c84fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -928.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c8aff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3598.000000 -435.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c101c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3241.000000 -1114.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200ed10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3243.000000 -1256.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2010190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3307.000000 -1368.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20103d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3292.000000 -1320.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2010610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3301.000000 -1298.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2010850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3291.000000 -1189.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2010a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3310.000000 -1238.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c179b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2421.000000 -572.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c17eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2405.000000 -635.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c180f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2405.000000 -525.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c18470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2622.000000 -1446.558877) translate(0,12)">3806</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c18780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2734.000000 -1323.558877) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c189c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2743.000000 -1261.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c18c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2741.000000 -1195.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c95110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3467.000000 -458.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c95600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3467.000000 -626.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c95840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.000000 -566.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c95bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -633.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c95ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2147.000000 -671.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c96110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2155.000000 -611.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c96350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2491.000000 -1313.558877) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c965a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2951.000000 -1043.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c967d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2949.000000 -1119.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c96a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 -834.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c96c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2948.000000 -769.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c96e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2559.000000 -833.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c970d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2557.000000 -768.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c97310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2583.000000 -1045.558877) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c97550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2555.000000 -1117.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c97790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -991.000000) translate(0,15)">35kV2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c97790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -991.000000) translate(0,33)">SZ11-2000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c97790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2976.000000 -991.000000) translate(0,51)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1c97a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3228.000000 -1369.000000) translate(0,10)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1c8e4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 -1346.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1c8ea10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 -1385.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c8ed00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -1028.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1c8ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -442.000000) translate(0,17)">7361060</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c8f180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2598.000000 -910.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1c91630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -515.000000) translate(0,12)">10kV避雷器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c92380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2374.000000 -332.897163) translate(0,15)">唐家线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fce0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2985.000000 -914.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fce2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2985.000000 -931.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fd0090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -400.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fd0090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -400.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fd02d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -410.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fd02d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -410.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fd02d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -410.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1fd1ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.500000 -1372.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb9760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2856.000000 -634.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb9c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2870.000000 -570.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb9e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2856.000000 -515.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fbab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2819.000000 -330.897163) translate(0,15)">平掌孜线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2054a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2652.000000 -504.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2054e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2652.000000 -624.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20550d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2665.000000 -561.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2055e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2621.000000 -324.062677) translate(0,15)">后山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205ddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3281.000000 -570.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205e0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -448.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205e330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -635.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2049b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -576.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2049e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3076.000000 -516.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204a1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3076.000000 -644.000000) translate(0,12)">0771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_204d230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3000.000000 -335.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_204d720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3048.000000 -327.295354) translate(0,15)">兔街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_204dd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3238.000000 -322.000000) translate(0,15)">法空线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_204e140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3447.000000 -338.649789) translate(0,15)">机关线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202ef10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2769.000000 -1218.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20302b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3152.000000 -1125.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2031290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3272.000000 -1064.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2032660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2863.000000 -1067.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2035290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -446.897163) translate(0,15)"> 备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2460.000000 -1108.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_203b690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2211.000000 -456.897163) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_1ff5400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2416.000000 -343.897163) translate(0,8)">（安装于站外杆塔）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_1ff7610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2662.000000 -332.897163) translate(0,8)">（安装于站外杆塔）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_1ff7c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2876.000000 -340.897163) translate(0,8)">（安装于站外杆塔）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_1ff8430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3096.000000 -333.897163) translate(0,8)">（安装于站外杆塔）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_1ff92f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2972.000000 -318.897163) translate(0,8)">（安装于站外杆塔）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1c5f880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2837.000000 -966.000000) translate(0,16)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c60140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3320.000000 -1541.000000) translate(0,15)">35kV八马线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" graphid="g_1c610d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2416.000000 -966.000000) translate(0,19)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c61590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2596.000000 -994.000000) translate(0,15)">35kV1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c61590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2596.000000 -994.000000) translate(0,33)">SZ9-5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c61590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2596.000000 -994.000000) translate(0,51)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c61b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2683.000000 -1594.000000) translate(0,15)">35kV马红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1c69e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2762.000000 -1408.000000) translate(0,10)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6b000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2779.000000 -1279.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6b6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2643.000000 -1398.000000) translate(0,12)">38067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2146a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2598.000000 -930.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21490c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2762.000000 -1359.000000) translate(0,12)">37167</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="1846" y="-1383"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_MJ.NH_MJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2270,-1150 3454,-1150 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24135" ObjectName="BS-NH_MJ.NH_MJ_3IM"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   <polyline fill="none" opacity="0" points="2270,-1150 3454,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_MJ.NH_MJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2026,-717 3776,-717 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24136" ObjectName="BS-NH_MJ.NH_MJ_9IM"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   <polyline fill="none" opacity="0" points="2026,-717 3776,-717 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c78680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2819.700000 -1366.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201a360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3351.200000 -1336.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c4a1a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3338.200000 -1270.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200e280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3348.200000 -1209.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fff0f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3321.200000 -1064.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2002850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2807.200000 -1217.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2005fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3141.800000 -1131.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2006ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2854.800000 -1073.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c5c140" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2461.800000 -1075.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c663a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2808.200000 -1280.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c6a360" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2710.000000 -1417.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1460" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1412" y="-1403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1413" y="-1404"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3243" y="-1256"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3243" y="-1256"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2420" y="-574"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2420" y="-574"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3481" y="-568"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3481" y="-568"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2743" y="-1261"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2743" y="-1261"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1682" y="-1353"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1682" y="-1353"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1682" y="-1392"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1682" y="-1392"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="1845" y="-1384"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="1845" y="-1384"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2869" y="-572"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2869" y="-572"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2663" y="-563"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2663" y="-563"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3280" y="-571"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3280" y="-571"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3088" y="-578"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3088" y="-578"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2154" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2154" y="-613"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="66" x="2837" y="-966"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="66" x="2837" y="-966"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="1372" y="-1030"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="1372" y="-1030"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="80" x="2416" y="-966"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="80" x="2416" y="-966"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fea300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2631.000000 1058.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fea810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2620.000000 1043.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1feaa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2645.000000 1028.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c73c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2615.000000 855.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c73f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2604.000000 840.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c74150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2629.000000 825.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c74570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2869.000000 1273.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c74830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2858.000000 1258.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c74a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2883.000000 1243.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200fa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3387.000000 1268.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200fd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3376.000000 1253.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200ff50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3401.000000 1238.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c927a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2997.000000 1052.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c92a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2986.000000 1037.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c92ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3011.000000 1022.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fce710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3005.000000 851.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fce9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2994.000000 836.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3019.000000 821.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcf030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2367.000000 283.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcf2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2356.000000 268.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcf530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2381.000000 253.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcf950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3432.000000 286.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcfc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3421.000000 271.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcfe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3446.000000 256.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbb290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2827.000000 282.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbb550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2816.000000 267.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbb790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2841.000000 252.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbbbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2595.000000 276.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbbe70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2584.000000 261.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbc0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2609.000000 246.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3224.000000 284.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20416b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3213.000000 269.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20418f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3238.000000 254.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204b0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3027.000000 281.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204b5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3016.000000 266.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204b800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3041.000000 251.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_214e5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2863.000000 938.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2870" cy="931" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_214edd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 1539.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="3429" cy="1532" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_214f2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2908.000000 325.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2915" cy="318" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_214f790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2684.000000 316.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2691" cy="309" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_214fc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3115.000000 319.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="3122" cy="312" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2150150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3304.000000 320.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="3311" cy="313" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2150630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3516.000000 337.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="3523" cy="330" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2151170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2789.000000 1593.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2796" cy="1586" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2151920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2046.000000 807.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2151c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2046.000000 823.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2151e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2045.000000 777.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21520d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2038.000000 762.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2152310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2046.000000 792.500000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2152640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2223.000000 1220.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21528b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2223.000000 1235.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2152af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2223.000000 1251.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2152d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2223.000000 1205.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2152f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2215.000000 1190.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21532a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2437.000000 325.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2444" cy="318" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2388.976118 -337.897163)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34110" ObjectName="EC-NH_MJ.072Ld"/>
    <cge:TPSR_Ref TObjectID="34110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2840.401413 -335.897163)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34112" ObjectName="EC-NH_MJ.074Ld"/>
    <cge:TPSR_Ref TObjectID="34112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.674935 -328.062677)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34111" ObjectName="EC-NH_MJ.073Ld"/>
    <cge:TPSR_Ref TObjectID="34111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.077Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3060.340230 -333.295354)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34115" ObjectName="EC-NH_MJ.077Ld"/>
    <cge:TPSR_Ref TObjectID="34115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3451.216940 -343.649789)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34114" ObjectName="EC-NH_MJ.076Ld"/>
    <cge:TPSR_Ref TObjectID="34114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3253.340230 -333.295354)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34113" ObjectName="EC-NH_MJ.075Ld"/>
    <cge:TPSR_Ref TObjectID="34113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2130.976118 -462.897163)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42873" ObjectName="EC-NH_MJ.071Ld"/>
    <cge:TPSR_Ref TObjectID="42873"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2422.976118 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24141"/>
     <cge:Term_Ref ObjectID="34042"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2422.976118 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24141"/>
     <cge:Term_Ref ObjectID="34042"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2422.976118 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24141"/>
     <cge:Term_Ref ObjectID="34042"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.216940 -287.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24153"/>
     <cge:Term_Ref ObjectID="34066"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.216940 -287.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24153"/>
     <cge:Term_Ref ObjectID="34066"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.216940 -287.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24153"/>
     <cge:Term_Ref ObjectID="34066"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.712348 -850.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24164"/>
     <cge:Term_Ref ObjectID="34088"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.712348 -850.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24164"/>
     <cge:Term_Ref ObjectID="34088"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.712348 -850.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24164"/>
     <cge:Term_Ref ObjectID="34088"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131626" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3062.793845 -851.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24168"/>
     <cge:Term_Ref ObjectID="34096"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3062.793845 -851.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24168"/>
     <cge:Term_Ref ObjectID="34096"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3062.793845 -851.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24168"/>
     <cge:Term_Ref ObjectID="34096"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3050.793845 -1051.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24166"/>
     <cge:Term_Ref ObjectID="34092"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3050.793845 -1051.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24166"/>
     <cge:Term_Ref ObjectID="34092"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3050.793845 -1051.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24166"/>
     <cge:Term_Ref ObjectID="34092"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2691.712348 -1059.558877) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24162"/>
     <cge:Term_Ref ObjectID="34084"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2691.712348 -1059.558877) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24162"/>
     <cge:Term_Ref ObjectID="34084"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2691.712348 -1059.558877) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24162"/>
     <cge:Term_Ref ObjectID="34084"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2925.000000 -1274.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24137"/>
     <cge:Term_Ref ObjectID="34034"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2925.000000 -1274.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24137"/>
     <cge:Term_Ref ObjectID="34034"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2925.000000 -1274.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24137"/>
     <cge:Term_Ref ObjectID="34034"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-131633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -823.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-131634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -823.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-131635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -823.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-131638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -823.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-131636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -823.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-131629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1246.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-131630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1246.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-131631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1246.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-131637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1246.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-131632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1246.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-131639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3049.793845 -930.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24181"/>
     <cge:Term_Ref ObjectID="34119"/>
    <cge:TPSR_Ref TObjectID="24181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-131641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3049.793845 -930.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24181"/>
     <cge:Term_Ref ObjectID="34119"/>
    <cge:TPSR_Ref TObjectID="24181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-193532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3440.000000 -1268.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29423"/>
     <cge:Term_Ref ObjectID="41904"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-193533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3440.000000 -1268.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29423"/>
     <cge:Term_Ref ObjectID="41904"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-193518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3440.000000 -1268.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29423"/>
     <cge:Term_Ref ObjectID="41904"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-131640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2668.000000 -910.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24174"/>
     <cge:Term_Ref ObjectID="34111"/>
    <cge:TPSR_Ref TObjectID="24174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2882.641413 -282.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24147"/>
     <cge:Term_Ref ObjectID="34054"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2882.641413 -282.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24147"/>
     <cge:Term_Ref ObjectID="34054"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2882.641413 -282.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24147"/>
     <cge:Term_Ref ObjectID="34054"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2654.174935 -272.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24144"/>
     <cge:Term_Ref ObjectID="34048"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2654.174935 -272.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24144"/>
     <cge:Term_Ref ObjectID="34048"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2654.174935 -272.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24144"/>
     <cge:Term_Ref ObjectID="34048"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.840230 -283.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24150"/>
     <cge:Term_Ref ObjectID="34060"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.840230 -283.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24150"/>
     <cge:Term_Ref ObjectID="34060"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.840230 -283.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24150"/>
     <cge:Term_Ref ObjectID="34060"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3085.000000 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24156"/>
     <cge:Term_Ref ObjectID="34072"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3085.000000 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24156"/>
     <cge:Term_Ref ObjectID="34072"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3085.000000 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24156"/>
     <cge:Term_Ref ObjectID="34072"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-257868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2668.000000 -928.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="257868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24174"/>
     <cge:Term_Ref ObjectID="34108"/>
    <cge:TPSR_Ref TObjectID="24174"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-131730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2131.338841 -641.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24160" ObjectName="SW-NH_MJ.NH_MJ_0711SW"/>
     <cge:Meas_Ref ObjectId="131730"/>
    <cge:TPSR_Ref TObjectID="24160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2541.212348 -1087.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24163" ObjectName="SW-NH_MJ.NH_MJ_3011SW"/>
     <cge:Meas_Ref ObjectId="131741"/>
    <cge:TPSR_Ref TObjectID="24163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2540.712348 -738.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24165" ObjectName="SW-NH_MJ.NH_MJ_0011SW"/>
     <cge:Meas_Ref ObjectId="131747"/>
    <cge:TPSR_Ref TObjectID="24165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131763">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2932.793845 -1089.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24167" ObjectName="SW-NH_MJ.NH_MJ_3021SW"/>
     <cge:Meas_Ref ObjectId="131763"/>
    <cge:TPSR_Ref TObjectID="24167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2931.793845 -739.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24169" ObjectName="SW-NH_MJ.NH_MJ_0021SW"/>
     <cge:Meas_Ref ObjectId="131769"/>
    <cge:TPSR_Ref TObjectID="24169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2720.000000 -1167.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24138" ObjectName="SW-NH_MJ.NH_MJ_3711SW"/>
     <cge:Meas_Ref ObjectId="131650"/>
    <cge:TPSR_Ref TObjectID="24138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131651">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2720.000000 -1293.558877)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24139" ObjectName="SW-NH_MJ.NH_MJ_3716SW"/>
     <cge:Meas_Ref ObjectId="131651"/>
    <cge:TPSR_Ref TObjectID="24139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131786">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2606.000000 -1417.558877)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24171" ObjectName="SW-NH_MJ.NH_MJ_3806SW"/>
     <cge:Meas_Ref ObjectId="131786"/>
    <cge:TPSR_Ref TObjectID="24171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131664">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2388.976118 -605.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24142" ObjectName="SW-NH_MJ.NH_MJ_0721SW"/>
     <cge:Meas_Ref ObjectId="131664"/>
    <cge:TPSR_Ref TObjectID="24142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2388.976118 -495.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24143" ObjectName="SW-NH_MJ.NH_MJ_0726SW"/>
     <cge:Meas_Ref ObjectId="131665"/>
    <cge:TPSR_Ref TObjectID="24143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131708">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3451.216940 -596.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24154" ObjectName="SW-NH_MJ.NH_MJ_0761SW"/>
     <cge:Meas_Ref ObjectId="131708"/>
    <cge:TPSR_Ref TObjectID="24154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3451.216940 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24155" ObjectName="SW-NH_MJ.NH_MJ_0766SW"/>
     <cge:Meas_Ref ObjectId="131709"/>
    <cge:TPSR_Ref TObjectID="24155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.279570 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24172" ObjectName="SW-NH_MJ.NH_MJ_0901SW"/>
     <cge:Meas_Ref ObjectId="131787"/>
    <cge:TPSR_Ref TObjectID="24172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131783">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3221.000000 -1085.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24170" ObjectName="SW-NH_MJ.NH_MJ_3901SW"/>
     <cge:Meas_Ref ObjectId="131783"/>
    <cge:TPSR_Ref TObjectID="24170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3306.600000 -1316.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29426" ObjectName="SW-NH_MJ.NH_MJ_37267SW"/>
     <cge:Meas_Ref ObjectId="193516"/>
    <cge:TPSR_Ref TObjectID="29426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193512">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3275.000000 -1159.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29424" ObjectName="SW-NH_MJ.NH_MJ_3721SW"/>
     <cge:Meas_Ref ObjectId="193512"/>
    <cge:TPSR_Ref TObjectID="29424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193514">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3275.000000 -1284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29425" ObjectName="SW-NH_MJ.NH_MJ_3726SW"/>
     <cge:Meas_Ref ObjectId="193514"/>
    <cge:TPSR_Ref TObjectID="29425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3299.600000 -1250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29428" ObjectName="SW-NH_MJ.NH_MJ_37260SW"/>
     <cge:Meas_Ref ObjectId="193515"/>
    <cge:TPSR_Ref TObjectID="29428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193513">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3303.600000 -1189.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29427" ObjectName="SW-NH_MJ.NH_MJ_37217SW"/>
     <cge:Meas_Ref ObjectId="193513"/>
    <cge:TPSR_Ref TObjectID="29427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2840.081413 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24149" ObjectName="SW-NH_MJ.NH_MJ_0746SW"/>
     <cge:Meas_Ref ObjectId="131687"/>
    <cge:TPSR_Ref TObjectID="24149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2840.081413 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24148" ObjectName="SW-NH_MJ.NH_MJ_0741SW"/>
     <cge:Meas_Ref ObjectId="131686"/>
    <cge:TPSR_Ref TObjectID="24148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.674935 -594.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24145" ObjectName="SW-NH_MJ.NH_MJ_0731SW"/>
     <cge:Meas_Ref ObjectId="131675"/>
    <cge:TPSR_Ref TObjectID="24145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.674935 -474.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24146" ObjectName="SW-NH_MJ.NH_MJ_0736SW"/>
     <cge:Meas_Ref ObjectId="131676"/>
    <cge:TPSR_Ref TObjectID="24146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3253.280230 -605.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24151" ObjectName="SW-NH_MJ.NH_MJ_0751SW"/>
     <cge:Meas_Ref ObjectId="131697"/>
    <cge:TPSR_Ref TObjectID="24151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3253.280230 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24152" ObjectName="SW-NH_MJ.NH_MJ_0756SW"/>
     <cge:Meas_Ref ObjectId="131698"/>
    <cge:TPSR_Ref TObjectID="24152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131719">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3060.280230 -614.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24157" ObjectName="SW-NH_MJ.NH_MJ_0771SW"/>
     <cge:Meas_Ref ObjectId="131719"/>
    <cge:TPSR_Ref TObjectID="24157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3060.280230 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24158" ObjectName="SW-NH_MJ.NH_MJ_0776SW"/>
     <cge:Meas_Ref ObjectId="131720"/>
    <cge:TPSR_Ref TObjectID="24158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2245.000000 -679.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3276.600000 -1044.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42332" ObjectName="SW-NH_MJ.NH_MJ_39017SW"/>
     <cge:Meas_Ref ObjectId="257740"/>
    <cge:TPSR_Ref TObjectID="42332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257204">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2762.600000 -1197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42327" ObjectName="SW-NH_MJ.NH_MJ_37117SW"/>
     <cge:Meas_Ref ObjectId="257204"/>
    <cge:TPSR_Ref TObjectID="42327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3186.400000 -1111.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42333" ObjectName="SW-NH_MJ.NH_MJ_39010SW"/>
     <cge:Meas_Ref ObjectId="257741"/>
    <cge:TPSR_Ref TObjectID="42333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2899.400000 -1053.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42331" ObjectName="SW-NH_MJ.NH_MJ_30217SW"/>
     <cge:Meas_Ref ObjectId="257606"/>
    <cge:TPSR_Ref TObjectID="42331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257478">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2506.400000 -1055.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42330" ObjectName="SW-NH_MJ.NH_MJ_30117SW"/>
     <cge:Meas_Ref ObjectId="257478"/>
    <cge:TPSR_Ref TObjectID="42330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257205">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2763.600000 -1260.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42328" ObjectName="SW-NH_MJ.NH_MJ_37160SW"/>
     <cge:Meas_Ref ObjectId="257205"/>
    <cge:TPSR_Ref TObjectID="42328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257206">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2648.600000 -1385.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42329" ObjectName="SW-NH_MJ.NH_MJ_38067SW"/>
     <cge:Meas_Ref ObjectId="257206"/>
    <cge:TPSR_Ref TObjectID="42329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2762.558877 -1381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24140" ObjectName="SW-NH_MJ.NH_MJ_37167SW"/>
     <cge:Meas_Ref ObjectId="131661"/>
    <cge:TPSR_Ref TObjectID="24140"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/></g>
   <g href="cx_配调_配网接线图35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/></g>
   <g href="35kV马街变35kV八马线372间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3243" y="-1256"/></g>
   <g href="35kV马街变10kV唐家线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2420" y="-574"/></g>
   <g href="35kV马街变10kV机关线076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3481" y="-568"/></g>
   <g href="35kV马街变35kV大马红线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2743" y="-1261"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1682" y="-1353"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1682" y="-1392"/></g>
   <g href="AVC马街站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="1845" y="-1384"/></g>
   <g href="35kV马街变10kV平掌孜线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2869" y="-572"/></g>
   <g href="35kV马街变10kV后山线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2663" y="-563"/></g>
   <g href="35kV马街变10kV法空线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3280" y="-571"/></g>
   <g href="35kV马街变10kV兔街线077间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3088" y="-578"/></g>
   <g href="35kV马街变10kV备用071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2154" y="-613"/></g>
   <g href="35kV马街变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="66" x="2837" y="-966"/></g>
   <g href="35kV马街变NH_MJ_GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="1372" y="-1030"/></g>
   <g href="35kV马街变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="80" x="2416" y="-966"/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1c056c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2550,-1026 2550,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24162@0" ObjectIDZND0="24174@0" Pin0InfoVect0LinkObjId="g_1c63470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-1026 2550,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fe0790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2550,-812 2550,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24164@0" ObjectIDZND0="24165@1" Pin0InfoVect0LinkObjId="SW-131747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-812 2550,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bf7a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2550,-743 2550,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24165@0" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_1ff1780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-743 2550,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c8c860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2942,-1150 2942,-1130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24135@0" ObjectIDZND0="24167@1" Pin0InfoVect0LinkObjId="SW-131763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c578f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2942,-1150 2942,-1130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fde790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2942,-1022 2942,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24166@0" ObjectIDZND0="24181@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2942,-1022 2942,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fd58d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2941,-897 2941,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24181@1" ObjectIDZND0="24168@1" Pin0InfoVect0LinkObjId="SW-131767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fde790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2941,-897 2941,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff1520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2941,-813 2941,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24168@0" ObjectIDZND0="24169@1" Pin0InfoVect0LinkObjId="SW-131769_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2941,-813 2941,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff1780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2941,-744 2941,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24169@0" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_1bf7a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2941,-744 2941,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c578f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1172 2729,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24138@0" ObjectIDZND0="24135@0" Pin0InfoVect0LinkObjId="g_1fe5670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1172 2729,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c55080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2140,-717 2140,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="24160@1" Pin0InfoVect0LinkObjId="SW-131730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2140,-717 2140,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feb090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2140,-646 2140,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24160@0" ObjectIDZND0="24159@1" Pin0InfoVect0LinkObjId="SW-131728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2140,-646 2140,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c55d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-717 2398,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="24142@1" Pin0InfoVect0LinkObjId="SW-131664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-717 2398,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c59010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-610 2398,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24142@0" ObjectIDZND0="24141@1" Pin0InfoVect0LinkObjId="SW-131662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-610 2398,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c985d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3460,-717 3460,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="24154@1" Pin0InfoVect0LinkObjId="SW-131708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3460,-717 3460,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fcbb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-493 3633,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c8b4e0@1" ObjectIDZND0="g_1fd5fd0@0" Pin0InfoVect0LinkObjId="g_1fd5fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8b4e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-493 3633,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0ff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3177,-978 3177,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c6f380@1" ObjectIDZND0="g_1fcbdb0@0" Pin0InfoVect0LinkObjId="g_1fcbdb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c6f380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3177,-978 3177,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c812c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3460,-570 3460,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24153@1" ObjectIDZND0="24154@0" Pin0InfoVect0LinkObjId="SW-131708_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131706_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3460,-570 3460,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c814b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-644 3633,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24172@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_1bf7a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-644 3633,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c816a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-608 3633,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24172@0" ObjectIDZND0="g_1c8b4e0@0" ObjectIDZND1="g_1fdc080@0" Pin0InfoVect0LinkObjId="g_1c8b4e0_0" Pin0InfoVect1LinkObjId="g_1fdc080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-608 3633,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4d860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-525 3633,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1c8b4e0@0" ObjectIDZND0="24172@x" ObjectIDZND1="g_1fdc080@0" Pin0InfoVect0LinkObjId="SW-131787_0" Pin0InfoVect1LinkObjId="g_1fdc080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8b4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-525 3633,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4da90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-559 3683,-559 3683,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24172@x" ObjectIDND1="g_1c8b4e0@0" ObjectIDZND0="g_1fdc080@0" Pin0InfoVect0LinkObjId="g_1fdc080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131787_0" Pin1InfoVect1LinkObjId="g_1c8b4e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-559 3683,-559 3683,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201a100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3356,-1342 3334,-1342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_201a360@0" ObjectIDZND0="29426@1" Pin0InfoVect0LinkObjId="SW-193516_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_201a360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3356,-1342 3334,-1342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe5670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1164 3284,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29424@0" ObjectIDZND0="24135@0" Pin0InfoVect0LinkObjId="g_1c578f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1164 3284,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c49f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3343,-1276 3327,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c4a1a0@0" ObjectIDZND0="29428@1" Pin0InfoVect0LinkObjId="SW-193515_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c4a1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3343,-1276 3327,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_200e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3353,-1215 3331,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_200e280@0" ObjectIDZND0="29427@1" Pin0InfoVect0LinkObjId="SW-193513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200e280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3353,-1215 3331,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2010e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3291,-1276 3284,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29428@0" ObjectIDZND0="29425@x" ObjectIDZND1="29423@x" Pin0InfoVect0LinkObjId="SW-193514_0" Pin0InfoVect1LinkObjId="SW-193510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3291,-1276 3284,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2011000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1289 3284,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29425@0" ObjectIDZND0="29428@x" ObjectIDZND1="29423@x" Pin0InfoVect0LinkObjId="SW-193515_0" Pin0InfoVect1LinkObjId="SW-193510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1289 3284,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20111f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1276 3284,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29425@x" ObjectIDND1="29428@x" ObjectIDZND0="29423@1" Pin0InfoVect0LinkObjId="SW-193510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193514_0" Pin1InfoVect1LinkObjId="SW-193515_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1276 3284,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2011400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3295,-1215 3284,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29427@0" ObjectIDZND0="29423@x" ObjectIDZND1="29424@x" Pin0InfoVect0LinkObjId="SW-193510_0" Pin0InfoVect1LinkObjId="SW-193512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3295,-1215 3284,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2011630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1234 3284,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29423@0" ObjectIDZND0="29427@x" ObjectIDZND1="29424@x" Pin0InfoVect0LinkObjId="SW-193513_0" Pin0InfoVect1LinkObjId="SW-193512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1234 3284,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c14820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1215 3284,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29427@x" ObjectIDND1="29423@x" ObjectIDZND0="29424@1" Pin0InfoVect0LinkObjId="SW-193512_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193513_0" Pin1InfoVect1LinkObjId="SW-193510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1215 3284,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c14a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3298,-1342 3297,-1343 3284,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29426@0" ObjectIDZND0="29425@x" ObjectIDZND1="g_214d850@0" Pin0InfoVect0LinkObjId="SW-193514_0" Pin0InfoVect1LinkObjId="g_214d850_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3298,-1342 3297,-1343 3284,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c14c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1343 3284,-1325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="29426@x" ObjectIDND1="g_214d850@0" ObjectIDZND0="29425@1" Pin0InfoVect0LinkObjId="SW-193514_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193516_0" Pin1InfoVect1LinkObjId="g_214d850_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1343 3284,-1325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c8f980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2140,-589 2140,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="load" ObjectIDND0="24159@0" ObjectIDZND0="42873@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2140,-589 2140,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c91170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3460,-469 3460,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24155@1" ObjectIDZND0="g_1c90550@0" Pin0InfoVect0LinkObjId="g_1c90550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3460,-469 3460,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c913d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3460,-524 3460,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1c90550@1" ObjectIDZND0="24153@0" Pin0InfoVect0LinkObjId="SW-131706_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c90550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3460,-524 3460,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc72c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2849,-645 2849,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24148@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_1bf7a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2849,-645 2849,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc74b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2849,-576 2849,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24147@1" ObjectIDZND0="24148@0" Pin0InfoVect0LinkObjId="SW-131686_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131684_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2849,-576 2849,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fbea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2645,-717 2645,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="24145@1" Pin0InfoVect0LinkObjId="SW-131675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2645,-717 2645,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc0d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2645,-599 2645,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24145@0" ObjectIDZND0="24144@1" Pin0InfoVect0LinkObjId="SW-131673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2645,-599 2645,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205d9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3262,-646 3262,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24151@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_1bf7a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3262,-646 3262,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3262,-577 3262,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24150@1" ObjectIDZND0="24151@0" Pin0InfoVect0LinkObjId="SW-131697_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3262,-577 3262,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2040a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3262,-459 3262,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24152@1" ObjectIDZND0="g_205e6b0@0" Pin0InfoVect0LinkObjId="g_205e6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131698_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3262,-459 3262,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2040cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3262,-524 3262,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_205e6b0@1" ObjectIDZND0="24150@0" Pin0InfoVect0LinkObjId="SW-131695_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_205e6b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3262,-524 3262,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2049770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3069,-655 3069,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24157@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_1bf7a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3069,-655 3069,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2049960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3069,-582 3069,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24156@1" ObjectIDZND0="24157@0" Pin0InfoVect0LinkObjId="SW-131719_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3069,-582 3069,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204d960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3293,-395 3262,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1ff99e0@0" ObjectIDZND0="24152@x" ObjectIDZND1="34113@x" Pin0InfoVect0LinkObjId="SW-131698_0" Pin0InfoVect1LinkObjId="EC-NH_MJ.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff99e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3293,-395 3262,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204db50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3262,-395 3262,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24152@x" ObjectIDND1="g_1ff99e0@0" ObjectIDZND0="34113@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131698_0" Pin1InfoVect1LinkObjId="g_1ff99e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3262,-395 3262,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_202deb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2419,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2419,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_202f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-1137 3193,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24135@0" ObjectIDND1="24170@x" ObjectIDZND0="42333@0" Pin0InfoVect0LinkObjId="SW-257741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c578f0_0" Pin1InfoVect1LinkObjId="SW-131783_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3230,-1137 3193,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_202feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-1150 3230,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24135@0" ObjectIDZND0="24170@x" ObjectIDZND1="42333@x" Pin0InfoVect0LinkObjId="SW-131783_0" Pin0InfoVect1LinkObjId="SW-257741_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c578f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3230,-1150 3230,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20300a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-1137 3230,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24135@0" ObjectIDND1="42333@x" ObjectIDZND0="24170@1" Pin0InfoVect0LinkObjId="SW-131783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c578f0_0" Pin1InfoVect1LinkObjId="SW-257741_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3230,-1137 3230,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2030730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-1070 3267,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24170@x" ObjectIDND1="g_1c6f380@0" ObjectIDND2="g_1c6e610@0" ObjectIDZND0="42332@0" Pin0InfoVect0LinkObjId="SW-257740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131783_0" Pin1InfoVect1LinkObjId="g_1c6f380_0" Pin1InfoVect2LinkObjId="g_1c6e610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3230,-1070 3267,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20310a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-1070 3230,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c6f380@0" ObjectIDND1="g_1c6e610@0" ObjectIDND2="42332@x" ObjectIDZND0="24170@0" Pin0InfoVect0LinkObjId="SW-131783_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c6f380_0" Pin1InfoVect1LinkObjId="g_1c6e610_0" Pin1InfoVect2LinkObjId="SW-257740_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3230,-1070 3230,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20316c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-1030 3176,-1030 3176,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1c6e610@0" ObjectIDND1="24170@x" ObjectIDND2="42332@x" ObjectIDZND0="g_1c6f380@0" Pin0InfoVect0LinkObjId="g_1c6f380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c6e610_0" Pin1InfoVect1LinkObjId="SW-131783_0" Pin1InfoVect2LinkObjId="SW-257740_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3230,-1030 3176,-1030 3176,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2032030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3280,-1005 3280,-1030 3230,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c6e610@0" ObjectIDZND0="g_1c6f380@0" ObjectIDZND1="24170@x" ObjectIDZND2="42332@x" Pin0InfoVect0LinkObjId="g_1c6f380_0" Pin0InfoVect1LinkObjId="SW-131783_0" Pin0InfoVect2LinkObjId="SW-257740_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c6e610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3280,-1005 3280,-1030 3230,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2032220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3230,-1030 3230,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1c6f380@0" ObjectIDND1="g_1c6e610@0" ObjectIDZND0="24170@x" ObjectIDZND1="42332@x" Pin0InfoVect0LinkObjId="SW-131783_0" Pin0InfoVect1LinkObjId="SW-257740_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c6f380_0" Pin1InfoVect1LinkObjId="g_1c6e610_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3230,-1030 3230,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2032430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2941,-1079 2904,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="24167@x" ObjectIDND1="24166@x" ObjectIDZND0="42331@0" Pin0InfoVect0LinkObjId="SW-257606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131763_0" Pin1InfoVect1LinkObjId="SW-131761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2941,-1079 2904,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2033290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2942,-1094 2942,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24167@0" ObjectIDZND0="24166@x" ObjectIDZND1="42331@x" Pin0InfoVect0LinkObjId="SW-131761_0" Pin0InfoVect1LinkObjId="SW-257606_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2942,-1094 2942,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2033480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2942,-1079 2942,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24167@x" ObjectIDND1="42331@x" ObjectIDZND0="24166@1" Pin0InfoVect0LinkObjId="SW-131761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131763_0" Pin1InfoVect1LinkObjId="SW-257606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2942,-1079 2942,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2035780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2250,-717 2250,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1ff87a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2250,-717 2250,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_203b430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2250,-629 2250,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1ff87a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff87a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2250,-629 2250,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-551 2398,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24141@0" ObjectIDZND0="24143@1" Pin0InfoVect0LinkObjId="SW-131665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-551 2398,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203bd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-500 2398,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24143@0" ObjectIDZND0="g_1c8fb70@1" Pin0InfoVect0LinkObjId="g_1c8fb70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-500 2398,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-445 2398,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1c8fb70@0" ObjectIDZND0="g_1c08170@0" ObjectIDZND1="34110@x" Pin0InfoVect0LinkObjId="g_1c08170_0" Pin0InfoVect1LinkObjId="TF-NH_MJ.NH_MJ_1T_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8fb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-445 2398,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203c950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2432,-398 2398,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1c08170@0" ObjectIDZND0="g_1c8fb70@0" ObjectIDZND1="34110@x" Pin0InfoVect0LinkObjId="g_1c8fb70_0" Pin0InfoVect1LinkObjId="TF-NH_MJ.NH_MJ_1T_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c08170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2432,-398 2398,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-398 2398,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_1c8fb70@0" ObjectIDND1="g_1c08170@0" ObjectIDZND0="34110@0" Pin0InfoVect0LinkObjId="TF-NH_MJ.NH_MJ_1T_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c8fb70_0" Pin1InfoVect1LinkObjId="g_1c08170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-398 2398,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203cdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2849,-472 2849,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fba210@1" ObjectIDZND0="24149@0" Pin0InfoVect0LinkObjId="SW-131687_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fba210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2849,-472 2849,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203cfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2849,-526 2849,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24149@1" ObjectIDZND0="24147@0" Pin0InfoVect0LinkObjId="SW-131684_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131687_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2849,-526 2849,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203d240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2849,-433 2849,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_1fba210@0" ObjectIDZND0="34112@x" ObjectIDZND1="g_1fc76a0@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.074Ld_0" Pin0InfoVect1LinkObjId="g_1fc76a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fba210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2849,-433 2849,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2849,-363 2849,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="34112@0" ObjectIDZND0="g_1fba210@0" ObjectIDZND1="g_1fc76a0@0" Pin0InfoVect0LinkObjId="g_1fba210_0" Pin0InfoVect1LinkObjId="g_1fc76a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_MJ.074Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2849,-363 2849,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2849,-398 2879,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_1fba210@0" ObjectIDND1="34112@x" ObjectIDZND0="g_1fc76a0@0" Pin0InfoVect0LinkObjId="g_1fc76a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fba210_0" Pin1InfoVect1LinkObjId="EC-NH_MJ.074Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2849,-398 2879,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff62a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2645,-539 2645,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24144@0" ObjectIDZND0="24146@1" Pin0InfoVect0LinkObjId="SW-131676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2645,-539 2645,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff6490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2645,-479 2645,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24146@0" ObjectIDZND0="g_2055450@1" Pin0InfoVect0LinkObjId="g_2055450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2645,-479 2645,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff66a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2645,-416 2645,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_2055450@0" ObjectIDZND0="34111@x" ObjectIDZND1="g_2052f30@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.073Ld_0" Pin0InfoVect1LinkObjId="g_2052f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2055450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2645,-416 2645,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff7170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2645,-355 2645,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="34111@0" ObjectIDZND0="g_2055450@0" ObjectIDZND1="g_2052f30@0" Pin0InfoVect0LinkObjId="g_2055450_0" Pin0InfoVect1LinkObjId="g_2052f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_MJ.073Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2645,-355 2645,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2645,-388 2676,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_2055450@0" ObjectIDND1="34111@x" ObjectIDZND0="g_2052f30@0" Pin0InfoVect0LinkObjId="g_2052f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2055450_0" Pin1InfoVect1LinkObjId="EC-NH_MJ.073Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2645,-388 2676,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff7e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3069,-456 3069,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_204a500@1" ObjectIDZND0="24158@0" Pin0InfoVect0LinkObjId="SW-131720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_204a500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3069,-456 3069,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff8030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3069,-527 3069,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24158@1" ObjectIDZND0="24156@0" Pin0InfoVect0LinkObjId="SW-131717_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3069,-527 3069,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3069,-417 3069,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_204a500@0" ObjectIDZND0="34115@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.077Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_204a500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3069,-417 3069,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff90c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-389 3027,-389 3027,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_204c4f0@0" ObjectIDZND0="g_1ff87a0@0" Pin0InfoVect0LinkObjId="g_1ff87a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_204c4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-389 3027,-389 3027,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff97f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3262,-423 3262,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24152@0" ObjectIDZND0="g_1ff99e0@0" ObjectIDZND1="34113@x" Pin0InfoVect0LinkObjId="g_1ff99e0_0" Pin0InfoVect1LinkObjId="EC-NH_MJ.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3262,-423 3262,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffa4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3460,-398 3501,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24155@x" ObjectIDND1="34114@x" ObjectIDZND0="g_204e440@0" Pin0InfoVect0LinkObjId="g_204e440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131709_0" Pin1InfoVect1LinkObjId="EC-NH_MJ.076Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3460,-398 3501,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffaf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3460,-433 3460,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24155@0" ObjectIDZND0="g_204e440@0" ObjectIDZND1="34114@x" Pin0InfoVect0LinkObjId="g_204e440_0" Pin0InfoVect1LinkObjId="EC-NH_MJ.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3460,-433 3460,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffb1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3460,-398 3460,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_204e440@0" ObjectIDND1="24155@x" ObjectIDZND0="34114@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.076Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_204e440_0" Pin1InfoVect1LinkObjId="SW-131709_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3460,-398 3460,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ffee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3326,-1070 3304,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fff0f0@0" ObjectIDZND0="42332@1" Pin0InfoVect0LinkObjId="SW-257740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3326,-1070 3304,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20025f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2812,-1223 2790,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2002850@0" ObjectIDZND0="42327@1" Pin0InfoVect0LinkObjId="SW-257204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2002850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2812,-1223 2790,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2005d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3137,-1137 3159,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2005fb0@0" ObjectIDZND0="42333@1" Pin0InfoVect0LinkObjId="SW-257741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2005fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3137,-1137 3159,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2006a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2850,-1079 2872,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2006ca0@0" ObjectIDZND0="42331@1" Pin0InfoVect0LinkObjId="SW-257606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2006ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2850,-1079 2872,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5bee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2457,-1081 2479,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c5c140@0" ObjectIDZND0="42330@1" Pin0InfoVect0LinkObjId="SW-257478_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c5c140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2457,-1081 2479,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c61f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2550,-1128 2550,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24163@1" ObjectIDZND0="24135@0" Pin0InfoVect0LinkObjId="g_1c578f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-1128 2550,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c625e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2515,-1081 2550,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="42330@0" ObjectIDZND0="24163@x" ObjectIDZND1="24162@x" Pin0InfoVect0LinkObjId="SW-131741_0" Pin0InfoVect1LinkObjId="SW-131739_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2515,-1081 2550,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c62fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2550,-1052 2550,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24162@1" ObjectIDZND0="42330@x" ObjectIDZND1="24163@x" Pin0InfoVect0LinkObjId="SW-257478_0" Pin0InfoVect1LinkObjId="SW-131741_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-1052 2550,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c63210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2550,-1081 2550,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42330@x" ObjectIDND1="24162@x" ObjectIDZND0="24163@0" Pin0InfoVect0LinkObjId="SW-131741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-257478_0" Pin1InfoVect1LinkObjId="SW-131739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-1081 2550,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c63470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2550,-839 2550,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24164@1" ObjectIDZND0="24174@1" Pin0InfoVect0LinkObjId="g_1c056c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2550,-839 2550,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c66140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2813,-1286 2791,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c663a0@0" ObjectIDZND0="42328@1" Pin0InfoVect0LinkObjId="SW-257205_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c663a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2813,-1286 2791,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c66e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2754,-1223 2729,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42327@0" ObjectIDZND0="24137@x" ObjectIDZND1="24138@x" Pin0InfoVect0LinkObjId="SW-131648_0" Pin0InfoVect1LinkObjId="SW-131650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2754,-1223 2729,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c67920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1242 2729,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24137@0" ObjectIDZND0="42327@x" ObjectIDZND1="24138@x" Pin0InfoVect0LinkObjId="SW-257204_0" Pin0InfoVect1LinkObjId="SW-131650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1242 2729,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c67b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1223 2729,-1208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42327@x" ObjectIDND1="24137@x" ObjectIDZND0="24138@1" Pin0InfoVect0LinkObjId="SW-131650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-257204_0" Pin1InfoVect1LinkObjId="SW-131648_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1223 2729,-1208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c67de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2755,-1286 2729,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42328@0" ObjectIDZND0="24137@x" ObjectIDZND1="24139@x" Pin0InfoVect0LinkObjId="SW-131648_0" Pin0InfoVect1LinkObjId="SW-131651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2755,-1286 2729,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c688d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1269 2729,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24137@1" ObjectIDZND0="42328@x" ObjectIDZND1="24139@x" Pin0InfoVect0LinkObjId="SW-257205_0" Pin0InfoVect1LinkObjId="SW-131651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131648_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1269 2729,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c68b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1286 2729,-1299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42328@x" ObjectIDND1="24137@x" ObjectIDZND0="24139@0" Pin0InfoVect0LinkObjId="SW-131651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-257205_0" Pin1InfoVect1LinkObjId="SW-131648_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1286 2729,-1299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6aba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1343 3284,-1474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29426@x" ObjectIDND1="29425@x" ObjectIDZND0="g_214d850@0" Pin0InfoVect0LinkObjId="g_214d850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193516_0" Pin1InfoVect1LinkObjId="SW-193514_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1343 3284,-1474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6add0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3294,-1452 3245,-1452 3246,-1446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1fe58d0@0" ObjectIDZND0="g_1ffb430@0" Pin0InfoVect0LinkObjId="g_1ffb430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fe58d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3294,-1452 3245,-1452 3246,-1446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6b4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2615,-1341 2615,-1354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="24182@1" ObjectIDZND0="g_21496f0@0" Pin0InfoVect0LinkObjId="g_21496f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2615,-1341 2615,-1354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2149d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2640,-1411 2615,-1411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="42329@0" ObjectIDZND0="24171@x" ObjectIDZND1="g_21496f0@0" Pin0InfoVect0LinkObjId="SW-131786_0" Pin0InfoVect1LinkObjId="g_21496f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2640,-1411 2615,-1411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214a7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2615,-1423 2615,-1411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24171@0" ObjectIDZND0="42329@x" ObjectIDZND1="g_21496f0@0" Pin0InfoVect0LinkObjId="SW-257206_0" Pin0InfoVect1LinkObjId="g_21496f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2615,-1423 2615,-1411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214aa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2615,-1411 2615,-1400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="42329@x" ObjectIDND1="24171@x" ObjectIDZND0="g_21496f0@1" Pin0InfoVect0LinkObjId="g_21496f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-257206_0" Pin1InfoVect1LinkObjId="SW-131786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2615,-1411 2615,-1400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214ac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2676,-1411 2692,-1411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42329@1" ObjectIDZND0="g_1c6a360@0" Pin0InfoVect0LinkObjId="g_1c6a360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-257206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2676,-1411 2692,-1411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214aed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1372 2768,-1372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="24139@x" ObjectIDND1="34577@1" ObjectIDZND0="24140@0" Pin0InfoVect0LinkObjId="SW-131661_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131651_0" Pin1InfoVect1LinkObjId="g_214bb70_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1372 2768,-1372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1335 2729,-1372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="24139@1" ObjectIDZND0="24140@x" ObjectIDZND1="34577@1" Pin0InfoVect0LinkObjId="SW-131661_0" Pin0InfoVect1LinkObjId="g_214bb70_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131651_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1335 2729,-1372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2729,-1372 2729,-1497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="24139@x" ObjectIDND1="24140@x" ObjectIDZND0="34577@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131651_0" Pin1InfoVect1LinkObjId="SW-131661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2729,-1372 2729,-1497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2804,-1372 2824,-1372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24140@1" ObjectIDZND0="g_1c78680@0" Pin0InfoVect0LinkObjId="g_1c78680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131661_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2804,-1372 2824,-1372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_214c030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-1495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-1495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2781,-1502 2830,-1502 2830,-1462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24171@x" ObjectIDND1="g_1c68d90@0" ObjectIDZND0="g_1c77fc0@0" Pin0InfoVect0LinkObjId="g_1c77fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131786_0" Pin1InfoVect1LinkObjId="g_1c68d90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2781,-1502 2830,-1502 2830,-1462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214d390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2615,-1459 2615,-1502 2781,-1502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24171@1" ObjectIDZND0="g_1c77fc0@0" ObjectIDZND1="g_1c68d90@0" Pin0InfoVect0LinkObjId="g_1c77fc0_0" Pin0InfoVect1LinkObjId="g_1c68d90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131786_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2615,-1459 2615,-1502 2781,-1502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2781,-1502 2781,-1482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1c77fc0@0" ObjectIDND1="24171@x" ObjectIDZND0="g_1c68d90@0" Pin0InfoVect0LinkObjId="g_1c68d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c77fc0_0" Pin1InfoVect1LinkObjId="SW-131786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2781,-1502 2781,-1482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3284,-1528 3284,-1541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_214d850@1" ObjectIDZND0="34579@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_214d850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3284,-1528 3284,-1541 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_MJ"/>
</svg>