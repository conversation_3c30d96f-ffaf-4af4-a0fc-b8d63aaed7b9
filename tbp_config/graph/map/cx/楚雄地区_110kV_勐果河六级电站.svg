<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-138" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 1622 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape152">
    <ellipse cx="13" cy="27" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="53" x2="60" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="47" x2="65" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="54" x2="58" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="56" y1="28" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="56" y1="28" y2="28"/>
    <circle cx="13" cy="46" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="23" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="27" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="31" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="46" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="50" y2="46"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape27_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape27_1">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
   </symbol>
   <symbol id="voltageTransformer:shape68">
    <circle cx="14" cy="19" r="7.5" stroke-width="0.804311"/>
    <circle cx="26" cy="19" r="7.5" stroke-width="0.804311"/>
    <circle cx="26" cy="8" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="29" x2="27" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="24" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="29" x2="27" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="24" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="29" x2="27" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="29" x2="27" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="17" x2="13" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="17" x2="13" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="13" x2="13" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="14" x2="12" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="16" x2="14" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="16" x2="14" y1="16" y2="19"/>
    <circle cx="14" cy="8" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="19,9 35,9 35,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="voltageTransformer:shape14">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="7" y1="3" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="6,19 26,19 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="2" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="6" y2="4"/>
    <circle cx="30" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="39" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="30" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="22" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="40" x2="40" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="24" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="18" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="24" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251787" x1="6" x2="6" y1="19" y2="9"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_31862d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3186ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31879e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3188920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3189bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_318a8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_318b020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_318bb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_254bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_254bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_318eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_318eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31908e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31908e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3191900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3193500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31940f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3194eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31957f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3196eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3197ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3198350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3198b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3199bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_319a570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_319b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319ba20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319ce40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319e9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_319f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31addf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31a0f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31a2500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31a3a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1632" x="3112" y="-1205"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4587,-856 4597,-856 4592,-866 4587,-856 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-88850">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19196" ObjectName="SW-CX_MGH.CX_MGH_681BK"/>
     <cge:Meas_Ref ObjectId="88850"/>
    <cge:TPSR_Ref TObjectID="19196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88820">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.000000 -746.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19187" ObjectName="SW-CX_MGH.CX_MGH_081BK"/>
     <cge:Meas_Ref ObjectId="88820"/>
    <cge:TPSR_Ref TObjectID="19187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88847">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -320.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19193" ObjectName="SW-CX_MGH.CX_MGH_683BK"/>
     <cge:Meas_Ref ObjectId="88847"/>
    <cge:TPSR_Ref TObjectID="19193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88853">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19199" ObjectName="SW-CX_MGH.CX_MGH_682BK"/>
     <cge:Meas_Ref ObjectId="88853"/>
    <cge:TPSR_Ref TObjectID="19199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88864">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.131579 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19203" ObjectName="SW-CX_MGH.CX_MGH_601BK"/>
     <cge:Meas_Ref ObjectId="88864"/>
    <cge:TPSR_Ref TObjectID="19203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88816">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.131579 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19183" ObjectName="SW-CX_MGH.CX_MGH_181BK"/>
     <cge:Meas_Ref ObjectId="88816"/>
    <cge:TPSR_Ref TObjectID="19183"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2535a90">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.131579 -987.000000)" xlink:href="#voltageTransformer:shape68"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264d870">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 -626.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25eccb0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 -183.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25bcfe0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 -183.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_MGH" endPointId="0" endStationName="CX_TX" flowDrawDirect="1" flowShape="0" id="AC-110kV.mengguoheliu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3837,-1096 3837,-1053 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34032" ObjectName="AC-110kV.mengguoheliu_line"/>
    <cge:TPSR_Ref TObjectID="34032_SS-138"/></metadata>
   <polyline fill="none" opacity="0" points="3837,-1096 3837,-1053 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25e2280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 3735.131579 -951.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2586bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 3733.131579 -874.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25abd40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3951.000000 -654.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264c980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3725.131579 -555.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d63d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 -537.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2572020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3619.000000 -397.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f9030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.000000 -381.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2613fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4115.000000 -397.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2551730">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -583.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2551f00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -584.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2552430">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.131579 -625.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e0110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -659.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2650e10">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.131579 -1030.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25379f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.131579 -996.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f3aa0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -661.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ab2a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3921.000000 -662.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d12a0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4587.000000 -821.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e7500">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 -232.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e8440">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3798.000000 -248.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e9510">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -245.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ea1b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 -171.000000)" xlink:href="#lightningRod:shape152"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f7320">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 -252.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2554410">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -220.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2555150">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3640.000000 -317.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2556eb0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 -232.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fa850">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -248.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fc5d0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -245.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fd310">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.000000 -171.000000)" xlink:href="#lightningRod:shape152"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25bf7e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -220.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d9a60">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 -317.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116502" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3240.538462 -1014.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116502" ObjectName="CX_MGH:CX_MGH_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116503" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3239.538462 -973.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116503" ObjectName="CX_MGH:CX_MGH_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88808" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.131579 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19183"/>
     <cge:Term_Ref ObjectID="26667"/>
    <cge:TPSR_Ref TObjectID="19183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.131579 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19183"/>
     <cge:Term_Ref ObjectID="26667"/>
    <cge:TPSR_Ref TObjectID="19183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.131579 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19183"/>
     <cge:Term_Ref ObjectID="26667"/>
    <cge:TPSR_Ref TObjectID="19183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88828" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -411.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19196"/>
     <cge:Term_Ref ObjectID="26686"/>
    <cge:TPSR_Ref TObjectID="19196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88829" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -411.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88829" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19196"/>
     <cge:Term_Ref ObjectID="26686"/>
    <cge:TPSR_Ref TObjectID="19196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -411.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19196"/>
     <cge:Term_Ref ObjectID="26686"/>
    <cge:TPSR_Ref TObjectID="19196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -412.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19199"/>
     <cge:Term_Ref ObjectID="26692"/>
    <cge:TPSR_Ref TObjectID="19199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88835" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -412.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88835" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19199"/>
     <cge:Term_Ref ObjectID="26692"/>
    <cge:TPSR_Ref TObjectID="19199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88837" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -412.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19199"/>
     <cge:Term_Ref ObjectID="26692"/>
    <cge:TPSR_Ref TObjectID="19199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-88840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -559.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19192"/>
     <cge:Term_Ref ObjectID="26679"/>
    <cge:TPSR_Ref TObjectID="19192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-88841" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -559.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88841" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19192"/>
     <cge:Term_Ref ObjectID="26679"/>
    <cge:TPSR_Ref TObjectID="19192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-88842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -559.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19192"/>
     <cge:Term_Ref ObjectID="26679"/>
    <cge:TPSR_Ref TObjectID="19192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-88843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -559.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19192"/>
     <cge:Term_Ref ObjectID="26679"/>
    <cge:TPSR_Ref TObjectID="19192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-88846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -559.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19192"/>
     <cge:Term_Ref ObjectID="26679"/>
    <cge:TPSR_Ref TObjectID="19192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.131579 -621.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19203"/>
     <cge:Term_Ref ObjectID="26698"/>
    <cge:TPSR_Ref TObjectID="19203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.131579 -621.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19203"/>
     <cge:Term_Ref ObjectID="26698"/>
    <cge:TPSR_Ref TObjectID="19203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.131579 -621.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19203"/>
     <cge:Term_Ref ObjectID="26698"/>
    <cge:TPSR_Ref TObjectID="19203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4640.000000 -355.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19193"/>
     <cge:Term_Ref ObjectID="26680"/>
    <cge:TPSR_Ref TObjectID="19193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4640.000000 -355.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19193"/>
     <cge:Term_Ref ObjectID="26680"/>
    <cge:TPSR_Ref TObjectID="19193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4640.000000 -355.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19193"/>
     <cge:Term_Ref ObjectID="26680"/>
    <cge:TPSR_Ref TObjectID="19193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-88884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.000000 -788.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19187"/>
     <cge:Term_Ref ObjectID="26675"/>
    <cge:TPSR_Ref TObjectID="19187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-88885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.000000 -788.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19187"/>
     <cge:Term_Ref ObjectID="26675"/>
    <cge:TPSR_Ref TObjectID="19187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-88886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.000000 -788.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19187"/>
     <cge:Term_Ref ObjectID="26675"/>
    <cge:TPSR_Ref TObjectID="19187"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="173" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="173" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="173" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_MGH.CX_MGH_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-480 4614,-480 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19192" ObjectName="BS-CX_MGH.CX_MGH_6IM"/>
    <cge:TPSR_Ref TObjectID="19192"/></metadata>
   <polyline fill="none" opacity="0" points="3589,-480 4614,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_MGH.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-826 3847,-826 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49491" ObjectName="BS-CX_MGH.XM"/>
    <cge:TPSR_Ref TObjectID="49491"/></metadata>
   <polyline fill="none" opacity="0" points="3830,-826 3847,-826 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_MGH.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 -158.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43421" ObjectName="SM-CX_MGH.P1"/>
    <cge:TPSR_Ref TObjectID="43421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_MGH.P2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -158.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43422" ObjectName="SM-CX_MGH.P2"/>
    <cge:TPSR_Ref TObjectID="43422"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="19192" cx="4345" cy="-480" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19192" cx="3731" cy="-480" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19192" cx="4511" cy="-480" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19192" cx="4227" cy="-480" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19192" cx="3837" cy="-480" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49491" cx="3837" cy="-826" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49491" cx="3837" cy="-826" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24febb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3684.000000 -138.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2661330" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4266.000000 -678.000000) translate(0,15)">6kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_239e2b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3863.000000 -1144.000000) translate(0,15)">勐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_239e2b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3863.000000 -1144.000000) translate(0,33)">果</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_239e2b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3863.000000 -1144.000000) translate(0,51)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_239e2b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3863.000000 -1144.000000) translate(0,69)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_239e2b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3863.000000 -1144.000000) translate(0,87)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_239e2b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3863.000000 -1144.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_251e730" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3784.000000 -132.000000) translate(0,15)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_251ea90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4563.000000 -922.000000) translate(0,15)">近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f0310" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4527.000000 -552.000000) translate(0,15)">10kV 2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2552c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -864.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2552df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3874.000000 -749.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2552f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -387.000000) translate(0,12)">681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25530d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3745.000000 -448.000000) translate(0,12)">6811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2553240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4603.000000 -775.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25533b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3776.131579 -909.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2553520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -925.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2553690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3776.131579 -983.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2599d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2599d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2599d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2599d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2599d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2599d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2599d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_25e2070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3272.000000 -1166.500000) translate(0,16)">勐果河六级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -751.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -751.000000) translate(0,33)">SF10-12500/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -751.000000) translate(0,51)">12500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -751.000000) translate(0,69)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -751.000000) translate(0,87)">121±2×2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -751.000000) translate(0,105)">Ud=10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -305.000000) translate(0,15)">1号机组参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -305.000000) translate(0,33)">SFW4500-8/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -305.000000) translate(0,51)">Pe=4500kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -305.000000) translate(0,69)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -305.000000) translate(0,87)">cos∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e0720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -305.000000) translate(0,105)">750转/分</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2652070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -985.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -706.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264d320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3858.000000 -613.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264d510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.000000 -588.000000) translate(0,12)">60117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264d6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3860.000000 -528.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d6f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -570.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d72f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -517.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26251c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3577.000000 -471.000000) translate(0,15)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2572d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -430.000000) translate(0,12)">68117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24f6c20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4468.000000 -136.000000) translate(0,15)">6kV 1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f70f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 -347.000000) translate(0,12)">683</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2610cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -410.000000) translate(0,12)">68317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26111a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4463.000000 -446.000000) translate(0,12)">6831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26113e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4180.000000 -138.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2611c60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4280.000000 -132.000000) translate(0,15)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2612150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4236.000000 -387.000000) translate(0,12)">682</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2612390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -448.000000) translate(0,12)">6821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -320.000000) translate(0,15)">2号机组参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -320.000000) translate(0,33)">SFW4500-8/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -320.000000) translate(0,51)">Pe=4500kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -320.000000) translate(0,69)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -320.000000) translate(0,87)">cos∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2612ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -320.000000) translate(0,105)">750转/分</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_262f0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -430.000000) translate(0,12)">68217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_24f1780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -219.000000) translate(0,14)">4718</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2553800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3904.000000 879.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 864.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2586940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 849.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2673ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 621.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2674030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 606.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26741a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 591.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26744f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 413.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2674660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 398.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26747d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 383.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2674b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 353.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 338.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251cf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.000000 323.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26127b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 413.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2612a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 398.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2612cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 383.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26440f0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3572.000000 544.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2644c40" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3572.000000 529.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26451a0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3564.000000 514.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2645420" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3580.000000 500.500000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2645f80" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3572.000000 559.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2648ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 788.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f1370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 773.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f1540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 758.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_MGH.CX_MGH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="26710"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3812.131579 -650.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3812.131579 -650.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19202" ObjectName="TF-CX_MGH.CX_MGH_1T"/>
    <cge:TPSR_Ref TObjectID="19202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -560.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -560.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -138.000000)" xlink:href="#transformer2:shape27_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -138.000000)" xlink:href="#transformer2:shape27_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -146.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -146.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 -138.000000)" xlink:href="#transformer2:shape27_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 -138.000000)" xlink:href="#transformer2:shape27_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-87891" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19043" ObjectName="DYN-CX_MGH"/>
     <cge:Meas_Ref ObjectId="87891"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-88818">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.131579 -878.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19185" ObjectName="SW-CX_MGH.CX_MGH_18160SW"/>
     <cge:Meas_Ref ObjectId="88818"/>
    <cge:TPSR_Ref TObjectID="19185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88819">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.131579 -955.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19186" ObjectName="SW-CX_MGH.CX_MGH_18167SW"/>
     <cge:Meas_Ref ObjectId="88819"/>
    <cge:TPSR_Ref TObjectID="19186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88824">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -678.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19188" ObjectName="SW-CX_MGH.CX_MGH_1010SW"/>
     <cge:Meas_Ref ObjectId="88824"/>
    <cge:TPSR_Ref TObjectID="19188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88865">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.131579 -496.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19204" ObjectName="SW-CX_MGH.CX_MGH_6011SW"/>
     <cge:Meas_Ref ObjectId="88865"/>
    <cge:TPSR_Ref TObjectID="19204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88869">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.131579 -556.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19207" ObjectName="SW-CX_MGH.CX_MGH_60117SW"/>
     <cge:Meas_Ref ObjectId="88869"/>
    <cge:TPSR_Ref TObjectID="19207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88867">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19205" ObjectName="SW-CX_MGH.CX_MGH_6901SW"/>
     <cge:Meas_Ref ObjectId="88867"/>
    <cge:TPSR_Ref TObjectID="19205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88868">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4273.000000 -538.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19206" ObjectName="SW-CX_MGH.CX_MGH_69017SW"/>
     <cge:Meas_Ref ObjectId="88868"/>
    <cge:TPSR_Ref TObjectID="19206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88851">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19197" ObjectName="SW-CX_MGH.CX_MGH_6811SW"/>
     <cge:Meas_Ref ObjectId="88851"/>
    <cge:TPSR_Ref TObjectID="19197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88852">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -398.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19198" ObjectName="SW-CX_MGH.CX_MGH_68117SW"/>
     <cge:Meas_Ref ObjectId="88852"/>
    <cge:TPSR_Ref TObjectID="19198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88848">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -417.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19194" ObjectName="SW-CX_MGH.CX_MGH_6831SW"/>
     <cge:Meas_Ref ObjectId="88848"/>
    <cge:TPSR_Ref TObjectID="19194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88849">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -382.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19195" ObjectName="SW-CX_MGH.CX_MGH_68317SW"/>
     <cge:Meas_Ref ObjectId="88849"/>
    <cge:TPSR_Ref TObjectID="19195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88854">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -424.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19200" ObjectName="SW-CX_MGH.CX_MGH_6821SW"/>
     <cge:Meas_Ref ObjectId="88854"/>
    <cge:TPSR_Ref TObjectID="19200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88855">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -398.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19201" ObjectName="SW-CX_MGH.CX_MGH_68217SW"/>
     <cge:Meas_Ref ObjectId="88855"/>
    <cge:TPSR_Ref TObjectID="19201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-88817">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.131579 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19184" ObjectName="SW-CX_MGH.CX_MGH_1816SW"/>
     <cge:Meas_Ref ObjectId="88817"/>
    <cge:TPSR_Ref TObjectID="19184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319280">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.131579 -767.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49492" ObjectName="SW-CX_MGH.XB"/>
     <cge:Meas_Ref ObjectId="319280"/>
    <cge:TPSR_Ref TObjectID="49492"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_24fe9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3783,-960 3758,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19186@0" ObjectIDZND0="g_25e2280@0" Pin0InfoVect0LinkObjId="g_25e2280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88819_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3783,-960 3758,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2596700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-883 3778,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2586bf0@0" ObjectIDZND0="19185@0" Pin0InfoVect0LinkObjId="SW-88818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2586bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-883 3778,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2588920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-883 3837,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19185@1" ObjectIDZND0="19183@x" ObjectIDZND1="19184@x" Pin0InfoVect0LinkObjId="SW-88816_0" Pin0InfoVect1LinkObjId="SW-88817_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-883 3837,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2615290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-883 3837,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19185@x" ObjectIDND1="19184@x" ObjectIDZND0="19183@1" Pin0InfoVect0LinkObjId="SW-88816_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88818_0" Pin1InfoVect1LinkObjId="SW-88817_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-883 3837,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2615480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-900 3837,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19184@0" ObjectIDZND0="19185@x" ObjectIDZND1="19183@x" Pin0InfoVect0LinkObjId="SW-88818_0" Pin0InfoVect1LinkObjId="SW-88816_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88817_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-900 3837,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_251e540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-461 3731,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19197@1" ObjectIDZND0="19192@0" Pin0InfoVect0LinkObjId="g_251e8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-461 3731,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_251e8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-501 3837,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19204@0" ObjectIDZND0="19192@0" Pin0InfoVect0LinkObjId="g_251e540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-501 3837,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-664 4593,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_25e0110@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2535a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e0110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-664 4593,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e5fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-717 4593,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_25e0110@1" ObjectIDZND0="19187@0" Pin0InfoVect0LinkObjId="SW-88820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e0110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-717 4593,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25e61b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-960 3837,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="19186@1" ObjectIDZND0="19184@x" ObjectIDZND1="g_25379f0@0" ObjectIDZND2="34032@1" Pin0InfoVect0LinkObjId="SW-88817_0" Pin0InfoVect1LinkObjId="g_25379f0_0" Pin0InfoVect2LinkObjId="g_2651820_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88819_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-960 3837,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25e6970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-936 3837,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="19184@1" ObjectIDZND0="g_25379f0@0" ObjectIDZND1="34032@1" ObjectIDZND2="g_2650e10@0" Pin0InfoVect0LinkObjId="g_25379f0_0" Pin0InfoVect1LinkObjId="g_2651820_1" Pin0InfoVect2LinkObjId="g_2650e10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88817_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-936 3837,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_264fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-843 3837,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="19183@0" ObjectIDZND0="49491@0" Pin0InfoVect0LinkObjId="g_2bd7980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-843 3837,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2651820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-1038 3837,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2650e10@0" ObjectIDZND0="34032@1" ObjectIDZND1="19184@x" ObjectIDZND2="19186@x" Pin0InfoVect0LinkObjId="g_25358a0_1" Pin0InfoVect1LinkObjId="SW-88817_0" Pin0InfoVect2LinkObjId="SW-88819_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2650e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-1038 3837,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25358a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-1038 3837,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2650e10@0" ObjectIDND1="19184@x" ObjectIDND2="19186@x" ObjectIDZND0="34032@1" Pin0InfoVect0LinkObjId="g_2651820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2650e10_0" Pin1InfoVect1LinkObjId="SW-88817_0" Pin1InfoVect2LinkObjId="SW-88819_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-1038 3837,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2537610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-960 3837,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="19184@x" ObjectIDND1="19186@x" ObjectIDZND0="g_25379f0@0" ObjectIDZND1="34032@1" ObjectIDZND2="g_2650e10@0" Pin0InfoVect0LinkObjId="g_25379f0_0" Pin0InfoVect1LinkObjId="g_2651820_1" Pin0InfoVect2LinkObjId="g_2650e10_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88817_0" Pin1InfoVect1LinkObjId="SW-88819_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-960 3837,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2537800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-1005 3837,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="19184@x" ObjectIDND1="19186@x" ObjectIDND2="g_25379f0@0" ObjectIDZND0="34032@1" ObjectIDZND1="g_2650e10@0" Pin0InfoVect0LinkObjId="g_2651820_1" Pin0InfoVect1LinkObjId="g_2650e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88817_0" Pin1InfoVect1LinkObjId="SW-88819_0" Pin1InfoVect2LinkObjId="g_25379f0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-1005 3837,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2538060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-1005 3896,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2535a90@0" ObjectIDZND0="g_25379f0@1" Pin0InfoVect0LinkObjId="g_25379f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2535a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-1005 3896,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2538250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3865,-1005 3837,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_25379f0@0" ObjectIDZND0="19184@x" ObjectIDZND1="19186@x" ObjectIDZND2="34032@1" Pin0InfoVect0LinkObjId="SW-88817_0" Pin0InfoVect1LinkObjId="SW-88819_0" Pin0InfoVect2LinkObjId="g_2651820_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25379f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3865,-1005 3837,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25abb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3907,-719 3957,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25f3aa0@0" ObjectIDZND0="19188@1" Pin0InfoVect0LinkObjId="SW-88824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25f3aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3907,-719 3957,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25ac4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-672 3957,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25abd40@0" ObjectIDZND0="19188@0" Pin0InfoVect0LinkObjId="SW-88824_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25abd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-672 3957,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_264d130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3770,-561 3743,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19207@0" ObjectIDZND0="g_264c980@0" Pin0InfoVect0LinkObjId="g_264c980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3770,-561 3743,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25d4400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-480 4345,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19192@0" ObjectIDZND0="19205@0" Pin0InfoVect0LinkObjId="SW-88867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_251e540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-480 4345,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25d6d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-543 4251,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19206@0" ObjectIDZND0="g_25d63d0@0" Pin0InfoVect0LinkObjId="g_25d63d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-543 4251,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d1cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-829 4592,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_25d12a0@0" ObjectIDZND0="19187@x" Pin0InfoVect0LinkObjId="SW-88820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d12a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-829 4592,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d2630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-781 4592,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="19187@1" ObjectIDZND0="g_25d12a0@0" Pin0InfoVect0LinkObjId="g_25d12a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-781 4592,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d2850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-829 4592,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" ObjectIDND0="g_25d12a0@0" ObjectIDND1="19187@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d12a0_0" Pin1InfoVect1LinkObjId="SW-88820_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-829 4592,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2572950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3700,-403 3731,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="19198@1" ObjectIDZND0="19196@x" ObjectIDZND1="19197@x" Pin0InfoVect0LinkObjId="SW-88850_0" Pin0InfoVect1LinkObjId="SW-88851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88852_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3700,-403 3731,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2572b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3664,-403 3637,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19198@0" ObjectIDZND0="g_2572020@0" Pin0InfoVect0LinkObjId="g_2572020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88852_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3664,-403 3637,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e7120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-393 3731,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19196@1" ObjectIDZND0="19198@x" ObjectIDZND1="19197@x" Pin0InfoVect0LinkObjId="SW-88852_0" Pin0InfoVect1LinkObjId="SW-88851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-393 3731,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e7310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-403 3731,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19196@x" ObjectIDND1="19198@x" ObjectIDZND0="19197@0" Pin0InfoVect0LinkObjId="SW-88851_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88850_0" Pin1InfoVect1LinkObjId="SW-88852_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-403 3731,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e7de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-207 3731,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="43421@0" ObjectIDZND0="g_25e7500@0" Pin0InfoVect0LinkObjId="g_25e7500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_MGH.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-207 3731,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e8000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-271 3731,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_25e7500@1" ObjectIDZND0="g_25e8440@0" ObjectIDZND1="g_25e9510@0" ObjectIDZND2="g_2554410@0" Pin0InfoVect0LinkObjId="g_25e8440_0" Pin0InfoVect1LinkObjId="g_25e9510_0" Pin0InfoVect2LinkObjId="g_2554410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e7500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-271 3731,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-301 3807,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_25e7500@0" ObjectIDND1="g_2554410@0" ObjectIDND2="g_2555150@0" ObjectIDZND0="g_25e8440@0" ObjectIDZND1="g_25e9510@0" Pin0InfoVect0LinkObjId="g_25e8440_0" Pin0InfoVect1LinkObjId="g_25e9510_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e7500_0" Pin1InfoVect1LinkObjId="g_2554410_0" Pin1InfoVect2LinkObjId="g_2555150_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-301 3807,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e8be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3807,-301 3807,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25e9510@0" ObjectIDND1="g_25e7500@0" ObjectIDND2="g_2554410@0" ObjectIDZND0="g_25e8440@1" Pin0InfoVect0LinkObjId="g_25e8440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e9510_0" Pin1InfoVect1LinkObjId="g_25e7500_0" Pin1InfoVect2LinkObjId="g_2554410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3807,-301 3807,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e8e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3807,-253 3807,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_25e8440@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2535a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e8440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3807,-253 3807,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e9cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-230 3883,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25ea1b0@0" ObjectIDZND0="g_25e9510@0" Pin0InfoVect0LinkObjId="g_25e9510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25ea1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-230 3883,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-281 3883,-301 3807,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_25e9510@1" ObjectIDZND0="g_25e8440@0" ObjectIDZND1="g_25e7500@0" ObjectIDZND2="g_2554410@0" Pin0InfoVect0LinkObjId="g_25e8440_0" Pin0InfoVect1LinkObjId="g_25e7500_0" Pin0InfoVect2LinkObjId="g_2554410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e9510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-281 3883,-301 3807,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24f7a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-257 4512,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_24f7320@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2535a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24f7320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-257 4512,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24f7cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-310 4512,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_24f7320@1" ObjectIDZND0="19193@0" Pin0InfoVect0LinkObjId="SW-88847_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24f7320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-310 4512,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24f7f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-462 4511,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19194@1" ObjectIDZND0="19192@0" Pin0InfoVect0LinkObjId="g_251e540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88848_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-462 4511,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24fa150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-355 4511,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19193@1" ObjectIDZND0="19195@x" ObjectIDZND1="19194@x" Pin0InfoVect0LinkObjId="SW-88849_0" Pin0InfoVect1LinkObjId="SW-88848_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88847_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-355 4511,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24fa3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-387 4511,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19193@x" ObjectIDND1="19195@x" ObjectIDZND0="19194@0" Pin0InfoVect0LinkObjId="SW-88848_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88847_0" Pin1InfoVect1LinkObjId="SW-88849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-387 4511,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2610830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-387 4561,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19193@x" ObjectIDND1="19194@x" ObjectIDZND0="19195@0" Pin0InfoVect0LinkObjId="SW-88849_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88847_0" Pin1InfoVect1LinkObjId="SW-88848_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-387 4561,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2610a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-387 4635,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19195@1" ObjectIDZND0="g_24f9030@0" Pin0InfoVect0LinkObjId="g_24f9030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88849_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-387 4635,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2611a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-465 4227,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19200@1" ObjectIDZND0="19192@0" Pin0InfoVect0LinkObjId="g_251e540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-465 4227,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_262ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4196,-403 4227,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19201@1" ObjectIDZND0="19200@x" ObjectIDZND1="19199@x" Pin0InfoVect0LinkObjId="SW-88854_0" Pin0InfoVect1LinkObjId="SW-88853_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4196,-403 4227,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_262ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-403 4133,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19201@0" ObjectIDZND0="g_2613fc0@0" Pin0InfoVect0LinkObjId="g_2613fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-403 4133,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_262f590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-403 4227,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="19201@x" ObjectIDND1="19199@x" ObjectIDZND0="19200@0" Pin0InfoVect0LinkObjId="SW-88854_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88855_0" Pin1InfoVect1LinkObjId="SW-88853_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-403 4227,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_253c410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-631 3837,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_2552430@0" ObjectIDZND0="19202@x" ObjectIDZND1="19203@x" Pin0InfoVect0LinkObjId="g_2647ef0_0" Pin0InfoVect1LinkObjId="SW-88864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2552430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-631 3837,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_253cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-656 3837,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="19202@1" ObjectIDZND0="g_2552430@0" ObjectIDZND1="19203@x" Pin0InfoVect0LinkObjId="g_2552430_0" Pin0InfoVect1LinkObjId="SW-88864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_253c410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-656 3837,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_253d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-631 3837,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_2552430@0" ObjectIDND1="19202@x" ObjectIDZND0="19203@1" Pin0InfoVect0LinkObjId="SW-88864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2552430_0" Pin1InfoVect1LinkObjId="g_253c410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-631 3837,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_253d3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-561 3837,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19207@1" ObjectIDZND0="19204@x" ObjectIDZND1="19203@x" Pin0InfoVect0LinkObjId="SW-88865_0" Pin0InfoVect1LinkObjId="SW-88864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88869_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-561 3837,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_253deb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-537 3837,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="19204@1" ObjectIDZND0="19207@x" ObjectIDZND1="19203@x" Pin0InfoVect0LinkObjId="SW-88869_0" Pin0InfoVect1LinkObjId="SW-88864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-537 3837,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_253e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-561 3837,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="19204@x" ObjectIDND1="19207@x" ObjectIDZND0="19203@0" Pin0InfoVect0LinkObjId="SW-88864_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88865_0" Pin1InfoVect1LinkObjId="SW-88869_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-561 3837,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2554c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-205 3656,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_25eccb0@0" ObjectIDZND0="g_2554410@1" Pin0InfoVect0LinkObjId="g_2554410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25eccb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-205 3656,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2554ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-256 3656,-301 3731,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2554410@0" ObjectIDZND0="g_25e7500@0" ObjectIDZND1="g_25e8440@0" ObjectIDZND2="g_25e9510@0" Pin0InfoVect0LinkObjId="g_25e7500_0" Pin0InfoVect1LinkObjId="g_25e8440_0" Pin0InfoVect2LinkObjId="g_25e9510_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2554410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-256 3656,-301 3731,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2555f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3699,-325 3731,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2555150@0" ObjectIDZND0="g_25e7500@0" ObjectIDZND1="g_25e8440@0" ObjectIDZND2="g_25e9510@0" Pin0InfoVect0LinkObjId="g_25e7500_0" Pin0InfoVect1LinkObjId="g_25e8440_0" Pin0InfoVect2LinkObjId="g_25e9510_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2555150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3699,-325 3731,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25569f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-301 3731,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_25e7500@0" ObjectIDND1="g_25e8440@0" ObjectIDND2="g_25e9510@0" ObjectIDZND0="g_2555150@0" ObjectIDZND1="19196@x" Pin0InfoVect0LinkObjId="g_2555150_0" Pin0InfoVect1LinkObjId="SW-88850_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e7500_0" Pin1InfoVect1LinkObjId="g_25e8440_0" Pin1InfoVect2LinkObjId="g_25e9510_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-301 3731,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2556c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-325 3731,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2555150@0" ObjectIDND1="g_25e7500@0" ObjectIDND2="g_25e8440@0" ObjectIDZND0="19196@0" Pin0InfoVect0LinkObjId="SW-88850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2555150_0" Pin1InfoVect1LinkObjId="g_25e7500_0" Pin1InfoVect2LinkObjId="g_25e8440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3731,-325 3731,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2557930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-207 4227,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="43422@0" ObjectIDZND0="g_2556eb0@0" Pin0InfoVect0LinkObjId="g_2556eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_MGH.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-207 4227,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2557b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-271 4227,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2556eb0@1" ObjectIDZND0="g_24fa850@0" ObjectIDZND1="g_24fc5d0@0" ObjectIDZND2="g_25d9a60@0" Pin0InfoVect0LinkObjId="g_24fa850_0" Pin0InfoVect1LinkObjId="g_24fc5d0_0" Pin0InfoVect2LinkObjId="g_25d9a60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2556eb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-271 4227,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24fa5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-301 4303,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2556eb0@0" ObjectIDND1="g_25d9a60@0" ObjectIDND2="19199@x" ObjectIDZND0="g_24fa850@0" ObjectIDZND1="g_24fc5d0@0" Pin0InfoVect0LinkObjId="g_24fa850_0" Pin0InfoVect1LinkObjId="g_24fc5d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2556eb0_0" Pin1InfoVect1LinkObjId="g_25d9a60_0" Pin1InfoVect2LinkObjId="SW-88853_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-301 4303,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24faf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-301 4303,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2556eb0@0" ObjectIDND1="g_25d9a60@0" ObjectIDND2="19199@x" ObjectIDZND0="g_24fa850@1" Pin0InfoVect0LinkObjId="g_24fa850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2556eb0_0" Pin1InfoVect1LinkObjId="g_25d9a60_0" Pin1InfoVect2LinkObjId="SW-88853_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-301 4303,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24fb160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-253 4303,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_24fa850@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2535a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24fa850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-253 4303,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24fce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-230 4379,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_24fd310@0" ObjectIDZND0="g_24fc5d0@0" Pin0InfoVect0LinkObjId="g_24fc5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24fd310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-230 4379,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24fd0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-281 4379,-301 4303,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_24fc5d0@1" ObjectIDZND0="g_24fa850@0" ObjectIDZND1="g_2556eb0@0" ObjectIDZND2="g_25d9a60@0" Pin0InfoVect0LinkObjId="g_24fa850_0" Pin0InfoVect1LinkObjId="g_2556eb0_0" Pin0InfoVect2LinkObjId="g_25d9a60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24fc5d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-281 4379,-301 4303,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25d95a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-205 4152,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_25bcfe0@0" ObjectIDZND0="g_25bf7e0@1" Pin0InfoVect0LinkObjId="g_25bf7e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25bcfe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-205 4152,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25d9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-256 4152,-301 4227,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_25bf7e0@0" ObjectIDZND0="g_24fa850@0" ObjectIDZND1="g_24fc5d0@0" ObjectIDZND2="g_2556eb0@0" Pin0InfoVect0LinkObjId="g_24fa850_0" Pin0InfoVect1LinkObjId="g_24fc5d0_0" Pin0InfoVect2LinkObjId="g_2556eb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25bf7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-256 4152,-301 4227,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25da810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4195,-325 4227,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_25d9a60@0" ObjectIDZND0="g_24fa850@0" ObjectIDZND1="g_24fc5d0@0" ObjectIDZND2="g_2556eb0@0" Pin0InfoVect0LinkObjId="g_24fa850_0" Pin0InfoVect1LinkObjId="g_24fc5d0_0" Pin0InfoVect2LinkObjId="g_2556eb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d9a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4195,-325 4227,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25daa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-301 4227,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_24fa850@0" ObjectIDND1="g_24fc5d0@0" ObjectIDND2="g_2556eb0@0" ObjectIDZND0="g_25d9a60@0" ObjectIDZND1="19199@x" Pin0InfoVect0LinkObjId="g_25d9a60_0" Pin0InfoVect1LinkObjId="SW-88853_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24fa850_0" Pin1InfoVect1LinkObjId="g_24fc5d0_0" Pin1InfoVect2LinkObjId="g_2556eb0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-301 4227,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25dc8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-324 4227,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_24fa850@0" ObjectIDND1="g_24fc5d0@0" ObjectIDND2="g_2556eb0@0" ObjectIDZND0="19199@0" Pin0InfoVect0LinkObjId="SW-88853_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24fa850_0" Pin1InfoVect1LinkObjId="g_24fc5d0_0" Pin1InfoVect2LinkObjId="g_2556eb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-324 4227,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25dcb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-393 4227,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19199@1" ObjectIDZND0="19201@x" ObjectIDZND1="19200@x" Pin0InfoVect0LinkObjId="SW-88855_0" Pin0InfoVect1LinkObjId="SW-88854_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88853_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-393 4227,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2646200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-543 4345,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="19205@x" ObjectIDND1="19206@x" ObjectIDZND0="g_2551730@0" ObjectIDZND1="g_2551f00@0" Pin0InfoVect0LinkObjId="g_2551730_0" Pin0InfoVect1LinkObjId="g_2551f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-88867_0" Pin1InfoVect1LinkObjId="SW-88868_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-543 4345,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2646b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4314,-543 4347,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19206@1" ObjectIDZND0="g_2551730@0" ObjectIDZND1="g_2551f00@0" ObjectIDZND2="19205@x" Pin0InfoVect0LinkObjId="g_2551730_0" Pin0InfoVect1LinkObjId="g_2551f00_0" Pin0InfoVect2LinkObjId="SW-88867_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-88868_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4314,-543 4347,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2646d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-543 4345,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2551730@0" ObjectIDND1="g_2551f00@0" ObjectIDND2="19206@x" ObjectIDZND0="19205@1" Pin0InfoVect0LinkObjId="SW-88867_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2551730_0" Pin1InfoVect1LinkObjId="g_2551f00_0" Pin1InfoVect2LinkObjId="SW-88868_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-543 4345,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_26477d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-588 4377,-573 4345,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2551730@0" ObjectIDZND0="19205@x" ObjectIDZND1="19206@x" ObjectIDZND2="g_2551f00@0" Pin0InfoVect0LinkObjId="SW-88867_0" Pin0InfoVect1LinkObjId="SW-88868_0" Pin0InfoVect2LinkObjId="g_2551f00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2551730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-588 4377,-573 4345,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2647a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-573 4345,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="19205@x" ObjectIDND1="19206@x" ObjectIDND2="g_2551730@0" ObjectIDZND0="g_2551f00@0" Pin0InfoVect0LinkObjId="g_2551f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-88867_0" Pin1InfoVect1LinkObjId="SW-88868_0" Pin1InfoVect2LinkObjId="g_2551730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-573 4345,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2647c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-620 4345,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2551f00@1" ObjectIDZND0="g_264d870@0" Pin0InfoVect0LinkObjId="g_264d870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2551f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-620 4345,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2647ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-720 3846,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="19202@x" Pin0InfoVect0LinkObjId="g_253c410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-720 3846,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27bd520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-770 3837,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="49492@0" ObjectIDZND0="19202@0" Pin0InfoVect0LinkObjId="g_253c410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-770 3837,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bd7980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-808 3837,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49492@1" ObjectIDZND0="49491@0" Pin0InfoVect0LinkObjId="g_264fa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-808 3837,-826 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_MGH"/>
</svg>