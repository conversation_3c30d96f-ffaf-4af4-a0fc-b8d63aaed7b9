<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-225" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3857 -3524 2210 1636">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="transformer:shape5_0">
    <circle cx="38" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="22" y2="29"/>
   </symbol>
   <symbol id="transformer:shape5_1">
    <circle cx="38" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape5-2">
    <circle cx="68" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="54" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="54"/>
   </symbol>
   <symbol id="transformer:shape4_0">
    <circle cx="68" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="67" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="82" x2="74" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="74" y1="37" y2="46"/>
   </symbol>
   <symbol id="transformer:shape4_1">
    <circle cx="38" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
   </symbol>
   <symbol id="transformer:shape4-2">
    <circle cx="38" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="32" y1="23" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="32" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="32" x2="32" y1="32" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape71_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,55 6,55 6,26 " stroke-width="1"/>
    <circle cx="31" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="31,12 25,25 37,25 31,12 31,13 31,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="55" y2="60"/>
   </symbol>
   <symbol id="transformer2:shape71_1">
    <circle cx="31" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="79" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="79" y2="84"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2315fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2317130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2317b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2318310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2319330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2319fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_231ab70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_231b570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dfa180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dfa180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_231e550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_231e550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23204d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23204d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_23214f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23230f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2323ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2324aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23253e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2326450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23270e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23279a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2328160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23295c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2329d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_232a6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232b070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232c4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232d0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232e0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_232ed60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_233d560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_233dd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2330f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2332530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1646" width="2220" x="3852" y="-3529"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="6065" x2="6065" y1="-2332" y2="-2318"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-155734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5128.226115 -2683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26275" ObjectName="SW-DY_ZX.DY_ZX_3901SW"/>
     <cge:Meas_Ref ObjectId="155734"/>
    <cge:TPSR_Ref TObjectID="26275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.226115 -2660.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26276" ObjectName="SW-DY_ZX.DY_ZX_39017SW"/>
     <cge:Meas_Ref ObjectId="155735"/>
    <cge:TPSR_Ref TObjectID="26276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.819337 -2792.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26248" ObjectName="SW-DY_ZX.DY_ZX_3411SW"/>
     <cge:Meas_Ref ObjectId="155592"/>
    <cge:TPSR_Ref TObjectID="26248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4283.819337 -2968.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155593">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.819337 -2919.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26249" ObjectName="SW-DY_ZX.DY_ZX_3416SW"/>
     <cge:Meas_Ref ObjectId="155593"/>
    <cge:TPSR_Ref TObjectID="26249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155594">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -2977.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26250" ObjectName="SW-DY_ZX.DY_ZX_34167SW"/>
     <cge:Meas_Ref ObjectId="155594"/>
    <cge:TPSR_Ref TObjectID="26250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155617">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.819337 -2987.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26257" ObjectName="SW-DY_ZX.DY_ZX_34267SW"/>
     <cge:Meas_Ref ObjectId="155617"/>
    <cge:TPSR_Ref TObjectID="26257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.819337 -2794.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26252" ObjectName="SW-DY_ZX.DY_ZX_3421SW"/>
     <cge:Meas_Ref ObjectId="155612"/>
    <cge:TPSR_Ref TObjectID="26252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.819337 -2942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26253" ObjectName="SW-DY_ZX.DY_ZX_3426SW"/>
     <cge:Meas_Ref ObjectId="155613"/>
    <cge:TPSR_Ref TObjectID="26253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155615">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.819337 -2844.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26255" ObjectName="SW-DY_ZX.DY_ZX_34217SW"/>
     <cge:Meas_Ref ObjectId="155615"/>
    <cge:TPSR_Ref TObjectID="26255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155616">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4623.819337 -2920.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26256" ObjectName="SW-DY_ZX.DY_ZX_34260SW"/>
     <cge:Meas_Ref ObjectId="155616"/>
    <cge:TPSR_Ref TObjectID="26256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4533.819337 -2887.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.819337 -3000.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26254" ObjectName="SW-DY_ZX.DY_ZX_3429SW"/>
     <cge:Meas_Ref ObjectId="155614"/>
    <cge:TPSR_Ref TObjectID="26254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155664">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.819337 -2790.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26259" ObjectName="SW-DY_ZX.DY_ZX_3431SW"/>
     <cge:Meas_Ref ObjectId="155664"/>
    <cge:TPSR_Ref TObjectID="26259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.819337 -2917.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26260" ObjectName="SW-DY_ZX.DY_ZX_3436SW"/>
     <cge:Meas_Ref ObjectId="155665"/>
    <cge:TPSR_Ref TObjectID="26260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.819337 -2978.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26261" ObjectName="SW-DY_ZX.DY_ZX_3439SW"/>
     <cge:Meas_Ref ObjectId="155666"/>
    <cge:TPSR_Ref TObjectID="26261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155682">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.819337 -2798.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26263" ObjectName="SW-DY_ZX.DY_ZX_3441SW"/>
     <cge:Meas_Ref ObjectId="155682"/>
    <cge:TPSR_Ref TObjectID="26263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155683">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.819337 -2925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26264" ObjectName="SW-DY_ZX.DY_ZX_3446SW"/>
     <cge:Meas_Ref ObjectId="155683"/>
    <cge:TPSR_Ref TObjectID="26264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155685">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5204.000000 -2928.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26266" ObjectName="SW-DY_ZX.DY_ZX_34497SW"/>
     <cge:Meas_Ref ObjectId="155685"/>
    <cge:TPSR_Ref TObjectID="26266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155684">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.819337 -2981.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26265" ObjectName="SW-DY_ZX.DY_ZX_3449SW"/>
     <cge:Meas_Ref ObjectId="155684"/>
    <cge:TPSR_Ref TObjectID="26265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5431.819337 -2796.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26268" ObjectName="SW-DY_ZX.DY_ZX_3451SW"/>
     <cge:Meas_Ref ObjectId="155701"/>
    <cge:TPSR_Ref TObjectID="26268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155702">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5431.819337 -2923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26269" ObjectName="SW-DY_ZX.DY_ZX_3456SW"/>
     <cge:Meas_Ref ObjectId="155702"/>
    <cge:TPSR_Ref TObjectID="26269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5369.819337 -2984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26271" ObjectName="SW-DY_ZX.DY_ZX_3459SW"/>
     <cge:Meas_Ref ObjectId="155704"/>
    <cge:TPSR_Ref TObjectID="26271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5475.000000 -2999.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26270" ObjectName="SW-DY_ZX.DY_ZX_34567SW"/>
     <cge:Meas_Ref ObjectId="155703"/>
    <cge:TPSR_Ref TObjectID="26270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5640.819337 -2801.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26273" ObjectName="SW-DY_ZX.DY_ZX_3461SW"/>
     <cge:Meas_Ref ObjectId="155720"/>
    <cge:TPSR_Ref TObjectID="26273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5640.819337 -2928.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26274" ObjectName="SW-DY_ZX.DY_ZX_3466SW"/>
     <cge:Meas_Ref ObjectId="155721"/>
    <cge:TPSR_Ref TObjectID="26274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155935">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5242.000000 -2750.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26326" ObjectName="SW-DY_ZX.DY_ZX_3121SW"/>
     <cge:Meas_Ref ObjectId="155935"/>
    <cge:TPSR_Ref TObjectID="26326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155750">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4823.241796 -2696.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26282" ObjectName="SW-DY_ZX.DY_ZX_3031SW"/>
     <cge:Meas_Ref ObjectId="155750"/>
    <cge:TPSR_Ref TObjectID="26282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -2493.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26285" ObjectName="SW-DY_ZX.DY_ZX_0036SW"/>
     <cge:Meas_Ref ObjectId="155763"/>
    <cge:TPSR_Ref TObjectID="26285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -2390.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26284" ObjectName="SW-DY_ZX.DY_ZX_0031SW"/>
     <cge:Meas_Ref ObjectId="155762"/>
    <cge:TPSR_Ref TObjectID="26284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155900">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -2297.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26318" ObjectName="SW-DY_ZX.DY_ZX_0491SW"/>
     <cge:Meas_Ref ObjectId="155900"/>
    <cge:TPSR_Ref TObjectID="26318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155902">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 -2261.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26320" ObjectName="SW-DY_ZX.DY_ZX_04917SW"/>
     <cge:Meas_Ref ObjectId="155902"/>
    <cge:TPSR_Ref TObjectID="26320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155901">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -2151.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26319" ObjectName="SW-DY_ZX.DY_ZX_0496SW"/>
     <cge:Meas_Ref ObjectId="155901"/>
    <cge:TPSR_Ref TObjectID="26319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155885">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4512.000000 -2298.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26314" ObjectName="SW-DY_ZX.DY_ZX_0481SW"/>
     <cge:Meas_Ref ObjectId="155885"/>
    <cge:TPSR_Ref TObjectID="26314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155887">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.000000 -2262.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26316" ObjectName="SW-DY_ZX.DY_ZX_04817SW"/>
     <cge:Meas_Ref ObjectId="155887"/>
    <cge:TPSR_Ref TObjectID="26316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155886">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4512.000000 -2152.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26315" ObjectName="SW-DY_ZX.DY_ZX_0486SW"/>
     <cge:Meas_Ref ObjectId="155886"/>
    <cge:TPSR_Ref TObjectID="26315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155870">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.000000 -2295.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26310" ObjectName="SW-DY_ZX.DY_ZX_0471SW"/>
     <cge:Meas_Ref ObjectId="155870"/>
    <cge:TPSR_Ref TObjectID="26310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4710.000000 -2258.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26312" ObjectName="SW-DY_ZX.DY_ZX_04717SW"/>
     <cge:Meas_Ref ObjectId="155872"/>
    <cge:TPSR_Ref TObjectID="26312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155871">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.000000 -2149.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26311" ObjectName="SW-DY_ZX.DY_ZX_0476SW"/>
     <cge:Meas_Ref ObjectId="155871"/>
    <cge:TPSR_Ref TObjectID="26311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155857">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 -2255.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26308" ObjectName="SW-DY_ZX.DY_ZX_04617SW"/>
     <cge:Meas_Ref ObjectId="155857"/>
    <cge:TPSR_Ref TObjectID="26308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155856">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.000000 -2146.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26307" ObjectName="SW-DY_ZX.DY_ZX_0466SW"/>
     <cge:Meas_Ref ObjectId="155856"/>
    <cge:TPSR_Ref TObjectID="26307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155855">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.000000 -2292.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26306" ObjectName="SW-DY_ZX.DY_ZX_0461SW"/>
     <cge:Meas_Ref ObjectId="155855"/>
    <cge:TPSR_Ref TObjectID="26306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5257.000000 -2259.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26301" ObjectName="SW-DY_ZX.DY_ZX_04417SW"/>
     <cge:Meas_Ref ObjectId="155832"/>
    <cge:TPSR_Ref TObjectID="26301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5279.000000 -2150.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26300" ObjectName="SW-DY_ZX.DY_ZX_0446SW"/>
     <cge:Meas_Ref ObjectId="155831"/>
    <cge:TPSR_Ref TObjectID="26300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155830">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5279.000000 -2296.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26299" ObjectName="SW-DY_ZX.DY_ZX_0441SW"/>
     <cge:Meas_Ref ObjectId="155830"/>
    <cge:TPSR_Ref TObjectID="26299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5488.000000 -2149.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26296" ObjectName="SW-DY_ZX.DY_ZX_0436SW"/>
     <cge:Meas_Ref ObjectId="155816"/>
    <cge:TPSR_Ref TObjectID="26296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5488.000000 -2295.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26295" ObjectName="SW-DY_ZX.DY_ZX_0431SW"/>
     <cge:Meas_Ref ObjectId="155815"/>
    <cge:TPSR_Ref TObjectID="26295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155817">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5466.000000 -2258.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26297" ObjectName="SW-DY_ZX.DY_ZX_04317SW"/>
     <cge:Meas_Ref ObjectId="155817"/>
    <cge:TPSR_Ref TObjectID="26297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155801">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5694.000000 -2143.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26292" ObjectName="SW-DY_ZX.DY_ZX_0426SW"/>
     <cge:Meas_Ref ObjectId="155801"/>
    <cge:TPSR_Ref TObjectID="26292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155800">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5694.000000 -2289.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26291" ObjectName="SW-DY_ZX.DY_ZX_0421SW"/>
     <cge:Meas_Ref ObjectId="155800"/>
    <cge:TPSR_Ref TObjectID="26291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155802">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5672.000000 -2252.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26293" ObjectName="SW-DY_ZX.DY_ZX_04217SW"/>
     <cge:Meas_Ref ObjectId="155802"/>
    <cge:TPSR_Ref TObjectID="26293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5887.000000 -2141.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26288" ObjectName="SW-DY_ZX.DY_ZX_0416SW"/>
     <cge:Meas_Ref ObjectId="155786"/>
    <cge:TPSR_Ref TObjectID="26288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155785">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5885.000000 -2287.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26287" ObjectName="SW-DY_ZX.DY_ZX_0411SW"/>
     <cge:Meas_Ref ObjectId="155785"/>
    <cge:TPSR_Ref TObjectID="26287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5863.000000 -2250.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26289" ObjectName="SW-DY_ZX.DY_ZX_04117SW"/>
     <cge:Meas_Ref ObjectId="155787"/>
    <cge:TPSR_Ref TObjectID="26289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5038.000000 -2384.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26323" ObjectName="SW-DY_ZX.DY_ZX_0122SW"/>
     <cge:Meas_Ref ObjectId="155916"/>
    <cge:TPSR_Ref TObjectID="26323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5214.000000 -2385.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26322" ObjectName="SW-DY_ZX.DY_ZX_0121SW"/>
     <cge:Meas_Ref ObjectId="155915"/>
    <cge:TPSR_Ref TObjectID="26322"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5797.000000 -2630.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5908.000000 -2630.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155845">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5768.819337 -2391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26303" ObjectName="SW-DY_ZX.DY_ZX_0451SW"/>
     <cge:Meas_Ref ObjectId="155845"/>
    <cge:TPSR_Ref TObjectID="26303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155846">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5768.819337 -2517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26304" ObjectName="SW-DY_ZX.DY_ZX_0456SW"/>
     <cge:Meas_Ref ObjectId="155846"/>
    <cge:TPSR_Ref TObjectID="26304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -2626.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -2626.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156068">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.819337 -2460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26325" ObjectName="SW-DY_ZX.DY_ZX_0501SW"/>
     <cge:Meas_Ref ObjectId="156068"/>
    <cge:TPSR_Ref TObjectID="26325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155738">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5550.000000 -2399.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26277" ObjectName="SW-DY_ZX.DY_ZX_0901SW"/>
     <cge:Meas_Ref ObjectId="155738"/>
    <cge:TPSR_Ref TObjectID="26277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155739">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5525.819337 -2467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26278" ObjectName="SW-DY_ZX.DY_ZX_09017SW"/>
     <cge:Meas_Ref ObjectId="155739"/>
    <cge:TPSR_Ref TObjectID="26278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155740">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -2403.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26279" ObjectName="SW-DY_ZX.DY_ZX_0902SW"/>
     <cge:Meas_Ref ObjectId="155740"/>
    <cge:TPSR_Ref TObjectID="26279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.819337 -2471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26280" ObjectName="SW-DY_ZX.DY_ZX_09027SW"/>
     <cge:Meas_Ref ObjectId="155741"/>
    <cge:TPSR_Ref TObjectID="26280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5061.180663 -2861.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.428571 -0.000000 -0.000000 1.217391 5101.000000 -3294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.428571 -0.000000 -0.000000 1.217391 5101.000000 -3417.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.428571 -0.000000 -0.000000 1.173913 5955.000000 -3212.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5933.000000 -3301.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_36baae0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.819337 -2922.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32dc220">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4550.000000 -3119.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b6a200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4814.000000 -2898.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e4da0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5177.000000 -2892.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_363d100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5371.000000 -2904.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac85d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5570.000000 -2529.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3596900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -2533.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b7ac30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5121.000000 -2592.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zhongbei2" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4339,-3079 4339,-3110 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34583" ObjectName="AC-35kV.LN_zhongbei2"/>
    <cge:TPSR_Ref TObjectID="34583_SS-225"/></metadata>
   <polyline fill="none" opacity="0" points="4339,-3079 4339,-3110 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zhongcangTzx" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4597,-3083 4597,-3114 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34581" ObjectName="AC-35kV.LN_zhongcangTzx"/>
    <cge:TPSR_Ref TObjectID="34581_SS-225"/></metadata>
   <polyline fill="none" opacity="0" points="4597,-3083 4597,-3114 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zhongbei1" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5650,-3083 5650,-3116 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34582" ObjectName="AC-35kV.LN_zhongbei1"/>
    <cge:TPSR_Ref TObjectID="34582_SS-225"/></metadata>
   <polyline fill="none" opacity="0" points="5650,-3083 5650,-3116 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5942,-3062 5942,-3164 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5942,-3062 5942,-3164 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dazhong1" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5089,-3069 5088,-3244 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38067" ObjectName="AC-35kV.LN_dazhong1"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5089,-3069 5088,-3244 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="DY_YLP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_liuzhongyangshi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5441,-3074 5441,-3111 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38069" ObjectName="AC-35kV.LN_liuzhongyangshi"/>
    <cge:TPSR_Ref TObjectID="38069_SS-225"/></metadata>
   <polyline fill="none" opacity="0" points="5441,-3074 5441,-3111 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zhongtianTzx" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4884,-3080 4884,-3112 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38064" ObjectName="AC-35kV.LN_zhongtianTzx"/>
    <cge:TPSR_Ref TObjectID="38064_SS-225"/></metadata>
   <polyline fill="none" opacity="0" points="4884,-3080 4884,-3112 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-DY_ZX.DY_ZX_3T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37204"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -2549.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -2549.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26324" ObjectName="TF-DY_ZX.DY_ZX_3T"/>
    <cge:TPSR_Ref TObjectID="26324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 -2784.000000)" xlink:href="#transformer2:shape71_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 -2784.000000)" xlink:href="#transformer2:shape71_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3b5d920">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5088.141266 -2578.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3afe9c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 -3026.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a6b30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -3057.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b5e350">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4786.734488 -2903.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35ff0f0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5149.734488 -2897.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b2ce20">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5343.734488 -2909.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3336400">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4298.011926 -2070.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ab4410">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4507.011926 -2071.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ba2e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4727.011926 -2068.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3348ef0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4955.011926 -2065.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b7bd30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5274.011926 -2069.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3448a80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5483.011926 -2068.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_330c610">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5689.011926 -2062.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3386e90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5880.011926 -2060.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b6fe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5773.000000 -2564.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b8daf0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5738.141266 -2561.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34468c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -2533.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac7d50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5579.000000 -2465.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d7700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5608.000000 -2468.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3596000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -2469.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3429010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.000000 -2472.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b7a330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5130.000000 -2596.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a6c600">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -2070.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a6d540">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -2070.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a6e480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -2067.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a6f3c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -2064.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a70300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5305.000000 -2068.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a71240">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5514.000000 -2066.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a72180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5720.000000 -2061.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a730c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5913.000000 -2059.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3949.000000 -3064.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-155487" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -2972.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155487" ObjectName="DY_ZX:DY_ZX_3ⅠM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-156101" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -2842.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156101" ObjectName="DY_ZX:DY_ZX_YGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-156102" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -2800.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156102" ObjectName="DY_ZX:DY_ZX_WGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226302" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -2926.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226302" ObjectName="DY_ZX:DY_ZX_XWP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226302" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4015.000000 -2887.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226302" ObjectName="DY_ZX:DY_ZX_XWP"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4339" cy="-2756" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4597" cy="-2756" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4884" cy="-2756" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="5089" cy="-2756" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="5234" cy="-2756" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26327" cx="5441" cy="-2755" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26327" cx="5650" cy="-2755" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26327" cx="5298" cy="-2755" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4832" cy="-2756" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5978" cy="-2635" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="4831" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="4339" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="4548" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="4768" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="4996" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="5074" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="4499" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26245" cx="4657" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26244" cx="5315" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26244" cx="5524" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26244" cx="5730" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26244" cx="5921" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26244" cx="5250" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26244" cx="5778" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26244" cx="5586" cy="-2374" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="5137" cy="-2756" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4288" cy="-2631" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26327" cx="5942" cy="-2755" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153542" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.500000 -3034.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26032" ObjectName="DYN-DY_ZX"/>
     <cge:Meas_Ref ObjectId="153542"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_36b2cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -3111.500000) translate(0,16)">中心变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2970.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,374)">联系方式：0878-6148253  </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_38d4730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -2532.000000) translate(0,395)">               0878-6148254</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b4ba40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.096525 -2566.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e5b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -3134.000000) translate(0,12)">中北Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c8230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4424.096525 -2858.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c5ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -3132.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33c4df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -2892.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337bd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -2890.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331fc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -3133.000000) translate(0,12)">中仓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326df20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -3130.000000) translate(0,12)">大石河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b112a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5343.000000 -2898.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b15a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5397.000000 -3131.000000) translate(0,12)">35kV六中杨石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32f47d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5613.000000 -3135.000000) translate(0,12)">中北Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3abb3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -3130.000000) translate(0,12)">35kV大中Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3374840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5810.000000 -3129.000000) translate(0,12)">35kV大中Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e0b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -2018.000000) translate(0,12)">中金线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fdd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -2014.000000) translate(0,12)">中东Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32f2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.000000 -2014.000000) translate(0,12)">中北联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331a630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4973.000000 -2011.000000) translate(0,12)">环南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32707b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5292.000000 -2012.000000) translate(0,12)">环西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3449ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5501.000000 -2010.000000) translate(0,12)">中芦线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3389e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5707.000000 -2009.000000) translate(0,12)">中东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33c1ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5898.000000 -2008.000000) translate(0,12)">中李线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f1530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5809.000000 -2661.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b53260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5917.000000 -2660.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b534a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5865.000000 -2661.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3445d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4335.000000 -2657.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3446250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4443.000000 -2656.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3446490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -2657.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34426c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5535.000000 -2572.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3434f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4606.000000 -2577.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3435460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -2617.000000) translate(0,12)">10kV中心站Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34356a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5823.000000 -2618.000000) translate(0,12)">10kV中心站Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34358e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5883.000000 -2543.000000) translate(0,12)">110kV大姚变10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4542.000000 -2396.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5646.000000 -2396.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5512.000000 -2745.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32c6ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -2750.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e98a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.000000 -2236.000000) translate(0,12)">049</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e9ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -2181.000000) translate(0,12)">0496</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e9d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -2326.000000) translate(0,12)">0491</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e9f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -2290.000000) translate(0,12)">04917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ea1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -2237.000000) translate(0,12)">048</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ea3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -2182.000000) translate(0,12)">0486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ea620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4555.000000 -2327.000000) translate(0,12)">0481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ea860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 -2291.000000) translate(0,12)">04817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eaaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.000000 -2233.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eace0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -2174.000000) translate(0,12)">0476</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eaf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4776.000000 -2325.000000) translate(0,12)">0471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eb160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -2288.000000) translate(0,12)">04717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eb3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -2229.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eb5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5004.000000 -2174.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eb820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -2322.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32eba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -2286.000000) translate(0,12)">04617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ebca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -2442.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ebee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5083.000000 -2415.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ec120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5260.000000 -2416.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ec360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5327.000000 -2234.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32dda40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5322.000000 -2183.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ddc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5323.000000 -2326.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ddec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5264.000000 -2291.000000) translate(0,12)">04417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32de100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5535.000000 -2233.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32de340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5533.000000 -2175.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32de580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5534.000000 -2325.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32de7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5468.000000 -2289.000000) translate(0,12)">04317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32dea00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5744.000000 -2228.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32dec40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5737.000000 -2170.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32dee80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5738.000000 -2318.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32df0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5676.000000 -2282.000000) translate(0,12)">04217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32df300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5931.000000 -2225.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32df540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5931.000000 -2321.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32df780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5870.000000 -2279.000000) translate(0,12)">04117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32df9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5791.000000 -2485.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32dfc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5787.000000 -2543.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32dfe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5786.000000 -2420.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e0080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5596.000000 -2425.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e02c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5480.000000 -2501.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e0500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -2433.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e0740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4551.000000 -2506.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ccb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -2496.000000) translate(0,12)">0501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ccd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4845.000000 -2470.000000) translate(0,12)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ccf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -2520.000000) translate(0,12)">0036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4842.000000 -2411.000000) translate(0,12)">0031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4848.000000 -2669.000000) translate(0,12)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4845.000000 -2725.000000) translate(0,12)">3031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cd870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5147.000000 -2712.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cdab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -2656.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cdcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5249.000000 -2781.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cdf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4358.000000 -2885.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -2946.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -2818.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4404.000000 -3006.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ce830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -2889.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cea70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -2970.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cecb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4637.000000 -3017.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ceef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -2949.000000) translate(0,12)">34260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4636.000000 -2874.000000) translate(0,12)">34217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4606.000000 -2822.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -3030.000000) translate(0,12)">3429</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cf7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.000000 -2885.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4894.000000 -2814.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f66c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4893.000000 -2945.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4832.000000 -3009.000000) translate(0,12)">3439</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -2894.000000) translate(0,12)">344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5099.000000 -2829.000000) translate(0,12)">3441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f6fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5099.000000 -2956.000000) translate(0,12)">3446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5192.000000 -3013.000000) translate(0,12)">3449</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5223.000000 -2956.000000) translate(0,12)">34497</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5456.000000 -2888.000000) translate(0,12)">345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f78c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5452.000000 -2950.000000) translate(0,12)">3456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5452.000000 -2824.000000) translate(0,12)">3451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -3013.000000) translate(0,12)">3459</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f7f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5493.000000 -3027.000000) translate(0,12)">34567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f81c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5663.000000 -2894.000000) translate(0,12)">346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5659.000000 -2957.000000) translate(0,12)">3466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5659.000000 -2830.000000) translate(0,12)">3461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5930.000000 -2171.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -2557.000000) translate(0,12)">110kV大姚变10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f99b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -2601.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b88940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5024.000000 -2883.000000) translate(0,12)">34417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b710b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 -2623.000000) translate(0,12)">SJ-3200/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b710b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 -2623.000000) translate(0,27)">35±5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b710b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 -2623.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b710b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 -2623.000000) translate(0,57)">6.815%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b71ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -2684.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3b774f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.000000 -3095.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3b779a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.000000 -3130.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a79130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.000000 -2117.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a7a7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -2127.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a7a7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -2127.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7b900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5108.000000 -3335.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7bd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -3273.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7bf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5105.000000 -3396.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5172.000000 -3486.000000) translate(0,12)">110kV大姚变1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7d5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5675.000000 -3485.000000) translate(0,12)">110kV大姚变2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7dab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5950.000000 -3264.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7dce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5957.000000 -3192.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a7df20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5949.000000 -3331.000000) translate(0,12)">3026</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-155591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -2857.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26247" ObjectName="SW-DY_ZX.DY_ZX_341BK"/>
     <cge:Meas_Ref ObjectId="155591"/>
    <cge:TPSR_Ref TObjectID="26247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155611">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -2859.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26251" ObjectName="SW-DY_ZX.DY_ZX_342BK"/>
     <cge:Meas_Ref ObjectId="155611"/>
    <cge:TPSR_Ref TObjectID="26251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155663">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -2855.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26258" ObjectName="SW-DY_ZX.DY_ZX_343BK"/>
     <cge:Meas_Ref ObjectId="155663"/>
    <cge:TPSR_Ref TObjectID="26258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155681">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.000000 -2863.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26262" ObjectName="SW-DY_ZX.DY_ZX_344BK"/>
     <cge:Meas_Ref ObjectId="155681"/>
    <cge:TPSR_Ref TObjectID="26262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5432.000000 -2861.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26267" ObjectName="SW-DY_ZX.DY_ZX_345BK"/>
     <cge:Meas_Ref ObjectId="155700"/>
    <cge:TPSR_Ref TObjectID="26267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5641.000000 -2866.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26272" ObjectName="SW-DY_ZX.DY_ZX_346BK"/>
     <cge:Meas_Ref ObjectId="155719"/>
    <cge:TPSR_Ref TObjectID="26272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4823.241796 -2641.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26281" ObjectName="SW-DY_ZX.DY_ZX_303BK"/>
     <cge:Meas_Ref ObjectId="155749"/>
    <cge:TPSR_Ref TObjectID="26281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.241796 -2443.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26283" ObjectName="SW-DY_ZX.DY_ZX_003BK"/>
     <cge:Meas_Ref ObjectId="155761"/>
    <cge:TPSR_Ref TObjectID="26283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155899">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.241796 -2206.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26317" ObjectName="SW-DY_ZX.DY_ZX_049BK"/>
     <cge:Meas_Ref ObjectId="155899"/>
    <cge:TPSR_Ref TObjectID="26317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155884">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4539.241796 -2207.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26313" ObjectName="SW-DY_ZX.DY_ZX_048BK"/>
     <cge:Meas_Ref ObjectId="155884"/>
    <cge:TPSR_Ref TObjectID="26313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155869">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.241796 -2203.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26309" ObjectName="SW-DY_ZX.DY_ZX_047BK"/>
     <cge:Meas_Ref ObjectId="155869"/>
    <cge:TPSR_Ref TObjectID="26309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155854">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.241796 -2200.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26305" ObjectName="SW-DY_ZX.DY_ZX_046BK"/>
     <cge:Meas_Ref ObjectId="155854"/>
    <cge:TPSR_Ref TObjectID="26305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155829">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5306.241796 -2204.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26298" ObjectName="SW-DY_ZX.DY_ZX_044BK"/>
     <cge:Meas_Ref ObjectId="155829"/>
    <cge:TPSR_Ref TObjectID="26298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155814">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5515.241796 -2203.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26294" ObjectName="SW-DY_ZX.DY_ZX_043BK"/>
     <cge:Meas_Ref ObjectId="155814"/>
    <cge:TPSR_Ref TObjectID="26294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155799">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5721.241796 -2197.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26290" ObjectName="SW-DY_ZX.DY_ZX_042BK"/>
     <cge:Meas_Ref ObjectId="155799"/>
    <cge:TPSR_Ref TObjectID="26290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5912.241796 -2195.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26286" ObjectName="SW-DY_ZX.DY_ZX_041BK"/>
     <cge:Meas_Ref ObjectId="155784"/>
    <cge:TPSR_Ref TObjectID="26286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 -2445.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26321" ObjectName="SW-DY_ZX.DY_ZX_012BK"/>
     <cge:Meas_Ref ObjectId="155914"/>
    <cge:TPSR_Ref TObjectID="26321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5853.000000 -2625.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155844">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5769.000000 -2456.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26302" ObjectName="SW-DY_ZX.DY_ZX_045BK"/>
     <cge:Meas_Ref ObjectId="155844"/>
    <cge:TPSR_Ref TObjectID="26302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -2621.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.000000 -3306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.497278 5933.142857 -3223.559891)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_ZX.DY_ZX_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-2756 5235,-2756 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26243" ObjectName="BS-DY_ZX.DY_ZX_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   <polyline fill="none" opacity="0" points="4276,-2756 5235,-2756 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ZX.DY_ZX_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5296,-2755 5986,-2755 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26327" ObjectName="BS-DY_ZX.DY_ZX_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="26327"/></metadata>
   <polyline fill="none" opacity="0" points="5296,-2755 5986,-2755 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5978,-2714 5978,-2553 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5978,-2714 5978,-2553 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ZX.DY_ZX_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-2374 5108,-2374 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26245" ObjectName="BS-DY_ZX.DY_ZX_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   <polyline fill="none" opacity="0" points="4275,-2374 5108,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ZX.DY_ZX_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5987,-2374 5217,-2374 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26244" ObjectName="BS-DY_ZX.DY_ZX_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   <polyline fill="none" opacity="0" points="5987,-2374 5217,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4288,-2704 4288,-2565 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4288,-2704 4288,-2565 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3b50900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5212.226115 -2659.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_335fa80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -2948.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b4acc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.819337 -2986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33faf80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.819337 -2843.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34362d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.819337 -2919.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ff0d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5206.000000 -2898.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3949190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5479.000000 -2969.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32cf860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 -2260.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3343fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4466.000000 -2261.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35fe550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4686.000000 -2258.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aa9260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 -2255.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c2670" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 -2259.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_333ed00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.000000 -2258.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32fa1d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5648.000000 -2252.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_340e720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5839.000000 -2250.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3441770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5529.000000 -2521.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34341a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 -2525.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b879f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5010.180663 -2862.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="4001" y="-3122"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="4001" y="-3122"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3952" y="-3139"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3952" y="-3139"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4351" y="-2236"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4351" y="-2236"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4559" y="-2237"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4559" y="-2237"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4780" y="-2233"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4780" y="-2233"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5008" y="-2229"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5008" y="-2229"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5327" y="-2234"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5327" y="-2234"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5535" y="-2233"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5535" y="-2233"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5744" y="-2228"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5744" y="-2228"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5931" y="-2225"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5931" y="-2225"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="4749" y="-2602"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="4749" y="-2602"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4358" y="-2885"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4358" y="-2885"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4899" y="-2885"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4899" y="-2885"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5456" y="-2888"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5456" y="-2888"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5103" y="-2894"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5103" y="-2894"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5663" y="-2894"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5663" y="-2894"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4611" y="-2889"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4611" y="-2889"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="81" x="3904" y="-2685"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="81" x="3904" y="-2685"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="5144" y="-2442"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="5144" y="-2442"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5791" y="-2485"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5791" y="-2485"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="4151" y="-3103"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="4151" y="-3103"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="4151" y="-3138"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="4151" y="-3138"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b61950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 1957.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b61f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 1944.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b62170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 1927.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b623b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4304.500000 1911.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b628b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 1987.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b62b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.500000 1972.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b631d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 2471.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b633e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 2456.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b635e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 2501.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b63820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 2426.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b63a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 2411.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b63ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 2396.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b63ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 2486.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b64120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 2441.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8a0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5325.000000 2469.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8a300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5331.000000 2454.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8a540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5325.000000 2499.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8a780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5317.000000 2424.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8a9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5317.000000 2409.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8ac00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5333.000000 2394.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8ae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5325.000000 2484.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8b080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5317.000000 2439.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8ba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4613.000000 2718.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8bc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 2703.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8bed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4613.000000 2748.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8c110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 2673.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8c350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 2658.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8c590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 2643.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8c7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4613.000000 2733.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8ca10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 2688.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8cd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 3208.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8cfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 3195.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3596eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 3178.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35970f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.500000 3162.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3597330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 3238.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3597570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.500000 3223.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_359a370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 2709.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_359a600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 2696.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_359a840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 2679.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_359aa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.500000 2663.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_359acc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4892.000000 2739.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_359af00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.500000 2724.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d17b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 2475.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 2462.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 2445.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.500000 2429.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d20c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4892.000000 2505.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.500000 2490.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac3b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.000000 2498.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac3da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.000000 2481.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac3fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.000000 2511.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac4910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5850.000000 2448.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac4ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5850.000000 2435.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac4de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5850.000000 2418.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac5020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5848.500000 2402.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac5260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5836.000000 2478.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac54a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5824.500000 2463.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac6460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 1956.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac66e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 1943.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac6920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 1926.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac6b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.500000 1910.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac6da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.000000 1986.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac6fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4492.500000 1971.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac7310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 1955.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac7590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 1942.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac77d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 1925.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3358040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.500000 1909.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3358280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4728.000000 1985.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33584c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4716.500000 1970.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33587f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 1953.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3358a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 1940.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3358cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 1923.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3358ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.500000 1907.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 1983.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4939.500000 1968.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33596a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5286.000000 1951.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5286.000000 1938.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5286.000000 1921.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5284.500000 1905.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5272.000000 1981.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335a220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5260.500000 1966.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335a550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5499.000000 1950.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335a7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5499.000000 1937.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335aa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5499.000000 1920.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335ac50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5497.500000 1904.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335ae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5485.000000 1980.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335b0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5473.500000 1965.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335b400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5706.000000 1953.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335b680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5706.000000 1940.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335b8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5706.000000 1923.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335bb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5704.500000 1907.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335bd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5692.000000 1983.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335bf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5680.500000 1968.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335c2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5888.000000 1957.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335c530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5888.000000 1944.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b84980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5888.000000 1927.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b84b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5886.500000 1911.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b84dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5874.000000 1987.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b85010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5862.500000 1972.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b72840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 3204.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b72d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 3191.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b72f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 3174.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b731b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.500000 3158.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b733f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 3234.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b73630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4512.500000 3219.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b73960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 3204.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b73be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 3191.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b73e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 3174.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b74060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.500000 3158.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b742a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4818.000000 3234.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b744e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.500000 3219.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b74810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4997.000000 3206.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b74a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4997.000000 3193.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b74cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4997.000000 3176.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b74f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.500000 3160.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b75150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 3236.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b75390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4971.500000 3221.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b756c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5389.000000 3206.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b75940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5389.000000 3193.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b75b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5389.000000 3176.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b75dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5387.500000 3160.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b76000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5375.000000 3236.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b76240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5363.500000 3221.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b76570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5598.000000 3204.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b767f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5598.000000 3191.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b76a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5598.000000 3174.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b76c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5596.500000 3158.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b76eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5584.000000 3234.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b770f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5572.500000 3219.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.049Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.096774 -2025.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34261" ObjectName="EC-DY_ZX.049Ld"/>
    <cge:TPSR_Ref TObjectID="34261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.048Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4539.096774 -2023.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34260" ObjectName="EC-DY_ZX.048Ld"/>
    <cge:TPSR_Ref TObjectID="34260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.047Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.096774 -2020.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34259" ObjectName="EC-DY_ZX.047Ld"/>
    <cge:TPSR_Ref TObjectID="34259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.046Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.096774 -2019.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34258" ObjectName="EC-DY_ZX.046Ld"/>
    <cge:TPSR_Ref TObjectID="34258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5306.096774 -2015.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34257" ObjectName="EC-DY_ZX.044Ld"/>
    <cge:TPSR_Ref TObjectID="34257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5515.096774 -2015.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34256" ObjectName="EC-DY_ZX.043Ld"/>
    <cge:TPSR_Ref TObjectID="34256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5721.096774 -2011.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34255" ObjectName="EC-DY_ZX.042Ld"/>
    <cge:TPSR_Ref TObjectID="34255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ZX.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5914.096774 -2011.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34254" ObjectName="EC-DY_ZX.041Ld"/>
    <cge:TPSR_Ref TObjectID="34254"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-155496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-155497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-155498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-155502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-155499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-155500" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155500" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-155501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-155503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -2500.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26245"/>
     <cge:Term_Ref ObjectID="37045"/>
    <cge:TPSR_Ref TObjectID="26245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-155488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-155489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-155490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-155494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-155491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-155492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-155493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-155495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5386.000000 -2499.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26244"/>
     <cge:Term_Ref ObjectID="37044"/>
    <cge:TPSR_Ref TObjectID="26244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-155480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-155481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-155482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-155486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-155483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-155484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-155485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-155487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -2749.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    <cge:TPSR_Ref TObjectID="26243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -3234.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    <cge:TPSR_Ref TObjectID="26247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -3234.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    <cge:TPSR_Ref TObjectID="26247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -3234.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    <cge:TPSR_Ref TObjectID="26247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -3234.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    <cge:TPSR_Ref TObjectID="26247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -3234.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    <cge:TPSR_Ref TObjectID="26247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -3234.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    <cge:TPSR_Ref TObjectID="26247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -3232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    <cge:TPSR_Ref TObjectID="26251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -3232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    <cge:TPSR_Ref TObjectID="26251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -3232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    <cge:TPSR_Ref TObjectID="26251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -3232.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    <cge:TPSR_Ref TObjectID="26251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -3232.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    <cge:TPSR_Ref TObjectID="26251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -3232.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    <cge:TPSR_Ref TObjectID="26251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -3232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    <cge:TPSR_Ref TObjectID="26258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -3232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    <cge:TPSR_Ref TObjectID="26258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -3232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    <cge:TPSR_Ref TObjectID="26258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -3232.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    <cge:TPSR_Ref TObjectID="26258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -3232.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    <cge:TPSR_Ref TObjectID="26258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -3232.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    <cge:TPSR_Ref TObjectID="26258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -3234.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155465" ObjectName="DY_ZX.DY_ZX_344BK:F"/>
     <cge:PSR_Ref ObjectID="26262"/>
     <cge:Term_Ref ObjectID="37078"/>
    <cge:TPSR_Ref TObjectID="26262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -3234.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155466" ObjectName="DY_ZX.DY_ZX_344BK:F"/>
     <cge:PSR_Ref ObjectID="26262"/>
     <cge:Term_Ref ObjectID="37078"/>
    <cge:TPSR_Ref TObjectID="26262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -3234.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155462" ObjectName="DY_ZX.DY_ZX_344BK:F"/>
     <cge:PSR_Ref ObjectID="26262"/>
     <cge:Term_Ref ObjectID="37078"/>
    <cge:TPSR_Ref TObjectID="26262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -3234.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155463" ObjectName="DY_ZX.DY_ZX_344BK:F"/>
     <cge:PSR_Ref ObjectID="26262"/>
     <cge:Term_Ref ObjectID="37078"/>
    <cge:TPSR_Ref TObjectID="26262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -3234.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155464" ObjectName="DY_ZX.DY_ZX_344BK:F"/>
     <cge:PSR_Ref ObjectID="26262"/>
     <cge:Term_Ref ObjectID="37078"/>
    <cge:TPSR_Ref TObjectID="26262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -3234.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155467" ObjectName="DY_ZX.DY_ZX_344BK:F"/>
     <cge:PSR_Ref ObjectID="26262"/>
     <cge:Term_Ref ObjectID="37078"/>
    <cge:TPSR_Ref TObjectID="26262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5429.000000 -3234.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    <cge:TPSR_Ref TObjectID="26267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5429.000000 -3234.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    <cge:TPSR_Ref TObjectID="26267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5429.000000 -3234.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    <cge:TPSR_Ref TObjectID="26267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5429.000000 -3234.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    <cge:TPSR_Ref TObjectID="26267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5429.000000 -3234.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    <cge:TPSR_Ref TObjectID="26267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5429.000000 -3234.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    <cge:TPSR_Ref TObjectID="26267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5638.000000 -3233.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    <cge:TPSR_Ref TObjectID="26272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5638.000000 -3233.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    <cge:TPSR_Ref TObjectID="26272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5638.000000 -3233.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    <cge:TPSR_Ref TObjectID="26272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5638.000000 -3233.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    <cge:TPSR_Ref TObjectID="26272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5638.000000 -3233.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    <cge:TPSR_Ref TObjectID="26272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5638.000000 -3233.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    <cge:TPSR_Ref TObjectID="26272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -2739.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26281"/>
     <cge:Term_Ref ObjectID="37116"/>
    <cge:TPSR_Ref TObjectID="26281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -2739.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26281"/>
     <cge:Term_Ref ObjectID="37116"/>
    <cge:TPSR_Ref TObjectID="26281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -2739.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26281"/>
     <cge:Term_Ref ObjectID="37116"/>
    <cge:TPSR_Ref TObjectID="26281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155505" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -2739.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155505" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26281"/>
     <cge:Term_Ref ObjectID="37116"/>
    <cge:TPSR_Ref TObjectID="26281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -2739.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26281"/>
     <cge:Term_Ref ObjectID="37116"/>
    <cge:TPSR_Ref TObjectID="26281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -2739.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26281"/>
     <cge:Term_Ref ObjectID="37116"/>
    <cge:TPSR_Ref TObjectID="26281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -2503.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26283"/>
     <cge:Term_Ref ObjectID="37120"/>
    <cge:TPSR_Ref TObjectID="26283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -2503.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26283"/>
     <cge:Term_Ref ObjectID="37120"/>
    <cge:TPSR_Ref TObjectID="26283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155510" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -2503.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26283"/>
     <cge:Term_Ref ObjectID="37120"/>
    <cge:TPSR_Ref TObjectID="26283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -2503.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26283"/>
     <cge:Term_Ref ObjectID="37120"/>
    <cge:TPSR_Ref TObjectID="26283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -2503.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26283"/>
     <cge:Term_Ref ObjectID="37120"/>
    <cge:TPSR_Ref TObjectID="26283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -2503.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26283"/>
     <cge:Term_Ref ObjectID="37120"/>
    <cge:TPSR_Ref TObjectID="26283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1986.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26317"/>
     <cge:Term_Ref ObjectID="37188"/>
    <cge:TPSR_Ref TObjectID="26317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1986.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26317"/>
     <cge:Term_Ref ObjectID="37188"/>
    <cge:TPSR_Ref TObjectID="26317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1986.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26317"/>
     <cge:Term_Ref ObjectID="37188"/>
    <cge:TPSR_Ref TObjectID="26317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1986.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26317"/>
     <cge:Term_Ref ObjectID="37188"/>
    <cge:TPSR_Ref TObjectID="26317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1986.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26317"/>
     <cge:Term_Ref ObjectID="37188"/>
    <cge:TPSR_Ref TObjectID="26317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -1986.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26317"/>
     <cge:Term_Ref ObjectID="37188"/>
    <cge:TPSR_Ref TObjectID="26317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -1986.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26313"/>
     <cge:Term_Ref ObjectID="37180"/>
    <cge:TPSR_Ref TObjectID="26313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -1986.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26313"/>
     <cge:Term_Ref ObjectID="37180"/>
    <cge:TPSR_Ref TObjectID="26313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -1986.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26313"/>
     <cge:Term_Ref ObjectID="37180"/>
    <cge:TPSR_Ref TObjectID="26313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -1986.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26313"/>
     <cge:Term_Ref ObjectID="37180"/>
    <cge:TPSR_Ref TObjectID="26313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -1986.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26313"/>
     <cge:Term_Ref ObjectID="37180"/>
    <cge:TPSR_Ref TObjectID="26313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -1986.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26313"/>
     <cge:Term_Ref ObjectID="37180"/>
    <cge:TPSR_Ref TObjectID="26313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -1986.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26309"/>
     <cge:Term_Ref ObjectID="37172"/>
    <cge:TPSR_Ref TObjectID="26309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -1986.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26309"/>
     <cge:Term_Ref ObjectID="37172"/>
    <cge:TPSR_Ref TObjectID="26309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -1986.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26309"/>
     <cge:Term_Ref ObjectID="37172"/>
    <cge:TPSR_Ref TObjectID="26309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -1986.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26309"/>
     <cge:Term_Ref ObjectID="37172"/>
    <cge:TPSR_Ref TObjectID="26309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -1986.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26309"/>
     <cge:Term_Ref ObjectID="37172"/>
    <cge:TPSR_Ref TObjectID="26309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -1986.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26309"/>
     <cge:Term_Ref ObjectID="37172"/>
    <cge:TPSR_Ref TObjectID="26309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5012.000000 -1985.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26305"/>
     <cge:Term_Ref ObjectID="37164"/>
    <cge:TPSR_Ref TObjectID="26305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5012.000000 -1985.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26305"/>
     <cge:Term_Ref ObjectID="37164"/>
    <cge:TPSR_Ref TObjectID="26305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5012.000000 -1985.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26305"/>
     <cge:Term_Ref ObjectID="37164"/>
    <cge:TPSR_Ref TObjectID="26305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5012.000000 -1985.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26305"/>
     <cge:Term_Ref ObjectID="37164"/>
    <cge:TPSR_Ref TObjectID="26305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5012.000000 -1985.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26305"/>
     <cge:Term_Ref ObjectID="37164"/>
    <cge:TPSR_Ref TObjectID="26305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5012.000000 -1985.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26305"/>
     <cge:Term_Ref ObjectID="37164"/>
    <cge:TPSR_Ref TObjectID="26305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1978.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26298"/>
     <cge:Term_Ref ObjectID="37150"/>
    <cge:TPSR_Ref TObjectID="26298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155540" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1978.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26298"/>
     <cge:Term_Ref ObjectID="37150"/>
    <cge:TPSR_Ref TObjectID="26298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1978.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26298"/>
     <cge:Term_Ref ObjectID="37150"/>
    <cge:TPSR_Ref TObjectID="26298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155537" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1978.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26298"/>
     <cge:Term_Ref ObjectID="37150"/>
    <cge:TPSR_Ref TObjectID="26298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1978.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26298"/>
     <cge:Term_Ref ObjectID="37150"/>
    <cge:TPSR_Ref TObjectID="26298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1978.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26298"/>
     <cge:Term_Ref ObjectID="37150"/>
    <cge:TPSR_Ref TObjectID="26298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1978.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26294"/>
     <cge:Term_Ref ObjectID="37142"/>
    <cge:TPSR_Ref TObjectID="26294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1978.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26294"/>
     <cge:Term_Ref ObjectID="37142"/>
    <cge:TPSR_Ref TObjectID="26294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1978.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26294"/>
     <cge:Term_Ref ObjectID="37142"/>
    <cge:TPSR_Ref TObjectID="26294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1978.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26294"/>
     <cge:Term_Ref ObjectID="37142"/>
    <cge:TPSR_Ref TObjectID="26294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1978.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26294"/>
     <cge:Term_Ref ObjectID="37142"/>
    <cge:TPSR_Ref TObjectID="26294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1978.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26294"/>
     <cge:Term_Ref ObjectID="37142"/>
    <cge:TPSR_Ref TObjectID="26294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5750.000000 -1984.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26290"/>
     <cge:Term_Ref ObjectID="37134"/>
    <cge:TPSR_Ref TObjectID="26290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5750.000000 -1984.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26290"/>
     <cge:Term_Ref ObjectID="37134"/>
    <cge:TPSR_Ref TObjectID="26290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5750.000000 -1984.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26290"/>
     <cge:Term_Ref ObjectID="37134"/>
    <cge:TPSR_Ref TObjectID="26290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5750.000000 -1984.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26290"/>
     <cge:Term_Ref ObjectID="37134"/>
    <cge:TPSR_Ref TObjectID="26290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5750.000000 -1984.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26290"/>
     <cge:Term_Ref ObjectID="37134"/>
    <cge:TPSR_Ref TObjectID="26290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5750.000000 -1984.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26290"/>
     <cge:Term_Ref ObjectID="37134"/>
    <cge:TPSR_Ref TObjectID="26290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5932.000000 -1984.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26286"/>
     <cge:Term_Ref ObjectID="37126"/>
    <cge:TPSR_Ref TObjectID="26286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155522" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5932.000000 -1984.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26286"/>
     <cge:Term_Ref ObjectID="37126"/>
    <cge:TPSR_Ref TObjectID="26286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5932.000000 -1984.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26286"/>
     <cge:Term_Ref ObjectID="37126"/>
    <cge:TPSR_Ref TObjectID="26286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155519" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5932.000000 -1984.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26286"/>
     <cge:Term_Ref ObjectID="37126"/>
    <cge:TPSR_Ref TObjectID="26286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5932.000000 -1984.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26286"/>
     <cge:Term_Ref ObjectID="37126"/>
    <cge:TPSR_Ref TObjectID="26286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5932.000000 -1984.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26286"/>
     <cge:Term_Ref ObjectID="37126"/>
    <cge:TPSR_Ref TObjectID="26286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -2510.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26321"/>
     <cge:Term_Ref ObjectID="37196"/>
    <cge:TPSR_Ref TObjectID="26321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -2510.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26321"/>
     <cge:Term_Ref ObjectID="37196"/>
    <cge:TPSR_Ref TObjectID="26321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -2510.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26321"/>
     <cge:Term_Ref ObjectID="37196"/>
    <cge:TPSR_Ref TObjectID="26321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5895.000000 -2477.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26302"/>
     <cge:Term_Ref ObjectID="37158"/>
    <cge:TPSR_Ref TObjectID="26302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5895.000000 -2477.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26302"/>
     <cge:Term_Ref ObjectID="37158"/>
    <cge:TPSR_Ref TObjectID="26302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5895.000000 -2477.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26302"/>
     <cge:Term_Ref ObjectID="37158"/>
    <cge:TPSR_Ref TObjectID="26302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-155543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5895.000000 -2477.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26302"/>
     <cge:Term_Ref ObjectID="37158"/>
    <cge:TPSR_Ref TObjectID="26302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-155544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5895.000000 -2477.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26302"/>
     <cge:Term_Ref ObjectID="37158"/>
    <cge:TPSR_Ref TObjectID="26302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-155547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5895.000000 -2477.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26302"/>
     <cge:Term_Ref ObjectID="37158"/>
    <cge:TPSR_Ref TObjectID="26302"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5051.500000 -3432.500000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5051.500000 -3432.500000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5051.500000 -3432.500000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5825.500000 -3428.500000)" xlink:href="#transformer:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5825.500000 -3428.500000)" xlink:href="#transformer:shape4_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5825.500000 -3428.500000)" xlink:href="#transformer:shape4-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="4001" y="-3122"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3952" y="-3139"/></g>
   <g href="35kV中心变DY_ZX_049间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4351" y="-2236"/></g>
   <g href="35kV中心变DY_ZX_048间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4559" y="-2237"/></g>
   <g href="35kV中心变DY_ZX_047间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4780" y="-2233"/></g>
   <g href="35kV中心变DY_ZX_046间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5008" y="-2229"/></g>
   <g href="35kV中心变DY_ZX_044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5327" y="-2234"/></g>
   <g href="35kV中心变DY_ZX_043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5535" y="-2233"/></g>
   <g href="35kV中心变DY_ZX_042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5744" y="-2228"/></g>
   <g href="35kV中心变DY_ZX_041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5931" y="-2225"/></g>
   <g href="35kV中心变3号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="4749" y="-2602"/></g>
   <g href="35kV中心变DY_ZX_341间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4358" y="-2885"/></g>
   <g href="35kV中心变DY_ZX_343间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4899" y="-2885"/></g>
   <g href="35kV中心变DY_ZX_345间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5456" y="-2888"/></g>
   <g href="35kV中心变DY_ZX_344间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5103" y="-2894"/></g>
   <g href="35kV中心变DY_ZX_346间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5663" y="-2894"/></g>
   <g href="35kV中心变DY_ZX_342间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4611" y="-2889"/></g>
   <g href="35kV中心变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="81" x="3904" y="-2685"/></g>
   <g href="35kV中心变DY_ZX_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="5144" y="-2442"/></g>
   <g href="35kV中心变DY_ZX_045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5791" y="-2485"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="4151" y="-3103"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="4151" y="-3138"/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_33f28f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5137,-2756 5137,-2724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26243@0" ObjectIDZND0="26275@1" Pin0InfoVect0LinkObjId="SW-155734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_360c890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5137,-2756 5137,-2724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5aad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5095,-2632 5095,-2653 5137,-2653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b5d920@0" ObjectIDZND0="g_3b7a330@0" ObjectIDZND1="26275@x" ObjectIDZND2="26276@x" Pin0InfoVect0LinkObjId="g_3b7a330_0" Pin0InfoVect1LinkObjId="SW-155734_0" Pin0InfoVect2LinkObjId="SW-155735_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b5d920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5095,-2632 5095,-2653 5137,-2653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3abb9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5137,-2653 5137,-2645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3b5d920@0" ObjectIDND1="26275@x" ObjectIDND2="26276@x" ObjectIDZND0="g_3b7a330@1" Pin0InfoVect0LinkObjId="g_3b7a330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b5d920_0" Pin1InfoVect1LinkObjId="SW-155734_0" Pin1InfoVect2LinkObjId="SW-155735_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5137,-2653 5137,-2645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3abb7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-2665 5167,-2665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26275@x" ObjectIDND1="g_3b7a330@0" ObjectIDND2="g_3b5d920@0" ObjectIDZND0="26276@1" Pin0InfoVect0LinkObjId="SW-155735_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155734_0" Pin1InfoVect1LinkObjId="g_3b7a330_0" Pin1InfoVect2LinkObjId="g_3b5d920_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-2665 5167,-2665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b4d540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5203,-2665 5217,-2665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26276@0" ObjectIDZND0="g_3b50900@0" Pin0InfoVect0LinkObjId="g_3b50900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5203,-2665 5217,-2665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b4d300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5137,-2688 5137,-2665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26275@0" ObjectIDZND0="26276@x" ObjectIDZND1="g_3b7a330@0" ObjectIDZND2="g_3b5d920@0" Pin0InfoVect0LinkObjId="SW-155735_0" Pin0InfoVect1LinkObjId="g_3b7a330_0" Pin0InfoVect2LinkObjId="g_3b5d920_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5137,-2688 5137,-2665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b4c340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5137,-2665 5137,-2653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26275@x" ObjectIDND1="26276@x" ObjectIDZND0="g_3b7a330@0" ObjectIDZND1="g_3b5d920@0" Pin0InfoVect0LinkObjId="g_3b7a330_0" Pin0InfoVect1LinkObjId="g_3b5d920_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155734_0" Pin1InfoVect1LinkObjId="SW-155735_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5137,-2665 5137,-2653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2797 4339,-2756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26248@0" ObjectIDZND0="26243@0" Pin0InfoVect0LinkObjId="g_35ea520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155592_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2797 4339,-2756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36bb0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2865 4339,-2833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26247@0" ObjectIDZND0="26248@1" Pin0InfoVect0LinkObjId="SW-155592_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2865 4339,-2833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_394b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2924 4339,-2892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26249@0" ObjectIDZND0="26247@1" Pin0InfoVect0LinkObjId="SW-155591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155593_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2924 4339,-2892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b4ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-2973 4289,-2953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_36baae0@0" Pin0InfoVect0LinkObjId="g_36baae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-2973 4289,-2953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33773f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-2981 4395,-2966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26250@1" ObjectIDZND0="g_335fa80@0" Pin0InfoVect0LinkObjId="g_335fa80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155594_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-2981 4395,-2966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ac1580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-3018 4339,-3018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="0@1" ObjectIDZND0="26249@x" ObjectIDZND1="26250@x" ObjectIDZND2="34583@1" Pin0InfoVect0LinkObjId="SW-155593_0" Pin0InfoVect1LinkObjId="SW-155594_0" Pin0InfoVect2LinkObjId="g_330cd20_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-3018 4339,-3018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a8690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2960 4339,-3018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26249@1" ObjectIDZND0="0@x" ObjectIDZND1="26250@x" ObjectIDZND2="34583@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-155594_0" Pin0InfoVect2LinkObjId="g_330cd20_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155593_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2960 4339,-3018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_330cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-3018 4339,-3082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="26249@x" ObjectIDND1="0@x" ObjectIDND2="26250@x" ObjectIDZND0="34583@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155593_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-155594_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-3018 4339,-3082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3277df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-3018 4395,-3018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="26249@x" ObjectIDND1="0@x" ObjectIDND2="34583@1" ObjectIDZND0="26250@0" Pin0InfoVect0LinkObjId="SW-155594_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155593_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_330cd20_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-3018 4395,-3018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35e8c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-3018 4289,-3030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3afe9c0@0" Pin0InfoVect0LinkObjId="g_3afe9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-3018 4289,-3030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e6ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-2849 4598,-2849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26255@1" ObjectIDZND0="26251@x" ObjectIDZND1="26252@x" Pin0InfoVect0LinkObjId="SW-155611_0" Pin0InfoVect1LinkObjId="SW-155612_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155615_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-2849 4598,-2849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-2992 4678,-2992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26257@0" ObjectIDZND0="g_3b4acc0@0" Pin0InfoVect0LinkObjId="g_3b4acc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155617_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-2992 4678,-2992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ea520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2799 4597,-2756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26252@0" ObjectIDZND0="26243@0" Pin0InfoVect0LinkObjId="g_360c890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2799 4597,-2756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333e060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2867 4597,-2849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26251@0" ObjectIDZND0="26255@x" ObjectIDZND1="26252@x" Pin0InfoVect0LinkObjId="SW-155615_0" Pin0InfoVect1LinkObjId="SW-155612_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155611_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2867 4597,-2849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d6100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2849 4597,-2835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26251@x" ObjectIDND1="26255@x" ObjectIDZND0="26252@1" Pin0InfoVect0LinkObjId="SW-155612_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155611_0" Pin1InfoVect1LinkObjId="SW-155615_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2849 4597,-2835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cbea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4667,-2849 4681,-2849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26255@0" ObjectIDZND0="g_33faf80@0" Pin0InfoVect0LinkObjId="g_33faf80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4667,-2849 4681,-2849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b914a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-2925 4596,-2925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26256@1" ObjectIDZND0="26251@x" ObjectIDZND1="0@x" ObjectIDZND2="26253@x" Pin0InfoVect0LinkObjId="SW-155611_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-155613_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155616_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-2925 4596,-2925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32f9e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-2925 4679,-2925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26256@0" ObjectIDZND0="g_34362d0@0" Pin0InfoVect0LinkObjId="g_34362d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-2925 4679,-2925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3186ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2894 4597,-2925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26251@1" ObjectIDZND0="26256@x" ObjectIDZND1="0@x" ObjectIDZND2="26253@x" Pin0InfoVect0LinkObjId="SW-155616_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-155613_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155611_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2894 4597,-2925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3187380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-2892 4539,-2878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-2892 4539,-2878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f794d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2983 4597,-3084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="26253@1" ObjectIDZND0="34581@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2983 4597,-3084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3397250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4628,-2992 4542,-2992 4542,-3005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="26257@1" ObjectIDZND0="26254@0" Pin0InfoVect0LinkObjId="SW-155614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4628,-2992 4542,-2992 4542,-3005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d2a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4542,-3041 4542,-3052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="26254@1" ObjectIDZND0="g_32dc220@0" Pin0InfoVect0LinkObjId="g_32dc220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155614_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4542,-3041 4542,-3052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b5890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4501,-3061 4501,-3047 4542,-3047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_33a6b30@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a6b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4501,-3061 4501,-3047 4542,-3047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c41b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-2795 4884,-2756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26259@0" ObjectIDZND0="26243@0" Pin0InfoVect0LinkObjId="g_360c890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-2795 4884,-2756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cd630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-2922 4884,-2890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26260@0" ObjectIDZND0="26258@1" Pin0InfoVect0LinkObjId="SW-155663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-2922 4884,-2890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3425450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-2958 4884,-3034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="26260@1" ObjectIDZND0="26261@x" ObjectIDZND1="38064@1" Pin0InfoVect0LinkObjId="SW-155666_0" Pin0InfoVect1LinkObjId="g_334dc80_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-2958 4884,-3034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-3034 4884,-3081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="26260@x" ObjectIDND1="26261@x" ObjectIDZND0="38064@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155665_0" Pin1InfoVect1LinkObjId="SW-155666_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-3034 4884,-3081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6b0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-3019 4822,-3034 4884,-3034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="26261@1" ObjectIDZND0="26260@x" ObjectIDZND1="38064@1" Pin0InfoVect0LinkObjId="SW-155665_0" Pin0InfoVect1LinkObjId="g_334dc80_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-3019 4822,-3034 4884,-3034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5a880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-2957 4794,-2975 4822,-2975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_3b5e350@0" ObjectIDZND0="26261@x" ObjectIDZND1="g_3b6a200@0" Pin0InfoVect0LinkObjId="SW-155666_0" Pin0InfoVect1LinkObjId="g_3b6a200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b5e350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-2957 4794,-2975 4822,-2975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ab13f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-2983 4822,-2975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="26261@0" ObjectIDZND0="g_3b5e350@0" ObjectIDZND1="g_3b6a200@0" Pin0InfoVect0LinkObjId="g_3b5e350_0" Pin0InfoVect1LinkObjId="g_3b6a200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-2983 4822,-2975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a7180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-2975 4822,-2964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="26261@x" ObjectIDND1="g_3b5e350@0" ObjectIDZND0="g_3b6a200@0" Pin0InfoVect0LinkObjId="g_3b6a200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155666_0" Pin1InfoVect1LinkObjId="g_3b5e350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-2975 4822,-2964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338d650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-2803 5089,-2756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26263@0" ObjectIDZND0="26243@0" Pin0InfoVect0LinkObjId="g_360c890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-2803 5089,-2756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ab4bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-2930 5089,-2898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26264@0" ObjectIDZND0="26262@1" Pin0InfoVect0LinkObjId="SW-155681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-2930 5089,-2898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32eff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5157,-2951 5157,-2969 5185,-2969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_35ff0f0@0" ObjectIDZND0="g_35e4da0@0" ObjectIDZND1="26265@x" ObjectIDZND2="26266@x" Pin0InfoVect0LinkObjId="g_35e4da0_0" Pin0InfoVect1LinkObjId="SW-155684_0" Pin0InfoVect2LinkObjId="SW-155685_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35ff0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5157,-2951 5157,-2969 5185,-2969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cb080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5185,-2969 5185,-2958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_35ff0f0@0" ObjectIDND1="26265@x" ObjectIDND2="26266@x" ObjectIDZND0="g_35e4da0@0" Pin0InfoVect0LinkObjId="g_35e4da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35ff0f0_0" Pin1InfoVect1LinkObjId="SW-155684_0" Pin1InfoVect2LinkObjId="SW-155685_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5185,-2969 5185,-2958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b59a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5212,-2931 5212,-2916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26266@1" ObjectIDZND0="g_33ff0d0@0" Pin0InfoVect0LinkObjId="g_33ff0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5212,-2931 5212,-2916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338eba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5185,-2969 5213,-2969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_35e4da0@0" ObjectIDND1="g_35ff0f0@0" ObjectIDND2="26265@x" ObjectIDZND0="26266@0" Pin0InfoVect0LinkObjId="SW-155685_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35e4da0_0" Pin1InfoVect1LinkObjId="g_35ff0f0_0" Pin1InfoVect2LinkObjId="SW-155684_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5185,-2969 5213,-2969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5185,-2986 5185,-2970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26265@0" ObjectIDZND0="g_35e4da0@0" ObjectIDZND1="g_35ff0f0@0" ObjectIDZND2="26266@x" Pin0InfoVect0LinkObjId="g_35e4da0_0" Pin0InfoVect1LinkObjId="g_35ff0f0_0" Pin0InfoVect2LinkObjId="SW-155685_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5185,-2986 5185,-2970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33e8d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5441,-2801 5441,-2755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26268@0" ObjectIDZND0="26327@0" Pin0InfoVect0LinkObjId="g_37d2ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5441,-2801 5441,-2755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b8b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5441,-2964 5441,-3040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26269@1" ObjectIDZND0="26270@x" ObjectIDZND1="26271@x" ObjectIDZND2="38069@1" Pin0InfoVect0LinkObjId="SW-155703_0" Pin0InfoVect1LinkObjId="SW-155704_0" Pin0InfoVect2LinkObjId="g_3a77760_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155702_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5441,-2964 5441,-3040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_363ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5379,-3025 5379,-3040 5441,-3040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26271@1" ObjectIDZND0="26269@x" ObjectIDZND1="26270@x" ObjectIDZND2="38069@1" Pin0InfoVect0LinkObjId="SW-155702_0" Pin0InfoVect1LinkObjId="SW-155703_0" Pin0InfoVect2LinkObjId="g_3a77760_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155704_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5379,-3025 5379,-3040 5441,-3040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b191e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5351,-2963 5351,-2981 5379,-2981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_3b2ce20@0" ObjectIDZND0="26271@x" ObjectIDZND1="g_363d100@0" Pin0InfoVect0LinkObjId="SW-155704_0" Pin0InfoVect1LinkObjId="g_363d100_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b2ce20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5351,-2963 5351,-2981 5379,-2981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b15780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5379,-2989 5379,-2981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="26271@0" ObjectIDZND0="g_3b2ce20@0" ObjectIDZND1="g_363d100@0" Pin0InfoVect0LinkObjId="g_3b2ce20_0" Pin0InfoVect1LinkObjId="g_363d100_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5379,-2989 5379,-2981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b11040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5379,-2981 5379,-2970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="26271@x" ObjectIDND1="g_3b2ce20@0" ObjectIDZND0="g_363d100@0" Pin0InfoVect0LinkObjId="g_363d100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155704_0" Pin1InfoVect1LinkObjId="g_3b2ce20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5379,-2981 5379,-2970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_394a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5485,-3002 5485,-2987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26270@1" ObjectIDZND0="g_3949190@0" Pin0InfoVect0LinkObjId="g_3949190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155703_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5485,-3002 5485,-2987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d38d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5441,-3040 5484,-3040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="26269@x" ObjectIDND1="26271@x" ObjectIDND2="38069@1" ObjectIDZND0="26270@0" Pin0InfoVect0LinkObjId="SW-155703_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155702_0" Pin1InfoVect1LinkObjId="SW-155704_0" Pin1InfoVect2LinkObjId="g_3a77760_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5441,-3040 5484,-3040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d2ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5650,-2806 5650,-2755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26273@0" ObjectIDZND0="26327@0" Pin0InfoVect0LinkObjId="g_33e8d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5650,-2806 5650,-2755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7de60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5234,-2756 5245,-2756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26243@0" ObjectIDZND0="26326@0" Pin0InfoVect0LinkObjId="SW-155935_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_360c890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5234,-2756 5245,-2756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f7590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-2755 5283,-2755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26327@0" ObjectIDZND0="26326@1" Pin0InfoVect0LinkObjId="SW-155935_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33e8d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-2755 5283,-2755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f55f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-2737 4832,-2756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26282@1" ObjectIDZND0="26243@0" Pin0InfoVect0LinkObjId="g_360c890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-2737 4832,-2756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b1fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-2701 4832,-2676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26282@0" ObjectIDZND0="26281@1" Pin0InfoVect0LinkObjId="SW-155749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-2701 4832,-2676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b2210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-2498 4831,-2478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26285@1" ObjectIDZND0="26283@1" Pin0InfoVect0LinkObjId="SW-155761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155763_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-2498 4831,-2478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c9060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-2395 4831,-2374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26284@1" ObjectIDZND0="26245@0" Pin0InfoVect0LinkObjId="g_337f100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155762_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-2395 4831,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c92c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-2451 4831,-2431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26283@0" ObjectIDZND0="26284@0" Pin0InfoVect0LinkObjId="SW-155762_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-2451 4831,-2431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ab2c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-2554 4831,-2534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="26324@0" ObjectIDZND0="26285@0" Pin0InfoVect0LinkObjId="SW-155763_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ab2e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-2554 4831,-2534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ab2e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-2649 4832,-2634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26281@0" ObjectIDZND0="26324@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-2649 4832,-2634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d9b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2374 4339,-2338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26245@0" ObjectIDZND0="26318@0" Pin0InfoVect0LinkObjId="SW-155900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c9060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2374 4339,-2338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c6690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-2266 4275,-2266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26320@0" ObjectIDZND0="g_32cf860@0" Pin0InfoVect0LinkObjId="g_32cf860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155902_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-2266 4275,-2266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c68f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2214 4339,-2192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26317@0" ObjectIDZND0="26319@0" Pin0InfoVect0LinkObjId="SW-155901_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2214 4339,-2192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f93e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-2124 4339,-2124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3336400@0" ObjectIDZND0="26319@x" ObjectIDZND1="g_3a6c600@0" Pin0InfoVect0LinkObjId="SW-155901_0" Pin0InfoVect1LinkObjId="g_3a6c600_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3336400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-2124 4339,-2124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f9640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2156 4339,-2124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26319@1" ObjectIDZND0="g_3336400@0" ObjectIDZND1="g_3a6c600@0" Pin0InfoVect0LinkObjId="g_3336400_0" Pin0InfoVect1LinkObjId="g_3a6c600_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155901_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2156 4339,-2124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30cbb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4322,-2266 4339,-2266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26320@1" ObjectIDZND0="26317@x" ObjectIDZND1="26318@x" Pin0InfoVect0LinkObjId="SW-155899_0" Pin0InfoVect1LinkObjId="SW-155900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155902_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4322,-2266 4339,-2266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30cbda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2241 4339,-2266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26317@1" ObjectIDZND0="26320@x" ObjectIDZND1="26318@x" Pin0InfoVect0LinkObjId="SW-155902_0" Pin0InfoVect1LinkObjId="SW-155900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2241 4339,-2266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32f7440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2266 4339,-2302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26317@x" ObjectIDND1="26320@x" ObjectIDZND0="26318@1" Pin0InfoVect0LinkObjId="SW-155900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155899_0" Pin1InfoVect1LinkObjId="SW-155902_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2266 4339,-2302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32f76a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4548,-2374 4548,-2339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26245@0" ObjectIDZND0="26314@0" Pin0InfoVect0LinkObjId="SW-155885_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c9060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4548,-2374 4548,-2339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3350730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-2267 4484,-2267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26316@0" ObjectIDZND0="g_3343fb0@0" Pin0InfoVect0LinkObjId="g_3343fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155887_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-2267 4484,-2267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d9a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4548,-2215 4548,-2193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26313@0" ObjectIDZND0="26315@0" Pin0InfoVect0LinkObjId="SW-155886_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4548,-2215 4548,-2193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cd9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-2125 4548,-2125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3ab4410@0" ObjectIDZND0="26315@x" ObjectIDZND1="g_3a6d540@0" Pin0InfoVect0LinkObjId="SW-155886_0" Pin0InfoVect1LinkObjId="g_3a6d540_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ab4410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-2125 4548,-2125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cdbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4548,-2157 4548,-2125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26315@1" ObjectIDZND0="g_3ab4410@0" ObjectIDZND1="g_3a6d540@0" Pin0InfoVect0LinkObjId="g_3ab4410_0" Pin0InfoVect1LinkObjId="g_3a6d540_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155886_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4548,-2157 4548,-2125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cde40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4531,-2267 4548,-2267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26316@1" ObjectIDZND0="26313@x" ObjectIDZND1="26314@x" Pin0InfoVect0LinkObjId="SW-155884_0" Pin0InfoVect1LinkObjId="SW-155885_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4531,-2267 4548,-2267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e8710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4548,-2242 4548,-2267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26313@1" ObjectIDZND0="26316@x" ObjectIDZND1="26314@x" Pin0InfoVect0LinkObjId="SW-155887_0" Pin0InfoVect1LinkObjId="SW-155885_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4548,-2242 4548,-2267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e8970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4548,-2267 4548,-2303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26313@x" ObjectIDND1="26316@x" ObjectIDZND0="26314@1" Pin0InfoVect0LinkObjId="SW-155885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155884_0" Pin1InfoVect1LinkObjId="SW-155887_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4548,-2267 4548,-2303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e8bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-2374 4768,-2336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26245@0" ObjectIDZND0="26310@0" Pin0InfoVect0LinkObjId="SW-155870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c9060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-2374 4768,-2336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b82400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-2264 4704,-2264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26312@0" ObjectIDZND0="g_35fe550@0" Pin0InfoVect0LinkObjId="g_35fe550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-2264 4704,-2264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35fdc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-2212 4768,-2190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26309@0" ObjectIDZND0="26311@0" Pin0InfoVect0LinkObjId="SW-155871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-2212 4768,-2190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-2154 4768,-2122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26311@1" ObjectIDZND0="g_33ba2e0@0" ObjectIDZND1="g_3a6e480@0" Pin0InfoVect0LinkObjId="g_33ba2e0_0" Pin0InfoVect1LinkObjId="g_3a6e480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-2154 4768,-2122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332ccf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4751,-2264 4768,-2264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26312@1" ObjectIDZND0="26309@x" ObjectIDZND1="26310@x" Pin0InfoVect0LinkObjId="SW-155869_0" Pin0InfoVect1LinkObjId="SW-155870_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155872_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4751,-2264 4768,-2264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332cf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-2239 4768,-2264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26309@1" ObjectIDZND0="26312@x" ObjectIDZND1="26310@x" Pin0InfoVect0LinkObjId="SW-155872_0" Pin0InfoVect1LinkObjId="SW-155870_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155869_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-2239 4768,-2264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b2aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-2264 4768,-2300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26309@x" ObjectIDND1="26312@x" ObjectIDZND0="26310@1" Pin0InfoVect0LinkObjId="SW-155870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155869_0" Pin1InfoVect1LinkObjId="SW-155872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-2264 4768,-2300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b2d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-2122 4768,-2122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_33ba2e0@0" ObjectIDZND0="26311@x" ObjectIDZND1="g_3a6e480@0" Pin0InfoVect0LinkObjId="SW-155871_0" Pin0InfoVect1LinkObjId="g_3a6e480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ba2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-2122 4768,-2122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3314130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-2261 4932,-2261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26308@0" ObjectIDZND0="g_3aa9260@0" Pin0InfoVect0LinkObjId="g_3aa9260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-2261 4932,-2261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ca2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-2209 4996,-2187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26305@0" ObjectIDZND0="26307@0" Pin0InfoVect0LinkObjId="SW-155856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155854_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-2209 4996,-2187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3311120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4962,-2119 4996,-2119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3348ef0@0" ObjectIDZND0="26307@x" ObjectIDZND1="g_3a6f3c0@0" Pin0InfoVect0LinkObjId="SW-155856_0" Pin0InfoVect1LinkObjId="g_3a6f3c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3348ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4962,-2119 4996,-2119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3311380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-2151 4996,-2119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26307@1" ObjectIDZND0="g_3348ef0@0" ObjectIDZND1="g_3a6f3c0@0" Pin0InfoVect0LinkObjId="g_3348ef0_0" Pin0InfoVect1LinkObjId="g_3a6f3c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-2151 4996,-2119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33115e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4979,-2261 4996,-2261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26308@1" ObjectIDZND0="26305@x" ObjectIDZND1="26306@x" Pin0InfoVect0LinkObjId="SW-155854_0" Pin0InfoVect1LinkObjId="SW-155855_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155857_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4979,-2261 4996,-2261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35e3ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-2236 4996,-2261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26305@1" ObjectIDZND0="26308@x" ObjectIDZND1="26306@x" Pin0InfoVect0LinkObjId="SW-155857_0" Pin0InfoVect1LinkObjId="SW-155855_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-2236 4996,-2261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35e4100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-2261 4996,-2297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26305@x" ObjectIDND1="26308@x" ObjectIDZND0="26306@1" Pin0InfoVect0LinkObjId="SW-155855_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155854_0" Pin1InfoVect1LinkObjId="SW-155857_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-2261 4996,-2297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3369620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5262,-2265 5251,-2265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26301@0" ObjectIDZND0="g_33c2670@0" Pin0InfoVect0LinkObjId="g_33c2670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155832_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5262,-2265 5251,-2265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3369880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-2213 5315,-2191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26298@0" ObjectIDZND0="26300@0" Pin0InfoVect0LinkObjId="SW-155831_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-2213 5315,-2191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34448b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5281,-2123 5315,-2123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3b7bd30@0" ObjectIDZND0="26300@x" ObjectIDZND1="g_3a70300@0" Pin0InfoVect0LinkObjId="SW-155831_0" Pin0InfoVect1LinkObjId="g_3a70300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b7bd30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5281,-2123 5315,-2123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-2155 5315,-2123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26300@1" ObjectIDZND0="g_3b7bd30@0" ObjectIDZND1="g_3a70300@0" Pin0InfoVect0LinkObjId="g_3b7bd30_0" Pin0InfoVect1LinkObjId="g_3a70300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-2155 5315,-2123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5298,-2265 5315,-2265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26301@1" ObjectIDZND0="26298@x" ObjectIDZND1="26299@x" Pin0InfoVect0LinkObjId="SW-155829_0" Pin0InfoVect1LinkObjId="SW-155830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155832_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5298,-2265 5315,-2265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-2240 5315,-2265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26298@1" ObjectIDZND0="26301@x" ObjectIDZND1="26299@x" Pin0InfoVect0LinkObjId="SW-155832_0" Pin0InfoVect1LinkObjId="SW-155830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155829_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-2240 5315,-2265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333ccd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-2265 5315,-2301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26298@x" ObjectIDND1="26301@x" ObjectIDZND0="26299@1" Pin0InfoVect0LinkObjId="SW-155830_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155829_0" Pin1InfoVect1LinkObjId="SW-155832_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-2265 5315,-2301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333cf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-2374 5315,-2337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26244@0" ObjectIDZND0="26299@0" Pin0InfoVect0LinkObjId="SW-155830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331bb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-2374 5315,-2337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-2264 5460,-2264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26297@0" ObjectIDZND0="g_333ed00@0" Pin0InfoVect0LinkObjId="g_333ed00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155817_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-2264 5460,-2264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336b160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-2212 5524,-2190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26294@0" ObjectIDZND0="26296@0" Pin0InfoVect0LinkObjId="SW-155816_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-2212 5524,-2190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336f160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5490,-2122 5524,-2122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3448a80@0" ObjectIDZND0="26296@x" ObjectIDZND1="g_3a71240@0" Pin0InfoVect0LinkObjId="SW-155816_0" Pin0InfoVect1LinkObjId="g_3a71240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3448a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5490,-2122 5524,-2122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-2154 5524,-2122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26296@1" ObjectIDZND0="g_3448a80@0" ObjectIDZND1="g_3a71240@0" Pin0InfoVect0LinkObjId="g_3448a80_0" Pin0InfoVect1LinkObjId="g_3a71240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-2154 5524,-2122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5507,-2264 5524,-2264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26297@1" ObjectIDZND0="26294@x" ObjectIDZND1="26295@x" Pin0InfoVect0LinkObjId="SW-155814_0" Pin0InfoVect1LinkObjId="SW-155815_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155817_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5507,-2264 5524,-2264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-2239 5524,-2264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26294@1" ObjectIDZND0="26297@x" ObjectIDZND1="26295@x" Pin0InfoVect0LinkObjId="SW-155817_0" Pin0InfoVect1LinkObjId="SW-155815_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155814_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-2239 5524,-2264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33e2fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-2264 5524,-2300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26294@x" ObjectIDND1="26297@x" ObjectIDZND0="26295@1" Pin0InfoVect0LinkObjId="SW-155815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155814_0" Pin1InfoVect1LinkObjId="SW-155817_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-2264 5524,-2300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33e3240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-2374 5524,-2336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26244@0" ObjectIDZND0="26295@0" Pin0InfoVect0LinkObjId="SW-155815_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331bb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-2374 5524,-2336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33daa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5677,-2258 5666,-2258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26293@0" ObjectIDZND0="g_32fa1d0@0" Pin0InfoVect0LinkObjId="g_32fa1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155802_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5677,-2258 5666,-2258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33dacf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5730,-2206 5730,-2184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26290@0" ObjectIDZND0="26292@0" Pin0InfoVect0LinkObjId="SW-155801_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5730,-2206 5730,-2184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e1700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5696,-2116 5730,-2116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_330c610@0" ObjectIDZND0="26292@x" ObjectIDZND1="g_3a72180@0" Pin0InfoVect0LinkObjId="SW-155801_0" Pin0InfoVect1LinkObjId="g_3a72180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_330c610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5696,-2116 5730,-2116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e1960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5730,-2148 5730,-2116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26292@1" ObjectIDZND0="g_330c610@0" ObjectIDZND1="g_3a72180@0" Pin0InfoVect0LinkObjId="g_330c610_0" Pin0InfoVect1LinkObjId="g_3a72180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155801_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5730,-2148 5730,-2116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b3c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5713,-2258 5730,-2258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26293@1" ObjectIDZND0="26290@x" ObjectIDZND1="26291@x" Pin0InfoVect0LinkObjId="SW-155799_0" Pin0InfoVect1LinkObjId="SW-155800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5713,-2258 5730,-2258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b3ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5730,-2233 5730,-2258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26290@1" ObjectIDZND0="26293@x" ObjectIDZND1="26291@x" Pin0InfoVect0LinkObjId="SW-155802_0" Pin0InfoVect1LinkObjId="SW-155800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5730,-2233 5730,-2258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b4140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5730,-2258 5730,-2294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26290@x" ObjectIDND1="26293@x" ObjectIDZND0="26291@1" Pin0InfoVect0LinkObjId="SW-155800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155799_0" Pin1InfoVect1LinkObjId="SW-155802_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5730,-2258 5730,-2294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b43a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5730,-2374 5730,-2330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26244@0" ObjectIDZND0="26291@0" Pin0InfoVect0LinkObjId="SW-155800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331bb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5730,-2374 5730,-2330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32fac60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5923,-2202 5923,-2182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26286@0" ObjectIDZND0="26288@0" Pin0InfoVect0LinkObjId="SW-155786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5923,-2202 5923,-2182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aa640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5887,-2114 5923,-2114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3386e90@0" ObjectIDZND0="26288@x" ObjectIDZND1="g_3a730c0@0" Pin0InfoVect0LinkObjId="SW-155786_0" Pin0InfoVect1LinkObjId="g_3a730c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3386e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5887,-2114 5923,-2114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aa8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5923,-2146 5923,-2114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26288@1" ObjectIDZND0="g_3386e90@0" ObjectIDZND1="g_3a730c0@0" Pin0InfoVect0LinkObjId="g_3386e90_0" Pin0InfoVect1LinkObjId="g_3a730c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155786_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5923,-2146 5923,-2114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5904,-2256 5921,-2256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26289@1" ObjectIDZND0="26286@x" ObjectIDZND1="26287@x" Pin0InfoVect0LinkObjId="SW-155784_0" Pin0InfoVect1LinkObjId="SW-155785_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5904,-2256 5921,-2256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ffc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5921,-2231 5921,-2256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26286@1" ObjectIDZND0="26289@x" ObjectIDZND1="26287@x" Pin0InfoVect0LinkObjId="SW-155787_0" Pin0InfoVect1LinkObjId="SW-155785_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155784_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5921,-2231 5921,-2256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ffe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5921,-2256 5921,-2292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26286@x" ObjectIDND1="26289@x" ObjectIDZND0="26287@1" Pin0InfoVect0LinkObjId="SW-155785_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155784_0" Pin1InfoVect1LinkObjId="SW-155787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5921,-2256 5921,-2292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34000d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5921,-2374 5921,-2328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26244@0" ObjectIDZND0="26287@0" Pin0InfoVect0LinkObjId="SW-155785_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331bb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5921,-2374 5921,-2328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_337eea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5868,-2256 5857,-2256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26289@0" ObjectIDZND0="g_340e720@0" Pin0InfoVect0LinkObjId="g_340e720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5868,-2256 5857,-2256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_337f100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-2333 4996,-2374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26306@0" ObjectIDZND0="26245@0" Pin0InfoVect0LinkObjId="g_33c9060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-2333 4996,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3401f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-2374 5074,-2389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26245@0" ObjectIDZND0="26323@1" Pin0InfoVect0LinkObjId="SW-155916_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c9060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-2374 5074,-2389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_331bb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5250,-2390 5250,-2374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26322@1" ObjectIDZND0="26244@0" Pin0InfoVect0LinkObjId="g_3b8d170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5250,-2390 5250,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35f1070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5838,-2635 5862,-2635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5838,-2635 5862,-2635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35f12d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5889,-2635 5913,-2635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5889,-2635 5913,-2635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35ffca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5778,-2464 5778,-2432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26302@0" ObjectIDZND0="26303@1" Pin0InfoVect0LinkObjId="SW-155845_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5778,-2464 5778,-2432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3438320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5778,-2523 5778,-2491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26304@0" ObjectIDZND0="26302@1" Pin0InfoVect0LinkObjId="SW-155844_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5778,-2523 5778,-2491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5778,-2569 5778,-2558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_33b6fe0@1" ObjectIDZND0="26304@1" Pin0InfoVect0LinkObjId="SW-155846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b6fe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5778,-2569 5778,-2558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5778,-2396 5778,-2374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26303@0" ObjectIDZND0="26244@0" Pin0InfoVect0LinkObjId="g_331bb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5778,-2396 5778,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8d3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5778,-2622 5778,-2629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_33b6fe0@0" ObjectIDZND0="g_3b8daf0@0" Pin0InfoVect0LinkObjId="g_3b8daf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b6fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5778,-2622 5778,-2629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5778,-2629 5778,-2635 5794,-2635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_33b6fe0@0" ObjectIDND1="g_3b8daf0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33b6fe0_0" Pin1InfoVect1LinkObjId="g_3b8daf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5778,-2629 5778,-2635 5794,-2635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5745,-2615 5745,-2629 5778,-2629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b8daf0@0" ObjectIDZND0="g_33b6fe0@0" Pin0InfoVect0LinkObjId="g_33b6fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b8daf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5745,-2615 5745,-2629 5778,-2629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aa81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-2631 4388,-2631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-2631 4388,-2631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3445b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-2631 4439,-2631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-2631 4439,-2631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34466d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-2591 4499,-2631 4475,-2631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34468c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34468c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-2591 4499,-2631 4475,-2631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33e3c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-2538 4499,-2501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34468c0@1" ObjectIDZND0="26325@1" Pin0InfoVect0LinkObjId="SW-156068_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34468c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-2538 4499,-2501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3440dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-2465 4499,-2374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26325@0" ObjectIDZND0="26245@0" Pin0InfoVect0LinkObjId="g_33c9060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156068_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-2465 4499,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3447240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-2631 4288,-2631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-2631 4288,-2631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34474a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5949,-2635 5978,-2635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5949,-2635 5978,-2635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d74a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5586,-2514 5586,-2533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3ac7d50@1" ObjectIDZND0="g_3ac85d0@0" Pin0InfoVect0LinkObjId="g_3ac85d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ac7d50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5586,-2514 5586,-2533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3443560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5586,-2440 5586,-2450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26277@0" ObjectIDZND0="g_3ac7d50@0" ObjectIDZND1="26278@x" ObjectIDZND2="g_33d7700@0" Pin0InfoVect0LinkObjId="g_3ac7d50_0" Pin0InfoVect1LinkObjId="SW-155739_0" Pin0InfoVect2LinkObjId="g_33d7700_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5586,-2440 5586,-2450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34437c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5586,-2450 5586,-2470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26277@x" ObjectIDND1="26278@x" ObjectIDND2="g_33d7700@0" ObjectIDZND0="g_3ac7d50@0" Pin0InfoVect0LinkObjId="g_3ac7d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155738_0" Pin1InfoVect1LinkObjId="SW-155739_0" Pin1InfoVect2LinkObjId="g_33d7700_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5586,-2450 5586,-2470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3443a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5586,-2450 5615,-2450 5615,-2472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3ac7d50@0" ObjectIDND1="26277@x" ObjectIDND2="26278@x" ObjectIDZND0="g_33d7700@0" Pin0InfoVect0LinkObjId="g_33d7700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ac7d50_0" Pin1InfoVect1LinkObjId="SW-155738_0" Pin1InfoVect2LinkObjId="SW-155739_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5586,-2450 5615,-2450 5615,-2472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3443c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5535,-2472 5535,-2450 5587,-2450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26278@0" ObjectIDZND0="g_3ac7d50@0" ObjectIDZND1="26277@x" ObjectIDZND2="g_33d7700@0" Pin0InfoVect0LinkObjId="g_3ac7d50_0" Pin0InfoVect1LinkObjId="SW-155738_0" Pin0InfoVect2LinkObjId="g_33d7700_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5535,-2472 5535,-2450 5587,-2450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3442200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5535,-2508 5535,-2526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26278@1" ObjectIDZND0="g_3441770@0" Pin0InfoVect0LinkObjId="g_3441770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5535,-2508 5535,-2526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3442460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5586,-2404 5586,-2374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26277@1" ObjectIDZND0="26244@0" Pin0InfoVect0LinkObjId="g_331bb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155738_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5586,-2404 5586,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3428db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-2518 4657,-2537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3596000@1" ObjectIDZND0="g_3596900@0" Pin0InfoVect0LinkObjId="g_3596900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3596000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-2518 4657,-2537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-2444 4657,-2454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26279@0" ObjectIDZND0="g_3596000@0" ObjectIDZND1="26280@x" ObjectIDZND2="g_3429010@0" Pin0InfoVect0LinkObjId="g_3596000_0" Pin0InfoVect1LinkObjId="SW-155741_0" Pin0InfoVect2LinkObjId="g_3429010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-2444 4657,-2454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344c7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-2454 4657,-2474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26279@x" ObjectIDND1="26280@x" ObjectIDND2="g_3429010@0" ObjectIDZND0="g_3596000@0" Pin0InfoVect0LinkObjId="g_3596000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155740_0" Pin1InfoVect1LinkObjId="SW-155741_0" Pin1InfoVect2LinkObjId="g_3429010_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-2454 4657,-2474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-2454 4686,-2454 4686,-2476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3596000@0" ObjectIDND1="26279@x" ObjectIDND2="26280@x" ObjectIDZND0="g_3429010@0" Pin0InfoVect0LinkObjId="g_3429010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3596000_0" Pin1InfoVect1LinkObjId="SW-155740_0" Pin1InfoVect2LinkObjId="SW-155741_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-2454 4686,-2454 4686,-2476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-2476 4606,-2454 4658,-2454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26280@0" ObjectIDZND0="g_3596000@0" ObjectIDZND1="26279@x" ObjectIDZND2="g_3429010@0" Pin0InfoVect0LinkObjId="g_3596000_0" Pin0InfoVect1LinkObjId="SW-155740_0" Pin0InfoVect2LinkObjId="g_3429010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-2476 4606,-2454 4658,-2454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3434aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-2512 4606,-2530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26280@1" ObjectIDZND0="g_34341a0@0" Pin0InfoVect0LinkObjId="g_34341a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-2512 4606,-2530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3434d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4657,-2408 4657,-2374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26279@1" ObjectIDZND0="26245@0" Pin0InfoVect0LinkObjId="g_33c9060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4657,-2408 4657,-2374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3435b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-2425 5074,-2455 5145,-2455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26323@0" ObjectIDZND0="26321@1" Pin0InfoVect0LinkObjId="SW-155914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-2425 5074,-2455 5145,-2455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3435d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5250,-2426 5250,-2455 5172,-2455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26322@0" ObjectIDZND0="26321@0" Pin0InfoVect0LinkObjId="SW-155914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5250,-2426 5250,-2455 5172,-2455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b615a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2937 4539,-2937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="26251@x" ObjectIDND1="26256@x" ObjectIDND2="26253@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155611_0" Pin1InfoVect1LinkObjId="SW-155616_0" Pin1InfoVect2LinkObjId="SW-155613_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2937 4539,-2937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b88480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5020,-2856 5006,-2856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3b879f0@0" Pin0InfoVect0LinkObjId="g_3b879f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5020,-2856 5006,-2856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b886e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-2856 5089,-2856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="26262@x" ObjectIDZND1="26263@x" Pin0InfoVect0LinkObjId="SW-155681_0" Pin0InfoVect1LinkObjId="SW-155682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-2856 5089,-2856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b70890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-2871 5089,-2856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26262@0" ObjectIDZND0="0@x" ObjectIDZND1="26263@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-155682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-2871 5089,-2856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b70a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-2856 5089,-2839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="26262@x" ObjectIDZND0="26263@1" Pin0InfoVect0LinkObjId="SW-155682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-155681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-2856 5089,-2839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b70c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-2863 4884,-2831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26258@0" ObjectIDZND0="26259@1" Pin0InfoVect0LinkObjId="SW-155664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-2863 4884,-2831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b70e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5942,-3061 5942,-2755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="26327@0" Pin0InfoVect0LinkObjId="g_33e8d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5942,-3061 5942,-2755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b71580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5441,-2869 5441,-2837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26267@0" ObjectIDZND0="26268@1" Pin0InfoVect0LinkObjId="SW-155701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5441,-2869 5441,-2837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b71770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5441,-2928 5441,-2896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26269@0" ObjectIDZND0="26267@1" Pin0InfoVect0LinkObjId="SW-155700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5441,-2928 5441,-2896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b71960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5650,-2874 5650,-2842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26272@0" ObjectIDZND0="26273@1" Pin0InfoVect0LinkObjId="SW-155720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5650,-2874 5650,-2842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b71b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5650,-2933 5650,-2901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26274@0" ObjectIDZND0="26272@1" Pin0InfoVect0LinkObjId="SW-155719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155721_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5650,-2933 5650,-2901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b71dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5650,-2969 5650,-3084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="26274@1" ObjectIDZND0="34582@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5650,-2969 5650,-3084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b79e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2925 4597,-2937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26251@x" ObjectIDND1="26256@x" ObjectIDZND0="0@x" ObjectIDZND1="26253@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-155613_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155611_0" Pin1InfoVect1LinkObjId="SW-155616_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2925 4597,-2937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-2937 4597,-2947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="26251@x" ObjectIDND2="26256@x" ObjectIDZND0="26253@0" Pin0InfoVect0LinkObjId="SW-155613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-155611_0" Pin1InfoVect2LinkObjId="SW-155616_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-2937 4597,-2947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a6c3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5137,-2588 5137,-2601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3b7ac30@0" ObjectIDZND0="g_3b7a330@0" Pin0InfoVect0LinkObjId="g_3b7a330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b7ac30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5137,-2588 5137,-2601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2124 4339,-2109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26319@x" ObjectIDND1="g_3336400@0" ObjectIDZND0="g_3a6c600@1" Pin0InfoVect0LinkObjId="g_3a6c600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155901_0" Pin1InfoVect1LinkObjId="g_3336400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2124 4339,-2109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-2075 4339,-2052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a6c600@0" ObjectIDZND0="34261@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.049Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a6c600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-2075 4339,-2052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6dfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4548,-2125 4548,-2109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26315@x" ObjectIDND1="g_3ab4410@0" ObjectIDZND0="g_3a6d540@1" Pin0InfoVect0LinkObjId="g_3a6d540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155886_0" Pin1InfoVect1LinkObjId="g_3ab4410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4548,-2125 4548,-2109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6e220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4548,-2075 4548,-2050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a6d540@0" ObjectIDZND0="34260@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.048Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a6d540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4548,-2075 4548,-2050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6ef00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-2122 4768,-2106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26311@x" ObjectIDND1="g_33ba2e0@0" ObjectIDZND0="g_3a6e480@1" Pin0InfoVect0LinkObjId="g_3a6e480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155871_0" Pin1InfoVect1LinkObjId="g_33ba2e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-2122 4768,-2106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6f160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-2072 4768,-2047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a6e480@0" ObjectIDZND0="34259@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.047Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a6e480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-2072 4768,-2047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6fe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-2119 4996,-2103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26307@x" ObjectIDND1="g_3348ef0@0" ObjectIDZND0="g_3a6f3c0@1" Pin0InfoVect0LinkObjId="g_3a6f3c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155856_0" Pin1InfoVect1LinkObjId="g_3348ef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-2119 4996,-2103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a700a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-2069 4996,-2046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a6f3c0@0" ObjectIDZND0="34258@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.046Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a6f3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-2069 4996,-2046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a70d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-2123 5315,-2107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26300@x" ObjectIDND1="g_3b7bd30@0" ObjectIDZND0="g_3a70300@1" Pin0InfoVect0LinkObjId="g_3a70300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155831_0" Pin1InfoVect1LinkObjId="g_3b7bd30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-2123 5315,-2107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a70fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5315,-2073 5315,-2042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a70300@0" ObjectIDZND0="34257@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a70300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5315,-2073 5315,-2042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a71cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-2122 5524,-2105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26296@x" ObjectIDND1="g_3448a80@0" ObjectIDZND0="g_3a71240@1" Pin0InfoVect0LinkObjId="g_3a71240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155816_0" Pin1InfoVect1LinkObjId="g_3448a80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-2122 5524,-2105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a71f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-2071 5524,-2042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a71240@0" ObjectIDZND0="34256@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a71240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-2071 5524,-2042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a72c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5730,-2116 5730,-2100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26292@x" ObjectIDND1="g_330c610@0" ObjectIDZND0="g_3a72180@1" Pin0InfoVect0LinkObjId="g_3a72180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155801_0" Pin1InfoVect1LinkObjId="g_330c610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5730,-2116 5730,-2100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a72e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5730,-2066 5730,-2038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a72180@0" ObjectIDZND0="34255@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.042Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a72180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5730,-2066 5730,-2038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a73b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5923,-2114 5923,-2098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26288@x" ObjectIDND1="g_3386e90@0" ObjectIDZND0="g_3a730c0@1" Pin0InfoVect0LinkObjId="g_3a730c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155786_0" Pin1InfoVect1LinkObjId="g_3386e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5923,-2114 5923,-2098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a73da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5923,-2064 5923,-2038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3a730c0@0" ObjectIDZND0="34254@0" Pin0InfoVect0LinkObjId="EC-DY_ZX.041Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a730c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5923,-2064 5923,-2038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a76270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-3036 5089,-3070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="26264@x" ObjectIDND1="26265@x" ObjectIDZND0="38067@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155683_0" Pin1InfoVect1LinkObjId="SW-155684_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-3036 5089,-3070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a76d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-2966 5089,-3036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="26264@1" ObjectIDZND0="26265@x" ObjectIDZND1="38067@1" Pin0InfoVect0LinkObjId="SW-155684_0" Pin0InfoVect1LinkObjId="g_3a76270_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-2966 5089,-3036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a76fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-3036 5185,-3036 5185,-3022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="26264@x" ObjectIDND1="38067@1" ObjectIDZND0="26265@1" Pin0InfoVect0LinkObjId="SW-155684_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155683_0" Pin1InfoVect1LinkObjId="g_3a76270_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-3036 5185,-3036 5185,-3022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a77760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5441,-3040 5441,-3074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="26269@x" ObjectIDND1="26270@x" ObjectIDND2="26271@x" ObjectIDZND0="38069@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155702_0" Pin1InfoVect1LinkObjId="SW-155703_0" Pin1InfoVect2LinkObjId="SW-155704_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5441,-3040 5441,-3074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a7b2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-3314 5088,-3288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-3314 5088,-3288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a7b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-3367 5088,-3341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-3367 5088,-3341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a7b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-3437 5088,-3411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-3437 5088,-3411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a7ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5942,-3236 5942,-3206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5942,-3236 5942,-3206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a7d010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5942,-3306 5942,-3276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5942,-3306 5942,-3276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a7d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5917,-3474 5942,-3474 5943,-3475 5942,-3342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5917,-3474 5942,-3474 5943,-3475 5942,-3342 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_ZX"/>
</svg>