<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-342" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-358 -1225 3353 1812">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape16_0">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16_1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor2">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="capacitor:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape9">
    <polyline DF8003:Layer="PUBLIC" points="0,11 5,1 9,11 0,11 " stroke-width="0.5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="16" y2="11"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape78">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="8" x2="8" y1="45" y2="10"/>
    <rect height="28" stroke-width="1" width="14" x="1" y="5"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="65" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="52" y2="35"/>
    <circle cx="15" cy="90" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,22 9,35 21,35 15,22 15,23 15,22 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,65 48,65 48,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="54" x2="42" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="52" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="46" x2="50" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="65" y2="70"/>
    <circle cx="15" cy="68" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="20" x2="12" y1="91" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="20" x2="12" y1="91" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="12" x2="12" y1="86" y2="97"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,35 33,53 48,53 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="22" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="65" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape200">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="31" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="10" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="19" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="21" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="21" y2="14"/>
    <circle cx="17" cy="21" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="17" y1="31" y2="31"/>
   </symbol>
   <symbol id="lightningRod:shape24">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.24019" x1="1" x2="10" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.44688" x1="3" x2="8" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="5" x2="5" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="5" x2="5" y1="43" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="8" x2="8" y1="27" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="4" x2="4" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="8" x2="8" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.62712" x1="5" x2="8" y1="15" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.62712" x1="3" x2="9" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.62712" x1="3" x2="9" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.62712" x1="3" x2="9" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="4" x2="4" y1="30" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.62712" x1="3" x2="9" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="8" x2="8" y1="33" y2="28"/>
    <rect height="25" stroke-width="1.2679" width="10" x="1" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.2" x1="5" x2="9" y1="33" y2="33"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape206">
    <circle cx="7" cy="21" fillStyle="0" r="6" stroke-width="0.431185"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="3,17 11,25 " stroke-width="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="2,24 11,17 " stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="74" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="82" x2="77" y1="92" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="83" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="83" y1="41" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="77" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="91" x2="84" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="84" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="45" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="53" x2="45" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="45" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="45" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="44" x2="35" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="44" x2="53" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="53" y1="16" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape45">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="34" x2="34" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="24" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="24" x2="24" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="17" x2="17" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="41" x2="41" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="5" x2="5" y1="11" y2="4"/>
    <circle cx="29" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="26" x2="27" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="22" x2="21" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="21" x2="27" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="21" y2="23"/>
    <circle cx="19" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="21" y2="23"/>
    <circle cx="24" cy="42" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="29" y2="29"/>
    <circle cx="29" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="29" y2="29"/>
    <circle cx="19" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="31" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape32">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="34" y1="38" y2="40"/>
    <ellipse cx="34" cy="38" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="35" x2="35" y1="40" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="36" x2="34" y1="37" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="40" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="33" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="33" x2="33" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="40" x2="40" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="15" y1="7" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="17" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="55" x2="55" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="53" x2="53" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="28" y1="31" y2="33"/>
    <circle cx="28" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="28" cy="31" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="28" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="38" x2="41" y1="18" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="38" x2="35" y1="18" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="35" x2="41" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="33" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="49" x2="49" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="36" x2="38" y1="31" y2="33"/>
    <circle cx="38" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="38" cy="31" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="39" x2="39" y1="33" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="41" x2="38" y1="31" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape147">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="35" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="40" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="16" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="34" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="40" y2="39"/>
    <ellipse cx="34" cy="40" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="27" y2="27"/>
    <ellipse cx="33" cy="15" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="3" y2="3"/>
    <ellipse cx="50" cy="27" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="16" y2="15"/>
    <ellipse cx="14" cy="39" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="56" x2="56" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="49" x2="56" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="49" x2="56" y1="25" y2="22"/>
    <ellipse cx="13" cy="16" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="3" y2="3"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1faa180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fab320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fabd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1facc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fadf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1faebb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1faf750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1fb0150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_160aa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_160aa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb3550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb3550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb52e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb52e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1fb6300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb7f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fb8af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fb98b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fba1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbb8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbc4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbcd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fbd510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbe5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbfa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fc0420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fc1840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fc23b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1fc33e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fc4020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fd27f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc5910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1fc6f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1fc8430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1822" width="3363" x="-363" y="-1230"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1609ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -21.000000 -367.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15aca80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -32.000000 -382.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15acce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -7.000000 -397.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1562b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.000000 114.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1562cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 102.000000 99.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1562e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 93.000000 128.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 94.000000 144.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15be8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 93.000000 159.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bea60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 93.000000 174.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1588ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 698.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1589160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 683.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15892d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.000000 712.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1589440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 728.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15895b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.000000 743.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1589720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.000000 758.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149a7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1386.000000 820.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149acb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 805.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149ae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1400.000000 790.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149b160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 468.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149b340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1202.000000 453.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149b520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 438.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149b7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 136.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149b9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 121.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149bbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.000000 106.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15125a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 804.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1512820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 789.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1512a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 774.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.666667 -0.000000 0.000000 -2.200000 -1177.333333 318.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1620" x2="1630" y1="285" y2="280"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1620" x2="1630" y1="275" y2="280"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1620" x2="1620" y1="285" y2="275"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1620" x2="1630" y1="285" y2="280"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(-1.666667 -0.000000 -0.000000 2.200000 4348.333333 -914.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1620" x2="1630" y1="285" y2="280"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1620" x2="1630" y1="275" y2="280"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1620" x2="1620" y1="285" y2="275"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1620" x2="1630" y1="285" y2="280"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be3d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2092.000000 756.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf1570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2109.000000 741.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf16e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2100.000000 770.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf1850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2101.000000 786.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf19c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2100.000000 801.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf1b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2100.000000 816.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d026a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2725.000000 486.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d02b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2714.000000 471.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be3890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2739.000000 456.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e0880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1806.000000 436.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ed980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1795.000000 421.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ced3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.000000 406.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="423,500 426,508 420,508 423,500 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="374,511 378,517 372,517 374,511 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-317379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.089769 40.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48984" ObjectName="SW-CX_LJZ.CX_LJZ_373BK"/>
     <cge:Meas_Ref ObjectId="317379"/>
    <cge:TPSR_Ref TObjectID="48984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317359">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.987002 -101.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48968" ObjectName="SW-CX_LJZ.CX_LJZ_301BK"/>
     <cge:Meas_Ref ObjectId="317359"/>
    <cge:TPSR_Ref TObjectID="48968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317351">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.987002 -476.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48960" ObjectName="SW-CX_LJZ.CX_LJZ_201BK"/>
     <cge:Meas_Ref ObjectId="317351"/>
    <cge:TPSR_Ref TObjectID="48960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317335">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1280.475728 -789.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48951" ObjectName="SW-CX_LJZ.CX_LJZ_272BK"/>
     <cge:Meas_Ref ObjectId="317335"/>
    <cge:TPSR_Ref TObjectID="48951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317366">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.461805 42.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48973" ObjectName="SW-CX_LJZ.CX_LJZ_371BK"/>
     <cge:Meas_Ref ObjectId="317366"/>
    <cge:TPSR_Ref TObjectID="48973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317322">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 737.708738 -789.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48945" ObjectName="SW-CX_LJZ.CX_LJZ_271BK"/>
     <cge:Meas_Ref ObjectId="317322"/>
    <cge:TPSR_Ref TObjectID="48945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.614155 43.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48988" ObjectName="SW-CX_LJZ.CX_LJZ_374BK"/>
     <cge:Meas_Ref ObjectId="317384"/>
    <cge:TPSR_Ref TObjectID="48988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317389">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.222704 42.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48992" ObjectName="SW-CX_LJZ.CX_LJZ_375BK"/>
     <cge:Meas_Ref ObjectId="317389"/>
    <cge:TPSR_Ref TObjectID="48992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317394">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.951662 39.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48996" ObjectName="SW-CX_LJZ.CX_LJZ_376BK"/>
     <cge:Meas_Ref ObjectId="317394"/>
    <cge:TPSR_Ref TObjectID="48996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317423">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -288.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49007" ObjectName="SW-CX_LJZ.CX_LJZ_101BK"/>
     <cge:Meas_Ref ObjectId="317423"/>
    <cge:TPSR_Ref TObjectID="49007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317415">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2266.000000 -549.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49001" ObjectName="SW-CX_LJZ.CX_LJZ_121BK"/>
     <cge:Meas_Ref ObjectId="317415"/>
    <cge:TPSR_Ref TObjectID="49001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2288.000000 55.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317371">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 385.867233 50.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48977" ObjectName="SW-CX_LJZ.CX_LJZ_372BK"/>
     <cge:Meas_Ref ObjectId="317371"/>
    <cge:TPSR_Ref TObjectID="48977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317374">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 400.500000 364.500000)" xlink:href="#breaker2:shape16_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48981" ObjectName="SW-CX_LJZ.CX_LJZ_372QBK"/>
     <cge:Meas_Ref ObjectId="317374"/>
    <cge:TPSR_Ref TObjectID="48981"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LJZ.CX_LJZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="48428"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.987002 -251.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="48430"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.987002 -251.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="48432"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.987002 -251.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="49018" ObjectName="TF-CX_LJZ.CX_LJZ_1T"/>
    <cge:TPSR_Ref TObjectID="49018"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJZ.CX_LJZ_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="54,-48 1533,-48 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49021" ObjectName="BS-CX_LJZ.CX_LJZ_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="49021"/></metadata>
   <polyline fill="none" opacity="0" points="54,-48 1533,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJZ.CX_LJZ_2ⅠM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-647 1414,-647 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49020" ObjectName="BS-CX_LJZ.CX_LJZ_2ⅠM"/>
    <cge:TPSR_Ref TObjectID="49020"/></metadata>
   <polyline fill="none" opacity="0" points="574,-647 1414,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJZ.CX_LJZ_1ⅠM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2078,-823 2078,210 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49022" ObjectName="BS-CX_LJZ.CX_LJZ_1ⅠM"/>
    <cge:TPSR_Ref TObjectID="49022"/></metadata>
   <polyline fill="none" opacity="0" points="2078,-823 2078,210 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 381.740650 441.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_15fd420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 898.000000 -220.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e3c00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1187.000000 -165.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16ac6d0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -3.151515 -4.947148 -0.000000 2867.225787 -543.242424)" xlink:href="#earth:shape9"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ee270" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -3.151515 -4.947148 -0.000000 2889.225787 60.757576)" xlink:href="#earth:shape9"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1510730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,32 602,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48984@0" ObjectIDZND0="48986@1" Pin0InfoVect0LinkObjId="SW-317380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="602,32 602,60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15bebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,-12 602,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48985@1" ObjectIDZND0="48984@1" Pin0InfoVect0LinkObjId="SW-317379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="602,-12 602,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1558950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-155 1040,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48970@1" ObjectIDZND0="48968@1" Pin0InfoVect0LinkObjId="SW-317359_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-155 1040,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1558b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-110 1040,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48968@0" ObjectIDZND0="48969@1" Pin0InfoVect0LinkObjId="SW-317360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-110 1040,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15599b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1007,-196 1040,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="g_1559060@0" ObjectIDZND0="48970@x" ObjectIDZND1="g_1623580@0" ObjectIDZND2="49018@x" Pin0InfoVect0LinkObjId="SW-317360_0" Pin0InfoVect1LinkObjId="g_1623580_0" Pin0InfoVect2LinkObjId="g_1637f30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1559060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1007,-196 1040,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1559ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-196 1040,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="g_1559060@0" ObjectIDND1="g_1623580@0" ObjectIDND2="49018@x" ObjectIDZND0="48970@0" Pin0InfoVect0LinkObjId="SW-317360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1559060_0" Pin1InfoVect1LinkObjId="g_1623580_0" Pin1InfoVect2LinkObjId="g_1637f30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-196 1040,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1637f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-366 1041,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1443e90@0" ObjectIDZND0="49018@x" ObjectIDZND1="48965@x" ObjectIDZND2="48962@x" Pin0InfoVect0LinkObjId="g_15fc4c0_0" Pin0InfoVect1LinkObjId="SW-317356_0" Pin0InfoVect2LinkObjId="SW-317353_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1443e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1019,-366 1041,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1638120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-339 1041,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49018@1" ObjectIDZND0="48965@x" ObjectIDZND1="48962@x" ObjectIDZND2="g_1443e90@0" Pin0InfoVect0LinkObjId="SW-317356_0" Pin0InfoVect1LinkObjId="SW-317353_0" Pin0InfoVect2LinkObjId="g_1443e90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1637f30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-339 1041,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_14399e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1067,-539 1041,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48963@0" ObjectIDZND0="48960@x" ObjectIDZND1="48961@x" Pin0InfoVect0LinkObjId="SW-317351_0" Pin0InfoVect1LinkObjId="SW-317352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1067,-539 1041,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1439bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-512 1041,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48960@1" ObjectIDZND0="48963@x" ObjectIDZND1="48961@x" Pin0InfoVect0LinkObjId="SW-317354_0" Pin0InfoVect1LinkObjId="SW-317352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317351_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-512 1041,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1439dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-539 1041,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="48963@x" ObjectIDND1="48960@x" ObjectIDZND0="48961@0" Pin0InfoVect0LinkObjId="SW-317352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317354_0" Pin1InfoVect1LinkObjId="SW-317351_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-539 1041,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_143a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-608 1041,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48961@1" ObjectIDZND0="49020@0" Pin0InfoVect0LinkObjId="g_166ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-608 1041,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_155f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1310,-725 1289,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48954@0" ObjectIDZND0="48952@x" ObjectIDZND1="48951@x" Pin0InfoVect0LinkObjId="SW-317336_0" Pin0InfoVect1LinkObjId="SW-317335_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1310,-725 1289,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_155fe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-704 1289,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48952@1" ObjectIDZND0="48954@x" ObjectIDZND1="48951@x" Pin0InfoVect0LinkObjId="SW-317338_0" Pin0InfoVect1LinkObjId="SW-317335_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-704 1289,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1509920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-725 1289,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48954@x" ObjectIDND1="48952@x" ObjectIDZND0="48951@0" Pin0InfoVect0LinkObjId="SW-317335_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317338_0" Pin1InfoVect1LinkObjId="SW-317336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-725 1289,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_166ef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1060,-689 1086,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="48958@0" ObjectIDZND0="49020@0" ObjectIDZND1="48957@x" Pin0InfoVect0LinkObjId="g_143a140_0" Pin0InfoVect1LinkObjId="SW-317348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1060,-689 1086,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15aac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1086,-647 1086,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49020@0" ObjectIDZND0="48958@x" ObjectIDZND1="48957@x" Pin0InfoVect0LinkObjId="SW-317349_0" Pin0InfoVect1LinkObjId="SW-317348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_143a140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1086,-647 1086,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15f30c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1086,-689 1086,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="48958@x" ObjectIDND1="49020@0" ObjectIDZND0="48957@0" Pin0InfoVect0LinkObjId="SW-317348_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317349_0" Pin1InfoVect1LinkObjId="g_143a140_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1086,-689 1086,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15f32e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1106,-771 1086,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48959@0" ObjectIDZND0="48957@x" ObjectIDZND1="g_153c6d0@0" ObjectIDZND2="g_153d980@0" Pin0InfoVect0LinkObjId="SW-317348_0" Pin0InfoVect1LinkObjId="g_153c6d0_0" Pin0InfoVect2LinkObjId="g_153d980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-771 1086,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15cd720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1086,-749 1086,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48957@1" ObjectIDZND0="48959@x" ObjectIDZND1="g_153c6d0@0" ObjectIDZND2="g_153d980@0" Pin0InfoVect0LinkObjId="SW-317350_0" Pin0InfoVect1LinkObjId="g_153c6d0_0" Pin0InfoVect2LinkObjId="g_153d980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317348_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1086,-749 1086,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15cd940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-826 1086,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_153c6d0@0" ObjectIDZND0="48959@x" ObjectIDZND1="48957@x" ObjectIDZND2="g_153d980@0" Pin0InfoVect0LinkObjId="SW-317350_0" Pin0InfoVect1LinkObjId="SW-317348_0" Pin0InfoVect2LinkObjId="g_153d980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_153c6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-826 1086,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15ce2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1086,-771 1086,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="48959@x" ObjectIDND1="48957@x" ObjectIDZND0="g_153c6d0@0" ObjectIDZND1="g_153d980@0" Pin0InfoVect0LinkObjId="g_153c6d0_0" Pin0InfoVect1LinkObjId="g_153d980_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317350_0" Pin1InfoVect1LinkObjId="SW-317348_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1086,-771 1086,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15ce4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1086,-826 1086,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_153c6d0@0" ObjectIDND1="48959@x" ObjectIDND2="48957@x" ObjectIDZND0="g_153d980@0" Pin0InfoVect0LinkObjId="g_153d980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_153c6d0_0" Pin1InfoVect1LinkObjId="SW-317350_0" Pin1InfoVect2LinkObjId="SW-317348_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1086,-826 1086,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15ce800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,-29 602,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48985@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_1657790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="602,-29 602,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1565710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,34 202,62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48973@0" ObjectIDZND0="48975@1" Pin0InfoVect0LinkObjId="SW-317367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="202,34 202,62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1565930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,-10 202,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48974@1" ObjectIDZND0="48973@1" Pin0InfoVect0LinkObjId="SW-317366_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317367_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="202,-10 202,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1589db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-305 904,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_14f4630@0" ObjectIDZND0="48966@x" ObjectIDZND1="49018@x" ObjectIDZND2="g_14f3e90@0" Pin0InfoVect0LinkObjId="SW-317357_0" Pin0InfoVect1LinkObjId="g_1637f30_0" Pin0InfoVect2LinkObjId="g_14f3e90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f4630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="904,-305 904,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_159e530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-1040 1289,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_158a190@0" ObjectIDZND0="g_159c2c0@0" ObjectIDZND1="0@x" ObjectIDZND2="48956@x" Pin0InfoVect0LinkObjId="g_159c2c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-317340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_158a190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-1040 1289,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15d4620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-1080 1289,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_159c2c0@0" ObjectIDZND0="g_158a190@0" ObjectIDZND1="48956@x" ObjectIDZND2="48953@x" Pin0InfoVect0LinkObjId="g_158a190_0" Pin0InfoVect1LinkObjId="SW-317340_0" Pin0InfoVect2LinkObjId="SW-317337_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_159c2c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-1080 1289,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15d50f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-1040 1289,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_158a190@0" ObjectIDND1="48956@x" ObjectIDND2="48953@x" ObjectIDZND0="g_159c2c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_159c2c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_158a190_0" Pin1InfoVect1LinkObjId="SW-317340_0" Pin1InfoVect2LinkObjId="SW-317337_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-1040 1289,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15d5350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-1080 1289,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_159c2c0@0" ObjectIDND1="g_158a190@0" ObjectIDND2="48956@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_159c2c0_0" Pin1InfoVect1LinkObjId="g_158a190_0" Pin1InfoVect2LinkObjId="SW-317340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-1080 1289,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15d55b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-967 1289,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="48956@0" ObjectIDZND0="48953@x" ObjectIDZND1="g_158a190@0" ObjectIDZND2="g_159c2c0@0" Pin0InfoVect0LinkObjId="SW-317337_0" Pin0InfoVect1LinkObjId="g_158a190_0" Pin0InfoVect2LinkObjId="g_159c2c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-967 1289,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15d6080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-938 1289,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="48953@1" ObjectIDZND0="48956@x" ObjectIDZND1="g_158a190@0" ObjectIDZND2="g_159c2c0@0" Pin0InfoVect0LinkObjId="SW-317340_0" Pin0InfoVect1LinkObjId="g_158a190_0" Pin0InfoVect2LinkObjId="g_159c2c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-938 1289,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15d62e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-967 1289,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="48956@x" ObjectIDND1="48953@x" ObjectIDZND0="g_158a190@0" ObjectIDZND1="g_159c2c0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_158a190_0" Pin0InfoVect1LinkObjId="g_159c2c0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317340_0" Pin1InfoVect1LinkObjId="SW-317337_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-967 1289,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_153b740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1311,-858 1289,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48955@0" ObjectIDZND0="48951@x" ObjectIDZND1="48953@x" Pin0InfoVect0LinkObjId="SW-317335_0" Pin0InfoVect1LinkObjId="SW-317337_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317339_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1311,-858 1289,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_153c210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-825 1289,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48951@1" ObjectIDZND0="48955@x" ObjectIDZND1="48953@x" Pin0InfoVect0LinkObjId="SW-317339_0" Pin0InfoVect1LinkObjId="SW-317337_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-825 1289,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_153c470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-858 1289,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="48955@x" ObjectIDND1="48951@x" ObjectIDZND0="48953@0" Pin0InfoVect0LinkObjId="SW-317337_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317339_0" Pin1InfoVect1LinkObjId="SW-317335_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-858 1289,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_16132f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-668 1289,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48952@0" ObjectIDZND0="49020@0" Pin0InfoVect0LinkObjId="g_143a140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-668 1289,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15dede0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-725 747,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48948@0" ObjectIDZND0="48946@x" ObjectIDZND1="48945@x" Pin0InfoVect0LinkObjId="SW-317323_0" Pin0InfoVect1LinkObjId="SW-317322_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="768,-725 747,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15df040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-704 747,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48946@1" ObjectIDZND0="48948@x" ObjectIDZND1="48945@x" Pin0InfoVect0LinkObjId="SW-317325_0" Pin0InfoVect1LinkObjId="SW-317322_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="747,-704 747,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_157f2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-725 747,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48948@x" ObjectIDND1="48946@x" ObjectIDZND0="48945@0" Pin0InfoVect0LinkObjId="SW-317322_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317325_0" Pin1InfoVect1LinkObjId="SW-317323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="747,-725 747,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1607490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,-1040 747,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_1512ca0@0" ObjectIDZND0="g_1539060@0" ObjectIDZND1="0@x" ObjectIDZND2="48950@x" Pin0InfoVect0LinkObjId="g_1539060_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-317327_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1512ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="699,-1040 747,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_16076f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="779,-1080 747,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1539060@0" ObjectIDZND0="g_1512ca0@0" ObjectIDZND1="48950@x" ObjectIDZND2="48947@x" Pin0InfoVect0LinkObjId="g_1512ca0_0" Pin0InfoVect1LinkObjId="SW-317327_0" Pin0InfoVect2LinkObjId="SW-317324_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1539060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="779,-1080 747,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1607950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-1040 747,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1512ca0@0" ObjectIDND1="48950@x" ObjectIDND2="48947@x" ObjectIDZND0="g_1539060@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1539060_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1512ca0_0" Pin1InfoVect1LinkObjId="SW-317327_0" Pin1InfoVect2LinkObjId="SW-317324_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="747,-1040 747,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1607bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-1080 747,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1539060@0" ObjectIDND1="g_1512ca0@0" ObjectIDND2="48950@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1539060_0" Pin1InfoVect1LinkObjId="g_1512ca0_0" Pin1InfoVect2LinkObjId="SW-317327_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="747,-1080 747,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1607e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="786,-967 747,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="48950@0" ObjectIDZND0="48947@x" ObjectIDZND1="g_1512ca0@0" ObjectIDZND2="g_1539060@0" Pin0InfoVect0LinkObjId="SW-317324_0" Pin0InfoVect1LinkObjId="g_1512ca0_0" Pin0InfoVect2LinkObjId="g_1539060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317327_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="786,-967 747,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1608070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-938 747,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="48947@1" ObjectIDZND0="48950@x" ObjectIDZND1="g_1512ca0@0" ObjectIDZND2="g_1539060@0" Pin0InfoVect0LinkObjId="SW-317327_0" Pin0InfoVect1LinkObjId="g_1512ca0_0" Pin0InfoVect2LinkObjId="g_1539060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="747,-938 747,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_16082d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-967 747,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="48950@x" ObjectIDND1="48947@x" ObjectIDZND0="g_1512ca0@0" ObjectIDZND1="g_1539060@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1512ca0_0" Pin0InfoVect1LinkObjId="g_1539060_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317327_0" Pin1InfoVect1LinkObjId="SW-317324_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="747,-967 747,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1608530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,-858 747,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48949@0" ObjectIDZND0="48945@x" ObjectIDZND1="48947@x" Pin0InfoVect0LinkObjId="SW-317322_0" Pin0InfoVect1LinkObjId="SW-317324_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="769,-858 747,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1608790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-825 747,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48945@1" ObjectIDZND0="48949@x" ObjectIDZND1="48947@x" Pin0InfoVect0LinkObjId="SW-317326_0" Pin0InfoVect1LinkObjId="SW-317324_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="747,-825 747,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_16089f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-858 747,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="48949@x" ObjectIDND1="48945@x" ObjectIDZND0="48947@0" Pin0InfoVect0LinkObjId="SW-317324_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317326_0" Pin1InfoVect1LinkObjId="SW-317322_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="747,-858 747,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1608c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-668 747,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48946@0" ObjectIDZND0="49020@0" Pin0InfoVect0LinkObjId="g_143a140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="747,-668 747,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15eecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-322 877,-322 877,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_14f4630@0" ObjectIDND1="49018@x" ObjectIDND2="g_14f3e90@0" ObjectIDZND0="48966@0" Pin0InfoVect0LinkObjId="SW-317357_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14f4630_0" Pin1InfoVect1LinkObjId="g_1637f30_0" Pin1InfoVect2LinkObjId="g_14f3e90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-322 877,-322 877,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_14f4ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-265 904,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="earth" ObjectIDND0="g_14f4630@1" ObjectIDZND0="48966@x" ObjectIDZND1="g_14f3e90@0" ObjectIDZND2="g_15fd420@0" Pin0InfoVect0LinkObjId="SW-317357_0" Pin0InfoVect1LinkObjId="g_14f3e90_0" Pin0InfoVect2LinkObjId="g_15fd420_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f4630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="904,-265 904,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15fc000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-266 877,-254 904,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="earth" ObjectIDND0="48966@1" ObjectIDZND0="g_14f4630@0" ObjectIDZND1="g_14f3e90@0" ObjectIDZND2="g_15fd420@0" Pin0InfoVect0LinkObjId="g_14f4630_0" Pin0InfoVect1LinkObjId="g_14f3e90_0" Pin0InfoVect2LinkObjId="g_15fd420_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="877,-266 877,-254 904,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15fc260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-254 934,-254 934,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="earth" EndDevType0="lightningRod" ObjectIDND0="g_14f4630@0" ObjectIDND1="48966@x" ObjectIDND2="g_15fd420@0" ObjectIDZND0="g_14f3e90@1" Pin0InfoVect0LinkObjId="g_14f3e90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14f4630_0" Pin1InfoVect1LinkObjId="SW-317357_0" Pin1InfoVect2LinkObjId="g_15fd420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-254 934,-254 934,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15fc4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="935,-322 1042,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="g_14f4630@0" ObjectIDND1="48966@x" ObjectIDND2="g_14f3e90@0" ObjectIDZND0="49018@x" Pin0InfoVect0LinkObjId="g_1637f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14f4630_0" Pin1InfoVect1LinkObjId="SW-317357_0" Pin1InfoVect2LinkObjId="g_14f3e90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="935,-322 1042,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15fcf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-322 935,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="g_14f4630@0" ObjectIDND1="48966@x" ObjectIDZND0="49018@x" ObjectIDZND1="g_14f3e90@0" Pin0InfoVect0LinkObjId="g_1637f30_0" Pin0InfoVect1LinkObjId="g_14f3e90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14f4630_0" Pin1InfoVect1LinkObjId="SW-317357_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-322 935,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15fd1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="935,-322 935,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="49018@x" ObjectIDND1="g_14f4630@0" ObjectIDND2="48966@x" ObjectIDZND0="g_14f3e90@0" Pin0InfoVect0LinkObjId="g_14f3e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1637f30_0" Pin1InfoVect1LinkObjId="g_14f4630_0" Pin1InfoVect2LinkObjId="SW-317357_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="935,-322 935,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_15fde70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-238 904,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_15fd420@0" ObjectIDZND0="g_14f4630@0" ObjectIDZND1="48966@x" ObjectIDZND2="g_14f3e90@0" Pin0InfoVect0LinkObjId="g_14f4630_0" Pin0InfoVect1LinkObjId="SW-317357_0" Pin0InfoVect2LinkObjId="g_14f3e90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15fd420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="904,-238 904,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15fe0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-250 1193,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_15fe330@0" ObjectIDZND0="48967@x" ObjectIDZND1="49018@x" ObjectIDZND2="g_15e48b0@0" Pin0InfoVect0LinkObjId="SW-317358_0" Pin0InfoVect1LinkObjId="g_1637f30_0" Pin0InfoVect2LinkObjId="g_15e48b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15fe330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-250 1193,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15feba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-210 1193,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="earth" ObjectIDND0="g_15fe330@1" ObjectIDZND0="g_15e48b0@0" ObjectIDZND1="48967@x" ObjectIDZND2="g_15e3c00@0" Pin0InfoVect0LinkObjId="g_15e48b0_0" Pin0InfoVect1LinkObjId="SW-317358_0" Pin0InfoVect2LinkObjId="g_15e3c00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15fe330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-210 1193,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15fee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1166,-211 1166,-199 1193,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="earth" ObjectIDND0="g_15e48b0@1" ObjectIDZND0="g_15fe330@0" ObjectIDZND1="48967@x" ObjectIDZND2="g_15e3c00@0" Pin0InfoVect0LinkObjId="g_15fe330_0" Pin0InfoVect1LinkObjId="SW-317358_0" Pin0InfoVect2LinkObjId="g_15e3c00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15e48b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1166,-211 1166,-199 1193,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15ff060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-199 1223,-199 1223,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="earth" EndDevType0="switch" ObjectIDND0="g_15fe330@0" ObjectIDND1="g_15e48b0@0" ObjectIDND2="g_15e3c00@0" ObjectIDZND0="48967@1" Pin0InfoVect0LinkObjId="SW-317358_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15fe330_0" Pin1InfoVect1LinkObjId="g_15e48b0_0" Pin1InfoVect2LinkObjId="g_15e3c00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-199 1223,-199 1223,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15e4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-183 1193,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_15e3c00@0" ObjectIDZND0="g_15fe330@0" ObjectIDZND1="g_15e48b0@0" ObjectIDZND2="48967@x" Pin0InfoVect0LinkObjId="g_15fe330_0" Pin0InfoVect1LinkObjId="g_15e48b0_0" Pin0InfoVect2LinkObjId="SW-317358_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15e3c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-183 1193,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1622c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-267 1224,-267 1224,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49018@x" ObjectIDND1="g_15e48b0@0" ObjectIDND2="g_15fe330@0" ObjectIDZND0="48967@0" Pin0InfoVect0LinkObjId="SW-317358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1637f30_0" Pin1InfoVect1LinkObjId="g_15e48b0_0" Pin1InfoVect2LinkObjId="g_15fe330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-267 1224,-267 1224,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1622e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1166,-267 1111,-267 1079,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="48967@x" ObjectIDND1="g_15fe330@0" ObjectIDND2="g_15e48b0@0" ObjectIDZND0="49018@x" Pin0InfoVect0LinkObjId="g_1637f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317358_0" Pin1InfoVect1LinkObjId="g_15fe330_0" Pin1InfoVect2LinkObjId="g_15e48b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1166,-267 1111,-267 1079,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16230c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-267 1166,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="48967@x" ObjectIDND1="g_15fe330@0" ObjectIDZND0="49018@x" ObjectIDZND1="g_15e48b0@0" Pin0InfoVect0LinkObjId="g_1637f30_0" Pin0InfoVect1LinkObjId="g_15e48b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317358_0" Pin1InfoVect1LinkObjId="g_15fe330_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-267 1166,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1623320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1166,-267 1166,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="49018@x" ObjectIDND1="48967@x" ObjectIDND2="g_15fe330@0" ObjectIDZND0="g_15e48b0@0" Pin0InfoVect0LinkObjId="g_15e48b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1637f30_0" Pin1InfoVect1LinkObjId="SW-317358_0" Pin1InfoVect2LinkObjId="g_15fe330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1166,-267 1166,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152aef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,96 203,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48976@0" ObjectIDZND0="48975@x" ObjectIDZND1="g_15c2de0@0" ObjectIDZND2="g_1625060@0" Pin0InfoVect0LinkObjId="SW-317367_0" Pin0InfoVect1LinkObjId="g_15c2de0_0" Pin0InfoVect2LinkObjId="g_1625060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="219,96 203,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152b150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,76 203,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48975@0" ObjectIDZND0="g_15c2de0@0" ObjectIDZND1="g_1625060@0" ObjectIDZND2="48976@x" Pin0InfoVect0LinkObjId="g_15c2de0_0" Pin0InfoVect1LinkObjId="g_1625060_0" Pin0InfoVect2LinkObjId="SW-317368_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="203,76 203,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152b3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,112 203,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_15c2de0@0" ObjectIDZND0="48975@x" ObjectIDZND1="48976@x" ObjectIDZND2="g_1625060@0" Pin0InfoVect0LinkObjId="SW-317367_0" Pin0InfoVect1LinkObjId="SW-317368_0" Pin0InfoVect2LinkObjId="g_1625060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c2de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="185,112 203,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152b610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,96 203,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48975@x" ObjectIDND1="48976@x" ObjectIDZND0="g_15c2de0@0" ObjectIDZND1="g_1625060@0" Pin0InfoVect0LinkObjId="g_15c2de0_0" Pin0InfoVect1LinkObjId="g_1625060_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317367_0" Pin1InfoVect1LinkObjId="SW-317368_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="203,96 203,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152b870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,112 203,138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="48975@x" ObjectIDND1="48976@x" ObjectIDND2="g_15c2de0@0" ObjectIDZND0="g_1625060@0" Pin0InfoVect0LinkObjId="g_1625060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317367_0" Pin1InfoVect1LinkObjId="SW-317368_0" Pin1InfoVect2LinkObjId="g_15c2de0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="203,112 203,138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152bad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="619,91 603,91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="48987@0" ObjectIDZND0="48986@x" ObjectIDZND1="0@x" ObjectIDZND2="g_15c3b90@0" Pin0InfoVect0LinkObjId="SW-317380_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_15c3b90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="619,91 603,91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152bd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="603,74 603,91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48986@0" ObjectIDZND0="0@x" ObjectIDZND1="g_15c3b90@0" ObjectIDZND2="48987@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_15c3b90_0" Pin0InfoVect2LinkObjId="SW-317381_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="603,74 603,91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152bf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="585,107 603,107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_15c3b90@0" ObjectIDZND0="48986@x" ObjectIDZND1="48987@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-317380_0" Pin0InfoVect1LinkObjId="SW-317381_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c3b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="585,107 603,107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152c1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="603,91 603,107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="48986@x" ObjectIDND1="48987@x" ObjectIDZND0="0@x" ObjectIDZND1="g_15c3b90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_15c3b90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317380_0" Pin1InfoVect1LinkObjId="SW-317381_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="603,91 603,107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152c450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="603,107 603,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="48986@x" ObjectIDND1="48987@x" ObjectIDND2="g_15c3b90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317380_0" Pin1InfoVect1LinkObjId="SW-317381_0" Pin1InfoVect2LinkObjId="g_15c3b90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="603,107 603,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15a3750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,35 790,63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48988@0" ObjectIDZND0="48990@1" Pin0InfoVect0LinkObjId="SW-317385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,35 790,63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15a39b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-9 790,8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48989@1" ObjectIDZND0="48988@1" Pin0InfoVect0LinkObjId="SW-317384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-9 790,8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1657790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-26 790,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48989@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_15ce800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-26 790,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1658600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,94 791,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="48991@0" ObjectIDZND0="48990@x" ObjectIDZND1="0@x" ObjectIDZND2="g_15c2060@0" Pin0InfoVect0LinkObjId="SW-317385_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_15c2060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="807,94 791,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1658860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,77 791,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48990@0" ObjectIDZND0="0@x" ObjectIDZND1="g_15c2060@0" ObjectIDZND2="48991@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_15c2060_0" Pin0InfoVect2LinkObjId="SW-317386_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="791,77 791,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1658ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,110 791,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_15c2060@0" ObjectIDZND0="48990@x" ObjectIDZND1="48991@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-317385_0" Pin0InfoVect1LinkObjId="SW-317386_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c2060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="773,110 791,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1658d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,94 791,110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="48990@x" ObjectIDND1="48991@x" ObjectIDZND0="0@x" ObjectIDZND1="g_15c2060@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_15c2060_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317385_0" Pin1InfoVect1LinkObjId="SW-317386_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="791,94 791,110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1658f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,110 791,193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="48990@x" ObjectIDND1="48991@x" ObjectIDND2="g_15c2060@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317385_0" Pin1InfoVect1LinkObjId="SW-317386_0" Pin1InfoVect2LinkObjId="g_15c2060_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,110 791,193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d0390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="972,34 972,62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48992@0" ObjectIDZND0="48994@1" Pin0InfoVect0LinkObjId="SW-317390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="972,34 972,62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d05f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="972,-10 972,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48993@1" ObjectIDZND0="48992@1" Pin0InfoVect0LinkObjId="SW-317389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="972,-10 972,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d3910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="972,-27 972,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48993@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_15ce800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="972,-27 972,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14bfb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,93 973,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="48995@0" ObjectIDZND0="48994@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1518e30@0" Pin0InfoVect0LinkObjId="SW-317390_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1518e30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="989,93 973,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14bfdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,76 973,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48994@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1518e30@0" ObjectIDZND2="48995@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1518e30_0" Pin0InfoVect2LinkObjId="SW-317391_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="973,76 973,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c0010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="955,109 973,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_1518e30@0" ObjectIDZND0="48994@x" ObjectIDZND1="48995@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-317390_0" Pin0InfoVect1LinkObjId="SW-317391_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1518e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="955,109 973,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c0270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,93 973,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="48994@x" ObjectIDND1="48995@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1518e30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1518e30_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317390_0" Pin1InfoVect1LinkObjId="SW-317391_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="973,93 973,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c04d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,109 973,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="48994@x" ObjectIDND1="48995@x" ObjectIDND2="g_1518e30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317390_0" Pin1InfoVect1LinkObjId="SW-317391_0" Pin1InfoVect2LinkObjId="g_1518e30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="973,109 973,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1528320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1365,31 1365,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48996@0" ObjectIDZND0="48998@1" Pin0InfoVect0LinkObjId="SW-317395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317394_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1365,31 1365,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1528580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1365,-13 1365,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48997@1" ObjectIDZND0="48996@1" Pin0InfoVect0LinkObjId="SW-317394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317395_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1365,-13 1365,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_151f520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1365,-30 1365,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48997@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_15ce800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1365,-30 1365,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_151c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-26 1162,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48971@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_15ce800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-26 1162,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_156c7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-9 1162,40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48971@1" ObjectIDZND0="48972@1" Pin0InfoVect0LinkObjId="SW-317365_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317365_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-9 1162,40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_156ca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1179,94 1162,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_155ebf0@0" ObjectIDZND0="48972@x" ObjectIDZND1="g_1568dd0@0" Pin0InfoVect0LinkObjId="SW-317365_0" Pin0InfoVect1LinkObjId="g_1568dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_155ebf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1179,94 1162,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_156d570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,57 1162,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48972@0" ObjectIDZND0="g_155ebf0@0" ObjectIDZND1="g_1568dd0@0" Pin0InfoVect0LinkObjId="g_155ebf0_0" Pin0InfoVect1LinkObjId="g_1568dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1162,57 1162,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_156d7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,94 1162,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_155ebf0@0" ObjectIDND1="48972@x" ObjectIDZND0="g_1568dd0@0" Pin0InfoVect0LinkObjId="g_1568dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_155ebf0_0" Pin1InfoVect1LinkObjId="SW-317365_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,94 1162,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_153f670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,143 1162,155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1568dd0@1" ObjectIDZND0="g_15695c0@0" Pin0InfoVect0LinkObjId="g_15695c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1568dd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1162,143 1162,155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_153f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1382,108 1366,108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49000@0" ObjectIDZND0="48998@x" ObjectIDZND1="g_1518080@0" ObjectIDZND2="g_1541390@0" Pin0InfoVect0LinkObjId="SW-317395_0" Pin0InfoVect1LinkObjId="g_1518080_0" Pin0InfoVect2LinkObjId="g_1541390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1382,108 1366,108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15403a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1343,87 1366,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1518080@0" ObjectIDZND0="48998@x" ObjectIDZND1="g_1541390@0" ObjectIDZND2="49000@x" Pin0InfoVect0LinkObjId="SW-317395_0" Pin0InfoVect1LinkObjId="g_1541390_0" Pin0InfoVect2LinkObjId="SW-317397_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1518080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1343,87 1366,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1540ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,73 1366,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48998@0" ObjectIDZND0="g_1541390@0" ObjectIDZND1="49000@x" ObjectIDZND2="g_1518080@0" Pin0InfoVect0LinkObjId="g_1541390_0" Pin0InfoVect1LinkObjId="SW-317397_0" Pin0InfoVect2LinkObjId="g_1518080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1366,73 1366,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1541130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,87 1366,108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48998@x" ObjectIDND1="g_1518080@0" ObjectIDZND0="g_1541390@0" ObjectIDZND1="49000@x" Pin0InfoVect0LinkObjId="g_1541390_0" Pin0InfoVect1LinkObjId="SW-317397_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317395_0" Pin1InfoVect1LinkObjId="g_1518080_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1366,87 1366,108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1542470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,108 1366,130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48998@x" ObjectIDND1="g_1518080@0" ObjectIDND2="49000@x" ObjectIDZND0="g_1541390@1" Pin0InfoVect0LinkObjId="g_1541390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317395_0" Pin1InfoVect1LinkObjId="g_1518080_0" Pin1InfoVect2LinkObjId="SW-317397_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,108 1366,130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15426d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,172 1366,172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_15172d0@0" ObjectIDZND0="g_1541390@0" ObjectIDZND1="48999@x" Pin0InfoVect0LinkObjId="g_1541390_0" Pin0InfoVect1LinkObjId="SW-317396_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15172d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1348,172 1366,172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1543200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,162 1366,172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1541390@0" ObjectIDZND0="48999@x" ObjectIDZND1="g_15172d0@0" Pin0InfoVect0LinkObjId="SW-317396_0" Pin0InfoVect1LinkObjId="g_15172d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1541390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1366,162 1366,172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f8f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,172 1366,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1541390@0" ObjectIDND1="g_15172d0@0" ObjectIDZND0="48999@1" Pin0InfoVect0LinkObjId="SW-317396_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1541390_0" Pin1InfoVect1LinkObjId="g_15172d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,172 1366,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f91e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,225 1366,242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48999@0" ObjectIDZND0="g_14f9440@0" Pin0InfoVect0LinkObjId="g_14f9440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,225 1366,242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fb380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,-27 202,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48974@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_15ce800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="202,-27 202,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fb5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-66 1040,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48969@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_15ce800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-66 1040,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1516320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-217 1040,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_1623580@0" ObjectIDZND0="g_1559060@0" ObjectIDZND1="48970@x" ObjectIDZND2="49018@x" Pin0InfoVect0LinkObjId="g_1559060_0" Pin0InfoVect1LinkObjId="SW-317360_0" Pin0InfoVect2LinkObjId="g_1637f30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1623580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-217 1040,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1516e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-196 1040,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="g_1559060@0" ObjectIDND1="48970@x" ObjectIDZND0="g_1623580@0" ObjectIDZND1="49018@x" Pin0InfoVect0LinkObjId="g_1623580_0" Pin0InfoVect1LinkObjId="g_1637f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1559060_0" Pin1InfoVect1LinkObjId="SW-317360_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-196 1040,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1517070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-217 1040,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_1623580@0" ObjectIDND1="g_1559060@0" ObjectIDND2="48970@x" ObjectIDZND0="49018@2" Pin0InfoVect0LinkObjId="g_1637f30_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1623580_0" Pin1InfoVect1LinkObjId="g_1559060_0" Pin1InfoVect2LinkObjId="SW-317360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-217 1040,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_146f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-332 1367,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_146e7d0@0" ObjectIDZND0="49018@x" ObjectIDZND1="49009@x" Pin0InfoVect0LinkObjId="g_1637f30_0" Pin0InfoVect1LinkObjId="SW-317425_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_146e7d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1367,-332 1367,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1470030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1096,-298 1367,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="49018@0" ObjectIDZND0="g_146e7d0@0" ObjectIDZND1="49009@x" Pin0InfoVect0LinkObjId="g_146e7d0_0" Pin0InfoVect1LinkObjId="SW-317425_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1637f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1096,-298 1367,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1470290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-298 1393,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_146e7d0@0" ObjectIDND1="49018@x" ObjectIDZND0="49009@0" Pin0InfoVect0LinkObjId="SW-317425_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_146e7d0_0" Pin1InfoVect1LinkObjId="g_1637f30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1367,-298 1393,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_158fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-291 1417,-247 1458,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-291 1417,-247 1458,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_156ec60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-298 1694,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="49014@x" ObjectIDND1="49010@x" ObjectIDND2="49012@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317430_0" Pin1InfoVect1LinkObjId="SW-317426_0" Pin1InfoVect2LinkObjId="SW-317428_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-298 1694,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15746e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1779,-270 1779,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49013@0" ObjectIDZND0="49010@x" ObjectIDZND1="49007@x" Pin0InfoVect0LinkObjId="SW-317426_0" Pin0InfoVect1LinkObjId="SW-317423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317429_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1779,-270 1779,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1574940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1763,-298 1779,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49010@1" ObjectIDZND0="49013@x" ObjectIDZND1="49007@x" Pin0InfoVect0LinkObjId="SW-317429_0" Pin0InfoVect1LinkObjId="SW-317423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1763,-298 1779,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1574ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1779,-298 1873,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49013@x" ObjectIDND1="49010@x" ObjectIDZND0="49007@1" Pin0InfoVect0LinkObjId="SW-317423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317429_0" Pin1InfoVect1LinkObjId="SW-317426_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1779,-298 1873,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15054a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1964,-276 1964,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49011@0" ObjectIDZND0="49007@x" ObjectIDZND1="49008@x" Pin0InfoVect0LinkObjId="SW-317423_0" Pin0InfoVect1LinkObjId="SW-317424_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1964,-276 1964,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1505700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-298 1964,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49007@0" ObjectIDZND0="49011@x" ObjectIDZND1="49008@x" Pin0InfoVect0LinkObjId="SW-317427_0" Pin0InfoVect1LinkObjId="SW-317424_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-298 1964,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1505960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1964,-298 1980,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49011@x" ObjectIDND1="49007@x" ObjectIDZND0="49008@0" Pin0InfoVect0LinkObjId="SW-317424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317427_0" Pin1InfoVect1LinkObjId="SW-317423_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1964,-298 1980,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1505bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2016,-298 2078,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49008@1" ObjectIDZND0="49022@0" Pin0InfoVect0LinkObjId="g_14c8de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317424_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2016,-298 2078,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15066b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-270 1462,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49012@0" ObjectIDZND0="49009@x" ObjectIDZND1="49014@x" ObjectIDZND2="49010@x" Pin0InfoVect0LinkObjId="SW-317425_0" Pin0InfoVect1LinkObjId="SW-317430_0" Pin0InfoVect2LinkObjId="SW-317426_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-270 1462,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1506910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-298 1429,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="49012@x" ObjectIDND1="49014@x" ObjectIDND2="49010@x" ObjectIDZND0="49009@1" Pin0InfoVect0LinkObjId="SW-317425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317428_0" Pin1InfoVect1LinkObjId="SW-317430_0" Pin1InfoVect2LinkObjId="SW-317426_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-298 1429,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1507c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-270 1694,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49014@0" ObjectIDZND0="49010@x" ObjectIDZND1="49012@x" ObjectIDZND2="49009@x" Pin0InfoVect0LinkObjId="SW-317426_0" Pin0InfoVect1LinkObjId="SW-317428_0" Pin0InfoVect2LinkObjId="SW-317425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-270 1694,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15089a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-298 1694,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49010@0" ObjectIDZND0="49014@x" ObjectIDZND1="49012@x" ObjectIDZND2="49009@x" Pin0InfoVect0LinkObjId="SW-317430_0" Pin0InfoVect1LinkObjId="SW-317428_0" Pin0InfoVect2LinkObjId="SW-317425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-298 1694,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1508c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-298 1462,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49014@x" ObjectIDND1="49010@x" ObjectIDND2="0@x" ObjectIDZND0="49012@x" ObjectIDZND1="49009@x" Pin0InfoVect0LinkObjId="SW-317428_0" Pin0InfoVect1LinkObjId="SW-317425_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317430_0" Pin1InfoVect1LinkObjId="SW-317426_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-298 1462,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14c8de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-559 2078,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49002@0" ObjectIDZND0="49022@0" Pin0InfoVect0LinkObjId="g_1505bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-559 2078,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14c9610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2181,-531 2181,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49004@0" ObjectIDZND0="49002@x" ObjectIDZND1="49001@x" Pin0InfoVect0LinkObjId="SW-317416_0" Pin0InfoVect1LinkObjId="SW-317415_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2181,-531 2181,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ca100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2275,-559 2181,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49001@1" ObjectIDZND0="49004@x" ObjectIDZND1="49002@x" Pin0InfoVect0LinkObjId="SW-317418_0" Pin0InfoVect1LinkObjId="SW-317416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2275,-559 2181,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ca360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2181,-559 2165,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49004@x" ObjectIDND1="49001@x" ObjectIDZND0="49002@1" Pin0InfoVect0LinkObjId="SW-317416_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317418_0" Pin1InfoVect1LinkObjId="SW-317415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2181,-559 2165,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16a74a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-531 2398,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49005@0" ObjectIDZND0="49001@x" ObjectIDZND1="49003@x" Pin0InfoVect0LinkObjId="SW-317415_0" Pin0InfoVect1LinkObjId="SW-317417_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-531 2398,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16a7f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2398,-559 2302,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49005@x" ObjectIDND1="49003@x" ObjectIDZND0="49001@0" Pin0InfoVect0LinkObjId="SW-317415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317419_0" Pin1InfoVect1LinkObjId="SW-317417_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2398,-559 2302,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16a81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2432,-559 2398,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49003@0" ObjectIDZND0="49005@x" ObjectIDZND1="49001@x" Pin0InfoVect0LinkObjId="SW-317419_0" Pin0InfoVect1LinkObjId="SW-317415_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2432,-559 2398,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16ab520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2522,-534 2522,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="49006@0" ObjectIDZND0="49003@x" ObjectIDZND1="g_16acd30@0" ObjectIDZND2="g_14a12e0@0" Pin0InfoVect0LinkObjId="SW-317417_0" Pin0InfoVect1LinkObjId="g_16acd30_0" Pin0InfoVect2LinkObjId="g_14a12e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2522,-534 2522,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16ac210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2522,-559 2468,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49006@x" ObjectIDND1="g_16acd30@0" ObjectIDND2="g_14a12e0@0" ObjectIDZND0="49003@1" Pin0InfoVect0LinkObjId="SW-317417_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317420_0" Pin1InfoVect1LinkObjId="g_16acd30_0" Pin1InfoVect2LinkObjId="g_14a12e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2522,-559 2468,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16ac470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2522,-559 2522,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="capacitor" ObjectIDND0="49006@x" ObjectIDND1="49003@x" ObjectIDND2="g_16acd30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317420_0" Pin1InfoVect1LinkObjId="SW-317417_0" Pin1InfoVect2LinkObjId="g_16acd30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2522,-559 2522,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16b0520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,-511 2630,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_16acd30@0" ObjectIDZND0="49006@x" ObjectIDZND1="49003@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-317420_0" Pin0InfoVect1LinkObjId="SW-317417_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16acd30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2630,-511 2630,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16b1010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,-559 2522,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="earth" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_16acd30@0" ObjectIDND1="g_14a12e0@0" ObjectIDND2="g_16ac6d0@0" ObjectIDZND0="49006@x" ObjectIDZND1="49003@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-317420_0" Pin0InfoVect1LinkObjId="SW-317417_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16acd30_0" Pin1InfoVect1LinkObjId="g_14a12e0_0" Pin1InfoVect2LinkObjId="g_16ac6d0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2630,-559 2522,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16b2d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2221,-249 2221,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_16b1270@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16b1270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2221,-249 2221,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16b3580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2221,-218 2221,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="49022@0" ObjectIDZND1="49016@x" ObjectIDZND2="49015@x" Pin0InfoVect0LinkObjId="g_1505bc0_0" Pin0InfoVect1LinkObjId="SW-317433_0" Pin0InfoVect2LinkObjId="SW-317432_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2221,-218 2221,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16b4070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2221,-192 2078,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="49016@x" ObjectIDND2="49015@x" ObjectIDZND0="49022@0" Pin0InfoVect0LinkObjId="g_1505bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-317433_0" Pin1InfoVect2LinkObjId="SW-317432_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2221,-192 2078,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16b5db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2522,-616 2522,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_16b42d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16b42d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2522,-616 2522,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16b7af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-354 1694,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_16b6010@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16b6010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-354 1694,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16bb040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2261,-152 2261,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="busSection" EndDevType2="switch" ObjectIDND0="49016@0" ObjectIDZND0="0@x" ObjectIDZND1="49022@0" ObjectIDZND2="49015@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1505bc0_0" Pin0InfoVect2LinkObjId="SW-317432_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2261,-152 2261,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16bbb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2261,-192 2221,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" EndDevType1="busSection" ObjectIDND0="49016@x" ObjectIDND1="49015@x" ObjectIDZND0="0@x" ObjectIDZND1="49022@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1505bc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317433_0" Pin1InfoVect1LinkObjId="SW-317432_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2261,-192 2221,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d4ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2261,-192 2348,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="busSection" EndDevType0="switch" ObjectIDND0="49016@x" ObjectIDND1="0@x" ObjectIDND2="49022@0" ObjectIDZND0="49015@0" Pin0InfoVect0LinkObjId="SW-317432_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317433_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1505bc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2261,-192 2348,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d4f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2406,-159 2406,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="49017@0" ObjectIDZND0="49015@x" ObjectIDZND1="0@x" ObjectIDZND2="g_14d86e0@0" Pin0InfoVect0LinkObjId="SW-317432_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_14d86e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2406,-159 2406,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d59f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2384,-192 2406,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="49015@1" ObjectIDZND0="49017@x" ObjectIDZND1="0@x" ObjectIDZND2="g_14d86e0@0" Pin0InfoVect0LinkObjId="SW-317434_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_14d86e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317432_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2384,-192 2406,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d7730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-249 2451,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_14d5c50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d5c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2451,-249 2451,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d7990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-218 2451,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="49017@x" ObjectIDZND1="49015@x" ObjectIDZND2="g_14d86e0@0" Pin0InfoVect0LinkObjId="SW-317434_0" Pin0InfoVect1LinkObjId="SW-317432_0" Pin0InfoVect2LinkObjId="g_14d86e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2451,-218 2451,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d8480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2406,-192 2451,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="49017@x" ObjectIDND1="49015@x" ObjectIDZND0="0@x" ObjectIDZND1="g_14d86e0@0" ObjectIDZND2="g_14da3c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14d86e0_0" Pin0InfoVect2LinkObjId="g_14da3c0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317434_0" Pin1InfoVect1LinkObjId="SW-317432_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2406,-192 2451,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d9410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2517,-161 2517,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_14d86e0@0" ObjectIDZND0="0@x" ObjectIDZND1="49017@x" ObjectIDZND2="49015@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317434_0" Pin0InfoVect2LinkObjId="SW-317432_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d86e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2517,-161 2517,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14d9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2650,-192 2517,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_14da3c0@0" ObjectIDZND0="g_14d86e0@0" ObjectIDZND1="0@x" ObjectIDZND2="49017@x" Pin0InfoVect0LinkObjId="g_14d86e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-317434_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14da3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2650,-192 2517,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14da160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2517,-192 2451,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_14d86e0@0" ObjectIDND1="g_14da3c0@0" ObjectIDZND0="0@x" ObjectIDZND1="49017@x" ObjectIDZND2="49015@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317434_0" Pin0InfoVect2LinkObjId="SW-317432_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14d86e0_0" Pin1InfoVect1LinkObjId="g_14da3c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2517,-192 2451,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e2570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2203,73 2203,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2203,73 2203,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e27d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2297,45 2203,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2297,45 2203,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14e2a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2203,45 2187,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2203,45 2187,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ea1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2420,73 2420,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2420,73 2420,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ea400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2420,45 2324,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2420,45 2324,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ea660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2454,45 2420,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2454,45 2420,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14edb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,70 2544,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_14ee9d0@0" ObjectIDZND2="g_14a3000@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_14ee9d0_0" Pin0InfoVect2LinkObjId="g_14a3000_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2544,70 2544,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14eddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,45 2490,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_14ee9d0@0" ObjectIDND2="g_14a3000@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_14ee9d0_0" Pin1InfoVect2LinkObjId="g_14a3000_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,45 2490,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ee010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,45 2544,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_14ee9d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_14ee9d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,45 2544,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f2280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2652,93 2652,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_14ee9d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14ee9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2652,93 2652,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149c800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2652,45 2544,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="earth" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_14ee9d0@0" ObjectIDND1="g_14a3000@0" ObjectIDND2="g_14ee270@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14ee9d0_0" Pin1InfoVect1LinkObjId="g_14a3000_0" Pin1InfoVect2LinkObjId="g_14ee270_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2652,45 2544,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-12 2544,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_149ca60@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_149ca60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-12 2544,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14a0ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,45 2078,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="49022@0" Pin0InfoVect0LinkObjId="g_1505bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2151,45 2078,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14a2050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2711,-605 2711,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_14a12e0@0" ObjectIDZND0="g_16acd30@0" ObjectIDZND1="49006@x" ObjectIDZND2="49003@x" Pin0InfoVect0LinkObjId="g_16acd30_0" Pin0InfoVect1LinkObjId="SW-317420_0" Pin0InfoVect2LinkObjId="SW-317417_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a12e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2711,-605 2711,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14a2b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,-559 2711,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="earth" ObjectIDND0="g_16acd30@0" ObjectIDND1="49006@x" ObjectIDND2="49003@x" ObjectIDZND0="g_14a12e0@0" ObjectIDZND1="g_16ac6d0@0" Pin0InfoVect0LinkObjId="g_14a12e0_0" Pin0InfoVect1LinkObjId="g_16ac6d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16acd30_0" Pin1InfoVect1LinkObjId="SW-317420_0" Pin1InfoVect2LinkObjId="SW-317417_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2630,-559 2711,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14a2da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2711,-559 2788,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="earth" ObjectIDND0="g_14a12e0@0" ObjectIDND1="g_16acd30@0" ObjectIDND2="49006@x" ObjectIDZND0="g_16ac6d0@0" Pin0InfoVect0LinkObjId="g_16ac6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14a12e0_0" Pin1InfoVect1LinkObjId="g_16acd30_0" Pin1InfoVect2LinkObjId="SW-317420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2711,-559 2788,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a3d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2736,10 2736,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_14a3000@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a3000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2736,10 2736,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a4860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2652,45 2736,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="earth" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_14a3000@0" ObjectIDZND1="g_14ee270@0" Pin0InfoVect0LinkObjId="g_14a3000_0" Pin0InfoVect1LinkObjId="g_14ee270_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2652,45 2736,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14a4ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2736,45 2810,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="earth" ObjectIDND0="g_14a3000@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_14ee270@0" Pin0InfoVect0LinkObjId="g_14ee270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14a3000_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2736,45 2810,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ab350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,4 395,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48978@1" ObjectIDZND0="48977@1" Pin0InfoVect0LinkObjId="SW-317371_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,4 395,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ab5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,41 395,53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48977@0" ObjectIDZND0="48979@1" Pin0InfoVect0LinkObjId="SW-317372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,41 395,53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b0720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-13 395,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48978@0" ObjectIDZND0="49021@0" Pin0InfoVect0LinkObjId="g_15ce800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,-13 395,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b45e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,394 395,372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="48981@x" Pin0InfoVect0LinkObjId="SW-317374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,394 395,372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b4840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,352 352,372 395,372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" EndDevType1="breaker" ObjectIDZND0="0@x" ObjectIDZND1="48981@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317374_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="352,352 352,372 395,372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b4aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,372 395,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDZND0="48981@0" Pin0InfoVect0LinkObjId="SW-317374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,372 395,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14b8800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="389,247 348,247 348,271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="389,247 348,247 348,271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b8a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,278 395,278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48983@0" ObjectIDZND0="48982@x" Pin0InfoVect0LinkObjId="SW-317375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,278 395,278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b8cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,278 395,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48983@x" ObjectIDZND0="48982@0" Pin0InfoVect0LinkObjId="SW-317375_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,278 395,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1441f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1068,-458 1041,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48964@0" ObjectIDZND0="48962@x" ObjectIDZND1="48960@x" Pin0InfoVect0LinkObjId="SW-317353_0" Pin0InfoVect1LinkObjId="SW-317351_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1068,-458 1041,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1442a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-435 1042,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48962@1" ObjectIDZND0="48964@x" ObjectIDZND1="48960@x" Pin0InfoVect0LinkObjId="SW-317355_0" Pin0InfoVect1LinkObjId="SW-317351_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317353_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-435 1042,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1442c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-457 1041,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48964@x" ObjectIDND1="48962@x" ObjectIDZND0="48960@0" Pin0InfoVect0LinkObjId="SW-317351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317355_0" Pin1InfoVect1LinkObjId="SW-317353_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-457 1041,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1442ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-378 1041,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48965@0" ObjectIDZND0="49018@x" ObjectIDZND1="g_1443e90@0" ObjectIDZND2="48962@x" Pin0InfoVect0LinkObjId="g_1637f30_0" Pin0InfoVect1LinkObjId="g_1443e90_0" Pin0InfoVect2LinkObjId="SW-317353_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-378 1041,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_14439d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-366 1041,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49018@x" ObjectIDND1="g_1443e90@0" ObjectIDZND0="48965@x" ObjectIDZND1="48962@x" Pin0InfoVect0LinkObjId="SW-317356_0" Pin0InfoVect1LinkObjId="SW-317353_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1637f30_0" Pin1InfoVect1LinkObjId="g_1443e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-366 1041,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1443c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-378 1041,-391 1042,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="48965@x" ObjectIDND1="49018@x" ObjectIDND2="g_1443e90@0" ObjectIDZND0="48962@0" Pin0InfoVect0LinkObjId="SW-317353_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317356_0" Pin1InfoVect1LinkObjId="g_1637f30_0" Pin1InfoVect2LinkObjId="g_1443e90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-378 1041,-391 1042,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,125 395,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1447cb0@0" ObjectIDZND0="g_14b0980@0" ObjectIDZND1="48980@x" ObjectIDZND2="48979@x" Pin0InfoVect0LinkObjId="g_14b0980_0" Pin0InfoVect1LinkObjId="SW-317373_0" Pin0InfoVect2LinkObjId="SW-317372_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1447cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="382,125 395,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144be30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,137 395,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_14b0980@1" ObjectIDZND0="g_1447cb0@0" ObjectIDZND1="48980@x" ObjectIDZND2="48979@x" Pin0InfoVect0LinkObjId="g_1447cb0_0" Pin0InfoVect1LinkObjId="SW-317373_0" Pin0InfoVect2LinkObjId="SW-317372_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14b0980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="395,137 395,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144c090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,109 395,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48980@0" ObjectIDZND0="g_1447cb0@0" ObjectIDZND1="g_14b0980@0" ObjectIDZND2="48979@x" Pin0InfoVect0LinkObjId="g_1447cb0_0" Pin0InfoVect1LinkObjId="g_14b0980_0" Pin0InfoVect2LinkObjId="SW-317372_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317373_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="416,109 395,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,125 395,109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1447cb0@0" ObjectIDND1="g_14b0980@0" ObjectIDZND0="48980@x" ObjectIDZND1="48979@x" Pin0InfoVect0LinkObjId="SW-317373_0" Pin0InfoVect1LinkObjId="SW-317372_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1447cb0_0" Pin1InfoVect1LinkObjId="g_14b0980_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="395,125 395,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144cde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,109 395,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="48980@x" ObjectIDND1="g_1447cb0@0" ObjectIDND2="g_14b0980@0" ObjectIDZND0="48979@0" Pin0InfoVect0LinkObjId="SW-317372_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317373_0" Pin1InfoVect1LinkObjId="g_1447cb0_0" Pin1InfoVect2LinkObjId="g_14b0980_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,109 395,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144d670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,278 395,336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="48982@x" ObjectIDND1="48983@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317375_0" Pin1InfoVect1LinkObjId="SW-317376_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="395,278 395,336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144d860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="381,209 395,209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14b7a50@0" ObjectIDZND0="48982@x" ObjectIDZND1="g_14b0980@0" Pin0InfoVect0LinkObjId="SW-317375_0" Pin0InfoVect1LinkObjId="g_14b0980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14b7a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="381,209 395,209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,231 395,209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48982@1" ObjectIDZND0="g_14b7a50@0" ObjectIDZND1="g_14b0980@0" Pin0InfoVect0LinkObjId="g_14b7a50_0" Pin0InfoVect1LinkObjId="g_14b0980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317375_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="395,231 395,209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144e3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,209 395,186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_14b7a50@0" ObjectIDND1="48982@x" ObjectIDZND0="g_14b0980@0" Pin0InfoVect0LinkObjId="g_14b0980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14b7a50_0" Pin1InfoVect1LinkObjId="SW-317375_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,209 395,186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,454 395,436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,454 395,436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,327 352,305 395,305 395,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="48981@1" Pin0InfoVect0LinkObjId="SW-317374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="352,327 352,305 395,305 395,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1456d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="396,440 396,454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="396,440 396,454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1456fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="396,454 396,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="396,454 396,479 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="49021" cx="602" cy="-48" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49021" cx="790" cy="-48" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49021" cx="972" cy="-48" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49021" cx="1365" cy="-48" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49021" cx="1162" cy="-48" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49021" cx="202" cy="-48" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49020" cx="1041" cy="-647" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49020" cx="1086" cy="-647" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49020" cx="1289" cy="-647" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49020" cx="747" cy="-647" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49022" cx="2078" cy="-298" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49022" cx="2078" cy="-559" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49022" cx="2078" cy="-192" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49022" cx="2078" cy="45" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49021" cx="395" cy="-48" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-317252" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -66.000000 -988.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48930" ObjectName="DYN-CX_LJZ"/>
     <cge:Meas_Ref ObjectId="317252"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1662120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1602310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1602310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1602310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1602310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1602310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1602310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1602310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_15eacc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -1084.500000) translate(0,16)">李家庄光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_118cc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.500000 225.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_118cc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.500000 225.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_166fab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 158.000000 259.000000) translate(0,15)">#1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1558d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.987002 -130.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1561700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -1185.000000) translate(0,17)">220kV仁庄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15889c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 551.000000) translate(0,15)">#1无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15889c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 551.000000) translate(0,33)">34MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15cd550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 975.500000 -937.000000) translate(0,15)">220kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15cd550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 975.500000 -937.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1589890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -340.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1589a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -626.000000) translate(0,12)">220kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1511ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -1176.000000) translate(0,17)">220kV杨庄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16242f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1188.000000 -324.000000) translate(0,12)">至110kV部分</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a4d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2501.987002 -478.000000) translate(0,12)">12167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a54c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2868.987002 -543.000000) translate(0,12)">备用(110kV桂花变)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a6600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.987002 -193.000000) translate(0,12)">110kVⅠ母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a7670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2859.987002 89.000000) translate(0,12)">预留间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143bf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1997.000000 -817.000000) translate(0,12)">110kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143c550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -68.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143cb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 835.000000 -288.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143cdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 -819.000000) translate(0,12)">271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143d000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -693.000000) translate(0,12)">2711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143d240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -751.000000) translate(0,12)">27117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143d480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -927.000000) translate(0,12)">2716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143d6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -884.000000) translate(0,12)">27160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143d900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 788.000000 -993.000000) translate(0,12)">27167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143db40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1097.000000 -738.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143dd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1017.000000 -715.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143dfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -797.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143e200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1299.000000 -819.000000) translate(0,12)">272</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143e440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1300.000000 -693.000000) translate(0,12)">2721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143e680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1313.000000 -751.000000) translate(0,12)">27217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143e8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1313.000000 -884.000000) translate(0,12)">27260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143eb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1300.000000 -927.000000) translate(0,12)">2726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143ed40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -993.000000) translate(0,12)">27267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 -506.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143f1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1052.000000 -597.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_143f400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1053.000000 -424.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1444c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -565.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1445270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -484.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14454b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -404.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14456f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -240.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1445930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -324.000000) translate(0,12)">1013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1445b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 -254.000000) translate(0,12)">10137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1445db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1703.000000 -254.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1445ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1731.000000 -324.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1446230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.000000 -254.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1446470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1875.000000 -322.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14466b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -324.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14468f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1973.000000 -260.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1446b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 212.000000 13.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1446d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 70.000000) translate(0,12)">37147</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1447370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 404.000000 20.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14475f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 332.000000) translate(0,12)">372Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1447830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 241.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1447a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 317.000000 252.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144d040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 83.000000) translate(0,12)">37247</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_144e870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 245.000000) translate(0,15)">李家庄集电线Ⅰ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1450140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1336.000000 317.000000) translate(0,15)">#1接地用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1450c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 243.000000) translate(0,15)">李家庄集电线Ⅱ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1451120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 242.000000) translate(0,15)">李家庄集电线Ⅲ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14516c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 611.000000 11.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 622.000000 65.000000) translate(0,12)">37347</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 799.000000 14.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 810.000000 68.000000) translate(0,12)">37447</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.000000 13.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 67.000000) translate(0,12)">37547</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 9.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14526a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 10.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14528e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 82.000000) translate(0,12)">37647</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 199.000000) translate(0,12)">3760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2350.000000 -218.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2270.000000 -136.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14531e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2415.000000 -143.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1453420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -583.000000) translate(0,12)">121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1453660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2132.000000 -585.000000) translate(0,12)">1211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14538a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 -515.000000) translate(0,12)">12117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1453ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2434.000000 -585.000000) translate(0,12)">1216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1453d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2407.000000 -515.000000) translate(0,12)">12160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1453f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2531.000000 -518.000000) translate(0,12)">12167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb7180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -207.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb6e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -192.000000) translate(0,12)">温度:</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="914" x2="1050" y1="-1223" y2="-1223"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="442" x2="442" y1="480" y2="533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="344" x2="344" y1="480" y2="533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="344" x2="442" y1="533" y2="533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="380" x2="423" y1="489" y2="489"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="423" x2="423" y1="489" y2="524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="423" x2="381" y1="524" y2="524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="426" x2="420" y1="501" y2="501"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="380" x2="380" y1="529" y2="518"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="380" x2="380" y1="484" y2="495"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="367" x2="367" y1="498" y2="517"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="360" x2="360" y1="503" y2="514"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="360" x2="352" y1="508" y2="508"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="380" x2="367" y1="518" y2="510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="380" x2="367" y1="495" y2="502"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="344" x2="343" y1="507" y2="507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="467" x2="442" y1="529" y2="529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="442" x2="467" y1="485" y2="485"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="467" x2="467" y1="485" y2="500"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="467" x2="467" y1="513" y2="529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="453" x2="491" y1="513" y2="513"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="344" x2="442" y1="480" y2="480"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-317380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.089769 -4.941176)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48985" ObjectName="SW-CX_LJZ.CX_LJZ_373XC"/>
     <cge:Meas_Ref ObjectId="317380"/>
    <cge:TPSR_Ref TObjectID="48985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.861921 81.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48986" ObjectName="SW-CX_LJZ.CX_LJZ_373XC1"/>
     <cge:Meas_Ref ObjectId="317380"/>
    <cge:TPSR_Ref TObjectID="48986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1050.214850 -179.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48970" ObjectName="SW-CX_LJZ.CX_LJZ_301XC1"/>
     <cge:Meas_Ref ObjectId="317360"/>
    <cge:TPSR_Ref TObjectID="48970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1049.987002 -89.811765)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48969" ObjectName="SW-CX_LJZ.CX_LJZ_301XC"/>
     <cge:Meas_Ref ObjectId="317360"/>
    <cge:TPSR_Ref TObjectID="48969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317353">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1036.987002 -394.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48962" ObjectName="SW-CX_LJZ.CX_LJZ_2016SW"/>
     <cge:Meas_Ref ObjectId="317353"/>
    <cge:TPSR_Ref TObjectID="48962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317352">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1035.987002 -567.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48961" ObjectName="SW-CX_LJZ.CX_LJZ_2011SW"/>
     <cge:Meas_Ref ObjectId="317352"/>
    <cge:TPSR_Ref TObjectID="48961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317355">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.987002 -451.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48964" ObjectName="SW-CX_LJZ.CX_LJZ_20160SW"/>
     <cge:Meas_Ref ObjectId="317355"/>
    <cge:TPSR_Ref TObjectID="48964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317354">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1061.987002 -532.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48963" ObjectName="SW-CX_LJZ.CX_LJZ_20117SW"/>
     <cge:Meas_Ref ObjectId="317354"/>
    <cge:TPSR_Ref TObjectID="48963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317336">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1284.475728 -663.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48952" ObjectName="SW-CX_LJZ.CX_LJZ_2721SW"/>
     <cge:Meas_Ref ObjectId="317336"/>
    <cge:TPSR_Ref TObjectID="48952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317338">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1305.475728 -718.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48954" ObjectName="SW-CX_LJZ.CX_LJZ_27217SW"/>
     <cge:Meas_Ref ObjectId="317338"/>
    <cge:TPSR_Ref TObjectID="48954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317337">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1284.475728 -897.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48953" ObjectName="SW-CX_LJZ.CX_LJZ_2726SW"/>
     <cge:Meas_Ref ObjectId="317337"/>
    <cge:TPSR_Ref TObjectID="48953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317339">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1306.475728 -851.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48955" ObjectName="SW-CX_LJZ.CX_LJZ_27260SW"/>
     <cge:Meas_Ref ObjectId="317339"/>
    <cge:TPSR_Ref TObjectID="48955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317348">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1080.611650 -708.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48957" ObjectName="SW-CX_LJZ.CX_LJZ_2901SW"/>
     <cge:Meas_Ref ObjectId="317348"/>
    <cge:TPSR_Ref TObjectID="48957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317350">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.611650 -764.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48959" ObjectName="SW-CX_LJZ.CX_LJZ_29017SW"/>
     <cge:Meas_Ref ObjectId="317350"/>
    <cge:TPSR_Ref TObjectID="48959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 192.461805 -2.941176)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48974" ObjectName="SW-CX_LJZ.CX_LJZ_371XC"/>
     <cge:Meas_Ref ObjectId="317367"/>
    <cge:TPSR_Ref TObjectID="48974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.233957 83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48975" ObjectName="SW-CX_LJZ.CX_LJZ_371XC1"/>
     <cge:Meas_Ref ObjectId="317367"/>
    <cge:TPSR_Ref TObjectID="48975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317357">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 872.000000 -307.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48966" ObjectName="SW-CX_LJZ.CX_LJZ_2010SW"/>
     <cge:Meas_Ref ObjectId="317357"/>
    <cge:TPSR_Ref TObjectID="48966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317340">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.475728 -960.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48956" ObjectName="SW-CX_LJZ.CX_LJZ_27267SW"/>
     <cge:Meas_Ref ObjectId="317340"/>
    <cge:TPSR_Ref TObjectID="48956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317349">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.611650 -682.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48958" ObjectName="SW-CX_LJZ.CX_LJZ_29010SW"/>
     <cge:Meas_Ref ObjectId="317349"/>
    <cge:TPSR_Ref TObjectID="48958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317323">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 741.708738 -663.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48946" ObjectName="SW-CX_LJZ.CX_LJZ_2711SW"/>
     <cge:Meas_Ref ObjectId="317323"/>
    <cge:TPSR_Ref TObjectID="48946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317325">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 762.708738 -718.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48948" ObjectName="SW-CX_LJZ.CX_LJZ_27117SW"/>
     <cge:Meas_Ref ObjectId="317325"/>
    <cge:TPSR_Ref TObjectID="48948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317324">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 741.708738 -897.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48947" ObjectName="SW-CX_LJZ.CX_LJZ_2716SW"/>
     <cge:Meas_Ref ObjectId="317324"/>
    <cge:TPSR_Ref TObjectID="48947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317326">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.708738 -851.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48949" ObjectName="SW-CX_LJZ.CX_LJZ_27160SW"/>
     <cge:Meas_Ref ObjectId="317326"/>
    <cge:TPSR_Ref TObjectID="48949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317327">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.708738 -960.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48950" ObjectName="SW-CX_LJZ.CX_LJZ_27167SW"/>
     <cge:Meas_Ref ObjectId="317327"/>
    <cge:TPSR_Ref TObjectID="48950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317358">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1219.000000 -256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48967" ObjectName="SW-CX_LJZ.CX_LJZ_1010SW"/>
     <cge:Meas_Ref ObjectId="317358"/>
    <cge:TPSR_Ref TObjectID="48967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.614155 -1.941176)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48989" ObjectName="SW-CX_LJZ.CX_LJZ_374XC"/>
     <cge:Meas_Ref ObjectId="317385"/>
    <cge:TPSR_Ref TObjectID="48989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.386307 84.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48990" ObjectName="SW-CX_LJZ.CX_LJZ_374XC1"/>
     <cge:Meas_Ref ObjectId="317385"/>
    <cge:TPSR_Ref TObjectID="48990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317390">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.222704 -2.941176)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48993" ObjectName="SW-CX_LJZ.CX_LJZ_375XC"/>
     <cge:Meas_Ref ObjectId="317390"/>
    <cge:TPSR_Ref TObjectID="48993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317390">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.994856 83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48994" ObjectName="SW-CX_LJZ.CX_LJZ_375XC1"/>
     <cge:Meas_Ref ObjectId="317390"/>
    <cge:TPSR_Ref TObjectID="48994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.951662 -5.941176)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48997" ObjectName="SW-CX_LJZ.CX_LJZ_376XC"/>
     <cge:Meas_Ref ObjectId="317395"/>
    <cge:TPSR_Ref TObjectID="48997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.723814 80.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48998" ObjectName="SW-CX_LJZ.CX_LJZ_376XC1"/>
     <cge:Meas_Ref ObjectId="317395"/>
    <cge:TPSR_Ref TObjectID="48998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317365">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1152.696594 -1.941176)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48971" ObjectName="SW-CX_LJZ.CX_LJZ_3901XC"/>
     <cge:Meas_Ref ObjectId="317365"/>
    <cge:TPSR_Ref TObjectID="48971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317365">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1152.468745 64.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48972" ObjectName="SW-CX_LJZ.CX_LJZ_3901XC1"/>
     <cge:Meas_Ref ObjectId="317365"/>
    <cge:TPSR_Ref TObjectID="48972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317396">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1360.951662 230.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48999" ObjectName="SW-CX_LJZ.CX_LJZ_3760SW"/>
     <cge:Meas_Ref ObjectId="317396"/>
    <cge:TPSR_Ref TObjectID="48999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317368">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 214.000000 103.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48976" ObjectName="SW-CX_LJZ.CX_LJZ_37147SW"/>
     <cge:Meas_Ref ObjectId="317368"/>
    <cge:TPSR_Ref TObjectID="48976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 614.000000 98.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48987" ObjectName="SW-CX_LJZ.CX_LJZ_37347SW"/>
     <cge:Meas_Ref ObjectId="317381"/>
    <cge:TPSR_Ref TObjectID="48987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.000000 101.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48991" ObjectName="SW-CX_LJZ.CX_LJZ_37447SW"/>
     <cge:Meas_Ref ObjectId="317386"/>
    <cge:TPSR_Ref TObjectID="48991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317391">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 100.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48995" ObjectName="SW-CX_LJZ.CX_LJZ_37547SW"/>
     <cge:Meas_Ref ObjectId="317391"/>
    <cge:TPSR_Ref TObjectID="48995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 115.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49000" ObjectName="SW-CX_LJZ.CX_LJZ_37647SW"/>
     <cge:Meas_Ref ObjectId="317397"/>
    <cge:TPSR_Ref TObjectID="49000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317425">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1388.000000 -293.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49009" ObjectName="SW-CX_LJZ.CX_LJZ_1013SW"/>
     <cge:Meas_Ref ObjectId="317425"/>
    <cge:TPSR_Ref TObjectID="49009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317428">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -219.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49012" ObjectName="SW-CX_LJZ.CX_LJZ_10137SW"/>
     <cge:Meas_Ref ObjectId="317428"/>
    <cge:TPSR_Ref TObjectID="49012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317430">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1685.000000 -219.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49014" ObjectName="SW-CX_LJZ.CX_LJZ_10167SW"/>
     <cge:Meas_Ref ObjectId="317430"/>
    <cge:TPSR_Ref TObjectID="49014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317426">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.000000 -293.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49010" ObjectName="SW-CX_LJZ.CX_LJZ_1016SW"/>
     <cge:Meas_Ref ObjectId="317426"/>
    <cge:TPSR_Ref TObjectID="49010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317429">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1770.000000 -219.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49013" ObjectName="SW-CX_LJZ.CX_LJZ_10160SW"/>
     <cge:Meas_Ref ObjectId="317429"/>
    <cge:TPSR_Ref TObjectID="49013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317427">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1955.000000 -225.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49011" ObjectName="SW-CX_LJZ.CX_LJZ_10117SW"/>
     <cge:Meas_Ref ObjectId="317427"/>
    <cge:TPSR_Ref TObjectID="49011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317424">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1975.000000 -293.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49008" ObjectName="SW-CX_LJZ.CX_LJZ_1011SW"/>
     <cge:Meas_Ref ObjectId="317424"/>
    <cge:TPSR_Ref TObjectID="49008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317418">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2172.000000 -480.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49004" ObjectName="SW-CX_LJZ.CX_LJZ_12117SW"/>
     <cge:Meas_Ref ObjectId="317418"/>
    <cge:TPSR_Ref TObjectID="49004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317416">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2124.000000 -554.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49002" ObjectName="SW-CX_LJZ.CX_LJZ_1211SW"/>
     <cge:Meas_Ref ObjectId="317416"/>
    <cge:TPSR_Ref TObjectID="49002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317419">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2389.000000 -480.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49005" ObjectName="SW-CX_LJZ.CX_LJZ_12160SW"/>
     <cge:Meas_Ref ObjectId="317419"/>
    <cge:TPSR_Ref TObjectID="49005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317417">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2427.000000 -554.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49003" ObjectName="SW-CX_LJZ.CX_LJZ_1216SW"/>
     <cge:Meas_Ref ObjectId="317417"/>
    <cge:TPSR_Ref TObjectID="49003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317420">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2513.000000 -483.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49006" ObjectName="SW-CX_LJZ.CX_LJZ_12167SW"/>
     <cge:Meas_Ref ObjectId="317420"/>
    <cge:TPSR_Ref TObjectID="49006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317433">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2252.000000 -101.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49016" ObjectName="SW-CX_LJZ.CX_LJZ_19010SW"/>
     <cge:Meas_Ref ObjectId="317433"/>
    <cge:TPSR_Ref TObjectID="49016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317434">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2397.000000 -108.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49017" ObjectName="SW-CX_LJZ.CX_LJZ_19017SW"/>
     <cge:Meas_Ref ObjectId="317434"/>
    <cge:TPSR_Ref TObjectID="49017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317432">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2343.000000 -187.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49015" ObjectName="SW-CX_LJZ.CX_LJZ_1901SW"/>
     <cge:Meas_Ref ObjectId="317432"/>
    <cge:TPSR_Ref TObjectID="49015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2194.000000 124.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 50.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2411.000000 124.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2449.000000 50.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2535.000000 121.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317372">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 384.867233 77.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48979" ObjectName="SW-CX_LJZ.CX_LJZ_372XC1"/>
     <cge:Meas_Ref ObjectId="317372"/>
    <cge:TPSR_Ref TObjectID="48979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317372">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 384.867233 11.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48978" ObjectName="SW-CX_LJZ.CX_LJZ_372XC"/>
     <cge:Meas_Ref ObjectId="317372"/>
    <cge:TPSR_Ref TObjectID="48978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317375">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 399.740650 272.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48982" ObjectName="SW-CX_LJZ.CX_LJZ_3726SW"/>
     <cge:Meas_Ref ObjectId="317375"/>
    <cge:TPSR_Ref TObjectID="48982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317376">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.987002 285.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48983" ObjectName="SW-CX_LJZ.CX_LJZ_37267SW"/>
     <cge:Meas_Ref ObjectId="317376"/>
    <cge:TPSR_Ref TObjectID="48983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317356">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.987002 -371.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48965" ObjectName="SW-CX_LJZ.CX_LJZ_20167SW"/>
     <cge:Meas_Ref ObjectId="317356"/>
    <cge:TPSR_Ref TObjectID="48965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317373">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.000000 116.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48980" ObjectName="SW-CX_LJZ.CX_LJZ_37247SW"/>
     <cge:Meas_Ref ObjectId="317373"/>
    <cge:TPSR_Ref TObjectID="48980"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.580645 -0.000000 -0.000000 0.666667 2230.000000 -231.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.580645 -0.000000 -0.000000 0.666667 2531.000000 -598.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.580645 -0.000000 -0.000000 0.666667 1703.000000 -339.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.580645 -0.000000 -0.000000 0.666667 2460.000000 -231.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.580645 -0.000000 -0.000000 0.666667 2553.000000 7.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1559060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 999.987002 -142.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_159c2c0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.475728 -1072.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_153c6d0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1108.611650 -818.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1539060">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 774.708738 -1072.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f3e90">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 927.000000 -262.000000)" xlink:href="#lightningRod:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f4630">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 899.000000 -310.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15fe330">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1188.000000 -255.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e48b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 -203.000000)" xlink:href="#lightningRod:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1623580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1049.987002 -209.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1625060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.461805 243.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_155ebf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.696594 103.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1568dd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.696594 148.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1541390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.951662 167.000000)" xlink:href="#lightningRod:shape200"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f9440">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1360.951662 286.000000)" xlink:href="#lightningRod:shape24"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15172d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.951662 179.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1518080">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.951662 94.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1518e30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.222704 116.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c2060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.614155 117.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c2de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 127.461805 119.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c3b90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 527.614155 114.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_146e7d0">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1375.500000 -327.487002)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16b1270">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 2214.000000 -287.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16b42d0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 2515.000000 -654.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16b6010">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1687.000000 -392.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d5c50">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 2444.000000 -287.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d86e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2509.987002 -107.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149ca60">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 2537.000000 -50.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a12e0">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2719.500000 -600.487002)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a3000">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2744.500000 14.512998)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b0980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 389.740650 190.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b7a50">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 323.740650 216.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1443e90">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -359.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1447cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 324.614155 132.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-317474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -758.000000) translate(0,12)">317474.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317474" ObjectName="CX_LJZ.CX_LJZ_2ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49020"/>
     <cge:Term_Ref ObjectID="48434"/>
    <cge:TPSR_Ref TObjectID="49020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-317475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -758.000000) translate(0,27)">317475.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317475" ObjectName="CX_LJZ.CX_LJZ_2ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49020"/>
     <cge:Term_Ref ObjectID="48434"/>
    <cge:TPSR_Ref TObjectID="49020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-317476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -758.000000) translate(0,42)">317476.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317476" ObjectName="CX_LJZ.CX_LJZ_2ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49020"/>
     <cge:Term_Ref ObjectID="48434"/>
    <cge:TPSR_Ref TObjectID="49020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-317480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -758.000000) translate(0,57)">317480.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317480" ObjectName="CX_LJZ.CX_LJZ_2ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49020"/>
     <cge:Term_Ref ObjectID="48434"/>
    <cge:TPSR_Ref TObjectID="49020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-317477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -758.000000) translate(0,72)">317477.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317477" ObjectName="CX_LJZ.CX_LJZ_2ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49020"/>
     <cge:Term_Ref ObjectID="48434"/>
    <cge:TPSR_Ref TObjectID="49020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-317481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -758.000000) translate(0,87)">317481.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317481" ObjectName="CX_LJZ.CX_LJZ_2ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49020"/>
     <cge:Term_Ref ObjectID="48434"/>
    <cge:TPSR_Ref TObjectID="49020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-317482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 154.000000 -173.000000) translate(0,12)">317482.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317482" ObjectName="CX_LJZ.CX_LJZ_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49021"/>
     <cge:Term_Ref ObjectID="48435"/>
    <cge:TPSR_Ref TObjectID="49021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-317483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 154.000000 -173.000000) translate(0,27)">317483.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317483" ObjectName="CX_LJZ.CX_LJZ_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49021"/>
     <cge:Term_Ref ObjectID="48435"/>
    <cge:TPSR_Ref TObjectID="49021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-317484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 154.000000 -173.000000) translate(0,42)">317484.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317484" ObjectName="CX_LJZ.CX_LJZ_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49021"/>
     <cge:Term_Ref ObjectID="48435"/>
    <cge:TPSR_Ref TObjectID="49021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-317488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 154.000000 -173.000000) translate(0,57)">317488.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317488" ObjectName="CX_LJZ.CX_LJZ_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49021"/>
     <cge:Term_Ref ObjectID="48435"/>
    <cge:TPSR_Ref TObjectID="49021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-317485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 154.000000 -173.000000) translate(0,72)">317485.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317485" ObjectName="CX_LJZ.CX_LJZ_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49021"/>
     <cge:Term_Ref ObjectID="48435"/>
    <cge:TPSR_Ref TObjectID="49021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-317489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 154.000000 -173.000000) translate(0,87)">317489.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317489" ObjectName="CX_LJZ.CX_LJZ_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49021"/>
     <cge:Term_Ref ObjectID="48435"/>
    <cge:TPSR_Ref TObjectID="49021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-317536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2163.000000 -814.000000) translate(0,12)">317536.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317536" ObjectName="CX_LJZ.CX_LJZ_1ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49022"/>
     <cge:Term_Ref ObjectID="48436"/>
    <cge:TPSR_Ref TObjectID="49022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-317537" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2163.000000 -814.000000) translate(0,27)">317537.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317537" ObjectName="CX_LJZ.CX_LJZ_1ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49022"/>
     <cge:Term_Ref ObjectID="48436"/>
    <cge:TPSR_Ref TObjectID="49022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-317538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2163.000000 -814.000000) translate(0,42)">317538.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317538" ObjectName="CX_LJZ.CX_LJZ_1ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49022"/>
     <cge:Term_Ref ObjectID="48436"/>
    <cge:TPSR_Ref TObjectID="49022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-317542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2163.000000 -814.000000) translate(0,57)">317542.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317542" ObjectName="CX_LJZ.CX_LJZ_1ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49022"/>
     <cge:Term_Ref ObjectID="48436"/>
    <cge:TPSR_Ref TObjectID="49022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-317539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2163.000000 -814.000000) translate(0,72)">317539.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317539" ObjectName="CX_LJZ.CX_LJZ_1ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49022"/>
     <cge:Term_Ref ObjectID="48436"/>
    <cge:TPSR_Ref TObjectID="49022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-317543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2163.000000 -814.000000) translate(0,87)">317543.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317543" ObjectName="CX_LJZ.CX_LJZ_1ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49022"/>
     <cge:Term_Ref ObjectID="48436"/>
    <cge:TPSR_Ref TObjectID="49022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -801.000000) translate(0,12)">317445.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317445" ObjectName="CX_LJZ.CX_LJZ_271BK:F"/>
     <cge:PSR_Ref ObjectID="48945"/>
     <cge:Term_Ref ObjectID="48271"/>
    <cge:TPSR_Ref TObjectID="48945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -801.000000) translate(0,27)">317446.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317446" ObjectName="CX_LJZ.CX_LJZ_271BK:F"/>
     <cge:PSR_Ref ObjectID="48945"/>
     <cge:Term_Ref ObjectID="48271"/>
    <cge:TPSR_Ref TObjectID="48945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -801.000000) translate(0,42)">317442.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317442" ObjectName="CX_LJZ.CX_LJZ_271BK:F"/>
     <cge:PSR_Ref ObjectID="48945"/>
     <cge:Term_Ref ObjectID="48271"/>
    <cge:TPSR_Ref TObjectID="48945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1442.000000 -818.000000) translate(0,12)">317458.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317458" ObjectName="CX_LJZ.CX_LJZ_272BK:F"/>
     <cge:PSR_Ref ObjectID="48951"/>
     <cge:Term_Ref ObjectID="48283"/>
    <cge:TPSR_Ref TObjectID="48951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1442.000000 -818.000000) translate(0,27)">317459.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317459" ObjectName="CX_LJZ.CX_LJZ_272BK:F"/>
     <cge:PSR_Ref ObjectID="48951"/>
     <cge:Term_Ref ObjectID="48283"/>
    <cge:TPSR_Ref TObjectID="48951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1442.000000 -818.000000) translate(0,42)">317455.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317455" ObjectName="CX_LJZ.CX_LJZ_272BK:F"/>
     <cge:PSR_Ref ObjectID="48951"/>
     <cge:Term_Ref ObjectID="48283"/>
    <cge:TPSR_Ref TObjectID="48951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 -465.000000) translate(0,12)">317545.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317545" ObjectName="CX_LJZ.CX_LJZ_201BK:F"/>
     <cge:PSR_Ref ObjectID="48960"/>
     <cge:Term_Ref ObjectID="48301"/>
    <cge:TPSR_Ref TObjectID="48960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 -465.000000) translate(0,27)">317546.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317546" ObjectName="CX_LJZ.CX_LJZ_201BK:F"/>
     <cge:PSR_Ref ObjectID="48960"/>
     <cge:Term_Ref ObjectID="48301"/>
    <cge:TPSR_Ref TObjectID="48960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 -465.000000) translate(0,42)">317462.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317462" ObjectName="CX_LJZ.CX_LJZ_201BK:F"/>
     <cge:PSR_Ref ObjectID="48960"/>
     <cge:Term_Ref ObjectID="48301"/>
    <cge:TPSR_Ref TObjectID="48960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1866.000000 -433.000000) translate(0,12)">317533.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317533" ObjectName="CX_LJZ.CX_LJZ_101BK:F"/>
     <cge:PSR_Ref ObjectID="49007"/>
     <cge:Term_Ref ObjectID="48395"/>
    <cge:TPSR_Ref TObjectID="49007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1866.000000 -433.000000) translate(0,27)">317534.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317534" ObjectName="CX_LJZ.CX_LJZ_101BK:F"/>
     <cge:PSR_Ref ObjectID="49007"/>
     <cge:Term_Ref ObjectID="48395"/>
    <cge:TPSR_Ref TObjectID="49007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1866.000000 -433.000000) translate(0,42)">317530.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317530" ObjectName="CX_LJZ.CX_LJZ_101BK:F"/>
     <cge:PSR_Ref ObjectID="49007"/>
     <cge:Term_Ref ObjectID="48395"/>
    <cge:TPSR_Ref TObjectID="49007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2795.000000 -482.000000) translate(0,12)">317527.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317527" ObjectName="CX_LJZ.CX_LJZ_121BK:F"/>
     <cge:PSR_Ref ObjectID="49001"/>
     <cge:Term_Ref ObjectID="48383"/>
    <cge:TPSR_Ref TObjectID="49001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2795.000000 -482.000000) translate(0,27)">317528.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317528" ObjectName="CX_LJZ.CX_LJZ_121BK:F"/>
     <cge:PSR_Ref ObjectID="49001"/>
     <cge:Term_Ref ObjectID="48383"/>
    <cge:TPSR_Ref TObjectID="49001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2795.000000 -482.000000) translate(0,42)">317524.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317524" ObjectName="CX_LJZ.CX_LJZ_121BK:F"/>
     <cge:PSR_Ref ObjectID="49001"/>
     <cge:Term_Ref ObjectID="48383"/>
    <cge:TPSR_Ref TObjectID="49001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.000000 -132.000000) translate(0,12)">317467.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317467" ObjectName="CX_LJZ.CX_LJZ_301BK:F"/>
     <cge:PSR_Ref ObjectID="48968"/>
     <cge:Term_Ref ObjectID="48317"/>
    <cge:TPSR_Ref TObjectID="48968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.000000 -132.000000) translate(0,27)">317468.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317468" ObjectName="CX_LJZ.CX_LJZ_301BK:F"/>
     <cge:PSR_Ref ObjectID="48968"/>
     <cge:Term_Ref ObjectID="48317"/>
    <cge:TPSR_Ref TObjectID="48968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.000000 -132.000000) translate(0,42)">317464.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317464" ObjectName="CX_LJZ.CX_LJZ_301BK:F"/>
     <cge:PSR_Ref ObjectID="48968"/>
     <cge:Term_Ref ObjectID="48317"/>
    <cge:TPSR_Ref TObjectID="48968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 367.000000) translate(0,12)">317490.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317490" ObjectName="CX_LJZ.CX_LJZ_371BK:F"/>
     <cge:PSR_Ref ObjectID="48973"/>
     <cge:Term_Ref ObjectID="48327"/>
    <cge:TPSR_Ref TObjectID="48973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 367.000000) translate(0,27)">317491.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317491" ObjectName="CX_LJZ.CX_LJZ_371BK:F"/>
     <cge:PSR_Ref ObjectID="48973"/>
     <cge:Term_Ref ObjectID="48327"/>
    <cge:TPSR_Ref TObjectID="48973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 367.000000) translate(0,42)">317492.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317492" ObjectName="CX_LJZ.CX_LJZ_371BK:F"/>
     <cge:PSR_Ref ObjectID="48973"/>
     <cge:Term_Ref ObjectID="48327"/>
    <cge:TPSR_Ref TObjectID="48973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 291.000000 435.000000) translate(0,12)">317495.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317495" ObjectName="CX_LJZ.CX_LJZ_372BK:F"/>
     <cge:PSR_Ref ObjectID="48977"/>
     <cge:Term_Ref ObjectID="48335"/>
    <cge:TPSR_Ref TObjectID="48977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 291.000000 435.000000) translate(0,27)">317497.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317497" ObjectName="CX_LJZ.CX_LJZ_372BK:F"/>
     <cge:PSR_Ref ObjectID="48977"/>
     <cge:Term_Ref ObjectID="48335"/>
    <cge:TPSR_Ref TObjectID="48977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317500" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 367.000000) translate(0,12)">317500.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317500" ObjectName="CX_LJZ.CX_LJZ_373BK:F"/>
     <cge:PSR_Ref ObjectID="48984"/>
     <cge:Term_Ref ObjectID="48349"/>
    <cge:TPSR_Ref TObjectID="48984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 367.000000) translate(0,27)">317501.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317501" ObjectName="CX_LJZ.CX_LJZ_373BK:F"/>
     <cge:PSR_Ref ObjectID="48984"/>
     <cge:Term_Ref ObjectID="48349"/>
    <cge:TPSR_Ref TObjectID="48984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 367.000000) translate(0,42)">317502.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317502" ObjectName="CX_LJZ.CX_LJZ_373BK:F"/>
     <cge:PSR_Ref ObjectID="48984"/>
     <cge:Term_Ref ObjectID="48349"/>
    <cge:TPSR_Ref TObjectID="48984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 367.000000) translate(0,12)">317506.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317506" ObjectName="CX_LJZ.CX_LJZ_374BK:F"/>
     <cge:PSR_Ref ObjectID="48988"/>
     <cge:Term_Ref ObjectID="48357"/>
    <cge:TPSR_Ref TObjectID="48988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 367.000000) translate(0,27)">317507.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317507" ObjectName="CX_LJZ.CX_LJZ_374BK:F"/>
     <cge:PSR_Ref ObjectID="48988"/>
     <cge:Term_Ref ObjectID="48357"/>
    <cge:TPSR_Ref TObjectID="48988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 367.000000) translate(0,42)">317508.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317508" ObjectName="CX_LJZ.CX_LJZ_374BK:F"/>
     <cge:PSR_Ref ObjectID="48988"/>
     <cge:Term_Ref ObjectID="48357"/>
    <cge:TPSR_Ref TObjectID="48988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 367.000000) translate(0,12)">317512.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317512" ObjectName="CX_LJZ.CX_LJZ_375BK:F"/>
     <cge:PSR_Ref ObjectID="48992"/>
     <cge:Term_Ref ObjectID="48365"/>
    <cge:TPSR_Ref TObjectID="48992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 367.000000) translate(0,27)">317513.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317513" ObjectName="CX_LJZ.CX_LJZ_375BK:F"/>
     <cge:PSR_Ref ObjectID="48992"/>
     <cge:Term_Ref ObjectID="48365"/>
    <cge:TPSR_Ref TObjectID="48992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 367.000000) translate(0,42)">317514.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317514" ObjectName="CX_LJZ.CX_LJZ_375BK:F"/>
     <cge:PSR_Ref ObjectID="48992"/>
     <cge:Term_Ref ObjectID="48365"/>
    <cge:TPSR_Ref TObjectID="48992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1343.000000 367.000000) translate(0,12)">317518.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317518" ObjectName="CX_LJZ.CX_LJZ_376BK:F"/>
     <cge:PSR_Ref ObjectID="48996"/>
     <cge:Term_Ref ObjectID="48373"/>
    <cge:TPSR_Ref TObjectID="48996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317519" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1343.000000 367.000000) translate(0,27)">317519.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317519" ObjectName="CX_LJZ.CX_LJZ_376BK:F"/>
     <cge:PSR_Ref ObjectID="48996"/>
     <cge:Term_Ref ObjectID="48373"/>
    <cge:TPSR_Ref TObjectID="48996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1343.000000 367.000000) translate(0,42)">317520.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317520" ObjectName="CX_LJZ.CX_LJZ_376BK:F"/>
     <cge:PSR_Ref ObjectID="48996"/>
     <cge:Term_Ref ObjectID="48373"/>
    <cge:TPSR_Ref TObjectID="48996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-317470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -206.000000) translate(0,12)">317470.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317470" ObjectName="CX_LJZ.CX_LJZ_1T:MF"/>
     <cge:PSR_Ref ObjectID="49018"/>
     <cge:Term_Ref ObjectID="48429"/>
    <cge:TPSR_Ref TObjectID="49018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-317471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -206.000000) translate(0,27)">317471.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317471" ObjectName="CX_LJZ.CX_LJZ_1T:MF"/>
     <cge:PSR_Ref ObjectID="49018"/>
     <cge:Term_Ref ObjectID="48429"/>
    <cge:TPSR_Ref TObjectID="49018"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/></g>
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="380" cy="524" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="380" cy="490" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_158a190">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1188.475728 -1048.000000)" xlink:href="#voltageTransformer:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_153d980">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1050.611650 -855.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1512ca0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 645.708738 -1048.000000)" xlink:href="#voltageTransformer:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15695c0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1195.696594 150.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16acd30">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2622.021127 -515.500000)" xlink:href="#voltageTransformer:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14da3c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2617.000000 -131.000000)" xlink:href="#voltageTransformer:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ee9d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2644.021127 88.500000)" xlink:href="#voltageTransformer:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -267.000000 -1036.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -903.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -862.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="346" y="327"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(60,120,255)" stroke-width="1" width="39" x="452" y="500"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-255" y="-1095"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-304" y="-1112"/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.089769 211.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.475728 -1128.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.708738 -1128.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.614155 214.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.222704 213.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LJZ"/>
</svg>