<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-7" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2703 -1199 1782 942">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape204">
    <rect fill="none" height="18" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape205">
    <rect fill="rgb(0,255,0)" fillStyle="1" height="18" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape206">
    <rect fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape207">
    <rect fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="59" y2="67"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3140ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31dc190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31dcac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31dda80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31deaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31df580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31dfec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_31e0670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26c6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26c6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2784700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2784700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_316c4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_316c4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_316cfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_316ec30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_316f8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3170330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3170a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3172f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3172330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3172970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31e7d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31e8f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31e98f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31ea3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31eada0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31e2760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31e3090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31e4230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31e4e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31e62d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3059b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33e3d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33e0d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="952" width="1792" x="2698" y="-1204"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="440" stroke="rgb(0,205,0)" stroke-width="1.52996" width="680" x="3804" y="-1102"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="269" stroke="rgb(0,205,0)" stroke-width="1.86055" width="320" x="2704" y="-527"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="272" stroke="rgb(0,205,0)" stroke-width="1.83166" width="315" x="3458" y="-539"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="108" stroke="rgb(21,40,56)" stroke-width="1" width="320" x="3119" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="243" stroke="rgb(0,205,0)" stroke-width="1.78017" width="315" x="2705" y="-850"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="243" stroke="rgb(0,205,0)" stroke-width="1.78017" width="315" x="3458" y="-846"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-12645">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3326.712695 -991.888889)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1927" ObjectName="SW-CX_ZX.CX_ZX_3861SW"/>
     <cge:Meas_Ref ObjectId="12645"/>
    <cge:TPSR_Ref TObjectID="1927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-12646">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3282.712695 -940.851852)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1928" ObjectName="SW-CX_ZX.CX_ZX_38667SW"/>
     <cge:Meas_Ref ObjectId="12646"/>
    <cge:TPSR_Ref TObjectID="1928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-12643">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3103.259346 -989.962963)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1925" ObjectName="SW-CX_ZX.CX_ZX_3851SW"/>
     <cge:Meas_Ref ObjectId="12643"/>
    <cge:TPSR_Ref TObjectID="1925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-12644">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3059.259346 -938.925926)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1926" ObjectName="SW-CX_ZX.CX_ZX_38567SW"/>
     <cge:Meas_Ref ObjectId="12644"/>
    <cge:TPSR_Ref TObjectID="1926"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.540000 -0.000000 0.000000 -0.534979 3322.712695 -874.407407)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.540000 -0.000000 0.000000 -0.534979 3322.712695 -874.407407)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.540000 -0.000000 0.000000 -0.524280 3098.259346 -874.407407)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.540000 -0.000000 0.000000 -0.524280 3098.259346 -874.407407)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_28289d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3336,-920 3336,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="1928@x" ObjectIDZND1="1289@x" Pin0InfoVect0LinkObjId="SW-12646_0" Pin0InfoVect1LinkObjId="SW-7433_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3336,-920 3336,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_273a4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3336,-946 3324,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="1289@x" ObjectIDZND0="1928@1" Pin0InfoVect0LinkObjId="SW-12646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-7433_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3336,-946 3324,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27956a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3288,-946 3273,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="1928@0" ObjectIDZND0="g_2712a90@0" Pin0InfoVect0LinkObjId="g_2712a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3288,-946 3273,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2822d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3062,-944 3018,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="1926@0" ObjectIDZND0="g_2822f50@0" Pin0InfoVect0LinkObjId="g_2822f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3062,-944 3018,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2823580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-1028 3112,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1925@1" ObjectIDZND0="1144@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12643_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-1028 3112,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2823770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-980 3112,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1288@1" ObjectIDZND0="1925@0" Pin0InfoVect0LinkObjId="SW-12643_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-7423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-980 3112,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2823960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-944 3112,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="1926@x" ObjectIDND1="1288@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-12644_0" Pin1InfoVect1LinkObjId="SW-7423_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-944 3112,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2823b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-944 3112,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="1926@1" ObjectIDZND0="0@x" ObjectIDZND1="1288@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="SW-7423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-944 3112,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2823d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-944 3112,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="1926@x" ObjectIDZND0="1288@0" Pin0InfoVect0LinkObjId="SW-7423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-12644_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-944 3112,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2823f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3335,-1028 3335,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1927@1" ObjectIDZND0="1145@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12645_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3335,-1028 3335,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2824120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3336,-944 3336,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="1928@x" ObjectIDZND0="1289@0" Pin0InfoVect0LinkObjId="SW-7433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-12646_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3336,-944 3336,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27128a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3336,-982 3336,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1289@1" ObjectIDZND0="1927@0" Pin0InfoVect0LinkObjId="SW-12645_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-7433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3336,-982 3336,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2771bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-743 3112,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41446@2" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-743 3112,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27d1b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3339,-731 3339,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41449@2" Pin0InfoVect0LinkObjId="g_27f3670_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3339,-731 3339,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f3290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3280,-801 3280,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="transformer2" ObjectIDND0="41447@2" ObjectIDZND0="0@x" ObjectIDZND1="41446@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_2771bd0_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27f4560_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3280,-801 3280,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f3480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3280,-859 3280,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" BeginDevType2="breaker" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="breaker" ObjectIDND0="0@x" ObjectIDND1="41446@x" ObjectIDND2="41447@x" ObjectIDZND0="0@x" ObjectIDZND1="41446@x" ObjectIDZND2="41447@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_2771bd0_0" Pin0InfoVect2LinkObjId="g_27f4560_0" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_2771bd0_0" Pin1InfoVect2LinkObjId="g_27f4560_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3280,-859 3280,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f3670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3172,-803 3172,-843 3336,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="41448@2" ObjectIDZND0="41449@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_27d1b40_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27f3f90_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3172,-803 3172,-843 3336,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f3f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3336,-800 3336,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="41449@2" ObjectIDZND0="41448@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_27f4370_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27d1b40_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3336,-800 3336,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f4180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3336,-844 3336,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="41448@x" ObjectIDND1="41449@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27f3f90_0" Pin1InfoVect1LinkObjId="g_27d1b40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3336,-844 3336,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f4370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3172,-743 3172,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41448@2" Pin0InfoVect0LinkObjId="g_27f3f90_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3172,-743 3172,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f4560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3281,-732 3281,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41447@2" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3281,-732 3281,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f4750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-659 3145,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-659 3145,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f9920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-687 3112,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-687 3112,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fa3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-697 3112,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-697 3112,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fa650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-687 3112,-659 3144,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-687 3112,-659 3144,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fa8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3171,-687 3171,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3171,-687 3171,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fb380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-659 3171,-659 3171,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-659 3171,-659 3171,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fb5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3171,-687 3171,-697 3112,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3171,-687 3171,-697 3112,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2dbd8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3309,-414 3309,-659 3281,-659 3281,-696 3338,-696 3338,-659 3309,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3309,-414 3309,-659 3281,-659 3281,-696 3338,-696 3338,-659 3309,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34bcb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-859 3112,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" BeginDevType2="breaker" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="41446@x" ObjectIDND2="41447@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_2771bd0_0" Pin1InfoVect2LinkObjId="g_27f4560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-859 3112,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29dc5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3280,-859 3112,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" BeginDevType2="breaker" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="41446@x" ObjectIDND2="41447@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_2771bd0_0" Pin1InfoVect2LinkObjId="g_27f4560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3280,-859 3112,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29dc7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3112,-859 3112,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" BeginDevType2="breaker" ObjectIDND0="0@x" ObjectIDND1="41446@x" ObjectIDND2="41447@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_2771bd0_0" Pin1InfoVect2LinkObjId="g_27f4560_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3112,-859 3112,-801 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="1145" cx="3335" cy="-1073" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="1144" cx="3112" cy="-1074" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3145" cy="-416" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="36" qtmmishow="hidden" width="215" x="2851" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="36" opacity="0" stroke="white" transform="" width="215" x="2851" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="62" qtmmishow="hidden" width="63" x="2790" y="-1188"/>
    </a>
   <metadata/><rect fill="white" height="62" opacity="0" stroke="white" transform="" width="63" x="2790" y="-1188"/></g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_220kV_紫溪变电站.svg" style="fill-opacity:0"><rect height="36" qtmmishow="hidden" width="215" x="2851" y="-1177"/></g>
   <g href="楚雄地区_220kV_紫溪变电站.svg" style="fill-opacity:0"><rect height="62" qtmmishow="hidden" width="63" x="2790" y="-1188"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-7433">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3326.712695 -948.555556)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1289" ObjectName="SW-CX_ZX.CX_ZX_386BK"/>
     <cge:Meas_Ref ObjectId="7433"/>
    <cge:TPSR_Ref TObjectID="1289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-7423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3103.259346 -945.629630)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1288" ObjectName="SW-CX_ZX.CX_ZX_385BK"/>
     <cge:Meas_Ref ObjectId="7423"/>
    <cge:TPSR_Ref TObjectID="1288"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2822f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3000.259346 -937.962963)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2712a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 3254.712695 -939.888889)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175051" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -1061.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175051"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175050" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -1021.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175050"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175049" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -981.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175049"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175052" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -941.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175052"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175054" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -901.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175054"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175063" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -740.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175063"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175062" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -704.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175062"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175061" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -666.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175061"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175064" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -1061.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175064"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175057" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -861.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175057"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175060" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -820.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175060"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175053" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -780.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175053"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175066" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -1021.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175066"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175069" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -982.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175069"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175072" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -943.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175072"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175065" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -903.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175065"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175055" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3108.000000 -726.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175055"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175056" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3168.000000 -726.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175056"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175067" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3277.000000 -714.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175067"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175068" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3335.000000 -713.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175068"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175058" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3108.000000 -783.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175058"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175059" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3168.000000 -785.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175059"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175070" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3277.000000 -783.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175070"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-175071" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3335.000000 -783.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="175071"/>
    </metadata>
   </g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2705" x2="3020" y1="-811" y2="-811"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2706" x2="3019" y1="-773" y2="-773"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2706" x2="3019" y1="-728" y2="-728"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="0.993651" x1="2706" x2="3019" y1="-689" y2="-689"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2705" x2="3019" y1="-652" y2="-652"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2915" x2="2915" y1="-850" y2="-608"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3458" x2="3773" y1="-807" y2="-807"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3459" x2="3772" y1="-769" y2="-769"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3459" x2="3772" y1="-724" y2="-724"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="0.993651" x1="3459" x2="3772" y1="-685" y2="-685"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3458" x2="3772" y1="-648" y2="-648"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3664" x2="3664" y1="-846" y2="-604"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3458" x2="3773" y1="-500" y2="-500"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3459" x2="3772" y1="-462" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3459" x2="3772" y1="-417" y2="-417"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="0.993651" x1="3459" x2="3772" y1="-378" y2="-378"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3458" x2="3772" y1="-341" y2="-341"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3666" x2="3666" y1="-539" y2="-266"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3457" x2="3772" y1="-300" y2="-300"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2704" x2="3023" y1="-488" y2="-488"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2705" x2="3022" y1="-450" y2="-450"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2705" x2="3023" y1="-405" y2="-405"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2705" x2="3023" y1="-366" y2="-366"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2704" x2="3022" y1="-329" y2="-329"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2926" x2="2926" y1="-527" y2="-258"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="2703" x2="3023" y1="-294" y2="-294"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3804" x2="4483" y1="-1056" y2="-1056"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3803" x2="4482" y1="-1017" y2="-1017"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3804" x2="4483" y1="-976" y2="-976"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3803" x2="4482" y1="-937" y2="-937"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3805" x2="4484" y1="-896" y2="-896"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3803" x2="4482" y1="-857" y2="-857"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3805" x2="4484" y1="-816" y2="-816"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3804" x2="4483" y1="-775" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3805" x2="4484" y1="-736" y2="-736"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="3805" x2="4484" y1="-696" y2="-696"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4161" x2="4161" y1="-1101" y2="-663"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3281" x2="3281" y1="-714" y2="-687"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3338" x2="3338" y1="-713" y2="-688"/>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 2855.500000 -1125.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6675" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -399.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6675" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6688" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -412.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6688" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6672" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -363.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6672" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6685" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -371.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6685" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6673" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -323.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6673" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6686" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -331.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6686" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6674" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -283.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6674" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6687" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -291.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6687" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6669" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -523.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6669" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6682" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -531.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6682" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6670" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -483.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6670" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6683" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -491.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6683" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6671" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -443.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6671" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6684" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.000000 -451.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6684" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6663" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2932.000000 -839.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6663" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6666" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2932.000000 -719.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6666" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6676" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -838.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6676" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6664" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2932.000000 -797.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6664" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6667" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2932.000000 -679.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6667" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6677" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -798.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6677" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6665" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2932.000000 -759.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6665" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6668" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2932.000000 -639.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6668" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6678" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -758.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6678" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6679" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -718.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6679" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6680" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -678.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6680" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6681" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -638.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6681" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZX.CX_ZX_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3048,-1075 3176,-1075 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1144" ObjectName="BS-CX_ZX.CX_ZX_3IM"/>
    <cge:TPSR_Ref TObjectID="1144"/></metadata>
   <polyline fill="none" opacity="0" points="3048,-1075 3176,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_ZX.CX_ZX_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3272,-1074 3400,-1074 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1145" ObjectName="BS-CX_ZX.CX_ZX_3IIM"/>
    <cge:TPSR_Ref TObjectID="1145"/></metadata>
   <polyline fill="none" opacity="0" points="3272,-1074 3400,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3034,-416 3176,-416 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3034,-416 3176,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3272,-415 3400,-415 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3272,-415 3400,-415 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2879c30" transform="matrix(0.806878 -0.000000 -0.000000 0.885714 2892.134076 -1167.642857) translate(0,16)">站用变一次接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27130c0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3334.000000 -1100.703704) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2839930" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3059.000000 -1101.666667) translate(0,15)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2794340" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3062.259346 -937.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283d7c0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3116.259346 -1018.851852) translate(0,12)">3851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27f9340" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3131.000000 -900.407407) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f94b0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3344.712695 -976.481481) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f9760" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3347.712695 -1020.777778) translate(0,12)">3861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2713ce0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3278.712695 -938.925926) translate(0,12)">38667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2713e50" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3362.000000 -896.555556) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27f4940" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3093.000000 -409.481481) translate(0,15)">380VI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_282aae0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3361.000000 -408.481481) translate(0,15)">380VII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_282ae10" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3118.000000 -683.740741) translate(0,15)">1ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_282afc0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3288.000000 -683.740741) translate(0,15)">2ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_282b2f0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3121.259346 -974.555556) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_282b4a0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3065.259346 -800.148148) translate(0,12)">11QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_282b650" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3178.259346 -802.148148) translate(0,12)">12QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2841d80" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3231.259346 -803.148148) translate(0,12)">21QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28420b0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3351.259346 -803.148148) translate(0,12)">22QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2842260" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3081.259346 -718.407407) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2842410" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3257.259346 -718.407407) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2842740" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3180.259346 -718.407407) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28428f0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 3348.259346 -718.407407) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2843b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2998.000000 -401.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2808a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -409.000000) translate(0,15)">400VⅡ母零序电流3I0</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_28091e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -409.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2809390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2709.000000 -361.000000) translate(0,15)">400VⅠ母二次A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2809950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2998.000000 -361.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2809b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -369.000000) translate(0,15)">400VⅡ母二次A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2809f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -369.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_280a0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2709.000000 -321.000000) translate(0,15)">400VⅠ母二次B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_280a6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2998.000000 -321.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_280a860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -329.000000) translate(0,15)">400VⅡ母二次B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fc870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -329.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2709.000000 -281.000000) translate(0,15)">400VⅠ母二次C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2998.000000 -281.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fd190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -289.000000) translate(0,15)">400VⅡ母二次C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fd5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -289.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fd780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2709.000000 -401.000000) translate(0,15)">400VⅠ母零序电流3I0</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fd930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2709.000000 -521.000000) translate(0,15)">400VⅠ母A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fdf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2998.000000 -521.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fe0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -529.000000) translate(0,15)">400VⅡ母A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fe520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -529.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27fe6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2709.000000 -481.000000) translate(0,15)">400VⅠ母B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_28041b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2998.000000 -481.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2804360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -489.000000) translate(0,15)">400VⅡ母B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_28047a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -489.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2804950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2709.000000 -441.000000) translate(0,15)">400VⅠ母C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2804d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2998.000000 -441.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2804f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -449.000000) translate(0,15)">400VⅡ母C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2805380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -449.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2805530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2721.000000 -837.000000) translate(0,15)">1ATS进线1A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_28059f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2994.000000 -837.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2805ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2721.000000 -717.000000) translate(0,15)">1ATS进线2A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2805fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2994.000000 -717.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2806190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -838.000000) translate(0,15)">2ATS进线1A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279c280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -838.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279c430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2721.000000 -797.000000) translate(0,15)">1ATS进线1B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279c870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2994.000000 -797.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279ca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2721.000000 -677.000000) translate(0,15)">1ATS进线2B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279ce60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2994.000000 -677.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279d010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -798.000000) translate(0,15)">2ATS进线1B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279d450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -798.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279d600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2721.000000 -757.000000) translate(0,15)">1ATS进线1C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279da40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2994.000000 -757.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279dbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2721.000000 -637.000000) translate(0,15)">1ATS进线2C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_279e030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2994.000000 -637.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266c640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -758.000000) translate(0,15)">2ATS进线1C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266ca80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -758.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266cc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -718.000000) translate(0,15)">2ATS进线2A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266d070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -718.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266d220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -678.000000) translate(0,15)">2ATS进线2B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266d660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -678.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266d810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -638.000000) translate(0,15)">2ATS进线2C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_266dc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -638.000000) translate(0,15)"> V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_275ad60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -1085.000000) translate(0,15)">1号站用电源系统电源1电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26dd1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -1045.000000) translate(0,15)">1号站用电源系统电源2电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26deef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -1008.000000) translate(0,15)">1号站用电源系统母线电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_270b6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -967.000000) translate(0,15)">1号站用电源系统母线故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_278eab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -927.000000) translate(0,15)">1号站用电源系统装置故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27e3670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 -765.000000) translate(0,15)">2号站用电源系统电源1电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27e5230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 -730.000000) translate(0,15)">2号站用电源系统电源2电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27a6c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 -690.000000) translate(0,15)">2号站用电源系统母线电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_284e980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 -1083.000000) translate(0,15)">2号站用电源系统母线故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2850590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 -886.000000) translate(0,15)">1号站用电源系统1ATS在0位</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_281d660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 -846.000000) translate(0,15)">1号站用电源系统闭锁切换状态</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_26c46a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 -806.000000) translate(0,15)">1号站用电源系统装置动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27da8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -1043.000000) translate(0,15)">2号站用电源系统装置故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27d7520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -1004.000000) translate(0,15)">2号站用电源系统2ATS在0位</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27e7e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -964.000000) translate(0,15)">2号站用电源系统闭锁切换状态</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_27e9e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -924.000000) translate(0,15)">2号站用电源系统装置动作</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>