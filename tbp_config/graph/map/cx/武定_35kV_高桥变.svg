<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-175" aopId="3949070" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1240 2251 1167">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.592016" x1="11" x2="60" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="46" x2="27" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="46" x2="27" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="11" x2="11" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="36" x2="36" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="60" x2="60" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="69" x2="50" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="69" x2="50" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="20" x2="1" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="20" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="24" y2="32"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape51_0">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="97" y1="54" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="100" x2="94" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="98" x2="96" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="91" y1="54" y2="54"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape51_1">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape66_0">
    <circle cx="31" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="50" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="31,12 25,25 37,25 31,12 31,13 31,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="43" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="79" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape66_1">
    <circle cx="31" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,55 6,55 6,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="55" y2="60"/>
   </symbol>
   <symbol id="voltageTransformer:shape93">
    <rect height="24" stroke-width="0.379884" width="14" x="14" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="8" y2="11"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="75" y2="31"/>
   </symbol>
   <symbol id="voltageTransformer:shape73">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="22" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="28" y1="32" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="51" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="67" x2="59" y1="30" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="59" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2fe39f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2fe49e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fe5390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fe66a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fe7530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fe80d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2fe8c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2fe96b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ac7410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ac7410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2fec710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2fec710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2fee540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2fee540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2fef550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ff11e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ff1e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ff2d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ff35f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ff4fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ff5a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ff6310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ff6ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ff7bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ff8530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ff9020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ff99e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ffaed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ffb9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ffca40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ffd690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_300b9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ffef90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3000680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3001ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1177" width="2261" x="3110" y="-1245"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5357" x2="5366" y1="-506" y2="-506"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="41" stroke="rgb(255,255,0)" stroke-width="1" width="20" x="4342" y="-845"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="41" stroke="rgb(255,255,0)" stroke-width="1" width="20" x="4502" y="-855"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120889">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -892.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22527" ObjectName="SW-WD_GQ.WD_GQ_3011SW"/>
     <cge:Meas_Ref ObjectId="120889"/>
    <cge:TPSR_Ref TObjectID="22527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22533" ObjectName="SW-WD_GQ.WD_GQ_0016SW"/>
     <cge:Meas_Ref ObjectId="120915"/>
    <cge:TPSR_Ref TObjectID="22533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22534" ObjectName="SW-WD_GQ.WD_GQ_0011SW"/>
     <cge:Meas_Ref ObjectId="120916"/>
    <cge:TPSR_Ref TObjectID="22534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120905">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4301.000000 -890.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22529" ObjectName="SW-WD_GQ.WD_GQ_3901SW"/>
     <cge:Meas_Ref ObjectId="120905"/>
    <cge:TPSR_Ref TObjectID="22529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22540" ObjectName="SW-WD_GQ.WD_GQ_0612SW"/>
     <cge:Meas_Ref ObjectId="120960"/>
    <cge:TPSR_Ref TObjectID="22540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22542" ObjectName="SW-WD_GQ.WD_GQ_0616SW"/>
     <cge:Meas_Ref ObjectId="120962"/>
    <cge:TPSR_Ref TObjectID="22542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22541" ObjectName="SW-WD_GQ.WD_GQ_0611SW"/>
     <cge:Meas_Ref ObjectId="120961"/>
    <cge:TPSR_Ref TObjectID="22541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121066">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -356.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22564" ObjectName="SW-WD_GQ.WD_GQ_0676SW"/>
     <cge:Meas_Ref ObjectId="121066"/>
    <cge:TPSR_Ref TObjectID="22564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121070">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22566" ObjectName="SW-WD_GQ.WD_GQ_0671SW"/>
     <cge:Meas_Ref ObjectId="121070"/>
    <cge:TPSR_Ref TObjectID="22566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5160.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22530" ObjectName="SW-WD_GQ.WD_GQ_0901SW"/>
     <cge:Meas_Ref ObjectId="120906"/>
    <cge:TPSR_Ref TObjectID="22530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -886.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22536" ObjectName="SW-WD_GQ.WD_GQ_3021SW"/>
     <cge:Meas_Ref ObjectId="120928"/>
    <cge:TPSR_Ref TObjectID="22536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22538" ObjectName="SW-WD_GQ.WD_GQ_0026SW"/>
     <cge:Meas_Ref ObjectId="120948"/>
    <cge:TPSR_Ref TObjectID="22538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22539" ObjectName="SW-WD_GQ.WD_GQ_0021SW"/>
     <cge:Meas_Ref ObjectId="120949"/>
    <cge:TPSR_Ref TObjectID="22539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120978">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22544" ObjectName="SW-WD_GQ.WD_GQ_0622SW"/>
     <cge:Meas_Ref ObjectId="120978"/>
    <cge:TPSR_Ref TObjectID="22544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120980">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22546" ObjectName="SW-WD_GQ.WD_GQ_0626SW"/>
     <cge:Meas_Ref ObjectId="120980"/>
    <cge:TPSR_Ref TObjectID="22546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120979">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -452.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22545" ObjectName="SW-WD_GQ.WD_GQ_0621SW"/>
     <cge:Meas_Ref ObjectId="120979"/>
    <cge:TPSR_Ref TObjectID="22545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22548" ObjectName="SW-WD_GQ.WD_GQ_0632SW"/>
     <cge:Meas_Ref ObjectId="120996"/>
    <cge:TPSR_Ref TObjectID="22548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120998">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22550" ObjectName="SW-WD_GQ.WD_GQ_0636SW"/>
     <cge:Meas_Ref ObjectId="120998"/>
    <cge:TPSR_Ref TObjectID="22550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22549" ObjectName="SW-WD_GQ.WD_GQ_0631SW"/>
     <cge:Meas_Ref ObjectId="120997"/>
    <cge:TPSR_Ref TObjectID="22549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22552" ObjectName="SW-WD_GQ.WD_GQ_0642SW"/>
     <cge:Meas_Ref ObjectId="121014"/>
    <cge:TPSR_Ref TObjectID="22552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121016">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22554" ObjectName="SW-WD_GQ.WD_GQ_0646SW"/>
     <cge:Meas_Ref ObjectId="121016"/>
    <cge:TPSR_Ref TObjectID="22554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -451.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22553" ObjectName="SW-WD_GQ.WD_GQ_0641SW"/>
     <cge:Meas_Ref ObjectId="121015"/>
    <cge:TPSR_Ref TObjectID="22553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121032">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22556" ObjectName="SW-WD_GQ.WD_GQ_0652SW"/>
     <cge:Meas_Ref ObjectId="121032"/>
    <cge:TPSR_Ref TObjectID="22556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22558" ObjectName="SW-WD_GQ.WD_GQ_0656SW"/>
     <cge:Meas_Ref ObjectId="121034"/>
    <cge:TPSR_Ref TObjectID="22558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121033">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -451.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22557" ObjectName="SW-WD_GQ.WD_GQ_0651SW"/>
     <cge:Meas_Ref ObjectId="121033"/>
    <cge:TPSR_Ref TObjectID="22557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121050">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22560" ObjectName="SW-WD_GQ.WD_GQ_0662SW"/>
     <cge:Meas_Ref ObjectId="121050"/>
    <cge:TPSR_Ref TObjectID="22560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22562" ObjectName="SW-WD_GQ.WD_GQ_0666SW"/>
     <cge:Meas_Ref ObjectId="121052"/>
    <cge:TPSR_Ref TObjectID="22562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121051">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22561" ObjectName="SW-WD_GQ.WD_GQ_0661SW"/>
     <cge:Meas_Ref ObjectId="121051"/>
    <cge:TPSR_Ref TObjectID="22561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120904">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -884.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22528" ObjectName="SW-WD_GQ.WD_GQ_3621SW"/>
     <cge:Meas_Ref ObjectId="120904"/>
    <cge:TPSR_Ref TObjectID="22528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120908">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -997.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22531" ObjectName="SW-WD_GQ.WD_GQ_3616SW"/>
     <cge:Meas_Ref ObjectId="120908"/>
    <cge:TPSR_Ref TObjectID="22531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -998.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22525" ObjectName="SW-WD_GQ.WD_GQ_36167SW"/>
     <cge:Meas_Ref ObjectId="120885"/>
    <cge:TPSR_Ref TObjectID="22525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219833">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -873.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34392" ObjectName="SW-WD_GQ.WD_GQ_30117SW"/>
     <cge:Meas_Ref ObjectId="219833"/>
    <cge:TPSR_Ref TObjectID="34392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219834">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -881.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34393" ObjectName="SW-WD_GQ.WD_GQ_39017SW"/>
     <cge:Meas_Ref ObjectId="219834"/>
    <cge:TPSR_Ref TObjectID="34393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219835">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4539.000000 -864.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34394" ObjectName="SW-WD_GQ.WD_GQ_36217SW"/>
     <cge:Meas_Ref ObjectId="219835"/>
    <cge:TPSR_Ref TObjectID="34394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -876.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34395" ObjectName="SW-WD_GQ.WD_GQ_30217SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="34395"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_GQ.WD_GQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3834,-941 5090,-941 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22523" ObjectName="BS-WD_GQ.WD_GQ_3IM"/>
    <cge:TPSR_Ref TObjectID="22523"/></metadata>
   <polyline fill="none" opacity="0" points="3834,-941 5090,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_GQ.WD_GQ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3584,-508 5327,-508 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22524" ObjectName="BS-WD_GQ.WD_GQ_9IM"/>
    <cge:TPSR_Ref TObjectID="22524"/></metadata>
   <polyline fill="none" opacity="0" points="3584,-508 5327,-508 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_GQ.WD_GQ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -244.000000)" xlink:href="#capacitor:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39671" ObjectName="CB-WD_GQ.WD_GQ_Cb1"/>
    <cge:TPSR_Ref TObjectID="39671"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_GQ.WD_GQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31704"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -715.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -715.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22567" ObjectName="TF-WD_GQ.WD_GQ_1T"/>
    <cge:TPSR_Ref TObjectID="22567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_GQ.WD_GQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31708"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 -716.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 -716.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22568" ObjectName="TF-WD_GQ.WD_GQ_2T"/>
    <cge:TPSR_Ref TObjectID="22568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -707.000000)" xlink:href="#transformer2:shape66_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -707.000000)" xlink:href="#transformer2:shape66_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cc4d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -737.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d91f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d35120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d648e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5122.000000 -311.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2da3a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -287.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac9740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f88ac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac6300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dbae30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cf4560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4298.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d5d320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4572.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd0830">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aea8d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -287.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce2520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c072b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -1036.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3215.000000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200687" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3275.538462 -951.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200687" ObjectName="WD_GQ:WD_GQ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200688" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3275.538462 -909.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200688" ObjectName="WD_GQ:WD_GQ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-121093" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5153.000000 -777.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121093" ObjectName="WD_GQ:WD_GQ_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-220039" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5153.000000 -809.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="220039" ObjectName="WD_GQ:WD_GQ_2T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-213416" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -792.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213416" ObjectName="WD_GQ:WD_GQ_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-120829" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -763.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120829" ObjectName="WD_GQ:WD_GQ_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213570" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -601.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213570" ObjectName="WD_GQ:WD_GQ_9IM_3U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213569" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -580.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213569" ObjectName="WD_GQ:WD_GQ_9IM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213565" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5066.000000 -1043.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213565" ObjectName="WD_GQ:WD_GQ_3IM_3U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213564" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5067.000000 -1021.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213564" ObjectName="WD_GQ:WD_GQ_3IM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213561" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5066.000000 -1065.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213561" ObjectName="WD_GQ:WD_GQ_3IM_Uab1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213557" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -1086.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213557" ObjectName="WD_GQ:WD_GQ_3IM_Uc1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213556" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5063.000000 -1108.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213556" ObjectName="WD_GQ:WD_GQ_3IM_Ub1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213555" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5062.000000 -1131.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213555" ObjectName="WD_GQ:WD_GQ_3IM_Ua1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213566" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -623.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213566" ObjectName="WD_GQ:WD_GQ_9IM_Uab1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213560" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -647.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213560" ObjectName="WD_GQ:WD_GQ_9IM_Uc1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213559" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -668.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213559" ObjectName="WD_GQ:WD_GQ_9IM_Ub1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-213558" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -691.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213558" ObjectName="WD_GQ:WD_GQ_9IM_Ua1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200687" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3274.538462 -1027.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200687" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200687" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3274.538462 -988.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200687" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -614.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22532"/>
     <cge:Term_Ref ObjectID="31632"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -614.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22532"/>
     <cge:Term_Ref ObjectID="31632"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -614.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22532"/>
     <cge:Term_Ref ObjectID="31632"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4884.000000 -607.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22537"/>
     <cge:Term_Ref ObjectID="31642"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4884.000000 -607.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22537"/>
     <cge:Term_Ref ObjectID="31642"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4884.000000 -607.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22537"/>
     <cge:Term_Ref ObjectID="31642"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -136.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22563"/>
     <cge:Term_Ref ObjectID="31694"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -136.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22563"/>
     <cge:Term_Ref ObjectID="31694"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4753.000000 -136.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22563"/>
     <cge:Term_Ref ObjectID="31694"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -138.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22559"/>
     <cge:Term_Ref ObjectID="31686"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -138.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22559"/>
     <cge:Term_Ref ObjectID="31686"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -138.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22559"/>
     <cge:Term_Ref ObjectID="31686"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -138.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22555"/>
     <cge:Term_Ref ObjectID="31678"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -138.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22555"/>
     <cge:Term_Ref ObjectID="31678"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -138.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22555"/>
     <cge:Term_Ref ObjectID="31678"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.000000 -134.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22551"/>
     <cge:Term_Ref ObjectID="31670"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.000000 -134.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22551"/>
     <cge:Term_Ref ObjectID="31670"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.000000 -134.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22551"/>
     <cge:Term_Ref ObjectID="31670"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -133.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22547"/>
     <cge:Term_Ref ObjectID="31662"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -133.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22547"/>
     <cge:Term_Ref ObjectID="31662"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -133.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22547"/>
     <cge:Term_Ref ObjectID="31662"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120853" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -140.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22543"/>
     <cge:Term_Ref ObjectID="31654"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120854" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -140.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22543"/>
     <cge:Term_Ref ObjectID="31654"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -140.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22543"/>
     <cge:Term_Ref ObjectID="31654"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -891.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22526"/>
     <cge:Term_Ref ObjectID="31620"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120826" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -891.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22526"/>
     <cge:Term_Ref ObjectID="31620"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -891.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22526"/>
     <cge:Term_Ref ObjectID="31620"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-120827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -891.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22526"/>
     <cge:Term_Ref ObjectID="31620"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -896.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22535"/>
     <cge:Term_Ref ObjectID="31638"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -896.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22535"/>
     <cge:Term_Ref ObjectID="31638"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -896.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22535"/>
     <cge:Term_Ref ObjectID="31638"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-120844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -896.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22535"/>
     <cge:Term_Ref ObjectID="31638"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-213693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="213693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22565"/>
     <cge:Term_Ref ObjectID="31698"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22565"/>
     <cge:Term_Ref ObjectID="31698"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22565"/>
     <cge:Term_Ref ObjectID="31698"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-219825" prefix="P " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -1185.000000) translate(0,12)">P  219825.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219825" ObjectName="WD_GQ.WD_GQ_3616SW:F"/>
     <cge:PSR_Ref ObjectID="22531"/>
     <cge:Term_Ref ObjectID="31630"/>
    <cge:TPSR_Ref TObjectID="22531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-219826" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -1185.000000) translate(0,27)">Q  219826.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219826" ObjectName="WD_GQ.WD_GQ_3616SW:F"/>
     <cge:PSR_Ref ObjectID="22531"/>
     <cge:Term_Ref ObjectID="31630"/>
    <cge:TPSR_Ref TObjectID="22531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-219822" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -1185.000000) translate(0,42)">Ia   219822.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219822" ObjectName="WD_GQ.WD_GQ_3616SW:F"/>
     <cge:PSR_Ref ObjectID="22531"/>
     <cge:Term_Ref ObjectID="31630"/>
    <cge:TPSR_Ref TObjectID="22531"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="59" qtmmishow="hidden" width="203" x="3235" y="-1189"/>
    </a>
   <metadata/><rect fill="white" height="59" opacity="0" stroke="white" transform="" width="203" x="3235" y="-1189"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="89" qtmmishow="hidden" width="96" x="3167" y="-1215"/>
    </a>
   <metadata/><rect fill="white" height="89" opacity="0" stroke="white" transform="" width="96" x="3167" y="-1215"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="70" x="3961" y="-748"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="70" x="3961" y="-748"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="5001" y="-750"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="5001" y="-750"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="30" x="3732" y="-434"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="30" x="3732" y="-434"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="29" x="3940" y="-434"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="29" x="3940" y="-434"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4147" y="-434"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4147" y="-434"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="30" x="4355" y="-434"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="30" x="4355" y="-434"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="29" x="4563" y="-436"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="29" x="4563" y="-436"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="30" x="4770" y="-431"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="30" x="4770" y="-431"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4979" y="-430"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4979" y="-430"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3562" y="-1159"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3562" y="-1159"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3562" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3562" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="64" x="3179" y="-759"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="64" x="3179" y="-759"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3482,-1183 3479,-1186 3479,-1133 3482,-1136 3482,-1183" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3482,-1183 3479,-1186 3530,-1186 3527,-1183 3482,-1183" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3482,-1136 3479,-1133 3530,-1133 3527,-1136 3482,-1136" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3527,-1183 3530,-1186 3530,-1133 3527,-1136 3527,-1183" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3482" y="-1183"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3482" y="-1183"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="59" qtmmishow="hidden" width="203" x="3235" y="-1189"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="89" qtmmishow="hidden" width="96" x="3167" y="-1215"/></g>
   <g href="35kV高桥变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="70" x="3961" y="-748"/></g>
   <g href="35kV高桥变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="5001" y="-750"/></g>
   <g href="35kV高桥变10kV备用线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="30" x="3732" y="-434"/></g>
   <g href="35kV高桥变10kV机关线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="29" x="3940" y="-434"/></g>
   <g href="35kV高桥变10kV马鞍线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4147" y="-434"/></g>
   <g href="35kV高桥变10kV花桥线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="30" x="4355" y="-434"/></g>
   <g href="35kV高桥变10kV勒外线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="29" x="4563" y="-436"/></g>
   <g href="35kV高桥变10kV备用线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="30" x="4770" y="-431"/></g>
   <g href="35kV高桥变10kV电容器组067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4979" y="-430"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3562" y="-1159"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3562" y="-1194"/></g>
   <g href="35kV高桥变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="64" x="3179" y="-759"/></g>
   <g href="AVC高桥站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3482" y="-1183"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-674 3914,-659 3929,-659 3921,-674 3921,-674 3921,-674 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-686 3914,-701 3929,-701 3921,-686 3921,-686 3921,-686 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-320 3706,-305 3721,-305 3713,-320 3713,-320 3713,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-332 3706,-347 3721,-347 3713,-332 3713,-332 3713,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-317 4954,-302 4969,-302 4961,-317 4961,-317 4961,-317 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-329 4954,-344 4969,-344 4961,-329 4961,-329 4961,-329 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-675 4954,-660 4969,-660 4961,-675 4961,-675 4961,-675 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-687 4954,-702 4969,-702 4961,-687 4961,-687 4961,-687 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-319 3914,-304 3929,-304 3921,-319 3921,-319 3921,-319 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-331 3914,-346 3929,-346 3921,-331 3921,-331 3921,-331 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-320 4122,-305 4137,-305 4129,-320 4129,-320 4129,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-332 4122,-347 4137,-347 4129,-332 4129,-332 4129,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-320 4330,-305 4345,-305 4337,-320 4337,-320 4337,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-332 4330,-347 4345,-347 4337,-332 4337,-332 4337,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-320 4538,-305 4553,-305 4545,-320 4545,-320 4545,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-332 4538,-347 4553,-347 4545,-332 4545,-332 4545,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-319 4746,-304 4761,-304 4753,-319 4753,-319 4753,-319 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-331 4746,-346 4761,-346 4753,-331 4753,-331 4753,-331 " stroke="rgb(0,255,0)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d69a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5184.000000 -281.000000)" xlink:href="#voltageTransformer:shape93"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e61870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -670.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_GQ.GQ_061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -143.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33938" ObjectName="EC-WD_GQ.GQ_061Ld"/>
    <cge:TPSR_Ref TObjectID="33938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_GQ.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -142.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33925" ObjectName="EC-WD_GQ.062Ld"/>
    <cge:TPSR_Ref TObjectID="33925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_GQ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -143.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33926" ObjectName="EC-WD_GQ.063Ld"/>
    <cge:TPSR_Ref TObjectID="33926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_GQ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -143.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33927" ObjectName="EC-WD_GQ.064Ld"/>
    <cge:TPSR_Ref TObjectID="33927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_GQ.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -143.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33935" ObjectName="EC-WD_GQ.065Ld"/>
    <cge:TPSR_Ref TObjectID="33935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_GQ.GQ_066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -142.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33936" ObjectName="EC-WD_GQ.GQ_066Ld"/>
    <cge:TPSR_Ref TObjectID="33936"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2e19600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-608 3921,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22533@0" ObjectIDZND0="22532@1" Pin0InfoVect0LinkObjId="SW-120914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-608 3921,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d6c840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-569 3921,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22532@0" ObjectIDZND0="22534@1" Pin0InfoVect0LinkObjId="SW-120916_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-569 3921,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d42420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-521 3921,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22534@0" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_24d8f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-521 3921,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dd7b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-826 3921,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22526@0" ObjectIDZND0="22567@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120888_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-826 3921,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc4aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-720 3921,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22567@1" ObjectIDZND0="22533@1" Pin0InfoVect0LinkObjId="SW-120915_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd7b00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-720 3921,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf6950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4270,-791 4270,-857 4310,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2cc4d00@0" ObjectIDZND0="34393@x" ObjectIDZND1="22529@x" ObjectIDZND2="g_2e61870@0" Pin0InfoVect0LinkObjId="SW-219834_0" Pin0InfoVect1LinkObjId="SW-120905_0" Pin0InfoVect2LinkObjId="g_2e61870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cc4d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4270,-791 4270,-857 4310,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d8f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-494 3713,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22541@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-494 3713,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-236 3681,-248 3713,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2d35120@0" ObjectIDZND0="33938@x" ObjectIDZND1="22542@x" Pin0InfoVect0LinkObjId="EC-WD_GQ.GQ_061Ld_0" Pin0InfoVect1LinkObjId="SW-120962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d35120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-236 3681,-248 3713,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce0d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3747,-342 3747,-355 3714,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_24d91f0@0" ObjectIDZND0="22542@x" ObjectIDZND1="22540@x" Pin0InfoVect0LinkObjId="SW-120962_0" Pin0InfoVect1LinkObjId="SW-120960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24d91f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3747,-342 3747,-355 3714,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ce0f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-400 3713,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22540@1" ObjectIDZND0="22543@0" Pin0InfoVect0LinkObjId="SW-120963_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-400 3713,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d16090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-170 3713,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33938@0" ObjectIDZND0="g_2d35120@0" ObjectIDZND1="22542@x" Pin0InfoVect0LinkObjId="g_2d35120_0" Pin0InfoVect1LinkObjId="SW-120962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_GQ.GQ_061Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-170 3713,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0b780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-248 3713,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2d35120@0" ObjectIDND1="33938@x" ObjectIDZND0="22542@0" Pin0InfoVect0LinkObjId="SW-120962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d35120_0" Pin1InfoVect1LinkObjId="EC-WD_GQ.GQ_061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-248 3713,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5b4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-491 4961,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22566@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-491 4961,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c37480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-397 4961,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22564@1" ObjectIDZND0="22565@0" Pin0InfoVect0LinkObjId="SW-121069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-397 4961,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e03490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-365 5129,-437 5169,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_2d648e0@0" ObjectIDZND0="22530@x" ObjectIDZND1="g_2d69a90@0" Pin0InfoVect0LinkObjId="SW-120906_0" Pin0InfoVect1LinkObjId="g_2d69a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d648e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-365 5129,-437 5169,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9d3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-927 4961,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22536@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_2d942d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-927 4961,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-609 4961,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22538@0" ObjectIDZND0="22537@1" Pin0InfoVect0LinkObjId="SW-120947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-609 4961,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-570 4961,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22537@0" ObjectIDZND0="22539@1" Pin0InfoVect0LinkObjId="SW-120949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-570 4961,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2da05b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-522 4961,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22539@0" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-522 4961,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3ac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-829 4961,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22535@0" ObjectIDZND0="22568@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-829 4961,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d3aea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-721 4961,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22568@1" ObjectIDZND0="22538@1" Pin0InfoVect0LinkObjId="SW-120948_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d3ac70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-721 4961,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b0990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-438 3713,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22543@1" ObjectIDZND0="22541@0" Pin0InfoVect0LinkObjId="SW-120961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120963_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-438 3713,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2acc990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-295 3713,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22542@1" ObjectIDZND0="g_24d91f0@0" ObjectIDZND1="22540@x" Pin0InfoVect0LinkObjId="g_24d91f0_0" Pin0InfoVect1LinkObjId="SW-120960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-295 3713,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2accbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-355 3713,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_24d91f0@0" ObjectIDND1="22542@x" ObjectIDZND0="22540@0" Pin0InfoVect0LinkObjId="SW-120960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24d91f0_0" Pin1InfoVect1LinkObjId="SW-120962_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-355 3713,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2da3830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-493 3921,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22545@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-493 3921,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d532a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-235 3889,-247 3921,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2ac9740@0" ObjectIDZND0="33925@x" ObjectIDZND1="22546@x" Pin0InfoVect0LinkObjId="EC-WD_GQ.062Ld_0" Pin0InfoVect1LinkObjId="SW-120980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac9740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-235 3889,-247 3921,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d53500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-341 3955,-354 3922,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2da3a90@0" ObjectIDZND0="22546@x" ObjectIDZND1="22544@x" Pin0InfoVect0LinkObjId="SW-120980_0" Pin0InfoVect1LinkObjId="SW-120978_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2da3a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-341 3955,-354 3922,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b1350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-399 3921,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22544@1" ObjectIDZND0="22547@0" Pin0InfoVect0LinkObjId="SW-120981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120978_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-399 3921,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfd640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-169 3921,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33925@0" ObjectIDZND0="g_2ac9740@0" ObjectIDZND1="22546@x" Pin0InfoVect0LinkObjId="g_2ac9740_0" Pin0InfoVect1LinkObjId="SW-120980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_GQ.062Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-169 3921,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfd8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-247 3921,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2ac9740@0" ObjectIDND1="33925@x" ObjectIDZND0="22546@0" Pin0InfoVect0LinkObjId="SW-120980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ac9740_0" Pin1InfoVect1LinkObjId="EC-WD_GQ.062Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-247 3921,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dfdb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-437 3921,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22547@1" ObjectIDZND0="22545@0" Pin0InfoVect0LinkObjId="SW-120979_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-437 3921,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd9790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-294 3921,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22546@1" ObjectIDZND0="g_2da3a90@0" ObjectIDZND1="22544@x" Pin0InfoVect0LinkObjId="g_2da3a90_0" Pin0InfoVect1LinkObjId="SW-120978_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-294 3921,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd99f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-354 3921,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2da3a90@0" ObjectIDND1="22546@x" ObjectIDZND0="22544@0" Pin0InfoVect0LinkObjId="SW-120978_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2da3a90_0" Pin1InfoVect1LinkObjId="SW-120980_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-354 3921,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dadf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-494 4129,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22549@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-494 4129,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccf320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-236 4097,-248 4129,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2ac6300@0" ObjectIDZND0="33926@x" ObjectIDZND1="22550@x" Pin0InfoVect0LinkObjId="EC-WD_GQ.063Ld_0" Pin0InfoVect1LinkObjId="SW-120998_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac6300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-236 4097,-248 4129,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccf560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-342 4163,-355 4130,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3f88ac0@0" ObjectIDZND0="22550@x" ObjectIDZND1="22548@x" Pin0InfoVect0LinkObjId="SW-120998_0" Pin0InfoVect1LinkObjId="SW-120996_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f88ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-342 4163,-355 4130,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccf7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-400 4129,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22548@1" ObjectIDZND0="22551@0" Pin0InfoVect0LinkObjId="SW-120999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-400 4129,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e2a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-170 4129,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33926@0" ObjectIDZND0="g_2ac6300@0" ObjectIDZND1="22550@x" Pin0InfoVect0LinkObjId="g_2ac6300_0" Pin0InfoVect1LinkObjId="SW-120998_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_GQ.063Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-170 4129,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e2cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-248 4129,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33926@x" ObjectIDND1="g_2ac6300@0" ObjectIDZND0="22550@0" Pin0InfoVect0LinkObjId="SW-120998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_GQ.063Ld_0" Pin1InfoVect1LinkObjId="g_2ac6300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-248 4129,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d6a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-438 4129,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22551@1" ObjectIDZND0="22549@0" Pin0InfoVect0LinkObjId="SW-120997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-438 4129,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d6a470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-295 4129,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22550@1" ObjectIDZND0="g_3f88ac0@0" ObjectIDZND1="22548@x" Pin0InfoVect0LinkObjId="g_3f88ac0_0" Pin0InfoVect1LinkObjId="SW-120996_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-295 4129,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d6a6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-355 4129,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22550@x" ObjectIDND1="g_3f88ac0@0" ObjectIDZND0="22548@0" Pin0InfoVect0LinkObjId="SW-120996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120998_0" Pin1InfoVect1LinkObjId="g_3f88ac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-355 4129,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d156f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-236 4305,-248 4337,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2cf4560@0" ObjectIDZND0="33927@x" ObjectIDZND1="22554@x" Pin0InfoVect0LinkObjId="EC-WD_GQ.064Ld_0" Pin0InfoVect1LinkObjId="SW-121016_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf4560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-236 4305,-248 4337,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d15950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-342 4371,-355 4338,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2dbae30@0" ObjectIDZND0="22554@x" ObjectIDZND1="22552@x" Pin0InfoVect0LinkObjId="SW-121016_0" Pin0InfoVect1LinkObjId="SW-121014_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dbae30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-342 4371,-355 4338,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1d660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-400 4337,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22552@1" ObjectIDZND0="22555@0" Pin0InfoVect0LinkObjId="SW-121017_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-400 4337,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d22840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-170 4337,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33927@0" ObjectIDZND0="g_2cf4560@0" ObjectIDZND1="22554@x" Pin0InfoVect0LinkObjId="g_2cf4560_0" Pin0InfoVect1LinkObjId="SW-121016_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_GQ.064Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-170 4337,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d22aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-248 4337,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2cf4560@0" ObjectIDND1="33927@x" ObjectIDZND0="22554@0" Pin0InfoVect0LinkObjId="SW-121016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cf4560_0" Pin1InfoVect1LinkObjId="EC-WD_GQ.064Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-248 4337,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d22d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-438 4337,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22555@1" ObjectIDZND0="22553@0" Pin0InfoVect0LinkObjId="SW-121015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-438 4337,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d21320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-295 4337,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22554@1" ObjectIDZND0="g_2dbae30@0" ObjectIDZND1="22552@x" Pin0InfoVect0LinkObjId="g_2dbae30_0" Pin0InfoVect1LinkObjId="SW-121014_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-295 4337,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d21580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-355 4337,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2dbae30@0" ObjectIDND1="22554@x" ObjectIDZND0="22552@0" Pin0InfoVect0LinkObjId="SW-121014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2dbae30_0" Pin1InfoVect1LinkObjId="SW-121016_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-355 4337,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-492 4337,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22553@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-492 4337,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf4ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-236 4513,-248 4545,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2cd0830@0" ObjectIDZND0="33935@x" ObjectIDZND1="22558@x" Pin0InfoVect0LinkObjId="EC-WD_GQ.065Ld_0" Pin0InfoVect1LinkObjId="SW-121034_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd0830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-236 4513,-248 4545,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf4f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-342 4579,-355 4546,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2d5d320@0" ObjectIDZND0="22558@x" ObjectIDZND1="22556@x" Pin0InfoVect0LinkObjId="SW-121034_0" Pin0InfoVect1LinkObjId="SW-121032_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d5d320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-342 4579,-355 4546,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf5160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-400 4545,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22556@1" ObjectIDZND0="22559@0" Pin0InfoVect0LinkObjId="SW-121035_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121032_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-400 4545,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5a710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-170 4545,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33935@0" ObjectIDZND0="22558@x" ObjectIDZND1="g_2cd0830@0" Pin0InfoVect0LinkObjId="SW-121034_0" Pin0InfoVect1LinkObjId="g_2cd0830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_GQ.065Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-170 4545,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5a970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-248 4545,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33935@x" ObjectIDND1="g_2cd0830@0" ObjectIDZND0="22558@0" Pin0InfoVect0LinkObjId="SW-121034_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_GQ.065Ld_0" Pin1InfoVect1LinkObjId="g_2cd0830_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-248 4545,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1c7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-438 4545,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22559@1" ObjectIDZND0="22557@0" Pin0InfoVect0LinkObjId="SW-121033_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-438 4545,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-295 4545,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22558@1" ObjectIDZND0="g_2d5d320@0" ObjectIDZND1="22556@x" Pin0InfoVect0LinkObjId="g_2d5d320_0" Pin0InfoVect1LinkObjId="SW-121032_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121034_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-295 4545,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1cc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-355 4545,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22558@x" ObjectIDND1="g_2d5d320@0" ObjectIDZND0="22556@0" Pin0InfoVect0LinkObjId="SW-121032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121034_0" Pin1InfoVect1LinkObjId="g_2d5d320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-355 4545,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-492 4545,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22557@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-492 4545,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-235 4721,-247 4753,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2ce2520@0" ObjectIDZND0="33936@x" ObjectIDZND1="22562@x" Pin0InfoVect0LinkObjId="EC-WD_GQ.GQ_066Ld_0" Pin0InfoVect1LinkObjId="SW-121052_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce2520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-235 4721,-247 4753,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2caea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-341 4787,-354 4754,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2aea8d0@0" ObjectIDZND0="22562@x" ObjectIDZND1="22560@x" Pin0InfoVect0LinkObjId="SW-121052_0" Pin0InfoVect1LinkObjId="SW-121050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aea8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-341 4787,-354 4754,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2caec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-399 4753,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22560@1" ObjectIDZND0="22563@0" Pin0InfoVect0LinkObjId="SW-121053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-399 4753,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d04d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-169 4753,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33936@0" ObjectIDZND0="g_2ce2520@0" ObjectIDZND1="22562@x" Pin0InfoVect0LinkObjId="g_2ce2520_0" Pin0InfoVect1LinkObjId="SW-121052_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_GQ.GQ_066Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-169 4753,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c21370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-247 4753,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33936@x" ObjectIDND1="g_2ce2520@0" ObjectIDZND0="22562@0" Pin0InfoVect0LinkObjId="SW-121052_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_GQ.GQ_066Ld_0" Pin1InfoVect1LinkObjId="g_2ce2520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-247 4753,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c215d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-437 4753,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22563@1" ObjectIDZND0="22561@0" Pin0InfoVect0LinkObjId="SW-121051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121053_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-437 4753,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c21830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-294 4753,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22562@1" ObjectIDZND0="g_2aea8d0@0" ObjectIDZND1="22560@x" Pin0InfoVect0LinkObjId="g_2aea8d0_0" Pin0InfoVect1LinkObjId="SW-121050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121052_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-294 4753,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c21a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-354 4753,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22562@x" ObjectIDND1="g_2aea8d0@0" ObjectIDZND0="22560@0" Pin0InfoVect0LinkObjId="SW-121050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121052_0" Pin1InfoVect1LinkObjId="g_2aea8d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-354 4753,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c21cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-491 4753,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22561@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-491 4753,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c65ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-268 4961,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="39671@0" ObjectIDZND0="22564@0" Pin0InfoVect0LinkObjId="SW-121066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_GQ.WD_GQ_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-268 4961,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c65d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-455 4961,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22566@0" ObjectIDZND0="22565@1" Pin0InfoVect0LinkObjId="SW-121069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-455 4961,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d58560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-437 5169,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2d648e0@0" ObjectIDND1="g_2d69a90@0" ObjectIDZND0="22530@0" Pin0InfoVect0LinkObjId="SW-120906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d648e0_0" Pin1InfoVect1LinkObjId="g_2d69a90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-437 5169,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d587c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-491 5169,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22530@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_2d42420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120906_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-491 5169,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d69830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-355 5205,-437 5169,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d69a90@0" ObjectIDZND0="g_2d648e0@0" ObjectIDZND1="22530@x" Pin0InfoVect0LinkObjId="g_2d648e0_0" Pin0InfoVect1LinkObjId="SW-120906_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d69a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-355 5205,-437 5169,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d942d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-931 4310,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22529@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_2d9d3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120905_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-931 4310,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f82bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-925 4512,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22528@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_2d9d3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120904_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-925 4512,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd8310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-940 4448,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22523@0" ObjectIDZND0="22531@0" Pin0InfoVect0LinkObjId="SW-120908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d9d3d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-940 4448,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf74b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1069 4369,-1069 4369,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="22531@x" ObjectIDND1="g_2c072b0@0" ObjectIDND2="38124@1" ObjectIDZND0="22525@1" Pin0InfoVect0LinkObjId="SW-120885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120908_0" Pin1InfoVect1LinkObjId="g_2c072b0_0" Pin1InfoVect2LinkObjId="g_2f88860_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1069 4369,-1069 4369,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf7710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-1003 4369,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22525@0" ObjectIDZND0="g_2cf7970@0" Pin0InfoVect0LinkObjId="g_2cf7970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-1003 4369,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c07050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-1103 4502,-1103 4502,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="22525@x" ObjectIDND1="22531@x" ObjectIDND2="38124@1" ObjectIDZND0="g_2c072b0@0" Pin0InfoVect0LinkObjId="g_2c072b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120885_0" Pin1InfoVect1LinkObjId="SW-120908_0" Pin1InfoVect2LinkObjId="g_2f88860_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-1103 4502,-1103 4502,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aedc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1038 4448,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22531@1" ObjectIDZND0="22525@x" ObjectIDZND1="g_2c072b0@0" ObjectIDZND2="38124@1" Pin0InfoVect0LinkObjId="SW-120885_0" Pin0InfoVect1LinkObjId="g_2c072b0_0" Pin0InfoVect2LinkObjId="g_2f88860_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1038 4448,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aeded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1069 4448,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="22525@x" ObjectIDND1="22531@x" ObjectIDZND0="g_2c072b0@0" ObjectIDZND1="38124@1" Pin0InfoVect0LinkObjId="g_2c072b0_0" Pin0InfoVect1LinkObjId="g_2f88860_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120885_0" Pin1InfoVect1LinkObjId="SW-120908_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1069 4448,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1103 4448,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2c072b0@0" ObjectIDND1="22525@x" ObjectIDND2="22531@x" ObjectIDZND0="38124@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c072b0_0" Pin1InfoVect1LinkObjId="SW-120885_0" Pin1InfoVect2LinkObjId="SW-120908_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1103 4448,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f49b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3922,-878 3958,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22526@x" ObjectIDND1="22527@x" ObjectIDZND0="34392@0" Pin0InfoVect0LinkObjId="SW-219833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120888_0" Pin1InfoVect1LinkObjId="SW-120889_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3922,-878 3958,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f49dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-878 4024,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34392@1" ObjectIDZND0="g_2c70580@0" Pin0InfoVect0LinkObjId="g_2c70580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-878 4024,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f560b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-853 3921,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22526@1" ObjectIDZND0="34392@x" ObjectIDZND1="22527@x" Pin0InfoVect0LinkObjId="SW-219833_0" Pin0InfoVect1LinkObjId="SW-120889_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120888_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-853 3921,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f562a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-878 3921,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="34392@x" ObjectIDND1="22526@x" ObjectIDZND0="22527@0" Pin0InfoVect0LinkObjId="SW-120889_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-219833_0" Pin1InfoVect1LinkObjId="SW-120888_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-878 3921,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f56490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-933 3921,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22527@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_2d9d3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120889_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-933 3921,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c4eb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-886 4341,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="22529@x" ObjectIDND1="g_2cc4d00@0" ObjectIDND2="g_2e61870@0" ObjectIDZND0="34393@0" Pin0InfoVect0LinkObjId="SW-219834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120905_0" Pin1InfoVect1LinkObjId="g_2cc4d00_0" Pin1InfoVect2LinkObjId="g_2e61870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-886 4341,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c4eda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-886 4406,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34393@1" ObjectIDZND0="g_2f566a0@0" Pin0InfoVect0LinkObjId="g_2f566a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-886 4406,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c4fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-886 4310,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="34393@x" ObjectIDND1="g_2cc4d00@0" ObjectIDND2="g_2e61870@0" ObjectIDZND0="22529@0" Pin0InfoVect0LinkObjId="SW-120905_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-219834_0" Pin1InfoVect1LinkObjId="g_2cc4d00_0" Pin1InfoVect2LinkObjId="g_2e61870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-886 4310,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c6e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-856 4310,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2cc4d00@0" ObjectIDND1="g_2e61870@0" ObjectIDZND0="34393@x" ObjectIDZND1="22529@x" Pin0InfoVect0LinkObjId="SW-219834_0" Pin0InfoVect1LinkObjId="SW-120905_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cc4d00_0" Pin1InfoVect1LinkObjId="g_2e61870_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-856 4310,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cc3f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-869 4543,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="22528@x" ObjectIDZND0="34394@0" Pin0InfoVect0LinkObjId="SW-219835_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-120904_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-869 4543,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cc4170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-869 4609,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34394@1" ObjectIDZND0="g_2c6e9d0@0" Pin0InfoVect0LinkObjId="g_2c6e9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-869 4609,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c0d4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-801 4512,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="34394@x" ObjectIDZND1="22528@x" Pin0InfoVect0LinkObjId="SW-219835_0" Pin0InfoVect1LinkObjId="SW-120904_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-801 4512,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c0d740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-869 4512,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="34394@x" ObjectIDND1="0@x" ObjectIDZND0="22528@0" Pin0InfoVect0LinkObjId="SW-120904_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-219835_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-869 4512,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5035,-881 5065,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34395@1" ObjectIDZND0="g_2d3d4d0@0" Pin0InfoVect0LinkObjId="g_2d3d4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5035,-881 5065,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e60600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4963,-881 4999,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22535@x" ObjectIDND1="22536@x" ObjectIDZND0="34395@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120927_0" Pin1InfoVect1LinkObjId="SW-120928_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4963,-881 4999,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e610f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-856 4961,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22535@1" ObjectIDZND0="34395@x" ObjectIDZND1="22536@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-120928_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-856 4961,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e61350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-881 4961,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="34395@x" ObjectIDND1="22535@x" ObjectIDZND0="22536@0" Pin0InfoVect0LinkObjId="SW-120928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-120927_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-881 4961,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cace20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-749 4352,-857 4310,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e61870@0" ObjectIDZND0="g_2cc4d00@0" ObjectIDZND1="34393@x" ObjectIDZND2="22529@x" Pin0InfoVect0LinkObjId="g_2cc4d00_0" Pin0InfoVect1LinkObjId="SW-219834_0" Pin0InfoVect2LinkObjId="SW-120905_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e61870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-749 4352,-857 4310,-857 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-119711" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.500000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22308" ObjectName="DYN-WD_GQ"/>
     <cge:Meas_Ref ObjectId="119711"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 -854.262295 43.733333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae9ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad3eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de0080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.385965 -0.000000 0.000000 -1.338983 -151.754386 184.864407)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ddc100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3694.000000 935.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ddc330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 950.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 965.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 979.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.924627 -0.000000 0.000000 -1.802191 581.272388 466.468046)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e07ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 685.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e08580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 701.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.014925 -0.000000 0.000000 -1.741935 207.402985 415.322581)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e08d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 688.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2accea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 704.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 -715.262295 -432.266667)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2acd7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2acdb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2acdd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 325.737705 -424.266667)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ace1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dce4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dce710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcea40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 875.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcecb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 860.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dceef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 844.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c99f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.000000 890.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9a2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 884.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9a530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 869.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9a770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4831.000000 853.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9a9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 899.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 -624.262295 48.733333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f51f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f52470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f526b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 -416.262295 46.733333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f52ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f52dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f53010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 -209.262295 45.733333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f53430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f53730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f53970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 -0.262295 45.733333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f53d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3cfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3d1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.229508 -0.000000 0.000000 -1.288889 207.737705 46.733333)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3d600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3d900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3db40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.986842 -0.000000 0.000000 -1.070433 1328.828947 12.761582)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3df60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 141.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3e260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 126.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3e4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="WD_GQ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damaogaoTgq" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4448,-1148 4448,-1200 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38124" ObjectName="AC-35kV.LN_damaogaoTgq"/>
    <cge:TPSR_Ref TObjectID="38124_SS-175"/></metadata>
   <polyline fill="none" opacity="0" points="4448,-1148 4448,-1200 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="3921" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="3713" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="3921" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="3921" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4129" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4337" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4545" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4753" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4961" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4961" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4961" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="5169" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4512" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4448" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4310" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120888">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -818.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22526" ObjectName="SW-WD_GQ.WD_GQ_301BK"/>
     <cge:Meas_Ref ObjectId="120888"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -561.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22532" ObjectName="SW-WD_GQ.WD_GQ_001BK"/>
     <cge:Meas_Ref ObjectId="120914"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22543" ObjectName="SW-WD_GQ.WD_GQ_061BK"/>
     <cge:Meas_Ref ObjectId="120963"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22565" ObjectName="SW-WD_GQ.WD_GQ_067BK"/>
     <cge:Meas_Ref ObjectId="121069"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -821.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22535" ObjectName="SW-WD_GQ.WD_GQ_302BK"/>
     <cge:Meas_Ref ObjectId="120927"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -562.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22537" ObjectName="SW-WD_GQ.WD_GQ_002BK"/>
     <cge:Meas_Ref ObjectId="120947"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -402.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22547" ObjectName="SW-WD_GQ.WD_GQ_062BK"/>
     <cge:Meas_Ref ObjectId="120981"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120999">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22551" ObjectName="SW-WD_GQ.WD_GQ_063BK"/>
     <cge:Meas_Ref ObjectId="120999"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121017">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22555" ObjectName="SW-WD_GQ.WD_GQ_064BK"/>
     <cge:Meas_Ref ObjectId="121017"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121035">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22559" ObjectName="SW-WD_GQ.WD_GQ_065BK"/>
     <cge:Meas_Ref ObjectId="121035"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -402.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22563" ObjectName="SW-WD_GQ.WD_GQ_066BK"/>
     <cge:Meas_Ref ObjectId="121053"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,16)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,56)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,96)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,136)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,176)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,16)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,276)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,296)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,316)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c20d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,336)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="40" graphid="g_2da1f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3277.000000 -1178.500000) translate(0,32)">高桥变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3051a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -809.000000) translate(0,16)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3051a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -809.000000) translate(0,36)">SZ11-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3051a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -809.000000) translate(0,56)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3051a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -809.000000) translate(0,76)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3051a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -809.000000) translate(0,96)">Ud=7.05%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_21ed400" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3721.142857 -210.800000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_21ed400" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3721.142857 -210.800000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_21ed400" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3721.142857 -210.800000) translate(0,56)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d3b100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -807.000000) translate(0,16)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d3b100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -807.000000) translate(0,36)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d3b100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -807.000000) translate(0,56)">3150kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d3b100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -807.000000) translate(0,76)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d3b100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -807.000000) translate(0,96)">Ud=7.38%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c39fd0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4770.142857 -210.800000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c39fd0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4770.142857 -210.800000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c39fd0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4770.142857 -210.800000) translate(0,56)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c9b880" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4222.142857 -659.800000) translate(0,16)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f8ccd0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4468.142857 -700.800000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aee130" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5098.142857 -267.800000) translate(0,16)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_3f8dd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5181.000000 -548.000000) translate(0,24)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2cbcc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -978.000000) translate(0,24)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c520b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4767.000000 -481.000000) translate(0,16)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c525f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -387.000000) translate(0,16)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c52810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4760.000000 -285.000000) translate(0,16)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c52a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.000000 -431.000000) translate(0,16)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c52c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3725.000000 -484.000000) translate(0,16)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c52ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3726.000000 -392.000000) translate(0,16)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c53110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3720.000000 -286.000000) translate(0,16)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c53350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -434.000000) translate(0,16)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c7e540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -483.000000) translate(0,16)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c7e780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -383.000000) translate(0,16)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c7e9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -430.000000) translate(0,16)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c7ec00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5183.000000 -482.000000) translate(0,16)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c7ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.000000 -483.000000) translate(0,16)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c7f380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.000000 -391.000000) translate(0,16)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c7f5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -286.000000) translate(0,16)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c80310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -434.000000) translate(0,16)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c80550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -483.000000) translate(0,16)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c80790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -392.000000) translate(0,16)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c809d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -285.000000) translate(0,16)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c80c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -434.000000) translate(0,16)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c80e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -485.000000) translate(0,16)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c81090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -391.000000) translate(0,16)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c812d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -286.000000) translate(0,16)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c81510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -436.000000) translate(0,16)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f57230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -485.000000) translate(0,16)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f57470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -388.000000) translate(0,16)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f576b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4136.000000 -286.000000) translate(0,16)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f578f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -434.000000) translate(0,16)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f57b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -916.000000) translate(0,16)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f57d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -548.000000) translate(0,16)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f57fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -635.000000) translate(0,16)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f581f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -592.000000) translate(0,16)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f58430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3930.000000 -922.000000) translate(0,16)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acab70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -849.000000) translate(0,16)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acadb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4980.000000 -592.000000) translate(0,16)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acaff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -920.000000) translate(0,16)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acb230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4976.000000 -851.000000) translate(0,16)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acb470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4978.000000 -548.000000) translate(0,16)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acb6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -637.000000) translate(0,16)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acb8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -1030.000000) translate(0,16)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acbb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -1028.000000) translate(0,16)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2acbd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -920.000000) translate(0,16)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c2a500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3961.000000 -748.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c2a740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5001.000000 -750.000000) translate(0,16)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d16790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3945.000000 -697.000000) translate(0,16)">YJV22-8.7/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d16790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3945.000000 -697.000000) translate(0,36)">3×95mm2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d17360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -695.000000) translate(0,16)">YJV22-8.7/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d17360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -695.000000) translate(0,36)">3×95mm2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e07cc0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3268.142857 -270.800000) translate(0,16)">8830380</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d6dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -1240.000000) translate(0,16)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d6dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -1240.000000) translate(0,36)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d6dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -1240.000000) translate(0,56)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d6dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -1240.000000) translate(0,76)">猫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d6dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -1240.000000) translate(0,96)">高</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2d6dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -1240.000000) translate(0,116)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f89320" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 5167.000000 -626.881356) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f89560" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 5178.649123 -649.762712) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc0b70" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 5178.649123 -672.644068) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc0db0" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 5178.649123 -694.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc0ff0" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 5173.649123 -604.644068) translate(0,12)">3U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc1230" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 5230.649123 -581.762712) translate(0,12)">F:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc1cd0" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 5033.649123 -1020.762712) translate(0,12)">F:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de87d0" transform="matrix(1.456140 -0.000000 -0.000000 1.525424 4976.649123 -1043.644068) translate(0,12)">3U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2f8aae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 -1151.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2da43f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 -1186.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da48f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3183.000000 -757.000000) translate(0,12)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_2f4a030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -872.000000) translate(0,16)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_2c4f000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4333.000000 -880.000000) translate(0,16)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_2c0d9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4538.000000 -862.000000) translate(0,16)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_2c0de90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4992.000000 -875.000000) translate(0,16)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2cad080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3581.000000 -334.000000) translate(0,16)">YJV22-8.7/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2cad080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3581.000000 -334.000000) translate(0,36)">3×70mm2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c78f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3946.000000 -212.000000) translate(0,16)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c78f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3946.000000 -212.000000) translate(0,36)">关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c78f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3946.000000 -212.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c79960" transform="matrix(1.000000 0.000000 0.000000 1.000000 4154.000000 -209.000000) translate(0,16)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c79960" transform="matrix(1.000000 0.000000 0.000000 1.000000 4154.000000 -209.000000) translate(0,36)">鞍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2c79960" transform="matrix(1.000000 0.000000 0.000000 1.000000 4154.000000 -209.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e1e2b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -214.000000) translate(0,16)">花</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e1e2b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -214.000000) translate(0,36)">桥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e1e2b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -214.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e1ebb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4576.000000 -217.000000) translate(0,16)">勒</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e1ebb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4576.000000 -217.000000) translate(0,36)">外</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e1ebb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4576.000000 -217.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2feba10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4926.000000 -238.000000) translate(0,12)">10kV电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2bd8070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -1169.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f3e6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3124.000000 -218.500000) translate(0,16)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2caa500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -238.500000) translate(0,16)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2caa500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -238.500000) translate(0,36)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2caa500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -238.500000) translate(0,56)">13987880311</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cf7970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -970.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c70580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -872.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f566a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -880.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c6e9d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4604.000000 -863.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3d4d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.000000 -875.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_GQ"/>
</svg>