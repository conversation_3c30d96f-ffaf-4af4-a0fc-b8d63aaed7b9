<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-40" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3122 -1198 2121 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="24" y2="24"/>
    <ellipse cx="14" cy="9" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="8" cy="17" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="24" y1="21" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="19" y1="21" y2="16"/>
    <ellipse cx="19" cy="17" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_18e58b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_16efc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e8240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e8e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e9fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18eaae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18eb340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_18ebe00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1795eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1795eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ef950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ef950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f1600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f1600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18f2530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f4210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_18f4e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_18f5c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18f63d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f7c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f88b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f9170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18f9930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18faa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18fb390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18fbe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_18fc840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_18fde60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_18fe880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_18ffa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19006b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_190eac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1906e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1901840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1902bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="2131" x="3117" y="-1203"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3123" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3124" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3123" y="-597"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-38454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3694.013739 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6136" ObjectName="SW-CX_BH.CX_BH_0901SW"/>
     <cge:Meas_Ref ObjectId="38454"/>
    <cge:TPSR_Ref TObjectID="6136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38456">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6138" ObjectName="SW-CX_BH.CX_BH_0341SW"/>
     <cge:Meas_Ref ObjectId="38456"/>
    <cge:TPSR_Ref TObjectID="6138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38457">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6139" ObjectName="SW-CX_BH.CX_BH_0346SW"/>
     <cge:Meas_Ref ObjectId="38457"/>
    <cge:TPSR_Ref TObjectID="6139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38458">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3665.500000 -562.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6140" ObjectName="SW-CX_BH.CX_BH_03417SW"/>
     <cge:Meas_Ref ObjectId="38458"/>
    <cge:TPSR_Ref TObjectID="6140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.013739 -655.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6146" ObjectName="SW-CX_BH.CX_BH_0311SW"/>
     <cge:Meas_Ref ObjectId="38464"/>
    <cge:TPSR_Ref TObjectID="6146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58685">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4009.000000 -706.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11101" ObjectName="SW-CX_BH.CX_BH_03117SW_S"/>
     <cge:Meas_Ref ObjectId="58685"/>
    <cge:TPSR_Ref TObjectID="11101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4010.000000 -777.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11102" ObjectName="SW-CX_BH.CX_BH_03167SW_S"/>
     <cge:Meas_Ref ObjectId="58688"/>
    <cge:TPSR_Ref TObjectID="11102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -580.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6134" ObjectName="SW-CX_BH.CX_BH_0331SW"/>
     <cge:Meas_Ref ObjectId="38452"/>
    <cge:TPSR_Ref TObjectID="6134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6135" ObjectName="SW-CX_BH.CX_BH_0336SW"/>
     <cge:Meas_Ref ObjectId="38453"/>
    <cge:TPSR_Ref TObjectID="6135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58693">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3904.500000 -566.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11103" ObjectName="SW-CX_BH.CX_BH_03317SW_S"/>
     <cge:Meas_Ref ObjectId="58693"/>
    <cge:TPSR_Ref TObjectID="11103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38460">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -579.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6142" ObjectName="SW-CX_BH.CX_BH_0321SW"/>
     <cge:Meas_Ref ObjectId="38460"/>
    <cge:TPSR_Ref TObjectID="6142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38461">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6143" ObjectName="SW-CX_BH.CX_BH_0326SW"/>
     <cge:Meas_Ref ObjectId="38461"/>
    <cge:TPSR_Ref TObjectID="6143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38462">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4144.500000 -564.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6144" ObjectName="SW-CX_BH.CX_BH_03217SW"/>
     <cge:Meas_Ref ObjectId="38462"/>
    <cge:TPSR_Ref TObjectID="6144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38487">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4918.013739 -661.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6167" ObjectName="SW-CX_BH.CX_BH_0902SW"/>
     <cge:Meas_Ref ObjectId="38487"/>
    <cge:TPSR_Ref TObjectID="6167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6153" ObjectName="SW-CX_BH.CX_BH_0421SW"/>
     <cge:Meas_Ref ObjectId="38475"/>
    <cge:TPSR_Ref TObjectID="6153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58476">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6154" ObjectName="SW-CX_BH.CX_BH_0426SW"/>
     <cge:Meas_Ref ObjectId="58476"/>
    <cge:TPSR_Ref TObjectID="6154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38476">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4495.500000 -563.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6155" ObjectName="SW-CX_BH.CX_BH_04217SW"/>
     <cge:Meas_Ref ObjectId="38476"/>
    <cge:TPSR_Ref TObjectID="6155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.013739 -655.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6151" ObjectName="SW-CX_BH.CX_BH_0411SW"/>
     <cge:Meas_Ref ObjectId="38473"/>
    <cge:TPSR_Ref TObjectID="6151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4611.000000 -706.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11107" ObjectName="SW-CX_BH.CX_BH_04117SW_S"/>
     <cge:Meas_Ref ObjectId="58686"/>
    <cge:TPSR_Ref TObjectID="11107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4612.000000 -777.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11108" ObjectName="SW-CX_BH.CX_BH_04167SW_S"/>
     <cge:Meas_Ref ObjectId="58687"/>
    <cge:TPSR_Ref TObjectID="11108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38486">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6165" ObjectName="SW-CX_BH.CX_BH_0431SW"/>
     <cge:Meas_Ref ObjectId="38486"/>
    <cge:TPSR_Ref TObjectID="6165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58477">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6166" ObjectName="SW-CX_BH.CX_BH_0436SW"/>
     <cge:Meas_Ref ObjectId="58477"/>
    <cge:TPSR_Ref TObjectID="6166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6161" ObjectName="SW-CX_BH.CX_BH_0441SW"/>
     <cge:Meas_Ref ObjectId="38482"/>
    <cge:TPSR_Ref TObjectID="6161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6162" ObjectName="SW-CX_BH.CX_BH_0446SW"/>
     <cge:Meas_Ref ObjectId="38483"/>
    <cge:TPSR_Ref TObjectID="6162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38484">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4928.500000 -562.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6163" ObjectName="SW-CX_BH.CX_BH_04417SW"/>
     <cge:Meas_Ref ObjectId="38484"/>
    <cge:TPSR_Ref TObjectID="6163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38478">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 -573.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6157" ObjectName="SW-CX_BH.CX_BH_0451SW"/>
     <cge:Meas_Ref ObjectId="38478"/>
    <cge:TPSR_Ref TObjectID="6157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38479">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6158" ObjectName="SW-CX_BH.CX_BH_0456SW"/>
     <cge:Meas_Ref ObjectId="38479"/>
    <cge:TPSR_Ref TObjectID="6158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5151.500000 -560.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6159" ObjectName="SW-CX_BH.CX_BH_04517SW"/>
     <cge:Meas_Ref ObjectId="38480"/>
    <cge:TPSR_Ref TObjectID="6159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -578.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6148" ObjectName="SW-CX_BH.CX_BH_0121SW"/>
     <cge:Meas_Ref ObjectId="38466"/>
    <cge:TPSR_Ref TObjectID="6148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38467">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -578.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6149" ObjectName="SW-CX_BH.CX_BH_0122SW"/>
     <cge:Meas_Ref ObjectId="38467"/>
    <cge:TPSR_Ref TObjectID="6149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11104" ObjectName="SW-CX_BH.CX_BH_01217SW_S"/>
     <cge:Meas_Ref ObjectId="58694"/>
    <cge:TPSR_Ref TObjectID="11104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -498.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11106" ObjectName="SW-CX_BH.CX_BH_01227SW_S"/>
     <cge:Meas_Ref ObjectId="58695"/>
    <cge:TPSR_Ref TObjectID="11106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58684">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3659.000000 -716.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11097" ObjectName="SW-CX_BH.CX_BH_09017SW_S"/>
     <cge:Meas_Ref ObjectId="58684"/>
    <cge:TPSR_Ref TObjectID="11097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58689">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4878.000000 -717.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11115" ObjectName="SW-CX_BH.CX_BH_09027SW_S"/>
     <cge:Meas_Ref ObjectId="58689"/>
    <cge:TPSR_Ref TObjectID="11115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4700.500000 -561.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11116" ObjectName="SW-CX_BH.CX_BH_04317SW_S"/>
     <cge:Meas_Ref ObjectId="58696"/>
    <cge:TPSR_Ref TObjectID="11116"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3633,-449 3633,-387 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3633,-449 3633,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4113,-449 4113,-387 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4113,-449 4113,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4464,-447 4464,-385 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4464,-447 4464,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4672,-445 4672,-383 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4672,-445 4672,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4897,-445 4897,-383 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4897,-445 4897,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5120,-443 5120,-381 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5120,-443 5120,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3872,-449 3872,-387 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3872,-449 3872,-387 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1724920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3726.500000 -578.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17aa640" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3633.000000 -752.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1723160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -722.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_174e770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1761090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3949.500000 -582.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16ff070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4188.500000 -580.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1713400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4539.500000 -579.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1715900" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4841.000000 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16d29b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4659.000000 -722.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16686e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4659.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16da140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4989.500000 -578.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_163e090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5212.500000 -576.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_173c510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -471.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_173cf60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -471.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1644a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4744.500000 -577.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1724730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3634,-589 3656,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6138@x" ObjectIDND1="6137@x" ObjectIDZND0="6140@0" Pin0InfoVect0LinkObjId="SW-38458_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38456_0" Pin1InfoVect1LinkObjId="SW-38455_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3634,-589 3656,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16eff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-588 3731,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6140@1" ObjectIDZND0="g_1724920@0" Pin0InfoVect0LinkObjId="g_1724920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38458_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-588 3731,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a9710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3741,-806 3741,-788 3709,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_16f4970@0" ObjectIDZND0="6136@x" ObjectIDZND1="11097@x" ObjectIDZND2="g_16f5fa0@0" Pin0InfoVect0LinkObjId="SW-38454_0" Pin0InfoVect1LinkObjId="SW-58684_0" Pin0InfoVect2LinkObjId="g_16f5fa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f4970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3741,-806 3741,-788 3709,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17aa450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3709,-682 3709,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6136@0" ObjectIDZND0="6131@0" Pin0InfoVect0LinkObjId="g_1750a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3709,-682 3709,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17aac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3650,-742 3628,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11097@0" ObjectIDZND0="g_17aa640@0" Pin0InfoVect0LinkObjId="g_17aa640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3650,-742 3628,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17aae60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3709,-742 3686,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_16f5fa0@0" ObjectIDND1="g_16f4970@0" ObjectIDND2="6136@x" ObjectIDZND0="11097@1" Pin0InfoVect0LinkObjId="SW-58684_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16f5fa0_0" Pin1InfoVect1LinkObjId="g_16f4970_0" Pin1InfoVect2LinkObjId="SW-38454_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3709,-742 3686,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17527b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3709,-788 3709,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_16f5fa0@0" ObjectIDND1="g_16f4970@0" ObjectIDZND0="6136@x" ObjectIDZND1="11097@x" Pin0InfoVect0LinkObjId="SW-38454_0" Pin0InfoVect1LinkObjId="SW-58684_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16f5fa0_0" Pin1InfoVect1LinkObjId="g_16f4970_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3709,-788 3709,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17529a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3709,-742 3709,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_16f5fa0@0" ObjectIDND1="g_16f4970@0" ObjectIDND2="11097@x" ObjectIDZND0="6136@1" Pin0InfoVect0LinkObjId="SW-38454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16f5fa0_0" Pin1InfoVect1LinkObjId="g_16f4970_0" Pin1InfoVect2LinkObjId="SW-58684_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3709,-742 3709,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-652 3979,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6131@0" ObjectIDZND0="6146@0" Pin0InfoVect0LinkObjId="SW-38464_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17aa450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-652 3979,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1729bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4036,-732 4057,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11101@1" ObjectIDZND0="g_1723160@0" Pin0InfoVect0LinkObjId="g_1723160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4036,-732 4057,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1729db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3978,-732 4000,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6146@x" ObjectIDND1="6145@x" ObjectIDZND0="11101@0" Pin0InfoVect0LinkObjId="SW-58685_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38464_0" Pin1InfoVect1LinkObjId="SW-38463_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3978,-732 4000,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_172a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-713 3979,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6146@1" ObjectIDZND0="11101@x" ObjectIDZND1="6145@x" Pin0InfoVect0LinkObjId="SW-58685_0" Pin0InfoVect1LinkObjId="SW-38463_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-713 3979,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174ef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-803 4057,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11102@1" ObjectIDZND0="g_174e770@0" Pin0InfoVect0LinkObjId="g_174e770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58688_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-803 4057,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-803 4001,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="6145@x" ObjectIDND1="0@x" ObjectIDZND0="11102@0" Pin0InfoVect0LinkObjId="SW-58688_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38463_0" Pin1InfoVect1LinkObjId="g_1724920_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-803 4001,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-732 3979,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11101@x" ObjectIDND1="6146@x" ObjectIDZND0="6145@0" Pin0InfoVect0LinkObjId="SW-38463_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58685_0" Pin1InfoVect1LinkObjId="SW-38464_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-732 3979,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174fac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-781 3979,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="6145@1" ObjectIDZND0="11102@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-58688_0" Pin0InfoVect1LinkObjId="g_1724920_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38463_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-781 3979,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1750840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-803 3979,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="11102@x" ObjectIDND1="6145@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1724920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58688_0" Pin1InfoVect1LinkObjId="SW-38463_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-803 3979,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1750a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-635 3633,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6138@1" ObjectIDZND0="6131@0" Pin0InfoVect0LinkObjId="g_17aa450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-635 3633,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1750c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-517 3633,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6139@1" ObjectIDZND0="6137@0" Pin0InfoVect0LinkObjId="SW-38455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38457_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-517 3633,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1761840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-592 3954,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11103@1" ObjectIDZND0="g_1761090@0" Pin0InfoVect0LinkObjId="g_1761090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58693_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-592 3954,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1761d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-638 3872,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6134@1" ObjectIDZND0="6131@0" Pin0InfoVect0LinkObjId="g_17aa450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-638 3872,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1761f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-520 3872,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6135@1" ObjectIDZND0="6133@0" Pin0InfoVect0LinkObjId="SW-38451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-520 3872,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16fee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-591 4135,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6142@x" ObjectIDND1="6141@x" ObjectIDZND0="6144@0" Pin0InfoVect0LinkObjId="SW-38462_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38460_0" Pin1InfoVect1LinkObjId="SW-38459_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-591 4135,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1732030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4171,-590 4193,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6144@1" ObjectIDZND0="g_16ff070@0" Pin0InfoVect0LinkObjId="g_16ff070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4171,-590 4193,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1732760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-637 4113,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6142@1" ObjectIDZND0="6131@0" Pin0InfoVect0LinkObjId="g_17aa450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-637 4113,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1732950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-519 4113,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6143@1" ObjectIDZND0="6141@0" Pin0InfoVect0LinkObjId="SW-38459_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38461_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-519 4113,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17131e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-589 4486,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6153@x" ObjectIDND1="6152@x" ObjectIDZND0="6155@0" Pin0InfoVect0LinkObjId="SW-38476_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38475_0" Pin1InfoVect1LinkObjId="SW-38474_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-589 4486,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1713d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-589 4544,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6155@1" ObjectIDZND0="g_1713400@0" Pin0InfoVect0LinkObjId="g_1713400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38476_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-589 4544,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1714320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4966,-807 4966,-789 4934,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_16f3bc0@0" ObjectIDZND0="6167@x" ObjectIDZND1="11115@x" ObjectIDZND2="g_16f5720@0" Pin0InfoVect0LinkObjId="SW-38487_0" Pin0InfoVect1LinkObjId="SW-58689_0" Pin0InfoVect2LinkObjId="g_16f5720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f3bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4966,-807 4966,-789 4934,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17156e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-683 4933,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6167@0" ObjectIDZND0="6132@0" Pin0InfoVect0LinkObjId="g_171eef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-683 4933,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1779360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-788 4933,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_16f5720@0" ObjectIDND1="g_16f3bc0@0" ObjectIDZND0="6167@x" ObjectIDZND1="11115@x" Pin0InfoVect0LinkObjId="SW-38487_0" Pin0InfoVect1LinkObjId="SW-58689_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16f5720_0" Pin1InfoVect1LinkObjId="g_16f3bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-788 4933,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1779580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-743 4933,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_16f5720@0" ObjectIDND1="g_16f3bc0@0" ObjectIDND2="11115@x" ObjectIDZND0="6167@1" Pin0InfoVect0LinkObjId="SW-38487_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16f5720_0" Pin1InfoVect1LinkObjId="g_16f3bc0_0" Pin1InfoVect2LinkObjId="SW-58689_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-743 4933,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177bab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4581,-651 4581,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6132@0" ObjectIDZND0="6151@0" Pin0InfoVect0LinkObjId="SW-38473_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17156e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4581,-651 4581,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16d32e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-732 4664,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11107@1" ObjectIDZND0="g_16d29b0@0" Pin0InfoVect0LinkObjId="g_16d29b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-732 4664,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1665d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-732 4602,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6151@x" ObjectIDND1="6150@x" ObjectIDZND0="11107@0" Pin0InfoVect0LinkObjId="SW-58686_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38473_0" Pin1InfoVect1LinkObjId="SW-38472_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-732 4602,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1665fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4581,-713 4581,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6151@1" ObjectIDZND0="11107@x" ObjectIDZND1="6150@x" Pin0InfoVect0LinkObjId="SW-58686_0" Pin0InfoVect1LinkObjId="SW-38472_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4581,-713 4581,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171d560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-803 4664,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11108@1" ObjectIDZND0="g_16686e0@0" Pin0InfoVect0LinkObjId="g_16686e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58687_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-803 4664,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171d780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4581,-803 4603,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="6150@x" ObjectIDND1="0@x" ObjectIDZND0="11108@0" Pin0InfoVect0LinkObjId="SW-58687_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38472_0" Pin1InfoVect1LinkObjId="g_1724920_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4581,-803 4603,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171d9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4581,-732 4581,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11107@x" ObjectIDND1="6151@x" ObjectIDZND0="6150@0" Pin0InfoVect0LinkObjId="SW-38472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58686_0" Pin1InfoVect1LinkObjId="SW-38473_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4581,-732 4581,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171dbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4581,-781 4581,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="6150@1" ObjectIDZND0="11108@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-58687_0" Pin0InfoVect1LinkObjId="g_1724920_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4581,-781 4581,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4581,-806 4581,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="11108@x" ObjectIDND1="6150@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1724920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58687_0" Pin1InfoVect1LinkObjId="SW-38472_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4581,-806 4581,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-635 4464,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6153@1" ObjectIDZND0="6132@0" Pin0InfoVect0LinkObjId="g_17156e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38475_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-635 4464,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17487b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-588 4693,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6165@x" ObjectIDND1="6164@x" ObjectIDZND0="11116@0" Pin0InfoVect0LinkObjId="SW-58696_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38486_0" Pin1InfoVect1LinkObjId="SW-38485_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-588 4693,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1748ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-633 4672,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6165@1" ObjectIDZND0="6132@0" Pin0InfoVect0LinkObjId="g_17156e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-633 4672,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174b9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-589 4919,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6161@x" ObjectIDND1="6160@x" ObjectIDZND0="6163@0" Pin0InfoVect0LinkObjId="SW-38484_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38482_0" Pin1InfoVect1LinkObjId="SW-38481_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-589 4919,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16da690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-588 4994,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6163@1" ObjectIDZND0="g_16da140@0" Pin0InfoVect0LinkObjId="g_16da140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38484_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-588 4994,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16dadc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-633 4897,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6161@1" ObjectIDZND0="6132@0" Pin0InfoVect0LinkObjId="g_17156e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-633 4897,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16dafb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-515 4897,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6162@1" ObjectIDZND0="6160@0" Pin0InfoVect0LinkObjId="SW-38481_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38483_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-515 4897,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163de30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-587 5142,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6157@x" ObjectIDND1="6156@x" ObjectIDZND0="6159@0" Pin0InfoVect0LinkObjId="SW-38480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38478_0" Pin1InfoVect1LinkObjId="SW-38477_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-587 5142,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5178,-586 5217,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6159@1" ObjectIDZND0="g_163e090@0" Pin0InfoVect0LinkObjId="g_163e090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5178,-586 5217,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163ed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-631 5120,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6157@1" ObjectIDZND0="6132@0" Pin0InfoVect0LinkObjId="g_17156e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38478_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-631 5120,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163ef60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-513 5120,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6158@1" ObjectIDZND0="6156@0" Pin0InfoVect0LinkObjId="SW-38477_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-513 5120,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1781080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-652 4257,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6131@0" ObjectIDZND0="6148@1" Pin0InfoVect0LinkObjId="SW-38466_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17aa450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-652 4257,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_178e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-636 4399,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6149@1" ObjectIDZND0="6132@0" Pin0InfoVect0LinkObjId="g_17156e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38467_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-636 4399,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17667f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4311,-575 4257,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6147@1" ObjectIDZND0="6148@x" ObjectIDZND1="11104@x" Pin0InfoVect0LinkObjId="SW-38466_0" Pin0InfoVect1LinkObjId="SW-58694_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38465_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4311,-575 4257,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1766a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-575 4257,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6147@x" ObjectIDND1="11104@x" ObjectIDZND0="6148@0" Pin0InfoVect0LinkObjId="SW-38466_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38465_0" Pin1InfoVect1LinkObjId="SW-58694_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-575 4257,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1769560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-575 4257,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6147@x" ObjectIDND1="6148@x" ObjectIDZND0="11104@1" Pin0InfoVect0LinkObjId="SW-58694_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38465_0" Pin1InfoVect1LinkObjId="SW-38466_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-575 4257,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17697c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-522 4257,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11104@0" ObjectIDZND0="g_173c510@0" Pin0InfoVect0LinkObjId="g_173c510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-522 4257,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1739310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-600 4399,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6149@0" ObjectIDZND0="6147@x" ObjectIDZND1="11106@x" Pin0InfoVect0LinkObjId="SW-38465_0" Pin0InfoVect1LinkObjId="SW-58695_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38467_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-600 4399,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1739540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-575 4338,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6149@x" ObjectIDND1="11106@x" ObjectIDZND0="6147@0" Pin0InfoVect0LinkObjId="SW-38465_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38467_0" Pin1InfoVect1LinkObjId="SW-58695_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-575 4338,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_173c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-575 4399,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6149@x" ObjectIDND1="6147@x" ObjectIDZND0="11106@1" Pin0InfoVect0LinkObjId="SW-58695_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38467_0" Pin1InfoVect1LinkObjId="SW-38465_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-575 4399,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_173c2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-520 4399,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11106@0" ObjectIDZND0="g_173cf60@0" Pin0InfoVect0LinkObjId="g_173cf60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-520 4399,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f6820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3709,-787 3675,-787 3675,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6136@x" ObjectIDND1="11097@x" ObjectIDND2="g_16f4970@0" ObjectIDZND0="g_16f5fa0@0" Pin0InfoVect0LinkObjId="g_16f5fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-38454_0" Pin1InfoVect1LinkObjId="SW-58684_0" Pin1InfoVect2LinkObjId="g_16f4970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3709,-787 3675,-787 3675,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f6a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-836 3675,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_16f5fa0@1" ObjectIDZND0="g_17a9900@0" Pin0InfoVect0LinkObjId="g_17a9900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f5fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-836 3675,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f6ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4934,-789 4900,-789 4900,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6167@x" ObjectIDND1="11115@x" ObjectIDND2="g_16f3bc0@0" ObjectIDZND0="g_16f5720@0" Pin0InfoVect0LinkObjId="g_16f5720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-38487_0" Pin1InfoVect1LinkObjId="SW-58689_0" Pin1InfoVect2LinkObjId="g_16f3bc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4934,-789 4900,-789 4900,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-838 4900,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_16f5720@1" ObjectIDZND0="g_1714510@0" Pin0InfoVect0LinkObjId="g_1714510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16f5720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-838 4900,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-743 4905,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_16f5720@0" ObjectIDND1="g_16f3bc0@0" ObjectIDND2="6167@x" ObjectIDZND0="11115@1" Pin0InfoVect0LinkObjId="SW-58689_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16f5720_0" Pin1InfoVect1LinkObjId="g_16f3bc0_0" Pin1InfoVect2LinkObjId="SW-38487_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-743 4905,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e85c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-743 4836,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11115@0" ObjectIDZND0="g_1715900@0" Pin0InfoVect0LinkObjId="g_1715900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-743 4836,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e8820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-447 4464,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="6154@0" Pin0InfoVect0LinkObjId="SW-58476_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1724920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-447 4464,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e8a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-515 4464,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6154@1" ObjectIDZND0="6152@0" Pin0InfoVect0LinkObjId="SW-38474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58476_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-515 4464,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e8ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-513 4672,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6166@1" ObjectIDZND0="6164@0" Pin0InfoVect0LinkObjId="SW-38485_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58477_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-513 4672,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c4330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-448 4113,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="6143@0" Pin0InfoVect0LinkObjId="SW-38461_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1724920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-448 4113,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c4590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-441 4897,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="6162@0" Pin0InfoVect0LinkObjId="SW-38483_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1724920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-441 4897,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c47f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-443 5120,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="6158@0" Pin0InfoVect0LinkObjId="SW-38479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1724920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-443 5120,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c5900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-589 3633,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6137@x" ObjectIDND1="6140@x" ObjectIDZND0="6138@0" Pin0InfoVect0LinkObjId="SW-38456_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38455_0" Pin1InfoVect1LinkObjId="SW-38458_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-589 3633,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16ddb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-446 3633,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="6139@0" Pin0InfoVect0LinkObjId="SW-38457_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1724920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-446 3633,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16de2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-592 3872,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="11103@0" ObjectIDZND0="6133@x" ObjectIDZND1="6134@x" Pin0InfoVect0LinkObjId="SW-38451_0" Pin0InfoVect1LinkObjId="SW-38452_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58693_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-592 3872,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16dedd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-573 3872,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6133@1" ObjectIDZND0="11103@x" ObjectIDZND1="6134@x" Pin0InfoVect0LinkObjId="SW-58693_0" Pin0InfoVect1LinkObjId="SW-38452_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-573 3872,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16df030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-592 3872,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11103@x" ObjectIDND1="6133@x" ObjectIDZND0="6134@0" Pin0InfoVect0LinkObjId="SW-38452_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58693_0" Pin1InfoVect1LinkObjId="SW-38451_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-592 3872,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16df490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-484 3872,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="6135@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1724920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-484 3872,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16df6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-589 3633,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6138@x" ObjectIDND1="6140@x" ObjectIDZND0="6137@1" Pin0InfoVect0LinkObjId="SW-38455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38456_0" Pin1InfoVect1LinkObjId="SW-38458_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-589 3633,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e01e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-601 4113,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6142@0" ObjectIDZND0="6144@x" ObjectIDZND1="6141@x" Pin0InfoVect0LinkObjId="SW-38462_0" Pin0InfoVect1LinkObjId="SW-38459_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-601 4113,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e0440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-591 4113,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6144@x" ObjectIDND1="6142@x" ObjectIDZND0="6141@1" Pin0InfoVect0LinkObjId="SW-38459_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38462_0" Pin1InfoVect1LinkObjId="SW-38460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-591 4113,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e1b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-599 4464,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6153@0" ObjectIDZND0="6155@x" ObjectIDZND1="6152@x" Pin0InfoVect0LinkObjId="SW-38476_0" Pin0InfoVect1LinkObjId="SW-38474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-599 4464,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e1db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-589 4464,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6155@x" ObjectIDND1="6153@x" ObjectIDZND0="6152@1" Pin0InfoVect0LinkObjId="SW-38474_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38476_0" Pin1InfoVect1LinkObjId="SW-38475_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-589 4464,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e28a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-597 4672,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6165@0" ObjectIDZND0="6164@x" ObjectIDZND1="11116@x" Pin0InfoVect0LinkObjId="SW-38485_0" Pin0InfoVect1LinkObjId="SW-58696_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38486_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-597 4672,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e2b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-588 4672,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6165@x" ObjectIDND1="11116@x" ObjectIDZND0="6164@1" Pin0InfoVect0LinkObjId="SW-38485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38486_0" Pin1InfoVect1LinkObjId="SW-58696_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-588 4672,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e2d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-477 4672,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="6166@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1724920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58477_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-477 4672,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b2610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-597 4897,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6161@0" ObjectIDZND0="6163@x" ObjectIDZND1="6160@x" Pin0InfoVect0LinkObjId="SW-38484_0" Pin0InfoVect1LinkObjId="SW-38481_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-597 4897,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b2870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-589 4897,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6163@x" ObjectIDND1="6161@x" ObjectIDZND0="6160@1" Pin0InfoVect0LinkObjId="SW-38481_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38484_0" Pin1InfoVect1LinkObjId="SW-38482_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-589 4897,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b3970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-595 5120,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6157@0" ObjectIDZND0="6159@x" ObjectIDZND1="6156@x" Pin0InfoVect0LinkObjId="SW-38480_0" Pin0InfoVect1LinkObjId="SW-38477_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-595 5120,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b3bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-587 5120,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6159@x" ObjectIDND1="6157@x" ObjectIDZND0="6156@1" Pin0InfoVect0LinkObjId="SW-38477_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38480_0" Pin1InfoVect1LinkObjId="SW-38478_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-587 5120,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1645500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-587 4749,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11116@1" ObjectIDZND0="g_1644a70@0" Pin0InfoVect0LinkObjId="g_1644a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58696_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-587 4749,-587 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6131" cx="3709" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6131" cx="3979" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6131" cx="3633" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6131" cx="3872" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6131" cx="4113" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6132" cx="4933" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6132" cx="4581" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6132" cx="4464" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6132" cx="4672" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6132" cx="4897" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6132" cx="5120" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6131" cx="4257" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6132" cx="4399" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37309" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.000000 -1085.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5891" ObjectName="DYN-CX_BH"/>
     <cge:Meas_Ref ObjectId="37309"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1688c60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3627.000000 -907.000000) translate(0,15)">10kV I段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16f0150" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3594.000000 -367.000000) translate(0,15)">10kV城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1761a30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3819.000000 -370.000000) translate(0,15)">10kV楚城II回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1732250" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4067.000000 -369.000000) translate(0,15)">10kV滨河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1732cd0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4852.000000 -907.000000) translate(0,15)">10kV II段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1713f50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4449.000000 -368.000000) translate(0,15)">10kV滨城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17489d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4626.000000 -365.000000) translate(0,15)">10kV西郊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16da8c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4838.000000 -365.000000) translate(0,15)">10kV岔街支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_163e870" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5050.000000 -363.000000) translate(0,15)">10kV州中医院专线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_175a590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3948.000000 -907.000000) translate(0,15)">10kV1号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_175aec0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4549.000000 -907.000000) translate(0,15)">10kV2号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_175b730" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3506.000000 -676.000000) translate(0,15)">10kV I段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_175b940" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4364.000000 -675.000000) translate(0,15)">10kV II段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175e770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 -775.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17954c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3991.000000 -705.000000) translate(0,12)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1795920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 -709.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170f970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.000000 -704.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170fe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4590.000000 -777.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17100e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -710.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1710420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -602.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1710880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -627.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1710ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4406.000000 -627.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1710d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -626.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1710f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -566.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1711180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -508.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17114c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -586.000000) translate(0,12)">04217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1711a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4681.000000 -564.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1711e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -624.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17120c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -506.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1712300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 -560.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1712540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -624.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1712780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -506.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17129c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4921.000000 -583.000000) translate(0,12)">04417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1712c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -562.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a5070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5127.000000 -622.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a54d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5127.000000 -504.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a5710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5141.000000 -580.000000) translate(0,12)">04517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a5950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3644.000000 -557.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a5b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -626.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a5dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -508.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a6010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3660.000000 -583.000000) translate(0,12)">03417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a6250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3882.000000 -567.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a6490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -629.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a66d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -511.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a6910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -564.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a6b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4120.000000 -628.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4120.000000 -510.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a6fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -586.000000) translate(0,12)">03217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1630ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1630ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1630ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1630ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1630ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1630ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1630ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1634280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3271.500000 -1168.500000) translate(0,16)">滨河开闭所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16be1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3643.000000 -764.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c1500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -755.000000) translate(0,12)">03117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c1710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -832.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c1950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -588.000000) translate(0,12)">03317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c1b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -544.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c1dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -547.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c2010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -583.000000) translate(0,12)">04317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c2250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -769.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c2490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4612.000000 -755.000000) translate(0,12)">04117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -830.000000) translate(0,12)">04167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c4a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -873.000000) translate(0,12)">315kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c4f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 -873.000000) translate(0,12)">315kVA</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-38455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6137" ObjectName="SW-CX_BH.CX_BH_034BK"/>
     <cge:Meas_Ref ObjectId="38455"/>
    <cge:TPSR_Ref TObjectID="6137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -746.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6145" ObjectName="SW-CX_BH.CX_BH_031BK"/>
     <cge:Meas_Ref ObjectId="38463"/>
    <cge:TPSR_Ref TObjectID="6145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -538.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6133" ObjectName="SW-CX_BH.CX_BH_033BK"/>
     <cge:Meas_Ref ObjectId="38451"/>
    <cge:TPSR_Ref TObjectID="6133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38459">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4104.000000 -537.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6141" ObjectName="SW-CX_BH.CX_BH_032BK"/>
     <cge:Meas_Ref ObjectId="38459"/>
    <cge:TPSR_Ref TObjectID="6141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6152" ObjectName="SW-CX_BH.CX_BH_042BK"/>
     <cge:Meas_Ref ObjectId="38474"/>
    <cge:TPSR_Ref TObjectID="6152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4572.000000 -746.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6150" ObjectName="SW-CX_BH.CX_BH_041BK"/>
     <cge:Meas_Ref ObjectId="38472"/>
    <cge:TPSR_Ref TObjectID="6150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 -533.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6164" ObjectName="SW-CX_BH.CX_BH_043BK"/>
     <cge:Meas_Ref ObjectId="38485"/>
    <cge:TPSR_Ref TObjectID="6164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38481">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 -533.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6160" ObjectName="SW-CX_BH.CX_BH_044BK"/>
     <cge:Meas_Ref ObjectId="38481"/>
    <cge:TPSR_Ref TObjectID="6160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38477">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -531.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6156" ObjectName="SW-CX_BH.CX_BH_045BK"/>
     <cge:Meas_Ref ObjectId="38477"/>
    <cge:TPSR_Ref TObjectID="6156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4347.000000 -566.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6147" ObjectName="SW-CX_BH.CX_BH_012BK"/>
     <cge:Meas_Ref ObjectId="38465"/>
    <cge:TPSR_Ref TObjectID="6147"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -833.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -833.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4568.000000 -833.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4568.000000 -833.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3233.500000 -1117.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3245" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3245" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3644" y="-557"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3644" y="-557"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3882" y="-567"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3882" y="-567"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4122" y="-564"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4122" y="-564"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4311" y="-602"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4311" y="-602"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4473" y="-566"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4473" y="-566"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4681" y="-564"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4681" y="-564"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4908" y="-560"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4908" y="-560"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5129" y="-562"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5129" y="-562"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4590" y="-777"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4590" y="-777"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3997" y="-775"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3997" y="-775"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175bd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 798.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175d160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4105.000000 783.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175de70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 768.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a73f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 788.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a76b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 773.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a78f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 758.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a7d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3600.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a7fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a8210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3614.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a8630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a88f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3817.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a8b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a8f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a9210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a9450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a9870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a9b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4407.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d3660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4432.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d3a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4623.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d3d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d3f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d43a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d4660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4835.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d48a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d4cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5070.000000 336.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d4f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.000000 321.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d51c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5084.000000 306.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d5a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 546.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d5d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4261.000000 531.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16d5f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4286.000000 516.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16354a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 756.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bc050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3450.500000 710.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bc4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3436.000000 696.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bc940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 725.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bce60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 740.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bd1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5069.000000 748.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bd440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5075.500000 702.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bd680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.000000 688.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bd8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5069.000000 717.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16bdb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5069.000000 732.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_17a9900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3661.000000 -850.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1714510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4886.000000 -851.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f3bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -802.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f4970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3734.000000 -801.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f5720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4891.000000 -802.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f5fa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3666.000000 -800.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3245" y="-1176"/></g>
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1193"/></g>
   <g href="10kV滨河开闭所10kV城区线034断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3644" y="-557"/></g>
   <g href="10kV滨河开闭所10kV楚城Ⅱ回线033断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3882" y="-567"/></g>
   <g href="10kV滨河开闭所10kV滨河线032断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4122" y="-564"/></g>
   <g href="10kV滨河开闭所10kV母联012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4311" y="-602"/></g>
   <g href="10kV滨河开闭所10kV备用线042断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4473" y="-566"/></g>
   <g href="10kV滨河开闭所10kV西郊线043断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4681" y="-564"/></g>
   <g href="10kV滨河开闭所10kV鹿城西路线044断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4908" y="-560"/></g>
   <g href="10kV滨河开闭所10kV州中医院专线045断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5129" y="-562"/></g>
   <g href="10kV滨河开闭所10kV2号变041断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4590" y="-777"/></g>
   <g href="10kV滨河开闭所10kV1号变031断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3997" y="-775"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_BH.CX_BH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3514,-652 4299,-652 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6131" ObjectName="BS-CX_BH.CX_BH_9IM"/>
    <cge:TPSR_Ref TObjectID="6131"/></metadata>
   <polyline fill="none" opacity="0" points="3514,-652 4299,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BH.CX_BH_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-651 5221,-651 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6132" ObjectName="BS-CX_BH.CX_BH_9IIM"/>
    <cge:TPSR_Ref TObjectID="6132"/></metadata>
   <polyline fill="none" opacity="0" points="4363,-651 5221,-651 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -788.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6150"/>
     <cge:Term_Ref ObjectID="8905"/>
    <cge:TPSR_Ref TObjectID="6150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -788.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6150"/>
     <cge:Term_Ref ObjectID="8905"/>
    <cge:TPSR_Ref TObjectID="6150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -788.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6150"/>
     <cge:Term_Ref ObjectID="8905"/>
    <cge:TPSR_Ref TObjectID="6150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6152"/>
     <cge:Term_Ref ObjectID="8909"/>
    <cge:TPSR_Ref TObjectID="6152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6152"/>
     <cge:Term_Ref ObjectID="8909"/>
    <cge:TPSR_Ref TObjectID="6152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6152"/>
     <cge:Term_Ref ObjectID="8909"/>
    <cge:TPSR_Ref TObjectID="6152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4677.000000 -336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6164"/>
     <cge:Term_Ref ObjectID="8933"/>
    <cge:TPSR_Ref TObjectID="6164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4677.000000 -336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6164"/>
     <cge:Term_Ref ObjectID="8933"/>
    <cge:TPSR_Ref TObjectID="6164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4677.000000 -336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6164"/>
     <cge:Term_Ref ObjectID="8933"/>
    <cge:TPSR_Ref TObjectID="6164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4902.000000 -336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6160"/>
     <cge:Term_Ref ObjectID="8925"/>
    <cge:TPSR_Ref TObjectID="6160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4902.000000 -336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38432" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6160"/>
     <cge:Term_Ref ObjectID="8925"/>
    <cge:TPSR_Ref TObjectID="6160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4902.000000 -336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6160"/>
     <cge:Term_Ref ObjectID="8925"/>
    <cge:TPSR_Ref TObjectID="6160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6156"/>
     <cge:Term_Ref ObjectID="8917"/>
    <cge:TPSR_Ref TObjectID="6156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6156"/>
     <cge:Term_Ref ObjectID="8917"/>
    <cge:TPSR_Ref TObjectID="6156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6156"/>
     <cge:Term_Ref ObjectID="8917"/>
    <cge:TPSR_Ref TObjectID="6156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-38443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -748.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6132"/>
     <cge:Term_Ref ObjectID="8870"/>
    <cge:TPSR_Ref TObjectID="6132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-38444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -748.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6132"/>
     <cge:Term_Ref ObjectID="8870"/>
    <cge:TPSR_Ref TObjectID="6132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-38445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -748.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6132"/>
     <cge:Term_Ref ObjectID="8870"/>
    <cge:TPSR_Ref TObjectID="6132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-38446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -748.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6132"/>
     <cge:Term_Ref ObjectID="8870"/>
    <cge:TPSR_Ref TObjectID="6132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-38447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -748.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6132"/>
     <cge:Term_Ref ObjectID="8870"/>
    <cge:TPSR_Ref TObjectID="6132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -797.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6145"/>
     <cge:Term_Ref ObjectID="8895"/>
    <cge:TPSR_Ref TObjectID="6145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38392" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -797.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6145"/>
     <cge:Term_Ref ObjectID="8895"/>
    <cge:TPSR_Ref TObjectID="6145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -797.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6145"/>
     <cge:Term_Ref ObjectID="8895"/>
    <cge:TPSR_Ref TObjectID="6145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38383" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -333.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6141"/>
     <cge:Term_Ref ObjectID="8887"/>
    <cge:TPSR_Ref TObjectID="6141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -333.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6141"/>
     <cge:Term_Ref ObjectID="8887"/>
    <cge:TPSR_Ref TObjectID="6141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38379" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -333.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6141"/>
     <cge:Term_Ref ObjectID="8887"/>
    <cge:TPSR_Ref TObjectID="6141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38364" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -334.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38364" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6133"/>
     <cge:Term_Ref ObjectID="8871"/>
    <cge:TPSR_Ref TObjectID="6133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38365" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -334.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38365" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6133"/>
     <cge:Term_Ref ObjectID="8871"/>
    <cge:TPSR_Ref TObjectID="6133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38360" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -334.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38360" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6133"/>
     <cge:Term_Ref ObjectID="8871"/>
    <cge:TPSR_Ref TObjectID="6133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-38356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -753.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6131"/>
     <cge:Term_Ref ObjectID="8869"/>
    <cge:TPSR_Ref TObjectID="6131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-38357" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -753.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38357" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6131"/>
     <cge:Term_Ref ObjectID="8869"/>
    <cge:TPSR_Ref TObjectID="6131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-38358" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -753.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38358" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6131"/>
     <cge:Term_Ref ObjectID="8869"/>
    <cge:TPSR_Ref TObjectID="6131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-38359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -753.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6131"/>
     <cge:Term_Ref ObjectID="8869"/>
    <cge:TPSR_Ref TObjectID="6131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-38368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -753.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6131"/>
     <cge:Term_Ref ObjectID="8869"/>
    <cge:TPSR_Ref TObjectID="6131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -545.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6147"/>
     <cge:Term_Ref ObjectID="8899"/>
    <cge:TPSR_Ref TObjectID="6147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -545.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6147"/>
     <cge:Term_Ref ObjectID="8899"/>
    <cge:TPSR_Ref TObjectID="6147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -545.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6147"/>
     <cge:Term_Ref ObjectID="8899"/>
    <cge:TPSR_Ref TObjectID="6147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -333.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6137"/>
     <cge:Term_Ref ObjectID="8879"/>
    <cge:TPSR_Ref TObjectID="6137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -333.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6137"/>
     <cge:Term_Ref ObjectID="8879"/>
    <cge:TPSR_Ref TObjectID="6137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38371" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -333.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38371" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6137"/>
     <cge:Term_Ref ObjectID="8879"/>
    <cge:TPSR_Ref TObjectID="6137"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_BH"/>
</svg>