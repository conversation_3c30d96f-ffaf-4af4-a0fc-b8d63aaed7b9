<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-263" aopId="3940870" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1029 1710 1331">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="capacitor:shape29">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="19" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
   </symbol>
   <symbol id="currentTransformer:shape6">
    <circle cx="7" cy="11" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="16" y1="36" y2="36"/>
    <circle cx="16" cy="16" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="16" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="26" x2="26" y1="29" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="33" x2="33" y1="29" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="33" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="14" x2="18" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="12" x2="20" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.658501" x1="22" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="23" y2="54"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape20">
    <polyline DF8003:Layer="PUBLIC" points="2,51 16,51 9,39 2,51 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="7" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="2" x2="17" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="5" x2="14" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="9" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="2,23 16,23 9,35 2,23 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="65" y2="51"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05051" x1="47" x2="47" y1="33" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="56" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="63" x2="63" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="60" x2="60" y1="22" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="56" x2="56" y1="26" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="33" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="33" y2="33"/>
   </symbol>
   <symbol id="switch2:shape46_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05051" x1="38" x2="38" y1="33" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="22" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="26" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="33" y2="33"/>
   </symbol>
   <symbol id="switch2:shape46-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05051" x1="47" x2="47" y1="33" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="56" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="63" x2="63" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="60" x2="60" y1="22" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="56" x2="56" y1="26" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="33" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="33" y2="33"/>
   </symbol>
   <symbol id="switch2:shape46-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05051" x1="38" x2="38" y1="33" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="22" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="26" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="33" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape38_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape38_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
   </symbol>
   <symbol id="voltageTransformer:shape78">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="40" x2="40" y1="14" y2="1"/>
    <polyline points="6,39 6,23 29,23 " stroke-width="0.587413"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="41" x2="41" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="38" x2="41" y1="43" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="38" x2="41" y1="40" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="30" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="30" x2="30" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="48" x2="45" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="45" x2="45" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="45" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="30" y2="33"/>
    <circle cx="30" cy="41" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="45" cy="30" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="40" cy="41" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="38" x2="35" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="35" x2="35" y1="23" y2="26"/>
    <circle cx="25" cy="30" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="35" y1="21" y2="23"/>
    <circle cx="35" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="23" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="47" x2="47" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="47" y1="7" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="23" x2="23" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="30" x2="30" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="30" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="23" y1="8" y2="8"/>
   </symbol>
   <symbol id="voltageTransformer:shape86">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="15" x2="27" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="15" y2="8"/>
    <rect height="16" stroke-width="1" width="9" x="18" y="16"/>
    <polyline points="28,19 16,28 16,32 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="22" x2="20" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="24" x2="18" y1="5" y2="5"/>
    <circle cx="8" cy="61" r="7.5" stroke-width="0.804311"/>
    <circle cx="21" cy="61" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="10" x2="6" y1="63" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="10" x2="6" y1="60" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="6" x2="6" y1="59" y2="64"/>
    <circle cx="8" cy="48" r="7.5" stroke-width="0.804311"/>
    <circle cx="21" cy="48" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="24" x2="22" y1="52" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="24" y1="48" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="17" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="24" x2="22" y1="66" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="24" y1="62" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="17" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="11" x2="9" y1="52" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="9" x2="11" y1="48" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="9" x2="4" y1="48" y2="48"/>
    <circle cx="8" cy="35" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="11" x2="9" y1="39" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="9" x2="11" y1="35" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="9" x2="4" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.29526" x1="23" x2="23" y1="31" y2="41"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f7afd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26910e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f7d610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f7e2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f7f550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f80170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f80bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f816f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f7c650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f7c650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f84ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f84ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f86850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f86850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2f87870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f89470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f8a060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f8ae20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8b760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8ce20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8da00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8e2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f8ea80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f8fb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f904e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f90eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f91870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f92cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f93890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2f948c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f95500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fa3cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f96ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f97d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f992e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1341" width="1720" x="11" y="-1034"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="739" x2="739" y1="99" y2="55"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="638" x2="638" y1="56" y2="56"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="679" x2="739" y1="55" y2="55"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-211914">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1025.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31671" ObjectName="SW-CX_YT.CX_YT_1516SW"/>
     <cge:Meas_Ref ObjectId="211914"/>
    <cge:TPSR_Ref TObjectID="31671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211912">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1025.000000 -715.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31669" ObjectName="SW-CX_YT.CX_YT_1511SW"/>
     <cge:Meas_Ref ObjectId="211912"/>
    <cge:TPSR_Ref TObjectID="31669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211913">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 959.000000 -768.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31670" ObjectName="SW-CX_YT.CX_YT_15117SW"/>
     <cge:Meas_Ref ObjectId="211913"/>
    <cge:TPSR_Ref TObjectID="31670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211916">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -830.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31673" ObjectName="SW-CX_YT.CX_YT_15160SW"/>
     <cge:Meas_Ref ObjectId="211916"/>
    <cge:TPSR_Ref TObjectID="31673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211915">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.000000 -899.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31672" ObjectName="SW-CX_YT.CX_YT_15167SW"/>
     <cge:Meas_Ref ObjectId="211915"/>
    <cge:TPSR_Ref TObjectID="31672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211989">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 -645.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31689" ObjectName="SW-CX_YT.CX_YT_1011SW"/>
     <cge:Meas_Ref ObjectId="211989"/>
    <cge:TPSR_Ref TObjectID="31689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211991">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 -514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31691" ObjectName="SW-CX_YT.CX_YT_1016SW"/>
     <cge:Meas_Ref ObjectId="211991"/>
    <cge:TPSR_Ref TObjectID="31691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211993">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -567.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31693" ObjectName="SW-CX_YT.CX_YT_10160SW"/>
     <cge:Meas_Ref ObjectId="211993"/>
    <cge:TPSR_Ref TObjectID="31693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211990">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 -629.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31690" ObjectName="SW-CX_YT.CX_YT_10117SW"/>
     <cge:Meas_Ref ObjectId="211990"/>
    <cge:TPSR_Ref TObjectID="31690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211995">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 -645.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31694" ObjectName="SW-CX_YT.CX_YT_1031SW"/>
     <cge:Meas_Ref ObjectId="211995"/>
    <cge:TPSR_Ref TObjectID="31694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211997">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 -514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31696" ObjectName="SW-CX_YT.CX_YT_1036SW"/>
     <cge:Meas_Ref ObjectId="211997"/>
    <cge:TPSR_Ref TObjectID="31696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211999">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.000000 -567.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31698" ObjectName="SW-CX_YT.CX_YT_10360SW"/>
     <cge:Meas_Ref ObjectId="211999"/>
    <cge:TPSR_Ref TObjectID="31698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211996">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.000000 -629.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31695" ObjectName="SW-CX_YT.CX_YT_10317SW"/>
     <cge:Meas_Ref ObjectId="211996"/>
    <cge:TPSR_Ref TObjectID="31695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211992">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 -498.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31692" ObjectName="SW-CX_YT.CX_YT_10167SW"/>
     <cge:Meas_Ref ObjectId="211992"/>
    <cge:TPSR_Ref TObjectID="31692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211998">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.000000 -496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31697" ObjectName="SW-CX_YT.CX_YT_10367SW"/>
     <cge:Meas_Ref ObjectId="211998"/>
    <cge:TPSR_Ref TObjectID="31697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -77.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 -76.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211938">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 693.000000 -39.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31677" ObjectName="SW-CX_YT.CX_YT_05160SW"/>
     <cge:Meas_Ref ObjectId="211938"/>
    <cge:TPSR_Ref TObjectID="31677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-212001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.000000 -30.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31702" ObjectName="SW-CX_YT.CX_YT_05367SW"/>
     <cge:Meas_Ref ObjectId="212001"/>
    <cge:TPSR_Ref TObjectID="31702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-212014">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 595.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31686" ObjectName="SW-CX_YT.CX_YT_1010SW"/>
     <cge:Meas_Ref ObjectId="212014"/>
    <cge:TPSR_Ref TObjectID="31686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-212000">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31700" ObjectName="SW-CX_YT.CX_YT_1030SW"/>
     <cge:Meas_Ref ObjectId="212000"/>
    <cge:TPSR_Ref TObjectID="31700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211942">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -750.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31680" ObjectName="SW-CX_YT.CX_YT_1901SW"/>
     <cge:Meas_Ref ObjectId="211942"/>
    <cge:TPSR_Ref TObjectID="31680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211944">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -727.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31682" ObjectName="SW-CX_YT.CX_YT_19010SW"/>
     <cge:Meas_Ref ObjectId="211944"/>
    <cge:TPSR_Ref TObjectID="31682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211943">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -804.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31681" ObjectName="SW-CX_YT.CX_YT_19017SW"/>
     <cge:Meas_Ref ObjectId="211943"/>
    <cge:TPSR_Ref TObjectID="31681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211939">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 673.000000 75.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31678" ObjectName="SW-CX_YT.CX_YT_0516SW"/>
     <cge:Meas_Ref ObjectId="211939"/>
    <cge:TPSR_Ref TObjectID="31678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 110.000000)" xlink:href="#switch2:shape46_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31679" ObjectName="SW-CX_YT.CX_YT_05167SW"/>
     <cge:Meas_Ref ObjectId="211940"/>
    <cge:TPSR_Ref TObjectID="31679"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ff1d30">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 621.000000 -858.000000)" xlink:href="#voltageTransformer:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb4210">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 500.000000 90.000000)" xlink:href="#voltageTransformer:shape86"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_MuD" endPointId="0" endStationName="CX_YT" flowDrawDirect="1" flowShape="0" id="AC-110kV.dantaizhi_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1034,-967 1034,-1005 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31741" ObjectName="AC-110kV.dantaizhi_line"/>
    <cge:TPSR_Ref TObjectID="31741_SS-263"/></metadata>
   <polyline fill="none" opacity="0" points="1034,-967 1034,-1005 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YT.CX_YT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="45690"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 738.000000 -384.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 738.000000 -384.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="31701" ObjectName="TF-CX_YT.CX_YT_1T"/>
    <cge:TPSR_Ref TObjectID="31701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 -230.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 -230.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -230.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -230.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1201.000000 -230.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1201.000000 -230.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 170.000000)" xlink:href="#transformer2:shape38_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 170.000000)" xlink:href="#transformer2:shape38_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e37140">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.000000 -941.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7e4e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.000000 -479.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e80f50">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -296.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e81970">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.000000 -364.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e836d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.000000 -281.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fb8c10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.000000 -52.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fb99c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 512.000000 -0.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fbb450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 -37.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fbf1c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 769.000000 -14.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc0ee0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 -29.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc2710">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 60.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc37f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 -467.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc64d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1351.000000 -362.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc6ef0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1269.000000 -335.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fccff0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 -363.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fcda10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 -362.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fce430">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -335.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fcf1e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 -336.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd4710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 22.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd4df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 -27.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fdb710">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 -374.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fdf690">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 -366.000000)" xlink:href="#lightningRod:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe1580">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1141.000000 -218.000000)" xlink:href="#lightningRod:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe24c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 -228.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ff6650">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 -829.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ffda90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 27.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211952" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -756.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211952" ObjectName="CX_YT:CX_YT_1IM_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211953" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -743.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211953" ObjectName="CX_YT:CX_YT_1IM_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211954" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -728.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211954" ObjectName="CX_YT:CX_YT_1IM_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211955" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -713.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211955" ObjectName="CX_YT:CX_YT_1IM_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211958" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -700.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211958" ObjectName="CX_YT:CX_YT_1IM_U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211959" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 -686.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211959" ObjectName="CX_YT:CX_YT_1IM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211961" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -624.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211961" ObjectName="CX_YT:CX_YT_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211962" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -609.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211962" ObjectName="CX_YT:CX_YT_101BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211960" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -593.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211960" ObjectName="CX_YT:CX_YT_101BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211963" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -579.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211963" ObjectName="CX_YT:CX_YT_101BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211965" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -259.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211965" ObjectName="CX_YT:CX_YT_001BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211966" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -243.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211966" ObjectName="CX_YT:CX_YT_001BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211964" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -229.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211964" ObjectName="CX_YT:CX_YT_001BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211967" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -214.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211967" ObjectName="CX_YT:CX_YT_001BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211969" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -620.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211969" ObjectName="CX_YT:CX_YT_103BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211970" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -603.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211970" ObjectName="CX_YT:CX_YT_103BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211968" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -585.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211968" ObjectName="CX_YT:CX_YT_103BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211971" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -570.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211971" ObjectName="CX_YT:CX_YT_103BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211972" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -269.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211972" ObjectName="CX_YT:CX_YT_9IM_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211973" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -252.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211973" ObjectName="CX_YT:CX_YT_9IM_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211974" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -235.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211974" ObjectName="CX_YT:CX_YT_9IM_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211975" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -217.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211975" ObjectName="CX_YT:CX_YT_9IM_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211978" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -202.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211978" ObjectName="CX_YT:CX_YT_9IM_U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211979" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -185.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211979" ObjectName="CX_YT:CX_YT_9IM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211985" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 240.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211985" ObjectName="CX_YT:CX_YT_051BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211986" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 254.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211986" ObjectName="CX_YT:CX_YT_051BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211984" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 268.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211984" ObjectName="CX_YT:CX_YT_051BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211987" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 284.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211987" ObjectName="CX_YT:CX_YT_051BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211981" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 110.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211981" ObjectName="CX_YT:CX_YT_053BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211982" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 127.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211982" ObjectName="CX_YT:CX_YT_053BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211980" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 144.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211980" ObjectName="CX_YT:CX_YT_053BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211983" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 159.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211983" ObjectName="CX_YT:CX_YT_053BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211948" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -811.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211948" ObjectName="CX_YT:CX_YT_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211949" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -796.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211949" ObjectName="CX_YT:CX_YT_151BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211947" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -781.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211947" ObjectName="CX_YT:CX_YT_151BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211950" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -766.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211950" ObjectName="CX_YT:CX_YT_151BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211949" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -776.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211949" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-211948" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 -814.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="211948" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-209599" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.500000 -917.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31655" ObjectName="DYN-CX_YT"/>
     <cge:Meas_Ref ObjectId="209599"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30082b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.500000 -925.000000) translate(0,15)">110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30082b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.500000 -925.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30082b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.500000 -925.000000) translate(0,51)">丹</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30082b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.500000 -925.000000) translate(0,69)">钛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30082b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.500000 -925.000000) translate(0,87)">纸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30082b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.500000 -925.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_301dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_301dea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 159.000000 -1001.500000) translate(0,16)">永钛变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3003e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 614.500000 -939.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3003e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 614.500000 -939.000000) translate(0,26)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3006620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.500000 -312.000000) translate(0,12)">HTDSPZ11-6700/110（单相容量）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3006620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.500000 -312.000000) translate(0,26)">110±10×2%/0.28-0.34-0.45kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3006620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.500000 -312.000000) translate(0,40)">Ud=3.27%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_3006620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1520.500000 -312.000000) translate(0,54)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.500000 -440.000000) translate(0,12)">S11-1250/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.500000 -440.000000) translate(0,26)">1.25MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.500000 -440.000000) translate(0,40)">110±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.500000 -440.000000) translate(0,54)">Ud=5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.500000 -440.000000) translate(0,68)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9b080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.500000 -463.000000) translate(0,12)">1号动力变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9c7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1521.500000 -112.000000) translate(0,12)">1号钛渣炉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.500000 92.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 484.500000 92.000000) translate(0,26)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.500000 215.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 812.500000 179.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9e3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.500000 79.000000) translate(0,12)">抽水线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2e9ec70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1516.500000 -336.000000) translate(0,12)">1号电炉变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9eff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1043.000000 -810.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -876.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9f440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -745.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9f680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -799.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9f8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 965.000000 -861.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9fb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -899.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9fd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -780.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9ff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -835.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea01c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 717.000000 -758.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -609.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 -660.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea0880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -675.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea0ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 691.000000 -598.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea0d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -529.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea0f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -544.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 774.000000 -234.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea13c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1014.000000 -111.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 -111.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 -68.000000) translate(0,12)">05160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 40.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1339.000000 -609.000000) translate(0,12)">103</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -269.000000) translate(0,12)">1030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -660.000000) translate(0,12)">10317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1337.000000 -675.000000) translate(0,12)">1031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea25c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 -598.000000) translate(0,12)">10360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -527.000000) translate(0,12)">10367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1337.000000 -544.000000) translate(0,12)">1036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -723.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 498.000000 -186.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea3380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -420.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eacab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1093.000000 -797.000000) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaccf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1118.000000 -782.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eacf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -812.000000) translate(0,12)">P(MW):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ead170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 461.000000 -729.000000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ead3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 -714.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ead5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -682.000000) translate(0,12)">F(Hz):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ead830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1117.000000 -763.000000) translate(0,12)">COS:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eade20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 461.000000 -758.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eae0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 461.000000 -743.000000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eae2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 -697.000000) translate(0,12)">3U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eae520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 -190.000000) translate(0,12)">F(Hz):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eae760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -266.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eae9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -251.000000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaebe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 380.000000 -205.000000) translate(0,12)">3U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -237.000000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaf060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 373.000000 -222.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaf2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 -596.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaf4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 813.000000 -626.000000) translate(0,12)">P(MW):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaf720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -577.000000) translate(0,12)">COS:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaf960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 802.000000 -611.000000) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eafba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1438.000000 -588.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eafde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1424.000000 -618.000000) translate(0,12)">P(MW):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -569.000000) translate(0,12)">COS:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1413.000000 -603.000000) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb1560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -260.000000) translate(0,12)">P(MW):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb17a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -211.000000) translate(0,12)">COS:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb19e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -245.000000) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb1c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -230.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb1e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 989.000000 160.000000) translate(0,12)">COS:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb20a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 965.000000 126.000000) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb22e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 141.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb2520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 111.000000) translate(0,12)">P(MW):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb2760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 631.000000 253.000000) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb29a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 268.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb2be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 238.000000) translate(0,12)">P(MW):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb2e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 287.000000) translate(0,12)">COS:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -519.000000) translate(0,12)">1号动力变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2eb88f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.500000 -122.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2eb92d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.500000 -119.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb9690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -61.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb9ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -989.000000) translate(0,12)">110kV丹钛纸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec12e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 54.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 122.000000 -61.000000) translate(0,12)">13987815993</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-211911">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1025.000000 -781.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31668" ObjectName="SW-CX_YT.CX_YT_151BK"/>
     <cge:Meas_Ref ObjectId="211911"/>
    <cge:TPSR_Ref TObjectID="31668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211930">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31674" ObjectName="SW-CX_YT.CX_YT_101BK"/>
     <cge:Meas_Ref ObjectId="211930"/>
    <cge:TPSR_Ref TObjectID="31674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211994">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31688" ObjectName="SW-CX_YT.CX_YT_103BK"/>
     <cge:Meas_Ref ObjectId="211994"/>
    <cge:TPSR_Ref TObjectID="31688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -174.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31687" ObjectName="SW-CX_YT.CX_YT_001BK"/>
     <cge:Meas_Ref ObjectId="211988"/>
    <cge:TPSR_Ref TObjectID="31687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211937">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 672.000000 -51.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31676" ObjectName="SW-CX_YT.CX_YT_051BK"/>
     <cge:Meas_Ref ObjectId="211937"/>
    <cge:TPSR_Ref TObjectID="31676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-211935">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.000000 -51.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31675" ObjectName="SW-CX_YT.CX_YT_053BK"/>
     <cge:Meas_Ref ObjectId="211935"/>
    <cge:TPSR_Ref TObjectID="31675"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YT.CX_YT_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-703 1536,-703 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31684" ObjectName="BS-CX_YT.CX_YT_1IM"/>
    <cge:TPSR_Ref TObjectID="31684"/></metadata>
   <polyline fill="none" opacity="0" points="566,-703 1536,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YT.CX_YT_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="498,-166 1030,-166 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31685" ObjectName="BS-CX_YT.CX_YT_9IM"/>
    <cge:TPSR_Ref TObjectID="31685"/></metadata>
   <polyline fill="none" opacity="0" points="498,-166 1030,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-449 1544,-449 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1162,-449 1544,-449 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d023a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 -767.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d02e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 -829.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d038c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 -898.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3009940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -566.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_300a390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -628.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e747c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1230.000000 -566.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e751f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1230.000000 -628.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7a5d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.000000 -497.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7b2c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1232.000000 -495.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fbe730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -38.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd8090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 912.000000 -29.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fdec40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 -367.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe59f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -219.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fef3a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.000000 -726.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fefe30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -803.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1330" cy="-449" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1356" cy="-449" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1483" cy="-449" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1226" cy="-449" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31684" cx="1034" cy="-703" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31684" cx="763" cy="-703" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31684" cx="1330" cy="-703" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31685" cx="521" cy="-166" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31685" cx="682" cy="-166" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31685" cx="842" cy="-166" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31685" cx="1002" cy="-166" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31685" cx="762" cy="-166" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31684" cx="690" cy="-703" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="110kV永钛变CX_YT_151间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1043" y="-810"/></g>
   <g href="110kV永钛变1号电炉变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1339" y="-609"/></g>
   <g href="110kV永钛变1号动力变间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="75" x="896" y="-519"/></g>
   <g href="110kV永钛变CX_YT_051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="694" y="-111"/></g>
   <g href="110kV永钛变CX_YT_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1014" y="-111"/></g>
  </g><g id="ArcThreePointsF_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1195,-109 1218,-97 1241,-85 1266,-75 1290,-68 1316,-63 1342,-62 1368,-62 1394,-63 1420,-68 1444,-75 1469,-85 1492,-97 1515,-109 1195,-109 " stroke="rgb(60,120,255)"/>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 202.000000)" xlink:href="#capacitor:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1043" y="-810"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1043" y="-810"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1339" y="-609"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1339" y="-609"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="75" x="896" y="-519"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="75" x="896" y="-519"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="694" y="-111"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="694" y="-111"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1014" y="-111"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1014" y="-111"/></g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d05a20">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.000000 -913.000000)" xlink:href="#currentTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_2d04350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-773 1034,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31670@1" ObjectIDZND0="31668@x" ObjectIDZND1="31669@x" Pin0InfoVect0LinkObjId="SW-211911_0" Pin0InfoVect1LinkObjId="SW-211912_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211913_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-773 1034,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d04e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-789 1034,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31668@0" ObjectIDZND0="31670@x" ObjectIDZND1="31669@x" Pin0InfoVect0LinkObjId="SW-211913_0" Pin0InfoVect1LinkObjId="SW-211912_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211911_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-789 1034,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d050a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-777 1034,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31670@x" ObjectIDND1="31668@x" ObjectIDZND0="31669@1" Pin0InfoVect0LinkObjId="SW-211912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211913_0" Pin1InfoVect1LinkObjId="SW-211911_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-777 1034,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d05300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-904 969,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d038c0@0" ObjectIDZND0="31672@0" Pin0InfoVect0LinkObjId="SW-211915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d038c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-904 969,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d05560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-773 964,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d023a0@0" ObjectIDZND0="31670@0" Pin0InfoVect0LinkObjId="SW-211913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d023a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-773 964,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d057c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,-835 1034,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31673@1" ObjectIDZND0="31668@x" ObjectIDZND1="31671@x" Pin0InfoVect0LinkObjId="SW-211911_0" Pin0InfoVect1LinkObjId="SW-211914_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1002,-835 1034,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e37eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-835 966,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d02e30@0" ObjectIDZND0="31673@0" Pin0InfoVect0LinkObjId="SW-211916_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d02e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-835 966,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e39330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1058,-949 1034,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e37140@0" ObjectIDZND0="g_2d05a20@0" ObjectIDZND1="31672@x" ObjectIDZND2="31671@x" Pin0InfoVect0LinkObjId="g_2d05a20_0" Pin0InfoVect1LinkObjId="SW-211915_0" Pin0InfoVect2LinkObjId="SW-211914_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e37140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1058,-949 1034,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-949 1034,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="currentTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2e37140@0" ObjectIDND1="g_2d05a20@0" ObjectIDND2="31672@x" ObjectIDZND0="31741@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e37140_0" Pin1InfoVect1LinkObjId="g_2d05a20_0" Pin1InfoVect2LinkObjId="SW-211915_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-949 1034,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1011,-949 1034,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2d05a20@0" ObjectIDZND0="g_2e37140@0" ObjectIDZND1="31672@x" ObjectIDZND2="31671@x" Pin0InfoVect0LinkObjId="g_2e37140_0" Pin0InfoVect1LinkObjId="SW-211915_0" Pin0InfoVect2LinkObjId="SW-211914_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d05a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1011,-949 1034,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-904 1034,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="currentTransformer" ObjectIDND0="31672@1" ObjectIDZND0="31671@x" ObjectIDZND1="g_2e37140@0" ObjectIDZND2="g_2d05a20@0" Pin0InfoVect0LinkObjId="SW-211914_0" Pin0InfoVect1LinkObjId="g_2e37140_0" Pin0InfoVect2LinkObjId="g_2d05a20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-904 1034,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3aff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-887 1034,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="currentTransformer" ObjectIDND0="31671@1" ObjectIDZND0="31672@x" ObjectIDZND1="g_2e37140@0" ObjectIDZND2="g_2d05a20@0" Pin0InfoVect0LinkObjId="SW-211915_0" Pin0InfoVect1LinkObjId="g_2e37140_0" Pin0InfoVect2LinkObjId="g_2d05a20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-887 1034,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3b250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-904 1034,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="currentTransformer" EndDevType2="powerLine" ObjectIDND0="31672@x" ObjectIDND1="31671@x" ObjectIDZND0="g_2e37140@0" ObjectIDZND1="g_2d05a20@0" ObjectIDZND2="31741@1" Pin0InfoVect0LinkObjId="g_2e37140_0" Pin0InfoVect1LinkObjId="g_2d05a20_0" Pin0InfoVect2LinkObjId="g_2e3a040_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211915_0" Pin1InfoVect1LinkObjId="SW-211914_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-904 1034,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3bd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-835 1034,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="31673@x" ObjectIDND1="31671@x" ObjectIDZND0="31668@1" Pin0InfoVect0LinkObjId="SW-211911_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211916_0" Pin1InfoVect1LinkObjId="SW-211914_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-835 1034,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-851 1034,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="31671@0" ObjectIDZND0="31673@x" ObjectIDZND1="31668@x" Pin0InfoVect0LinkObjId="SW-211916_0" Pin0InfoVect1LinkObjId="SW-211911_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-851 1034,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300adf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="729,-572 763,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31693@1" ObjectIDZND0="31674@x" ObjectIDZND1="31691@x" Pin0InfoVect0LinkObjId="SW-211930_0" Pin0InfoVect1LinkObjId="SW-211991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211993_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="729,-572 763,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300b050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-588 763,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31674@0" ObjectIDZND0="31693@x" ObjectIDZND1="31691@x" Pin0InfoVect0LinkObjId="SW-211993_0" Pin0InfoVect1LinkObjId="SW-211991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="763,-588 763,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-576 763,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31693@x" ObjectIDND1="31674@x" ObjectIDZND0="31691@1" Pin0InfoVect0LinkObjId="SW-211991_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211993_0" Pin1InfoVect1LinkObjId="SW-211930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-576 763,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300b510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-572 693,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3009940@0" ObjectIDZND0="31693@0" Pin0InfoVect0LinkObjId="SW-211993_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3009940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-572 693,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-634 763,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31690@1" ObjectIDZND0="31674@x" ObjectIDZND1="31689@x" Pin0InfoVect0LinkObjId="SW-211930_0" Pin0InfoVect1LinkObjId="SW-211989_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="731,-634 763,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-634 695,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_300a390@0" ObjectIDZND0="31690@0" Pin0InfoVect0LinkObjId="SW-211990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_300a390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-634 695,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-634 763,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="31690@x" ObjectIDND1="31689@x" ObjectIDZND0="31674@1" Pin0InfoVect0LinkObjId="SW-211930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211990_0" Pin1InfoVect1LinkObjId="SW-211989_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-634 763,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_300be90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-650 763,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="31689@0" ObjectIDZND0="31690@x" ObjectIDZND1="31674@x" Pin0InfoVect0LinkObjId="SW-211990_0" Pin0InfoVect1LinkObjId="SW-211930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="763,-650 763,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e75c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1296,-572 1330,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31698@1" ObjectIDZND0="31688@x" ObjectIDZND1="31696@x" Pin0InfoVect0LinkObjId="SW-211994_0" Pin0InfoVect1LinkObjId="SW-211997_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1296,-572 1330,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e75ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-588 1330,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31688@0" ObjectIDZND0="31698@x" ObjectIDZND1="31696@x" Pin0InfoVect0LinkObjId="SW-211999_0" Pin0InfoVect1LinkObjId="SW-211997_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-588 1330,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-576 1330,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31698@x" ObjectIDND1="31688@x" ObjectIDZND0="31696@1" Pin0InfoVect0LinkObjId="SW-211997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211999_0" Pin1InfoVect1LinkObjId="SW-211994_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-576 1330,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e763a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-572 1260,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e747c0@0" ObjectIDZND0="31698@0" Pin0InfoVect0LinkObjId="SW-211999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e747c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-572 1260,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1298,-634 1330,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31695@1" ObjectIDZND0="31688@x" ObjectIDZND1="31694@x" Pin0InfoVect0LinkObjId="SW-211994_0" Pin0InfoVect1LinkObjId="SW-211995_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1298,-634 1330,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-634 1262,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e751f0@0" ObjectIDZND0="31695@0" Pin0InfoVect0LinkObjId="SW-211996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e751f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-634 1262,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-634 1330,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="31695@x" ObjectIDND1="31694@x" ObjectIDZND0="31688@1" Pin0InfoVect0LinkObjId="SW-211994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211996_0" Pin1InfoVect1LinkObjId="SW-211995_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-634 1330,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-650 1330,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="31694@0" ObjectIDZND0="31695@x" ObjectIDZND1="31688@x" Pin0InfoVect0LinkObjId="SW-211996_0" Pin0InfoVect1LinkObjId="SW-211994_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-650 1330,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e7b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="680,-503 692,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e7a5d0@0" ObjectIDZND0="31692@0" Pin0InfoVect0LinkObjId="SW-211992_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7a5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="680,-503 692,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e7bd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1250,-501 1262,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e7b2c0@0" ObjectIDZND0="31697@0" Pin0InfoVect0LinkObjId="SW-211998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7b2c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1250,-501 1262,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e7f250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="728,-503 763,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="31692@1" ObjectIDZND0="31691@x" ObjectIDZND1="g_2e7e4e0@0" ObjectIDZND2="31701@x" Pin0InfoVect0LinkObjId="SW-211991_0" Pin0InfoVect1LinkObjId="g_2e7e4e0_0" Pin0InfoVect2LinkObjId="g_2e80cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211992_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="728,-503 763,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e7fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-519 763,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="31691@0" ObjectIDZND0="31692@1" ObjectIDZND1="g_2e7e4e0@0" ObjectIDZND2="31701@x" Pin0InfoVect0LinkObjId="SW-211992_1" Pin0InfoVect1LinkObjId="g_2e7e4e0_0" Pin0InfoVect2LinkObjId="g_2e80cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211991_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="763,-519 763,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e7ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-487 790,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="31692@x" ObjectIDND1="31691@x" ObjectIDND2="31701@x" ObjectIDZND0="g_2e7e4e0@0" Pin0InfoVect0LinkObjId="g_2e7e4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-211992_0" Pin1InfoVect1LinkObjId="SW-211991_0" Pin1InfoVect2LinkObjId="g_2e80cf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-487 790,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e80a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-503 763,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="31692@1" ObjectIDND1="31691@x" ObjectIDZND0="g_2e7e4e0@0" ObjectIDZND1="31701@x" Pin0InfoVect0LinkObjId="g_2e7e4e0_0" Pin0InfoVect1LinkObjId="g_2e80cf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211992_1" Pin1InfoVect1LinkObjId="SW-211991_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="763,-503 763,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e80cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-487 763,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_2e7e4e0@0" ObjectIDND1="31692@x" ObjectIDND2="31691@x" ObjectIDZND0="31701@1" Pin0InfoVect0LinkObjId="g_2e82720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e7e4e0_0" Pin1InfoVect1LinkObjId="SW-211992_0" Pin1InfoVect2LinkObjId="SW-211991_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-487 763,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e82720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-371 762,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_2e81970@0" ObjectIDZND0="31701@x" ObjectIDZND1="g_2e80f50@0" Pin0InfoVect0LinkObjId="g_2e80cf0_0" Pin0InfoVect1LinkObjId="g_2e80f50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e81970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-371 762,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e83210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-389 762,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="31701@0" ObjectIDZND0="g_2e81970@0" ObjectIDZND1="g_2e80f50@0" Pin0InfoVect0LinkObjId="g_2e81970_0" Pin0InfoVect1LinkObjId="g_2e80f50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e80cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="762,-389 762,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e83470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-367 762,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2e81970@0" ObjectIDND1="31701@x" ObjectIDZND0="g_2e80f50@1" Pin0InfoVect0LinkObjId="g_2e80f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e81970_0" Pin1InfoVect1LinkObjId="g_2e80cf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-367 762,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e88e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-288 726,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2e80f50@0" ObjectIDND1="31687@x" ObjectIDZND0="g_2e836d0@0" Pin0InfoVect0LinkObjId="g_2e836d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e80f50_0" Pin1InfoVect1LinkObjId="SW-211988_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-288 726,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e89920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-301 762,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_2e80f50@0" ObjectIDZND0="g_2e836d0@0" ObjectIDZND1="31687@x" Pin0InfoVect0LinkObjId="g_2e836d0_0" Pin0InfoVect1LinkObjId="SW-211988_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e80f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="762,-301 762,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e89b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-288 762,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2e836d0@0" ObjectIDND1="g_2e80f50@0" ObjectIDZND0="31687@0" Pin0InfoVect0LinkObjId="SW-211988_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e836d0_0" Pin1InfoVect1LinkObjId="g_2e80f50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-288 762,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fba240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="505,-59 521,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2fb8c10@0" ObjectIDZND0="g_2fb99c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2fb99c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fb8c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="505,-59 521,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fbad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-59 521,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2fb8c10@0" ObjectIDND1="0@x" ObjectIDZND0="g_2fb99c0@0" Pin0InfoVect0LinkObjId="g_2fb99c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fb8c10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-59 521,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fbaf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-82 521,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2fb8c10@0" ObjectIDZND1="g_2fb99c0@0" Pin0InfoVect0LinkObjId="g_2fb8c10_0" Pin0InfoVect1LinkObjId="g_2fb99c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="521,-82 521,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fbb1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-5 521,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2fb99c0@1" ObjectIDZND0="g_2eb4210@0" Pin0InfoVect0LinkObjId="g_2eb4210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fb99c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-5 521,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,-58 682,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="31676@1" ObjectIDZND0="31677@x" ObjectIDZND1="g_2fbb450@0" ObjectIDZND2="g_2ffda90@0" Pin0InfoVect0LinkObjId="SW-211938_0" Pin0InfoVect1LinkObjId="g_2fbb450_0" Pin0InfoVect2LinkObjId="g_2ffda90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="682,-58 682,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc0c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="698,-44 682,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="31677@0" ObjectIDZND0="g_2fbb450@0" ObjectIDZND1="31676@x" ObjectIDZND2="g_2ffda90@0" Pin0InfoVect0LinkObjId="g_2fbb450_0" Pin0InfoVect1LinkObjId="SW-211937_0" Pin0InfoVect2LinkObjId="g_2ffda90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="698,-44 682,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc1760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-81 842,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2fc0ee0@0" Pin0InfoVect0LinkObjId="g_2fc0ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,-81 842,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc19c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-21 842,-21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2fbf1c0@0" ObjectIDZND0="g_2fc0ee0@0" ObjectIDZND1="g_2fc2710@0" Pin0InfoVect0LinkObjId="g_2fc0ee0_0" Pin0InfoVect1LinkObjId="g_2fc2710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fbf1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="826,-21 842,-21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc24b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-34 842,-21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2fc0ee0@1" ObjectIDZND0="g_2fbf1c0@0" ObjectIDZND1="g_2fc2710@0" Pin0InfoVect0LinkObjId="g_2fbf1c0_0" Pin0InfoVect1LinkObjId="g_2fc2710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc0ee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="842,-34 842,-21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc3130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-21 842,2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2fbf1c0@0" ObjectIDND1="g_2fc0ee0@0" ObjectIDZND0="g_2fc2710@1" Pin0InfoVect0LinkObjId="g_2fc2710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fbf1c0_0" Pin1InfoVect1LinkObjId="g_2fc0ee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,-21 842,2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc3390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,55 842,77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2fc2710@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc2710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,55 842,77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc5780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1298,-501 1330,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" EndDevType2="lightningRod" ObjectIDND0="31697@1" ObjectIDZND0="31696@x" ObjectIDZND1="0@0" ObjectIDZND2="g_2fc37f0@0" Pin0InfoVect0LinkObjId="SW-211997_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2fc37f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1298,-501 1330,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc6270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-519 1330,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" EndDevType2="lightningRod" ObjectIDND0="31696@0" ObjectIDZND0="31697@x" ObjectIDZND1="0@0" ObjectIDZND2="g_2fc37f0@0" Pin0InfoVect0LinkObjId="SW-211998_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2fc37f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-519 1330,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc7ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-342 1356,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_2fc6ef0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2fc64d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2fc64d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc6ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-342 1356,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc8790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1356,-315 1356,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2fc6ef0@0" ObjectIDZND1="g_2fc64d0@0" Pin0InfoVect0LinkObjId="g_2fc6ef0_0" Pin0InfoVect1LinkObjId="g_2fc64d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1356,-315 1356,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc89f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1356,-342 1356,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2fc6ef0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2fc64d0@0" Pin0InfoVect0LinkObjId="g_2fc64d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fc6ef0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1356,-342 1356,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fc9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1453,-342 1483,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_2fce430@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2fcda10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2fcda10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fce430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1453,-342 1483,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fca0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1483,-315 1483,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2fce430@0" ObjectIDZND1="g_2fcda10@0" Pin0InfoVect0LinkObjId="g_2fce430_0" Pin0InfoVect1LinkObjId="g_2fcda10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1483,-315 1483,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fca330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1483,-342 1483,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2fce430@0" ObjectIDND1="0@x" ObjectIDZND0="g_2fcda10@0" Pin0InfoVect0LinkObjId="g_2fcda10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fce430_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1483,-342 1483,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fcc040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1196,-343 1226,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_2fcf1e0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2fccff0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2fccff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fcf1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1196,-343 1226,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fcc2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,-315 1226,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2fcf1e0@0" ObjectIDZND1="g_2fccff0@0" Pin0InfoVect0LinkObjId="g_2fcf1e0_0" Pin0InfoVect1LinkObjId="g_2fccff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-315 1226,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fcc500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,-343 1226,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2fcf1e0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2fccff0@0" Pin0InfoVect0LinkObjId="g_2fccff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fcf1e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-343 1226,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fcff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-475 1330,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="busSection" ObjectIDND0="g_2fc37f0@0" ObjectIDND1="31697@x" ObjectIDND2="31696@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fc37f0_0" Pin1InfoVect1LinkObjId="SW-211998_0" Pin1InfoVect2LinkObjId="SW-211997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-475 1330,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fd0a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-475 1330,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2fc37f0@0" ObjectIDZND0="0@0" ObjectIDZND1="31697@x" ObjectIDZND2="31696@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-211998_0" Pin0InfoVect2LinkObjId="SW-211997_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc37f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-475 1330,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fd0ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-475 1330,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDND1="g_2fc37f0@0" ObjectIDZND0="31697@x" ObjectIDZND1="31696@x" Pin0InfoVect0LinkObjId="SW-211998_0" Pin0InfoVect1LinkObjId="SW-211997_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2fc37f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-475 1330,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd0f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1356,-420 1356,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2fc64d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc64d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1356,-420 1356,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd11a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1483,-420 1483,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2fcda10@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fcda10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1483,-420 1483,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd1400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,-421 1226,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2fccff0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fccff0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-421 1226,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd1660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1355,-235 1355,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1355,-235 1355,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd18c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1482,-235 1482,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1482,-235 1482,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd2e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1355,-159 1482,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="transformer2" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1355,-159 1482,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd30c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1482,-159 1482,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="transformer2" BeginDevType2="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1482,-159 1482,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd3320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1355,-159 1355,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="transformer2" BeginDevType2="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1355,-159 1355,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd3580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1225,-159 1225,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="transformer2" BeginDevType2="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-159 1225,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd4070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1225,-235 1225,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-235 1225,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd42d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1225,-159 1355,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" EndDevType1="transformer2" ObjectIDND0="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-159 1355,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd8b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-35 944,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2fd8090@0" ObjectIDZND0="31702@0" Pin0InfoVect0LinkObjId="SW-212001_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd8090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-35 944,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd8d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-35 1002,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="31702@1" ObjectIDZND0="31675@x" ObjectIDZND1="g_2fd4df0@0" ObjectIDZND2="g_2fd4710@0" Pin0InfoVect0LinkObjId="SW-211935_0" Pin0InfoVect1LinkObjId="g_2fd4df0_0" Pin0InfoVect2LinkObjId="g_2fd4710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-212001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="980,-35 1002,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd9a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,-58 1002,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="31675@1" ObjectIDZND0="31702@x" ObjectIDZND1="g_2fd4df0@0" ObjectIDZND2="g_2fd4710@0" Pin0InfoVect0LinkObjId="SW-212001_0" Pin0InfoVect1LinkObjId="g_2fd4df0_0" Pin0InfoVect2LinkObjId="g_2fd4710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211935_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1002,-58 1002,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd9cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,-35 1024,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="31702@x" ObjectIDND1="31675@x" ObjectIDND2="g_2fd4710@0" ObjectIDZND0="g_2fd4df0@0" Pin0InfoVect0LinkObjId="g_2fd4df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-212001_0" Pin1InfoVect1LinkObjId="SW-211935_0" Pin1InfoVect2LinkObjId="g_2fd4710_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1002,-35 1024,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,-35 1002,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="31702@x" ObjectIDND1="31675@x" ObjectIDND2="g_2fd4df0@0" ObjectIDZND0="g_2fd4710@0" Pin0InfoVect0LinkObjId="g_2fd4710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-212001_0" Pin1InfoVect1LinkObjId="SW-211935_0" Pin1InfoVect2LinkObjId="g_2fd4df0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1002,-35 1002,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fda1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,17 1002,71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2fd4710@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd4710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1002,17 1002,71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fda410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-720 1034,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31669@0" ObjectIDZND0="31684@0" Pin0InfoVect0LinkObjId="g_2fda670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-720 1034,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fda670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-686 763,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31689@1" ObjectIDZND0="31684@0" Pin0InfoVect0LinkObjId="g_2fda410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-686 763,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fda8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-686 1330,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31694@1" ObjectIDZND0="31684@0" Pin0InfoVect0LinkObjId="g_2fda410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-686 1330,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fdab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-151 521,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31685@0" Pin0InfoVect0LinkObjId="g_2fdad90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-151 521,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fdad90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,-150 682,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="31676@0" ObjectIDZND0="31685@0" Pin0InfoVect0LinkObjId="g_2fdab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="682,-150 682,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fdaff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-150 842,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31685@0" Pin0InfoVect0LinkObjId="g_2fdab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,-150 842,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fdb250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,-150 1002,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="31675@0" ObjectIDZND0="31685@0" Pin0InfoVect0LinkObjId="g_2fdab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211935_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1002,-150 1002,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fdb4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-181 762,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="31687@1" ObjectIDZND0="31685@0" Pin0InfoVect0LinkObjId="g_2fdab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211988_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-181 762,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe05d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="604,-429 604,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="31686@1" ObjectIDZND0="g_2fdb710@0" ObjectIDZND1="g_2fdf690@0" ObjectIDZND2="31701@x" Pin0InfoVect0LinkObjId="g_2fdb710_0" Pin0InfoVect1LinkObjId="g_2fdf690_0" Pin0InfoVect2LinkObjId="g_2e80cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-212014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="604,-429 604,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe10c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="604,-451 579,-451 579,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="31686@x" ObjectIDND1="g_2fdf690@0" ObjectIDND2="31701@x" ObjectIDZND0="g_2fdb710@0" Pin0InfoVect0LinkObjId="g_2fdb710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-212014_0" Pin1InfoVect1LinkObjId="g_2fdf690_0" Pin1InfoVect2LinkObjId="g_2e80cf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="604,-451 579,-451 579,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe1320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-429 653,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2fdf690@0" ObjectIDZND0="31701@x" ObjectIDZND1="31686@x" ObjectIDZND2="g_2fdb710@0" Pin0InfoVect0LinkObjId="g_2e80cf0_0" Pin0InfoVect1LinkObjId="SW-212014_0" Pin0InfoVect2LinkObjId="g_2fdb710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fdf690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="653,-429 653,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe6440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-282 1105,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="31700@1" ObjectIDZND0="g_2fe24c0@0" ObjectIDZND1="g_2fe1580@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2fe24c0_0" Pin0InfoVect1LinkObjId="g_2fe1580_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-212000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-282 1105,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe6f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-296 1076,-296 1076,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="31700@x" ObjectIDND1="g_2fe1580@0" ObjectIDND2="0@x" ObjectIDZND0="g_2fe24c0@0" Pin0InfoVect0LinkObjId="g_2fe24c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-212000_0" Pin1InfoVect1LinkObjId="g_2fe1580_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-296 1076,-296 1076,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe7190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-281 1150,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2fe1580@0" ObjectIDZND0="0@x" ObjectIDZND1="31700@x" ObjectIDZND2="g_2fe24c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-212000_0" Pin0InfoVect2LinkObjId="g_2fe24c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fe1580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-281 1150,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe7c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1483,-296 1150,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_2fe1580@0" ObjectIDZND1="31700@x" ObjectIDZND2="g_2fe24c0@0" Pin0InfoVect0LinkObjId="g_2fe1580_0" Pin0InfoVect1LinkObjId="SW-212000_0" Pin0InfoVect2LinkObjId="g_2fe24c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1483,-296 1150,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fe7ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-296 1105,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2fe1580@0" ObjectIDND1="0@x" ObjectIDZND0="31700@x" ObjectIDZND1="g_2fe24c0@0" Pin0InfoVect0LinkObjId="SW-212000_0" Pin0InfoVect1LinkObjId="g_2fe24c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fe1580_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-296 1105,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff08c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="604,-385 604,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2fdec40@0" ObjectIDZND0="31686@0" Pin0InfoVect0LinkObjId="SW-212014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fdec40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="604,-385 604,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff0b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-237 1105,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2fe59f0@0" ObjectIDZND0="31700@0" Pin0InfoVect0LinkObjId="SW-212000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fe59f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-237 1105,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff0d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-732 690,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="31682@0" ObjectIDZND0="31680@x" ObjectIDZND1="31684@0" Pin0InfoVect0LinkObjId="SW-211942_0" Pin0InfoVect1LinkObjId="g_2fda410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211944_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-732 690,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff1870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="690,-755 690,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="31680@0" ObjectIDZND0="31682@x" ObjectIDZND1="31684@0" Pin0InfoVect0LinkObjId="SW-211944_0" Pin0InfoVect1LinkObjId="g_2fda410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211942_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="690,-755 690,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff1ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="690,-732 690,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="31682@x" ObjectIDND1="31680@x" ObjectIDZND0="31684@0" Pin0InfoVect0LinkObjId="g_2fda410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211944_0" Pin1InfoVect1LinkObjId="SW-211942_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="690,-732 690,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff5440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-732 764,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31682@1" ObjectIDZND0="g_2fef3a0@0" Pin0InfoVect0LinkObjId="g_2fef3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211944_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-732 764,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff56a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-809 752,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2fefe30@0" ObjectIDZND0="31681@1" Pin0InfoVect0LinkObjId="SW-211943_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fefe30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-809 752,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff5900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-809 690,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="31681@0" ObjectIDZND0="31680@x" ObjectIDZND1="g_2ff1d30@0" ObjectIDZND2="g_2ff6650@0" Pin0InfoVect0LinkObjId="SW-211942_0" Pin0InfoVect1LinkObjId="g_2ff1d30_0" Pin0InfoVect2LinkObjId="g_2ff6650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211943_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="716,-809 690,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff63f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="690,-791 690,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="31680@1" ObjectIDZND0="31681@x" ObjectIDZND1="g_2ff1d30@0" ObjectIDZND2="g_2ff6650@0" Pin0InfoVect0LinkObjId="SW-211943_0" Pin0InfoVect1LinkObjId="g_2ff1d30_0" Pin0InfoVect2LinkObjId="g_2ff6650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211942_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="690,-791 690,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff7400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="679,-865 690,-865 690,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ff1d30@0" ObjectIDZND0="g_2ff6650@0" ObjectIDZND1="31681@x" ObjectIDZND2="31680@x" Pin0InfoVect0LinkObjId="g_2ff6650_0" Pin0InfoVect1LinkObjId="SW-211943_0" Pin0InfoVect2LinkObjId="SW-211942_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ff1d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="679,-865 690,-865 690,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff7ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-836 690,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ff6650@0" ObjectIDZND0="g_2ff1d30@0" ObjectIDZND1="31681@x" ObjectIDZND2="31680@x" Pin0InfoVect0LinkObjId="g_2ff1d30_0" Pin0InfoVect1LinkObjId="SW-211943_0" Pin0InfoVect2LinkObjId="SW-211942_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ff6650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="669,-836 690,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ff8150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="690,-836 690,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2ff1d30@0" ObjectIDND1="g_2ff6650@0" ObjectIDZND0="31681@x" ObjectIDZND1="31680@x" Pin0InfoVect0LinkObjId="SW-211943_0" Pin0InfoVect1LinkObjId="SW-211942_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ff1d30_0" Pin1InfoVect1LinkObjId="g_2ff6650_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="690,-836 690,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffe4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,-39 682,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="31677@x" ObjectIDND1="g_2fbb450@0" ObjectIDND2="31676@x" ObjectIDZND0="g_2ffda90@1" Pin0InfoVect0LinkObjId="g_2ffda90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-211938_0" Pin1InfoVect1LinkObjId="g_2fbb450_0" Pin1InfoVect2LinkObjId="SW-211937_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="682,-39 682,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffe710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="683,-44 663,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="31677@x" ObjectIDND1="31676@x" ObjectIDND2="g_2ffda90@0" ObjectIDZND0="g_2fbb450@0" Pin0InfoVect0LinkObjId="g_2fbb450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-211938_0" Pin1InfoVect1LinkObjId="SW-211937_0" Pin1InfoVect2LinkObjId="g_2ffda90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="683,-44 663,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffe970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="745,-44 734,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2fbe730@0" ObjectIDZND0="31677@1" Pin0InfoVect0LinkObjId="SW-211938_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fbe730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="745,-44 734,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,22 682,34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ffda90@0" ObjectIDZND0="31678@1" Pin0InfoVect0LinkObjId="SW-211939_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ffda90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="682,22 682,34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ea3ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-451 653,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="31701@x" ObjectIDZND0="g_2fdf690@0" ObjectIDZND1="31686@x" ObjectIDZND2="g_2fdb710@0" Pin0InfoVect0LinkObjId="g_2fdf690_0" Pin0InfoVect1LinkObjId="SW-212014_0" Pin0InfoVect2LinkObjId="g_2fdb710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e80cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="762,-451 653,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ea40d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="604,-451 653,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="31686@x" ObjectIDND1="g_2fdb710@0" ObjectIDZND0="g_2fdf690@0" ObjectIDZND1="31701@x" Pin0InfoVect0LinkObjId="g_2fdf690_0" Pin0InfoVect1LinkObjId="g_2e80cf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-212014_0" Pin1InfoVect1LinkObjId="g_2fdb710_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="604,-451 653,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eba740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,193 682,201 722,201 722,105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="31679@1" Pin0InfoVect0LinkObjId="SW-211940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="682,193 682,201 722,201 722,105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eba930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,77 722,77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="31678@x" ObjectIDND1="0@x" ObjectIDZND0="31679@0" Pin0InfoVect0LinkObjId="SW-211940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211939_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="682,77 722,77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebb2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,70 682,77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="31678@0" ObjectIDZND0="0@x" ObjectIDZND1="31679@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-211940_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-211939_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="682,70 682,77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebb4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,77 682,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="31678@x" ObjectIDND1="31679@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-211939_0" Pin1InfoVect1LinkObjId="SW-211940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="682,77 682,86 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YT"/>
</svg>