<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-115" aopId="2098430" id="thSvg" viewBox="3117 -1207 2321 1266">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <polyline arcFlag="1" fill="none" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="100" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <polyline arcFlag="1" fill="none" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape173">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="29" x2="29" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99998" x1="46" x2="46" y1="59" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.49157" x1="15" x2="15" y1="14" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99998" x1="46" x2="36" y1="59" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="2" x2="46" y1="15" y2="59"/>
    <polyline fill="none" points="29,14 23,17 21,18 21,19 21,19 23,21 26,22 30,24 31,25 31,25 31,26 30,27 26,28 23,30 22,30 22,31 22,32 23,33 26,34 30,36 31,36 31,37 31,38 30,38 26,40 23,41 22,42 22,43 22,44 23,44 26,46 30,47 31,48 31,49 31,49 30,50 26,52 23,53 21,55 21,55 21,56 23,57 29,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="24" x2="33" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="20" x2="36" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="26" x2="30" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="29" x2="29" y1="59" y2="66"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape70">
    <circle cx="43" cy="47" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="29" x2="32" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="27" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="31" x2="31" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="37" x2="25" y1="8" y2="8"/>
    <polyline arcFlag="1" fill="none" points="57,22 58,22 58,22 58,22 59,23 59,23 60,24 60,24 60,25 61,25 61,26 61,27 61,27 61,28 61,28 61,29 61,30 60,30 60,31 60,31 59,32 59,32 58,33 58,33 58,33 57,33 " stroke-width="0.0290179"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="98" y2="113"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="45" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429293" x1="56" x2="56" y1="22" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429293" x1="57" x2="57" y1="48" y2="43"/>
    <polyline arcFlag="1" fill="none" points="57,32 58,32 58,32 58,32 59,33 59,33 60,34 60,34 60,35 61,35 61,36 61,37 61,37 61,38 61,38 61,39 61,40 60,40 60,41 60,41 59,42 59,42 58,43 58,43 58,43 57,43 " stroke-width="0.0290179"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="98" y2="57"/>
    <rect height="29" stroke-width="0.416667" width="14" x="28" y="62"/>
    <circle cx="43" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="35" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="97" y2="54"/>
    <rect height="29" stroke-width="0.416667" width="14" x="0" y="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="55" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="51" x2="57" y1="48" y2="48"/>
    <circle cx="34" cy="47" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="34" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape172">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="40" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="17" x2="33" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="40" x2="40" y1="44" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="33" x2="33" y1="44" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="8" x2="25" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="25" y1="56" y2="56"/>
    <circle cx="16" cy="18" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="16" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="48" y2="28"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape24_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="transformer:shape5_0">
    <circle cx="38" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="22" y2="29"/>
   </symbol>
   <symbol id="transformer:shape5_1">
    <circle cx="38" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape5-2">
    <circle cx="68" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="54" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="54"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="14" x2="11" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="41" x2="48" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="35" x2="53" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="42" x2="46" y1="38" y2="38"/>
    <polyline fill="none" points="20,13 45,13 45,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="7" x2="11" y1="15" y2="13"/>
    <circle cx="10" cy="28" r="9.5" stroke-width="1.0673"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="14" x2="11" y1="31" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07262" x1="11" x2="11" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="7" x2="11" y1="31" y2="28"/>
    <ellipse cx="10" cy="13" rx="9.5" ry="10" stroke-width="1.0673"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07262" x1="11" x2="11" y1="13" y2="9"/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29fd120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29fe110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29feb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29ff800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a00a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a01680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a020e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a02df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a03690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a03f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a04c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a05510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a05e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a069b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a072a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a07b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a09300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2249420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a026b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a0b850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a0ca00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a0d380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21de640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a13460" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a140d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a0ff30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a11370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a1f760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a0f660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1276" width="2331" x="3112" y="-1212"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4432" x2="4448" y1="-127" y2="-127"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4432" x2="4448" y1="-99" y2="-99"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-68397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5183.000000 -867.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14533" ObjectName="SW-CX_SB.CX_SB_361BK"/>
     <cge:Meas_Ref ObjectId="68397"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5420.000000 -934.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5183.000000 -741.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14528" ObjectName="SW-CX_SB.CX_SB_363BK"/>
     <cge:Meas_Ref ObjectId="68422"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5186.000000 -590.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14529" ObjectName="SW-CX_SB.CX_SB_365BK"/>
     <cge:Meas_Ref ObjectId="68445"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5186.000000 -269.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14530" ObjectName="SW-CX_SB.CX_SB_362BK"/>
     <cge:Meas_Ref ObjectId="68468"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70977">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -948.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15959" ObjectName="SW-CX_SB.CX_SB_156BK"/>
     <cge:Meas_Ref ObjectId="70977"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70904">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -943.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15955" ObjectName="SW-CX_SB.CX_SB_154BK"/>
     <cge:Meas_Ref ObjectId="70904"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69725">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -819.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15608" ObjectName="SW-CX_SB.CX_SB_101BK"/>
     <cge:Meas_Ref ObjectId="69725"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4054.442247 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15614" ObjectName="SW-CX_SB.CX_SB_001BK"/>
     <cge:Meas_Ref ObjectId="69765"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.568116 -270.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15963" ObjectName="SW-CX_SB.CX_SB_012BK"/>
     <cge:Meas_Ref ObjectId="70734"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4609.563986 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11963" ObjectName="SW-CX_SB.CX_SB_002BK"/>
     <cge:Meas_Ref ObjectId="63162"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.000000 -514.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15613" ObjectName="SW-CX_SB.CX_SB_301BK"/>
     <cge:Meas_Ref ObjectId="69759"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68491">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5186.000000 -157.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14531" ObjectName="SW-CX_SB.CX_SB_364BK"/>
     <cge:Meas_Ref ObjectId="68491"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63431">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.000000 -815.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11974" ObjectName="SW-CX_SB.CX_SB_102BK"/>
     <cge:Meas_Ref ObjectId="63431"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -289.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11976" ObjectName="SW-CX_SB.CX_SB_302BK"/>
     <cge:Meas_Ref ObjectId="63455"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3500.000000 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14522" ObjectName="SW-CX_SB.CX_SB_061BK"/>
     <cge:Meas_Ref ObjectId="68269"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14523" ObjectName="SW-CX_SB.CX_SB_062BK"/>
     <cge:Meas_Ref ObjectId="68292"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3715.000000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14524" ObjectName="SW-CX_SB.CX_SB_063BK"/>
     <cge:Meas_Ref ObjectId="68315"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68338">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -236.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14525" ObjectName="SW-CX_SB.CX_SB_064BK"/>
     <cge:Meas_Ref ObjectId="68338"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14526" ObjectName="SW-CX_SB.CX_SB_065BK"/>
     <cge:Meas_Ref ObjectId="68359"/>
    <cge:TPSR_Ref TObjectID="14526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63884">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -236.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12008" ObjectName="SW-CX_SB.CX_SB_071BK"/>
     <cge:Meas_Ref ObjectId="63884"/>
    <cge:TPSR_Ref TObjectID="12008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 -237.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14527" ObjectName="SW-CX_SB.CX_SB_066BK"/>
     <cge:Meas_Ref ObjectId="68377"/>
    <cge:TPSR_Ref TObjectID="14527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -236.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11985" ObjectName="SW-CX_SB.CX_SB_072BK"/>
     <cge:Meas_Ref ObjectId="63464"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5142.000000 -405.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15948" ObjectName="SW-CX_SB.CX_SB_312BK"/>
     <cge:Meas_Ref ObjectId="70722"/>
    <cge:TPSR_Ref TObjectID="15948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106116">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20692" ObjectName="SW-CX_SB.CX_SB_073BK"/>
     <cge:Meas_Ref ObjectId="106116"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20697" ObjectName="SW-CX_SB.CX_SB_074BK"/>
     <cge:Meas_Ref ObjectId="106123"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SB.CX_SB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="7035"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -674.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="7037"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -674.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="7039"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -674.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4843" ObjectName="TF-CX_SB.CX_SB_1T"/>
    <cge:TPSR_Ref TObjectID="4843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SB.CX_SB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16992"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.121739 -673.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="16994"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.121739 -673.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="16996"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.121739 -673.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12047" ObjectName="TF-CX_SB.CX_SB_2T"/>
    <cge:TPSR_Ref TObjectID="12047"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-1140 5061,-454 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12192" ObjectName="BS-CX_SB.CX_SB_3IM"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   <polyline fill="none" opacity="0" points="5061,-1140 5061,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-398 5061,-4 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12193" ObjectName="BS-CX_SB.CX_SB_3IIM"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   <polyline fill="none" opacity="0" points="5061,-398 5061,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-351 3452,-351 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11961" ObjectName="BS-CX_SB.CX_SB_9IM"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   <polyline fill="none" opacity="0" points="4286,-351 3452,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-351 4334,-351 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11962" ObjectName="BS-CX_SB.CX_SB_9IIM"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   <polyline fill="none" opacity="0" points="4859,-351 4334,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-904 4665,-904 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12191" ObjectName="BS-CX_SB.CX_SB_1IM"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   <polyline fill="none" opacity="0" points="3802,-904 4665,-904 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_SB.CX_SB_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3913.000000 -19.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28611" ObjectName="CB-CX_SB.CX_SB_Cb1"/>
    <cge:TPSR_Ref TObjectID="28611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_SB.CX_SB_Cb3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -15.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28612" ObjectName="CB-CX_SB.CX_SB_Cb3"/>
    <cge:TPSR_Ref TObjectID="28612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_SB.CX_SB_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4407.852174 28.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12112" ObjectName="CB-CX_SB.CX_SB_2C"/>
    <cge:TPSR_Ref TObjectID="12112"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5377.000000 -911.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5377.000000 -911.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 -68.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 -68.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_201d100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -528.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20aa4d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4179.000000 -528.000000)" xlink:href="#lightningRod:shape173"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220ac10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -528.000000)" xlink:href="#lightningRod:shape173"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d2e40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -528.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222c4f0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5282.000000 -1085.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_224a100">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5284.000000 -1033.000000)" xlink:href="#lightningRod:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2268a70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5207.500000 -821.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2269240">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5275.500000 -899.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223f170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5207.500000 -711.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ed2e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5203.500000 -560.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ede80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5204.500000 -239.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21efe40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5210.500000 -127.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22126e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -1074.000000)" xlink:href="#lightningRod:shape172"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22133c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -1059.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2214300">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -1075.000000)" xlink:href="#lightningRod:shape172"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2215540">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -1060.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2244930">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 -655.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22454e0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3909.500000 -718.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c7460">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.000000 -654.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c8010">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4472.500000 -717.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e15c0">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3539.442247 -127.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2386200">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3645.442247 -127.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2389e60">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3754.442247 -126.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_238dac0">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3860.442247 -128.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2390350">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3716.000000 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2390ce0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3822.000000 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23918d0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3607.000000 -191.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23924f0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3501.000000 -191.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2530d40">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3929.000000 -197.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_253a350">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 4056.000000 -193.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2544480">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3568.442247 -436.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254b170">
    <use class="BV-10KV" transform="matrix(0.666667 -0.000000 0.000000 -0.642857 3743.000000 -422.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254e200">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.880000 4183.000000 -189.440000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2550cf0">
    <use class="BV-10KV" transform="matrix(0.666667 -0.000000 0.000000 -0.642857 4185.000000 -247.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25695c0">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4587.166885 -127.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256b3f0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 4548.724638 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2571370">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 4423.852174 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25881c0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4440.900218 -430.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258cbe0">
    <use class="BV-110KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4565.000000 -766.099782)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258d990">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4606.000000 -691.099782)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258e740">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4561.900218 -635.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_259ac30">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5283.000000 -83.000000)" xlink:href="#lightningRod:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a9010">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4103.442247 -1063.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d1020">
    <use class="BV-10KV" transform="matrix(0.833333 -0.000000 0.000000 -0.809524 4469.000000 -433.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_274f790">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2751d40">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.816327 4670.000000 -159.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2752e20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2762b80">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.816327 4796.000000 -159.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2763c60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27649d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.000000 -144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3225.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-69501" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4105.000000 -697.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69501" ObjectName="CX_SB:CX_SB_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-63855" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4704.000000 -757.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63855" ObjectName="CX_SB:CX_SB_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62632" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3270.538462 -1000.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62632" ObjectName="CX_SB:CX_SB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79740" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3270.538462 -958.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79740" ObjectName="CX_SB:CX_SB_sumQ"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="4429" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="4525" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="3954" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4557" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4432" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4600" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4476" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4370" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="3963" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3724" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3509" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4191" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3615" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3830" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4064" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3604" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3749" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4247" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4044" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4678" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4804" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5061" cy="-1012" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5061" cy="-62" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5061" cy="-1077" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5061" cy="-299" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5061" cy="-524" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5061" cy="-148" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5061" cy="-259" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5061" cy="-491" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5061" cy="-363" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5061" cy="-731" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5061" cy="-858" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5061" cy="-580" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3937" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-62776" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3427.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11953" ObjectName="DYN-CX_SBEQ"/>
     <cge:Meas_Ref ObjectId="62776"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21d3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -1041.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21d3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -1041.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21d3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -1041.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21d3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -1041.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21d3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -1041.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21d3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -1041.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21d3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -1041.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,17)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,164)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,374)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,395)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,416)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_226fa20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3143.000000 -589.000000) translate(0,437)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd6f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5385.000000 -948.000000) translate(0,12)">K02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22eab90" transform="matrix(1.000000 0.000000 0.000000 1.000000 5218.000000 -757.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22eb0a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5222.000000 -883.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22eb400" transform="matrix(1.000000 0.000000 0.000000 1.000000 5219.000000 -606.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22eb760" transform="matrix(1.000000 0.000000 0.000000 1.000000 5217.000000 -284.000000) translate(0,12)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ebac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5288.000000 -1085.000000) translate(0,15)">I段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226b0e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5293.000000 -1012.000000) translate(0,15)">I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226b4e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5334.000000 -878.000000) translate(0,15)">爱尼山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226c460" transform="matrix(1.000000 0.000000 0.000000 1.000000 5272.000000 -961.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226cb30" transform="matrix(1.000000 0.000000 0.000000 1.000000 5370.000000 -758.000000) translate(0,15)">雨龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226cef0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5334.000000 -603.000000) translate(0,15)">双小妥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226d250" transform="matrix(1.000000 0.000000 0.000000 1.000000 5334.000000 -140.000000) translate(0,15)">双妥大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226d440" transform="matrix(1.000000 0.000000 0.000000 1.000000 5334.000000 -281.000000) translate(0,15)">鱼庄河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_226dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3277.000000 -1167.500000) translate(0,16)">双柏变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226dfc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5348.000000 -656.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226e3d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5096.000000 -1038.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226e680" transform="matrix(1.000000 0.000000 0.000000 1.000000 5233.000000 -171.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22124d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5346.000000 -338.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238e7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -137.000000) translate(0,15)">瓦</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238e7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -137.000000) translate(0,33)">波</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238e7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -137.000000) translate(0,51)">里</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238e7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -137.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238ef10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -137.000000) translate(0,15)">花</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238ef10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -137.000000) translate(0,33)">箐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238ef10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3592.000000 -137.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f410" transform="matrix(1.000000 0.000000 0.000000 1.000000 3807.000000 -137.000000) translate(0,15)">朝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f410" transform="matrix(1.000000 0.000000 0.000000 1.000000 3807.000000 -137.000000) translate(0,33)">阳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f410" transform="matrix(1.000000 0.000000 0.000000 1.000000 3807.000000 -137.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -137.000000) translate(0,15)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -137.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -137.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -137.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_238f9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -137.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2543490" transform="matrix(1.000000 0.000000 0.000000 1.000000 3550.500000 -518.000000) translate(0,15)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2549720" transform="matrix(1.000000 0.000000 0.000000 1.000000 3701.500000 -518.000000) translate(0,15)">10kVI段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256a370" transform="matrix(1.000000 0.000000 0.000000 1.000000 4535.000000 -150.000000) translate(0,15)">玉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256a370" transform="matrix(1.000000 0.000000 0.000000 1.000000 4535.000000 -150.000000) translate(0,33)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256a370" transform="matrix(1.000000 0.000000 0.000000 1.000000 4535.000000 -150.000000) translate(0,51)">柏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256a370" transform="matrix(1.000000 0.000000 0.000000 1.000000 4535.000000 -150.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2587810" transform="matrix(1.000000 0.000000 0.000000 1.000000 4384.500000 -524.000000) translate(0,15)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_258f4f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4397.500000 -1203.000000) translate(0,15)">双鄂大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_259f7f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5292.000000 -62.000000) translate(0,15)">Ⅱ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259fe20" transform="matrix(1.000000 0.000000 0.000000 1.000000 5095.000000 -88.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,33)">SFSZ8-25000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,51)">110±8×1.25%38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,69)">25/25/25MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,105)">Uk1-2%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,123)">Uk1-3%=18.1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a0060" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.500000 -868.000000) translate(0,141)">Uk2-3%=6.86</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,15)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,33)">SFSZ11-40000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,51)">110±8×1.25%38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,69)">40/40/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,105)">Uk1-2%=10.37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,123)">Uk1-3%=18.51</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a5bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.500000 -878.000000) translate(0,141)">Uk2-3%=6.65</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a89d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4105.500000 -1121.000000) translate(0,15)">110kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ac950" transform="matrix(1.000000 0.000000 0.000000 1.000000 3451.000000 -372.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ae0b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 -370.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ae890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -392.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25aead0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4452.000000 -249.000000) translate(0,12)">07127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25af110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4439.000000 -313.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25af350" transform="matrix(1.000000 0.000000 0.000000 1.000000 4439.000000 -173.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25af590" transform="matrix(1.000000 0.000000 0.000000 1.000000 4564.000000 -312.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25af7d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4576.000000 -249.000000) translate(0,12)">07227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25afa10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4608.000000 -390.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25afc50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -487.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25afe90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4383.000000 -714.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b00d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4476.000000 -798.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0310" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -325.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0550" transform="matrix(1.000000 0.000000 0.000000 1.000000 4916.000000 -325.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0790" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -323.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b09d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3622.000000 -172.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0c10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -173.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b0e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3837.000000 -171.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b1090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4564.000000 -176.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b12d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -137.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b12d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -137.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b12d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -137.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b12d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -137.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b12d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -137.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b12d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -137.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.000000 -137.000000) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.000000 -137.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.000000 -137.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.000000 -137.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.000000 -137.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b1f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.000000 -137.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b2460" transform="matrix(1.000000 0.000000 0.000000 1.000000 3966.000000 -161.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b26c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4381.000000 -137.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b26c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4381.000000 -137.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b26c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4381.000000 -137.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b26c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4381.000000 -137.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b26c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4381.000000 -137.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b26c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4381.000000 -137.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b28e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4093.000000 -157.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b2b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4104.000000 -985.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b2d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4117.000000 -1038.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b2fc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4118.000000 -957.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b3200" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -1058.000000) translate(0,12)">15667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b3440" transform="matrix(1.000000 0.000000 0.000000 1.000000 3912.000000 -798.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b3680" transform="matrix(1.000000 0.000000 0.000000 1.000000 4917.000000 -550.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b38c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5015.000000 -550.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b3b00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4155.000000 -51.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b3d40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3516.000000 -315.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b3f80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3529.000000 -248.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b41c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3622.000000 -314.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b4400" transform="matrix(1.000000 0.000000 0.000000 1.000000 3635.000000 -248.000000) translate(0,12)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b4640" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -314.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b4880" transform="matrix(1.000000 0.000000 0.000000 1.000000 3744.000000 -247.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b4ac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3837.000000 -313.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b4d00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3850.000000 -249.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b4f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3944.000000 -318.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5180" transform="matrix(1.000000 0.000000 0.000000 1.000000 3957.000000 -254.000000) translate(0,12)">06517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b53c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3944.000000 -178.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5600" transform="matrix(1.000000 0.000000 0.000000 1.000000 4071.000000 -314.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5840" transform="matrix(1.000000 0.000000 0.000000 1.000000 4084.000000 -250.000000) translate(0,12)">06617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5a80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4198.000000 -313.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5cc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4255.000000 -322.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5f00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -390.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b6140" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -487.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b6380" transform="matrix(1.000000 0.000000 0.000000 1.000000 4462.000000 -156.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b65c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3611.000000 -398.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b6800" transform="matrix(1.000000 0.000000 0.000000 1.000000 3756.000000 -399.000000) translate(0,12)">0801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b6cc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3822.000000 -714.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b8d30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4055.000000 -717.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b97b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4654.000000 -778.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b9c70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -626.000000) translate(0,12)">3000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b9eb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4046.000000 -568.000000) translate(0,12)">    35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b9eb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4046.000000 -568.000000) translate(0,27)">1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bacc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4386.000000 -568.000000) translate(0,12)">    35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bacc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4386.000000 -568.000000) translate(0,27)">2号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bb110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4055.000000 -697.000000) translate(0,12)">温度1：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bb8d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4655.000000 -758.000000) translate(0,12)">温度1：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25be5f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3516.000000 -165.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bec20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4068.000000 -629.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bee60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4408.000000 -630.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25bf0a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4021.000000 -750.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25bf2e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4584.000000 -750.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c2ff0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3736.000000 -928.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c3240" transform="matrix(1.000000 0.000000 0.000000 1.000000 5067.000000 -1140.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c3480" transform="matrix(1.000000 0.000000 0.000000 1.000000 5070.000000 -25.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cdb40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4071.000000 -172.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cfeb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5097.000000 -1101.000000) translate(0,12)">3903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d04e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5096.000000 -757.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d0720" transform="matrix(1.000000 0.000000 0.000000 1.000000 5097.000000 -606.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d0960" transform="matrix(1.000000 0.000000 0.000000 1.000000 5095.000000 -516.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d0ba0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5092.000000 -389.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d0de0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5096.000000 -282.000000) translate(0,12)">3622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d1740" transform="matrix(1.000000 0.000000 0.000000 1.000000 3920.500000 -1207.000000) translate(0,15)">谢烟双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25de280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5092.000000 -882.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2704aa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4375.000000 -1062.000000) translate(0,12)">15467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2705660" transform="matrix(1.000000 0.000000 0.000000 1.000000 3850.000000 -1099.000000) translate(0,12)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27058a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4318.000000 -1101.000000) translate(0,12)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27065e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5211.000000 -433.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2720000" transform="matrix(1.000000 0.000000 0.000000 1.000000 3624.000000 -460.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2735ae0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4655.000000 -130.000000) translate(0,15)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2735ae0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4655.000000 -130.000000) translate(0,33)">平</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2735ae0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4655.000000 -130.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2756ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4781.000000 -130.000000) translate(0,15)">环</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2756ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4781.000000 -130.000000) translate(0,33)">东</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2756ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4781.000000 -130.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27676c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4685.000000 -144.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2767bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4685.000000 -243.000000) translate(0,12)">0733</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2767df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4685.000000 -326.000000) translate(0,12)">0732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2768030" transform="matrix(1.000000 0.000000 0.000000 1.000000 4693.000000 -224.000000) translate(0,12)">07337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2768270" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -144.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27684b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -242.000000) translate(0,12)">0743</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27686f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4820.000000 -224.000000) translate(0,12)">07437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2768930" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -326.000000) translate(0,12)">0742</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_276d440" transform="matrix(1.000000 0.000000 0.000000 1.000000 3254.500000 -169.000000) translate(0,15)">4937</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2773330" transform="matrix(1.000000 0.000000 0.000000 1.000000 5099.000000 -171.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27848f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5350.000000 -226.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2789fa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3518.000000 -264.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278a490" transform="matrix(1.000000 0.000000 0.000000 1.000000 3624.000000 -264.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278a6d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3733.000000 -263.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278a910" transform="matrix(1.000000 0.000000 0.000000 1.000000 3839.000000 -265.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278ab50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3946.000000 -270.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278ad90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4073.000000 -266.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278afd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4441.000000 -265.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278b210" transform="matrix(1.000000 0.000000 0.000000 1.000000 4566.000000 -265.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278b450" transform="matrix(1.000000 0.000000 0.000000 1.000000 4687.000000 -285.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278b690" transform="matrix(1.000000 0.000000 0.000000 1.000000 4813.000000 -285.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278b8d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5149.000000 -882.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278bb10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -756.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278bd50" transform="matrix(1.000000 0.000000 0.000000 1.000000 5160.000000 -434.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278bf90" transform="matrix(1.000000 0.000000 0.000000 1.000000 5152.000000 -605.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278c1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5151.000000 -284.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278c410" transform="matrix(1.000000 0.000000 0.000000 1.000000 5152.000000 -172.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -548.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278c890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4969.000000 -323.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278cad0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4055.000000 -438.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278cd10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4610.000000 -438.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278cf50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3972.000000 -848.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278d190" transform="matrix(1.000000 0.000000 0.000000 1.000000 4534.000000 -844.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278d3d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3963.000000 -977.000000) translate(0,12)">156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278d610" transform="matrix(1.000000 0.000000 0.000000 1.000000 4438.000000 -972.000000) translate(0,12)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278f1b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4299.000000 -304.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27918a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -452.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2791e40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3142.000000 -587.000000) translate(0,17)">公用信号</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20a81a0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4157.560660 -563.353553)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2545880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -454.000000)" xlink:href="#voltageTransformer:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2549d60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -455.000000)" xlink:href="#voltageTransformer:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2589710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.457971 -471.000000)" xlink:href="#voltageTransformer:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258fe90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5326.000000 -821.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2596680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5360.000000 -618.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_259a1c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5362.000000 -297.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25aaef0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -1044.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2787c00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5366.000000 -185.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD061">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3500.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16028" ObjectName="EC-CX_SB.CX_SB_LD061"/>
    <cge:TPSR_Ref TObjectID="16028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD062">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16021" ObjectName="EC-CX_SB.CX_SB_LD062"/>
    <cge:TPSR_Ref TObjectID="16021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD063">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3715.000000 -36.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16022" ObjectName="EC-CX_SB.CX_SB_LD063"/>
    <cge:TPSR_Ref TObjectID="16022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD064">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16023" ObjectName="EC-CX_SB.CX_SB_LD064"/>
    <cge:TPSR_Ref TObjectID="16023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD072">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16024" ObjectName="EC-CX_SB.CX_SB_LD072"/>
    <cge:TPSR_Ref TObjectID="16024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD361">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5435.000000 -848.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16020" ObjectName="EC-CX_SB.CX_SB_LD361"/>
    <cge:TPSR_Ref TObjectID="16020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD363">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5438.000000 -722.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16025" ObjectName="EC-CX_SB.CX_SB_LD363"/>
    <cge:TPSR_Ref TObjectID="16025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD365">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5432.000000 -571.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16026" ObjectName="EC-CX_SB.CX_SB_LD365"/>
    <cge:TPSR_Ref TObjectID="16026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_LD362">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5433.000000 -250.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16027" ObjectName="EC-CX_SB.CX_SB_LD362"/>
    <cge:TPSR_Ref TObjectID="16027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 -41.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -41.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.CX_SB_364LD">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5346.000000 -138.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22111" ObjectName="EC-CX_SB.CX_SB_364LD"/>
    <cge:TPSR_Ref TObjectID="22111"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_26c4f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-579 4136,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_21d2e40@0" ObjectIDZND0="g_20aa4d0@0" ObjectIDZND1="15647@x" ObjectIDZND2="g_20a81a0@0" Pin0InfoVect0LinkObjId="g_20aa4d0_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="g_20a81a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d2e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-579 4136,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c5170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4101,-604 4136,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="12190@1" ObjectIDZND0="g_21d2e40@0" ObjectIDZND1="g_20aa4d0@0" ObjectIDZND2="15647@x" Pin0InfoVect0LinkObjId="g_21d2e40_0" Pin0InfoVect1LinkObjId="g_20aa4d0_0" Pin0InfoVect2LinkObjId="SW-69816_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64539_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4101,-604 4136,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c53d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4166,-558 4166,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_20a81a0@0" ObjectIDZND0="g_20aa4d0@0" ObjectIDZND1="15647@x" ObjectIDZND2="g_21d2e40@0" Pin0InfoVect0LinkObjId="g_20aa4d0_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="g_21d2e40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a81a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4166,-558 4166,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c5630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-594 4208,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_20aa4d0@0" ObjectIDZND0="g_20a81a0@0" ObjectIDZND1="g_21d2e40@0" ObjectIDZND2="12190@x" Pin0InfoVect0LinkObjId="g_20a81a0_0" Pin0InfoVect1LinkObjId="g_21d2e40_0" Pin0InfoVect2LinkObjId="SW-64539_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20aa4d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-594 4208,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c5890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-604 4208,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="15647@0" ObjectIDZND0="g_20aa4d0@0" ObjectIDZND1="g_20a81a0@0" ObjectIDZND2="g_21d2e40@0" Pin0InfoVect0LinkObjId="g_20aa4d0_0" Pin0InfoVect1LinkObjId="g_20a81a0_0" Pin0InfoVect2LinkObjId="g_21d2e40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-604 4208,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c5af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-604 4166,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_20aa4d0@0" ObjectIDND1="15647@x" ObjectIDZND0="g_20a81a0@0" ObjectIDZND1="g_21d2e40@0" ObjectIDZND2="12190@x" Pin0InfoVect0LinkObjId="g_20a81a0_0" Pin0InfoVect1LinkObjId="g_21d2e40_0" Pin0InfoVect2LinkObjId="SW-64539_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20aa4d0_0" Pin1InfoVect1LinkObjId="SW-69816_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-604 4166,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c5d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4340,-594 4340,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_220ac10@0" ObjectIDZND0="g_201d100@0" ObjectIDZND1="12188@x" ObjectIDZND2="15647@x" Pin0InfoVect0LinkObjId="g_201d100_0" Pin0InfoVect1LinkObjId="SW-64538_0" Pin0InfoVect2LinkObjId="SW-69816_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220ac10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4340,-594 4340,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c5fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4287,-604 4340,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15647@1" ObjectIDZND0="g_220ac10@0" ObjectIDZND1="g_201d100@0" ObjectIDZND2="12188@x" Pin0InfoVect0LinkObjId="g_220ac10_0" Pin0InfoVect1LinkObjId="g_201d100_0" Pin0InfoVect2LinkObjId="SW-64538_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4287,-604 4340,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c6210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-579 4377,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_201d100@0" ObjectIDZND0="g_220ac10@0" ObjectIDZND1="15647@x" ObjectIDZND2="12188@x" Pin0InfoVect0LinkObjId="g_220ac10_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="SW-64538_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_201d100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-579 4377,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c6470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4340,-604 4377,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_220ac10@0" ObjectIDND1="15647@x" ObjectIDZND0="g_201d100@0" ObjectIDZND1="12188@x" Pin0InfoVect0LinkObjId="g_201d100_0" Pin0InfoVect1LinkObjId="SW-64538_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_220ac10_0" Pin1InfoVect1LinkObjId="SW-69816_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4340,-604 4377,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c66d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-604 4407,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_220ac10@0" ObjectIDND1="15647@x" ObjectIDND2="g_201d100@0" ObjectIDZND0="12188@0" Pin0InfoVect0LinkObjId="SW-64538_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_220ac10_0" Pin1InfoVect1LinkObjId="SW-69816_0" Pin1InfoVect2LinkObjId="g_201d100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-604 4407,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c6930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-604 4166,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_21d2e40@0" ObjectIDND1="12190@x" ObjectIDZND0="g_20aa4d0@0" ObjectIDZND1="15647@x" ObjectIDZND2="g_20a81a0@0" Pin0InfoVect0LinkObjId="g_20aa4d0_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="g_20a81a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21d2e40_0" Pin1InfoVect1LinkObjId="SW-64539_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-604 4166,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c6b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5196,-857 5196,-815 5212,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="14533@x" ObjectIDND1="15966@x" ObjectIDZND0="g_2268a70@0" Pin0InfoVect0LinkObjId="g_2268a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68397_0" Pin1InfoVect1LinkObjId="SW-68399_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5196,-857 5196,-815 5212,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c6df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5196,-857 5174,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2268a70@0" ObjectIDND1="15966@x" ObjectIDZND0="14533@0" Pin0InfoVect0LinkObjId="SW-68397_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2268a70_0" Pin1InfoVect1LinkObjId="SW-68399_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5196,-857 5174,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26c7050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5434,-924 5411,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5434,-924 5411,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c72b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-580 5192,-554 5208,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="14529@x" ObjectIDND1="14558@x" ObjectIDZND0="g_21ed2e0@0" Pin0InfoVect0LinkObjId="g_21ed2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68445_0" Pin1InfoVect1LinkObjId="SW-68606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-580 5192,-554 5208,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c7510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-580 5177,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_21ed2e0@0" ObjectIDND1="14558@x" ObjectIDZND0="14529@0" Pin0InfoVect0LinkObjId="SW-68445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21ed2e0_0" Pin1InfoVect1LinkObjId="SW-68606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-580 5177,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c7770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5209,-1077 5128,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_222c4f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_222c4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5209,-1077 5128,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c79d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5096,-1077 5061,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_26c7e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5096,-1077 5061,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c7c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5172,-1012 5130,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_224a100@0" ObjectIDZND0="15952@1" Pin0InfoVect0LinkObjId="SW-70748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_224a100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5172,-1012 5130,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c7e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5094,-1012 5061,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15952@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_26c79d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5094,-1012 5061,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c80f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5147,-858 5124,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14533@1" ObjectIDZND0="15965@1" Pin0InfoVect0LinkObjId="SW-68398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68397_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5147,-858 5124,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c8350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5090,-858 5061,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15965@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_26c79d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5090,-858 5061,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c85b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5093,-731 5061,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="14555@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_26c79d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5093,-731 5061,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c8810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-580 5192,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="14558@0" ObjectIDZND0="14529@x" ObjectIDZND1="g_21ed2e0@0" Pin0InfoVect0LinkObjId="SW-68445_0" Pin0InfoVect1LinkObjId="g_21ed2e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-580 5192,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c8a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5220,-857 5196,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="15966@0" ObjectIDZND0="14533@x" ObjectIDZND1="g_2268a70@0" Pin0InfoVect0LinkObjId="SW-68397_0" Pin0InfoVect1LinkObjId="g_2268a70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5220,-857 5196,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c8cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5280,-580 5280,-627 5310,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16026@x" ObjectIDND1="14558@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD365_0" Pin1InfoVect1LinkObjId="SW-68606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5280,-580 5280,-627 5310,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c8f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5253,-580 5280,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" ObjectIDND0="14558@1" ObjectIDZND0="16026@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD365_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5253,-580 5280,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c9190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5280,-580 5405,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="14558@x" ObjectIDZND0="16026@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD365_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-68606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5280,-580 5405,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c93f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5281,-259 5281,-306 5311,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16027@x" ObjectIDND1="14560@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD362_0" Pin1InfoVect1LinkObjId="SW-68608_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5281,-259 5281,-306 5311,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c9650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5281,-259 5253,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16027@x" ObjectIDND1="0@x" ObjectIDZND0="14560@1" Pin0InfoVect0LinkObjId="SW-68608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD362_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5281,-259 5253,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c98b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-857 5408,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="15966@x" ObjectIDND2="0@x" ObjectIDZND0="16020@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD361_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-68399_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-857 5408,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26c9b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5256,-857 5266,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15966@1" ObjectIDZND0="16020@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD361_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5256,-857 5266,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26c9d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-999 3954,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15960@1" ObjectIDZND0="15959@1" Pin0InfoVect0LinkObjId="SW-70977_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-999 3954,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26c9fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-997 4429,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15956@1" ObjectIDZND0="15955@1" Pin0InfoVect0LinkObjId="SW-70904_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-997 4429,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ca230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-951 4429,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15955@0" ObjectIDZND0="15957@1" Pin0InfoVect0LinkObjId="SW-70917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70904_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-951 4429,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ca490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-920 4429,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15957@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_26d56b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-920 4429,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ca6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-774 3914,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21fd230@0" ObjectIDZND0="15609@0" Pin0InfoVect0LinkObjId="SW-69737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21fd230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-774 3914,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ca950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-672 3869,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2246140@0" ObjectIDZND0="15610@1" Pin0InfoVect0LinkObjId="SW-69749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2246140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-672 3869,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cabb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-712 3894,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_2244930@0" ObjectIDZND0="15610@x" ObjectIDZND1="4843@x" ObjectIDZND2="g_22454e0@0" Pin0InfoVect0LinkObjId="SW-69749_0" Pin0InfoVect1LinkObjId="g_26cb070_0" Pin0InfoVect2LinkObjId="g_22454e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2244930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-712 3894,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-724 3869,-740 3894,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="15610@0" ObjectIDZND0="g_2244930@0" ObjectIDZND1="4843@x" ObjectIDZND2="g_22454e0@0" Pin0InfoVect0LinkObjId="g_2244930_0" Pin0InfoVect1LinkObjId="g_26cb070_0" Pin0InfoVect2LinkObjId="g_22454e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-724 3869,-740 3894,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cb070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-740 3917,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="15610@x" ObjectIDND1="g_2244930@0" ObjectIDZND0="4843@x" ObjectIDZND1="g_22454e0@0" Pin0InfoVect0LinkObjId="g_26cb2d0_0" Pin0InfoVect1LinkObjId="g_22454e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69749_0" Pin1InfoVect1LinkObjId="g_2244930_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-740 3917,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cb2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-740 3962,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="15610@x" ObjectIDND1="g_2244930@0" ObjectIDND2="g_22454e0@0" ObjectIDZND0="4843@x" Pin0InfoVect0LinkObjId="g_26cb070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-69749_0" Pin1InfoVect1LinkObjId="g_2244930_0" Pin1InfoVect2LinkObjId="g_22454e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-740 3962,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cb530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-671 4431,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21c8c70@0" ObjectIDZND0="11969@1" Pin0InfoVect0LinkObjId="SW-63168_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21c8c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-671 4431,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4456,-711 4456,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="g_21c7460@0" ObjectIDZND0="g_21c8010@0" ObjectIDZND1="12047@x" ObjectIDZND2="11969@x" Pin0InfoVect0LinkObjId="g_21c8010_0" Pin0InfoVect1LinkObjId="g_26cb9f0_0" Pin0InfoVect2LinkObjId="SW-63168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21c7460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4456,-711 4456,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cb9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4479,-739 4524,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_21c8010@0" ObjectIDND1="g_21c7460@0" ObjectIDND2="11969@x" ObjectIDZND0="12047@x" Pin0InfoVect0LinkObjId="g_26d7330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21c8010_0" Pin1InfoVect1LinkObjId="g_21c7460_0" Pin1InfoVect2LinkObjId="SW-63168_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4479,-739 4524,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26cbc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-714 3917,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22454e0@0" ObjectIDZND0="4843@x" ObjectIDZND1="15610@x" ObjectIDZND2="g_2244930@0" Pin0InfoVect0LinkObjId="g_26cb070_0" Pin0InfoVect1LinkObjId="SW-69749_0" Pin0InfoVect2LinkObjId="g_2244930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22454e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-714 3917,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26cbeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-695 4011,-604 4066,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4843@x" ObjectIDZND0="12190@0" Pin0InfoVect0LinkObjId="SW-64539_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cb070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-695 4011,-604 4066,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cc110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-351 3724,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14545@0" Pin0InfoVect0LinkObjId="SW-68593_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cc5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-351 3724,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cc370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-351 3509,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14541@0" Pin0InfoVect0LinkObjId="SW-68589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cc5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-351 3509,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cc5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-325 4191,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15947@0" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_26d43b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-325 4191,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cc830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-227 3852,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14525@x" ObjectIDND1="g_2390ce0@0" ObjectIDZND0="14548@0" Pin0InfoVect0LinkObjId="SW-68596_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68338_0" Pin1InfoVect1LinkObjId="g_2390ce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-227 3852,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-227 3896,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14548@1" ObjectIDZND0="g_21e0bc0@0" Pin0InfoVect0LinkObjId="g_21e0bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-227 3896,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cccf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-244 3830,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14525@0" ObjectIDZND0="g_2390ce0@0" ObjectIDZND1="14548@x" Pin0InfoVect0LinkObjId="g_2390ce0_0" Pin0InfoVect1LinkObjId="SW-68596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-244 3830,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ccf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-269 3724,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14524@1" ObjectIDZND0="14545@1" Pin0InfoVect0LinkObjId="SW-68593_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-269 3724,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cd1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-225 3746,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14524@x" ObjectIDND1="g_2390350@0" ObjectIDZND0="14546@0" Pin0InfoVect0LinkObjId="SW-68594_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68315_0" Pin1InfoVect1LinkObjId="g_2390350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-225 3746,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cd410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3782,-225 3790,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14546@1" ObjectIDZND0="g_23857d0@0" Pin0InfoVect0LinkObjId="g_23857d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68594_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3782,-225 3790,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cd670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-242 3724,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14524@0" ObjectIDZND0="g_2390350@0" ObjectIDZND1="14546@x" Pin0InfoVect0LinkObjId="g_2390350_0" Pin0InfoVect1LinkObjId="SW-68594_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-242 3724,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cd8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-270 3615,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14523@1" ObjectIDZND0="14543@1" Pin0InfoVect0LinkObjId="SW-68591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-270 3615,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cdb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-226 3637,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14523@x" ObjectIDND1="g_23918d0@0" ObjectIDZND0="14544@0" Pin0InfoVect0LinkObjId="SW-68592_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68292_0" Pin1InfoVect1LinkObjId="g_23918d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-226 3637,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cdd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-226 3681,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14544@1" ObjectIDZND0="g_2389430@0" Pin0InfoVect0LinkObjId="g_2389430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-226 3681,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cdff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-243 3615,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14523@0" ObjectIDZND0="g_23918d0@0" ObjectIDZND1="14544@x" Pin0InfoVect0LinkObjId="g_23918d0_0" Pin0InfoVect1LinkObjId="SW-68592_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-243 3615,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ce250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-270 3509,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14522@1" ObjectIDZND0="14541@1" Pin0InfoVect0LinkObjId="SW-68589_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-270 3509,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ce4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-226 3531,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14522@x" ObjectIDND1="g_23924f0@0" ObjectIDZND0="14542@0" Pin0InfoVect0LinkObjId="SW-68590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68269_0" Pin1InfoVect1LinkObjId="g_23924f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-226 3531,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ce710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3567,-226 3575,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14542@1" ObjectIDZND0="g_238d090@0" Pin0InfoVect0LinkObjId="g_238d090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3567,-226 3575,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ce970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-243 3509,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14522@0" ObjectIDZND0="g_23924f0@0" ObjectIDZND1="14542@x" Pin0InfoVect0LinkObjId="g_23924f0_0" Pin0InfoVect1LinkObjId="SW-68590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-243 3509,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-271 3830,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14525@1" ObjectIDZND0="14547@1" Pin0InfoVect0LinkObjId="SW-68595_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-271 3830,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-131 3854,-131 3854,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12035@x" ObjectIDND1="16023@x" ObjectIDZND0="g_238dac0@0" Pin0InfoVect0LinkObjId="g_238dac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63514_0" Pin1InfoVect1LinkObjId="EC-CX_SB.CX_SB_LD064_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-131 3854,-131 3854,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cf090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-132 3748,-132 3748,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16022@x" ObjectIDND1="12034@x" ObjectIDZND0="g_2389e60@0" Pin0InfoVect0LinkObjId="g_2389e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD063_0" Pin1InfoVect1LinkObjId="SW-63513_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-132 3748,-132 3748,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cf2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-132 3639,-132 3639,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12033@x" ObjectIDND1="16021@x" ObjectIDZND0="g_2386200@0" Pin0InfoVect0LinkObjId="g_2386200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63512_0" Pin1InfoVect1LinkObjId="EC-CX_SB.CX_SB_LD062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-132 3639,-132 3639,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cf550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-132 3724,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2389e60@0" ObjectIDND1="12034@x" ObjectIDZND0="16022@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2389e60_0" Pin1InfoVect1LinkObjId="SW-63513_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-132 3724,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cf7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-148 3724,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12034@1" ObjectIDZND0="16022@x" ObjectIDZND1="g_2389e60@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD063_0" Pin0InfoVect1LinkObjId="g_2389e60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-148 3724,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cfa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-225 3724,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="14524@x" ObjectIDND1="14546@x" ObjectIDZND0="g_2390350@1" Pin0InfoVect0LinkObjId="g_2390350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68315_0" Pin1InfoVect1LinkObjId="SW-68594_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-225 3724,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cfc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-194 3724,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2390350@0" ObjectIDZND0="12034@0" Pin0InfoVect0LinkObjId="SW-63513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2390350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-194 3724,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cfed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-131 3830,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12035@x" ObjectIDND1="g_238dac0@0" ObjectIDZND0="16023@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63514_0" Pin1InfoVect1LinkObjId="g_238dac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-131 3830,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d0130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-146 3830,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12035@1" ObjectIDZND0="16023@x" ObjectIDZND1="g_238dac0@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD064_0" Pin0InfoVect1LinkObjId="g_238dac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-146 3830,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d0390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-182 3830,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12035@0" ObjectIDZND0="g_2390ce0@0" Pin0InfoVect0LinkObjId="g_2390ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-182 3830,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d05f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-213 3830,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2390ce0@1" ObjectIDZND0="14525@x" ObjectIDZND1="14548@x" Pin0InfoVect0LinkObjId="SW-68338_0" Pin0InfoVect1LinkObjId="SW-68596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2390ce0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-213 3830,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d0850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-132 3615,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12033@x" ObjectIDND1="g_2386200@0" ObjectIDZND0="16021@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63512_0" Pin1InfoVect1LinkObjId="g_2386200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-132 3615,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d0ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-147 3615,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12033@1" ObjectIDZND0="16021@x" ObjectIDZND1="g_2386200@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD062_0" Pin0InfoVect1LinkObjId="g_2386200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-147 3615,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d0d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-183 3615,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12033@0" ObjectIDZND0="g_23918d0@0" Pin0InfoVect0LinkObjId="g_23918d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-183 3615,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d0f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-212 3615,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_23918d0@1" ObjectIDZND0="14523@x" ObjectIDZND1="14544@x" Pin0InfoVect0LinkObjId="SW-68292_0" Pin0InfoVect1LinkObjId="SW-68592_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23918d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-212 3615,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d11d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-226 3509,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="14522@x" ObjectIDND1="14542@x" ObjectIDZND0="g_23924f0@1" Pin0InfoVect0LinkObjId="g_23924f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68269_0" Pin1InfoVect1LinkObjId="SW-68590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-226 3509,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-351 3615,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14543@0" Pin0InfoVect0LinkObjId="SW-68591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cc5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-351 3615,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d1690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-351 3830,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14547@0" Pin0InfoVect0LinkObjId="SW-68595_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cc5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-351 3830,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d18f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-351 4064,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14552@0" Pin0InfoVect0LinkObjId="SW-68600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cc5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-351 4064,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d1b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-232 4003,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14550@1" ObjectIDZND0="g_239cc10@0" Pin0InfoVect0LinkObjId="g_239cc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-232 4003,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d1db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-187 3937,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="14551@0" ObjectIDZND0="g_2530d40@0" Pin0InfoVect0LinkObjId="g_2530d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-187 3937,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d2010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-232 3937,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="14550@0" ObjectIDZND0="14526@x" ObjectIDZND1="g_2530d40@0" Pin0InfoVect0LinkObjId="SW-68359_0" Pin0InfoVect1LinkObjId="g_2530d40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-232 3937,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d2270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-218 3937,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2530d40@1" ObjectIDZND0="14526@x" ObjectIDZND1="14550@x" Pin0InfoVect0LinkObjId="SW-68359_0" Pin0InfoVect1LinkObjId="SW-68598_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2530d40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-218 3937,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d24d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-232 3937,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2530d40@0" ObjectIDND1="14550@x" ObjectIDZND0="14526@0" Pin0InfoVect0LinkObjId="SW-68359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2530d40_0" Pin1InfoVect1LinkObjId="SW-68598_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-232 3937,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d2730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-151 3937,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="14551@1" ObjectIDZND0="28611@x" ObjectIDZND1="12030@x" Pin0InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb1_0" Pin0InfoVect1LinkObjId="SW-63509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-151 3937,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d2990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-131 3937,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28611@0" ObjectIDZND0="14551@x" ObjectIDZND1="12030@x" Pin0InfoVect0LinkObjId="SW-68599_0" Pin0InfoVect1LinkObjId="SW-63509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-131 3937,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d2bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-228 4130,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14553@1" ObjectIDZND0="g_25373e0@0" Pin0InfoVect0LinkObjId="g_25373e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-228 4130,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d2e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-272 4064,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14527@1" ObjectIDZND0="14552@1" Pin0InfoVect0LinkObjId="SW-68600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-272 4064,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d30b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-183 4064,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="14554@0" ObjectIDZND0="g_253a350@0" Pin0InfoVect0LinkObjId="g_253a350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-183 4064,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d3310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4086,-228 4064,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="14553@0" ObjectIDZND0="14527@x" ObjectIDZND1="g_253a350@0" Pin0InfoVect0LinkObjId="SW-68377_0" Pin0InfoVect1LinkObjId="g_253a350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4086,-228 4064,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d3570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-214 4064,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_253a350@1" ObjectIDZND0="14527@x" ObjectIDZND1="14553@x" Pin0InfoVect0LinkObjId="SW-68377_0" Pin0InfoVect1LinkObjId="SW-68601_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_253a350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-214 4064,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d37d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-228 4064,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_253a350@0" ObjectIDND1="14553@x" ObjectIDZND0="14527@0" Pin0InfoVect0LinkObjId="SW-68377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_253a350_0" Pin1InfoVect1LinkObjId="SW-68601_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-228 4064,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d3a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-147 4064,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="14554@1" ObjectIDZND0="28612@x" ObjectIDZND1="12031@x" Pin0InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb3_0" Pin0InfoVect1LinkObjId="SW-63510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-147 4064,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d3c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-127 4064,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28612@0" ObjectIDZND0="14554@x" ObjectIDZND1="12031@x" Pin0InfoVect0LinkObjId="SW-68602_0" Pin0InfoVect1LinkObjId="SW-63510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-127 4064,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d3ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-434 3604,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="15946@x" ObjectIDND1="g_2544480@0" ObjectIDND2="18054@x" ObjectIDZND0="g_2545880@0" Pin0InfoVect0LinkObjId="g_2545880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70705_0" Pin1InfoVect1LinkObjId="g_2544480_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-434 3604,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d4150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-409 3604,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15946@0" ObjectIDZND0="g_2545880@0" ObjectIDZND1="g_2544480@0" ObjectIDZND2="18054@x" Pin0InfoVect0LinkObjId="g_2545880_0" Pin0InfoVect1LinkObjId="g_2544480_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-409 3604,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d43b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-373 3604,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15946@1" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_26cc5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-373 3604,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d4610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-434 3562,-434 3562,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2545880@0" ObjectIDND1="15946@x" ObjectIDND2="18054@x" ObjectIDZND0="g_2544480@0" Pin0InfoVect0LinkObjId="g_2544480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2545880_0" Pin1InfoVect1LinkObjId="SW-70705_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-434 3562,-434 3562,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d4870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-374 3749,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_26cc5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-374 3749,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d4ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-410 3749,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_254b170@1" Pin0InfoVect0LinkObjId="g_254b170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-410 3749,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d4d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-445 3749,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_254b170@0" ObjectIDZND0="g_2549d60@0" Pin0InfoVect0LinkObjId="g_2549d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_254b170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-445 3749,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d4f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-270 4191,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2550cf0@0" ObjectIDZND0="15947@1" Pin0InfoVect0LinkObjId="SW-70711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2550cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-270 4191,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d51f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-161 4191,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_254e200@0" Pin0InfoVect0LinkObjId="g_254e200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-161 4191,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d5450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-228 4191,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_254e200@1" ObjectIDZND0="g_2550cf0@1" Pin0InfoVect0LinkObjId="g_2550cf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_254e200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-228 4191,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d56b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-890 3963,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15632@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_26ca490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-890 3963,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d5910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-854 3963,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15608@1" ObjectIDZND0="15632@1" Pin0InfoVect0LinkObjId="SW-69736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-854 3963,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d5b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-808 3963,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15633@1" ObjectIDZND0="15608@0" Pin0InfoVect0LinkObjId="SW-69725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-808 3963,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d5dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-774 3963,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="15609@1" ObjectIDZND0="4843@x" ObjectIDZND1="15633@x" Pin0InfoVect0LinkObjId="g_26cb070_0" Pin0InfoVect1LinkObjId="SW-69736_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-774 3963,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d6030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-886 4525,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="12071@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_26ca490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-886 4525,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d6290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-759 3963,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4843@1" ObjectIDZND0="15633@x" ObjectIDZND1="15609@x" Pin0InfoVect0LinkObjId="SW-69736_0" Pin0InfoVect1LinkObjId="SW-69737_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cb070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-759 3963,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d64f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-773 3963,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4843@x" ObjectIDND1="15609@x" ObjectIDZND0="15633@0" Pin0InfoVect0LinkObjId="SW-69736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26cb070_0" Pin1InfoVect1LinkObjId="SW-69737_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-773 3963,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d6750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-758 4525,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="12047@1" ObjectIDZND0="12072@x" ObjectIDZND1="g_258cbe0@0" ObjectIDZND2="11975@x" Pin0InfoVect0LinkObjId="SW-63584_0" Pin0InfoVect1LinkObjId="g_258cbe0_0" Pin0InfoVect2LinkObjId="SW-63432_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cb9f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-758 4525,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d69b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-773 4525,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="12047@x" ObjectIDND1="g_258cbe0@0" ObjectIDND2="11975@x" ObjectIDZND0="12072@0" Pin0InfoVect0LinkObjId="SW-63584_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26cb9f0_0" Pin1InfoVect1LinkObjId="g_258cbe0_0" Pin1InfoVect2LinkObjId="SW-63432_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-773 4525,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d6c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-720 4044,-720 4044,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4843@2" ObjectIDZND0="15616@0" Pin0InfoVect0LinkObjId="SW-69769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cb070_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4017,-720 4044,-720 4044,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-462 4044,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15616@1" ObjectIDZND0="15614@1" Pin0InfoVect0LinkObjId="SW-69765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-462 4044,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d70d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-417 4044,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15614@0" ObjectIDZND0="15615@0" Pin0InfoVect0LinkObjId="SW-69768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-417 4044,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26d7330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-604 4476,-604 4524,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="12188@1" ObjectIDZND0="12047@x" Pin0InfoVect0LinkObjId="g_26cb9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64538_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-604 4476,-604 4524,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d7590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-351 4557,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="11986@0" Pin0InfoVect0LinkObjId="SW-63465_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26d9210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-351 4557,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d77f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4615,-227 4624,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11987@1" ObjectIDZND0="g_2566470@0" Pin0InfoVect0LinkObjId="g_2566470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63466_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4615,-227 4624,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d7a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-271 4557,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11985@1" ObjectIDZND0="11986@1" Pin0InfoVect0LinkObjId="SW-63465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-271 4557,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d7cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-182 4557,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12028@0" ObjectIDZND0="g_256b3f0@0" Pin0InfoVect0LinkObjId="g_256b3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-182 4557,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d7f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-351 4432,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="12010@0" Pin0InfoVect0LinkObjId="SW-63489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26d9210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-351 4432,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d8170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-227 4499,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12011@1" ObjectIDZND0="g_256e220@0" Pin0InfoVect0LinkObjId="g_256e220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63490_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-227 4499,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d83d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-271 4432,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12008@1" ObjectIDZND0="12010@1" Pin0InfoVect0LinkObjId="SW-63489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-271 4432,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d8630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-182 4432,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12012@0" ObjectIDZND0="g_2571370@0" Pin0InfoVect0LinkObjId="g_2571370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-182 4432,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d8890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4454,-227 4432,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="12011@0" ObjectIDZND0="g_2571370@0" ObjectIDZND1="12008@x" Pin0InfoVect0LinkObjId="g_2571370_0" Pin0InfoVect1LinkObjId="SW-63884_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4454,-227 4432,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d8af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-213 4432,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2571370@1" ObjectIDZND0="12008@x" ObjectIDZND1="12011@x" Pin0InfoVect0LinkObjId="SW-63884_0" Pin0InfoVect1LinkObjId="SW-63490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2571370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-213 4432,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d8d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-227 4432,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2571370@0" ObjectIDND1="12011@x" ObjectIDZND0="12008@0" Pin0InfoVect0LinkObjId="SW-63884_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2571370_0" Pin1InfoVect1LinkObjId="SW-63490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-227 4432,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d8fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4247,-351 4247,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="15951@0" Pin0InfoVect0LinkObjId="SW-70736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cc5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4247,-351 4247,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d9210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-334 4370,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="12036@0" ObjectIDZND0="11962@0" Pin0InfoVect0LinkObjId="g_26d9df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-334 4370,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d9470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4247,-297 4247,-280 4296,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15951@1" ObjectIDZND0="15963@1" Pin0InfoVect0LinkObjId="SW-70734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4247,-297 4247,-280 4296,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d96d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-280 4370,-280 4370,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15963@0" ObjectIDZND0="12036@1" Pin0InfoVect0LinkObjId="SW-63515_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-280 4370,-280 4370,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4600,-462 4600,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11966@1" ObjectIDZND0="11963@1" Pin0InfoVect0LinkObjId="SW-63162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63165_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4600,-462 4600,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d9b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4600,-417 4600,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11963@0" ObjectIDZND0="11965@0" Pin0InfoVect0LinkObjId="SW-63164_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4600,-417 4600,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d9df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4600,-365 4600,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11965@1" ObjectIDZND0="11962@0" Pin0InfoVect0LinkObjId="g_26d9210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63164_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4600,-365 4600,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26da050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-403 4476,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="12027@0" ObjectIDZND0="g_25d1020@0" ObjectIDZND1="g_25881c0@0" ObjectIDZND2="12196@x" Pin0InfoVect0LinkObjId="g_25d1020_0" Pin0InfoVect1LinkObjId="g_25881c0_0" Pin0InfoVect2LinkObjId="SW-64576_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-403 4476,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26da2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-428 4434,-428 4434,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12027@x" ObjectIDND1="g_25d1020@0" ObjectIDND2="12196@x" ObjectIDZND0="g_25881c0@0" Pin0InfoVect0LinkObjId="g_25881c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="g_25d1020_0" Pin1InfoVect2LinkObjId="SW-64576_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-428 4434,-428 4434,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26da510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-367 4476,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="12027@1" ObjectIDZND0="11962@0" Pin0InfoVect0LinkObjId="g_26d9210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-367 4476,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26da770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-524 5061,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15611@1" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_26c79d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69757_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-524 5061,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26da9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-524 5011,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15613@0" ObjectIDZND0="15611@0" Pin0InfoVect0LinkObjId="SW-69757_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-524 5011,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26dac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-773 4569,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12072@x" ObjectIDND1="12047@x" ObjectIDND2="11975@x" ObjectIDZND0="g_258cbe0@0" Pin0InfoVect0LinkObjId="g_258cbe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63584_0" Pin1InfoVect1LinkObjId="g_26cb9f0_0" Pin1InfoVect2LinkObjId="SW-63432_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-773 4569,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26dae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-719 4600,-719 4600,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="12047@2" ObjectIDZND0="g_258d990@0" ObjectIDZND1="11966@x" Pin0InfoVect0LinkObjId="g_258d990_0" Pin0InfoVect1LinkObjId="SW-63165_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cb9f0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-719 4600,-719 4600,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26db0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-631 4555,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_258e740@0" ObjectIDZND0="12047@x" ObjectIDZND1="11979@x" Pin0InfoVect0LinkObjId="g_26cb9f0_0" Pin0InfoVect1LinkObjId="SW-63458_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258e740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-631 4555,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26db350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4524,-678 4524,-649 4555,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="12047@0" ObjectIDZND0="g_258e740@0" ObjectIDZND1="11979@x" Pin0InfoVect0LinkObjId="g_258e740_0" Pin0InfoVect1LinkObjId="SW-63458_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cb9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4524,-678 4524,-649 4555,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26db5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-649 4893,-649 4893,-299 4915,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="12047@x" ObjectIDND1="g_258e740@0" ObjectIDZND0="11979@0" Pin0InfoVect0LinkObjId="SW-63458_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26cb9f0_0" Pin1InfoVect1LinkObjId="g_258e740_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-649 4893,-649 4893,-299 4915,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26db820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-679 3962,-524 4915,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4843@0" ObjectIDZND0="15612@0" Pin0InfoVect0LinkObjId="SW-69758_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26cb070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-679 3962,-524 4915,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26dba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-524 4967,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15612@1" ObjectIDZND0="15613@1" Pin0InfoVect0LinkObjId="SW-69759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-524 4967,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26dbce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5318,-924 5330,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5318,-924 5330,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26dbf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5372,-924 5384,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5372,-924 5384,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26dc1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-857 5266,-829 5275,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="16020@x" ObjectIDND1="15966@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD361_0" Pin1InfoVect1LinkObjId="SW-68399_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-857 5266,-829 5275,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26dc400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5320,-829 5331,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_258fe90@0" Pin0InfoVect0LinkObjId="g_258fe90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5320,-829 5331,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26dc660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-626 5365,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2596680@0" Pin0InfoVect0LinkObjId="g_2596680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-626 5365,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26dc8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5356,-305 5367,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_259a1c0@0" Pin0InfoVect0LinkObjId="g_259a1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5356,-305 5367,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26dcb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5171,-62 5129,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_259ac30@0" ObjectIDZND0="15953@1" Pin0InfoVect0LinkObjId="SW-70754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_259ac30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5171,-62 5129,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26dcd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5093,-62 5061,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15953@0" ObjectIDZND0="12193@0" Pin0InfoVect0LinkObjId="g_26de540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5093,-62 5061,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26dcfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-1053 4128,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_25a9010@0" ObjectIDND1="15605@x" ObjectIDND2="15606@x" ObjectIDZND0="g_25aaef0@0" Pin0InfoVect0LinkObjId="g_25aaef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25a9010_0" Pin1InfoVect1LinkObjId="SW-69508_0" Pin1InfoVect2LinkObjId="SW-69509_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-1053 4128,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26dd240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-1053 4097,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25aaef0@0" ObjectIDND1="15605@x" ObjectIDND2="15606@x" ObjectIDZND0="g_25a9010@0" Pin0InfoVect0LinkObjId="g_25a9010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25aaef0_0" Pin1InfoVect1LinkObjId="SW-69508_0" Pin1InfoVect2LinkObjId="SW-69509_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-1053 4097,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26dd4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4479,-713 4479,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_21c8010@0" ObjectIDZND0="12047@x" ObjectIDZND1="g_21c7460@0" ObjectIDZND2="11969@x" Pin0InfoVect0LinkObjId="g_26cb9f0_0" Pin0InfoVect1LinkObjId="g_21c7460_0" Pin0InfoVect2LinkObjId="SW-63168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21c8010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4479,-713 4479,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26dd700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4479,-739 4456,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_21c8010@0" ObjectIDND1="12047@x" ObjectIDZND0="g_21c7460@0" ObjectIDZND1="11969@x" Pin0InfoVect0LinkObjId="g_21c7460_0" Pin0InfoVect1LinkObjId="SW-63168_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21c8010_0" Pin1InfoVect1LinkObjId="g_26cb9f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4479,-739 4456,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26dd960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-723 4431,-739 4456,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="11969@0" ObjectIDZND0="g_21c8010@0" ObjectIDZND1="12047@x" ObjectIDZND2="g_21c7460@0" Pin0InfoVect0LinkObjId="g_21c8010_0" Pin0InfoVect1LinkObjId="g_26cb9f0_0" Pin0InfoVect2LinkObjId="g_21c7460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-723 4431,-739 4456,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ddbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-731 5147,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14555@1" ObjectIDZND0="14528@1" Pin0InfoVect0LinkObjId="SW-68422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68603_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-731 5147,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26dde20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5174,-731 5196,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14528@0" ObjectIDZND0="g_223f170@0" ObjectIDZND1="14556@x" Pin0InfoVect0LinkObjId="g_223f170_0" Pin0InfoVect1LinkObjId="SW-68604_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5174,-731 5196,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26de080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5212,-705 5196,-705 5196,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_223f170@0" ObjectIDZND0="14528@x" ObjectIDZND1="14556@x" Pin0InfoVect0LinkObjId="SW-68422_0" Pin0InfoVect1LinkObjId="SW-68604_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_223f170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5212,-705 5196,-705 5196,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26de2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5196,-731 5217,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_223f170@0" ObjectIDND1="14528@x" ObjectIDZND0="14556@0" Pin0InfoVect0LinkObjId="SW-68604_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_223f170_0" Pin1InfoVect1LinkObjId="SW-68422_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5196,-731 5217,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26de540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-299 5061,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11978@1" ObjectIDZND0="12193@0" Pin0InfoVect0LinkObjId="g_26dcd80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63457_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-299 5061,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26de7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5193,-259 5177,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_21ede80@0" ObjectIDND1="14560@x" ObjectIDZND0="14530@0" Pin0InfoVect0LinkObjId="SW-68468_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21ede80_0" Pin1InfoVect1LinkObjId="SW-68608_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5193,-259 5177,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26dea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5209,-233 5193,-233 5193,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_21ede80@0" ObjectIDZND0="14530@x" ObjectIDZND1="14560@x" Pin0InfoVect0LinkObjId="SW-68468_0" Pin0InfoVect1LinkObjId="SW-68608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ede80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5209,-233 5193,-233 5193,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26dec60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5193,-259 5217,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_21ede80@0" ObjectIDND1="14530@x" ObjectIDZND0="14560@0" Pin0InfoVect0LinkObjId="SW-68608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21ede80_0" Pin1InfoVect1LinkObjId="SW-68468_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5193,-259 5217,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26deec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-132 3533,-132 3533,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12032@x" ObjectIDND1="16028@x" ObjectIDZND0="g_21e15c0@0" Pin0InfoVect0LinkObjId="g_21e15c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63511_0" Pin1InfoVect1LinkObjId="EC-CX_SB.CX_SB_LD061_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-132 3533,-132 3533,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26df120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-132 3509,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12032@x" ObjectIDND1="g_21e15c0@0" ObjectIDZND0="16028@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63511_0" Pin1InfoVect1LinkObjId="g_21e15c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-132 3509,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26df380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-193 3509,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_23924f0@0" ObjectIDZND0="12032@0" Pin0InfoVect0LinkObjId="SW-63511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23924f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-193 3509,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26df5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-146 3509,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12032@1" ObjectIDZND0="16028@x" ObjectIDZND1="g_21e15c0@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD061_0" Pin0InfoVect1LinkObjId="g_21e15c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-146 3509,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26df840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-227 4557,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="11987@0" ObjectIDZND0="g_256b3f0@0" ObjectIDZND1="11985@x" Pin0InfoVect0LinkObjId="g_256b3f0_0" Pin0InfoVect1LinkObjId="SW-63464_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63466_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-227 4557,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26dfaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-244 4557,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11985@0" ObjectIDZND0="g_256b3f0@0" ObjectIDZND1="11987@x" Pin0InfoVect0LinkObjId="g_256b3f0_0" Pin0InfoVect1LinkObjId="SW-63466_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63464_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-244 4557,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26dfd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-227 4557,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11985@x" ObjectIDND1="11987@x" ObjectIDZND0="g_256b3f0@1" Pin0InfoVect0LinkObjId="g_256b3f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63464_0" Pin1InfoVect1LinkObjId="SW-63466_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-227 4557,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26dff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-130 4581,-130 4581,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12028@x" ObjectIDND1="16024@x" ObjectIDZND0="g_25695c0@0" Pin0InfoVect0LinkObjId="g_25695c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63507_0" Pin1InfoVect1LinkObjId="EC-CX_SB.CX_SB_LD072_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-130 4581,-130 4581,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e01c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-146 4557,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="12028@1" ObjectIDZND0="g_25695c0@0" ObjectIDZND1="16024@x" Pin0InfoVect0LinkObjId="g_25695c0_0" Pin0InfoVect1LinkObjId="EC-CX_SB.CX_SB_LD072_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63507_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-146 4557,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e0420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-130 4557,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12028@x" ObjectIDND1="g_25695c0@0" ObjectIDZND0="16024@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63507_0" Pin1InfoVect1LinkObjId="g_25695c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-130 4557,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e0680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4454,-99 4445,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="12114@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4454,-99 4445,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-428 4476,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12027@x" ObjectIDND1="g_25881c0@0" ObjectIDND2="12196@x" ObjectIDZND0="g_25d1020@1" Pin0InfoVect0LinkObjId="g_25d1020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="g_25881c0_0" Pin1InfoVect2LinkObjId="SW-64576_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-428 4476,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-463 4476,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_25d1020@0" ObjectIDZND0="g_2589710@0" Pin0InfoVect0LinkObjId="g_2589710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d1020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-463 4476,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e0da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-1110 3954,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_22126e0@0" ObjectIDZND0="g_22133c0@0" ObjectIDZND1="11720@1" ObjectIDZND2="15960@x" Pin0InfoVect0LinkObjId="g_22133c0_0" Pin0InfoVect1LinkObjId="g_26e14c0_1" Pin0InfoVect2LinkObjId="SW-70990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22126e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-1110 3954,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e1000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-1110 3979,-1119 3954,-1119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_22133c0@0" ObjectIDZND0="g_22126e0@0" ObjectIDZND1="15960@x" ObjectIDZND2="15962@x" Pin0InfoVect0LinkObjId="g_22126e0_0" Pin0InfoVect1LinkObjId="SW-70990_0" Pin0InfoVect2LinkObjId="SW-70991_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22133c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-1110 3979,-1119 3954,-1119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e1260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-1110 3954,-1119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_22126e0@0" ObjectIDND1="15960@x" ObjectIDND2="15962@x" ObjectIDZND0="g_22133c0@0" ObjectIDZND1="11720@1" Pin0InfoVect0LinkObjId="g_22133c0_0" Pin0InfoVect1LinkObjId="g_26e14c0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22126e0_0" Pin1InfoVect1LinkObjId="SW-70990_0" Pin1InfoVect2LinkObjId="SW-70991_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-1110 3954,-1119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-1119 3954,-1132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_22126e0@0" ObjectIDND1="15960@x" ObjectIDND2="15962@x" ObjectIDZND0="11720@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22126e0_0" Pin1InfoVect1LinkObjId="SW-70990_0" Pin1InfoVect2LinkObjId="SW-70991_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-1119 3954,-1132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e1720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-1111 4429,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_2214300@0" ObjectIDZND0="g_2215540@0" ObjectIDZND1="11856@1" ObjectIDZND2="15956@x" Pin0InfoVect0LinkObjId="g_2215540_0" Pin0InfoVect1LinkObjId="g_26e1e40_1" Pin0InfoVect2LinkObjId="SW-70917_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2214300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-1111 4429,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e1980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4455,-1111 4455,-1120 4429,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2215540@0" ObjectIDZND0="g_2214300@0" ObjectIDZND1="15956@x" ObjectIDZND2="15958@x" Pin0InfoVect0LinkObjId="g_2214300_0" Pin0InfoVect1LinkObjId="SW-70917_0" Pin0InfoVect2LinkObjId="SW-70918_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2215540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4455,-1111 4455,-1120 4429,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e1be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-1111 4429,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2214300@0" ObjectIDND1="15956@x" ObjectIDND2="15958@x" ObjectIDZND0="g_2215540@0" ObjectIDZND1="11856@1" Pin0InfoVect0LinkObjId="g_2215540_0" Pin0InfoVect1LinkObjId="g_26e1e40_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2214300_0" Pin1InfoVect1LinkObjId="SW-70917_0" Pin1InfoVect2LinkObjId="SW-70918_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-1111 4429,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e1e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-1120 4429,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2214300@0" ObjectIDND1="15956@x" ObjectIDND2="15958@x" ObjectIDZND0="11856@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2214300_0" Pin1InfoVect1LinkObjId="SW-70917_0" Pin1InfoVect2LinkObjId="SW-70918_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-1120 4429,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e20a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-956 3954,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15959@0" ObjectIDZND0="15961@1" Pin0InfoVect0LinkObjId="SW-70990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70977_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-956 3954,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26e2300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-923 3954,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15961@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_26ca490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-923 3954,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26e2560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5214,-121 5198,-121 5198,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_21efe40@0" ObjectIDZND0="14531@x" ObjectIDZND1="14562@x" Pin0InfoVect0LinkObjId="SW-68491_0" Pin0InfoVect1LinkObjId="SW-68610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21efe40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5214,-121 5198,-121 5198,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26e27c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-147 5198,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="14562@0" ObjectIDZND0="g_21efe40@0" ObjectIDZND1="14531@x" Pin0InfoVect0LinkObjId="g_21efe40_0" Pin0InfoVect1LinkObjId="SW-68491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-147 5198,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26e2a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5198,-147 5177,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_21efe40@0" ObjectIDND1="14562@x" ObjectIDZND0="14531@0" Pin0InfoVect0LinkObjId="SW-68491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21efe40_0" Pin1InfoVect1LinkObjId="SW-68610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5198,-147 5177,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e2c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-365 4044,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15615@1" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_26cc5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69768_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-365 4044,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f3670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-428 4498,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="12027@x" ObjectIDND1="g_25d1020@0" ObjectIDND2="g_25881c0@0" ObjectIDZND0="12196@0" Pin0InfoVect0LinkObjId="SW-64576_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="g_25d1020_0" Pin1InfoVect2LinkObjId="g_25881c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-428 4498,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f38d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4529,-428 4551,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12196@1" ObjectIDZND0="g_2588ce0@0" Pin0InfoVect0LinkObjId="g_2588ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4529,-428 4551,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-136 4086,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28612@x" ObjectIDND1="14554@x" ObjectIDZND0="12031@0" Pin0InfoVect0LinkObjId="SW-63510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb3_0" Pin1InfoVect1LinkObjId="SW-68602_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-136 4086,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f8770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-136 4139,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12031@1" ObjectIDZND0="g_253d410@0" Pin0InfoVect0LinkObjId="g_253d410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-136 4139,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f89d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-140 3959,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28611@x" ObjectIDND1="14551@x" ObjectIDZND0="12030@0" Pin0InfoVect0LinkObjId="SW-63509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb1_0" Pin1InfoVect1LinkObjId="SW-68599_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-140 3959,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f8c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-140 4012,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12030@1" ObjectIDZND0="g_2533d20@0" Pin0InfoVect0LinkObjId="g_2533d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-140 4012,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26fe100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-996 4097,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="15605@0" ObjectIDZND0="g_25a9010@0" ObjectIDZND1="g_25aaef0@0" ObjectIDZND2="15606@x" Pin0InfoVect0LinkObjId="g_25a9010_0" Pin0InfoVect1LinkObjId="g_25aaef0_0" Pin0InfoVect2LinkObjId="SW-69509_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-996 4097,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26fe360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-1012 4097,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="15605@x" ObjectIDND1="15606@x" ObjectIDZND0="g_25a9010@0" ObjectIDZND1="g_25aaef0@0" Pin0InfoVect0LinkObjId="g_25a9010_0" Pin0InfoVect1LinkObjId="g_25aaef0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="SW-69509_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-1012 4097,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26fe5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1012 4153,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25a99d0@0" ObjectIDZND0="15606@1" Pin0InfoVect0LinkObjId="SW-69509_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a99d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1012 4153,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26fe820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4117,-1012 4097,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="15606@0" ObjectIDZND0="15605@x" ObjectIDZND1="g_25a9010@0" ObjectIDZND2="g_25aaef0@0" Pin0InfoVect0LinkObjId="SW-69508_0" Pin0InfoVect1LinkObjId="g_25a9010_0" Pin0InfoVect2LinkObjId="g_25aaef0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4117,-1012 4097,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ff310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-960 4097,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="15605@1" ObjectIDZND0="12191@0" ObjectIDZND1="15607@x" Pin0InfoVect0LinkObjId="g_26ca490_0" Pin0InfoVect1LinkObjId="SW-69510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69508_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-960 4097,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ff570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-927 4097,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="15605@x" ObjectIDND1="15607@x" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_26ca490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="SW-69510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-927 4097,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ff7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-927 4116,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="15605@x" ObjectIDND1="12191@0" ObjectIDZND0="15607@0" Pin0InfoVect0LinkObjId="SW-69510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="g_26ca490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-927 4116,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ffa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-927 4173,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15607@1" ObjectIDZND0="g_25aa460@0" Pin0InfoVect0LinkObjId="g_25aa460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-927 4173,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2700520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-1033 3954,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="15960@x" ObjectIDND1="15962@x" ObjectIDZND0="g_22133c0@0" ObjectIDZND1="11720@1" ObjectIDZND2="g_22126e0@0" Pin0InfoVect0LinkObjId="g_22133c0_0" Pin0InfoVect1LinkObjId="g_26e14c0_1" Pin0InfoVect2LinkObjId="g_22126e0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70990_0" Pin1InfoVect1LinkObjId="SW-70991_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-1033 3954,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2700780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-1016 3954,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="15960@0" ObjectIDZND0="g_22133c0@0" ObjectIDZND1="11720@1" ObjectIDZND2="g_22126e0@0" Pin0InfoVect0LinkObjId="g_22133c0_0" Pin0InfoVect1LinkObjId="g_26e14c0_1" Pin0InfoVect2LinkObjId="g_22126e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-1016 3954,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2702f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-1033 3891,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2239d10@0" ObjectIDZND0="15962@0" Pin0InfoVect0LinkObjId="SW-70991_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2239d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-1033 3891,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2703170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-1033 3954,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="15962@1" ObjectIDZND0="g_22133c0@0" ObjectIDZND1="11720@1" ObjectIDZND2="g_22126e0@0" Pin0InfoVect0LinkObjId="g_22133c0_0" Pin0InfoVect1LinkObjId="g_26e14c0_1" Pin0InfoVect2LinkObjId="g_22126e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-1033 3954,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2703c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-1014 4429,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="15956@0" ObjectIDZND0="g_2214300@0" ObjectIDZND1="g_2215540@0" ObjectIDZND2="11856@1" Pin0InfoVect0LinkObjId="g_2214300_0" Pin0InfoVect1LinkObjId="g_2215540_0" Pin0InfoVect2LinkObjId="g_26e1e40_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-1014 4429,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2703ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-1037 4429,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="15956@x" ObjectIDND1="15958@x" ObjectIDZND0="g_2214300@0" ObjectIDZND1="g_2215540@0" ObjectIDZND2="11856@1" Pin0InfoVect0LinkObjId="g_2214300_0" Pin0InfoVect1LinkObjId="g_2215540_0" Pin0InfoVect2LinkObjId="g_26e1e40_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70917_0" Pin1InfoVect1LinkObjId="SW-70918_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-1037 4429,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2704120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-1037 4377,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_223a6e0@0" ObjectIDZND0="15958@0" Pin0InfoVect0LinkObjId="SW-70918_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_223a6e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-1037 4377,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2704380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4411,-1037 4429,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="15958@1" ObjectIDZND0="15956@x" ObjectIDZND1="g_2214300@0" ObjectIDZND2="g_2215540@0" Pin0InfoVect0LinkObjId="SW-70917_0" Pin0InfoVect1LinkObjId="g_2214300_0" Pin0InfoVect2LinkObjId="g_2215540_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4411,-1037 4429,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27045e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-773 4511,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="12072@x" ObjectIDND1="12047@x" ObjectIDND2="g_258cbe0@0" ObjectIDZND0="11975@1" Pin0InfoVect0LinkObjId="SW-63432_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63584_0" Pin1InfoVect1LinkObjId="g_26cb9f0_0" Pin1InfoVect2LinkObjId="g_258cbe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-773 4511,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2704840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4476,-773 4452,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11975@0" ObjectIDZND0="g_2232e80@0" Pin0InfoVect0LinkObjId="g_2232e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4476,-773 4452,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2706e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5406,-259 5281,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16027@0" ObjectIDZND0="0@x" ObjectIDZND1="14560@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-68608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD362_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5406,-259 5281,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2707b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5253,-731 5411,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="14556@1" ObjectIDZND0="16025@0" Pin0InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD363_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5253,-731 5411,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2717b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-869 4525,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12071@1" ObjectIDZND0="11974@1" Pin0InfoVect0LinkObjId="SW-63431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-869 4525,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2717da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-823 4525,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11974@0" ObjectIDZND0="12072@1" Pin0InfoVect0LinkObjId="SW-63584_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-823 4525,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2718890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-698 4600,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_258d990@0" ObjectIDZND0="12047@x" ObjectIDZND1="11966@x" Pin0InfoVect0LinkObjId="g_26cb9f0_0" Pin0InfoVect1LinkObjId="SW-63165_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258d990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-698 4600,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2718af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4600,-698 4600,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="12047@x" ObjectIDND1="g_258d990@0" ObjectIDZND0="11966@0" Pin0InfoVect0LinkObjId="SW-63165_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26cb9f0_0" Pin1InfoVect1LinkObjId="g_258d990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4600,-698 4600,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_271cd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5013,-299 4995,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11978@0" ObjectIDZND0="11976@0" Pin0InfoVect0LinkObjId="SW-63455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63457_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5013,-299 4995,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_271cfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-299 4951,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11976@1" ObjectIDZND0="11979@1" Pin0InfoVect0LinkObjId="SW-63458_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-299 4951,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4454,-127 4448,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="12114@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4454,-127 4448,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271fb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-434 3626,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2545880@0" ObjectIDND1="15946@x" ObjectIDND2="g_2544480@0" ObjectIDZND0="18054@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2545880_0" Pin1InfoVect1LinkObjId="SW-70705_0" Pin1InfoVect2LinkObjId="g_2544480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-434 3626,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271fda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3662,-434 3673,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18054@1" ObjectIDZND0="g_2544e20@0" Pin0InfoVect0LinkObjId="g_2544e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3662,-434 3673,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273b660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-202 4737,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20695@1" ObjectIDZND0="g_2732990@0" Pin0InfoVect0LinkObjId="g_2732990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106119_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-202 4737,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-111 4702,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="20696@x" ObjectIDZND0="g_2752e20@0" Pin0InfoVect0LinkObjId="g_2752e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-106120_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-111 4702,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-111 4678,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20696@x" ObjectIDND1="g_2752e20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106120_0" Pin1InfoVect1LinkObjId="g_2752e20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-111 4678,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273bd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-119 4678,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="20696@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2752e20@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2752e20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-119 4678,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274e840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-202 4678,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="20695@0" ObjectIDZND0="g_274f790@0" ObjectIDZND1="20694@x" ObjectIDZND2="g_2751d40@0" Pin0InfoVect0LinkObjId="g_274f790_0" Pin0InfoVect1LinkObjId="SW-106118_0" Pin0InfoVect2LinkObjId="g_2751d40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-202 4678,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274eaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-351 4678,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="20693@0" Pin0InfoVect0LinkObjId="SW-106117_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26d9210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-351 4678,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274ed00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-301 4678,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20693@1" ObjectIDZND0="20692@1" Pin0InfoVect0LinkObjId="SW-106116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106117_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-301 4678,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274ef60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-264 4678,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20692@0" ObjectIDZND0="20694@1" Pin0InfoVect0LinkObjId="SW-106118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-264 4678,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2750500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-202 4650,-211 4678,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_274f790@0" ObjectIDZND0="20694@x" ObjectIDZND1="g_2751d40@0" ObjectIDZND2="20695@x" Pin0InfoVect0LinkObjId="SW-106118_0" Pin0InfoVect1LinkObjId="g_2751d40_0" Pin0InfoVect2LinkObjId="SW-106119_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_274f790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-202 4650,-211 4678,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2750ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-211 4678,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_274f790@0" ObjectIDND1="g_2751d40@0" ObjectIDND2="20695@x" ObjectIDZND0="20694@0" Pin0InfoVect0LinkObjId="SW-106118_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_274f790_0" Pin1InfoVect1LinkObjId="g_2751d40_0" Pin1InfoVect2LinkObjId="SW-106119_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-211 4678,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2751ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-202 4678,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2751d40@0" ObjectIDND1="20695@x" ObjectIDZND0="g_274f790@0" ObjectIDZND1="20694@x" Pin0InfoVect0LinkObjId="g_274f790_0" Pin0InfoVect1LinkObjId="SW-106118_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2751d40_0" Pin1InfoVect1LinkObjId="SW-106119_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-202 4678,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2752960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-155 4678,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20696@0" ObjectIDZND0="g_2751d40@0" Pin0InfoVect0LinkObjId="g_2751d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-155 4678,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2752bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4678,-195 4678,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2751d40@1" ObjectIDZND0="g_274f790@0" ObjectIDZND1="20694@x" ObjectIDZND2="20695@x" Pin0InfoVect0LinkObjId="g_274f790_0" Pin0InfoVect1LinkObjId="SW-106118_0" Pin0InfoVect2LinkObjId="SW-106119_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2751d40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4678,-195 4678,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-202 4863,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20700@1" ObjectIDZND0="g_2753b90@0" Pin0InfoVect0LinkObjId="g_2753b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-202 4863,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-111 4828,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="20701@x" ObjectIDZND0="g_2763c60@0" Pin0InfoVect0LinkObjId="g_2763c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-106127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-111 4828,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275cd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-111 4804,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2763c60@0" ObjectIDND1="20701@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2763c60_0" Pin1InfoVect1LinkObjId="SW-106127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-111 4804,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275cfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-119 4804,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="20701@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2763c60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2763c60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106127_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-119 4804,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2761ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4818,-202 4804,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20700@0" ObjectIDZND0="20699@x" ObjectIDZND1="g_27649d0@0" ObjectIDZND2="g_2762b80@0" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_27649d0_0" Pin0InfoVect2LinkObjId="g_2762b80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106126_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4818,-202 4804,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2761d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-351 4804,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="20698@0" Pin0InfoVect0LinkObjId="SW-106124_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26d9210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-351 4804,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2761fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-301 4804,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20698@1" ObjectIDZND0="20697@1" Pin0InfoVect0LinkObjId="SW-106123_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-301 4804,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2762200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-264 4804,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20697@0" ObjectIDZND0="20699@1" Pin0InfoVect0LinkObjId="SW-106125_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-264 4804,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2762460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-202 4776,-211 4804,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_27649d0@0" ObjectIDZND0="20699@x" ObjectIDZND1="g_2762b80@0" ObjectIDZND2="20700@x" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_2762b80_0" Pin0InfoVect2LinkObjId="SW-106126_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27649d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-202 4776,-211 4804,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27626c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-211 4804,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_27649d0@0" ObjectIDND1="g_2762b80@0" ObjectIDND2="20700@x" ObjectIDZND0="20699@0" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27649d0_0" Pin1InfoVect1LinkObjId="g_2762b80_0" Pin1InfoVect2LinkObjId="SW-106126_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-211 4804,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2762920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-202 4804,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2762b80@0" ObjectIDND1="20700@x" ObjectIDZND0="20699@x" ObjectIDZND1="g_27649d0@0" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_27649d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2762b80_0" Pin1InfoVect1LinkObjId="SW-106126_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-202 4804,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27637a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-155 4804,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20701@0" ObjectIDZND0="g_2762b80@0" Pin0InfoVect0LinkObjId="g_2762b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-155 4804,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2763a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-195 4804,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2762b80@1" ObjectIDZND0="20699@x" ObjectIDZND1="g_27649d0@0" ObjectIDZND2="20700@x" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_27649d0_0" Pin0InfoVect2LinkObjId="SW-106126_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2762b80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-195 4804,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_276d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-84 4432,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="12112@0" ObjectIDZND0="12012@1" Pin0InfoVect0LinkObjId="SW-63491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_2C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-84 4432,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2772e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-148 5096,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="14561@0" Pin0InfoVect0LinkObjId="SW-68609_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26dcd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-148 5096,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27730d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5132,-148 5150,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14561@1" ObjectIDZND0="14531@1" Pin0InfoVect0LinkObjId="SW-68491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5132,-148 5150,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2776030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-259 5095,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="14559@0" Pin0InfoVect0LinkObjId="SW-68607_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26dcd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-259 5095,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2776290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5131,-259 5150,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14559@1" ObjectIDZND0="14530@1" Pin0InfoVect0LinkObjId="SW-68468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68607_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5131,-259 5150,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277aed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-491 5092,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="15954@0" Pin0InfoVect0LinkObjId="SW-70761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26c79d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-491 5092,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277b130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-491 5151,-491 5151,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15954@1" ObjectIDZND0="15948@1" Pin0InfoVect0LinkObjId="SW-70722_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-491 5151,-491 5151,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277b390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-363 5091,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="15949@0" Pin0InfoVect0LinkObjId="SW-70724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26dcd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-363 5091,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277b5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-363 5151,-363 5151,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15949@1" ObjectIDZND0="15948@0" Pin0InfoVect0LinkObjId="SW-70722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-363 5151,-363 5151,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2784430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-580 5097,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="14557@0" Pin0InfoVect0LinkObjId="SW-68605_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26c79d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-580 5097,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2784690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5133,-580 5150,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14557@1" ObjectIDZND0="14529@1" Pin0InfoVect0LinkObjId="SW-68445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5133,-580 5150,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2788670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5360,-193 5371,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2787c00@0" Pin0InfoVect0LinkObjId="g_2787c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5360,-193 5371,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27888d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5285,-147 5285,-194 5315,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="14562@x" ObjectIDND1="22111@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68610_0" Pin1InfoVect1LinkObjId="EC-CX_SB.CX_SB_364LD_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5285,-147 5285,-194 5315,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27893c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5319,-147 5285,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22111@0" ObjectIDZND0="0@x" ObjectIDZND1="14562@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-68610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_364LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5319,-147 5285,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2789620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5253,-147 5285,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="14562@1" ObjectIDZND0="0@x" ObjectIDZND1="22111@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-CX_SB.CX_SB_364LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5253,-147 5285,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_278fcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-893 5266,-924 5272,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="16020@x" ObjectIDND1="0@x" ObjectIDND2="15966@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD361_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-68399_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-893 5266,-924 5272,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2790740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-857 5266,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16020@x" ObjectIDND1="0@x" ObjectIDND2="15966@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2269240@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2269240_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_SB.CX_SB_LD361_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-68399_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-857 5266,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27909a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-893 5280,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="16020@x" ObjectIDND2="0@x" ObjectIDZND0="g_2269240@0" Pin0InfoVect0LinkObjId="g_2269240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="EC-CX_SB.CX_SB_LD361_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-893 5280,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2790df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-276 3937,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14526@1" ObjectIDZND0="14549@1" Pin0InfoVect0LinkObjId="SW-68597_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-276 3937,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2791070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-329 3937,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="14549@0" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_26cc5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68597_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-329 3937,-351 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63860" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4638.000000 -864.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11974"/>
     <cge:Term_Ref ObjectID="16840"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63861" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4638.000000 -864.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11974"/>
     <cge:Term_Ref ObjectID="16840"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63857" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4638.000000 -864.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11974"/>
     <cge:Term_Ref ObjectID="16840"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63879" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4434.000000 29.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12008"/>
     <cge:Term_Ref ObjectID="16862"/>
    <cge:TPSR_Ref TObjectID="12008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63876" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4434.000000 29.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12008"/>
     <cge:Term_Ref ObjectID="16862"/>
    <cge:TPSR_Ref TObjectID="12008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63873" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4547.000000 -40.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11985"/>
     <cge:Term_Ref ObjectID="16856"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63874" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4547.000000 -40.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11985"/>
     <cge:Term_Ref ObjectID="16856"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63869" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4547.000000 -40.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11985"/>
     <cge:Term_Ref ObjectID="16856"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63866" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4974.000000 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11976"/>
     <cge:Term_Ref ObjectID="16850"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63867" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4974.000000 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11976"/>
     <cge:Term_Ref ObjectID="16850"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63863" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4974.000000 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11976"/>
     <cge:Term_Ref ObjectID="16850"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-62940" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -777.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12042"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-62974" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4826.000000 -468.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-62975" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4826.000000 -468.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-62976" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4826.000000 -468.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-62978" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4826.000000 -468.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-62982" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4826.000000 -468.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-62986" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4826.000000 -468.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-64554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5022.000000 -1151.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-64555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5022.000000 -1151.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-64556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5022.000000 -1151.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-64560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5022.000000 -1151.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-64562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5022.000000 -1151.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-64568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5022.000000 -1151.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-64557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -84.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-64558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -84.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-64559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -84.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-64561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -84.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-64565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -84.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-64569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -84.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-69494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4105.000000 -716.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15617"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-69485" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -860.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15608"/>
     <cge:Term_Ref ObjectID="23024"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-69486" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -860.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15608"/>
     <cge:Term_Ref ObjectID="23024"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-69482" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -860.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15608"/>
     <cge:Term_Ref ObjectID="23024"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-69491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4976.000000 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15613"/>
     <cge:Term_Ref ObjectID="23034"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-69492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4976.000000 -511.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15613"/>
     <cge:Term_Ref ObjectID="23034"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-69488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4976.000000 -511.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15613"/>
     <cge:Term_Ref ObjectID="23034"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-69498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4170.000000 -452.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15614"/>
     <cge:Term_Ref ObjectID="23036"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-69499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4170.000000 -452.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15614"/>
     <cge:Term_Ref ObjectID="23036"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-69495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4170.000000 -452.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15614"/>
     <cge:Term_Ref ObjectID="23036"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-64540" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4634.000000 -991.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-64541" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4634.000000 -991.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-64542" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4634.000000 -991.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-64546" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4634.000000 -991.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-64552" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4634.000000 -991.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14527"/>
     <cge:Term_Ref ObjectID="21067"/>
    <cge:TPSR_Ref TObjectID="14527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14527"/>
     <cge:Term_Ref ObjectID="21067"/>
    <cge:TPSR_Ref TObjectID="14527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -38.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14524"/>
     <cge:Term_Ref ObjectID="21061"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -38.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14524"/>
     <cge:Term_Ref ObjectID="21061"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -38.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14524"/>
     <cge:Term_Ref ObjectID="21061"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -343.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14530"/>
     <cge:Term_Ref ObjectID="21073"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -343.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14530"/>
     <cge:Term_Ref ObjectID="21073"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -343.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14530"/>
     <cge:Term_Ref ObjectID="21073"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4318.000000 -266.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15963"/>
     <cge:Term_Ref ObjectID="14505"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4318.000000 -266.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15963"/>
     <cge:Term_Ref ObjectID="14505"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70669" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4318.000000 -266.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15963"/>
     <cge:Term_Ref ObjectID="14505"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68536" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3818.000000 -37.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14525"/>
     <cge:Term_Ref ObjectID="21063"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68537" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3818.000000 -37.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14525"/>
     <cge:Term_Ref ObjectID="21063"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68532" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3818.000000 -37.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14525"/>
     <cge:Term_Ref ObjectID="21063"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68552" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5168.000000 -933.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14533"/>
     <cge:Term_Ref ObjectID="21077"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68553" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5168.000000 -933.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14533"/>
     <cge:Term_Ref ObjectID="21077"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68548" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5168.000000 -933.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14533"/>
     <cge:Term_Ref ObjectID="21077"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68566" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5170.000000 -658.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14529"/>
     <cge:Term_Ref ObjectID="21071"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68567" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5170.000000 -658.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14529"/>
     <cge:Term_Ref ObjectID="21071"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68562" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5170.000000 -658.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14529"/>
     <cge:Term_Ref ObjectID="21071"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70692" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15955"/>
     <cge:Term_Ref ObjectID="23517"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70693" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15955"/>
     <cge:Term_Ref ObjectID="23517"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70687" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15955"/>
     <cge:Term_Ref ObjectID="23517"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70700" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3890.000000 -970.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15959"/>
     <cge:Term_Ref ObjectID="23525"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70701" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3890.000000 -970.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15959"/>
     <cge:Term_Ref ObjectID="23525"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70695" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3890.000000 -970.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15959"/>
     <cge:Term_Ref ObjectID="23525"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70668" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5252.000000 -433.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15948"/>
     <cge:Term_Ref ObjectID="23503"/>
    <cge:TPSR_Ref TObjectID="15948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68515" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14522"/>
     <cge:Term_Ref ObjectID="21057"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68516" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14522"/>
     <cge:Term_Ref ObjectID="21057"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68511" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14522"/>
     <cge:Term_Ref ObjectID="21057"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68522" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3600.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14523"/>
     <cge:Term_Ref ObjectID="21059"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68523" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3600.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14523"/>
     <cge:Term_Ref ObjectID="21059"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68518" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3600.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14523"/>
     <cge:Term_Ref ObjectID="21059"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68559" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5171.000000 -808.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14528"/>
     <cge:Term_Ref ObjectID="21069"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68560" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5171.000000 -808.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14528"/>
     <cge:Term_Ref ObjectID="21069"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68555" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5171.000000 -808.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14528"/>
     <cge:Term_Ref ObjectID="21069"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63852" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4657.000000 -452.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11963"/>
     <cge:Term_Ref ObjectID="16832"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63853" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4657.000000 -452.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11963"/>
     <cge:Term_Ref ObjectID="16832"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63849" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4657.000000 -452.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11963"/>
     <cge:Term_Ref ObjectID="16832"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68541" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -11.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14526"/>
     <cge:Term_Ref ObjectID="21065"/>
    <cge:TPSR_Ref TObjectID="14526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68538" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -11.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14526"/>
     <cge:Term_Ref ObjectID="21065"/>
    <cge:TPSR_Ref TObjectID="14526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-62971" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -470.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-62972" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -470.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-62973" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -470.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-62977" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -470.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-62979" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -470.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-62985" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -470.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4668.000000 -37.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20692"/>
     <cge:Term_Ref ObjectID="17280"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4668.000000 -37.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20692"/>
     <cge:Term_Ref ObjectID="17280"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4668.000000 -37.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20692"/>
     <cge:Term_Ref ObjectID="17280"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4794.000000 -37.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20697"/>
     <cge:Term_Ref ObjectID="28744"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4794.000000 -37.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20697"/>
     <cge:Term_Ref ObjectID="28744"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4794.000000 -37.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20697"/>
     <cge:Term_Ref ObjectID="28744"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5166.000000 -225.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14531"/>
     <cge:Term_Ref ObjectID="21075"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5166.000000 -225.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14531"/>
     <cge:Term_Ref ObjectID="21075"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5166.000000 -225.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14531"/>
     <cge:Term_Ref ObjectID="21075"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="sb_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3237" y="-1178"/></g>
   <g href="sb_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3188" y="-1195"/></g>
   <g href="110kV双柏变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4021" y="-750"/></g>
   <g href="110kV双柏变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4584" y="-750"/></g>
   <g href="110kV双柏变110kV谢烟双线156间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3963" y="-977"/></g>
   <g href="110kV双柏变110kV双鄂大线154间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4438" y="-972"/></g>
   <g href="110kV双柏变10kV瓦波里线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3518" y="-264"/></g>
   <g href="110kV双柏变10kV花箐线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3624" y="-264"/></g>
   <g href="110kV双柏变10kV城区Ⅰ回线06线间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3733" y="-263"/></g>
   <g href="110kV双柏变10kV朝阳线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3839" y="-265"/></g>
   <g href="110kV双柏变10kV1号电容器065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3946" y="-270"/></g>
   <g href="110kV双柏变10kV2号电容器071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4441" y="-265"/></g>
   <g href="110kV双柏变10kV玉楚柏线072间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4566" y="-265"/></g>
   <g href="110kV双柏变10kV和平线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4687" y="-285"/></g>
   <g href="110kV双柏变10kV环东线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4813" y="-285"/></g>
   <g href="110kV双柏变35kV双妥大线364间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5152" y="-172"/></g>
   <g href="110kV双柏变35kV鱼庄河线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5151" y="-284"/></g>
   <g href="110kV双柏变35kV分段断路器312间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="5160" y="-434"/></g>
   <g href="110kV双柏变10kV分段备自投012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4299" y="-304"/></g>
   <g href="110kV双柏变35kV双小妥线365间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5152" y="-605"/></g>
   <g href="110kV双柏变35kV雨龙线363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5148" y="-756"/></g>
   <g href="110kV双柏变35kV爱尼山线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5149" y="-882"/></g>
   <g href="110kV双柏变10kV3号电容器066间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4073" y="-266"/></g>
   <g href="110kV双柏变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="88" x="3141" y="-588"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c38a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3837.000000 970.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c4bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 955.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c57c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 940.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c6d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 959.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c7060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.000000 944.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c72a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 929.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c76c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 862.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c7980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 847.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c7bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 832.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c7fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 862.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c82a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4568.000000 847.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c84e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 832.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c8900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 510.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c8bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 495.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c8e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 480.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c9220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 280.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c94e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 265.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c9720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 250.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c9b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 343.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c9e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 328.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5128.000000 313.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 658.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 643.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5128.000000 628.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cad80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 807.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cb040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 792.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cb280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5128.000000 777.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cb6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 934.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cb960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 919.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cbba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5128.000000 904.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cbfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3436.000000 43.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cc280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3425.000000 28.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cc4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3450.000000 13.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cc8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.000000 266.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ccba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4249.000000 251.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ccde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 236.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cd110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3884.000000 -4.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cd370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.000000 11.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cd6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -44.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cd900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -29.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25db3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 452.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25db6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 437.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25db8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 422.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2718fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 961.375000) translate(0,12)">Uc(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2719be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 946.250000) translate(0,12)">Uab(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271a100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 991.625000) translate(0,12)">Ua(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271a340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 976.500000) translate(0,12)">Ub(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271a580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.000000 931.250000) translate(0,12)">F(Hz):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27697d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 438.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2769a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4772.000000 423.375000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2769c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 392.250000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2769ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 453.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276a0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 468.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276a320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 408.250000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276a650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3447.000000 440.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276a8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3453.000000 425.375000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276ab10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3455.000000 394.250000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276ad50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3447.000000 455.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276af90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3447.000000 470.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276b1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3439.000000 410.250000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276b500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 55.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276b780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 25.250000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276b9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 85.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276bc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 70.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276be40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4971.000000 9.250000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276c080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.000000 40.375000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276c3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 1123.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276c630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.000000 1093.250000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276c870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 1153.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276cab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 1138.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276ccf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4974.000000 1077.250000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276cf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.000000 1108.375000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276e1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5106.000000 223.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276e490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5095.000000 208.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276e6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5120.000000 193.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieshuangTbai_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3954,-1162 3954,-1133 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11720" ObjectName="AC-110kV.xieshuangTbai_line"/>
    <cge:TPSR_Ref TObjectID="11720_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="3954,-1162 3954,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.LNShuangEDaTSB" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4429,-1157 4429,-1130 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11856" ObjectName="AC-110kV.LNShuangEDaTSB"/>
    <cge:TPSR_Ref TObjectID="11856_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="4429,-1157 4429,-1130 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-69816">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4248.000000 -600.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15647" ObjectName="SW-CX_SB.CX_SB_3000SW"/>
     <cge:Meas_Ref ObjectId="69816"/>
    <cge:TPSR_Ref TObjectID="15647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64539">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4061.000000 -600.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12190" ObjectName="SW-CX_SB.CX_SB_3010SW"/>
     <cge:Meas_Ref ObjectId="64539"/>
    <cge:TPSR_Ref TObjectID="12190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64538">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4403.000000 -600.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12188" ObjectName="SW-CX_SB.CX_SB_3020SW"/>
     <cge:Meas_Ref ObjectId="64538"/>
    <cge:TPSR_Ref TObjectID="12188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70748">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5089.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15952" ObjectName="SW-CX_SB.CX_SB_3901SW"/>
     <cge:Meas_Ref ObjectId="70748"/>
    <cge:TPSR_Ref TObjectID="15952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5212.000000 -254.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14560" ObjectName="SW-CX_SB.CX_SB_3626SW"/>
     <cge:Meas_Ref ObjectId="68608"/>
    <cge:TPSR_Ref TObjectID="14560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5212.000000 -575.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14558" ObjectName="SW-CX_SB.CX_SB_3656SW"/>
     <cge:Meas_Ref ObjectId="68606"/>
    <cge:TPSR_Ref TObjectID="14558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5215.000000 -852.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15966" ObjectName="SW-CX_SB.CX_SB_3616SW"/>
     <cge:Meas_Ref ObjectId="68399"/>
    <cge:TPSR_Ref TObjectID="15966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5212.000000 -726.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14556" ObjectName="SW-CX_SB.CX_SB_3636SW"/>
     <cge:Meas_Ref ObjectId="68604"/>
    <cge:TPSR_Ref TObjectID="14556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70990">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 -992.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15960" ObjectName="SW-CX_SB.CX_SB_156XC"/>
     <cge:Meas_Ref ObjectId="70990"/>
    <cge:TPSR_Ref TObjectID="15960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70917">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -913.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15957" ObjectName="SW-CX_SB.CX_SB_154XC1"/>
     <cge:Meas_Ref ObjectId="70917"/>
    <cge:TPSR_Ref TObjectID="15957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70917">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -990.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15956" ObjectName="SW-CX_SB.CX_SB_154XC"/>
     <cge:Meas_Ref ObjectId="70917"/>
    <cge:TPSR_Ref TObjectID="15956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70918">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4372.000000 -1033.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15958" ObjectName="SW-CX_SB.CX_SB_15467SW"/>
     <cge:Meas_Ref ObjectId="70918"/>
    <cge:TPSR_Ref TObjectID="15958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69737">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 3910.000000 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15609" ObjectName="SW-CX_SB.CX_SB_10167SW"/>
     <cge:Meas_Ref ObjectId="69737"/>
    <cge:TPSR_Ref TObjectID="15609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63432">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.978261 -0.000000 0.000000 -0.857143 4471.000000 -769.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11975" ObjectName="SW-CX_SB.CX_SB_10267SW"/>
     <cge:Meas_Ref ObjectId="63432"/>
    <cge:TPSR_Ref TObjectID="11975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69749">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15610" ObjectName="SW-CX_SB.CX_SB_1010SW"/>
     <cge:Meas_Ref ObjectId="69749"/>
    <cge:TPSR_Ref TObjectID="15610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63168">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.000000 -682.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11969" ObjectName="SW-CX_SB.CX_SB_1020SW"/>
     <cge:Meas_Ref ObjectId="63168"/>
    <cge:TPSR_Ref TObjectID="11969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12033" ObjectName="SW-CX_SB.CX_SB_0626SW"/>
     <cge:Meas_Ref ObjectId="63512"/>
    <cge:TPSR_Ref TObjectID="12033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3715.000000 -143.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12034" ObjectName="SW-CX_SB.CX_SB_0636SW"/>
     <cge:Meas_Ref ObjectId="63513"/>
    <cge:TPSR_Ref TObjectID="12034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12035" ObjectName="SW-CX_SB.CX_SB_0646SW"/>
     <cge:Meas_Ref ObjectId="63514"/>
    <cge:TPSR_Ref TObjectID="12035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68589">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3500.000000 -285.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14541" ObjectName="SW-CX_SB.CX_SB_0611SW"/>
     <cge:Meas_Ref ObjectId="68589"/>
    <cge:TPSR_Ref TObjectID="14541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14543" ObjectName="SW-CX_SB.CX_SB_0621SW"/>
     <cge:Meas_Ref ObjectId="68591"/>
    <cge:TPSR_Ref TObjectID="14543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68593">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3715.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14545" ObjectName="SW-CX_SB.CX_SB_0631SW"/>
     <cge:Meas_Ref ObjectId="68593"/>
    <cge:TPSR_Ref TObjectID="14545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68595">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14547" ObjectName="SW-CX_SB.CX_SB_0641SW"/>
     <cge:Meas_Ref ObjectId="68595"/>
    <cge:TPSR_Ref TObjectID="14547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -146.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14551" ObjectName="SW-CX_SB.CX_SB_0656SW"/>
     <cge:Meas_Ref ObjectId="68599"/>
    <cge:TPSR_Ref TObjectID="14551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68597">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14549" ObjectName="SW-CX_SB.CX_SB_0651SW"/>
     <cge:Meas_Ref ObjectId="68597"/>
    <cge:TPSR_Ref TObjectID="14549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68602">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14554" ObjectName="SW-CX_SB.CX_SB_0666SW"/>
     <cge:Meas_Ref ObjectId="68602"/>
    <cge:TPSR_Ref TObjectID="14554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68600">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14552" ObjectName="SW-CX_SB.CX_SB_0661SW"/>
     <cge:Meas_Ref ObjectId="68600"/>
    <cge:TPSR_Ref TObjectID="14552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15946" ObjectName="SW-CX_SB.CX_SB_0901SW"/>
     <cge:Meas_Ref ObjectId="70705"/>
    <cge:TPSR_Ref TObjectID="15946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15947" ObjectName="SW-CX_SB.CX_SB_0671SW"/>
     <cge:Meas_Ref ObjectId="70711"/>
    <cge:TPSR_Ref TObjectID="15947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69736">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -784.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15633" ObjectName="SW-CX_SB.CX_SB_101XC1"/>
     <cge:Meas_Ref ObjectId="69736"/>
    <cge:TPSR_Ref TObjectID="15633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69736">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -866.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15632" ObjectName="SW-CX_SB.CX_SB_101XC"/>
     <cge:Meas_Ref ObjectId="69736"/>
    <cge:TPSR_Ref TObjectID="15632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63584">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4515.121739 -862.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12071" ObjectName="SW-CX_SB.CX_SB_102XC"/>
     <cge:Meas_Ref ObjectId="63584"/>
    <cge:TPSR_Ref TObjectID="12071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63584">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4515.121739 -784.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12072" ObjectName="SW-CX_SB.CX_SB_102XC1"/>
     <cge:Meas_Ref ObjectId="63584"/>
    <cge:TPSR_Ref TObjectID="12072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15616" ObjectName="SW-CX_SB.CX_SB_0016SW"/>
     <cge:Meas_Ref ObjectId="69769"/>
    <cge:TPSR_Ref TObjectID="15616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15615" ObjectName="SW-CX_SB.CX_SB_0011SW"/>
     <cge:Meas_Ref ObjectId="69768"/>
    <cge:TPSR_Ref TObjectID="15615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63507">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12028" ObjectName="SW-CX_SB.CX_SB_0726SW"/>
     <cge:Meas_Ref ObjectId="63507"/>
    <cge:TPSR_Ref TObjectID="12028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -281.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11986" ObjectName="SW-CX_SB.CX_SB_0722SW"/>
     <cge:Meas_Ref ObjectId="63465"/>
    <cge:TPSR_Ref TObjectID="11986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12012" ObjectName="SW-CX_SB.CX_SB_0716SW"/>
     <cge:Meas_Ref ObjectId="63491"/>
    <cge:TPSR_Ref TObjectID="12012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12010" ObjectName="SW-CX_SB.CX_SB_0712SW"/>
     <cge:Meas_Ref ObjectId="63489"/>
    <cge:TPSR_Ref TObjectID="12010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15951" ObjectName="SW-CX_SB.CX_SB_0121SW"/>
     <cge:Meas_Ref ObjectId="70736"/>
    <cge:TPSR_Ref TObjectID="15951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63515">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4362.000000 -293.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12036" ObjectName="SW-CX_SB.CX_SB_0122SW"/>
     <cge:Meas_Ref ObjectId="63515"/>
    <cge:TPSR_Ref TObjectID="12036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4591.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11966" ObjectName="SW-CX_SB.CX_SB_0026SW"/>
     <cge:Meas_Ref ObjectId="63165"/>
    <cge:TPSR_Ref TObjectID="11966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4591.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11965" ObjectName="SW-CX_SB.CX_SB_0022SW"/>
     <cge:Meas_Ref ObjectId="63164"/>
    <cge:TPSR_Ref TObjectID="11965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4468.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12027" ObjectName="SW-CX_SB.CX_SB_0902SW"/>
     <cge:Meas_Ref ObjectId="63506"/>
    <cge:TPSR_Ref TObjectID="12027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5270.000000 -825.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5304.000000 -622.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5306.000000 -301.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70754">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5088.000000 -57.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15953" ObjectName="SW-CX_SB.CX_SB_3902SW"/>
     <cge:Meas_Ref ObjectId="70754"/>
    <cge:TPSR_Ref TObjectID="15953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69508">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -955.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15605" ObjectName="SW-CX_SB.CX_SB_1901SW"/>
     <cge:Meas_Ref ObjectId="69508"/>
    <cge:TPSR_Ref TObjectID="15605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3500.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12032" ObjectName="SW-CX_SB.CX_SB_0616SW"/>
     <cge:Meas_Ref ObjectId="63511"/>
    <cge:TPSR_Ref TObjectID="12032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5268.000000 -920.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.928571 5091.000000 -1073.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70990">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 -916.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15961" ObjectName="SW-CX_SB.CX_SB_156XC1"/>
     <cge:Meas_Ref ObjectId="70990"/>
    <cge:TPSR_Ref TObjectID="15961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5212.000000 -142.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14562" ObjectName="SW-CX_SB.CX_SB_3646SW"/>
     <cge:Meas_Ref ObjectId="68610"/>
    <cge:TPSR_Ref TObjectID="14562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12011" ObjectName="SW-CX_SB.CX_SB_07127SW"/>
     <cge:Meas_Ref ObjectId="63490"/>
    <cge:TPSR_Ref TObjectID="12011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11987" ObjectName="SW-CX_SB.CX_SB_07227SW"/>
     <cge:Meas_Ref ObjectId="63466"/>
    <cge:TPSR_Ref TObjectID="11987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63458">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 -294.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11979" ObjectName="SW-CX_SB.CX_SB_3026SW"/>
     <cge:Meas_Ref ObjectId="63458"/>
    <cge:TPSR_Ref TObjectID="11979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63457">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5008.000000 -294.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11978" ObjectName="SW-CX_SB.CX_SB_3022SW"/>
     <cge:Meas_Ref ObjectId="63457"/>
    <cge:TPSR_Ref TObjectID="11978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69758">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 -519.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15612" ObjectName="SW-CX_SB.CX_SB_3016SW"/>
     <cge:Meas_Ref ObjectId="69758"/>
    <cge:TPSR_Ref TObjectID="15612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69757">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -519.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15611" ObjectName="SW-CX_SB.CX_SB_3011SW"/>
     <cge:Meas_Ref ObjectId="69757"/>
    <cge:TPSR_Ref TObjectID="15611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68592">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.000000 -221.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14544" ObjectName="SW-CX_SB.CX_SB_06217SW"/>
     <cge:Meas_Ref ObjectId="68592"/>
    <cge:TPSR_Ref TObjectID="14544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68594">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 -220.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14546" ObjectName="SW-CX_SB.CX_SB_06317SW"/>
     <cge:Meas_Ref ObjectId="68594"/>
    <cge:TPSR_Ref TObjectID="14546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68596">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 -222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14548" ObjectName="SW-CX_SB.CX_SB_06417SW"/>
     <cge:Meas_Ref ObjectId="68596"/>
    <cge:TPSR_Ref TObjectID="14548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -227.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14550" ObjectName="SW-CX_SB.CX_SB_06517SW"/>
     <cge:Meas_Ref ObjectId="68598"/>
    <cge:TPSR_Ref TObjectID="14550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68601">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -223.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14553" ObjectName="SW-CX_SB.CX_SB_06617SW"/>
     <cge:Meas_Ref ObjectId="68601"/>
    <cge:TPSR_Ref TObjectID="14553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68590">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 -221.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14542" ObjectName="SW-CX_SB.CX_SB_06117SW"/>
     <cge:Meas_Ref ObjectId="68590"/>
    <cge:TPSR_Ref TObjectID="14542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -135.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12030" ObjectName="SW-CX_SB.CX_SB_06567SW"/>
     <cge:Meas_Ref ObjectId="63509"/>
    <cge:TPSR_Ref TObjectID="12030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -131.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12031" ObjectName="SW-CX_SB.CX_SB_06667SW"/>
     <cge:Meas_Ref ObjectId="63510"/>
    <cge:TPSR_Ref TObjectID="12031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69509">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15606" ObjectName="SW-CX_SB.CX_SB_19017SW"/>
     <cge:Meas_Ref ObjectId="69509"/>
    <cge:TPSR_Ref TObjectID="15606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69510">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -922.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15607" ObjectName="SW-CX_SB.CX_SB_19010SW"/>
     <cge:Meas_Ref ObjectId="69510"/>
    <cge:TPSR_Ref TObjectID="15607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70991">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -1028.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15962" ObjectName="SW-CX_SB.CX_SB_15667SW"/>
     <cge:Meas_Ref ObjectId="70991"/>
    <cge:TPSR_Ref TObjectID="15962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -91.000000)" xlink:href="#switch2:shape24_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12114" ObjectName="SW-CX_SB.CX_SB_07167SW"/>
     <cge:Meas_Ref ObjectId="64506"/>
    <cge:TPSR_Ref TObjectID="12114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64576">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.804348 -0.000000 0.000000 -1.000000 4496.000000 -423.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12196" ObjectName="SW-CX_SB.CX_SB_09027SW"/>
     <cge:Meas_Ref ObjectId="64576"/>
    <cge:TPSR_Ref TObjectID="12196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 -429.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18054" ObjectName="SW-CX_SB.CX_SB_09017SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="18054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20696" ObjectName="SW-CX_SB.CX_SB_0736SW"/>
     <cge:Meas_Ref ObjectId="106120"/>
    <cge:TPSR_Ref TObjectID="20696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20693" ObjectName="SW-CX_SB.CX_SB_0732SW"/>
     <cge:Meas_Ref ObjectId="106117"/>
    <cge:TPSR_Ref TObjectID="20693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20695" ObjectName="SW-CX_SB.CX_SB_07337SW"/>
     <cge:Meas_Ref ObjectId="106119"/>
    <cge:TPSR_Ref TObjectID="20695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20694" ObjectName="SW-CX_SB.CX_SB_0733SW"/>
     <cge:Meas_Ref ObjectId="106118"/>
    <cge:TPSR_Ref TObjectID="20694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20701" ObjectName="SW-CX_SB.CX_SB_0746SW"/>
     <cge:Meas_Ref ObjectId="106127"/>
    <cge:TPSR_Ref TObjectID="20701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20698" ObjectName="SW-CX_SB.CX_SB_0742SW"/>
     <cge:Meas_Ref ObjectId="106124"/>
    <cge:TPSR_Ref TObjectID="20698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20700" ObjectName="SW-CX_SB.CX_SB_07437SW"/>
     <cge:Meas_Ref ObjectId="106126"/>
    <cge:TPSR_Ref TObjectID="20700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -212.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20699" ObjectName="SW-CX_SB.CX_SB_0743SW"/>
     <cge:Meas_Ref ObjectId="106125"/>
    <cge:TPSR_Ref TObjectID="20699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5091.000000 -143.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14561" ObjectName="SW-CX_SB.CX_SB_3642SW"/>
     <cge:Meas_Ref ObjectId="68609"/>
    <cge:TPSR_Ref TObjectID="14561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5090.000000 -254.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14559" ObjectName="SW-CX_SB.CX_SB_3622SW"/>
     <cge:Meas_Ref ObjectId="68607"/>
    <cge:TPSR_Ref TObjectID="14559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5086.000000 -358.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15949" ObjectName="SW-CX_SB.CX_SB_3122SW"/>
     <cge:Meas_Ref ObjectId="70724"/>
    <cge:TPSR_Ref TObjectID="15949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5087.000000 -486.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15954" ObjectName="SW-CX_SB.CX_SB_3121SW"/>
     <cge:Meas_Ref ObjectId="70761"/>
    <cge:TPSR_Ref TObjectID="15954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 -575.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14557" ObjectName="SW-CX_SB.CX_SB_3651SW"/>
     <cge:Meas_Ref ObjectId="68605"/>
    <cge:TPSR_Ref TObjectID="14557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5088.000000 -726.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14555" ObjectName="SW-CX_SB.CX_SB_3631SW"/>
     <cge:Meas_Ref ObjectId="68603"/>
    <cge:TPSR_Ref TObjectID="14555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5085.000000 -853.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15965" ObjectName="SW-CX_SB.CX_SB_3611SW"/>
     <cge:Meas_Ref ObjectId="68398"/>
    <cge:TPSR_Ref TObjectID="15965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5311.000000 -190.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3237" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3237" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3188" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3188" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4021" y="-750"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4021" y="-750"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4584" y="-750"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4584" y="-750"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3963" y="-977"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3963" y="-977"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4438" y="-972"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4438" y="-972"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3518" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3518" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3624" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3624" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3733" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3733" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3839" y="-265"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3839" y="-265"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3946" y="-270"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3946" y="-270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4441" y="-265"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4441" y="-265"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4566" y="-265"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4566" y="-265"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4687" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4687" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4813" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4813" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5152" y="-172"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5152" y="-172"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5151" y="-284"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5151" y="-284"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="5160" y="-434"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="5160" y="-434"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4299" y="-304"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4299" y="-304"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5152" y="-605"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5152" y="-605"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5148" y="-756"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5148" y="-756"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5149" y="-882"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5149" y="-882"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4073" y="-266"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4073" y="-266"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="88" x="3141" y="-588"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="88" x="3141" y="-588"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2239d10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 -1024.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223a6e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -1027.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21fd230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.000000 -764.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2232e80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4426.121739 -763.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2246140" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3863.000000 -677.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c8c70" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4425.000000 -676.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21e0bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3891.000000 -233.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23857d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3785.000000 -231.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2389430" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3676.000000 -232.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_238d090" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3570.000000 -232.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_239cc10" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3998.000000 -238.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2533d20" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4007.000000 -146.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25373e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4125.000000 -234.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_253d410" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4134.000000 -142.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2544e20" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3668.000000 -440.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2566470" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4618.724638 -233.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256e220" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4493.852174 -233.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2588ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4546.457971 -434.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a99d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4167.000000 -1018.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25aa460" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4168.000000 -933.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2732990" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4732.000000 -208.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2753b90" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4858.000000 -208.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SB"/>
</svg>