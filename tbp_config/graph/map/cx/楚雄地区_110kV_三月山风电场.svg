<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-188" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1220 1573 1282">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape131">
    <ellipse cx="32" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="8" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="32" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="16" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="14" y2="16"/>
    <ellipse cx="21" cy="15" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="43" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="32" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="43" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="3" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="41" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="46" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="41" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="18" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape53">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="5" x2="5" y1="13" y2="4"/>
    <polyline arcFlag="1" points="5,13 6,13 6,13 7,13 8,13 8,14 9,14 10,15 10,15 10,16 11,17 11,17 11,18 11,19 11,20 11,20 10,21 10,22 10,22 9,23 8,23 8,24 7,24 6,24 6,24 5,24 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="5,24 6,24 6,24 7,24 8,24 8,25 9,25 10,26 10,26 10,27 11,28 11,28 11,29 11,30 11,31 11,31 10,32 10,33 10,33 9,34 8,34 8,35 7,35 6,35 6,35 5,35 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="5,35 6,35 6,35 7,35 8,35 8,36 9,36 10,37 10,37 10,38 11,39 11,39 11,40 11,41 11,42 11,42 10,43 10,44 10,44 9,45 8,45 8,46 7,46 6,46 6,46 5,46 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="5" x2="5" y1="55" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_1e97760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape63_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="61" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="63" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="65" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="60" y1="23" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="26" y1="81" y2="81"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,56 60,56 60,27 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="42" y2="0"/>
    <circle cx="26" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="21" y1="56" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="56" y2="61"/>
    <polyline DF8003:Layer="PUBLIC" points="26,10 20,23 33,23 26,10 26,11 26,10 "/>
   </symbol>
   <symbol id="transformer2:shape63_1">
    <circle cx="26" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="85" y2="87"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="30,85 32,83 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,76 23,76 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="22" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="30" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="81" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d83420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d84580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d84f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d85c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d86e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d87a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d884e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d88dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d8a5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d8a5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8bdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8bdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1d8dc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8f8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d90500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d913e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d91cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d93480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d93c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d94370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d94b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d95be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d965c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d970b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d97a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d99010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d99ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d9ab00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d9b740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1da9f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d9d030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d9e090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d9f5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1292" width="1583" x="3112" y="-1225"/>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-606 3920,-606 3920,-628 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="3920" x2="3920" y1="-643" y2="-647"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="3920" x2="3920" y1="-685" y2="-664"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-653 3915,-664 3926,-664 3920,-653 3920,-654 3920,-653 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-639 3915,-628 3926,-628 3920,-639 3920,-638 3920,-639 " stroke="rgb(170,85,127)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-156818">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26418" ObjectName="SW-CX_SYS.CX_SYS_301BK"/>
     <cge:Meas_Ref ObjectId="156818"/>
    <cge:TPSR_Ref TObjectID="26418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156822">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3559.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26422" ObjectName="SW-CX_SYS.CX_SYS_361BK"/>
     <cge:Meas_Ref ObjectId="156822"/>
    <cge:TPSR_Ref TObjectID="26422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156806">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4214.000000 -965.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26406" ObjectName="SW-CX_SYS.CX_SYS_161BK"/>
     <cge:Meas_Ref ObjectId="156806"/>
    <cge:TPSR_Ref TObjectID="26406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156841">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4327.000000 -323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26441" ObjectName="SW-CX_SYS.CX_SYS_364BK"/>
     <cge:Meas_Ref ObjectId="156841"/>
    <cge:TPSR_Ref TObjectID="26441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156827">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3783.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26427" ObjectName="SW-CX_SYS.CX_SYS_362SBK"/>
     <cge:Meas_Ref ObjectId="156827"/>
    <cge:TPSR_Ref TObjectID="26427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156837">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4007.000000 -324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26437" ObjectName="SW-CX_SYS.CX_SYS_363BK"/>
     <cge:Meas_Ref ObjectId="156837"/>
    <cge:TPSR_Ref TObjectID="26437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156832">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4567.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26432" ObjectName="SW-CX_SYS.CX_SYS_365BK"/>
     <cge:Meas_Ref ObjectId="156832"/>
    <cge:TPSR_Ref TObjectID="26432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156812">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -773.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26412" ObjectName="SW-CX_SYS.CX_SYS_101BK"/>
     <cge:Meas_Ref ObjectId="156812"/>
    <cge:TPSR_Ref TObjectID="26412"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1e0fcc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3532.000000 -604.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SYS.CX_SYS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3426,-421 4690,-421 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26404" ObjectName="BS-CX_SYS.CX_SYS_3IM"/>
    <cge:TPSR_Ref TObjectID="26404"/></metadata>
   <polyline fill="none" opacity="0" points="3426,-421 4690,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SYS.CX_SYS_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-881 4647,-881 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26447" ObjectName="BS-CX_SYS.CX_SYS_1IIM"/>
    <cge:TPSR_Ref TObjectID="26447"/></metadata>
   <polyline fill="none" opacity="0" points="3631,-881 4647,-881 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -82.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b4ee00" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3940.500000 -493.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0aba0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3509.500000 -325.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3f120" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3508.500000 -206.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e24a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4163.500000 -1093.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d634a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4164.500000 -964.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d25750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 -566.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d77970" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4447.500000 -909.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d72340" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4447.500000 -985.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dbfd50" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4165.500000 -1026.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d69e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3504.000000 -624.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e76d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3476.500000 -518.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2113800" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4277.500000 -193.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e20ba0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4277.500000 -322.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2216960" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3733.500000 -326.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2219a30" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3732.500000 -207.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2228af0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3957.500000 -323.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222bd70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3956.500000 -204.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e957e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 -49.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea02f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4517.500000 -322.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea32f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4516.500000 -207.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ec4f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -815.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ec59d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -765.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ec6460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -710.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1d2e050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-623 4000,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="26449@1" ObjectIDZND0="26420@1" Pin0InfoVect0LinkObjId="SW-156820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d25520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-623 4000,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfa0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-483 3981,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26418@x" ObjectIDND1="26419@x" ObjectIDZND0="26421@1" Pin0InfoVect0LinkObjId="SW-156821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156818_0" Pin1InfoVect1LinkObjId="SW-156819_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-483 3981,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2ddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-483 3935,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26421@0" ObjectIDZND0="g_1b4ee00@0" Pin0InfoVect0LinkObjId="g_1b4ee00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-483 3935,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1de7db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-472 4000,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26419@1" ObjectIDZND0="26418@x" ObjectIDZND1="26421@x" Pin0InfoVect0LinkObjId="SW-156818_0" Pin0InfoVect1LinkObjId="SW-156821_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156819_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-472 4000,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16fb270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-483 4000,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26419@x" ObjectIDND1="26421@x" ObjectIDZND0="26418@0" Pin0InfoVect0LinkObjId="SW-156818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156819_0" Pin1InfoVect1LinkObjId="SW-156821_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-483 4000,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e90e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-421 4000,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26404@0" ObjectIDZND0="26419@0" Pin0InfoVect0LinkObjId="SW-156819_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-421 4000,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d14fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-421 3568,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26404@0" ObjectIDZND0="26423@1" Pin0InfoVect0LinkObjId="SW-156823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-421 3568,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d51e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-336 3550,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26422@x" ObjectIDND1="26423@x" ObjectIDZND0="26426@1" Pin0InfoVect0LinkObjId="SW-156826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156822_0" Pin1InfoVect1LinkObjId="SW-156823_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-336 3550,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf73a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3514,-336 3504,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26426@0" ObjectIDZND0="g_1e0aba0@0" Pin0InfoVect0LinkObjId="g_1e0aba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3514,-336 3504,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1720d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-357 3568,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26423@0" ObjectIDZND0="26422@x" ObjectIDZND1="26426@x" Pin0InfoVect0LinkObjId="SW-156822_0" Pin0InfoVect1LinkObjId="SW-156826_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-357 3568,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d15490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-336 3568,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26423@x" ObjectIDND1="26426@x" ObjectIDZND0="26422@0" Pin0InfoVect0LinkObjId="SW-156822_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156823_0" Pin1InfoVect1LinkObjId="SW-156826_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-336 3568,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21054a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3513,-217 3503,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26425@0" ObjectIDZND0="g_1b3f120@0" Pin0InfoVect0LinkObjId="g_1b3f120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156825_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3513,-217 3503,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1764970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-881 4223,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26447@0" ObjectIDZND0="26407@0" Pin0InfoVect0LinkObjId="SW-156807_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ebc1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-881 4223,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_16cbf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-933 4223,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26407@1" ObjectIDZND0="26406@x" ObjectIDZND1="26408@x" Pin0InfoVect0LinkObjId="SW-156806_0" Pin0InfoVect1LinkObjId="SW-156808_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-933 4223,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b47200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-954 4223,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26407@x" ObjectIDND1="26408@x" ObjectIDZND0="26406@0" Pin0InfoVect0LinkObjId="SW-156806_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156807_0" Pin1InfoVect1LinkObjId="SW-156808_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-954 4223,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d62bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4168,-1083 4158,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26411@0" ObjectIDZND0="g_20e24a0@0" Pin0InfoVect0LinkObjId="g_20e24a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156811_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4168,-1083 4158,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d636e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-954 4205,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26406@x" ObjectIDND1="26407@x" ObjectIDZND0="26408@1" Pin0InfoVect0LinkObjId="SW-156808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156806_0" Pin1InfoVect1LinkObjId="SW-156807_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-954 4205,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cfd180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-954 4159,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26408@0" ObjectIDZND0="g_1d634a0@0" Pin0InfoVect0LinkObjId="g_1d634a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-954 4159,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cf6c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-685 3945,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="26449@x" ObjectIDND1="26453@x" ObjectIDZND0="g_1e09ad0@0" Pin0InfoVect0LinkObjId="g_1e09ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d25520_0" Pin1InfoVect1LinkObjId="SW-157930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-685 3945,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d25520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-685 4000,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1e09ad0@0" ObjectIDND1="26453@x" ObjectIDZND0="26449@x" Pin0InfoVect0LinkObjId="g_1ec8730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e09ad0_0" Pin1InfoVect1LinkObjId="SW-157930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-685 4000,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d720e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-881 4512,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26447@0" ObjectIDZND0="26454@x" ObjectIDZND1="26458@x" Pin0InfoVect0LinkObjId="SW-157929_0" Pin0InfoVect1LinkObjId="SW-157932_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ebc1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-881 4512,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d74a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-996 4554,-996 4554,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d711f0@0" ObjectIDND1="26454@x" ObjectIDND2="26457@x" ObjectIDZND0="g_1d78900@0" Pin0InfoVect0LinkObjId="g_1d78900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d711f0_0" Pin1InfoVect1LinkObjId="SW-157929_0" Pin1InfoVect2LinkObjId="SW-157931_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-996 4554,-996 4554,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d786a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-1017 4512,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d711f0@0" ObjectIDZND0="g_1d78900@0" ObjectIDZND1="26454@x" ObjectIDZND2="26457@x" Pin0InfoVect0LinkObjId="g_1d78900_0" Pin0InfoVect1LinkObjId="SW-157929_0" Pin0InfoVect2LinkObjId="SW-157931_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d711f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-1017 4512,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1de8aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3598,-195 3599,-206 3568,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1de8d00@0" ObjectIDZND0="26424@x" ObjectIDZND1="26425@x" ObjectIDZND2="g_2213a10@0" Pin0InfoVect0LinkObjId="SW-156824_0" Pin0InfoVect1LinkObjId="SW-156825_0" Pin0InfoVect2LinkObjId="g_2213a10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1de8d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3598,-195 3599,-206 3568,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d33830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4170,-1016 4160,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26410@0" ObjectIDZND0="g_1dbfd50@0" Pin0InfoVect0LinkObjId="g_1dbfd50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4170,-1016 4160,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cf9b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1016 4206,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26406@x" ObjectIDND1="26409@x" ObjectIDZND0="26410@1" Pin0InfoVect0LinkObjId="SW-156810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156806_0" Pin1InfoVect1LinkObjId="SW-156809_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1016 4206,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cf9dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1000 4223,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26406@1" ObjectIDZND0="26409@x" ObjectIDZND1="26410@x" Pin0InfoVect0LinkObjId="SW-156809_0" Pin0InfoVect1LinkObjId="SW-156810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1000 4223,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d65250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1016 4223,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26406@x" ObjectIDND1="26410@x" ObjectIDZND0="26409@0" Pin0InfoVect0LinkObjId="SW-156809_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156806_0" Pin1InfoVect1LinkObjId="SW-156810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1016 4223,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d65460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-521 4047,-540 4000,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1d525f0@0" ObjectIDZND0="26418@x" ObjectIDZND1="26420@x" Pin0InfoVect0LinkObjId="SW-156818_0" Pin0InfoVect1LinkObjId="SW-156820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d525f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-521 4047,-540 4000,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d66eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-523 4000,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26418@1" ObjectIDZND0="g_1d525f0@0" ObjectIDZND1="26420@x" Pin0InfoVect0LinkObjId="g_1d525f0_0" Pin0InfoVect1LinkObjId="SW-156820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-523 4000,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d67110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-540 4000,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1d525f0@0" ObjectIDND1="26418@x" ObjectIDZND0="26420@0" Pin0InfoVect0LinkObjId="SW-156820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d525f0_0" Pin1InfoVect1LinkObjId="SW-156818_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-540 4000,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d64bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-517 3593,-517 3593,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26455@x" ObjectIDND1="26456@x" ObjectIDND2="g_1e0eed0@0" ObjectIDZND0="g_1e0e120@0" Pin0InfoVect0LinkObjId="g_1e0e120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-157933_0" Pin1InfoVect1LinkObjId="SW-157934_0" Pin1InfoVect2LinkObjId="g_1e0eed0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-517 3593,-517 3593,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d69bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-589 3514,-589 3514,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1d69e30@0" Pin0InfoVect0LinkObjId="g_1d69e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-589 3514,-589 3514,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d618c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3492,-508 3471,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26456@0" ObjectIDZND0="g_20e76d0@0" Pin0InfoVect0LinkObjId="g_20e76d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-157934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3492,-508 3471,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e4ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3546,-508 3528,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1e0e120@0" ObjectIDND1="g_1e0eed0@0" ObjectIDND2="26455@x" ObjectIDZND0="26456@1" Pin0InfoVect0LinkObjId="SW-157934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e0e120_0" Pin1InfoVect1LinkObjId="g_1e0eed0_0" Pin1InfoVect2LinkObjId="SW-157933_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3546,-508 3528,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e4d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-508 3547,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26455@x" ObjectIDND1="26456@x" ObjectIDZND0="g_1e0e120@0" ObjectIDZND1="g_1e0eed0@0" Pin0InfoVect0LinkObjId="g_1e0e120_0" Pin0InfoVect1LinkObjId="g_1e0eed0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-157933_0" Pin1InfoVect1LinkObjId="SW-157934_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-508 3547,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0dc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-421 3547,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26404@0" ObjectIDZND0="26455@0" Pin0InfoVect0LinkObjId="SW-157933_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-421 3547,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0dec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-484 3547,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26455@1" ObjectIDZND0="g_1e0e120@0" ObjectIDZND1="g_1e0eed0@0" ObjectIDZND2="26456@x" Pin0InfoVect0LinkObjId="g_1e0e120_0" Pin0InfoVect1LinkObjId="g_1e0eed0_0" Pin0InfoVect2LinkObjId="SW-157934_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-157933_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-484 3547,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0f750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-517 3547,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e0e120@0" ObjectIDND1="26455@x" ObjectIDND2="26456@x" ObjectIDZND0="g_1e0eed0@1" Pin0InfoVect0LinkObjId="g_1e0eed0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e0e120_0" Pin1InfoVect1LinkObjId="SW-157933_0" Pin1InfoVect2LinkObjId="SW-157934_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-517 3547,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0f9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-570 3547,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1e0eed0@0" ObjectIDZND0="g_1e0fcc0@0" Pin0InfoVect0LinkObjId="g_1e0fcc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e0eed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-570 3547,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2111380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-421 4336,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26404@0" ObjectIDZND0="26442@1" Pin0InfoVect0LinkObjId="SW-156842_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-421 4336,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2111570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-354 4336,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26442@0" ObjectIDZND0="26441@x" ObjectIDZND1="26446@x" Pin0InfoVect0LinkObjId="SW-156841_0" Pin0InfoVect1LinkObjId="SW-156846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156842_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-354 4336,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21135a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-333 4336,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26442@x" ObjectIDND1="26446@x" ObjectIDZND0="26441@0" Pin0InfoVect0LinkObjId="SW-156841_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156842_0" Pin1InfoVect1LinkObjId="SW-156846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-333 4336,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2114290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-204 4318,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1e960d0@0" ObjectIDND1="26443@x" ObjectIDZND0="26444@1" Pin0InfoVect0LinkObjId="SW-156844_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e960d0_0" Pin1InfoVect1LinkObjId="SW-156843_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-204 4318,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21144f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-204 4272,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26444@0" ObjectIDZND0="g_2113800@0" Pin0InfoVect0LinkObjId="g_2113800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-204 4272,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2114750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-219 4336,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26443@0" ObjectIDZND0="g_1e960d0@0" ObjectIDZND1="26444@x" Pin0InfoVect0LinkObjId="g_1e960d0_0" Pin0InfoVect1LinkObjId="SW-156844_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156843_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-219 4336,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e21630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-333 4272,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26446@0" ObjectIDZND0="g_1e20ba0@0" Pin0InfoVect0LinkObjId="g_1e20ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-333 4272,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e23dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-333 4318,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26441@x" ObjectIDND1="26442@x" ObjectIDZND0="26446@1" Pin0InfoVect0LinkObjId="SW-156846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156841_0" Pin1InfoVect1LinkObjId="SW-156842_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-333 4318,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e275d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-269 4381,-269 4381,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="26441@x" ObjectIDND1="26443@x" ObjectIDZND0="g_1e26820@0" Pin0InfoVect0LinkObjId="g_1e26820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156841_0" Pin1InfoVect1LinkObjId="SW-156843_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-269 4381,-269 4381,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e27830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-255 4336,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="26443@1" ObjectIDZND0="g_1e26820@0" ObjectIDZND1="26441@x" Pin0InfoVect0LinkObjId="g_1e26820_0" Pin0InfoVect1LinkObjId="SW-156841_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156843_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-255 4336,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e27a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-287 4336,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26441@1" ObjectIDZND0="g_1e26820@0" ObjectIDZND1="26443@x" Pin0InfoVect0LinkObjId="g_1e26820_0" Pin0InfoVect1LinkObjId="SW-156843_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-287 4336,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_224a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-1083 4223,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26411@1" ObjectIDZND0="g_16ce000@0" ObjectIDZND1="g_1d6e250@0" ObjectIDZND2="31889@1" Pin0InfoVect0LinkObjId="g_16ce000_0" Pin0InfoVect1LinkObjId="g_1d6e250_0" Pin0InfoVect2LinkObjId="g_1e45e60_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156811_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-1083 4223,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_224a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1069 4223,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26409@1" ObjectIDZND0="g_16ce000@0" ObjectIDZND1="g_1d6e250@0" ObjectIDZND2="31889@1" Pin0InfoVect0LinkObjId="g_16ce000_0" Pin0InfoVect1LinkObjId="g_1d6e250_0" Pin0InfoVect2LinkObjId="g_1e45e60_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156809_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1069 4223,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2255c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-592 3903,-606 3887,-606 3887,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d25750@0" ObjectIDZND0="26453@0" Pin0InfoVect0LinkObjId="SW-157930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d25750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3903,-592 3903,-606 3887,-606 3887,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2255ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-685 3888,-685 3888,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1e09ad0@0" ObjectIDND1="26449@x" ObjectIDZND0="26453@1" Pin0InfoVect0LinkObjId="SW-157930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e09ad0_0" Pin1InfoVect1LinkObjId="g_1d25520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-685 3888,-685 3888,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2256150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-76 4336,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26445@0" ObjectIDZND0="g_1e96fb0@0" Pin0InfoVect0LinkObjId="g_1e96fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-76 4336,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2258430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-1140 4223,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_16ce000@1" ObjectIDZND0="26409@x" ObjectIDZND1="26411@x" ObjectIDZND2="g_1d6e250@0" Pin0InfoVect0LinkObjId="SW-156809_0" Pin0InfoVect1LinkObjId="SW-156811_0" Pin0InfoVect2LinkObjId="g_1d6e250_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16ce000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-1140 4223,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2258690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1140 4223,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_16ce000@0" ObjectIDND1="g_1d6e250@0" ObjectIDND2="31889@1" ObjectIDZND0="26409@x" ObjectIDZND1="26411@x" Pin0InfoVect0LinkObjId="SW-156809_0" Pin0InfoVect1LinkObjId="SW-156811_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16ce000_0" Pin1InfoVect1LinkObjId="g_1d6e250_0" Pin1InfoVect2LinkObjId="g_1e45e60_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1140 4223,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22588f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-268 3568,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26424@1" ObjectIDZND0="26422@1" Pin0InfoVect0LinkObjId="SW-156822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156824_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-268 3568,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2258b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3549,-217 3568,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26425@1" ObjectIDZND0="g_1de8d00@0" ObjectIDZND1="g_2213a10@0" ObjectIDZND2="26424@x" Pin0InfoVect0LinkObjId="g_1de8d00_0" Pin0InfoVect1LinkObjId="g_2213a10_0" Pin0InfoVect2LinkObjId="SW-156824_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156825_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3549,-217 3568,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2259640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-232 3568,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26424@0" ObjectIDZND0="g_1de8d00@0" ObjectIDZND1="g_2213a10@0" ObjectIDZND2="26425@x" Pin0InfoVect0LinkObjId="g_1de8d00_0" Pin0InfoVect1LinkObjId="g_2213a10_0" Pin0InfoVect2LinkObjId="SW-156825_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-232 3568,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22137d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-217 3568,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26424@x" ObjectIDND1="26425@x" ObjectIDZND0="g_1de8d00@0" ObjectIDZND1="g_2213a10@0" Pin0InfoVect0LinkObjId="g_1de8d00_0" Pin0InfoVect1LinkObjId="g_2213a10_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156824_0" Pin1InfoVect1LinkObjId="SW-156825_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-217 3568,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2214290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-206 3568,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1de8d00@0" ObjectIDND1="26424@x" ObjectIDND2="26425@x" ObjectIDZND0="g_2213a10@1" Pin0InfoVect0LinkObjId="g_2213a10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1de8d00_0" Pin1InfoVect1LinkObjId="SW-156824_0" Pin1InfoVect2LinkObjId="SW-156825_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-206 3568,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2216770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-421 3792,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26404@0" ObjectIDZND0="26428@1" Pin0InfoVect0LinkObjId="SW-156828_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-421 3792,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22170c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-337 3774,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26427@x" ObjectIDND1="26428@x" ObjectIDZND0="26431@1" Pin0InfoVect0LinkObjId="SW-156831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156827_0" Pin1InfoVect1LinkObjId="SW-156828_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-337 3774,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2217320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-337 3728,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26431@0" ObjectIDZND0="g_2216960@0" Pin0InfoVect0LinkObjId="g_2216960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-337 3728,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2217580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-358 3792,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26428@0" ObjectIDZND0="26427@x" ObjectIDZND1="26431@x" Pin0InfoVect0LinkObjId="SW-156827_0" Pin0InfoVect1LinkObjId="SW-156831_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156828_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-358 3792,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22197d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-337 3792,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26428@x" ObjectIDND1="26431@x" ObjectIDZND0="26427@0" Pin0InfoVect0LinkObjId="SW-156827_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156828_0" Pin1InfoVect1LinkObjId="SW-156831_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-337 3792,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221a4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-218 3727,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26430@0" ObjectIDZND0="g_2219a30@0" Pin0InfoVect0LinkObjId="g_2219a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-218 3727,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2224180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-196 3823,-207 3792,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22243e0@0" ObjectIDZND0="26429@x" ObjectIDZND1="26430@x" ObjectIDZND2="g_2225b10@0" Pin0InfoVect0LinkObjId="SW-156829_0" Pin0InfoVect1LinkObjId="SW-156830_0" Pin0InfoVect2LinkObjId="g_2225b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22243e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-196 3823,-207 3792,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2225190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-269 3792,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26429@1" ObjectIDZND0="26427@1" Pin0InfoVect0LinkObjId="SW-156827_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156829_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-269 3792,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22253f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3773,-218 3792,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26430@1" ObjectIDZND0="g_22243e0@0" ObjectIDZND1="g_2225b10@0" ObjectIDZND2="26429@x" Pin0InfoVect0LinkObjId="g_22243e0_0" Pin0InfoVect1LinkObjId="g_2225b10_0" Pin0InfoVect2LinkObjId="SW-156829_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3773,-218 3792,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2225650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-233 3792,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26429@0" ObjectIDZND0="g_22243e0@0" ObjectIDZND1="g_2225b10@0" ObjectIDZND2="26430@x" Pin0InfoVect0LinkObjId="g_22243e0_0" Pin0InfoVect1LinkObjId="g_2225b10_0" Pin0InfoVect2LinkObjId="SW-156830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-233 3792,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22258b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-218 3792,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26429@x" ObjectIDND1="26430@x" ObjectIDZND0="g_22243e0@0" ObjectIDZND1="g_2225b10@0" Pin0InfoVect0LinkObjId="g_22243e0_0" Pin0InfoVect1LinkObjId="g_2225b10_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156829_0" Pin1InfoVect1LinkObjId="SW-156830_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-218 3792,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2226530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-207 3792,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_22243e0@0" ObjectIDND1="26429@x" ObjectIDND2="26430@x" ObjectIDZND0="g_2225b10@1" Pin0InfoVect0LinkObjId="g_2225b10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22243e0_0" Pin1InfoVect1LinkObjId="SW-156829_0" Pin1InfoVect2LinkObjId="SW-156830_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-207 3792,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22288d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-421 4016,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26404@0" ObjectIDZND0="26438@1" Pin0InfoVect0LinkObjId="SW-156838_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-421 4016,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22294d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-334 3998,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26437@x" ObjectIDND1="26438@x" ObjectIDZND0="26450@1" Pin0InfoVect0LinkObjId="SW-156857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156837_0" Pin1InfoVect1LinkObjId="SW-156838_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-334 3998,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2229730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-334 3952,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26450@0" ObjectIDZND0="g_2228af0@0" Pin0InfoVect0LinkObjId="g_2228af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-334 3952,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2229990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-355 4016,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26438@0" ObjectIDZND0="26437@x" ObjectIDZND1="26450@x" Pin0InfoVect0LinkObjId="SW-156837_0" Pin0InfoVect1LinkObjId="SW-156857_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156838_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-355 4016,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_222bb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-334 4016,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26438@x" ObjectIDND1="26450@x" ObjectIDZND0="26437@0" Pin0InfoVect0LinkObjId="SW-156837_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156838_0" Pin1InfoVect1LinkObjId="SW-156857_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-334 4016,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_222c800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-215 3951,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26440@0" ObjectIDZND0="g_222bd70@0" Pin0InfoVect0LinkObjId="g_222bd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-215 3951,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22364c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4046,-193 4047,-204 4016,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_2236720@0" ObjectIDZND0="26439@x" ObjectIDZND1="26440@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-156839_0" Pin0InfoVect1LinkObjId="SW-156840_0" Pin0InfoVect2LinkObjId="g_1e0fcc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2236720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4046,-193 4047,-204 4016,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22374d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-266 4016,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26439@1" ObjectIDZND0="26437@1" Pin0InfoVect0LinkObjId="SW-156837_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156839_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-266 4016,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2237730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-215 4016,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="26440@1" ObjectIDZND0="g_2236720@0" ObjectIDZND1="0@x" ObjectIDZND2="26439@x" Pin0InfoVect0LinkObjId="g_2236720_0" Pin0InfoVect1LinkObjId="g_1e0fcc0_0" Pin0InfoVect2LinkObjId="SW-156839_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-215 4016,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2237990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-230 4016,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="26439@0" ObjectIDZND0="g_2236720@0" ObjectIDZND1="0@x" ObjectIDZND2="26440@x" Pin0InfoVect0LinkObjId="g_2236720_0" Pin0InfoVect1LinkObjId="g_1e0fcc0_0" Pin0InfoVect2LinkObjId="SW-156840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156839_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-230 4016,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2237bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-215 4016,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="26439@x" ObjectIDND1="26440@x" ObjectIDZND0="g_2236720@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2236720_0" Pin0InfoVect1LinkObjId="g_1e0fcc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156839_0" Pin1InfoVect1LinkObjId="SW-156840_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-215 4016,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2237e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-204 4016,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_2236720@0" ObjectIDND1="26439@x" ObjectIDND2="26440@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1e0fcc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2236720_0" Pin1InfoVect1LinkObjId="SW-156839_0" Pin1InfoVect2LinkObjId="SW-156840_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-204 4016,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-137 3971,-137 3971,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_223c0e0@0" Pin0InfoVect0LinkObjId="g_223c0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e0fcc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-137 3971,-137 3971,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223d280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3971,-81 3971,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_223c0e0@1" ObjectIDZND0="g_1e957e0@0" Pin0InfoVect0LinkObjId="g_1e957e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_223c0e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3971,-81 3971,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e96af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-112 4336,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26445@1" ObjectIDZND0="g_1e960d0@0" Pin0InfoVect0LinkObjId="g_1e960d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156845_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-112 4336,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e96d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-188 4336,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1e960d0@1" ObjectIDZND0="26443@x" ObjectIDZND1="26444@x" Pin0InfoVect0LinkObjId="SW-156843_0" Pin0InfoVect1LinkObjId="SW-156844_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e960d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-188 4336,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-421 4576,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26404@0" ObjectIDZND0="26433@1" Pin0InfoVect0LinkObjId="SW-156833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-421 4576,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea0a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-333 4558,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26432@x" ObjectIDND1="26433@x" ObjectIDZND0="26436@1" Pin0InfoVect0LinkObjId="SW-156836_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156832_0" Pin1InfoVect1LinkObjId="SW-156833_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-333 4558,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea0cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-333 4512,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26436@0" ObjectIDZND0="g_1ea02f0@0" Pin0InfoVect0LinkObjId="g_1ea02f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156836_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-333 4512,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea0f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-358 4576,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26433@0" ObjectIDZND0="26432@x" ObjectIDZND1="26436@x" Pin0InfoVect0LinkObjId="SW-156832_0" Pin0InfoVect1LinkObjId="SW-156836_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-358 4576,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea3090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-333 4576,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26433@x" ObjectIDND1="26436@x" ObjectIDZND0="26432@0" Pin0InfoVect0LinkObjId="SW-156832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156833_0" Pin1InfoVect1LinkObjId="SW-156836_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-333 4576,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4521,-218 4511,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26435@0" ObjectIDZND0="g_1ea32f0@0" Pin0InfoVect0LinkObjId="g_1ea32f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156835_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4521,-218 4511,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eada40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-196 4607,-207 4576,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1eadca0@0" ObjectIDZND0="26434@x" ObjectIDZND1="26435@x" ObjectIDZND2="g_1eaf3d0@0" Pin0InfoVect0LinkObjId="SW-156834_0" Pin0InfoVect1LinkObjId="SW-156835_0" Pin0InfoVect2LinkObjId="g_1eaf3d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eadca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-196 4607,-207 4576,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eaea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-269 4576,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26434@1" ObjectIDZND0="26432@1" Pin0InfoVect0LinkObjId="SW-156832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-269 4576,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eaecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4557,-218 4576,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26435@1" ObjectIDZND0="g_1eadca0@0" ObjectIDZND1="g_1eaf3d0@0" ObjectIDZND2="26434@x" Pin0InfoVect0LinkObjId="g_1eadca0_0" Pin0InfoVect1LinkObjId="g_1eaf3d0_0" Pin0InfoVect2LinkObjId="SW-156834_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4557,-218 4576,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eaef10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-233 4576,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26434@0" ObjectIDZND0="g_1eadca0@0" ObjectIDZND1="g_1eaf3d0@0" ObjectIDZND2="26435@x" Pin0InfoVect0LinkObjId="g_1eadca0_0" Pin0InfoVect1LinkObjId="g_1eaf3d0_0" Pin0InfoVect2LinkObjId="SW-156835_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-233 4576,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eaf170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-218 4576,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26434@x" ObjectIDND1="26435@x" ObjectIDZND0="g_1eadca0@0" ObjectIDZND1="g_1eaf3d0@0" Pin0InfoVect0LinkObjId="g_1eadca0_0" Pin0InfoVect1LinkObjId="g_1eaf3d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156834_0" Pin1InfoVect1LinkObjId="SW-156835_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-218 4576,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eafdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-207 4576,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1eadca0@0" ObjectIDND1="26434@x" ObjectIDND2="26435@x" ObjectIDZND0="g_1eaf3d0@1" Pin0InfoVect0LinkObjId="g_1eaf3d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1eadca0_0" Pin1InfoVect1LinkObjId="SW-156834_0" Pin1InfoVect2LinkObjId="SW-156835_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-207 4576,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eb0050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-133 4576,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1eaf3d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e0fcc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eaf3d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-133 4576,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ebc1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-867 4000,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26413@1" ObjectIDZND0="26447@0" Pin0InfoVect0LinkObjId="g_1e3b420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156813_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-867 4000,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ebccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-808 4000,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26412@1" ObjectIDZND0="26413@x" ObjectIDZND1="26415@x" Pin0InfoVect0LinkObjId="SW-156813_0" Pin0InfoVect1LinkObjId="SW-156815_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156812_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-808 4000,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ebcf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-822 4000,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26412@x" ObjectIDND1="26415@x" ObjectIDZND0="26413@0" Pin0InfoVect0LinkObjId="SW-156813_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156812_0" Pin1InfoVect1LinkObjId="SW-156815_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-822 4000,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ebf6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-821 3990,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26412@x" ObjectIDND1="26413@x" ObjectIDZND0="26415@1" Pin0InfoVect0LinkObjId="SW-156815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156812_0" Pin1InfoVect1LinkObjId="SW-156813_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-821 3990,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ebf900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-821 3943,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26415@0" ObjectIDZND0="g_1ec4f40@0" Pin0InfoVect0LinkObjId="g_1ec4f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-821 3943,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec2090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-771 3990,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26412@x" ObjectIDND1="26414@x" ObjectIDZND0="26416@1" Pin0InfoVect0LinkObjId="SW-156816_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156812_0" Pin1InfoVect1LinkObjId="SW-156814_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-771 3990,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec22f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-771 3943,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26416@0" ObjectIDZND0="g_1ec59d0@0" Pin0InfoVect0LinkObjId="g_1ec59d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-771 3943,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec4a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-716 3990,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26449@x" ObjectIDND1="26414@x" ObjectIDZND0="26417@1" Pin0InfoVect0LinkObjId="SW-156817_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d25520_0" Pin1InfoVect1LinkObjId="SW-156814_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-716 3990,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec4ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-716 3943,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26417@0" ObjectIDZND0="g_1ec6460@0" Pin0InfoVect0LinkObjId="g_1ec6460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156817_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-716 3943,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec7780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-762 4000,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26414@1" ObjectIDZND0="26412@x" ObjectIDZND1="26416@x" Pin0InfoVect0LinkObjId="SW-156812_0" Pin0InfoVect1LinkObjId="SW-156816_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156814_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-762 4000,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec8270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-703 4000,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26449@0" ObjectIDZND0="26414@x" ObjectIDZND1="26417@x" Pin0InfoVect0LinkObjId="SW-156814_0" Pin0InfoVect1LinkObjId="SW-156817_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d25520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-703 4000,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-781 4000,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26412@0" ObjectIDZND0="26414@x" ObjectIDZND1="26416@x" Pin0InfoVect0LinkObjId="SW-156814_0" Pin0InfoVect1LinkObjId="SW-156816_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156812_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-781 4000,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ec8730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-726 4000,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="26414@0" ObjectIDZND0="26449@x" ObjectIDZND1="26417@x" Pin0InfoVect0LinkObjId="g_1d25520_0" Pin0InfoVect1LinkObjId="SW-156817_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-726 4000,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e38a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-996 4512,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1d78900@0" ObjectIDND1="g_1d711f0@0" ObjectIDZND0="26454@x" ObjectIDZND1="26457@x" Pin0InfoVect0LinkObjId="SW-157929_0" Pin0InfoVect1LinkObjId="SW-157931_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d78900_0" Pin1InfoVect1LinkObjId="g_1d711f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-996 4512,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e3b1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-977 4512,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d78900@0" ObjectIDND1="g_1d711f0@0" ObjectIDND2="26457@x" ObjectIDZND0="26454@1" Pin0InfoVect0LinkObjId="SW-157929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d78900_0" Pin1InfoVect1LinkObjId="g_1d711f0_0" Pin1InfoVect2LinkObjId="SW-157931_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-977 4512,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e3b420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-926 4512,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="26454@0" ObjectIDZND0="26447@0" ObjectIDZND1="26458@x" Pin0InfoVect0LinkObjId="g_1ebc1c0_0" Pin0InfoVect1LinkObjId="SW-157932_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-157929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-926 4512,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e3dbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-976 4500,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d78900@0" ObjectIDND1="g_1d711f0@0" ObjectIDND2="26454@x" ObjectIDZND0="26457@1" Pin0InfoVect0LinkObjId="SW-157931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d78900_0" Pin1InfoVect1LinkObjId="g_1d711f0_0" Pin1InfoVect2LinkObjId="SW-157929_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-976 4500,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e3de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-976 4441,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26457@0" ObjectIDZND0="g_1d72340@0" Pin0InfoVect0LinkObjId="g_1d72340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-157931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-976 4441,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e405a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-900 4497,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26447@0" ObjectIDND1="26454@x" ObjectIDZND0="26458@1" Pin0InfoVect0LinkObjId="SW-157932_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ebc1c0_0" Pin1InfoVect1LinkObjId="SW-157929_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-900 4497,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e40800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-900 4442,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26458@0" ObjectIDZND0="g_1d77970@0" Pin0InfoVect0LinkObjId="g_1d77970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-157932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-900 4442,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e45e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1158 4223,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1d6e250@0" ObjectIDND1="g_16ce000@0" ObjectIDND2="26409@x" ObjectIDZND0="31889@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d6e250_0" Pin1InfoVect1LinkObjId="g_16ce000_0" Pin1InfoVect2LinkObjId="SW-156809_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1158 4223,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e467d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-1151 4265,-1158 4223,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d6e250@0" ObjectIDZND0="g_16ce000@0" ObjectIDZND1="26409@x" ObjectIDZND2="26411@x" Pin0InfoVect0LinkObjId="g_16ce000_0" Pin0InfoVect1LinkObjId="SW-156809_0" Pin0InfoVect2LinkObjId="SW-156811_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d6e250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-1151 4265,-1158 4223,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e469c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1158 4223,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d6e250@0" ObjectIDND1="31889@1" ObjectIDZND0="g_16ce000@0" ObjectIDZND1="26409@x" ObjectIDZND2="26411@x" Pin0InfoVect0LinkObjId="g_16ce000_0" Pin0InfoVect1LinkObjId="SW-156809_0" Pin0InfoVect2LinkObjId="SW-156811_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d6e250_0" Pin1InfoVect1LinkObjId="g_1e45e60_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1158 4223,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3568,-132 3568,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2213a10@0" ObjectIDZND0="42831@0" Pin0InfoVect0LinkObjId="SM-CX_SYS.1G_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2213a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3568,-132 3568,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-133 3792,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2225b10@0" ObjectIDZND0="43310@0" Pin0InfoVect0LinkObjId="SM-CX_SYS.2G_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2225b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-133 3792,-103 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26447" cx="4512" cy="-881" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26404" cx="4000" cy="-421" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26404" cx="3568" cy="-421" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26447" cx="4223" cy="-881" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26404" cx="3547" cy="-421" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26404" cx="4336" cy="-421" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26404" cx="3792" cy="-421" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26404" cx="4016" cy="-421" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26404" cx="4576" cy="-421" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26447" cx="4000" cy="-881" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130467" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3386.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23908" ObjectName="DYN-CX_SYS"/>
     <cge:Meas_Ref ObjectId="130467"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d52190" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3517.000000 -74.000000) translate(0,15)">三月山Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d67370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -996.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -996.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -996.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -996.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -996.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -996.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -996.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1e08a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3229.000000 -1165.500000) translate(0,16)">三月山升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df5ba0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3492.000000 -649.000000) translate(0,15)">35kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20e80e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4254.000000 -31.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e27cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.500000 -1220.000000) translate(0,15)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e27cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.500000 -1220.000000) translate(0,33)">狮</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e27cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.500000 -1220.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e28600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -735.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e28600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -735.000000) translate(0,33)">SZ11-75000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e28600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -735.000000) translate(0,51)">115±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e28600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -735.000000) translate(0,69)">75000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e28600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -735.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e28600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -735.000000) translate(0,105)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2249d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4308.500000 -4.000000) translate(0,12)">±23MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22144f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3490.000000 -53.000000) translate(0,15)">(1-11、16号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2216080" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3741.000000 -75.000000) translate(0,15)">三月山Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2226790" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3697.000000 -54.000000) translate(0,15)">(12-15、17-24号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e983e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3928.000000 -44.000000) translate(0,15)">35kV1号接地变及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e998f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -150.000000) translate(0,12)">1号接地变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e998f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -150.000000) translate(0,27)">DKSC-1250/35-400/0.4GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e998f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -150.000000) translate(0,42)">1250/400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e998f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -150.000000) translate(0,57)">35±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e998f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -150.000000) translate(0,72)">ZN,yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e998f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.000000 -150.000000) translate(0,87)">Ud%=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e9f550" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4525.000000 -75.000000) translate(0,15)">石将军Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eb02b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4489.000000 -54.000000) translate(0,15)">(二期工程，未接风机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec8990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -994.000000) translate(0,12)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec8fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.000000 -922.000000) translate(0,12)">1611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec9200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -978.000000) translate(0,12)">16117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec9620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.000000 -1058.000000) translate(0,12)">1616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec9a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -1040.000000) translate(0,12)">16160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec9cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -1107.000000) translate(0,12)">16167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec9f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -802.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eca140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -751.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eca380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -847.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eca5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3946.000000 -797.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eca800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3946.000000 -742.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecaa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -517.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecac80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -585.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecaec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -465.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3943.000000 -509.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2c120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -311.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3575.000000 -382.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2c5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3523.000000 -362.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2c7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3575.000000 -257.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2ca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3522.000000 -243.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2cc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -312.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2cea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -383.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -258.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2d320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3748.000000 -363.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2d560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3746.000000 -244.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2d7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -309.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2d9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -380.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2dc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -255.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2de60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -360.000000) translate(0,12)">36317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2e0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3970.000000 -241.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2e2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -308.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2e520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -379.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2e760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -244.000000) translate(0,12)">3643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2e9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -101.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2ebe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4292.000000 -359.000000) translate(0,12)">36417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2ee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4291.000000 -230.000000) translate(0,12)">36437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2f060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.000000 -312.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2f2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -383.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2f4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -258.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2f720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.000000 -359.000000) translate(0,12)">36517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2f960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4530.000000 -244.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e2fba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3931.000000 -610.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e33b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -900.000000) translate(0,12)">110kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e348c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3433.000000 -441.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e370a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -856.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e44810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -652.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e44a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -951.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e45040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -1002.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e452c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4460.000000 -926.000000) translate(0,12)">11017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e45500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3554.000000 -473.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e45740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3490.000000 -534.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e45980" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3243.000000 -220.000000) translate(0,15)">8831288</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e45980" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3243.000000 -220.000000) translate(0,33)">4948</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e46e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -792.000000) translate(0,17)">风电场实时功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1e4c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3364.000000 -1061.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4016,-170 4011,-180 4021,-180 4016,-170 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-156820">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -555.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26420" ObjectName="SW-CX_SYS.CX_SYS_3016SW"/>
     <cge:Meas_Ref ObjectId="156820"/>
    <cge:TPSR_Ref TObjectID="26420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156819">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -435.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26419" ObjectName="SW-CX_SYS.CX_SYS_3011SW"/>
     <cge:Meas_Ref ObjectId="156819"/>
    <cge:TPSR_Ref TObjectID="26419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156821">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -478.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26421" ObjectName="SW-CX_SYS.CX_SYS_30117SW"/>
     <cge:Meas_Ref ObjectId="156821"/>
    <cge:TPSR_Ref TObjectID="26421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156826">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 -331.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26426" ObjectName="SW-CX_SYS.CX_SYS_36117SW"/>
     <cge:Meas_Ref ObjectId="156826"/>
    <cge:TPSR_Ref TObjectID="26426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156825">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3508.000000 -212.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26425" ObjectName="SW-CX_SYS.CX_SYS_36167SW"/>
     <cge:Meas_Ref ObjectId="156825"/>
    <cge:TPSR_Ref TObjectID="26425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156823">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3559.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26423" ObjectName="SW-CX_SYS.CX_SYS_3611SW"/>
     <cge:Meas_Ref ObjectId="156823"/>
    <cge:TPSR_Ref TObjectID="26423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3559.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26424" ObjectName="SW-CX_SYS.CX_SYS_3616SW"/>
     <cge:Meas_Ref ObjectId="156824"/>
    <cge:TPSR_Ref TObjectID="26424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156807">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4214.000000 -892.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26407" ObjectName="SW-CX_SYS.CX_SYS_1611SW"/>
     <cge:Meas_Ref ObjectId="156807"/>
    <cge:TPSR_Ref TObjectID="26407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156809">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4214.000000 -1028.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26409" ObjectName="SW-CX_SYS.CX_SYS_1616SW"/>
     <cge:Meas_Ref ObjectId="156809"/>
    <cge:TPSR_Ref TObjectID="26409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156811">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -1078.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26411" ObjectName="SW-CX_SYS.CX_SYS_16167SW"/>
     <cge:Meas_Ref ObjectId="156811"/>
    <cge:TPSR_Ref TObjectID="26411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156808">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -949.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26408" ObjectName="SW-CX_SYS.CX_SYS_16117SW"/>
     <cge:Meas_Ref ObjectId="156808"/>
    <cge:TPSR_Ref TObjectID="26408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156810">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -1011.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26410" ObjectName="SW-CX_SYS.CX_SYS_16160SW"/>
     <cge:Meas_Ref ObjectId="156810"/>
    <cge:TPSR_Ref TObjectID="26410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-157934">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3487.000000 -503.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26456" ObjectName="SW-CX_SYS.CX_SYS_39017SW"/>
     <cge:Meas_Ref ObjectId="157934"/>
    <cge:TPSR_Ref TObjectID="26456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-157933">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3538.000000 -443.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26455" ObjectName="SW-CX_SYS.CX_SYS_3901SW"/>
     <cge:Meas_Ref ObjectId="157933"/>
    <cge:TPSR_Ref TObjectID="26455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156844">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 -199.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26444" ObjectName="SW-CX_SYS.CX_SYS_36467SW"/>
     <cge:Meas_Ref ObjectId="156844"/>
    <cge:TPSR_Ref TObjectID="26444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156842">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26442" ObjectName="SW-CX_SYS.CX_SYS_3641SW"/>
     <cge:Meas_Ref ObjectId="156842"/>
    <cge:TPSR_Ref TObjectID="26442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156843">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -214.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26443" ObjectName="SW-CX_SYS.CX_SYS_3643SW"/>
     <cge:Meas_Ref ObjectId="156843"/>
    <cge:TPSR_Ref TObjectID="26443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156846">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 -328.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26446" ObjectName="SW-CX_SYS.CX_SYS_36417SW"/>
     <cge:Meas_Ref ObjectId="156846"/>
    <cge:TPSR_Ref TObjectID="26446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156845">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -71.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26445" ObjectName="SW-CX_SYS.CX_SYS_3646SW"/>
     <cge:Meas_Ref ObjectId="156845"/>
    <cge:TPSR_Ref TObjectID="26445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-157930">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -620.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26453" ObjectName="SW-CX_SYS.CX_SYS_1010SW"/>
     <cge:Meas_Ref ObjectId="157930"/>
    <cge:TPSR_Ref TObjectID="26453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156831">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26431" ObjectName="SW-CX_SYS.CX_SYS_36217SW"/>
     <cge:Meas_Ref ObjectId="156831"/>
    <cge:TPSR_Ref TObjectID="26431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156830">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 -213.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26430" ObjectName="SW-CX_SYS.CX_SYS_36267SW"/>
     <cge:Meas_Ref ObjectId="156830"/>
    <cge:TPSR_Ref TObjectID="26430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156828">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26428" ObjectName="SW-CX_SYS.CX_SYS_3621SW"/>
     <cge:Meas_Ref ObjectId="156828"/>
    <cge:TPSR_Ref TObjectID="26428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156829">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26429" ObjectName="SW-CX_SYS.CX_SYS_3626SW"/>
     <cge:Meas_Ref ObjectId="156829"/>
    <cge:TPSR_Ref TObjectID="26429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -329.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26450" ObjectName="SW-CX_SYS.CX_SYS_36317SW"/>
     <cge:Meas_Ref ObjectId="156857"/>
    <cge:TPSR_Ref TObjectID="26450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156840">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -210.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26440" ObjectName="SW-CX_SYS.CX_SYS_36367SW"/>
     <cge:Meas_Ref ObjectId="156840"/>
    <cge:TPSR_Ref TObjectID="26440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156838">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26438" ObjectName="SW-CX_SYS.CX_SYS_3631SW"/>
     <cge:Meas_Ref ObjectId="156838"/>
    <cge:TPSR_Ref TObjectID="26438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156839">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -225.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26439" ObjectName="SW-CX_SYS.CX_SYS_3636SW"/>
     <cge:Meas_Ref ObjectId="156839"/>
    <cge:TPSR_Ref TObjectID="26439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156836">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -328.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26436" ObjectName="SW-CX_SYS.CX_SYS_36517SW"/>
     <cge:Meas_Ref ObjectId="156836"/>
    <cge:TPSR_Ref TObjectID="26436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156835">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.000000 -213.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26435" ObjectName="SW-CX_SYS.CX_SYS_36567SW"/>
     <cge:Meas_Ref ObjectId="156835"/>
    <cge:TPSR_Ref TObjectID="26435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156833">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26433" ObjectName="SW-CX_SYS.CX_SYS_3651SW"/>
     <cge:Meas_Ref ObjectId="156833"/>
    <cge:TPSR_Ref TObjectID="26433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156834">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26434" ObjectName="SW-CX_SYS.CX_SYS_3656SW"/>
     <cge:Meas_Ref ObjectId="156834"/>
    <cge:TPSR_Ref TObjectID="26434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156814">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -721.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26414" ObjectName="SW-CX_SYS.CX_SYS_1016SW"/>
     <cge:Meas_Ref ObjectId="156814"/>
    <cge:TPSR_Ref TObjectID="26414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156815">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -816.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26415" ObjectName="SW-CX_SYS.CX_SYS_10117SW"/>
     <cge:Meas_Ref ObjectId="156815"/>
    <cge:TPSR_Ref TObjectID="26415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156816">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -766.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26416" ObjectName="SW-CX_SYS.CX_SYS_10160SW"/>
     <cge:Meas_Ref ObjectId="156816"/>
    <cge:TPSR_Ref TObjectID="26416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156817">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -711.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26417" ObjectName="SW-CX_SYS.CX_SYS_10167SW"/>
     <cge:Meas_Ref ObjectId="156817"/>
    <cge:TPSR_Ref TObjectID="26417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156813">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -826.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26413" ObjectName="SW-CX_SYS.CX_SYS_1011SW"/>
     <cge:Meas_Ref ObjectId="156813"/>
    <cge:TPSR_Ref TObjectID="26413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-157929">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -921.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26454" ObjectName="SW-CX_SYS.CX_SYS_1901SW"/>
     <cge:Meas_Ref ObjectId="157929"/>
    <cge:TPSR_Ref TObjectID="26454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-157931">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4459.000000 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26457" ObjectName="SW-CX_SYS.CX_SYS_19017SW"/>
     <cge:Meas_Ref ObjectId="157931"/>
    <cge:TPSR_Ref TObjectID="26457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-157932">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -895.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26458" ObjectName="SW-CX_SYS.CX_SYS_11017SW"/>
     <cge:Meas_Ref ObjectId="157932"/>
    <cge:TPSR_Ref TObjectID="26458"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SS" endPointId="0" endStationName="CX_SYS" flowDrawDirect="1" flowShape="0" id="AC-110kV.Sanshi_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4223,-1175 4223,-1215 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31889" ObjectName="AC-110kV.Sanshi_line"/>
    <cge:TPSR_Ref TObjectID="31889_SS-188"/></metadata>
   <polyline fill="none" opacity="0" points="4223,-1175 4223,-1215 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_16ce000">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -1100.000000)" xlink:href="#lightningRod:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d711f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -1015.000000)" xlink:href="#lightningRod:shape131"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d78900">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -1011.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d6e250">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4258.000000 -1093.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e09ad0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -613.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d525f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -463.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1de8d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0e120">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3586.000000 -611.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0eed0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3538.000000 -575.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e26820">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -194.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2213a10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3563.000000 -127.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22243e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2225b10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 -128.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2236720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 -135.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223c0e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -76.000000)" xlink:href="#lightningRod:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e960d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4331.000000 -130.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e96fb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 -36.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eadca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eaf3d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -128.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -818.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26412"/>
     <cge:Term_Ref ObjectID="37332"/>
    <cge:TPSR_Ref TObjectID="26412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -818.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26412"/>
     <cge:Term_Ref ObjectID="37332"/>
    <cge:TPSR_Ref TObjectID="26412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -818.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26412"/>
     <cge:Term_Ref ObjectID="37332"/>
    <cge:TPSR_Ref TObjectID="26412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -561.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26418"/>
     <cge:Term_Ref ObjectID="37344"/>
    <cge:TPSR_Ref TObjectID="26418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156771" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -561.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26418"/>
     <cge:Term_Ref ObjectID="37344"/>
    <cge:TPSR_Ref TObjectID="26418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -561.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26418"/>
     <cge:Term_Ref ObjectID="37344"/>
    <cge:TPSR_Ref TObjectID="26418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -324.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26422"/>
     <cge:Term_Ref ObjectID="37352"/>
    <cge:TPSR_Ref TObjectID="26422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -324.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26422"/>
     <cge:Term_Ref ObjectID="37352"/>
    <cge:TPSR_Ref TObjectID="26422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -324.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26422"/>
     <cge:Term_Ref ObjectID="37352"/>
    <cge:TPSR_Ref TObjectID="26422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -324.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26427"/>
     <cge:Term_Ref ObjectID="37362"/>
    <cge:TPSR_Ref TObjectID="26427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -324.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26427"/>
     <cge:Term_Ref ObjectID="37362"/>
    <cge:TPSR_Ref TObjectID="26427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -324.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26427"/>
     <cge:Term_Ref ObjectID="37362"/>
    <cge:TPSR_Ref TObjectID="26427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -324.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26437"/>
     <cge:Term_Ref ObjectID="37382"/>
    <cge:TPSR_Ref TObjectID="26437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156791" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -324.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26437"/>
     <cge:Term_Ref ObjectID="37382"/>
    <cge:TPSR_Ref TObjectID="26437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -324.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26437"/>
     <cge:Term_Ref ObjectID="37382"/>
    <cge:TPSR_Ref TObjectID="26437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4386.000000 -324.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26441"/>
     <cge:Term_Ref ObjectID="37390"/>
    <cge:TPSR_Ref TObjectID="26441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4386.000000 -324.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26441"/>
     <cge:Term_Ref ObjectID="37390"/>
    <cge:TPSR_Ref TObjectID="26441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156793" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4386.000000 -324.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26441"/>
     <cge:Term_Ref ObjectID="37390"/>
    <cge:TPSR_Ref TObjectID="26441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -324.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26432"/>
     <cge:Term_Ref ObjectID="37372"/>
    <cge:TPSR_Ref TObjectID="26432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -324.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26432"/>
     <cge:Term_Ref ObjectID="37372"/>
    <cge:TPSR_Ref TObjectID="26432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -324.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26432"/>
     <cge:Term_Ref ObjectID="37372"/>
    <cge:TPSR_Ref TObjectID="26432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4334.000000 -1010.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26406"/>
     <cge:Term_Ref ObjectID="37320"/>
    <cge:TPSR_Ref TObjectID="26406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4334.000000 -1010.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26406"/>
     <cge:Term_Ref ObjectID="37320"/>
    <cge:TPSR_Ref TObjectID="26406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4334.000000 -1010.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26406"/>
     <cge:Term_Ref ObjectID="37320"/>
    <cge:TPSR_Ref TObjectID="26406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-156757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -968.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26447"/>
     <cge:Term_Ref ObjectID="37402"/>
    <cge:TPSR_Ref TObjectID="26447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-156758" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -968.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156758" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26447"/>
     <cge:Term_Ref ObjectID="37402"/>
    <cge:TPSR_Ref TObjectID="26447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-156759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -968.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26447"/>
     <cge:Term_Ref ObjectID="37402"/>
    <cge:TPSR_Ref TObjectID="26447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-156760" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -968.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156760" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26447"/>
     <cge:Term_Ref ObjectID="37402"/>
    <cge:TPSR_Ref TObjectID="26447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-156764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -968.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26447"/>
     <cge:Term_Ref ObjectID="37402"/>
    <cge:TPSR_Ref TObjectID="26447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-156797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -516.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26404"/>
     <cge:Term_Ref ObjectID="37317"/>
    <cge:TPSR_Ref TObjectID="26404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-156798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -516.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26404"/>
     <cge:Term_Ref ObjectID="37317"/>
    <cge:TPSR_Ref TObjectID="26404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-156799" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -516.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26404"/>
     <cge:Term_Ref ObjectID="37317"/>
    <cge:TPSR_Ref TObjectID="26404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-156800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -516.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26404"/>
     <cge:Term_Ref ObjectID="37317"/>
    <cge:TPSR_Ref TObjectID="26404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-156804" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -516.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26404"/>
     <cge:Term_Ref ObjectID="37317"/>
    <cge:TPSR_Ref TObjectID="26404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-156776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -621.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26449"/>
     <cge:Term_Ref ObjectID="37409"/>
    <cge:TPSR_Ref TObjectID="26449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-156773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -621.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26449"/>
     <cge:Term_Ref ObjectID="37409"/>
    <cge:TPSR_Ref TObjectID="26449"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="164" x="3197" y="-1176"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3148" y="-1194"/></g>
   <g href="AVC三月山.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3337" y="-1076"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2246a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.000000 1010.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2247cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 995.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2248b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 980.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22495d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 561.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22498a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 546.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2249ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 531.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb4ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 964.000000) translate(0,12)">Ua(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb52d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 921.000000) translate(0,12)">Uab(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb5770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 935.000000) translate(0,12)">Uc(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb5d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3649.000000 907.000000) translate(0,12)">F(Hz)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb6930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 949.000000) translate(0,12)">Ub(kV)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb6ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 512.000000) translate(0,12)">Ua(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb6f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.000000 469.000000) translate(0,12)">Uab(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb7150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 483.000000) translate(0,12)">Uc(kV)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb7390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4519.000000 455.000000) translate(0,12)">F(Hz)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb75d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 497.000000) translate(0,12)">Ub(kV)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2b9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 817.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2bca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4043.000000 802.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2bee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 787.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2fed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 621.000000) translate(0,12)">档位：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e306a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 604.000000) translate(0,12)">温度：</text>
   <metadata/></g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_SYS.1G">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3563.000000 -81.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42831" ObjectName="SM-CX_SYS.1G"/>
    <cge:TPSR_Ref TObjectID="42831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_SYS.2G">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 -82.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43310" ObjectName="SM-CX_SYS.2G"/>
    <cge:TPSR_Ref TObjectID="43310"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3185.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-234812" ratioFlag="0">
    <text fill="rgb(255,0,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3306.000000 -789.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234812" ObjectName="CX_SYS:CX_SYS_GG_AGC_302"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-538"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="164" x="3197" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="164" x="3197" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3148" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3148" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3337,-1076 3334,-1079 3334,-1025 3337,-1028 3337,-1076" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3337,-1076 3334,-1079 3483,-1079 3480,-1076 3337,-1076" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3337,-1028 3334,-1025 3483,-1025 3480,-1028 3337,-1028" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3480,-1076 3483,-1079 3483,-1025 3480,-1028 3480,-1076" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3337" y="-1076"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3337" y="-1076"/>
    </a>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SYS.CX_SYS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37408"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.000000 -618.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.000000 -618.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26449" ObjectName="TF-CX_SYS.CX_SYS_1T"/>
    <cge:TPSR_Ref TObjectID="26449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -56.000000)" xlink:href="#transformer2:shape63_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -56.000000)" xlink:href="#transformer2:shape63_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SYS"/>
</svg>