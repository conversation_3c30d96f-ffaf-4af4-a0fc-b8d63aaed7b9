<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-308" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2884 -1298 3821 1454">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape27">
    <polyline points="22,19 34,19 34,6 32,7 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="37" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="33" x2="36" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="30" x2="39" y1="6" y2="6"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="18" cy="19" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="25" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="6" x2="8" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="9" x2="9" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="18" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="18" y1="17" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="17" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="12" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="12" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="45" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape137">
    <ellipse cx="18" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="17" y2="19"/>
    <ellipse cx="28" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="19" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="19" y2="22"/>
    <ellipse cx="17" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="13" y2="16"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="6" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape95">
    <ellipse cx="20" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="34" x2="29" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="34" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="31" x2="29" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="21" x2="21" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="9" x2="0" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="6" x2="3" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="7" x2="2" y1="4" y2="4"/>
    <polyline points="21,19 5,19 5,6 " stroke-width="1"/>
    <ellipse cx="31" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="20" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="31" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
   </symbol>
   <symbol id="lightningRod:shape103">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="23" x2="22" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.84362" x1="1" x2="22" y1="46" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="22" x2="15" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="10" x2="10" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675211" x1="11" x2="11" y1="5" y2="16"/>
    <polyline arcFlag="1" points="11,28 11,28 11,28 11,28 12,28 12,28 12,27 12,27 13,27 13,27 13,26 13,26 13,26 13,25 13,25 13,24 13,24 13,24 13,23 12,23 12,23 12,23 12,22 11,22 11,22 11,22 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="10,34 11,34 11,34 11,34 11,34 12,34 12,34 12,33 12,33 12,33 12,32 13,32 13,32 13,31 13,31 13,31 12,30 12,30 12,30 12,29 12,29 12,29 11,29 11,28 11,28 11,28 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="11,22 11,22 11,22 11,22 12,22 12,21 12,21 12,21 13,21 13,20 13,20 13,20 13,19 13,19 13,19 13,18 13,18 13,17 13,17 12,17 12,17 12,16 12,16 11,16 11,16 11,16 " stroke-width="0.0340106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="9" x2="12" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="8" x2="13" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="4" x2="17" y1="46" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape22">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="55" x2="60" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="46" x2="51" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="38" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="30" x2="35" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="86" x2="72" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="18" y1="9" y2="9"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.34694"/>
    <polyline points="58,100 64,100 " stroke-width="1.34694"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.34694"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape22">
    <polyline points="41,18 8,18 8,43 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="5" x2="11" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="0" x2="15" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.618687" x1="6" x2="10" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="38" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.17171" x1="22" x2="31" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21568" x1="27" x2="22" y1="20" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21447" x1="27" x2="31" y1="20" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="61" x2="57" y1="22" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="57" x2="57" y1="26" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="52" x2="56" y1="22" y2="26"/>
    <ellipse cx="27" cy="26" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="42" cy="35" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="55" cy="26" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="41" cy="20" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="35" y2="38"/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="19,9 35,9 35,22 " stroke-width="0.8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="voltageTransformer:shape125">
    <circle cx="8" cy="22" fillStyle="0" r="7" stroke-width="0.492782"/>
    <polyline points="51,14 51,8 " stroke-width="1"/>
    <polyline points="51,22 51,14 " stroke-width="1"/>
    <polyline points="51,14 26,14 " stroke-width="1"/>
    <polyline points="26,31 47,31 51,31 51,22 " stroke-width="1"/>
    <polyline points="51,22 39,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="56" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="53" x2="49" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="54" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="24" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="24" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="32" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="36" x2="32" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="28" x2="32" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="24" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="20" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="24" x2="20" y1="31" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="20" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="20" y1="31" y2="28"/>
    <circle cx="32" cy="22" fillStyle="0" r="7" stroke-width="0.492782"/>
    <circle cx="20" cy="17" fillStyle="0" r="7" stroke-width="0.492782"/>
    <circle cx="20" cy="27" fillStyle="0" r="7" stroke-width="0.492782"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="24" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="24" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="20" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c08ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0a190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0ab40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0b6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0c9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0d680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0de90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c0e880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13f5890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13f5890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c11390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c11390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c13090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c13090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1c140a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c15c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c16640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c17520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c17e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c190b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c19a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c1a340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c1ab00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c1bbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c1c560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c1d050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c1da10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c1f080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c1fba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c20bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c21840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c2fb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c23140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c24830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c25c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1464" width="3831" x="2879" y="-1303"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_131fb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 1229.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1320ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 1214.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1374ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 1199.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1375610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 1236.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1375910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 1221.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1375b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 1206.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1375f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5207.000000 905.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1376230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5196.000000 890.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1376470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5221.000000 875.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1376890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5322.000000 1227.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1376b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5311.000000 1212.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1376d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5336.000000 1197.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13771b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5575.000000 1225.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1377470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5564.000000 1210.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13776b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5589.000000 1195.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1377ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5088.000000 843.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1377d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 828.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1377fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5102.000000 813.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13783f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5647.000000 899.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13786b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5636.000000 884.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1258130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5661.000000 869.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1258550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5697.000000 764.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1258810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5686.000000 749.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1258a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5711.000000 734.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c70b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 887.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c7ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 872.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c8160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4640.000000 857.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c83e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 841.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c8620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 901.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c8860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4642.000000 825.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b67a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5558.000000 1003.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b6a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5558.000000 988.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b6c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5564.000000 973.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b6ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5550.000000 957.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b70e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5558.000000 1017.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b7320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5566.000000 941.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b7650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6001.000000 1180.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b78d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6001.000000 1165.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b7b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6007.000000 1150.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b7d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5993.000000 1134.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b7f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6001.000000 1194.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b81d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6009.000000 1118.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1263880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6474.000000 1045.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1263d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6463.000000 1030.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1292bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6488.000000 1015.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1293360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6488.000000 870.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12935f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6477.000000 855.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1293830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6502.000000 840.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1294220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6470.000000 688.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12944b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6459.000000 673.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12946f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6484.000000 658.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12950e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6468.000000 419.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1295370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6457.000000 404.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12955b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6482.000000 389.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1296580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6464.000000 311.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1296810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6453.000000 296.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1296a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6478.000000 281.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a4540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5656.000000 832.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a54b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5656.000000 847.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 908.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c36b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 893.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c38f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 878.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c3d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 1011.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c3fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 996.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c4210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4023.000000 981.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c5590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 21.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c5820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 6.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c5a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -9.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c7fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 541.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 526.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 511.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c86b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 495.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c88f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 555.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 479.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c9fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 834.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ca200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 849.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4979" x2="5007" y1="725" y2="708"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5007" x2="4980" y1="724" y2="708"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4979" x2="5007" y1="725" y2="708"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2938" y="-1219"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2937" y="-1099"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-632"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 -922.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5301.000000 -928.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5301.000000 -1002.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 -1002.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -1044.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -1095.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -1030.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -1044.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5028.000000 -1095.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5027.000000 -976.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5027.000000 -1030.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -976.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -800.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -874.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -815.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5694.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5694.000000 -1044.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5716.000000 -1095.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5715.000000 -976.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5644.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5413.000000 -924.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5413.000000 -1045.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5435.000000 -1096.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5434.000000 -977.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5434.000000 -1031.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -741.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -842.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -790.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5529.000000 -851.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5529.000000 -743.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5551.000000 -839.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5551.000000 -791.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5390.000000 -553.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5730.000000 -681.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5865.000000 -681.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6082.000000 -840.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6195.000000 -840.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 6077.000000 -289.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 6190.000000 -289.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6259.000000 -1012.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6064.000000 -407.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6186.000000 -407.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6259.000000 -866.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6075.000000 -663.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6220.000000 -663.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6321.000000 -627.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6049.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6195.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 6257.000000 -263.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 6254.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5957.000000 -1015.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5938.000000 -1029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5873.000000 -799.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5873.000000 -873.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5851.000000 -814.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5551.000000 -730.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -0.891304 5968.000000 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6300.000000 -687.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6136.000000 -721.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6214.000000 -710.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5422.000000 -656.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43845" ObjectName="SW-CX_DYYDB.CX_DYYDB_0033SW"/>
     <cge:Meas_Ref ObjectId="272654"/>
    <cge:TPSR_Ref TObjectID="43845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272537">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4466.000000 -957.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43841" ObjectName="SW-CX_DYYDB.CX_DYYDB_1031SW"/>
     <cge:Meas_Ref ObjectId="272537"/>
    <cge:TPSR_Ref TObjectID="43841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272539">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.760870 -0.000000 0.000000 -1.000000 4316.000000 -1093.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43843" ObjectName="SW-CX_DYYDB.CX_DYYDB_1030SW"/>
     <cge:Meas_Ref ObjectId="272539"/>
    <cge:TPSR_Ref TObjectID="43843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272538">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4441.000000 -976.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43842" ObjectName="SW-CX_DYYDB.CX_DYYDB_10317SW"/>
     <cge:Meas_Ref ObjectId="272538"/>
    <cge:TPSR_Ref TObjectID="43842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3458.000000 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3438.000000 -259.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3569.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3549.000000 -259.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3683.000000 -329.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3663.000000 -255.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -330.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3760.000000 -256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 -330.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3856.000000 -256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3953.000000 -260.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43847" ObjectName="SW-CX_DYYDB.CX_DYYDB_0311SW"/>
     <cge:Meas_Ref ObjectId="272666"/>
    <cge:TPSR_Ref TObjectID="43847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272667">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4048.000000 -260.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43848" ObjectName="SW-CX_DYYDB.CX_DYYDB_03167SW"/>
     <cge:Meas_Ref ObjectId="272667"/>
    <cge:TPSR_Ref TObjectID="43848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43859" ObjectName="SW-CX_DYYDB.CX_DYYDB_0351SW"/>
     <cge:Meas_Ref ObjectId="272773"/>
    <cge:TPSR_Ref TObjectID="43859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4466.000000 -257.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43860" ObjectName="SW-CX_DYYDB.CX_DYYDB_03567SW"/>
     <cge:Meas_Ref ObjectId="272774"/>
    <cge:TPSR_Ref TObjectID="43860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272800">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.000000 -393.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43862" ObjectName="SW-CX_DYYDB.CX_DYYDB_0361SW"/>
     <cge:Meas_Ref ObjectId="272800"/>
    <cge:TPSR_Ref TObjectID="43862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272855">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4563.000000 -261.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43863" ObjectName="SW-CX_DYYDB.CX_DYYDB_03667SW"/>
     <cge:Meas_Ref ObjectId="272855"/>
    <cge:TPSR_Ref TObjectID="43863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4656.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43865" ObjectName="SW-CX_DYYDB.CX_DYYDB_0371SW"/>
     <cge:Meas_Ref ObjectId="272831"/>
    <cge:TPSR_Ref TObjectID="43865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3533.000000 -494.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3499.000000 -636.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -475.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43864" ObjectName="SW-CX_DYYDB.CX_DYYDB_0903SW"/>
     <cge:Meas_Ref ObjectId="272824"/>
    <cge:TPSR_Ref TObjectID="43864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272693">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43850" ObjectName="SW-CX_DYYDB.CX_DYYDB_0321SW"/>
     <cge:Meas_Ref ObjectId="272693"/>
    <cge:TPSR_Ref TObjectID="43850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4138.000000 -260.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43851" ObjectName="SW-CX_DYYDB.CX_DYYDB_03267SW"/>
     <cge:Meas_Ref ObjectId="272694"/>
    <cge:TPSR_Ref TObjectID="43851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43853" ObjectName="SW-CX_DYYDB.CX_DYYDB_0331SW"/>
     <cge:Meas_Ref ObjectId="272720"/>
    <cge:TPSR_Ref TObjectID="43853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272721">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4236.000000 -261.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43854" ObjectName="SW-CX_DYYDB.CX_DYYDB_03367SW"/>
     <cge:Meas_Ref ObjectId="272721"/>
    <cge:TPSR_Ref TObjectID="43854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -379.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43856" ObjectName="SW-CX_DYYDB.CX_DYYDB_0341SW"/>
     <cge:Meas_Ref ObjectId="272747"/>
    <cge:TPSR_Ref TObjectID="43856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272854">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4341.000000 -257.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43857" ObjectName="SW-CX_DYYDB.CX_DYYDB_03467SW"/>
     <cge:Meas_Ref ObjectId="272854"/>
    <cge:TPSR_Ref TObjectID="43857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5839.000000 -614.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_128fb20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5867.500000 -978.500000)" xlink:href="#voltageTransformer:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_129ed10">
    <use class="BV-0KV" transform="matrix(1.250000 -0.000000 0.000000 1.928571 4751.000000 -772.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a1100">
    <use class="BV-0KV" transform="matrix(1.250000 -0.000000 0.000000 1.928571 5812.000000 -767.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1626ef0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4556.500000 -1093.500000)" xlink:href="#voltageTransformer:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4785,-1199 4785,-1245 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4785,-1199 4785,-1245 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5015,-1198 5015,-1243 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5015,-1198 5015,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5422,-1194 5422,-1240 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5422,-1194 5422,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5957,-601 6024,-601 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5957,-601 6024,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5703,-1201 5703,-1248 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5703,-1201 5703,-1248 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DYYDB.CX_YAYDB_3T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="20204"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 0.742424 0.774510 0.000000 4303.500000 -990.500000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(-0.000000 0.742424 0.774510 0.000000 4303.500000 -990.500000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="43866" ObjectName="TF-CX_DYYDB.CX_YAYDB_3T"/>
    <cge:TPSR_Ref TObjectID="43866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.869565 -0.000000 0.000000 -0.897959 4652.000000 -128.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.869565 -0.000000 0.000000 -0.897959 4652.000000 -128.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1982550">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4891.500000 -1162.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13105e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 -1125.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f8390">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5121.500000 -1162.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f8c70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5150.000000 -1125.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b0420">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4820.500000 -768.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140a680">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5809.500000 -1162.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1276fa0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5838.000000 -1125.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_129c050">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5528.500000 -1163.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_129cfa0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5557.000000 -1126.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c09e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5482.000000 -648.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c15b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5454.000000 -660.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135ddc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6366.000000 -614.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d9d10">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6355.500000 -1026.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13da7f0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6412.000000 -1029.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ce010">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6294.500000 -828.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ce890">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6340.000000 -831.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b57e0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6226.500000 -627.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135a870">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6274.000000 -630.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132f710">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6408.500000 -641.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132ff90">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6454.000000 -644.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1330db0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6306.000000 -994.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b2590">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6350.500000 -276.246531)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c6e00">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 6407.000000 -278.746531)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e4ff0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5940.000000 -984.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e5da0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5932.500000 -1029.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1369330">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5881.500000 -767.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1279600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6249.000000 -800.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127ad30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6310.000000 -241.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127c200">
    <use class="BV-0KV" transform="matrix(0.943820 -0.000000 0.000000 -0.764706 6289.000000 -286.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_162a230">
    <use class="BV-110KV" transform="matrix(0.640449 -0.000000 0.000000 -1.000000 4562.000000 -953.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_130df90">
    <use class="BV-10KV" transform="matrix(0.640449 -0.000000 0.000000 -1.000000 4237.000000 -953.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_130ec70">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4154.500000 -1011.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ec6e0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4481.500000 -936.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ee500">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4297.500000 -1131.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f3250">
    <use class="BV-110KV" transform="matrix(0.000000 -0.700000 -0.740000 -0.000000 4352.000000 -1073.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_195e7d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3493.000000 -238.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1322300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3462.000000 -164.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132b140">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -238.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132c830">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 -164.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b0ce0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -234.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b23d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -160.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12847f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 -235.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1285ee0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -161.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1611ac0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -235.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16131b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -161.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12aefb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4008.000000 -239.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b8fd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4103.000000 -239.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ba460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 -165.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15ff0d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4521.000000 -236.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1607bb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -240.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e9470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4656.000000 -310.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f2380">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3533.000000 -646.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f30c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3524.000000 -717.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f4e50">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4372.000000 -672.000000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f9d80">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3586.000000 -656.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b1170">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4325.000000 -604.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b2bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -561.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c1c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -239.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c30b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 -165.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1967060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -240.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19684f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.000000 -166.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1973c30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 -236.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19750c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -162.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19cce30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5316.000000 -491.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19cd960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5212.000000 -555.000000)" xlink:href="#lightningRod:shape103"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d64c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5620.000000 -677.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3047.500000 -1139.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62641" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3077.538462 -968.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62641" ObjectName="CX_DY:CX_DY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79712" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3076.538462 -929.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79712" ObjectName="CX_DY:CX_DY_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208330" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3071.538462 -1045.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208330" ObjectName="CX_DY:CX_DY_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239628" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3074.538462 -1006.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239628" ObjectName="CX_DY:CX_DY_sumP2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5719.000000 -830.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -1204.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5081.000000 -1205.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5493.000000 -1201.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5773.000000 -1206.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 -1229.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 -1229.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 -1229.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5265.000000 -905.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5265.000000 -905.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5265.000000 -905.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -1236.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -1236.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -1236.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5636.000000 -1225.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5636.000000 -1225.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5636.000000 -1225.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5384.000000 -1227.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5384.000000 -1227.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5384.000000 -1227.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -843.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -843.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -843.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5704.000000 -900.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5704.000000 -900.239382) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5704.000000 -900.239382) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-62472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5779.000000 -762.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-62473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5779.000000 -762.239382) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-62469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5779.000000 -762.239382) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-26113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5742.000000 -846.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4313"/>
     <cge:Term_Ref ObjectID="6275"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 -1018.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 -1018.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 -1018.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 -1018.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 -1018.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 -1018.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -900.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -900.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -900.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -900.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -900.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -900.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-155480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6061.000000 -1193.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-155481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6061.000000 -1193.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-155482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6061.000000 -1193.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-155486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6061.000000 -1193.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-155483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6061.000000 -1193.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-155487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6061.000000 -1193.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6540.000000 -1048.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6540.000000 -1048.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6540.000000 -1048.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6553.000000 -870.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6553.000000 -870.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6553.000000 -870.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6528.000000 -687.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6528.000000 -687.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6528.000000 -687.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6527.000000 -421.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6527.000000 -421.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6527.000000 -421.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6525.000000 -311.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6525.000000 -311.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6525.000000 -311.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4470.000000 -907.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43840"/>
     <cge:Term_Ref ObjectID="20150"/>
    <cge:TPSR_Ref TObjectID="43840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4470.000000 -907.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43840"/>
     <cge:Term_Ref ObjectID="20150"/>
    <cge:TPSR_Ref TObjectID="43840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4470.000000 -907.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43840"/>
     <cge:Term_Ref ObjectID="20150"/>
    <cge:TPSR_Ref TObjectID="43840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -1012.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43844"/>
     <cge:Term_Ref ObjectID="20158"/>
    <cge:TPSR_Ref TObjectID="43844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -1012.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43844"/>
     <cge:Term_Ref ObjectID="20158"/>
    <cge:TPSR_Ref TObjectID="43844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -1012.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43844"/>
     <cge:Term_Ref ObjectID="20158"/>
    <cge:TPSR_Ref TObjectID="43844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.000000 -20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43846"/>
     <cge:Term_Ref ObjectID="20162"/>
    <cge:TPSR_Ref TObjectID="43846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.000000 -20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43846"/>
     <cge:Term_Ref ObjectID="20162"/>
    <cge:TPSR_Ref TObjectID="43846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.000000 -20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43846"/>
     <cge:Term_Ref ObjectID="20162"/>
    <cge:TPSR_Ref TObjectID="43846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -26.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43849"/>
     <cge:Term_Ref ObjectID="20168"/>
    <cge:TPSR_Ref TObjectID="43849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -26.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43849"/>
     <cge:Term_Ref ObjectID="20168"/>
    <cge:TPSR_Ref TObjectID="43849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -26.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43849"/>
     <cge:Term_Ref ObjectID="20168"/>
    <cge:TPSR_Ref TObjectID="43849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -27.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43852"/>
     <cge:Term_Ref ObjectID="20174"/>
    <cge:TPSR_Ref TObjectID="43852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -27.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43852"/>
     <cge:Term_Ref ObjectID="20174"/>
    <cge:TPSR_Ref TObjectID="43852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -27.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43852"/>
     <cge:Term_Ref ObjectID="20174"/>
    <cge:TPSR_Ref TObjectID="43852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -27.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43855"/>
     <cge:Term_Ref ObjectID="20180"/>
    <cge:TPSR_Ref TObjectID="43855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -27.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43855"/>
     <cge:Term_Ref ObjectID="20180"/>
    <cge:TPSR_Ref TObjectID="43855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -27.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43855"/>
     <cge:Term_Ref ObjectID="20180"/>
    <cge:TPSR_Ref TObjectID="43855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -29.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43858"/>
     <cge:Term_Ref ObjectID="20186"/>
    <cge:TPSR_Ref TObjectID="43858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -29.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43858"/>
     <cge:Term_Ref ObjectID="20186"/>
    <cge:TPSR_Ref TObjectID="43858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -29.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43858"/>
     <cge:Term_Ref ObjectID="20186"/>
    <cge:TPSR_Ref TObjectID="43858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-272641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4603.000000 -30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43861"/>
     <cge:Term_Ref ObjectID="20192"/>
    <cge:TPSR_Ref TObjectID="43861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-272642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4603.000000 -30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43861"/>
     <cge:Term_Ref ObjectID="20192"/>
    <cge:TPSR_Ref TObjectID="43861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-272638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4603.000000 -30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43861"/>
     <cge:Term_Ref ObjectID="20192"/>
    <cge:TPSR_Ref TObjectID="43861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-272506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -555.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43838"/>
     <cge:Term_Ref ObjectID="20147"/>
    <cge:TPSR_Ref TObjectID="43838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-272507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -555.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43838"/>
     <cge:Term_Ref ObjectID="20147"/>
    <cge:TPSR_Ref TObjectID="43838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-272508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -555.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43838"/>
     <cge:Term_Ref ObjectID="20147"/>
    <cge:TPSR_Ref TObjectID="43838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-272512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -555.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43838"/>
     <cge:Term_Ref ObjectID="20147"/>
    <cge:TPSR_Ref TObjectID="43838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-272509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -555.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43838"/>
     <cge:Term_Ref ObjectID="20147"/>
    <cge:TPSR_Ref TObjectID="43838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-272513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -555.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43838"/>
     <cge:Term_Ref ObjectID="20147"/>
    <cge:TPSR_Ref TObjectID="43838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-272505" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4470.000000 -846.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272505" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43866"/>
     <cge:Term_Ref ObjectID="20205"/>
    <cge:TPSR_Ref TObjectID="43866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-272504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4470.000000 -846.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="272504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43866"/>
     <cge:Term_Ref ObjectID="20205"/>
    <cge:TPSR_Ref TObjectID="43866"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3059" y="-1198"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3059" y="-1198"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3011" y="-1215"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3011" y="-1215"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3233,-1207 3230,-1210 3230,-1148 3233,-1151 3233,-1207" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3233,-1207 3230,-1210 3289,-1210 3286,-1207 3233,-1207" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3233,-1151 3230,-1148 3289,-1148 3286,-1151 3233,-1151" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3286,-1207 3289,-1210 3289,-1148 3286,-1151 3286,-1207" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="56" stroke="rgb(255,255,255)" width="53" x="3233" y="-1207"/>
     <rect fill="none" height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="3233" y="-1207"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="31" qtmmishow="hidden" width="101" x="3012" y="-790"/>
    </a>
   <metadata/><rect fill="white" height="31" opacity="0" stroke="white" transform="" width="101" x="3012" y="-790"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3313" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3313" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3313" y="-1212"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3313" y="-1212"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="31" qtmmishow="hidden" width="101" x="3010" y="-719"/>
    </a>
   <metadata/><rect fill="white" height="31" opacity="0" stroke="white" transform="" width="101" x="3010" y="-719"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="22" x="5671" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="5671" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="5454" y="-749"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="5454" y="-749"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4793" y="-1017"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4793" y="-1017"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="5023" y="-1016"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="5023" y="-1016"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="24" x="5237" y="-1009"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="24" x="5237" y="-1009"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5431" y="-1017"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5431" y="-1017"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5712" y="-1016"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5712" y="-1016"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="83" x="4300" y="-926"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="83" x="4300" y="-926"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4086" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4086" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4176" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4176" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4274" y="-358"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4274" y="-358"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4379" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4379" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4504" y="-364"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4504" y="-364"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4601" y="-367"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4601" y="-367"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3059" y="-1198"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3011" y="-1215"/></g>
   <g href="AVC大姚站.svg" style="fill-opacity:0"><rect height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="3233" y="-1207"/></g>
   <g href="110kV大姚变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="31" qtmmishow="hidden" width="101" x="3012" y="-790"/></g>
   <g href="cx_配调_配网接线图110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3313" y="-1177"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3313" y="-1212"/></g>
   <g href="110kV大姚变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="31" qtmmishow="hidden" width="101" x="3010" y="-719"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="5671" y="-1209"/></g>
   <g href="110kV大姚变110kV2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="5454" y="-749"/></g>
   <g href="110kV大姚变110kV元大线151断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4793" y="-1017"/></g>
   <g href="110kV大姚变110kV姚大线152断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="5023" y="-1016"/></g>
   <g href="110kV大姚变110kV分段112断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="24" x="5237" y="-1009"/></g>
   <g href="110kV大姚变110kV渔大线153断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5431" y="-1017"/></g>
   <g href="110kV大姚变110kV大六线154断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5712" y="-1016"/></g>
   <g href="110kV大姚移动变110kV3号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="83" x="4300" y="-926"/></g>
   <g href="110kV大姚移动变10kV中东Ⅱ回线031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4086" y="-357"/></g>
   <g href="110kV大姚移动变10kV中芦线032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4176" y="-357"/></g>
   <g href="110kV大姚移动变10kV中金线033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4274" y="-358"/></g>
   <g href="110kV大姚移动变10kV中李线034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4379" y="-354"/></g>
   <g href="110kV大姚移动变10kV母线联络线二035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4504" y="-364"/></g>
   <g href="110kV大姚移动变10kV母线联络线一036间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4601" y="-367"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="6546" x2="6551" y1="-513" y2="-513"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="6476" x2="6481" y1="-665" y2="-661"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="6699" x2="6704" y1="-538" y2="-538"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3416" x2="3416" y1="-800" y2="154"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3416" x2="4032" y1="154" y2="154"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4032" x2="4032" y1="154" y2="-800"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3416" x2="4032" y1="-800" y2="-800"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 5496.000000 -637.050757)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 5496.000000 -637.050757)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 5496.000000 -637.050757)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 -65.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -61.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -62.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -62.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 -66.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 -66.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.000000 -67.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -63.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_13630a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5195,-915 5195,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5195,-915 5195,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1980560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5310,-915 5310,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5310,-915 5310,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19802e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5195,-963 5195,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5195,-963 5195,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1984020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5310,-969 5310,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5310,-969 5310,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12ad7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5195,-987 5237,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5195,-987 5237,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_139a6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5264,-987 5310,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5264,-987 5310,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_139a9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5195,-987 5195,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5195,-987 5195,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_125cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5195,-1043 5195,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1433140@0" Pin0InfoVect0LinkObjId="g_1433140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5195,-1043 5195,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c5f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5310,-987 5310,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5310,-987 5310,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13a5cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5310,-1043 5310,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13107d0@0" Pin0InfoVect0LinkObjId="g_13107d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5310,-1043 5310,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1980a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-965 4785,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-965 4785,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_197fca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-981 4785,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-981 4785,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1982360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1022 4785,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1022 4785,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13e69d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1035 4785,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1035 4785,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1981820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1035 4802,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1035 4802,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13103c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4838,-1035 4852,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1269be0@0" Pin0InfoVect0LinkObjId="g_1269be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4838,-1035 4852,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12699c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-1100 4852,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1267d40@0" Pin0InfoVect0LinkObjId="g_1267d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-1100 4852,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13e9390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1085 4785,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_13105e0@0" ObjectIDZND2="g_1982550@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13105e0_0" Pin0InfoVect2LinkObjId="g_1982550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1085 4785,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13e95b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1100 4803,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_13105e0@0" ObjectIDND2="g_1982550@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13105e0_0" Pin1InfoVect2LinkObjId="g_1982550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1100 4803,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1357c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-1168 4898,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_13105e0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1982550@0" Pin0InfoVect0LinkObjId="g_1982550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13105e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-1168 4898,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1357e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-1168 4928,-1168 4928,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1982550@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_13105e0@0" Pin0InfoVect0LinkObjId="g_13105e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1982550_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-1168 4928,-1168 4928,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1276700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-915 4785,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-915 4785,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13f7e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-965 5015,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-965 5015,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13f8030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-981 5015,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-981 5015,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12bf930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1022 5015,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1022 5015,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12bfb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1035 5015,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1035 5015,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14092b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-981 5032,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-981 5032,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14094d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5068,-981 5082,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1433660@0" Pin0InfoVect0LinkObjId="g_1433660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5068,-981 5082,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14096f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1035 5032,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1035 5032,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1409910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5068,-1035 5082,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13c2ec0@0" Pin0InfoVect0LinkObjId="g_13c2ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5068,-1035 5082,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1433440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-1100 5082,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1380b50@0" Pin0InfoVect0LinkObjId="g_1380b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-1100 5082,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1257600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1085 5015,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_13f8c70@0" ObjectIDZND2="g_13f8390@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13f8c70_0" Pin0InfoVect2LinkObjId="g_13f8390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1085 5015,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1257860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1100 5033,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_13f8c70@0" ObjectIDND2="g_13f8390@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13f8c70_0" Pin1InfoVect2LinkObjId="g_13f8390_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1100 5033,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1257ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-1168 5128,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_13f8c70@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_13f8390@0" Pin0InfoVect0LinkObjId="g_13f8390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13f8c70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-1168 5128,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1257d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5128,-1168 5158,-1168 5158,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_13f8390@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_13f8c70@0" Pin0InfoVect0LinkObjId="g_13f8c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13f8390_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5128,-1168 5158,-1168 5158,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13f8a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-915 5015,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-915 5015,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13f9ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-981 4802,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-981 4802,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13fa220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4838,-981 4852,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13fa480@0" Pin0InfoVect0LinkObjId="g_13fa480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4838,-981 4852,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_134f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4800,-805 4817,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_15b0420@0" ObjectIDND2="g_129ed10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_15b0420_0" Pin1InfoVect2LinkObjId="g_129ed10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4800,-805 4817,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_134f650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-805 4867,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_134f8b0@0" Pin0InfoVect0LinkObjId="g_134f8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-805 4867,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13d0330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4800,-879 4817,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4800,-879 4817,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13d0590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-879 4867,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13d07f0@0" Pin0InfoVect0LinkObjId="g_13d07f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-879 4867,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15afd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-820 4799,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_15b0420@0" ObjectIDZND2="g_129ed10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_15b0420_0" Pin0InfoVect2LinkObjId="g_129ed10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-820 4799,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15aff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-915 4799,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-915 4799,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b01c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-879 4799,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-879 4799,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1399750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-781 4775,-781 4775,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_15b0420@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_129ed10@0" Pin0InfoVect0LinkObjId="g_129ed10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15b0420_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-781 4775,-781 4775,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13999b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-764 4827,-781 4799,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_15b0420@0" ObjectIDZND0="g_129ed10@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_129ed10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15b0420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-764 4827,-781 4799,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1399c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-781 4799,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_15b0420@0" ObjectIDND1="g_129ed10@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15b0420_0" Pin1InfoVect1LinkObjId="g_129ed10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-781 4799,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1349710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-964 5703,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-964 5703,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1349970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-981 5703,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-981 5703,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12d9df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-981 5720,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-981 5720,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12da050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5756,-981 5770,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1310e30@0" Pin0InfoVect0LinkObjId="g_1310e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5756,-981 5770,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1310bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5757,-1100 5770,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13116d0@0" Pin0InfoVect0LinkObjId="g_13116d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5757,-1100 5770,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1409d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1085 5703,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1276fa0@0" ObjectIDZND2="g_140a680@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1276fa0_0" Pin0InfoVect2LinkObjId="g_140a680_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1085 5703,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1409f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1100 5721,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1276fa0@0" ObjectIDND2="g_140a680@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1276fa0_0" Pin1InfoVect2LinkObjId="g_140a680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1100 5721,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_140a1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5816,-1168 5816,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@1" ObjectIDZND0="g_140a680@0" Pin0InfoVect0LinkObjId="g_140a680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5816,-1168 5816,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_140a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5816,-1168 5846,-1168 5846,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@1" ObjectIDZND0="g_1276fa0@0" Pin0InfoVect0LinkObjId="g_1276fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5816,-1168 5846,-1168 5846,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1276d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-915 5703,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-915 5703,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13ccc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-965 5422,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-965 5422,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13ccec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-982 5422,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-982 5422,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13cd120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1023 5422,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1023 5422,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13cd380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1036 5422,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1036 5422,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_133b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-982 5439,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-982 5439,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_133b350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5475,-982 5489,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_133bcd0@0" Pin0InfoVect0LinkObjId="g_133bcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5475,-982 5489,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_133b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1036 5439,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1036 5439,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_133b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5475,-1036 5489,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_15b06e0@0" Pin0InfoVect0LinkObjId="g_15b06e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5475,-1036 5489,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_133ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5476,-1101 5489,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_15b0f80@0" Pin0InfoVect0LinkObjId="g_15b0f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5476,-1101 5489,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15b1990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1086 5422,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_129cfa0@0" ObjectIDZND2="g_129c050@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_129cfa0_0" Pin0InfoVect2LinkObjId="g_129c050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1086 5422,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15b1bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1101 5440,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_129cfa0@0" ObjectIDND2="g_129c050@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_129cfa0_0" Pin1InfoVect2LinkObjId="g_129c050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1101 5440,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15b1e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5535,-1169 5535,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_129cfa0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_129c050@0" Pin0InfoVect0LinkObjId="g_129c050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_129cfa0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5535,-1169 5535,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15b20b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5535,-1169 5565,-1169 5565,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_129c050@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_129cfa0@0" Pin0InfoVect0LinkObjId="g_129cfa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_129c050_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5535,-1169 5565,-1169 5565,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_129cd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-915 5422,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-915 5422,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12abce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-915 4992,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-915 4992,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13ce9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-705 4992,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-705 4992,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13cec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-847 5009,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-847 5009,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13cee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5045,-847 5059,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13cf0e0@0" Pin0InfoVect0LinkObjId="g_13cf0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5045,-847 5059,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c4f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-832 4992,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-832 4992,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c5190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-847 4992,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-847 4992,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c53f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-795 5009,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-795 5009,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c5650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5045,-795 5059,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13c58b0@0" Pin0InfoVect0LinkObjId="g_13c58b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5045,-795 5059,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c0520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-782 4992,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-782 4992,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c0780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-795 4992,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4992,-795 4992,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b3990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-844 5556,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-844 5556,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15b3bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-844 5606,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_15b3e50@0" Pin0InfoVect0LinkObjId="g_15b3e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-844 5606,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_141f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-796 5556,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-796 5556,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_141f370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-796 5606,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_141f5d0@0" Pin0InfoVect0LinkObjId="g_141f5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-796 5606,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13040d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5431,-558 5579,-558 5579,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5431,-558 5579,-558 5579,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144f9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5771,-686 5792,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5771,-686 5792,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-412 6069,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6025,-412 6069,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6105,-412 6141,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6105,-412 6141,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6168,-412 6191,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6168,-412 6191,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6227,-412 6430,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6227,-412 6430,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-477 5978,-477 5978,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6025,-477 5978,-477 5978,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135ed60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5978,-529 5978,-546 6027,-546 6025,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5978,-529 5978,-546 6027,-546 6025,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-1043 6054,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6025,-1043 6054,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144c780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6177,-1043 6200,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6177,-1043 6200,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144c9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6236,-1043 6250,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6236,-1043 6250,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144cc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6250,-1043 6445,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6250,-1043 6445,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_144cea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6250,-1043 6250,-1017 6264,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6250,-1043 6250,-1017 6264,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13da590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6350,-1017 6376,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13d9d10@0" ObjectIDZND0="g_13da7f0@0" Pin0InfoVect0LinkObjId="g_13da7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d9d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6350,-1017 6376,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1396010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-845 6087,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6025,-845 6087,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1396270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6123,-845 6150,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6123,-845 6150,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13964d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6177,-845 6200,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6177,-845 6200,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1396730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6236,-845 6250,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_12ce010@0" ObjectIDZND1="g_1279600@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_12ce010_0" Pin0InfoVect1LinkObjId="g_1279600_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6236,-845 6250,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1396990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6250,-845 6445,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_12ce010@0" ObjectIDND2="g_1279600@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_12ce010_0" Pin1InfoVect2LinkObjId="g_1279600_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6250,-845 6445,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1275cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6250,-845 6250,-871 6264,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_12ce010@0" ObjectIDND2="g_1279600@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_12ce010_0" Pin1InfoVect2LinkObjId="g_1279600_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6250,-845 6250,-871 6264,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1275f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6300,-871 6312,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1276190@0" Pin0InfoVect0LinkObjId="g_1276190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6300,-871 6312,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13b5580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-668 6079,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6025,-668 6079,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135af70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6238,-618 6221,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_135a870@0" ObjectIDZND0="g_13b57e0@0" Pin0InfoVect0LinkObjId="g_13b57e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_135a870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6238,-618 6221,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_135b1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6190,-618 6179,-618 6179,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13b57e0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b57e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6190,-618 6179,-618 6179,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1330690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6418,-632 6403,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_132ff90@0" ObjectIDZND0="g_132f710@0" Pin0InfoVect0LinkObjId="g_132f710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_132ff90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6418,-632 6403,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13308f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6372,-632 6362,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_132f710@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_132f710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6372,-632 6362,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1330b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6304,-819 6289,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12ce890@0" ObjectIDZND0="g_12ce010@0" Pin0InfoVect0LinkObjId="g_12ce010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ce890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6304,-819 6289,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1331b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5999,-1020 6025,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5999,-1020 6025,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1403f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6172,-293 6195,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6172,-293 6195,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14041f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6231,-293 6245,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_127c200@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_127c200_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6231,-293 6245,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13c6ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6345,-267 6371,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13b2590@0" ObjectIDZND0="g_13c6e00@0" Pin0InfoVect0LinkObjId="g_13c6e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b2590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6345,-267 6371,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1256ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5947,-1070 5947,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1256e00@0" Pin0InfoVect0LinkObjId="g_1256e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5947,-1070 5947,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12e6620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5861,-1020 5896,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_128fb20@0" ObjectIDZND0="g_12e5da0@1" Pin0InfoVect0LinkObjId="g_12e5da0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_128fb20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5861,-1020 5896,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12e6880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5861,-804 5878,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1369330@0" ObjectIDND2="g_12a1100@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1369330_0" Pin1InfoVect2LinkObjId="g_12a1100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5861,-804 5878,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12e6ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5914,-804 5928,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_12e6d40@0" Pin0InfoVect0LinkObjId="g_12e6d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5914,-804 5928,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12e77d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5861,-878 5878,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5861,-878 5878,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12e7a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5914,-878 5928,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_12e7c90@0" Pin0InfoVect0LinkObjId="g_12e7c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5914,-878 5928,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13684f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5860,-819 5860,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1369330@0" ObjectIDZND2="g_12a1100@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1369330_0" Pin0InfoVect2LinkObjId="g_12a1100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5860,-819 5860,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1368750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5860,-915 5860,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5860,-915 5860,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13689b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5860,-878 5860,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5860,-878 5860,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1368c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5860,-780 5836,-780 5836,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1369330@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_12a1100@0" Pin0InfoVect0LinkObjId="g_12a1100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1369330_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5860,-780 5836,-780 5836,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1368e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5888,-763 5888,-780 5860,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1369330@0" ObjectIDZND0="g_12a1100@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_12a1100_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1369330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5888,-763 5888,-780 5860,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13690d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5860,-780 5860,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1369330@0" ObjectIDND1="g_12a1100@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1369330_0" Pin1InfoVect1LinkObjId="g_12a1100_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5860,-780 5860,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15a4b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5323,-558 5395,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_19cce30@0" ObjectIDND1="g_19cd960@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19cce30_0" Pin1InfoVect1LinkObjId="g_19cd960_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5323,-558 5395,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15a58a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-735 5556,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-735 5556,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15a5a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-735 5606,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_15a5c80@0" Pin0InfoVect0LinkObjId="g_15a5c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-735 5606,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_131e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-892 5538,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-892 5538,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_131e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-844 5538,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-844 5538,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_131e750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-844 5538,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-844 5538,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_131e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-796 5538,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-796 5538,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_131eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-784 5538,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-784 5538,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1633680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-293 6082,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6025,-293 6082,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1633870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6118,-293 6145,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6118,-293 6145,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1633a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6245,-293 6245,-319 6259,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_127c200@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_127c200_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6245,-293 6245,-319 6259,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1633c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6295,-319 6307,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_13af760@0" Pin0InfoVect0LinkObjId="g_13af760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6295,-319 6307,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1633ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6245,-293 6245,-267 6262,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_127c200@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_127c200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6245,-293 6245,-267 6262,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16371a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5962,-1020 5947,-1020 5947,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5962,-1020 5947,-1020 5947,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1637400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5935,-990 5947,-990 5947,-1020 5927,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12e4ff0@0" ObjectIDZND0="g_12e5da0@0" Pin0InfoVect0LinkObjId="g_12e5da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12e4ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5935,-990 5947,-990 5947,-1020 5927,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1637670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5431,-707 5431,-714 5461,-714 5461,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5431,-707 5431,-714 5461,-714 5461,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1278c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6310,-987 6310,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1330db0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_13d9d10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13d9d10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1330db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6310,-987 6310,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1278ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6300,-1017 6310,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1330db0@0" ObjectIDZND1="g_13d9d10@0" Pin0InfoVect0LinkObjId="g_1330db0_0" Pin0InfoVect1LinkObjId="g_13d9d10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6300,-1017 6310,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1279140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6310,-1017 6319,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1330db0@0" ObjectIDZND0="g_13d9d10@1" Pin0InfoVect0LinkObjId="g_13d9d10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1330db0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6310,-1017 6319,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12793a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6319,-668 6319,-632 6326,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6319,-668 6319,-632 6326,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_127a3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6250,-819 6250,-793 6253,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_12ce010@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1279600@0" Pin0InfoVect0LinkObjId="g_1279600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12ce010_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6250,-819 6250,-793 6253,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_127a610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6258,-819 6250,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12ce010@1" ObjectIDZND0="g_1279600@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1279600_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ce010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6258,-819 6250,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_127a870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6250,-819 6250,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_12ce010@0" ObjectIDND1="g_1279600@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_12ce010_0" Pin1InfoVect1LinkObjId="g_1279600_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6250,-819 6250,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_127aad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6170,-668 6179,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_13b57e0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_13b57e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6170,-668 6179,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_127bae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6314,-234 6306,-234 6306,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_127ad30@0" ObjectIDZND0="0@x" ObjectIDZND1="g_13b2590@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13b2590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127ad30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6314,-234 6306,-234 6306,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_127bd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6298,-267 6306,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_127ad30@0" ObjectIDZND1="g_13b2590@0" Pin0InfoVect0LinkObjId="g_127ad30_0" Pin0InfoVect1LinkObjId="g_13b2590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6298,-267 6306,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_127bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6306,-267 6314,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_127ad30@0" ObjectIDZND0="g_13b2590@1" Pin0InfoVect0LinkObjId="g_13b2590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_127ad30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6306,-267 6314,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_127cc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6245,-293 6293,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_127c200@1" Pin0InfoVect0LinkObjId="g_127c200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6245,-293 6293,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_127cee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6368,-293 6431,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_127c200@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127c200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6368,-293 6431,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_127d140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1168 4785,-1201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_13105e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_13105e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1168 4785,-1201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_127d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1100 4785,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_13105e0@0" ObjectIDZND1="g_1982550@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_13105e0_0" Pin0InfoVect1LinkObjId="g_1982550_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1100 4785,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_127d600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1168 4898,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@1" ObjectIDZND0="g_13105e0@0" ObjectIDZND1="g_1982550@0" Pin0InfoVect0LinkObjId="g_13105e0_0" Pin0InfoVect1LinkObjId="g_1982550_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1168 4898,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_129dc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1168 5422,-1194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_129cfa0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_129cfa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1168 5422,-1194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_129ded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1101 5422,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_129cfa0@0" ObjectIDZND1="g_129c050@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_129cfa0_0" Pin0InfoVect1LinkObjId="g_129c050_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1101 5422,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_129e130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-1169 5535,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@1" ObjectIDZND0="g_129cfa0@0" ObjectIDZND1="g_129c050@0" Pin0InfoVect0LinkObjId="g_129cfa0_0" Pin0InfoVect1LinkObjId="g_129c050_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-1169 5535,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_129e390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1169 5703,-1202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1276fa0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1276fa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1169 5703,-1202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_129e5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1100 5703,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1276fa0@0" ObjectIDZND1="g_140a680@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_1276fa0_0" Pin0InfoVect1LinkObjId="g_140a680_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1100 5703,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_129e850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1168 5816,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@1" ObjectIDZND0="g_1276fa0@0" ObjectIDZND1="g_140a680@0" Pin0InfoVect0LinkObjId="g_1276fa0_0" Pin0InfoVect1LinkObjId="g_140a680_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1168 5816,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_129eab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6361,-632 6361,-607 6370,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_135ddc0@0" Pin0InfoVect0LinkObjId="g_135ddc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6361,-632 6361,-607 6370,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12c3cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5461,-714 5488,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="g_13c15b0@0" ObjectIDZND0="g_13c09e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_13c09e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13c15b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5461,-714 5488,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12c3f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-714 5488,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="g_13c15b0@0" ObjectIDND1="0@x" ObjectIDZND0="g_13c09e0@0" Pin0InfoVect0LinkObjId="g_13c09e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13c15b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-714 5488,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12c4190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5488,-714 5534,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="g_13c09e0@0" ObjectIDND1="g_13c15b0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13c09e0_0" Pin1InfoVect1LinkObjId="g_13c15b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5488,-714 5534,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12c43f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-748 5538,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-748 5538,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12c4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5538,-729 5538,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5538,-729 5538,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12c6880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1168 5015,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_13f8c70@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_13f8c70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1168 5015,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12c6b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1100 5015,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_13f8c70@0" ObjectIDZND1="g_13f8390@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_13f8c70_0" Pin0InfoVect1LinkObjId="g_13f8390_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1100 5015,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12c6d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-1168 5128,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@1" ObjectIDZND0="g_13f8c70@0" ObjectIDZND1="g_13f8390@0" Pin0InfoVect0LinkObjId="g_13f8c70_0" Pin0InfoVect1LinkObjId="g_13f8390_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-1168 5128,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6090,-1043 6150,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6090,-1043 6150,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19ba8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6291,-666 6291,-692 6305,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6291,-666 6291,-692 6305,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19bab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6341,-692 6353,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_19bad90@0" Pin0InfoVect0LinkObjId="g_19bad90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6341,-692 6353,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19bb820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6261,-668 6291,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6261,-668 6291,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19bba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6438,-668 6319,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6438,-668 6319,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19bbce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6319,-668 6291,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6319,-668 6291,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_125dad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6177,-726 6189,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_125dd30@0" Pin0InfoVect0LinkObjId="g_125dd30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6177,-726 6189,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_125ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6141,-726 6128,-726 6128,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6141,-726 6128,-726 6128,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_125eea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6116,-668 6128,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6116,-668 6128,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_125f090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6128,-668 6143,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6128,-668 6143,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1261630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6255,-715 6267,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1261890@0" Pin0InfoVect0LinkObjId="g_1261890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6255,-715 6267,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1262810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6219,-715 6203,-715 6203,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_13b57e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_13b57e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6219,-715 6203,-715 6203,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1262a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6179,-668 6203,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_13b57e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13b57e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6179,-668 6203,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1262bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6203,-668 6225,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_13b57e0@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_13b57e0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6203,-668 6225,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_162de70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5632,-1040 5649,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_162d3e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_162d3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5632,-1040 5649,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_162e0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5685,-1040 5703,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_162a230@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_162a230_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5685,-1040 5703,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_162e330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1049 5703,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_162a230@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_162a230_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1049 5703,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1623ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-491 4118,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43845@0" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_15f9b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-491 4118,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1625f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-962 4118,-962 4118,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43844@1" ObjectIDZND0="43845@1" Pin0InfoVect0LinkObjId="SW-272654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272628_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-962 4118,-962 4118,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1626a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1022 5703,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_162a230@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_162a230_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1022 5703,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1626c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1031 5703,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_162a230@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_162a230_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1031 5703,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1306e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5703,-1031 5776,-1031 5776,-1069 5897,-1069 5897,-1297 4621,-1297 4621,-962 4615,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_162a230@0" Pin0InfoVect0LinkObjId="g_162a230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5703,-1031 5776,-1031 5776,-1069 5897,-1069 5897,-1297 4621,-1297 4621,-962 4615,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_130cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-962 4379,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="43840@1" ObjectIDZND0="43866@1" Pin0InfoVect0LinkObjId="g_12f3d20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272536_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-962 4379,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_130cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4536,-1058 4536,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1626ef0@0" ObjectIDZND0="g_162a230@0" ObjectIDZND1="43841@x" ObjectIDZND2="g_12ec6e0@0" Pin0InfoVect0LinkObjId="g_162a230_0" Pin0InfoVect1LinkObjId="SW-272537_0" Pin0InfoVect2LinkObjId="g_12ec6e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1626ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4536,-1058 4536,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_130dad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-962 4536,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_162a230@1" ObjectIDZND0="g_1626ef0@0" ObjectIDZND1="43841@x" ObjectIDZND2="g_12ec6e0@0" Pin0InfoVect0LinkObjId="g_1626ef0_0" Pin0InfoVect1LinkObjId="SW-272537_0" Pin0InfoVect2LinkObjId="g_12ec6e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_162a230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-962 4536,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_130dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4536,-962 4507,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1626ef0@0" ObjectIDND1="g_162a230@0" ObjectIDND2="g_12ec6e0@0" ObjectIDZND0="43841@1" Pin0InfoVect0LinkObjId="SW-272537_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1626ef0_0" Pin1InfoVect1LinkObjId="g_162a230_0" Pin1InfoVect2LinkObjId="g_12ec6e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4536,-962 4507,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-962 4290,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="43866@0" ObjectIDZND0="g_130df90@0" Pin0InfoVect0LinkObjId="g_130df90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_130cb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-962 4290,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12ed2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-929 4535,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_12ec6e0@0" ObjectIDZND0="g_1626ef0@0" ObjectIDZND1="g_162a230@0" ObjectIDZND2="43841@x" Pin0InfoVect0LinkObjId="g_1626ef0_0" Pin0InfoVect1LinkObjId="g_162a230_0" Pin0InfoVect2LinkObjId="SW-272537_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ec6e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-929 4535,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ed550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-1004 4208,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_130ec70@0" ObjectIDZND0="g_130df90@0" ObjectIDZND1="43844@x" Pin0InfoVect0LinkObjId="g_130df90_0" Pin0InfoVect1LinkObjId="SW-272628_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_130ec70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-1004 4208,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ee040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-962 4208,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_130df90@1" ObjectIDZND0="g_130ec70@0" ObjectIDZND1="43844@x" Pin0InfoVect0LinkObjId="g_130ec70_0" Pin0InfoVect1LinkObjId="SW-272628_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_130df90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-962 4208,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ee2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-962 4179,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_130ec70@0" ObjectIDND1="g_130df90@0" ObjectIDZND0="43844@0" Pin0InfoVect0LinkObjId="SW-272628_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_130ec70_0" Pin1InfoVect1LinkObjId="g_130df90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-962 4179,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f17b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-1098 4309,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43843@0" ObjectIDZND0="g_12f3250@0" Pin0InfoVect0LinkObjId="g_12f3250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-1098 4309,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f22a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-1125 4309,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="43843@x" ObjectIDZND1="g_12f3250@0" Pin0InfoVect0LinkObjId="SW-272539_0" Pin0InfoVect1LinkObjId="g_12f3250_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-1125 4309,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f2500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-1098 4363,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="43843@1" ObjectIDZND0="g_12ee500@0" ObjectIDZND1="g_12f3250@0" ObjectIDZND2="43866@x" Pin0InfoVect0LinkObjId="g_12ee500_0" Pin0InfoVect1LinkObjId="g_12f3250_0" Pin0InfoVect2LinkObjId="g_130cb60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272539_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-1098 4363,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f2ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-1098 4363,-1124 4351,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="43843@x" ObjectIDND1="g_12f3250@0" ObjectIDND2="43866@x" ObjectIDZND0="g_12ee500@0" Pin0InfoVect0LinkObjId="g_12ee500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272539_0" Pin1InfoVect1LinkObjId="g_12f3250_0" Pin1InfoVect2LinkObjId="g_130cb60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-1098 4363,-1124 4351,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f3ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-1076 4309,-1076 4309,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12f3250@1" ObjectIDZND0="43843@x" Pin0InfoVect0LinkObjId="SW-272539_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f3250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-1076 4309,-1076 4309,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f3d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-1076 4363,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_12f3250@0" ObjectIDZND0="43866@x" ObjectIDZND1="43843@x" ObjectIDZND2="g_12ee500@0" Pin0InfoVect0LinkObjId="g_130cb60_0" Pin0InfoVect1LinkObjId="SW-272539_0" Pin0InfoVect2LinkObjId="g_12ee500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f3250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-1076 4363,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f4810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-961 4363,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43866@x" ObjectIDZND0="g_12f3250@0" ObjectIDZND1="43843@x" ObjectIDZND2="g_12ee500@0" Pin0InfoVect0LinkObjId="g_12f3250_0" Pin0InfoVect1LinkObjId="SW-272539_0" Pin0InfoVect2LinkObjId="g_12ee500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_130cb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-961 4363,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f4a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-1076 4363,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_12f3250@0" ObjectIDND1="43866@x" ObjectIDZND0="43843@x" ObjectIDZND1="g_12ee500@0" Pin0InfoVect0LinkObjId="SW-272539_0" Pin0InfoVect1LinkObjId="g_12ee500_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_12f3250_0" Pin1InfoVect1LinkObjId="g_130cb60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-1076 4363,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1954c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-981 4450,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43842@0" ObjectIDZND0="43841@x" ObjectIDZND1="43840@x" Pin0InfoVect0LinkObjId="SW-272537_0" Pin0InfoVect1LinkObjId="SW-272536_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272538_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-981 4450,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1955750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4471,-962 4450,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43841@0" ObjectIDZND0="43842@x" ObjectIDZND1="43840@x" Pin0InfoVect0LinkObjId="SW-272538_0" Pin0InfoVect1LinkObjId="SW-272536_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272537_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4471,-962 4450,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19559b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-962 4432,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="43842@x" ObjectIDND1="43841@x" ObjectIDZND0="43840@0" Pin0InfoVect0LinkObjId="SW-272536_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-272538_0" Pin1InfoVect1LinkObjId="SW-272537_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-962 4432,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19566a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-1038 4450,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1955c10@0" ObjectIDZND0="43842@1" Pin0InfoVect0LinkObjId="SW-272538_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1955c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-1038 4450,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1958970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3467,-450 3467,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3467,-450 3467,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_195b370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3467,-388 3467,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3467,-388 3467,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_195dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3433,-264 3433,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_195dd40@0" Pin0InfoVect0LinkObjId="g_195dd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3433,-264 3433,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3433,-300 3433,-319 3467,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_195e7d0@0" ObjectIDZND2="g_1322300@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_195e7d0_0" Pin0InfoVect2LinkObjId="g_1322300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3433,-300 3433,-319 3467,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1960030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3467,-338 3467,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_195e7d0@0" ObjectIDZND2="g_1322300@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_195e7d0_0" Pin0InfoVect2LinkObjId="g_1322300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3467,-338 3467,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1960290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3500,-292 3500,-308 3467,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_195e7d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1322300@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1322300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_195e7d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3500,-292 3500,-308 3467,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13220a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3467,-319 3467,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_195e7d0@0" ObjectIDZND1="g_1322300@0" Pin0InfoVect0LinkObjId="g_195e7d0_0" Pin0InfoVect1LinkObjId="g_1322300_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3467,-319 3467,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1323050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3467,-308 3467,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_195e7d0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1322300@0" Pin0InfoVect0LinkObjId="g_1322300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_195e7d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3467,-308 3467,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1325320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,-450 3578,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3578,-450 3578,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1327d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,-388 3578,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3578,-388 3578,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_132a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3544,-264 3544,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_132a6b0@0" Pin0InfoVect0LinkObjId="g_132a6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3544,-264 3544,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_132beb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3544,-300 3544,-319 3578,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_132b140@0" ObjectIDZND2="g_132c830@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_132b140_0" Pin0InfoVect2LinkObjId="g_132c830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3544,-300 3544,-319 3578,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_132c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,-338 3578,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_132b140@0" ObjectIDZND2="g_132c830@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_132b140_0" Pin0InfoVect2LinkObjId="g_132c830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3578,-338 3578,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_132c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-292 3611,-308 3578,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_132b140@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_132c830@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_132c830_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_132b140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-292 3611,-308 3578,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_132c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,-319 3578,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_132b140@0" ObjectIDZND1="g_132c830@0" Pin0InfoVect0LinkObjId="g_132b140_0" Pin0InfoVect1LinkObjId="g_132c830_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3578,-319 3578,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_132d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,-308 3578,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_132b140@0" ObjectIDZND0="g_132c830@0" Pin0InfoVect0LinkObjId="g_132c830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_132b140_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3578,-308 3578,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_132d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,-169 3578,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_132c830@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_132c830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3578,-169 3578,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19aaec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-450 3692,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-450 3692,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19ad8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-384 3692,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-384 3692,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19afff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3658,-260 3658,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_19b0250@0" Pin0InfoVect0LinkObjId="g_19b0250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3658,-260 3658,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b1a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3658,-296 3658,-315 3692,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_19b0ce0@0" ObjectIDZND2="g_19b23d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_19b0ce0_0" Pin0InfoVect2LinkObjId="g_19b23d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3658,-296 3658,-315 3692,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b1cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-334 3692,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_19b0ce0@0" ObjectIDZND2="g_19b23d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_19b0ce0_0" Pin0InfoVect2LinkObjId="g_19b23d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-334 3692,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b1f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3725,-288 3725,-304 3692,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_19b0ce0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_19b23d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_19b23d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19b0ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3725,-288 3725,-304 3692,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b2170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-315 3692,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_19b0ce0@0" ObjectIDZND1="g_19b23d0@0" Pin0InfoVect0LinkObjId="g_19b0ce0_0" Pin0InfoVect1LinkObjId="g_19b23d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-315 3692,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b3120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-304 3692,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_19b0ce0@0" ObjectIDZND0="g_19b23d0@0" Pin0InfoVect0LinkObjId="g_19b23d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_19b0ce0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-304 3692,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19b3380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-165 3692,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_19b23d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19b23d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-165 3692,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_127e9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-450 3789,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-450 3789,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12813d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-385 3789,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-385 3789,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1283b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-261 3755,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1283d60@0" Pin0InfoVect0LinkObjId="g_1283d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-261 3755,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1285560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-297 3755,-316 3789,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_12847f0@0" ObjectIDZND2="g_1285ee0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_12847f0_0" Pin0InfoVect2LinkObjId="g_1285ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-297 3755,-316 3789,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12857c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-335 3789,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_12847f0@0" ObjectIDZND2="g_1285ee0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_12847f0_0" Pin0InfoVect2LinkObjId="g_1285ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-335 3789,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1285a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-289 3822,-305 3789,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_12847f0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1285ee0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1285ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12847f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-289 3822,-305 3789,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1285c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-316 3789,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_12847f0@0" ObjectIDZND1="g_1285ee0@0" Pin0InfoVect0LinkObjId="g_12847f0_0" Pin0InfoVect1LinkObjId="g_1285ee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-316 3789,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1286c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-305 3789,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_12847f0@0" ObjectIDZND0="g_1285ee0@0" Pin0InfoVect0LinkObjId="g_1285ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_12847f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-305 3789,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1286e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-166 3789,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1285ee0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1285ee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-166 3789,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_128ae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-450 3885,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-450 3885,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_160e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-385 3885,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-385 3885,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1610dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3851,-261 3851,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1611030@0" Pin0InfoVect0LinkObjId="g_1611030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3851,-261 3851,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1612830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3851,-297 3851,-316 3885,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1611ac0@0" ObjectIDZND2="g_16131b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1611ac0_0" Pin0InfoVect2LinkObjId="g_16131b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3851,-297 3851,-316 3885,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1612a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-335 3885,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1611ac0@0" ObjectIDZND2="g_16131b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1611ac0_0" Pin0InfoVect2LinkObjId="g_16131b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-335 3885,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1612cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-289 3918,-305 3885,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1611ac0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_16131b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_16131b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1611ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-289 3918,-305 3885,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1612f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-316 3885,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1611ac0@0" ObjectIDZND1="g_16131b0@0" Pin0InfoVect0LinkObjId="g_1611ac0_0" Pin0InfoVect1LinkObjId="g_16131b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-316 3885,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1613f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-305 3885,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1611ac0@0" ObjectIDZND0="g_16131b0@0" Pin0InfoVect0LinkObjId="g_16131b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1611ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-305 3885,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1614160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-166 3885,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_16131b0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16131b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-166 3885,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1619e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-450 3982,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-450 3982,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_161c870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-389 3982,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-389 3982,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12ae2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3948,-265 3948,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_12ae520@0" Pin0InfoVect0LinkObjId="g_12ae520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3948,-265 3948,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12afd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3948,-301 3948,-320 3982,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_12aefb0@0" ObjectIDZND2="g_1607bb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_12aefb0_0" Pin0InfoVect2LinkObjId="g_1607bb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3948,-301 3948,-320 3982,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12aff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-339 3982,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_12aefb0@0" ObjectIDZND2="g_1607bb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_12aefb0_0" Pin0InfoVect2LinkObjId="g_1607bb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-339 3982,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b82e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-265 4043,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43848@0" ObjectIDZND0="g_12b8540@0" Pin0InfoVect0LinkObjId="g_12b8540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-265 4043,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-301 4043,-320 4077,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43848@1" ObjectIDZND0="g_12b8fd0@0" ObjectIDZND1="g_12ba460@0" ObjectIDZND2="43846@x" Pin0InfoVect0LinkObjId="g_12b8fd0_0" Pin0InfoVect1LinkObjId="g_12ba460_0" Pin0InfoVect2LinkObjId="SW-272665_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272667_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-301 4043,-320 4077,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b9fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-293 4110,-309 4077,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_12b8fd0@0" ObjectIDZND0="43848@x" ObjectIDZND1="43846@x" ObjectIDZND2="g_12ba460@0" Pin0InfoVect0LinkObjId="SW-272667_0" Pin0InfoVect1LinkObjId="SW-272665_0" Pin0InfoVect2LinkObjId="g_12ba460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b8fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-293 4110,-309 4077,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ba200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-320 4077,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43848@x" ObjectIDND1="43846@x" ObjectIDZND0="g_12b8fd0@0" ObjectIDZND1="g_12ba460@0" Pin0InfoVect0LinkObjId="g_12b8fd0_0" Pin0InfoVect1LinkObjId="g_12ba460_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-272667_0" Pin1InfoVect1LinkObjId="SW-272665_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-320 4077,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-309 4077,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43848@x" ObjectIDND1="43846@x" ObjectIDND2="g_12b8fd0@0" ObjectIDZND0="g_12ba460@0" Pin0InfoVect0LinkObjId="g_12ba460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272667_0" Pin1InfoVect1LinkObjId="SW-272665_0" Pin1InfoVect2LinkObjId="g_12b8fd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-309 4077,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-170 4077,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_12ba460@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ba460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-170 4077,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15fe3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-262 4461,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43860@0" ObjectIDZND0="g_15fe640@0" Pin0InfoVect0LinkObjId="g_15fe640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-262 4461,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15ffe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-298 4461,-317 4495,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43860@1" ObjectIDZND0="43858@x" ObjectIDZND1="g_15ff0d0@0" ObjectIDZND2="g_1322300@0" Pin0InfoVect0LinkObjId="SW-272772_0" Pin0InfoVect1LinkObjId="g_15ff0d0_0" Pin0InfoVect2LinkObjId="g_1322300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-298 4461,-317 4495,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1606ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-266 4558,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43863@0" ObjectIDZND0="g_1607120@0" Pin0InfoVect0LinkObjId="g_1607120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-266 4558,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e9210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-451 4665,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43838@0" ObjectIDZND0="43865@1" Pin0InfoVect0LinkObjId="SW-272831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1623ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-451 4665,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e9cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-388 4665,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43865@0" ObjectIDZND0="g_15e9470@1" Pin0InfoVect0LinkObjId="g_15e9470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-388 4665,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-315 4665,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_15e9470@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15e9470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-315 4665,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15eecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3542,-499 3542,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3542,-499 3542,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15f1420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-631 3504,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_15f1680@0" Pin0InfoVect0LinkObjId="g_15f1680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3504,-631 3504,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15f2110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-595 3504,-579 3580,-579 3580,-602 3579,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_15f9d80@0" Pin0InfoVect0LinkObjId="g_15f9d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3504,-595 3504,-579 3580,-579 3580,-602 3579,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15f2c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3542,-719 3542,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15f30c0@0" ObjectIDZND0="g_15f2380@1" Pin0InfoVect0LinkObjId="g_15f2380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f30c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3542,-719 3542,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15f2e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3542,-651 3542,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_15f2380@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f2380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3542,-651 3542,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f9b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-480 4352,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43864@0" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_1623ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-480 4352,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b1e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-550 4318,-535 4352,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_14b1170@0" ObjectIDZND0="43864@x" ObjectIDZND1="g_14b2bd0@0" Pin0InfoVect0LinkObjId="SW-272824_0" Pin0InfoVect1LinkObjId="g_14b2bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14b1170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-550 4318,-535 4352,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b2970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-535 4352,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_14b1170@0" ObjectIDND1="g_14b2bd0@0" ObjectIDZND0="43864@1" Pin0InfoVect0LinkObjId="SW-272824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14b1170_0" Pin1InfoVect1LinkObjId="g_14b2bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-535 4352,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b3450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-647 4352,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15f4e50@0" ObjectIDZND0="g_14b2bd0@1" Pin0InfoVect0LinkObjId="g_14b2bd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f4e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-647 4352,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b36b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-566 4352,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_14b2bd0@0" ObjectIDZND0="g_14b1170@0" ObjectIDZND1="43864@x" Pin0InfoVect0LinkObjId="g_14b1170_0" Pin0InfoVect1LinkObjId="SW-272824_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14b2bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-566 4352,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-320 4077,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="43848@x" ObjectIDND1="g_12b8fd0@0" ObjectIDND2="g_12ba460@0" ObjectIDZND0="43846@0" Pin0InfoVect0LinkObjId="SW-272665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272667_0" Pin1InfoVect1LinkObjId="g_12b8fd0_0" Pin1InfoVect2LinkObjId="g_12ba460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-320 4077,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-363 4077,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43846@1" ObjectIDZND0="43847@0" Pin0InfoVect0LinkObjId="SW-272666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-363 4077,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ba140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-423 4077,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43847@1" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_1623ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-423 4077,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c0f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-265 4133,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43851@0" ObjectIDZND0="g_14c1190@0" Pin0InfoVect0LinkObjId="g_14c1190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-265 4133,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c2990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-301 4133,-320 4167,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43851@1" ObjectIDZND0="g_14c1c20@0" ObjectIDZND1="g_14c30b0@0" ObjectIDZND2="43849@x" Pin0InfoVect0LinkObjId="g_14c1c20_0" Pin0InfoVect1LinkObjId="g_14c30b0_0" Pin0InfoVect2LinkObjId="SW-272692_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272694_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-301 4133,-320 4167,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c2bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-293 4200,-309 4167,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_14c1c20@0" ObjectIDZND0="43851@x" ObjectIDZND1="43849@x" ObjectIDZND2="g_14c30b0@0" Pin0InfoVect0LinkObjId="SW-272694_0" Pin0InfoVect1LinkObjId="SW-272692_0" Pin0InfoVect2LinkObjId="g_14c30b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14c1c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-293 4200,-309 4167,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c2e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-320 4167,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43851@x" ObjectIDND1="43849@x" ObjectIDZND0="g_14c1c20@0" ObjectIDZND1="g_14c30b0@0" Pin0InfoVect0LinkObjId="g_14c1c20_0" Pin0InfoVect1LinkObjId="g_14c30b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-272694_0" Pin1InfoVect1LinkObjId="SW-272692_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-320 4167,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c3e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-309 4167,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43851@x" ObjectIDND1="43849@x" ObjectIDND2="g_14c1c20@0" ObjectIDZND0="g_14c30b0@0" Pin0InfoVect0LinkObjId="g_14c30b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272694_0" Pin1InfoVect1LinkObjId="SW-272692_0" Pin1InfoVect2LinkObjId="g_14c1c20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-309 4167,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c4060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-170 4167,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_14c30b0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14c30b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-170 4167,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c4ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-320 4167,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_14c1c20@0" ObjectIDND1="g_14c30b0@0" ObjectIDND2="43851@x" ObjectIDZND0="43849@0" Pin0InfoVect0LinkObjId="SW-272692_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14c1c20_0" Pin1InfoVect1LinkObjId="g_14c30b0_0" Pin1InfoVect2LinkObjId="SW-272694_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-320 4167,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c5130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-363 4167,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43849@1" ObjectIDZND0="43850@0" Pin0InfoVect0LinkObjId="SW-272693_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272692_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-363 4167,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c5390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-423 4167,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43850@1" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_1623ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272693_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-423 4167,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1966370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4231,-266 4231,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43854@0" ObjectIDZND0="g_19665d0@0" Pin0InfoVect0LinkObjId="g_19665d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272721_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4231,-266 4231,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1967dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4231,-302 4231,-321 4265,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43854@1" ObjectIDZND0="g_1967060@0" ObjectIDZND1="g_19684f0@0" ObjectIDZND2="43852@x" Pin0InfoVect0LinkObjId="g_1967060_0" Pin0InfoVect1LinkObjId="g_19684f0_0" Pin0InfoVect2LinkObjId="SW-272719_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4231,-302 4231,-321 4265,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1968030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-294 4298,-310 4265,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_1967060@0" ObjectIDZND0="43854@x" ObjectIDZND1="43852@x" ObjectIDZND2="g_19684f0@0" Pin0InfoVect0LinkObjId="SW-272721_0" Pin0InfoVect1LinkObjId="SW-272719_0" Pin0InfoVect2LinkObjId="g_19684f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1967060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-294 4298,-310 4265,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1968290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-321 4265,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43854@x" ObjectIDND1="43852@x" ObjectIDZND0="g_1967060@0" ObjectIDZND1="g_19684f0@0" Pin0InfoVect0LinkObjId="g_1967060_0" Pin0InfoVect1LinkObjId="g_19684f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-272721_0" Pin1InfoVect1LinkObjId="SW-272719_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-321 4265,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1969240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-310 4265,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43854@x" ObjectIDND1="43852@x" ObjectIDND2="g_1967060@0" ObjectIDZND0="g_19684f0@0" Pin0InfoVect0LinkObjId="g_19684f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272721_0" Pin1InfoVect1LinkObjId="SW-272719_0" Pin1InfoVect2LinkObjId="g_1967060_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-310 4265,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19694a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-171 4265,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_19684f0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19684f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-171 4265,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_196a310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-321 4265,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1967060@0" ObjectIDND1="g_19684f0@0" ObjectIDND2="43854@x" ObjectIDZND0="43852@0" Pin0InfoVect0LinkObjId="SW-272719_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1967060_0" Pin1InfoVect1LinkObjId="g_19684f0_0" Pin1InfoVect2LinkObjId="SW-272721_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-321 4265,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_196a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-364 4265,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43852@1" ObjectIDZND0="43853@0" Pin0InfoVect0LinkObjId="SW-272720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-364 4265,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_196a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-424 4265,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43853@1" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_1623ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-424 4265,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1972f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-262 4336,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43857@0" ObjectIDZND0="g_19731a0@0" Pin0InfoVect0LinkObjId="g_19731a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272854_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-262 4336,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19749a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-298 4336,-317 4370,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="43857@1" ObjectIDZND0="g_1973c30@0" ObjectIDZND1="g_19750c0@0" ObjectIDZND2="43855@x" Pin0InfoVect0LinkObjId="g_1973c30_0" Pin0InfoVect1LinkObjId="g_19750c0_0" Pin0InfoVect2LinkObjId="SW-272746_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-298 4336,-317 4370,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1974c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-290 4403,-306 4370,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_1973c30@0" ObjectIDZND0="43857@x" ObjectIDZND1="43855@x" ObjectIDZND2="g_19750c0@0" Pin0InfoVect0LinkObjId="SW-272854_0" Pin0InfoVect1LinkObjId="SW-272746_0" Pin0InfoVect2LinkObjId="g_19750c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1973c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-290 4403,-306 4370,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1974e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-317 4370,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43857@x" ObjectIDND1="43855@x" ObjectIDZND0="g_1973c30@0" ObjectIDZND1="g_19750c0@0" Pin0InfoVect0LinkObjId="g_1973c30_0" Pin0InfoVect1LinkObjId="g_19750c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-272854_0" Pin1InfoVect1LinkObjId="SW-272746_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-317 4370,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1975e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-306 4370,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43857@x" ObjectIDND1="43855@x" ObjectIDND2="g_1973c30@0" ObjectIDZND0="g_19750c0@0" Pin0InfoVect0LinkObjId="g_19750c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272854_0" Pin1InfoVect1LinkObjId="SW-272746_0" Pin1InfoVect2LinkObjId="g_1973c30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-306 4370,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1976070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-167 4370,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_19750c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19750c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-167 4370,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1976ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-317 4370,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1973c30@0" ObjectIDND1="g_19750c0@0" ObjectIDND2="43857@x" ObjectIDZND0="43855@0" Pin0InfoVect0LinkObjId="SW-272746_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1973c30_0" Pin1InfoVect1LinkObjId="g_19750c0_0" Pin1InfoVect2LinkObjId="SW-272854_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-317 4370,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1977140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-360 4370,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43855@1" ObjectIDZND0="43856@0" Pin0InfoVect0LinkObjId="SW-272747_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-360 4370,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19773a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-420 4370,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43856@1" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_1623ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272747_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-420 4370,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1978cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-428 4495,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43859@1" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_1623ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-428 4495,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1978f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-317 4495,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="43860@x" ObjectIDND1="g_15ff0d0@0" ObjectIDND2="g_1322300@0" ObjectIDZND0="43858@0" Pin0InfoVect0LinkObjId="SW-272772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272774_0" Pin1InfoVect1LinkObjId="g_15ff0d0_0" Pin1InfoVect2LinkObjId="g_1322300_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-317 4495,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19791b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-370 4495,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43858@1" ObjectIDZND0="43859@0" Pin0InfoVect0LinkObjId="SW-272773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-370 4495,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1979410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-434 4592,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43862@1" ObjectIDZND0="43838@0" Pin0InfoVect0LinkObjId="g_1623ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272800_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-434 4592,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1979670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-373 4592,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43861@1" ObjectIDZND0="43862@0" Pin0InfoVect0LinkObjId="SW-272800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-272799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-373 4592,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c08f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-317 4495,-306 4528,-306 4528,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43860@x" ObjectIDND1="43858@x" ObjectIDND2="g_1322300@0" ObjectIDZND0="g_15ff0d0@0" Pin0InfoVect0LinkObjId="g_15ff0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272774_0" Pin1InfoVect1LinkObjId="SW-272772_0" Pin1InfoVect2LinkObjId="g_1322300_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-317 4495,-306 4528,-306 4528,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-317 4495,80 3467,80 3467,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43860@x" ObjectIDND1="43858@x" ObjectIDND2="g_15ff0d0@0" ObjectIDZND0="g_1322300@1" Pin0InfoVect0LinkObjId="g_1322300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272774_0" Pin1InfoVect1LinkObjId="SW-272772_0" Pin1InfoVect2LinkObjId="g_15ff0d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-317 4495,80 3467,80 3467,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c0f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-320 3982,-309 4015,-309 4015,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1607bb0@0" ObjectIDZND0="g_12aefb0@0" Pin0InfoVect0LinkObjId="g_12aefb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1607bb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-320 3982,-309 4015,-309 4015,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c11a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-317 4558,-317 4558,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="43861@x" ObjectIDND1="g_1607bb0@0" ObjectIDND2="0@x" ObjectIDZND0="43863@1" Pin0InfoVect0LinkObjId="SW-272855_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272799_0" Pin1InfoVect1LinkObjId="g_1607bb0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-317 4558,-317 4558,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c1c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-317 4592,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="43863@x" ObjectIDND1="g_1607bb0@0" ObjectIDND2="0@x" ObjectIDZND0="43861@0" Pin0InfoVect0LinkObjId="SW-272799_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-272855_0" Pin1InfoVect1LinkObjId="g_1607bb0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-317 4592,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c1ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-306 4625,-306 4625,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_12aefb0@0" ObjectIDZND0="g_1607bb0@0" Pin0InfoVect0LinkObjId="g_1607bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_12aefb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-306 4625,-306 4625,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c2970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-320 3982,40 4592,40 4592,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_12aefb0@0" ObjectIDZND0="g_1607bb0@0" ObjectIDZND1="43863@x" ObjectIDZND2="43861@x" Pin0InfoVect0LinkObjId="g_1607bb0_0" Pin0InfoVect1LinkObjId="SW-272855_0" Pin0InfoVect2LinkObjId="SW-272799_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_12aefb0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-320 3982,40 4592,40 4592,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c2bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-306 4592,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1607bb0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="43863@x" ObjectIDZND1="43861@x" Pin0InfoVect0LinkObjId="SW-272855_0" Pin0InfoVect1LinkObjId="SW-272799_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1607bb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-306 4592,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ccc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5323,-558 5323,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_19cd960@0" ObjectIDZND0="g_19cce30@0" Pin0InfoVect0LinkObjId="g_19cce30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_19cd960_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5323,-558 5323,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d06f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5223,-550 5223,-558 5323,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_19cd960@0" ObjectIDZND0="g_19cce30@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_19cce30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19cd960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5223,-550 5223,-558 5323,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5848,-665 5848,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5848,-665 5848,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d7ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5819,-686 5848,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5819,-686 5848,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5848,-686 5870,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5848,-686 5870,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-686 5625,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19d64c0@0" Pin0InfoVect0LinkObjId="g_19d64c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-686 5625,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d85c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5704,-686 5735,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19d64c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d64c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5704,-686 5735,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d8820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5906,-686 5919,-686 5919,-601 5962,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5906,-686 5919,-686 5919,-601 5962,-601 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-272209" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3242.000000 -1107.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43828" ObjectName="DYN-CX_DYYDB"/>
     <cge:Meas_Ref ObjectId="272209"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="5682" cy="-1198" fill="rgb(0,0,0)" fillStyle="1" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-915 5223,-915 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4726,-915 5223,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5285,-915 5917,-915 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5285,-915 5917,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-260 6025,-492 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="6025,-260 6025,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6025,-531 6025,-1075 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="6025,-531 6025,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3432,-450 4008,-450 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3432,-450 4008,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DYYDB.CX_DYYDB_9ⅢM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-451 4691,-451 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43838" ObjectName="BS-CX_DYYDB.CX_DYYDB_9ⅢM"/>
    <cge:TPSR_Ref TObjectID="43838"/></metadata>
   <polyline fill="none" opacity="0" points="4040,-451 4691,-451 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5195" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4785" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5015" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4799" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5310" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5422" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4992" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-412" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-477" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-545" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-1043" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-845" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-668" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-1020" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5860" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5538" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="6025" cy="-293" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5703" cy="-915" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3467" cy="-450" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3578" cy="-450" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3692" cy="-450" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3789" cy="-450" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3885" cy="-450" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3982" cy="-450" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4118" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4077" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4495" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4592" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4665" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3542" cy="-450" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4167" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4265" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43838" cx="4370" cy="-451" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.000000 -977.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -987.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -987.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5694.000000 -987.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -797.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5529.000000 -798.239382)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5783.000000 -676.239382)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6132.000000 -422.345005)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6141.000000 -1053.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6141.000000 -855.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6134.000000 -678.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6136.000000 -303.746531)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5413.000000 -988.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272628">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -952.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43844" ObjectName="SW-CX_DYYDB.CX_DYYDB_003BK"/>
     <cge:Meas_Ref ObjectId="272628"/>
    <cge:TPSR_Ref TObjectID="43844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272536">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 -952.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43840" ObjectName="SW-CX_DYYDB.CX_DYYDB_103BK"/>
     <cge:Meas_Ref ObjectId="272536"/>
    <cge:TPSR_Ref TObjectID="43840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3458.000000 -380.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3569.000000 -380.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3683.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -377.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 -377.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -381.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43846" ObjectName="SW-CX_DYYDB.CX_DYYDB_031BK"/>
     <cge:Meas_Ref ObjectId="272665"/>
    <cge:TPSR_Ref TObjectID="43846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -335.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43858" ObjectName="SW-CX_DYYDB.CX_DYYDB_035BK"/>
     <cge:Meas_Ref ObjectId="272772"/>
    <cge:TPSR_Ref TObjectID="43858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272799">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43861" ObjectName="SW-CX_DYYDB.CX_DYYDB_036BK"/>
     <cge:Meas_Ref ObjectId="272799"/>
    <cge:TPSR_Ref TObjectID="43861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43849" ObjectName="SW-CX_DYYDB.CX_DYYDB_032BK"/>
     <cge:Meas_Ref ObjectId="272692"/>
    <cge:TPSR_Ref TObjectID="43849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272719">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -329.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43852" ObjectName="SW-CX_DYYDB.CX_DYYDB_033BK"/>
     <cge:Meas_Ref ObjectId="272719"/>
    <cge:TPSR_Ref TObjectID="43852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-272746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43855" ObjectName="SW-CX_DYYDB.CX_DYYDB_034BK"/>
     <cge:Meas_Ref ObjectId="272746"/>
    <cge:TPSR_Ref TObjectID="43855"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,59)">片区有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1332250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1047.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1469d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2957.000000 -609.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_11a1540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.500000 -1187.500000) translate(0,16)">大姚移动变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1276b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3241.000000 -1190.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_123d840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3014.000000 -787.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_144a140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3071.000000 -248.000000) translate(0,17)">4913</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,16)">1、全站停电检修前应挂“全站停电检修”牌，复电后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,36)">方可摘除“全站停电检修”牌。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,76)">2、各类间隔工作时，停电完成后应在相应间隔挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,96)">“禁止合闸，有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,136)">3、线路工作时，停电完成后应在相应间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,156)">挂“禁止合闸，线路有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,196)">4、现场工作影响对应间隔四遥信息正确性的，应挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,216)">“禁止刷新”牌，工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,256)">5、现场开展相应间隔四遥信息核对前，应挂“调试一”牌，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13d2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -557.000000) translate(0,276)">核对工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1099350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3324.000000 -1169.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1401160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3324.000000 -1204.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12311d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -202.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13c3e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3068.000000 -212.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13c3e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3068.000000 -212.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_139b570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3012.000000 -715.000000) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13150e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4793.000000 -1017.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133c850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4788.000000 -953.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133ca90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -1000.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133ccf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -1058.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133d430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4802.000000 -1123.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -1075.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133d8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5023.000000 -1016.000000) translate(0,12)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133dc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5018.000000 -1074.000000) translate(0,12)">1526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5017.000000 -951.000000) translate(0,12)">1521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5031.000000 -1126.000000) translate(0,12)">15267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5030.000000 -1058.000000) translate(0,12)">15260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5031.000000 -1000.000000) translate(0,12)">15217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -939.000000) translate(0,12)">110kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135fbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5874.000000 -935.000000) translate(0,12)">110kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135ffe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5237.000000 -1009.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1360220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5200.000000 -955.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1360460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5313.000000 -957.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13606a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5198.000000 -1032.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13608e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5313.000000 -1031.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1360b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5431.000000 -1017.000000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1360d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.000000 -954.000000) translate(0,12)">1532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1360fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5438.000000 -1002.000000) translate(0,12)">15327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13611e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5437.000000 -1057.000000) translate(0,12)">15360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1361420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.000000 -1075.000000) translate(0,12)">1536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1361660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5438.000000 -1122.000000) translate(0,12)">15367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13618a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5712.000000 -1016.000000) translate(0,12)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1361ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5707.000000 -953.000000) translate(0,12)">1542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1361d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5707.000000 -1074.000000) translate(0,12)">1546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1361f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5720.000000 -1122.000000) translate(0,12)">15467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13621a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5649.000000 -1062.000000) translate(0,12)">15460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13623e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5720.500000 -1001.000000) translate(0,12)">15427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1362620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5867.000000 -844.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1362860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5876.000000 -904.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1362aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5876.000000 -830.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1362ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -845.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161e7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4815.000000 -901.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161ea20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4815.000000 -827.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161ec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.000000 -827.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161eea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4995.000000 -894.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161f0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5014.000000 -870.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.500000 -814.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161f560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4995.000000 -769.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_161f7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5972.000000 -1095.000000) translate(0,15)">35kV中心变Ⅰ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_1621a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5975.000000 -256.000000) translate(0,15)">35kV中心变Ⅱ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a48d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5396.000000 -584.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a4cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5547.000000 -828.239382) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a4fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5540.000000 -882.239382) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a51e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5543.000000 -776.239382) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a5420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5558.500000 -864.239382) translate(0,12)">10227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15a5660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5558.000000 -815.239382) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12d6260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5558.000000 -757.239382) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12d6750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5391.000000 -687.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12d6990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5793.000000 -706.239382) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12d6bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5877.000000 -670.239382) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12d6e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5736.000000 -708.239382) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12d7050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5964.000000 -1042.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -1182.000000) translate(0,15)">元</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -1182.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -1182.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4984.000000 -1180.000000) translate(0,15)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4984.000000 -1180.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4984.000000 -1180.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5392.000000 -1177.000000) translate(0,15)">渔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5392.000000 -1177.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5392.000000 -1177.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5674.000000 -1177.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5674.000000 -1177.000000) translate(0,33)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5674.000000 -1177.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5906.000000 -790.000000) translate(0,15)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5906.000000 -790.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5906.000000 -790.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5906.000000 -790.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5906.000000 -790.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125af90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6369.000000 -1066.000000) translate(0,15)">大石河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125b870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6369.000000 -870.000000) translate(0,15)">中北Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125c0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6390.000000 -696.000000) translate(0,15)">中仓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_125c650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6132.000000 -599.000000) translate(0,15)">35kV中心变1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16340d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6369.000000 -440.000000) translate(0,15)">中北I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16344d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6358.000000 -316.000000) translate(0,15)">六中杨石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16349f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -790.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16349f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -790.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16349f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -790.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16349f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -790.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16349f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -790.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128e640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5431.000000 -1017.000000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_128f550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5886.000000 -978.000000) translate(0,12)">35kVI母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19bc180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5456.000000 -748.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,27)">SFSZ11-40000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,42)">110±8×1.25%/38.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,57)">±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,72)">40/40/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,102)">Uk1-2%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,117)">Uk1-3%=17.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5358.000000 -872.000000) translate(0,132)">Uk2-3%=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fb880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -1204.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fc150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5023.000000 -1205.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fc780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5435.000000 -1201.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fcdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5715.000000 -1206.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fd0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5986.000000 -521.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fd380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5888.000000 -1064.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fd5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6151.000000 -1068.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fd800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6056.000000 -1069.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fda40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6202.000000 -1069.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fdc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6152.000000 -870.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fdec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6090.000000 -871.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fe100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6202.000000 -873.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fe340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6262.000000 -900.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fe580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6082.000000 -694.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fe7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6144.000000 -693.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fea00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6227.000000 -694.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fec40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6320.000000 -616.000000) translate(0,12)">3429</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12fee80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6303.000000 -718.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ff0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6142.000000 -437.000000) translate(0,12)">346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ff300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6071.000000 -438.000000) translate(0,12)">3461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ff540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6192.000000 -442.000000) translate(0,12)">3466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ff780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6146.000000 -328.000000) translate(0,12)">345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ff9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6085.000000 -319.000000) translate(0,12)">3451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ffc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6197.000000 -319.000000) translate(0,12)">3456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ffe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6257.000000 -345.000000) translate(0,12)">34567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1300080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6261.000000 -259.000000) translate(0,12)">3459</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_125e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6134.000000 -757.000000) translate(0,12)">34217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1262320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6215.000000 -742.000000) translate(0,12)">34260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_1262de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5964.000000 -1221.000000) translate(0,15)">中心变35kV母线数据</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="15" graphid="g_1296d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 -714.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="15" graphid="g_1296d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 -714.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="15" graphid="g_1296d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 -714.000000) translate(0,42)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="15" graphid="g_1296d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 -714.000000) translate(0,57)">心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="15" graphid="g_1296d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 -714.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="15" graphid="g_1296d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 -714.000000) translate(0,87)">设</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="15" graphid="g_1296d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 -714.000000) translate(0,102)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6264.000000 -1009.000000) translate(0,12)">3439</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b3910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3458.000000 -776.000000) translate(0,12)">10kV环网柜母线电压互感</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b53e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -696.000000) translate(0,12)">10kV移动母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3631.000000 -486.000000) translate(0,12)">10kV环网柜母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -516.000000) translate(0,12)">0033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -986.000000) translate(0,12)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b69a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.000000 -1108.000000) translate(0,12)">1030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4406.000000 -986.000000) translate(0,12)">103</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b6e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4457.000000 -1006.000000) translate(0,12)">10317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b7060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4473.000000 -988.000000) translate(0,12)">1031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b72a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -1116.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b74e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3484.000000 -405.000000) translate(0,12)">021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b7720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.000000 -355.000000) translate(0,12)">0216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3413.000000 -220.000000) translate(0,12)">02167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b7ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3594.000000 -409.000000) translate(0,12)">022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b7de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3592.000000 -359.000000) translate(0,12)">0226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b8020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3523.000000 -224.000000) translate(0,12)">02267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b8260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3711.000000 -403.000000) translate(0,12)">023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b84a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -353.000000) translate(0,12)">0236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b86e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -218.000000) translate(0,12)">02367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b8920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -396.000000) translate(0,12)">024</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b8b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -346.000000) translate(0,12)">0246</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b8da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -211.000000) translate(0,12)">02467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b8fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -400.000000) translate(0,12)">025</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b9220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -350.000000) translate(0,12)">0256</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b9460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -215.000000) translate(0,12)">02567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b96a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -396.000000) translate(0,12)">026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b98e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -346.000000) translate(0,12)">0266</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b9b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -211.000000) translate(0,12)">02667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19798d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3536.000000 -50.000000) translate(0,15)">10kV环南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197a8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -49.000000) translate(0,15)">10kV中东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197ae50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3751.000000 -49.000000) translate(0,15)">10kV环西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197b3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -47.000000) translate(0,15)">10kV中北联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197bc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 -48.000000) translate(0,15)">10kV中东Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197bec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -48.000000) translate(0,15)">10kV中芦线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197c3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -49.000000) translate(0,15)">10kV中金线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197c930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4335.000000 -49.000000) translate(0,15)">10kV中李线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_197ce90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -110.000000) translate(0,13)">10kV1号移动柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_197ce90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -110.000000) translate(0,29)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19bca00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 18.000000) translate(0,15)">10kV母线联络Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19bced0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 100.000000) translate(0,15)">10kV母线联络Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bd110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -357.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bd340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -412.000000) translate(0,12)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bd580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4029.000000 -233.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bd7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4176.000000 -357.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bda00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4174.000000 -412.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bdc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -230.000000) translate(0,12)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bde80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -358.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19be0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -413.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19be300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -232.000000) translate(0,12)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19be540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4379.000000 -354.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19be780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -409.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19be9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 -226.000000) translate(0,12)">03467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bec00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -364.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4599.000000 -423.000000) translate(0,12)">0361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bf080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -417.000000) translate(0,12)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bf2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4443.000000 -227.000000) translate(0,12)">03567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bf500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4601.000000 -367.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bf740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4543.000000 -228.000000) translate(0,12)">03667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bf980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -412.000000) translate(0,12)">0371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bfbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4300.000000 -926.000000) translate(0,12)">3号移动主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19bff40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -472.000000) translate(0,12)">10kV移动柜母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -505.000000) translate(0,12)">0903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -892.000000) translate(0,12)">移动变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -892.000000) translate(0,27)">SZ11-40000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -892.000000) translate(0,42)">110±8×1.25%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -892.000000) translate(0,57)">40/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c8fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -892.000000) translate(0,72)">Uk%=10.74</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c92e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6348.000000 -825.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c9820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6463.000000 -637.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3556.000000 -516.000000) translate(0,12)">0904</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c9c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3448.000000 -614.000000) translate(0,12)">09047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_19cae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -759.000000) translate(0,12)">   DTU柜设备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_19cae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -759.000000) translate(0,27)">配网OCS系统操作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cf110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5182.000000 -493.000000) translate(0,15)">1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d08e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5600.000000 -663.000000) translate(0,15)">35kV临供电缆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -725.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -725.000000) translate(0,33)">主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -725.000000) translate(0,51)">拆除</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -725.000000) translate(0,69)">解脱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -725.000000) translate(0,87)">点</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d5e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5835.000000 -594.239382) translate(0,12)">30217</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1433140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5189.000000 -1049.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13107d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5304.000000 -1049.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1269be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -1029.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1267d40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -1094.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1433660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -975.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c2ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -1029.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1380b50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -1094.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13fa480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -975.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_134f8b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4862.000000 -811.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d07f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4862.000000 -885.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1310e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5765.000000 -975.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13116d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5765.000000 -1094.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_133bcd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5484.000000 -976.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b06e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5484.000000 -1030.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b0f80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5484.000000 -1095.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13cf0e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5054.000000 -853.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c58b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5054.000000 -801.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b3e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5601.000000 -850.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_141f5d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5601.000000 -802.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1276190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6307.000000 -877.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13af760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6302.000000 -324.746531)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1256e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5941.000000 -1104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e6d40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5923.000000 -810.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e7c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5923.000000 -884.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a5c80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5601.000000 -741.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19bad90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6348.000000 -698.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125dd30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6184.000000 -732.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1261890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6262.000000 -721.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_162d3e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5637.000000 -1046.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1955c10" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4456.000000 -1033.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_195dd40" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3427.000000 -257.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132a6b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3538.000000 -257.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b0250" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3652.000000 -253.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1283d60" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3749.000000 -254.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1611030" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3845.000000 -254.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ae520" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3942.000000 -258.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b8540" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4037.000000 -258.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15fe640" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4455.000000 -255.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1607120" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4552.000000 -259.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f1680" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3510.000000 -638.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c1190" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4127.000000 -258.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19665d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4225.000000 -259.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19731a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4330.000000 -255.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DYYDB"/>
</svg>