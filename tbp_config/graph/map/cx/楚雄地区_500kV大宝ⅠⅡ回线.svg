<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="楚雄地区_500kV大宝ⅠⅡ回线.svg" MapType="line" StationID="SS-118" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3445 -1816 1742 960">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18df040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18dfa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e03e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e1530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e2850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e3410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e3ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e4950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e52e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e5c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e67d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18e70b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e8c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e98d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ea0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18eaae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ec010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ecd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_148d360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18ee310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ef4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18efe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f0930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_153ec30" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_153fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15403a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18f6060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f6e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_18fc4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_147ee20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="970" width="1752" x="3440" y="-1821"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196131">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29852" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_5081BK"/>
     <cge:Meas_Ref ObjectId="196131"/>
    <cge:TPSR_Ref TObjectID="29852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196132">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1187.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29853" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_5082BK"/>
     <cge:Meas_Ref ObjectId="196132"/>
    <cge:TPSR_Ref TObjectID="29853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213942">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -996.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31845" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_5083BK"/>
     <cge:Meas_Ref ObjectId="213942"/>
    <cge:TPSR_Ref TObjectID="31845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213934">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1384.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31824" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_5071BK"/>
     <cge:Meas_Ref ObjectId="213934"/>
    <cge:TPSR_Ref TObjectID="31824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196129">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1188.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29848" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_5072BK"/>
     <cge:Meas_Ref ObjectId="196129"/>
    <cge:TPSR_Ref TObjectID="29848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196130">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -997.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29849" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_5073BK"/>
     <cge:Meas_Ref ObjectId="196130"/>
    <cge:TPSR_Ref TObjectID="29849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66397">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20231" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_5011BK"/>
     <cge:Meas_Ref ObjectId="66397"/>
    <cge:TPSR_Ref TObjectID="20231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66398">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1187.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20234" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_5012BK"/>
     <cge:Meas_Ref ObjectId="66398"/>
    <cge:TPSR_Ref TObjectID="20234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66399">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -996.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20235" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_5013BK"/>
     <cge:Meas_Ref ObjectId="66399"/>
    <cge:TPSR_Ref TObjectID="20235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66406">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -1384.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20249" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_5021BK"/>
     <cge:Meas_Ref ObjectId="66406"/>
    <cge:TPSR_Ref TObjectID="20249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66407">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -1188.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20250" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_5022BK"/>
     <cge:Meas_Ref ObjectId="66407"/>
    <cge:TPSR_Ref TObjectID="20250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66408">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -997.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20251" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_5023BK"/>
     <cge:Meas_Ref ObjectId="66408"/>
    <cge:TPSR_Ref TObjectID="20251"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_BF_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3960,-1501 4429,-1501 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31793" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_BF_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="31793"/></metadata>
   <polyline fill="none" opacity="0" points="3960,-1501 4429,-1501 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_BF_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-906 4429,-906 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31798" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_BF_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="31798"/></metadata>
   <polyline fill="none" opacity="0" points="3965,-906 4429,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_DCS_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-1501 5048,-1501 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31792" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_DCS_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="31792"/></metadata>
   <polyline fill="none" opacity="0" points="4641,-1501 5048,-1501 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_DCS_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4636,-906 5051,-906 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31799" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_DCS_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="31799"/></metadata>
   <polyline fill="none" opacity="0" points="4636,-906 5051,-906 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-500KV" id="g_157f0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1501 4300,-1480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31793@0" ObjectIDZND0="31847@1" Pin0InfoVect0LinkObjId="SW-213943_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1501 4300,-1480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1510830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-934 4300,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31852@0" ObjectIDZND0="31798@0" Pin0InfoVect0LinkObjId="g_151e4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-934 4300,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1510a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1125 4300,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31851@0" ObjectIDZND0="31846@1" Pin0InfoVect0LinkObjId="SW-213948_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213945_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1125 4300,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_152f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1501 4024,-1481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31793@0" ObjectIDZND0="31825@1" Pin0InfoVect0LinkObjId="SW-213935_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1501 4024,-1481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14d5bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1322 4024,-1285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31831@0" ObjectIDZND0="31826@1" Pin0InfoVect0LinkObjId="SW-213938_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1322 4024,-1285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_151e4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-935 4024,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31828@0" ObjectIDZND0="31798@0" Pin0InfoVect0LinkObjId="g_1510830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-935 4024,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14efae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1444 4300,-1418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31847@0" ObjectIDZND0="29852@1" Pin0InfoVect0LinkObjId="SW-196131_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213943_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1444 4300,-1418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14efcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1248 4300,-1222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31850@0" ObjectIDZND0="29853@1" Pin0InfoVect0LinkObjId="SW-196132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213944_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1248 4300,-1222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14efec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1195 4300,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29853@0" ObjectIDZND0="31851@1" Pin0InfoVect0LinkObjId="SW-213945_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1195 4300,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14f00b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1031 4300,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31845@1" ObjectIDZND0="31846@0" Pin0InfoVect0LinkObjId="SW-213948_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213942_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1031 4300,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_15409f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-970 4300,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31852@1" ObjectIDZND0="31845@0" Pin0InfoVect0LinkObjId="SW-213942_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213949_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-970 4300,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1540be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1392 4024,-1358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31824@0" ObjectIDZND0="31831@1" Pin0InfoVect0LinkObjId="SW-213941_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1392 4024,-1358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1540dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1445 4024,-1419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31825@0" ObjectIDZND0="31824@1" Pin0InfoVect0LinkObjId="SW-213934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213935_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1445 4024,-1419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1540fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1249 4024,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31826@0" ObjectIDZND0="29848@1" Pin0InfoVect0LinkObjId="SW-196129_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1249 4024,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_15411b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1196 4024,-1162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29848@0" ObjectIDZND0="31829@1" Pin0InfoVect0LinkObjId="SW-213939_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196129_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1196 4024,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_15413a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1032 4024,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29849@1" ObjectIDZND0="31827@0" Pin0InfoVect0LinkObjId="SW-213936_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1032 4024,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1541590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-971 4024,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31828@1" ObjectIDZND0="29849@0" Pin0InfoVect0LinkObjId="SW-196130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-971 4024,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1541aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1501 4968,-1480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31792@0" ObjectIDZND0="20232@1" Pin0InfoVect0LinkObjId="SW-66400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1501 4968,-1480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14bbfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-1300 5143,-1300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31833@1" ObjectIDZND0="g_14bc1c0@0" Pin0InfoVect0LinkObjId="g_14bc1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-1300 5143,-1300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1491220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-934 4968,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20239@0" ObjectIDZND0="31799@0" Pin0InfoVect0LinkObjId="g_148ba00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-934 4968,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1491440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1125 4968,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20237@0" ObjectIDZND0="20238@1" Pin0InfoVect0LinkObjId="SW-66404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1125 4968,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_155dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1501 4734,-1481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31792@0" ObjectIDZND0="20252@1" Pin0InfoVect0LinkObjId="SW-66409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1501 4734,-1481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14e3140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1322 4734,-1285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20253@0" ObjectIDZND0="20254@1" Pin0InfoVect0LinkObjId="SW-66411_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1322 4734,-1285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148ba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-935 4734,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20257@0" ObjectIDZND0="31799@0" Pin0InfoVect0LinkObjId="g_1491220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-935 4734,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14ee090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1444 4968,-1418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20232@0" ObjectIDZND0="20231@1" Pin0InfoVect0LinkObjId="SW-66397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1444 4968,-1418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1391 4968,-1376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20231@0" ObjectIDZND0="20233@1" Pin0InfoVect0LinkObjId="SW-66401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1391 4968,-1376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148dad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1248 4968,-1222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20236@0" ObjectIDZND0="20234@1" Pin0InfoVect0LinkObjId="SW-66398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1248 4968,-1222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1195 4968,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20234@0" ObjectIDZND0="20237@1" Pin0InfoVect0LinkObjId="SW-66403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1195 4968,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148def0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1031 4968,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20235@1" ObjectIDZND0="20238@0" Pin0InfoVect0LinkObjId="SW-66404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1031 4968,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148e120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-970 4968,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20239@1" ObjectIDZND0="20235@0" Pin0InfoVect0LinkObjId="SW-66399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-970 4968,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1392 4734,-1358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20249@0" ObjectIDZND0="20253@1" Pin0InfoVect0LinkObjId="SW-66410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1392 4734,-1358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148e580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1445 4734,-1419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20252@0" ObjectIDZND0="20249@1" Pin0InfoVect0LinkObjId="SW-66406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1445 4734,-1419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148e7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1249 4734,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20254@0" ObjectIDZND0="20250@1" Pin0InfoVect0LinkObjId="SW-66407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1249 4734,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1032 4734,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20251@1" ObjectIDZND0="20256@0" Pin0InfoVect0LinkObjId="SW-66413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1032 4734,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-971 4734,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20257@1" ObjectIDZND0="20251@0" Pin0InfoVect0LinkObjId="SW-66408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-971 4734,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_148f650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1126 4024,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31829@0" ObjectIDZND0="31827@x" ObjectIDZND1="31830@x" ObjectIDZND2="31833@x" Pin0InfoVect0LinkObjId="SW-213936_0" Pin0InfoVect1LinkObjId="SW-213940_0" Pin0InfoVect2LinkObjId="SW-213910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213939_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1126 4024,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14ca4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1115 4024,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31829@x" ObjectIDND1="31830@x" ObjectIDND2="31833@x" ObjectIDZND0="31827@1" Pin0InfoVect0LinkObjId="SW-213936_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213939_0" Pin1InfoVect1LinkObjId="SW-213940_0" Pin1InfoVect2LinkObjId="SW-213910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1115 4024,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14cd830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1115 3949,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31829@x" ObjectIDND1="31827@x" ObjectIDZND0="31830@x" ObjectIDZND1="31833@x" ObjectIDZND2="31832@x" Pin0InfoVect0LinkObjId="SW-213940_0" Pin0InfoVect1LinkObjId="SW-213910_0" Pin0InfoVect2LinkObjId="SW-213909_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213939_0" Pin1InfoVect1LinkObjId="SW-213936_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1115 3949,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14cda90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-1115 3931,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31829@x" ObjectIDND1="31827@x" ObjectIDND2="31833@x" ObjectIDZND0="31830@1" Pin0InfoVect0LinkObjId="SW-213940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213939_0" Pin1InfoVect1LinkObjId="SW-213936_0" Pin1InfoVect2LinkObjId="SW-213910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-1115 3931,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14cdcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-1115 3873,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31830@0" ObjectIDZND0="g_14cc620@0" Pin0InfoVect0LinkObjId="g_14cc620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-1115 3873,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14cdf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1372 4300,-1391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31849@1" ObjectIDZND0="29852@0" Pin0InfoVect0LinkObjId="SW-196131_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213947_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1372 4300,-1391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14ce1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1179 4734,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20255@1" ObjectIDZND0="20250@0" Pin0InfoVect0LinkObjId="SW-66407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1179 4734,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14cebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1094 4734,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20256@1" ObjectIDZND0="20255@x" ObjectIDZND1="31853@x" Pin0InfoVect0LinkObjId="SW-66412_0" Pin0InfoVect1LinkObjId="SW-213911_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66413_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1094 4734,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14cee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1113 4734,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20256@x" ObjectIDND1="31853@x" ObjectIDZND0="20255@0" Pin0InfoVect0LinkObjId="SW-66412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66413_0" Pin1InfoVect1LinkObjId="SW-213911_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1113 4734,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1456950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-1113 4689,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20256@x" ObjectIDND1="20255@x" ObjectIDZND0="31853@1" Pin0InfoVect0LinkObjId="SW-213911_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66413_0" Pin1InfoVect1LinkObjId="SW-66412_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-1113 4689,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14598b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4622,-1113 4604,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31853@x" ObjectIDND1="31850@x" ObjectIDND2="31849@x" ObjectIDZND0="31854@1" Pin0InfoVect0LinkObjId="SW-213912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213911_0" Pin1InfoVect1LinkObjId="SW-213944_0" Pin1InfoVect2LinkObjId="SW-213947_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4622,-1113 4604,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1459b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-1113 4546,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31854@0" ObjectIDZND0="g_1458e80@0" Pin0InfoVect0LinkObjId="g_1458e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-1113 4546,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_145a550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-1113 4623,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31853@0" ObjectIDZND0="31854@x" ObjectIDZND1="31850@x" ObjectIDZND2="31849@x" Pin0InfoVect0LinkObjId="SW-213912_0" Pin0InfoVect1LinkObjId="SW-213944_0" Pin0InfoVect2LinkObjId="SW-213947_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213911_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-1113 4623,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_145af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1340 4968,-1300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20233@0" ObjectIDZND0="20236@x" ObjectIDZND1="31832@x" Pin0InfoVect0LinkObjId="SW-66402_0" Pin0InfoVect1LinkObjId="SW-213909_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1340 4968,-1300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_145b1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1300 4968,-1284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20233@x" ObjectIDND1="31832@x" ObjectIDZND0="20236@1" Pin0InfoVect0LinkObjId="SW-66402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66401_0" Pin1InfoVect1LinkObjId="SW-213909_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1300 4968,-1284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_145b430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5065,-1300 5087,-1300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31832@x" ObjectIDND1="31829@x" ObjectIDND2="31827@x" ObjectIDZND0="31833@0" Pin0InfoVect0LinkObjId="SW-213910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213909_0" Pin1InfoVect1LinkObjId="SW-213939_0" Pin1InfoVect2LinkObjId="SW-213936_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5065,-1300 5087,-1300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1495d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-1300 4998,-1300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20236@x" ObjectIDND1="20233@x" ObjectIDZND0="31832@0" Pin0InfoVect0LinkObjId="SW-213909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66402_0" Pin1InfoVect1LinkObjId="SW-66401_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-1300 4998,-1300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1495fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5034,-1300 5065,-1300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31832@1" ObjectIDZND0="31833@x" ObjectIDZND1="31829@x" ObjectIDZND2="31827@x" Pin0InfoVect0LinkObjId="SW-213910_0" Pin0InfoVect1LinkObjId="SW-213939_0" Pin0InfoVect2LinkObjId="SW-213936_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213909_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5034,-1300 5065,-1300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1496220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4299,-1303 4446,-1303 4446,-1598 4623,-1598 4623,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31850@x" ObjectIDND1="31849@x" ObjectIDND2="31848@x" ObjectIDZND0="31854@x" ObjectIDZND1="31853@x" Pin0InfoVect0LinkObjId="SW-213912_0" Pin0InfoVect1LinkObjId="SW-213911_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213944_0" Pin1InfoVect1LinkObjId="SW-213947_0" Pin1InfoVect2LinkObjId="SW-213946_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4299,-1303 4446,-1303 4446,-1598 4623,-1598 4623,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1496470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5065,-1300 5065,-1647 3949,-1647 3949,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31833@x" ObjectIDND1="31832@x" ObjectIDZND0="31829@x" ObjectIDZND1="31827@x" ObjectIDZND2="31830@x" Pin0InfoVect0LinkObjId="SW-213939_0" Pin0InfoVect1LinkObjId="SW-213936_0" Pin0InfoVect2LinkObjId="SW-213940_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213910_0" Pin1InfoVect1LinkObjId="SW-213909_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5065,-1300 5065,-1647 3949,-1647 3949,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14b6cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1284 4300,-1303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31850@1" ObjectIDZND0="31854@x" ObjectIDZND1="31853@x" ObjectIDZND2="31849@x" Pin0InfoVect0LinkObjId="SW-213912_0" Pin0InfoVect1LinkObjId="SW-213911_0" Pin0InfoVect2LinkObjId="SW-213947_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213944_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1284 4300,-1303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_14b6f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1303 4300,-1336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31854@x" ObjectIDND1="31853@x" ObjectIDND2="31850@x" ObjectIDZND0="31849@0" Pin0InfoVect0LinkObjId="SW-213947_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213912_0" Pin1InfoVect1LinkObjId="SW-213911_0" Pin1InfoVect2LinkObjId="SW-213944_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1303 4300,-1336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_149b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-1303 4201,-1303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31848@0" ObjectIDZND0="g_149a890@0" Pin0InfoVect0LinkObjId="g_149a890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-1303 4201,-1303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_149b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-1303 4259,-1303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31854@x" ObjectIDND1="31853@x" ObjectIDND2="31850@x" ObjectIDZND0="31848@1" Pin0InfoVect0LinkObjId="SW-213946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213912_0" Pin1InfoVect1LinkObjId="SW-213911_0" Pin1InfoVect2LinkObjId="SW-213944_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-1303 4259,-1303 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="31793" cx="4300" cy="-1501" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31793" cx="4024" cy="-1501" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31798" cx="4300" cy="-906" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31798" cx="4024" cy="-906" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31792" cx="4968" cy="-1501" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31792" cx="4734" cy="-1501" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31799" cx="4968" cy="-906" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31799" cx="4734" cy="-906" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_151e6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -1537.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1466d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4358.000000 -1629.000000) translate(0,16)">大宝Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_147e620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -876.000000) translate(0,16)">500kV宝峰变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_147f5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -937.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="30" graphid="g_14eea50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3602.500000 -1756.500000) translate(0,24)">大宝Ⅰ回线、大宝Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14eef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4313.000000 -1414.000000) translate(0,16)">5081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14ef150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4309.000000 -1222.000000) translate(0,16)">5082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14ef480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -1217.000000) translate(0,16)">5072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14ef7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -1025.000000) translate(0,16)">5073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_148bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -1533.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_148c290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -876.000000) translate(0,16)">500kV大朝山电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_147da80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -942.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14eddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4610.000000 -1676.000000) translate(0,16)">大宝Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14966c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -1413.000000) translate(0,16)">5021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1496cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -1470.000000) translate(0,16)">50211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1496f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -1347.000000) translate(0,16)">50212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1497150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -1274.000000) translate(0,16)">50221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1497390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -1217.000000) translate(0,16)">5022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14975d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -1168.000000) translate(0,16)">50222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1497810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -1083.000000) translate(0,16)">50231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1497a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -960.000000) translate(0,16)">50232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1497c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -1026.000000) translate(0,16)">5023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1497ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -1469.000000) translate(0,16)">50111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1498110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -1412.000000) translate(0,16)">5011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1498350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -1365.000000) translate(0,16)">50112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1498590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -1273.000000) translate(0,16)">50121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14987d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -1216.000000) translate(0,16)">5012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1498a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -1150.000000) translate(0,16)">50122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1498c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -1082.000000) translate(0,16)">50131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1498e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -1025.000000) translate(0,16)">5013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14990d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -959.000000) translate(0,16)">50132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149b760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -1470.000000) translate(0,16)">50711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149bc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -1413.000000) translate(0,16)">5071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149be90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -1347.000000) translate(0,16)">50712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149c0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -1274.000000) translate(0,16)">50721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149c310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -1151.000000) translate(0,16)">50722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149c550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -1148.000000) translate(0,16)">507367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149c790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -1083.000000) translate(0,16)">50731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149c9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -960.000000) translate(0,16)">50732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149cbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -1469.000000) translate(0,16)">50811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149ce20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -1361.000000) translate(0,16)">50812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1329.000000) translate(0,16)">508167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149d2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -1273.000000) translate(0,16)">50821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149d4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -1150.000000) translate(0,16)">50822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149d720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -1082.000000) translate(0,16)">50831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149d960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4309.000000 -1025.000000) translate(0,16)">5083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4307.000000 -959.000000) translate(0,16)">50832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149dde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4642.000000 -1146.000000) translate(0,16)">50236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149e020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -1151.000000) translate(0,16)">5023617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149e260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4990.000000 -1330.000000) translate(0,16)">50116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_149e4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5078.000000 -1335.000000) translate(0,16)">5011617</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-213947">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1331.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31849" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50812SW"/>
     <cge:Meas_Ref ObjectId="213947"/>
    <cge:TPSR_Ref TObjectID="31849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213944">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31850" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50821SW"/>
     <cge:Meas_Ref ObjectId="213944"/>
    <cge:TPSR_Ref TObjectID="31850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213945">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31851" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50822SW"/>
     <cge:Meas_Ref ObjectId="213945"/>
    <cge:TPSR_Ref TObjectID="31851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213949">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31852" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50832SW"/>
     <cge:Meas_Ref ObjectId="213949"/>
    <cge:TPSR_Ref TObjectID="31852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213948">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1052.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31846" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50831SW"/>
     <cge:Meas_Ref ObjectId="213948"/>
    <cge:TPSR_Ref TObjectID="31846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213943">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1439.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31847" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50811SW"/>
     <cge:Meas_Ref ObjectId="213943"/>
    <cge:TPSR_Ref TObjectID="31847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213935">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31825" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50711SW"/>
     <cge:Meas_Ref ObjectId="213935"/>
    <cge:TPSR_Ref TObjectID="31825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213941">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31831" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50712SW"/>
     <cge:Meas_Ref ObjectId="213941"/>
    <cge:TPSR_Ref TObjectID="31831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213938">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1244.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31826" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50721SW"/>
     <cge:Meas_Ref ObjectId="213938"/>
    <cge:TPSR_Ref TObjectID="31826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213939">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1121.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31829" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50722SW"/>
     <cge:Meas_Ref ObjectId="213939"/>
    <cge:TPSR_Ref TObjectID="31829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213937">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -930.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31828" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50732SW"/>
     <cge:Meas_Ref ObjectId="213937"/>
    <cge:TPSR_Ref TObjectID="31828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213936">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1053.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31827" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50731SW"/>
     <cge:Meas_Ref ObjectId="213936"/>
    <cge:TPSR_Ref TObjectID="31827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66401">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20233" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50112SW"/>
     <cge:Meas_Ref ObjectId="66401"/>
    <cge:TPSR_Ref TObjectID="20233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213910">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5082.000000 -1295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31833" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_5011617SW"/>
     <cge:Meas_Ref ObjectId="213910"/>
    <cge:TPSR_Ref TObjectID="31833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66402">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20236" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50121SW"/>
     <cge:Meas_Ref ObjectId="66402"/>
    <cge:TPSR_Ref TObjectID="20236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66403">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20237" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50122SW"/>
     <cge:Meas_Ref ObjectId="66403"/>
    <cge:TPSR_Ref TObjectID="20237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66405">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20239" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50132SW"/>
     <cge:Meas_Ref ObjectId="66405"/>
    <cge:TPSR_Ref TObjectID="20239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66404">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1052.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20238" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50131SW"/>
     <cge:Meas_Ref ObjectId="66404"/>
    <cge:TPSR_Ref TObjectID="20238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66400">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1439.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20232" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50111SW"/>
     <cge:Meas_Ref ObjectId="66400"/>
    <cge:TPSR_Ref TObjectID="20232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66409">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -1440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20252" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50211SW"/>
     <cge:Meas_Ref ObjectId="66409"/>
    <cge:TPSR_Ref TObjectID="20252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66410">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -1317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20253" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50212SW"/>
     <cge:Meas_Ref ObjectId="66410"/>
    <cge:TPSR_Ref TObjectID="20253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66411">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -1244.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20254" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50221SW"/>
     <cge:Meas_Ref ObjectId="66411"/>
    <cge:TPSR_Ref TObjectID="20254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66412">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -1138.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20255" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50222SW"/>
     <cge:Meas_Ref ObjectId="66412"/>
    <cge:TPSR_Ref TObjectID="20255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66414">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -930.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20257" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50232SW"/>
     <cge:Meas_Ref ObjectId="66414"/>
    <cge:TPSR_Ref TObjectID="20257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66413">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -1053.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20256" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50231SW"/>
     <cge:Meas_Ref ObjectId="66413"/>
    <cge:TPSR_Ref TObjectID="20256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213940">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.000000 -1110.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31830" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_507267SW"/>
     <cge:Meas_Ref ObjectId="213940"/>
    <cge:TPSR_Ref TObjectID="31830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213911">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -1108.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31853" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_50236SW"/>
     <cge:Meas_Ref ObjectId="213911"/>
    <cge:TPSR_Ref TObjectID="31853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213912">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.000000 -1108.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31854" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_5023617SW"/>
     <cge:Meas_Ref ObjectId="213912"/>
    <cge:TPSR_Ref TObjectID="31854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213909">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4993.000000 -1295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31832" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅠ_50116SW"/>
     <cge:Meas_Ref ObjectId="213909"/>
    <cge:TPSR_Ref TObjectID="31832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213946">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -1298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31848" ObjectName="SW-CX_TASE2JS.YN_DaBaoⅡ_508167SW"/>
     <cge:Meas_Ref ObjectId="213946"/>
    <cge:TPSR_Ref TObjectID="31848"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3619.500000 -1674.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66361" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1442.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66361" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66362" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1427.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66362" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66363" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1412.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66363" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66364" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4710.000000 -1600.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66364" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66365" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4710.000000 -1585.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66365" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66366" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4710.000000 -1570.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66366" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="84" qtmmishow="hidden" width="398" x="3544" y="-1784"/></g>
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="131" qtmmishow="hidden" width="173" x="3445" y="-1815"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b2af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 1600.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b2db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 1585.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b3170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 1570.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b35a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5090.000000 1442.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b3850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.000000 1427.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b3a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5104.000000 1412.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="84" qtmmishow="hidden" width="398" x="3544" y="-1784"/>
    </a>
   <metadata/><rect fill="white" height="84" opacity="0" stroke="white" transform="" width="398" x="3544" y="-1784"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="131" qtmmishow="hidden" width="173" x="3445" y="-1815"/>
    </a>
   <metadata/><rect fill="white" height="131" opacity="0" stroke="white" transform="" width="173" x="3445" y="-1815"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14bc1c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 -1290.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14cc620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 -1109.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1458e80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -1107.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_149a890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -1297.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>