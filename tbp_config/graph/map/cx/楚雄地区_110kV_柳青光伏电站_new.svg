<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-320" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="349 -579 2154 1340">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_1652090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape151">
    <ellipse cx="28" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="21,7 5,7 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.240771" x1="3" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.240771" x1="0" x2="10" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.206229" x1="4" x2="6" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="39" x2="39" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="36" x2="39" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="11" y2="9"/>
    <ellipse cx="38" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="18" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="27" x2="27" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="24" x2="27" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="30" x2="27" y1="24" y2="22"/>
    <ellipse cx="27" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="13" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="16" y2="16"/>
   </symbol>
   <symbol id="lightningRod:shape183">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.466741" x1="20" x2="20" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="3" x2="3" y1="25" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="20" x2="3" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.387695" x1="6" x2="0" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.232617" x1="4" x2="2" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.452311" x1="5" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="11" x2="11" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="15" x2="15" y1="42" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338352" x1="24" x2="14" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="24" x2="24" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="28" x2="28" y1="42" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="28" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.592865" x1="30" x2="27" y1="17" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578477" x1="27" x2="27" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596084" x1="30" x2="27" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="19" x2="21" y1="26" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578485" x1="21" x2="21" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="23" x2="21" y1="26" y2="25"/>
    <ellipse cx="21" cy="24" rx="6" ry="5.5" stroke-width="0.578489"/>
    <circle cx="28" cy="16" r="5.5" stroke-width="0.578489"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.498625" x1="11" x2="4" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578485" x1="17" x2="17" y1="14" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="19" x2="17" y1="16" y2="14"/>
    <ellipse cx="17" cy="14" rx="6" ry="5" stroke-width="0.578489"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="15" x2="17" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578485" x1="24" x2="24" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="26" x2="24" y1="8" y2="6"/>
    <ellipse cx="24" cy="6" rx="6" ry="5" stroke-width="0.578489"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="22" x2="24" y1="8" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape14">
    <polyline points="9,9 3,12 1,13 1,14 1,14 3,16 6,17 10,19 11,20 11,20 11,21 10,22 6,23 3,25 2,25 2,26 2,27 3,28 6,29 10,31 11,31 11,32 11,33 10,33 6,35 3,36 2,37 2,38 2,39 3,39 6,41 10,42 11,43 11,44 11,44 10,45 6,47 3,48 1,50 1,50 1,51 3,52 9,55 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="54" y2="61"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape6_0">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="73" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="65" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="57" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape6_1">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="27" x2="35" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="43" y1="25" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="35" y1="17" y2="25"/>
   </symbol>
   <symbol id="voltageTransformer:shape1">
    <circle cx="13" cy="13" fillStyle="0" r="12" stroke-width="1"/>
    <circle cx="31" cy="13" fillStyle="0" r="12" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a491c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a49e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a91b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a92710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a93880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a94390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a94dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a95710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa3670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b67610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b68050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b62fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b645c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1350" width="2164" x="344" y="-584"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="1525" x2="1542" y1="116" y2="116"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="1542" x2="1542" y1="116" y2="94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="1542" x2="1542" y1="79" y2="75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="1542" x2="1542" y1="37" y2="58"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="2" x1="1759" x2="1759" y1="244" y2="250"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99999" x1="1762" x2="1757" y1="257" y2="257"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99999" x1="1767" x2="1752" y1="249" y2="249"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99999" x1="1764" x2="1755" y1="253" y2="253"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="0.861111" x1="1732" x2="1732" y1="215" y2="246"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="0.923077" x1="1759" x2="1759" y1="158" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1" x1="1768" x2="1759" y1="196" y2="207"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1" x1="1759" x2="1759" y1="207" y2="244"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99999" x1="1735" x2="1730" y1="258" y2="258"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99999" x1="1740" x2="1725" y1="250" y2="250"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99999" x1="1737" x2="1728" y1="254" y2="254"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="2" x1="1732" x2="1732" y1="245" y2="251"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99998" x1="1725" x2="1723" y1="164" y2="175"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99999" x1="1746" x2="1725" y1="213" y2="163"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,139,0)" stroke-width="1.99998" x1="1725" x2="1734" y1="164" y2="170"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1542,69 1537,58 1548,58 1542,69 1542,68 1542,69 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1542,83 1537,94 1548,94 1542,83 1542,84 1542,83 " stroke="rgb(170,85,127)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-298831">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1614.000000 -168.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46228" ObjectName="SW-CX_LQGF.CX_LQGF_101BK"/>
     <cge:Meas_Ref ObjectId="298831"/>
    <cge:TPSR_Ref TObjectID="46228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298846">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1613.000000 233.981481)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46237" ObjectName="SW-CX_LQGF.CX_LQGF_301BK"/>
     <cge:Meas_Ref ObjectId="298846"/>
    <cge:TPSR_Ref TObjectID="46237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298880">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1398.000000 392.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46261" ObjectName="SW-CX_LQGF.CX_LQGF_363BK"/>
     <cge:Meas_Ref ObjectId="298880"/>
    <cge:TPSR_Ref TObjectID="46261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298850">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 979.000000 408.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46240" ObjectName="SW-CX_LQGF.CX_LQGF_361BK"/>
     <cge:Meas_Ref ObjectId="298850"/>
    <cge:TPSR_Ref TObjectID="46240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298855">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1189.000000 411.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46244" ObjectName="SW-CX_LQGF.CX_LQGF_362BK"/>
     <cge:Meas_Ref ObjectId="298855"/>
    <cge:TPSR_Ref TObjectID="46244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1613.000000 416.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46248" ObjectName="SW-CX_LQGF.CX_LQGF_364BK"/>
     <cge:Meas_Ref ObjectId="298860"/>
    <cge:TPSR_Ref TObjectID="46248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298865">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1828.000000 415.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46252" ObjectName="SW-CX_LQGF.CX_LQGF_365BK"/>
     <cge:Meas_Ref ObjectId="298865"/>
    <cge:TPSR_Ref TObjectID="46252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298870">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2053.000000 411.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46255" ObjectName="SW-CX_LQGF.CX_LQGF_366BK"/>
     <cge:Meas_Ref ObjectId="298870"/>
    <cge:TPSR_Ref TObjectID="46255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2296.000000 411.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46258" ObjectName="SW-CX_LQGF.CX_LQGF_367BK"/>
     <cge:Meas_Ref ObjectId="298875"/>
    <cge:TPSR_Ref TObjectID="46258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2190.000000 -148.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2189.000000 -281.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ea99a0">
    <use class="BV-0KV" transform="matrix(0.000000 -0.562500 -0.576923 -0.000000 1718.500000 184.500000)" xlink:href="#voltageTransformer:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LQGF.CX_LQGF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,298 2477,298 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46226" ObjectName="BS-CX_LQGF.CX_LQGF_3IM"/>
    <cge:TPSR_Ref TObjectID="46226"/></metadata>
   <polyline fill="none" opacity="0" points="886,298 2477,298 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LQGF.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1612,-122 1637,-122 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47821" ObjectName="BS-CX_LQGF.XM"/>
    <cge:TPSR_Ref TObjectID="47821"/></metadata>
   <polyline fill="none" opacity="0" points="1612,-122 1637,-122 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2184.000000 -133.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2184.000000 -133.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1183.000000 563.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1183.000000 563.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LQGF.CX_LiuQGF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39817"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1588.000000 94.000000)" xlink:href="#transformer2:shape6_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1588.000000 94.000000)" xlink:href="#transformer2:shape6_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="47291" ObjectName="TF-CX_LQGF.CX_LiuQGF_1T"/>
    <cge:TPSR_Ref TObjectID="47291"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_130dff0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b23a90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1661.000000 189.981481)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b08d20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1364.000000 117.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a6d040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1316.000000 152.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1428d80">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1653.000000 -372.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_136b9a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 530.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b36a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 536.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b6ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 521.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dcad20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1402.000000 552.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1635070">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1150.000000 531.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_160b970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1193.000000 538.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1605ec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1574.000000 535.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_142a230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1617.000000 543.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f61c50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1789.000000 537.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d62e50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1832.000000 542.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cab450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 536.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_136dfe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2057.000000 541.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d75be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2257.000000 532.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14291f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2300.000000 538.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_145d730">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.000000 -198.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1651e00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 659.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da8130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.314286 148.804348)" xlink:href="#lightningRod:shape151"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f05d60">
    <use class="BV-110KV" transform="matrix(1.611111 -0.000000 0.000000 -1.309524 1535.000000 -397.000000)" xlink:href="#lightningRod:shape183"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea86d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1740.500000 211.500000)" xlink:href="#lightningRod:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 440.000000 -503.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-298743" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 472.538462 -370.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298743" ObjectName="CX_LQGF:CX_LQGF_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-298744" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 472.538462 -329.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298744" ObjectName="CX_LQGF:CX_LQGF_101BK_Q"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1762.000000 -208.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46228"/>
     <cge:Term_Ref ObjectID="32267"/>
    <cge:TPSR_Ref TObjectID="46228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1762.000000 -208.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46228"/>
     <cge:Term_Ref ObjectID="32267"/>
    <cge:TPSR_Ref TObjectID="46228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1762.000000 -208.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46228"/>
     <cge:Term_Ref ObjectID="32267"/>
    <cge:TPSR_Ref TObjectID="46228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 714.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46240"/>
     <cge:Term_Ref ObjectID="32450"/>
    <cge:TPSR_Ref TObjectID="46240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 714.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46240"/>
     <cge:Term_Ref ObjectID="32450"/>
    <cge:TPSR_Ref TObjectID="46240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 714.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46240"/>
     <cge:Term_Ref ObjectID="32450"/>
    <cge:TPSR_Ref TObjectID="46240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 713.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46244"/>
     <cge:Term_Ref ObjectID="32458"/>
    <cge:TPSR_Ref TObjectID="46244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 713.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46244"/>
     <cge:Term_Ref ObjectID="32458"/>
    <cge:TPSR_Ref TObjectID="46244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 713.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46244"/>
     <cge:Term_Ref ObjectID="32458"/>
    <cge:TPSR_Ref TObjectID="46244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1590.000000 713.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46248"/>
     <cge:Term_Ref ObjectID="32466"/>
    <cge:TPSR_Ref TObjectID="46248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1590.000000 713.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46248"/>
     <cge:Term_Ref ObjectID="32466"/>
    <cge:TPSR_Ref TObjectID="46248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298792" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1590.000000 713.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46248"/>
     <cge:Term_Ref ObjectID="32466"/>
    <cge:TPSR_Ref TObjectID="46248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298804" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1815.000000 716.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46252"/>
     <cge:Term_Ref ObjectID="32482"/>
    <cge:TPSR_Ref TObjectID="46252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1815.000000 716.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46252"/>
     <cge:Term_Ref ObjectID="32482"/>
    <cge:TPSR_Ref TObjectID="46252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1815.000000 716.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46252"/>
     <cge:Term_Ref ObjectID="32482"/>
    <cge:TPSR_Ref TObjectID="46252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298813" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2036.000000 713.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46255"/>
     <cge:Term_Ref ObjectID="32585"/>
    <cge:TPSR_Ref TObjectID="46255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2036.000000 713.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46255"/>
     <cge:Term_Ref ObjectID="32585"/>
    <cge:TPSR_Ref TObjectID="46255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298810" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2036.000000 713.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46255"/>
     <cge:Term_Ref ObjectID="32585"/>
    <cge:TPSR_Ref TObjectID="46255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298822" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2276.000000 714.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298822" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46258"/>
     <cge:Term_Ref ObjectID="32603"/>
    <cge:TPSR_Ref TObjectID="46258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2276.000000 714.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46258"/>
     <cge:Term_Ref ObjectID="32603"/>
    <cge:TPSR_Ref TObjectID="46258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298819" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2276.000000 714.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46258"/>
     <cge:Term_Ref ObjectID="32603"/>
    <cge:TPSR_Ref TObjectID="46258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298828" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 714.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46261"/>
     <cge:Term_Ref ObjectID="32609"/>
    <cge:TPSR_Ref TObjectID="46261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 714.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46261"/>
     <cge:Term_Ref ObjectID="32609"/>
    <cge:TPSR_Ref TObjectID="46261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-298763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2472.000000 202.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46226"/>
     <cge:Term_Ref ObjectID="32125"/>
    <cge:TPSR_Ref TObjectID="46226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-298764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2472.000000 202.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46226"/>
     <cge:Term_Ref ObjectID="32125"/>
    <cge:TPSR_Ref TObjectID="46226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-298765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2472.000000 202.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46226"/>
     <cge:Term_Ref ObjectID="32125"/>
    <cge:TPSR_Ref TObjectID="46226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-298769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2472.000000 202.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46226"/>
     <cge:Term_Ref ObjectID="32125"/>
    <cge:TPSR_Ref TObjectID="46226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-298766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2472.000000 202.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46226"/>
     <cge:Term_Ref ObjectID="32125"/>
    <cge:TPSR_Ref TObjectID="46226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-298770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2472.000000 202.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46226"/>
     <cge:Term_Ref ObjectID="32125"/>
    <cge:TPSR_Ref TObjectID="46226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-298759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1793.000000 58.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46268"/>
     <cge:Term_Ref ObjectID="32627"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1499.000000 189.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46237"/>
     <cge:Term_Ref ObjectID="32361"/>
    <cge:TPSR_Ref TObjectID="46237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1499.000000 189.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46237"/>
     <cge:Term_Ref ObjectID="32361"/>
    <cge:TPSR_Ref TObjectID="46237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1499.000000 189.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46237"/>
     <cge:Term_Ref ObjectID="32361"/>
    <cge:TPSR_Ref TObjectID="46237"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="452" y="-562"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="452" y="-562"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="403" y="-579"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="403" y="-579"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="452" y="-562"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="403" y="-579"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1411,579 1454,579 1454,614 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-298833">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1608.000000 -280.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46230" ObjectName="SW-CX_LQGF.CX_LQGF_1016SW"/>
     <cge:Meas_Ref ObjectId="298833"/>
    <cge:TPSR_Ref TObjectID="46230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298835">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1576.000000 -328.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46232" ObjectName="SW-CX_LQGF.CX_LQGF_10167SW"/>
     <cge:Meas_Ref ObjectId="298835"/>
    <cge:TPSR_Ref TObjectID="46232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298837">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 -224.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46234" ObjectName="SW-CX_LQGF.CX_LQGF_10160SW"/>
     <cge:Meas_Ref ObjectId="298837"/>
    <cge:TPSR_Ref TObjectID="46234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298852">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1015.000000 484.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46243" ObjectName="SW-CX_LQGF.CX_LQGF_36167SW"/>
     <cge:Meas_Ref ObjectId="298852"/>
    <cge:TPSR_Ref TObjectID="46243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298882">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1434.000000 477.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46263" ObjectName="SW-CX_LQGF.CX_LQGF_36360SW"/>
     <cge:Meas_Ref ObjectId="298882"/>
    <cge:TPSR_Ref TObjectID="46263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1225.000000 486.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46247" ObjectName="SW-CX_LQGF.CX_LQGF_36267SW"/>
     <cge:Meas_Ref ObjectId="298857"/>
    <cge:TPSR_Ref TObjectID="46247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1649.000000 491.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46251" ObjectName="SW-CX_LQGF.CX_LQGF_36467SW"/>
     <cge:Meas_Ref ObjectId="298862"/>
    <cge:TPSR_Ref TObjectID="46251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298867">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1864.000000 491.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46254" ObjectName="SW-CX_LQGF.CX_LQGF_36567SW"/>
     <cge:Meas_Ref ObjectId="298867"/>
    <cge:TPSR_Ref TObjectID="46254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298872">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2089.000000 490.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46257" ObjectName="SW-CX_LQGF.CX_LQGF_36667SW"/>
     <cge:Meas_Ref ObjectId="298872"/>
    <cge:TPSR_Ref TObjectID="46257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298877">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2332.000000 486.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46260" ObjectName="SW-CX_LQGF.CX_LQGF_36767SW"/>
     <cge:Meas_Ref ObjectId="298877"/>
    <cge:TPSR_Ref TObjectID="46260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298847">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 183.981481)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46239" ObjectName="SW-CX_LQGF.CX_LQGF_301XC1"/>
     <cge:Meas_Ref ObjectId="298847"/>
    <cge:TPSR_Ref TObjectID="46239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298847">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 269.981481)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46238" ObjectName="SW-CX_LQGF.CX_LQGF_301XC"/>
     <cge:Meas_Ref ObjectId="298847"/>
    <cge:TPSR_Ref TObjectID="46238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298883">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 619.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46264" ObjectName="SW-CX_LQGF.CX_LQGF_3636SW"/>
     <cge:Meas_Ref ObjectId="298883"/>
    <cge:TPSR_Ref TObjectID="46264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298884">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 626.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46265" ObjectName="SW-CX_LQGF.CX_LQGF_36367SW"/>
     <cge:Meas_Ref ObjectId="298884"/>
    <cge:TPSR_Ref TObjectID="46265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298881">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1397.000000 347.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46262" ObjectName="SW-CX_LQGF.CX_LQGF_363XC"/>
     <cge:Meas_Ref ObjectId="298881"/>
    <cge:TPSR_Ref TObjectID="46262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298881">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1397.000000 423.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46561" ObjectName="SW-CX_LQGF.CX_LQGF_363XC1"/>
     <cge:Meas_Ref ObjectId="298881"/>
    <cge:TPSR_Ref TObjectID="46561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298851">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.000000 438.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46242" ObjectName="SW-CX_LQGF.CX_LQGF_361XC1"/>
     <cge:Meas_Ref ObjectId="298851"/>
    <cge:TPSR_Ref TObjectID="46242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298851">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 977.000000 359.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46241" ObjectName="SW-CX_LQGF.CX_LQGF_361XC"/>
     <cge:Meas_Ref ObjectId="298851"/>
    <cge:TPSR_Ref TObjectID="46241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 440.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46246" ObjectName="SW-CX_LQGF.CX_LQGF_362XC1"/>
     <cge:Meas_Ref ObjectId="298856"/>
    <cge:TPSR_Ref TObjectID="46246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 362.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46245" ObjectName="SW-CX_LQGF.CX_LQGF_362XC"/>
     <cge:Meas_Ref ObjectId="298856"/>
    <cge:TPSR_Ref TObjectID="46245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 445.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46250" ObjectName="SW-CX_LQGF.CX_LQGF_364XC1"/>
     <cge:Meas_Ref ObjectId="298861"/>
    <cge:TPSR_Ref TObjectID="46250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 364.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46249" ObjectName="SW-CX_LQGF.CX_LQGF_364XC"/>
     <cge:Meas_Ref ObjectId="298861"/>
    <cge:TPSR_Ref TObjectID="46249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298866">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1827.000000 445.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46562" ObjectName="SW-CX_LQGF.CX_LQGF_365XC1"/>
     <cge:Meas_Ref ObjectId="298866"/>
    <cge:TPSR_Ref TObjectID="46562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298866">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1826.000000 362.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46253" ObjectName="SW-CX_LQGF.CX_LQGF_365XC"/>
     <cge:Meas_Ref ObjectId="298866"/>
    <cge:TPSR_Ref TObjectID="46253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298871">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2052.000000 444.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46563" ObjectName="SW-CX_LQGF.CX_LQGF_366XC1"/>
     <cge:Meas_Ref ObjectId="298871"/>
    <cge:TPSR_Ref TObjectID="46563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298871">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2051.000000 362.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46256" ObjectName="SW-CX_LQGF.CX_LQGF_366XC"/>
     <cge:Meas_Ref ObjectId="298871"/>
    <cge:TPSR_Ref TObjectID="46256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2295.000000 440.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46564" ObjectName="SW-CX_LQGF.CX_LQGF_367XC1"/>
     <cge:Meas_Ref ObjectId="298876"/>
    <cge:TPSR_Ref TObjectID="46564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2295.000000 363.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46259" ObjectName="SW-CX_LQGF.CX_LQGF_367XC"/>
     <cge:Meas_Ref ObjectId="298876"/>
    <cge:TPSR_Ref TObjectID="46259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298838">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1494.000000 121.981481)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46235" ObjectName="SW-CX_LQGF.CX_LQGF_1010SW"/>
     <cge:Meas_Ref ObjectId="298838"/>
    <cge:TPSR_Ref TObjectID="46235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1314.942857 247.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46267" ObjectName="SW-CX_LQGF.CX_LQGF_3901XC1"/>
     <cge:Meas_Ref ObjectId="298885"/>
    <cge:TPSR_Ref TObjectID="46267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 283.413043)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46266" ObjectName="SW-CX_LQGF.CX_LQGF_3901XC"/>
     <cge:Meas_Ref ObjectId="298885"/>
    <cge:TPSR_Ref TObjectID="46266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298836">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1572.000000 -106.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46233" ObjectName="SW-CX_LQGF.CX_LQGF_10127SW"/>
     <cge:Meas_Ref ObjectId="298836"/>
    <cge:TPSR_Ref TObjectID="46233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298832">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1608.000000 -45.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46229" ObjectName="SW-CX_LQGF.CX_LQGF_1011SW"/>
     <cge:Meas_Ref ObjectId="298832"/>
    <cge:TPSR_Ref TObjectID="46229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298834">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 -8.018519)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46231" ObjectName="SW-CX_LQGF.CX_LQGF_10117SW"/>
     <cge:Meas_Ref ObjectId="298834"/>
    <cge:TPSR_Ref TObjectID="46231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2184.000000 -194.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2183.000000 -322.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2184.000000 -396.018519)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298845">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1716.000000 154.981481)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46236" ObjectName="SW-CX_LQGF.CX_LQGF_3010SW"/>
     <cge:Meas_Ref ObjectId="298845"/>
    <cge:TPSR_Ref TObjectID="46236"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SY" endPointId="0" endStationName="CX_LQGF" flowDrawDirect="1" flowShape="0" id="AC-110kV.shangliu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1623,-464 1623,-489 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46270" ObjectName="AC-110kV.shangliu_line"/>
    <cge:TPSR_Ref TObjectID="46270_SS-320"/></metadata>
   <polyline fill="none" opacity="0" points="1623,-464 1623,-489 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1d75960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1567,-354 1557,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46232@0" ObjectIDZND0="g_19ee200@0" Pin0InfoVect0LinkObjId="g_19ee200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298835_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1567,-354 1557,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b4aeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1567,37 1567,51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46235@x" ObjectIDZND0="g_130dff0@0" Pin0InfoVect0LinkObjId="g_130dff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298838_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1567,37 1567,51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_144b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1567,37 1622,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_130dff0@0" ObjectIDND1="46235@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_130dff0_0" Pin1InfoVect1LinkObjId="SW-298838_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1567,37 1622,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12a4ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1568,-250 1558,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46234@0" ObjectIDZND0="g_19ed770@0" Pin0InfoVect0LinkObjId="g_19ed770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298837_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1568,-250 1558,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13506d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1325,157 1325,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a6d040@0" ObjectIDZND0="g_1da8130@0" Pin0InfoVect0LinkObjId="g_1da8130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6d040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1325,157 1325,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,458 1052,458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46243@1" ObjectIDZND0="g_1d73f30@0" Pin0InfoVect0LinkObjId="g_1d73f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298852_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1042,458 1052,458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a37e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,531 988,565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_13b36a0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b36a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,531 988,565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_130c330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1461,451 1471,451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46263@1" ObjectIDZND0="g_1c58ad0@0" Pin0InfoVect0LinkObjId="g_1c58ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298882_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1461,451 1471,451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b37590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,460 1262,460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46247@1" ObjectIDZND0="g_1a2c3a0@0" Pin0InfoVect0LinkObjId="g_1a2c3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298857_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1252,460 1262,460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b4b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,460 1216,460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_160b970@0" ObjectIDND1="g_1635070@0" ObjectIDND2="46246@x" ObjectIDZND0="46247@0" Pin0InfoVect0LinkObjId="SW-298857_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_160b970_0" Pin1InfoVect1LinkObjId="g_1635070_0" Pin1InfoVect2LinkObjId="SW-298856_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,460 1216,460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad3080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46247@x" ObjectIDND1="g_160b970@0" ObjectIDND2="g_1635070@0" ObjectIDZND0="46247@x" ObjectIDZND1="g_160b970@0" ObjectIDZND2="g_1635070@0" Pin0InfoVect0LinkObjId="SW-298857_0" Pin0InfoVect1LinkObjId="g_160b970_0" Pin0InfoVect2LinkObjId="g_1635070_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298857_0" Pin1InfoVect1LinkObjId="g_160b970_0" Pin1InfoVect2LinkObjId="g_1635070_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1198,460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a5d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,454 1157,454 1157,472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46247@x" ObjectIDND1="g_160b970@0" ObjectIDND2="46247@x" ObjectIDZND0="g_1635070@0" Pin0InfoVect0LinkObjId="g_1635070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298857_0" Pin1InfoVect1LinkObjId="g_160b970_0" Pin1InfoVect2LinkObjId="SW-298857_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,454 1157,454 1157,472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12acd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,460 1198,480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46247@x" ObjectIDND1="g_1635070@0" ObjectIDND2="46246@x" ObjectIDZND0="g_160b970@0" Pin0InfoVect0LinkObjId="g_160b970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298857_0" Pin1InfoVect1LinkObjId="g_1635070_0" Pin1InfoVect2LinkObjId="SW-298856_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,460 1198,480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1605c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,465 1686,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46251@1" ObjectIDZND0="g_1b55d70@0" Pin0InfoVect0LinkObjId="g_1b55d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,465 1686,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b56440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1891,465 1901,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46254@1" ObjectIDZND0="g_1f63e60@0" Pin0InfoVect0LinkObjId="g_1f63e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1891,465 1901,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12b60d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,465 1855,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d62e50@0" ObjectIDND1="g_1f61c50@0" ObjectIDND2="46562@x" ObjectIDZND0="46254@0" Pin0InfoVect0LinkObjId="SW-298867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d62e50_0" Pin1InfoVect1LinkObjId="g_1f61c50_0" Pin1InfoVect2LinkObjId="SW-298866_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1837,465 1855,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12ae330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46254@x" ObjectIDND1="g_1d62e50@0" ObjectIDND2="g_1f61c50@0" ObjectIDZND0="46254@x" ObjectIDZND1="g_1d62e50@0" ObjectIDZND2="g_1f61c50@0" Pin0InfoVect0LinkObjId="SW-298867_0" Pin0InfoVect1LinkObjId="g_1d62e50_0" Pin0InfoVect2LinkObjId="g_1f61c50_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298867_0" Pin1InfoVect1LinkObjId="g_1d62e50_0" Pin1InfoVect2LinkObjId="g_1f61c50_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1837,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12ae590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,465 1837,485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46254@x" ObjectIDND1="g_1f61c50@0" ObjectIDND2="46562@x" ObjectIDZND0="g_1d62e50@0" Pin0InfoVect0LinkObjId="g_1d62e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298867_0" Pin1InfoVect1LinkObjId="g_1f61c50_0" Pin1InfoVect2LinkObjId="SW-298866_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1837,465 1837,485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1677720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,464 2080,464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_136dfe0@0" ObjectIDND1="g_1cab450@0" ObjectIDND2="46563@x" ObjectIDZND0="46257@0" Pin0InfoVect0LinkObjId="SW-298872_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_136dfe0_0" Pin1InfoVect1LinkObjId="g_1cab450_0" Pin1InfoVect2LinkObjId="SW-298871_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,464 2080,464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cc1b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46257@x" ObjectIDND1="g_136dfe0@0" ObjectIDND2="g_1cab450@0" ObjectIDZND0="46257@x" ObjectIDZND1="g_136dfe0@0" ObjectIDZND2="g_1cab450@0" Pin0InfoVect0LinkObjId="SW-298872_0" Pin0InfoVect1LinkObjId="g_136dfe0_0" Pin0InfoVect2LinkObjId="g_1cab450_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298872_0" Pin1InfoVect1LinkObjId="g_136dfe0_0" Pin1InfoVect2LinkObjId="g_1cab450_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2062,464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cc1d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,458 2021,458 2021,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46257@x" ObjectIDND1="g_136dfe0@0" ObjectIDND2="46257@x" ObjectIDZND0="g_1cab450@0" Pin0InfoVect0LinkObjId="g_1cab450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298872_0" Pin1InfoVect1LinkObjId="g_136dfe0_0" Pin1InfoVect2LinkObjId="SW-298872_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,458 2021,458 2021,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_210d740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,464 2062,484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46257@x" ObjectIDND1="g_1cab450@0" ObjectIDND2="46563@x" ObjectIDZND0="g_136dfe0@0" Pin0InfoVect0LinkObjId="g_136dfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298872_0" Pin1InfoVect1LinkObjId="g_1cab450_0" Pin1InfoVect2LinkObjId="SW-298871_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,464 2062,484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1406d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2359,460 2369,460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46260@1" ObjectIDZND0="g_1c96760@0" Pin0InfoVect0LinkObjId="g_1c96760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298877_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2359,460 2369,460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b46a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,460 2323,460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_14291f0@0" ObjectIDND1="g_1d75be0@0" ObjectIDND2="46564@x" ObjectIDZND0="46260@0" Pin0InfoVect0LinkObjId="SW-298877_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14291f0_0" Pin1InfoVect1LinkObjId="g_1d75be0_0" Pin1InfoVect2LinkObjId="SW-298876_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,460 2323,460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b46c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46260@x" ObjectIDND1="g_14291f0@0" ObjectIDND2="g_1d75be0@0" ObjectIDZND0="46260@x" ObjectIDZND1="g_14291f0@0" ObjectIDZND2="g_1d75be0@0" Pin0InfoVect0LinkObjId="SW-298877_0" Pin0InfoVect1LinkObjId="g_14291f0_0" Pin0InfoVect2LinkObjId="g_1d75be0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298877_0" Pin1InfoVect1LinkObjId="g_14291f0_0" Pin1InfoVect2LinkObjId="g_1d75be0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2305,460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b49ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,454 2264,454 2264,473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46260@x" ObjectIDND1="g_14291f0@0" ObjectIDND2="46260@x" ObjectIDZND0="g_1d75be0@0" Pin0InfoVect0LinkObjId="g_1d75be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298877_0" Pin1InfoVect1LinkObjId="g_14291f0_0" Pin1InfoVect2LinkObjId="SW-298877_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,454 2264,454 2264,473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b49d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,460 2305,480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46260@x" ObjectIDND1="g_1d75be0@0" ObjectIDND2="46564@x" ObjectIDZND0="g_14291f0@0" Pin0InfoVect0LinkObjId="g_14291f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298877_0" Pin1InfoVect1LinkObjId="g_1d75be0_0" Pin1InfoVect2LinkObjId="SW-298876_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,460 2305,480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c48d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2103,-487 2200,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2103,-487 2200,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c48fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2200,-487 2300,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2200,-487 2300,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14fc510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2250,-256 2250,-275 2199,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_145d730@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_145d730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2250,-256 2250,-275 2199,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14fc770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2200,-459 2200,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2200,-459 2200,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c591a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,246 1622,225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46238@0" ObjectIDZND0="46237@0" Pin0InfoVect0LinkObjId="SW-298846_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298847_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,246 1622,225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dbdff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,198 1622,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46237@1" ObjectIDZND0="46239@1" Pin0InfoVect0LinkObjId="SW-298847_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298846_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,198 1622,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dbe250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,264 1622,298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46238@1" ObjectIDZND0="46226@0" Pin0InfoVect0LinkObjId="g_1f05b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298847_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,264 1622,298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a2f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2116,464 2129,464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46257@1" ObjectIDZND0="g_1a70fd0@0" Pin0InfoVect0LinkObjId="g_1a70fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298872_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2116,464 2129,464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cbfc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-203 1623,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46228@1" ObjectIDZND0="46234@x" ObjectIDZND1="46230@x" Pin0InfoVect0LinkObjId="SW-298837_0" Pin0InfoVect1LinkObjId="SW-298833_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-203 1623,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c58080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1604,-250 1623,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46234@1" ObjectIDZND0="46228@x" ObjectIDZND1="46230@x" Pin0InfoVect0LinkObjId="SW-298831_0" Pin0InfoVect1LinkObjId="SW-298833_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298837_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1604,-250 1623,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c58270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-250 1623,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46234@x" ObjectIDND1="46228@x" ObjectIDZND0="46230@0" Pin0InfoVect0LinkObjId="SW-298833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298837_0" Pin1InfoVect1LinkObjId="SW-298831_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-250 1623,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca70d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,547 1407,561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1dcad20@1" ObjectIDZND0="46264@1" Pin0InfoVect0LinkObjId="SW-298883_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcad20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,547 1407,561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a85190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1435,619 1407,619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46265@0" ObjectIDZND0="46264@x" ObjectIDZND1="g_1651e00@0" Pin0InfoVect0LinkObjId="SW-298883_0" Pin0InfoVect1LinkObjId="g_1651e00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1435,619 1407,619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a853f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,596 1407,619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46264@0" ObjectIDZND0="46265@x" ObjectIDZND1="g_1651e00@0" Pin0InfoVect0LinkObjId="SW-298884_0" Pin0InfoVect1LinkObjId="g_1651e00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298883_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1407,596 1407,619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a85650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,619 1407,629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="46264@x" ObjectIDND1="46265@x" ObjectIDZND0="g_1651e00@0" Pin0InfoVect0LinkObjId="g_1651e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298883_0" Pin1InfoVect1LinkObjId="SW-298884_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,619 1407,629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ad280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,399 1407,383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46561@1" ObjectIDZND0="46261@0" Pin0InfoVect0LinkObjId="SW-298880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298881_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,399 1407,383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ad4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,356 1407,338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46261@1" ObjectIDZND0="46262@1" Pin0InfoVect0LinkObjId="SW-298881_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,356 1407,338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ebd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,416 2305,402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46564@1" ObjectIDZND0="46258@0" Pin0InfoVect0LinkObjId="SW-298875_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298876_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,416 2305,402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ebfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,375 2305,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46258@1" ObjectIDZND0="46259@1" Pin0InfoVect0LinkObjId="SW-298876_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,375 2305,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ec210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,420 2062,402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46563@1" ObjectIDZND0="46255@0" Pin0InfoVect0LinkObjId="SW-298870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,420 2062,402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ec470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,375 2062,353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46255@1" ObjectIDZND0="46256@1" Pin0InfoVect0LinkObjId="SW-298871_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,375 2062,353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ec6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,379 1837,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46252@1" ObjectIDZND0="46253@1" Pin0InfoVect0LinkObjId="SW-298866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1837,379 1837,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ec930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,421 1622,407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46250@1" ObjectIDZND0="46248@0" Pin0InfoVect0LinkObjId="SW-298860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,421 1622,407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ecb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,380 1622,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46248@1" ObjectIDZND0="46249@1" Pin0InfoVect0LinkObjId="SW-298861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,380 1622,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ecdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,416 1198,402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46246@1" ObjectIDZND0="46244@0" Pin0InfoVect0LinkObjId="SW-298855_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,416 1198,402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ed050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,375 1198,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46244@1" ObjectIDZND0="46245@1" Pin0InfoVect0LinkObjId="SW-298856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,375 1198,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ed2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,414 988,399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46242@1" ObjectIDZND0="46240@0" Pin0InfoVect0LinkObjId="SW-298850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,414 988,399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ed510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,372 988,352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46240@1" ObjectIDZND0="46241@1" Pin0InfoVect0LinkObjId="SW-298851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,372 988,352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19eec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="947,471 947,452 988,452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_136b9a0@0" ObjectIDZND0="46242@x" ObjectIDZND1="46243@x" ObjectIDZND2="g_13b36a0@0" Pin0InfoVect0LinkObjId="SW-298851_0" Pin0InfoVect1LinkObjId="SW-298852_0" Pin0InfoVect2LinkObjId="g_13b36a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_136b9a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="947,471 947,452 988,452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19eeef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,458 988,478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="46243@x" ObjectIDND1="46242@x" ObjectIDND2="g_136b9a0@0" ObjectIDZND0="g_13b36a0@0" Pin0InfoVect0LinkObjId="g_13b36a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298852_0" Pin1InfoVect1LinkObjId="SW-298851_0" Pin1InfoVect2LinkObjId="g_136b9a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,458 988,478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ef150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1006,458 988,458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="46243@0" ObjectIDZND0="g_13b36a0@0" ObjectIDZND1="46242@x" ObjectIDZND2="g_136b9a0@0" Pin0InfoVect0LinkObjId="g_13b36a0_0" Pin0InfoVect1LinkObjId="SW-298851_0" Pin0InfoVect2LinkObjId="g_136b9a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298852_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1006,458 988,458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ef3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,460 1198,454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="46247@x" ObjectIDND1="g_160b970@0" ObjectIDND2="46247@x" ObjectIDZND0="g_1635070@0" ObjectIDZND1="46246@x" Pin0InfoVect0LinkObjId="g_1635070_0" Pin0InfoVect1LinkObjId="SW-298856_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298857_0" Pin1InfoVect1LinkObjId="g_160b970_0" Pin1InfoVect2LinkObjId="SW-298857_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1198,460 1198,454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ef610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,454 1198,433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="46247@x" ObjectIDND1="g_160b970@0" ObjectIDND2="46247@x" ObjectIDZND0="46246@0" Pin0InfoVect0LinkObjId="SW-298856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298857_0" Pin1InfoVect1LinkObjId="g_160b970_0" Pin1InfoVect2LinkObjId="SW-298857_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,454 1198,433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ef870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,464 2062,458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="46257@x" ObjectIDND1="g_136dfe0@0" ObjectIDND2="46257@x" ObjectIDZND0="g_1cab450@0" ObjectIDZND1="46563@x" Pin0InfoVect0LinkObjId="g_1cab450_0" Pin0InfoVect1LinkObjId="SW-298871_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298872_0" Pin1InfoVect1LinkObjId="g_136dfe0_0" Pin1InfoVect2LinkObjId="SW-298872_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2062,464 2062,458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19efad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,458 2062,437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="46257@x" ObjectIDND1="g_136dfe0@0" ObjectIDND2="46257@x" ObjectIDZND0="46563@0" Pin0InfoVect0LinkObjId="SW-298871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298872_0" Pin1InfoVect1LinkObjId="g_136dfe0_0" Pin1InfoVect2LinkObjId="SW-298872_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,458 2062,437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19efd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,460 2305,454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="46260@x" ObjectIDND1="g_14291f0@0" ObjectIDND2="46260@x" ObjectIDZND0="g_1d75be0@0" ObjectIDZND1="46564@x" Pin0InfoVect0LinkObjId="g_1d75be0_0" Pin0InfoVect1LinkObjId="SW-298876_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298877_0" Pin1InfoVect1LinkObjId="g_14291f0_0" Pin1InfoVect2LinkObjId="SW-298877_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2305,460 2305,454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19eff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,454 2305,433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="46260@x" ObjectIDND1="g_14291f0@0" ObjectIDND2="46260@x" ObjectIDZND0="46564@0" Pin0InfoVect0LinkObjId="SW-298876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298877_0" Pin1InfoVect1LinkObjId="g_14291f0_0" Pin1InfoVect2LinkObjId="SW-298877_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,454 2305,433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da72f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,431 988,452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="46242@0" ObjectIDZND0="g_136b9a0@0" ObjectIDZND1="46243@x" ObjectIDZND2="g_13b36a0@0" Pin0InfoVect0LinkObjId="g_136b9a0_0" Pin0InfoVect1LinkObjId="SW-298852_0" Pin0InfoVect2LinkObjId="g_13b36a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="988,431 988,452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da7550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,452 988,458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46242@x" ObjectIDND1="g_136b9a0@0" ObjectIDZND0="46243@x" ObjectIDZND1="g_13b36a0@0" Pin0InfoVect0LinkObjId="SW-298852_0" Pin0InfoVect1LinkObjId="g_13b36a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298851_0" Pin1InfoVect1LinkObjId="g_136b9a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="988,452 988,458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da77b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,533 1198,599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_160b970@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160b970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,533 1198,599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da7a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,538 1622,574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_142a230@1" ObjectIDZND0="46557@0" Pin0InfoVect0LinkObjId="SM-CX_LQGF.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_142a230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,538 1622,574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da7c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,537 1837,573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1d62e50@1" ObjectIDZND0="46558@0" Pin0InfoVect0LinkObjId="SM-CX_LQGF.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d62e50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1837,537 1837,573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da7ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,533 2305,569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_14291f0@1" ObjectIDZND0="46560@0" Pin0InfoVect0LinkObjId="SM-CX_LQGF.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14291f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,533 2305,569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1daa420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1796,478 1796,459 1837,459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1f61c50@0" ObjectIDZND0="46254@x" ObjectIDZND1="g_1d62e50@1" ObjectIDZND2="46254@x" Pin0InfoVect0LinkObjId="SW-298867_0" Pin0InfoVect1LinkObjId="g_1d62e50_1" Pin0InfoVect2LinkObjId="SW-298867_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f61c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1796,478 1796,459 1837,459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1daa680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,459 1837,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1f61c50@0" ObjectIDND1="46562@x" ObjectIDZND0="46254@x" ObjectIDZND1="g_1d62e50@0" ObjectIDZND2="46254@x" Pin0InfoVect0LinkObjId="SW-298867_0" Pin0InfoVect1LinkObjId="g_1d62e50_0" Pin0InfoVect2LinkObjId="SW-298867_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f61c50_0" Pin1InfoVect1LinkObjId="SW-298866_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1837,459 1837,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1daa8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,459 1837,438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="46254@x" ObjectIDND1="g_1d62e50@1" ObjectIDND2="46254@x" ObjectIDZND0="46562@0" Pin0InfoVect0LinkObjId="SW-298866_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298867_0" Pin1InfoVect1LinkObjId="g_1d62e50_1" Pin1InfoVect2LinkObjId="SW-298867_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1837,459 1837,438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1daab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1837,421 1837,406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46562@1" ObjectIDZND0="46252@0" Pin0InfoVect0LinkObjId="SW-298865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1837,421 1837,406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc2ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1525,130 1525,116 1509,116 1509,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ca9b10@0" ObjectIDZND0="46235@0" Pin0InfoVect0LinkObjId="SW-298838_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ca9b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1525,130 1525,116 1509,116 1509,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc2f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1509,64 1509,37 1567,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46235@1" ObjectIDZND0="g_130dff0@0" Pin0InfoVect0LinkObjId="g_130dff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298838_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1509,64 1509,37 1567,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc3170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-354 1623,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46232@1" ObjectIDZND0="46230@x" ObjectIDZND1="g_1f05d60@0" ObjectIDZND2="g_1428d80@0" Pin0InfoVect0LinkObjId="SW-298833_0" Pin0InfoVect1LinkObjId="g_1f05d60_0" Pin0InfoVect2LinkObjId="g_1428d80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-354 1623,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc33d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-338 1623,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46230@1" ObjectIDZND0="46232@x" ObjectIDZND1="g_1f05d60@0" ObjectIDZND2="g_1428d80@0" Pin0InfoVect0LinkObjId="SW-298835_0" Pin0InfoVect1LinkObjId="g_1f05d60_0" Pin0InfoVect2LinkObjId="g_1428d80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-338 1623,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc3630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1589,-446 1623,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1f05d60@0" ObjectIDZND0="46230@x" ObjectIDZND1="46232@x" ObjectIDZND2="g_1428d80@0" Pin0InfoVect0LinkObjId="SW-298833_0" Pin0InfoVect1LinkObjId="SW-298835_0" Pin0InfoVect2LinkObjId="g_1428d80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f05d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-446 1623,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc3890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-354 1623,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="46230@x" ObjectIDND1="46232@x" ObjectIDZND0="g_1f05d60@0" ObjectIDZND1="g_1428d80@0" ObjectIDZND2="46270@1" Pin0InfoVect0LinkObjId="g_1f05d60_0" Pin0InfoVect1LinkObjId="g_1428d80_0" Pin0InfoVect2LinkObjId="g_1dc3af0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298833_0" Pin1InfoVect1LinkObjId="SW-298835_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-354 1623,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc3af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-446 1623,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="46230@x" ObjectIDND1="46232@x" ObjectIDND2="g_1f05d60@0" ObjectIDZND0="46270@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298833_0" Pin1InfoVect1LinkObjId="SW-298835_0" Pin1InfoVect2LinkObjId="g_1f05d60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-446 1623,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1dc3d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-446 1660,-446 1660,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="46230@x" ObjectIDND1="46232@x" ObjectIDND2="g_1f05d60@0" ObjectIDZND0="g_1428d80@0" Pin0InfoVect0LinkObjId="g_1428d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298833_0" Pin1InfoVect1LinkObjId="SW-298835_0" Pin1InfoVect2LinkObjId="g_1f05d60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-446 1660,-446 1660,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_137e0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1325,240 1325,261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="46267@1" ObjectIDZND0="46266@1" Pin0InfoVect0LinkObjId="SW-298885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298885_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1325,240 1325,261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_137e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1325,208 1325,223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a6d040@0" ObjectIDND1="g_1b08d20@0" ObjectIDZND0="46267@0" Pin0InfoVect0LinkObjId="SW-298885_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a6d040_0" Pin1InfoVect1LinkObjId="g_1b08d20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1325,208 1325,223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_137e570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1325,188 1325,210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1a6d040@1" ObjectIDZND0="46267@x" ObjectIDZND1="g_1b08d20@0" Pin0InfoVect0LinkObjId="SW-298885_0" Pin0InfoVect1LinkObjId="g_1b08d20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6d040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1325,188 1325,210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_137e7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1325,210 1371,210 1371,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1a6d040@0" ObjectIDND1="46267@x" ObjectIDZND0="g_1b08d20@0" Pin0InfoVect0LinkObjId="g_1b08d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a6d040_0" Pin1InfoVect1LinkObjId="SW-298885_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1325,210 1371,210 1371,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_137ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="987,298 987,335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46226@0" ObjectIDZND0="46241@0" Pin0InfoVect0LinkObjId="SW-298851_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbe250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="987,298 987,335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f04cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,298 1198,338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46226@0" ObjectIDZND0="46245@0" Pin0InfoVect0LinkObjId="SW-298856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbe250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,298 1198,338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f04f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1836,298 1836,338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46226@0" ObjectIDZND0="46253@0" Pin0InfoVect0LinkObjId="SW-298866_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbe250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1836,298 1836,338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f05180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2061,298 2061,338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46226@0" ObjectIDZND0="46256@0" Pin0InfoVect0LinkObjId="SW-298871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbe250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2061,298 2061,338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f053e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,298 1622,340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46226@0" ObjectIDZND0="46249@0" Pin0InfoVect0LinkObjId="SW-298861_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbe250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,298 1622,340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f05640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2305,298 2305,339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46226@0" ObjectIDZND0="46259@0" Pin0InfoVect0LinkObjId="SW-298876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbe250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2305,298 2305,339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f058a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,298 1407,323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46226@0" ObjectIDZND0="46262@0" Pin0InfoVect0LinkObjId="SW-298881_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbe250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,298 1407,323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f05b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1325,276 1325,298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46266@0" ObjectIDZND0="46226@0" Pin0InfoVect0LinkObjId="g_1dbe250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1325,276 1325,298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f08ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-134 1563,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f53c10@0" ObjectIDZND0="46233@0" Pin0InfoVect0LinkObjId="SW-298836_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f53c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-134 1563,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f546a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-138 1623,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" EndDevType1="busSection" ObjectIDZND0="46228@x" ObjectIDZND1="47821@0" Pin0InfoVect0LinkObjId="SW-298831_0" Pin0InfoVect1LinkObjId="g_13f3b20_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-138 1623,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f54900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-138 1623,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="47821@0" ObjectIDZND0="46228@0" Pin0InfoVect0LinkObjId="SW-298831_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f3b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-138 1623,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13f3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-138 1623,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="46228@x" ObjectIDZND0="47821@0" Pin0InfoVect0LinkObjId="g_1e09da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-138 1623,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1668,131 1668,112 1622,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1b23a90@0" ObjectIDZND0="46239@x" ObjectIDZND1="47291@x" Pin0InfoVect0LinkObjId="SW-298847_0" Pin0InfoVect1LinkObjId="g_13f4240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b23a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1668,131 1668,112 1622,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,160 1622,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="46239@0" ObjectIDZND0="g_1b23a90@0" ObjectIDZND1="47291@x" Pin0InfoVect0LinkObjId="g_1b23a90_0" Pin0InfoVect1LinkObjId="g_13f4240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298847_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1622,160 1622,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,112 1622,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="46239@x" ObjectIDND1="g_1b23a90@0" ObjectIDZND0="47291@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298847_0" Pin1InfoVect1LinkObjId="g_1b23a90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,112 1622,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13f44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,-34 1568,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_13f71b0@0" ObjectIDZND0="46231@0" Pin0InfoVect0LinkObjId="SW-298834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f71b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,-34 1568,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13f7c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1604,-34 1623,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="46231@1" ObjectIDZND0="46229@x" ObjectIDZND1="47291@x" Pin0InfoVect0LinkObjId="SW-298832_0" Pin0InfoVect1LinkObjId="g_13f4240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1604,-34 1623,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13f7ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,9 1623,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47291@0" ObjectIDZND0="46231@x" ObjectIDZND1="46229@x" Pin0InfoVect0LinkObjId="SW-298834_0" Pin0InfoVect1LinkObjId="SW-298832_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f4240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1623,9 1623,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13f8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-34 1623,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="46231@x" ObjectIDND1="47291@x" ObjectIDZND0="46229@0" Pin0InfoVect0LinkObjId="SW-298832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298834_0" Pin1InfoVect1LinkObjId="g_13f4240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-34 1623,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13f8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2199,-128 2199,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2199,-128 2199,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ddcc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2199,-183 2199,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2199,-183 2199,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c7ea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2199,-290 2199,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_145d730@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_145d730_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2199,-290 2199,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c7ec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2199,-275 2199,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_145d730@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_145d730_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2199,-275 2199,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ea6300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2199,-384 2199,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2199,-384 2199,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ea6560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2198,-316 2198,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2198,-316 2198,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ea9740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1732,207 1732,215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1ea86d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ea86d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1732,207 1732,215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eaa020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1732,150 1711,150 1711,160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1ea86d0@1" ObjectIDZND0="g_1ea99a0@0" Pin0InfoVect0LinkObjId="g_1ea99a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ea86d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1732,150 1711,150 1711,160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eaa280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1732,97 1732,78 1621,78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="46236@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298845_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1732,97 1732,78 1621,78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e1e910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1758,168 1758,151 1731,151 1731,133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="46236@0" Pin0InfoVect0LinkObjId="SW-298845_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1758,168 1758,151 1731,151 1731,133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1640,465 1622,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46251@0" ObjectIDZND0="46251@x" ObjectIDZND1="46251@x" ObjectIDZND2="46251@x" Pin0InfoVect0LinkObjId="SW-298862_0" Pin0InfoVect1LinkObjId="SW-298862_0" Pin0InfoVect2LinkObjId="SW-298862_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1640,465 1622,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1b3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46251@x" ObjectIDND1="46251@x" ObjectIDND2="46251@x" ObjectIDZND0="46251@x" ObjectIDZND1="46251@x" ObjectIDZND2="46251@x" Pin0InfoVect0LinkObjId="SW-298862_0" Pin0InfoVect1LinkObjId="SW-298862_0" Pin0InfoVect2LinkObjId="SW-298862_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298862_0" Pin1InfoVect1LinkObjId="SW-298862_0" Pin1InfoVect2LinkObjId="SW-298862_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1622,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d1b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1622,485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1b7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,465 1622,459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46251@x" ObjectIDND1="46251@x" ObjectIDND2="46251@x" ObjectIDZND0="46251@x" ObjectIDZND1="46251@x" ObjectIDZND2="46251@x" Pin0InfoVect0LinkObjId="SW-298862_0" Pin0InfoVect1LinkObjId="SW-298862_0" Pin0InfoVect2LinkObjId="SW-298862_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298862_0" Pin1InfoVect1LinkObjId="SW-298862_0" Pin1InfoVect2LinkObjId="SW-298862_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1622,465 1622,459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,459 1622,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46251@x" ObjectIDND1="46251@x" ObjectIDND2="46251@x" ObjectIDZND0="46251@x" ObjectIDZND1="46251@x" ObjectIDZND2="46251@x" Pin0InfoVect0LinkObjId="SW-298862_0" Pin0InfoVect1LinkObjId="SW-298862_0" Pin0InfoVect2LinkObjId="SW-298862_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298862_0" Pin1InfoVect1LinkObjId="SW-298862_0" Pin1InfoVect2LinkObjId="SW-298862_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1622,459 1622,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1de1770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,572 2062,536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" ObjectIDND0="46559@0" ObjectIDZND0="g_136dfe0@1" Pin0InfoVect0LinkObjId="g_136dfe0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_LQGF.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,572 2062,536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,465 1622,459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="46251@x" ObjectIDND1="46251@x" ObjectIDND2="46251@x" ObjectIDZND0="46251@x" ObjectIDZND1="46251@x" ObjectIDZND2="g_1605ec0@0" Pin0InfoVect0LinkObjId="SW-298862_0" Pin0InfoVect1LinkObjId="SW-298862_0" Pin0InfoVect2LinkObjId="g_1605ec0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298862_0" Pin1InfoVect1LinkObjId="SW-298862_0" Pin1InfoVect2LinkObjId="SW-298862_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1622,465 1622,459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,459 1581,459 1581,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="46251@x" ObjectIDND1="46251@x" ObjectIDND2="46251@x" ObjectIDZND0="g_1605ec0@0" Pin0InfoVect0LinkObjId="g_1605ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298862_0" Pin1InfoVect1LinkObjId="SW-298862_0" Pin1InfoVect2LinkObjId="SW-298862_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1622,459 1581,459 1581,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,451 1407,494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="46263@x" ObjectIDND1="46561@x" ObjectIDND2="g_12b6ea0@0" ObjectIDZND0="g_1dcad20@0" Pin0InfoVect0LinkObjId="g_1dcad20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298882_0" Pin1InfoVect1LinkObjId="SW-298881_0" Pin1InfoVect2LinkObjId="g_12b6ea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,451 1407,494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e04b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1425,451 1413,451 1407,451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46263@0" ObjectIDZND0="46561@x" ObjectIDZND1="g_1dcad20@0" ObjectIDZND2="g_12b6ea0@0" Pin0InfoVect0LinkObjId="SW-298881_0" Pin0InfoVect1LinkObjId="g_1dcad20_0" Pin0InfoVect2LinkObjId="g_12b6ea0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1425,451 1413,451 1407,451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e04d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,451 1407,416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="46263@x" ObjectIDND1="g_1dcad20@0" ObjectIDND2="g_12b6ea0@0" ObjectIDZND0="46561@0" Pin0InfoVect0LinkObjId="SW-298881_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298882_0" Pin1InfoVect1LinkObjId="g_1dcad20_0" Pin1InfoVect2LinkObjId="g_12b6ea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,451 1407,416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e04ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,451 1365,451 1364,452 1364,462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="46263@x" ObjectIDND1="46561@x" ObjectIDND2="g_1dcad20@0" ObjectIDZND0="g_12b6ea0@0" Pin0InfoVect0LinkObjId="g_12b6ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298882_0" Pin1InfoVect1LinkObjId="SW-298881_0" Pin1InfoVect2LinkObjId="g_1dcad20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,451 1365,451 1364,452 1364,462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e05240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1640,465 1622,465 1622,438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="46250@0" Pin0InfoVect0LinkObjId="SW-298861_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1640,465 1622,465 1622,438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e054a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1622,485 1622,465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_142a230@0" ObjectIDZND0="46251@x" ObjectIDZND1="46251@x" ObjectIDZND2="46251@x" Pin0InfoVect0LinkObjId="SW-298862_0" Pin0InfoVect1LinkObjId="SW-298862_0" Pin0InfoVect2LinkObjId="SW-298862_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_142a230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1622,485 1622,465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e09da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1623,-103 1623,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46229@1" ObjectIDZND0="47821@0" Pin0InfoVect0LinkObjId="g_13f3b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298832_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1623,-103 1623,-122 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-298348" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 651.000000 -458.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46206" ObjectName="DYN-CX_LQGF"/>
     <cge:Meas_Ref ObjectId="298348"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f601c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2417.000000 -201.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f60440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2417.000000 -216.400000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f60680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2417.000000 -231.800000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f608c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2423.000000 -247.200000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f60b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2409.000000 -262.600000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f60d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2423.000000 -278.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1705.000000 213.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f612d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1694.000000 198.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1719.000000 183.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 870.000000 -713.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.000000 -728.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de0ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -743.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e06370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1434.000000 -190.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e065d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.000000 -205.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e06810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1448.000000 -220.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1621.000000 1700.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1657" x2="1665" y1="-31" y2="-47"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1648" x2="1665" y1="-47" y2="-47"/>
    <circle DF8003:Layer="PUBLIC" cx="1657" cy="-36" fill="none" fillStyle="0" r="24" stroke="rgb(60,120,255)" stroke-width="0.510204"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1657" x2="1648" y1="-31" y2="-47"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1657" x2="1665" y1="-31" y2="-47"/></g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_LQGF.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2300.000000 590.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46560" ObjectName="SM-CX_LQGF.P4"/>
    <cge:TPSR_Ref TObjectID="46560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LQGF.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2057.000000 593.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46559" ObjectName="SM-CX_LQGF.P3"/>
    <cge:TPSR_Ref TObjectID="46559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LQGF.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1832.000000 594.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46558" ObjectName="SM-CX_LQGF.P2"/>
    <cge:TPSR_Ref TObjectID="46558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LQGF.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1617.000000 595.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46557" ObjectName="SM-CX_LQGF.P1"/>
    <cge:TPSR_Ref TObjectID="46557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 586.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2082.000000 -492.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="987" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="1198" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="1836" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="1622" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="2305" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="1407" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="1622" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="1325" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46226" cx="2061" cy="298" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47821" cx="1623" cy="-122" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47821" cx="1623" cy="-122" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,139,0)" stroke-width="1" width="10" x="1727" y="222"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,139,0)" stroke-width="1" width="10" x="1754" y="216"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a340b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -410.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a340b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -410.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a340b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -410.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a340b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -410.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a340b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -410.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a340b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -410.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a340b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -410.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1b20bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 -548.500000) translate(0,16)">柳青光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dbe4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2077.000000 -101.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12b3cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -242.000000) translate(0,15)">SCB-13-400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12b3cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -242.000000) translate(0,33)">10±2X2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12b3cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -242.000000) translate(0,51)">D,YN11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12b3cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -242.000000) translate(0,69)">Ud=6%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1449a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 62.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1449a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 62.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ca51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -117.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ca51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -117.000000) translate(0,33)">SZ18-120000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ca51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -117.000000) translate(0,51)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ca51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -117.000000) translate(0,69)">115±8х1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ca51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1697.000000 -117.000000) translate(0,87)">Ud%=12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a74c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 611.000000) translate(0,15)">储能回路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a7750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1152.000000 666.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 613.000000) translate(0,15)">    1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 613.000000) translate(0,33)">（35-46号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12c19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1773.000000 613.000000) translate(0,15)">    2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12c19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1773.000000 613.000000) translate(0,33)">（19,23-34号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12c1c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1996.000000 609.000000) translate(0,15)">    3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12c1c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1996.000000 609.000000) translate(0,33)">（10-18,20-22号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12c1f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 609.000000) translate(0,15)">    4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12c1f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 609.000000) translate(0,33)">（1-9号升压变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161cc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1633.000000 -196.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161ce80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1566.000000 -275.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161d090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 204.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161d2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2321.000000 434.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2078.000000 438.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2ff40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1853.000000 439.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a717b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1648.000000 442.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cbf550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 434.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cbf790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1425.000000 424.000000) translate(0,12)">36360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cbf9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.000000 432.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c58460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -327.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c587f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1566.000000 -380.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea67c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 663.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea67c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 663.000000) translate(0,33)">      38MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea7100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 605.000000) translate(0,15)">SCB13-400/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea7100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 605.000000) translate(0,33)">37±2X2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea7100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 605.000000) translate(0,51)">D,YN11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea7100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 605.000000) translate(0,69)">Ud=4%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e1eb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1786.000000 145.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e1eb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1786.000000 145.000000) translate(0,33)">35kV中性点成</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e1eb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1786.000000 145.000000) translate(0,51)">套接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e20200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1606.000000 -525.000000) translate(0,15)">上柳线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e20a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2151.000000 -519.000000) translate(0,15)">10kV松平抽水支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2130.000000 -470.000000) translate(0,15)">7号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 -416.000000) translate(0,15)">柳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 -416.000000) translate(0,33)">青</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 -416.000000) translate(0,51)">光</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 -416.000000) translate(0,69)">伏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 -416.000000) translate(0,87)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 -416.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d16360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1906.000000 -508.000000) translate(0,15)">至35kV黑井变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d16360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1906.000000 -508.000000) translate(0,33)">10kV红石岩065断路器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d18ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1575.000000 -160.000000) translate(0,12)">10127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d18fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -91.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -55.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 70.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1739.000000 108.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d198d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 378.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 380.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 362.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 565.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1438.000000 625.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1635.000000 393.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1850.000000 384.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2073.000000 383.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1aad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2314.000000 380.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ad10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.000000 278.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(224,224,224)" font-family="SimSun" font-size="15" graphid="g_1d1b140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1337.000000 260.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1bbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2216.000000 -455.000000) translate(0,12)">A011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5bec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2222.000000 -369.000000) translate(0,12)">A012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5c0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2226.000000 -311.000000) translate(0,12)">A01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5c310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2149.000000 -246.000000) translate(0,12)">A021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5c550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2223.000000 -172.000000) translate(0,12)">A02</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(239,239,239)" font-family="SimSun" font-size="20" graphid="g_1f5efa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1704.000000 26.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de0d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1753.000000 59.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_138e260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1940.000000 263.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e088c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -60.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e08e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 298.000000) translate(0,15)">19806988546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e08e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 298.000000) translate(0,33)">18748958416 李(站长)</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ca9b10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 1516.000000 155.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d73f30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 467.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c58ad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 460.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2c3a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.000000 469.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b55d70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 474.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f63e60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1896.000000 474.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c96760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2364.000000 469.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a70fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2124.000000 473.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ed770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 -241.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ee200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1532.000000 -345.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f53c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -125.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f71b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 -25.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LQGF"/>
</svg>