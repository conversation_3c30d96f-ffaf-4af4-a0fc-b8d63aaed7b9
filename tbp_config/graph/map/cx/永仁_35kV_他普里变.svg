<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-195" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="1363 -1463 2022 1167">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape134">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <ellipse cx="37" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="42" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="37" x2="37" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="39" x2="37" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="37" x2="34" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="31" x2="31" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="33" x2="31" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="31" x2="28" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="43" x2="43" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="48" x2="43" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="48" x2="43" y1="8" y2="10"/>
    <ellipse cx="31" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="22" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="22" y2="22"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="67" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="37" y1="67" y2="67"/>
    <rect height="27" stroke-width="0.416667" width="14" x="30" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="67" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="78" y2="68"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape6_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="38" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="38" y2="13"/>
   </symbol>
   <symbol id="switch2:shape6_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="13" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="11" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape2">
    <circle cx="7" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29cc780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29cd160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29cdb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29ced50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29d0040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29d0ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29d1880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29d2280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29d2c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29d2c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29d5b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29d5b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29d79f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29d79f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_29d8a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29da370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29dafc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29dbe70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29dc750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ddf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29de710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29dee00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29df820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29e0a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29e1380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29e1e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29e2830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29e3d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29e4880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29e58b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29e64f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29f4cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29e7de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29e93d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29ea900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1177" width="2032" x="1358" y="-1468"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-140382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.333333 -1162.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25128" ObjectName="SW-YR_TPL.YR_TPL_383BK"/>
     <cge:Meas_Ref ObjectId="140382"/>
    <cge:TPSR_Ref TObjectID="25128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140645">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -586.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25144" ObjectName="SW-YR_TPL.YR_TPL_083BK"/>
     <cge:Meas_Ref ObjectId="140645"/>
    <cge:TPSR_Ref TObjectID="25144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.200000 -962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28875" ObjectName="SW-YR_TPL.YR_TPL_301BK"/>
     <cge:Meas_Ref ObjectId="190324"/>
    <cge:TPSR_Ref TObjectID="28875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2568.333333 -1162.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25124" ObjectName="SW-YR_TPL.YR_TPL_382BK"/>
     <cge:Meas_Ref ObjectId="140331"/>
    <cge:TPSR_Ref TObjectID="25124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.333333 -1176.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25119" ObjectName="SW-YR_TPL.YR_TPL_381BK"/>
     <cge:Meas_Ref ObjectId="140279"/>
    <cge:TPSR_Ref TObjectID="25119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140433">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.200000 -961.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25156" ObjectName="SW-YR_TPL.YR_TPL_302BK"/>
     <cge:Meas_Ref ObjectId="140433"/>
    <cge:TPSR_Ref TObjectID="25156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140438">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2264.200000 -797.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25157" ObjectName="SW-YR_TPL.YR_TPL_002BK"/>
     <cge:Meas_Ref ObjectId="140438"/>
    <cge:TPSR_Ref TObjectID="25157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -587.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25141" ObjectName="SW-YR_TPL.YR_TPL_082BK"/>
     <cge:Meas_Ref ObjectId="140599"/>
    <cge:TPSR_Ref TObjectID="25141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140553">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2440.000000 -586.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25138" ObjectName="SW-YR_TPL.YR_TPL_081BK"/>
     <cge:Meas_Ref ObjectId="140553"/>
    <cge:TPSR_Ref TObjectID="25138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3274.000000 -586.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41923" ObjectName="SW-YR_TPL.YR_TPL_601BK"/>
     <cge:Meas_Ref ObjectId="251161"/>
    <cge:TPSR_Ref TObjectID="41923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2943.000000 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41922" ObjectName="SW-YR_TPL.YR_TPL_602BK"/>
     <cge:Meas_Ref ObjectId="251162"/>
    <cge:TPSR_Ref TObjectID="41922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190319">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.000000 -798.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28877" ObjectName="SW-YR_TPL.YR_TPL_001BK"/>
     <cge:Meas_Ref ObjectId="190319"/>
    <cge:TPSR_Ref TObjectID="28877"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22f5540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2841.000000 -1244.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YR_TPL" endPointId="0" endStationName="YR_ZH" flowDrawDirect="1" flowShape="0" id="AC-35kV.zhongta_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2913,-1397 2913,-1357 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="33969" ObjectName="AC-35kV.zhongta_line"/>
    <cge:TPSR_Ref TObjectID="33969_SS-195"/></metadata>
   <polyline fill="none" opacity="0" points="2913,-1397 2913,-1357 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YR_MH" endPointId="0" endStationName="YR_TPL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_mengta" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2577,-1343 2577,-1391 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38085" ObjectName="AC-35kV.LN_mengta"/>
    <cge:TPSR_Ref TObjectID="38085_SS-195"/></metadata>
   <polyline fill="none" opacity="0" points="2577,-1343 2577,-1391 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YR_TPL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_wantamahongTtp" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2274,-1400 2274,-1347 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37859" ObjectName="AC-35kV.LN_wantamahongTtp"/>
    <cge:TPSR_Ref TObjectID="37859_SS-195"/></metadata>
   <polyline fill="none" opacity="0" points="2274,-1400 2274,-1347 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YR_TPL.YR_TPL_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35477"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2239.000000 -856.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2239.000000 -856.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25155" ObjectName="TF-YR_TPL.YR_TPL_2T"/>
    <cge:TPSR_Ref TObjectID="25155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1893.000000 -449.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1893.000000 -449.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2633.000000 -460.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2633.000000 -460.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_TPL.YR_TPL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41153"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2888.000000 -857.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2888.000000 -857.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28881" ObjectName="TF-YR_TPL.YR_TPL_1T"/>
    <cge:TPSR_Ref TObjectID="28881"/></metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3259.000000 -453.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2928.000000 -452.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_229b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-970 2913,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28875@0" ObjectIDZND0="28881@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-970 2913,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229b4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1025 2913,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28878@0" ObjectIDZND0="28875@1" Pin0InfoVect0LinkObjId="SW-190324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1025 2913,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_228f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1148 2274,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25129@1" ObjectIDZND0="25128@0" Pin0InfoVect0LinkObjId="SW-140382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1148 2274,-1170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_228faa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1197 2274,-1221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25128@1" ObjectIDZND0="25130@0" Pin0InfoVect0LinkObjId="SW-140385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1197 2274,-1221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2262210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1147 2577,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25125@1" ObjectIDZND0="25124@0" Pin0InfoVect0LinkObjId="SW-140331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140333_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1147 2577,-1170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2262400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1197 2577,-1221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25124@1" ObjectIDZND0="25126@0" Pin0InfoVect0LinkObjId="SW-140334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1197 2577,-1221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22625f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1111 2577,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25125@0" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_22a3ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1111 2577,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a3900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-690 2022,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25145@1" ObjectIDZND0="25117@0" Pin0InfoVect0LinkObjId="g_22603c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140647_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-690 2022,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a3af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-594 2022,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25144@0" ObjectIDZND0="25146@1" Pin0InfoVect0LinkObjId="SW-140648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-594 2022,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a3ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1112 2274,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25129@0" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_22625f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1112 2274,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a3ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1112 2913,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25120@0" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_22625f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1112 2913,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a40c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1061 2913,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28878@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_22625f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190326_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1061 2913,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_228c840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2369,-1306 2351,-1306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2284be0@0" ObjectIDZND0="25131@0" Pin0InfoVect0LinkObjId="SW-140386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2284be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2369,-1306 2351,-1306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a7760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2315,-1306 2274,-1306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="25131@1" ObjectIDZND0="25132@x" ObjectIDZND1="25130@x" ObjectIDZND2="37859@1" Pin0InfoVect0LinkObjId="SW-140387_0" Pin0InfoVect1LinkObjId="SW-140385_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2315,-1306 2274,-1306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a8130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1347 2274,-1306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37859@1" ObjectIDZND0="25131@x" ObjectIDZND1="25132@x" ObjectIDZND2="25130@x" Pin0InfoVect0LinkObjId="SW-140386_0" Pin0InfoVect1LinkObjId="SW-140387_0" Pin0InfoVect2LinkObjId="SW-140385_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1347 2274,-1306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2296740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2143,-1218 2143,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25132@0" ObjectIDZND0="g_22a8350@0" Pin0InfoVect0LinkObjId="g_22a8350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2143,-1218 2143,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f44e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2672,-1306 2654,-1306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2296960@0" ObjectIDZND0="25127@0" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2296960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2672,-1306 2654,-1306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2618,-1306 2577,-1306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="25127@1" ObjectIDZND0="25126@x" ObjectIDZND1="38085@1" Pin0InfoVect0LinkObjId="SW-140334_0" Pin0InfoVect1LinkObjId="g_22f5320_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2618,-1306 2577,-1306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f5100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1257 2577,-1306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="25126@1" ObjectIDZND0="25127@x" ObjectIDZND1="38085@1" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="g_22f5320_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1257 2577,-1306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f5320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1306 2577,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="25127@x" ObjectIDND1="25126@x" ObjectIDZND0="38085@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="SW-140334_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1306 2577,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2244f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1306 2848,-1306 2848,-1264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="voltageTransformer" ObjectIDND0="25121@x" ObjectIDND1="33969@1" ObjectIDZND0="g_22f5540@0" Pin0InfoVect0LinkObjId="g_22f5540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140282_0" Pin1InfoVect1LinkObjId="g_2245850_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1306 2848,-1306 2848,-1264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2245850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1306 2913,-1358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="g_22f5540@0" ObjectIDND1="25121@x" ObjectIDZND0="33969@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22f5540_0" Pin1InfoVect1LinkObjId="SW-140282_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1306 2913,-1358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3008,-1164 2990,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2245a70@0" ObjectIDZND0="25122@0" Pin0InfoVect0LinkObjId="SW-140283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2245a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3008,-1164 2990,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224b950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2954,-1164 2913,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25122@1" ObjectIDZND0="25119@x" ObjectIDZND1="25120@x" Pin0InfoVect0LinkObjId="SW-140279_0" Pin0InfoVect1LinkObjId="SW-140281_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2954,-1164 2913,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224c350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1184 2913,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25119@0" ObjectIDZND0="25122@x" ObjectIDZND1="25120@x" Pin0InfoVect0LinkObjId="SW-140283_0" Pin0InfoVect1LinkObjId="SW-140281_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1184 2913,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1164 2913,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25122@x" ObjectIDND1="25119@x" ObjectIDZND0="25120@1" Pin0InfoVect0LinkObjId="SW-140281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140283_0" Pin1InfoVect1LinkObjId="SW-140279_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1164 2913,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2279890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-969 2274,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25156@0" ObjectIDZND0="25155@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-969 2274,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_222f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1024 2274,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25133@0" ObjectIDZND0="25156@1" Pin0InfoVect0LinkObjId="SW-140433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1024 2274,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_225fd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-805 2273,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25157@0" ObjectIDZND0="25134@1" Pin0InfoVect0LinkObjId="SW-140440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-805 2273,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225ff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1060 2274,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25133@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_22625f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1060 2274,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22601a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-861 2273,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25155@1" ObjectIDZND0="25157@1" Pin0InfoVect0LinkObjId="SW-140438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-861 2273,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22603c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-743 2273,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25134@0" ObjectIDZND0="25117@0" Pin0InfoVect0LinkObjId="g_22a3900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-743 2273,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c1fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-654 2022,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25145@0" ObjectIDZND0="25144@1" Pin0InfoVect0LinkObjId="SW-140645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-654 2022,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c2c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2072,-476 2022,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_22c21c0@0" ObjectIDZND0="25146@x" ObjectIDZND1="33963@x" ObjectIDZND2="28867@x" Pin0InfoVect0LinkObjId="SW-140648_0" Pin0InfoVect1LinkObjId="EC-YR_TPL.YR_TPL_083Ld_0" Pin0InfoVect2LinkObjId="SW-190235_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c21c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2072,-476 2022,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2232a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-524 2022,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="25146@0" ObjectIDZND0="g_22c21c0@0" ObjectIDZND1="33963@x" ObjectIDZND2="28867@x" Pin0InfoVect0LinkObjId="g_22c21c0_0" Pin0InfoVect1LinkObjId="EC-YR_TPL.YR_TPL_083Ld_0" Pin0InfoVect2LinkObjId="SW-190235_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-524 2022,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2232cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-476 2022,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_22c21c0@0" ObjectIDND1="25146@x" ObjectIDND2="28867@x" ObjectIDZND0="33963@0" Pin0InfoVect0LinkObjId="EC-YR_TPL.YR_TPL_083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22c21c0_0" Pin1InfoVect1LinkObjId="SW-140648_0" Pin1InfoVect2LinkObjId="SW-190235_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-476 2022,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2162900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-691 2230,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25142@1" ObjectIDZND0="25117@0" Pin0InfoVect0LinkObjId="g_22a3900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-691 2230,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2162b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-595 2230,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25141@0" ObjectIDZND0="25143@1" Pin0InfoVect0LinkObjId="SW-140602_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-595 2230,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2163460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-655 2230,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25142@0" ObjectIDZND0="25141@1" Pin0InfoVect0LinkObjId="SW-140599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-655 2230,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2164330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2280,-477 2230,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_21636c0@0" ObjectIDZND0="25143@x" ObjectIDZND1="33962@x" ObjectIDZND2="28868@x" Pin0InfoVect0LinkObjId="SW-140602_0" Pin0InfoVect1LinkObjId="EC-YR_TPL.YR_TPL_082Ld_0" Pin0InfoVect2LinkObjId="SW-190232_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21636c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2280,-477 2230,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2164590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-525 2230,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="25143@0" ObjectIDZND0="g_21636c0@0" ObjectIDZND1="33962@x" ObjectIDZND2="28868@x" Pin0InfoVect0LinkObjId="g_21636c0_0" Pin0InfoVect1LinkObjId="EC-YR_TPL.YR_TPL_082Ld_0" Pin0InfoVect2LinkObjId="SW-190232_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-525 2230,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21647f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2230,-477 2230,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="25143@x" ObjectIDND1="g_21636c0@0" ObjectIDND2="28868@x" ObjectIDZND0="33962@0" Pin0InfoVect0LinkObjId="EC-YR_TPL.YR_TPL_082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140602_0" Pin1InfoVect1LinkObjId="g_21636c0_0" Pin1InfoVect2LinkObjId="SW-190232_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-477 2230,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2289720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2449,-690 2449,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25139@1" ObjectIDZND0="25117@0" Pin0InfoVect0LinkObjId="g_22a3900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2449,-690 2449,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2289980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2449,-594 2449,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25138@0" ObjectIDZND0="25140@1" Pin0InfoVect0LinkObjId="SW-140556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2449,-594 2449,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_225a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2449,-654 2449,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25139@0" ObjectIDZND0="25138@1" Pin0InfoVect0LinkObjId="SW-140553_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2449,-654 2449,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_225b2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2502,-478 2449,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_225a5b0@0" ObjectIDZND0="33961@x" ObjectIDZND1="28869@x" ObjectIDZND2="25140@x" Pin0InfoVect0LinkObjId="EC-YR_TPL.YR_TPL_081Ld_0" Pin0InfoVect1LinkObjId="SW-190199_0" Pin0InfoVect2LinkObjId="SW-140556_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_225a5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2502,-478 2449,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_225b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2449,-478 2449,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="28869@x" ObjectIDND1="25140@x" ObjectIDND2="g_225a5b0@0" ObjectIDZND0="33961@0" Pin0InfoVect0LinkObjId="EC-YR_TPL.YR_TPL_081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-190199_0" Pin1InfoVect1LinkObjId="SW-140556_0" Pin1InfoVect2LinkObjId="g_225a5b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2449,-478 2449,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21aab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3283,-690 3283,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41928@1" ObjectIDZND0="41932@0" Pin0InfoVect0LinkObjId="g_2271f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3283,-690 3283,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21aacf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3283,-654 3283,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41928@0" ObjectIDZND0="41923@1" Pin0InfoVect0LinkObjId="SW-251161_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3283,-654 3283,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2271f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-688 2952,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41926@1" ObjectIDZND0="41932@0" Pin0InfoVect0LinkObjId="g_21aab00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251165_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-688 2952,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2266a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-771 3244,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41924@0" ObjectIDZND0="41932@0" Pin0InfoVect0LinkObjId="g_21aab00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251167_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-771 3244,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21d4ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1058 2577,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25135@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_22625f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1058 2577,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2237c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2672,-997 2654,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21d5d50@0" ObjectIDZND0="25136@0" Pin0InfoVect0LinkObjId="SW-140548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d5d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2672,-997 2654,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2237e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2618,-997 2577,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25136@1" ObjectIDZND0="25135@x" ObjectIDZND1="g_2274b40@0" Pin0InfoVect0LinkObjId="SW-140547_0" Pin0InfoVect1LinkObjId="g_2274b40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140548_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2618,-997 2577,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2238970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1022 2577,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25135@0" ObjectIDZND0="25136@x" ObjectIDZND1="g_2274b40@0" Pin0InfoVect0LinkObjId="SW-140548_0" Pin0InfoVect1LinkObjId="g_2274b40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140547_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1022 2577,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2238bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-997 2577,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25136@x" ObjectIDND1="25135@x" ObjectIDZND0="g_2274b40@0" Pin0InfoVect0LinkObjId="g_2274b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140548_0" Pin1InfoVect1LinkObjId="SW-140547_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-997 2577,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2238e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1306 2913,-1284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="g_22f5540@0" ObjectIDND1="33969@1" ObjectIDZND0="25121@1" Pin0InfoVect0LinkObjId="SW-140282_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22f5540_0" Pin1InfoVect1LinkObjId="g_2245850_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1306 2913,-1284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3008,-1230 2990,-1230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2239090@0" ObjectIDZND0="25123@0" Pin0InfoVect0LinkObjId="SW-140284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2239090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3008,-1230 2990,-1230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223e870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2954,-1230 2913,-1230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25123@1" ObjectIDZND0="25119@x" ObjectIDZND1="25121@x" Pin0InfoVect0LinkObjId="SW-140279_0" Pin0InfoVect1LinkObjId="SW-140282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2954,-1230 2913,-1230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223f360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1211 2913,-1230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25119@1" ObjectIDZND0="25123@x" ObjectIDZND1="25121@x" Pin0InfoVect0LinkObjId="SW-140284_0" Pin0InfoVect1LinkObjId="SW-140282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1211 2913,-1230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1230 2913,-1248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25123@x" ObjectIDND1="25119@x" ObjectIDZND0="25121@0" Pin0InfoVect0LinkObjId="SW-140282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140284_0" Pin1InfoVect1LinkObjId="SW-140279_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1230 2913,-1248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223f820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-862 2912,-833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28881@0" ObjectIDZND0="28877@1" Pin0InfoVect0LinkObjId="SW-190319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229b2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-862 2912,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a1f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2143,-1254 2143,-1280 2274,-1280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="25132@1" ObjectIDZND0="25131@x" ObjectIDZND1="37859@1" ObjectIDZND2="25130@x" Pin0InfoVect0LinkObjId="SW-140386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-140385_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2143,-1254 2143,-1280 2274,-1280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a28c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1306 2274,-1280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25131@x" ObjectIDND1="37859@1" ObjectIDZND0="25132@x" ObjectIDZND1="25130@x" Pin0InfoVect0LinkObjId="SW-140387_0" Pin0InfoVect1LinkObjId="SW-140385_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1306 2274,-1280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a2ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1280 2274,-1257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="25132@x" ObjectIDND1="25131@x" ObjectIDND2="37859@1" ObjectIDZND0="25130@1" Pin0InfoVect0LinkObjId="SW-140385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140387_0" Pin1InfoVect1LinkObjId="SW-140386_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1280 2274,-1257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2256120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-692 1908,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28871@1" ObjectIDZND0="25117@0" Pin0InfoVect0LinkObjId="g_22a3900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-692 1908,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21668b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1861,-600 1861,-620 1908,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="28872@0" ObjectIDZND0="0@x" ObjectIDZND1="28871@x" ObjectIDZND2="g_2167a80@0" Pin0InfoVect0LinkObjId="g_22f5540_0" Pin0InfoVect1LinkObjId="SW-190246_0" Pin0InfoVect2LinkObjId="g_2167a80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-600 1861,-620 1908,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21675c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-542 1908,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="28872@x" ObjectIDZND1="28871@x" ObjectIDZND2="g_2167a80@0" Pin0InfoVect0LinkObjId="SW-190247_0" Pin0InfoVect1LinkObjId="SW-190246_0" Pin0InfoVect2LinkObjId="g_2167a80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-542 1908,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2167820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-620 1908,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28872@x" ObjectIDND1="0@x" ObjectIDND2="g_2167a80@0" ObjectIDZND0="28871@0" Pin0InfoVect0LinkObjId="SW-190246_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-190247_0" Pin1InfoVect1LinkObjId="g_22f5540_0" Pin1InfoVect2LinkObjId="g_2167a80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-620 1908,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21687f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-620 1946,-620 1945,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28872@x" ObjectIDND1="0@x" ObjectIDND2="28871@x" ObjectIDZND0="g_2167a80@0" Pin0InfoVect0LinkObjId="g_2167a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-190247_0" Pin1InfoVect1LinkObjId="g_22f5540_0" Pin1InfoVect2LinkObjId="SW-190246_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-620 1946,-620 1945,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_216bca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1974,-456 1974,-476 2021,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="28867@0" ObjectIDZND0="g_22c21c0@0" ObjectIDZND1="25146@x" ObjectIDZND2="33963@x" Pin0InfoVect0LinkObjId="g_22c21c0_0" Pin0InfoVect1LinkObjId="SW-140648_0" Pin0InfoVect2LinkObjId="EC-YR_TPL.YR_TPL_083Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1974,-456 1974,-476 2021,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2227d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2182,-457 2182,-477 2229,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="28868@0" ObjectIDZND0="25143@x" ObjectIDZND1="g_21636c0@0" ObjectIDZND2="33962@x" Pin0InfoVect0LinkObjId="SW-140602_0" Pin0InfoVect1LinkObjId="g_21636c0_0" Pin0InfoVect2LinkObjId="EC-YR_TPL.YR_TPL_082Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2182,-457 2182,-477 2229,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222b2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-458 2401,-478 2448,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="28869@0" ObjectIDZND0="33961@x" ObjectIDZND1="25140@x" ObjectIDZND2="g_225a5b0@0" Pin0InfoVect0LinkObjId="EC-YR_TPL.YR_TPL_081Ld_0" Pin0InfoVect1LinkObjId="SW-140556_0" Pin0InfoVect2LinkObjId="g_225a5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190199_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-458 2401,-478 2448,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2449,-524 2449,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25140@0" ObjectIDZND0="33961@x" ObjectIDZND1="28869@x" ObjectIDZND2="g_225a5b0@0" Pin0InfoVect0LinkObjId="EC-YR_TPL.YR_TPL_081Ld_0" Pin0InfoVect1LinkObjId="SW-190199_0" Pin0InfoVect2LinkObjId="g_225a5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2449,-524 2449,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b4210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2648,-685 2648,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28882@1" ObjectIDZND0="41932@0" Pin0InfoVect0LinkObjId="g_21aab00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2648,-685 2648,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2691,-576 2691,-590 2648,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_21b4470@0" ObjectIDZND0="0@x" ObjectIDZND1="28883@x" ObjectIDZND2="28882@x" Pin0InfoVect0LinkObjId="g_22f5540_0" Pin0InfoVect1LinkObjId="SW-190397_0" Pin0InfoVect2LinkObjId="SW-190396_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b4470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2691,-576 2691,-590 2648,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b5c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2648,-553 2648,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_21b4470@0" ObjectIDZND1="28883@x" ObjectIDZND2="28882@x" Pin0InfoVect0LinkObjId="g_21b4470_0" Pin0InfoVect1LinkObjId="SW-190397_0" Pin0InfoVect2LinkObjId="SW-190396_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2648,-553 2648,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b9140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2598,-593 2598,-616 2648,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="28883@0" ObjectIDZND0="g_21b4470@0" ObjectIDZND1="0@x" ObjectIDZND2="28882@x" Pin0InfoVect0LinkObjId="g_21b4470_0" Pin0InfoVect1LinkObjId="g_22f5540_0" Pin0InfoVect2LinkObjId="SW-190396_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2598,-593 2598,-616 2648,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b9c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2648,-590 2648,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_21b4470@0" ObjectIDND1="0@x" ObjectIDZND0="28883@x" ObjectIDZND1="28882@x" Pin0InfoVect0LinkObjId="SW-190397_0" Pin0InfoVect1LinkObjId="SW-190396_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21b4470_0" Pin1InfoVect1LinkObjId="g_22f5540_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2648,-590 2648,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b9e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2648,-616 2648,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="28883@x" ObjectIDND1="g_21b4470@0" ObjectIDND2="0@x" ObjectIDZND0="28882@0" Pin0InfoVect0LinkObjId="SW-190396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-190397_0" Pin1InfoVect1LinkObjId="g_21b4470_0" Pin1InfoVect2LinkObjId="g_22f5540_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2648,-616 2648,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21bc8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-744 2912,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28879@0" ObjectIDZND0="41932@0" Pin0InfoVect0LinkObjId="g_21aab00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-744 2912,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e4aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-806 2912,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28877@0" ObjectIDZND0="28879@1" Pin0InfoVect0LinkObjId="SW-190321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-806 2912,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c49e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2065,-736 2065,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25137@0" ObjectIDZND0="25117@0" Pin0InfoVect0LinkObjId="g_22a3900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140549_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2065,-736 2065,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c4c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2021,-798 2065,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="28873@0" ObjectIDZND0="25137@x" ObjectIDZND1="g_21f0b10@0" Pin0InfoVect0LinkObjId="SW-140549_0" Pin0InfoVect1LinkObjId="g_21f0b10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-190248_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2021,-798 2065,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c5730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2065,-772 2065,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25137@1" ObjectIDZND0="28873@x" ObjectIDZND1="g_21f0b10@0" Pin0InfoVect0LinkObjId="SW-190248_0" Pin0InfoVect1LinkObjId="g_21f0b10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140549_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2065,-772 2065,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c5990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2065,-798 2065,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="28873@x" ObjectIDND1="25137@x" ObjectIDZND0="g_21f0b10@0" Pin0InfoVect0LinkObjId="g_21f0b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-190248_0" Pin1InfoVect1LinkObjId="SW-140549_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2065,-798 2065,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211bf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-501 2952,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="41927@0" Pin0InfoVect0LinkObjId="SW-251166_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f5540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-501 2952,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3283,-594 3283,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41923@0" ObjectIDZND0="41929@1" Pin0InfoVect0LinkObjId="SW-251164_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251161_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3283,-594 3283,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211f3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3283,-534 3283,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" ObjectIDND0="41929@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_22f5540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251164_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3283,-534 3283,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2122390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3273,-829 3244,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="41925@0" ObjectIDZND0="41924@x" ObjectIDZND1="g_21ce7e0@0" Pin0InfoVect0LinkObjId="SW-251167_0" Pin0InfoVect1LinkObjId="g_21ce7e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3273,-829 3244,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2122e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-807 3244,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="41924@1" ObjectIDZND0="41925@x" ObjectIDZND1="g_21ce7e0@0" Pin0InfoVect0LinkObjId="SW-251168_0" Pin0InfoVect1LinkObjId="g_21ce7e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251167_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-807 3244,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21230e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-829 3244,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="41925@x" ObjectIDND1="41924@x" ObjectIDZND0="g_21ce7e0@0" Pin0InfoVect0LinkObjId="g_21ce7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-251168_0" Pin1InfoVect1LinkObjId="SW-251167_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-829 3244,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2123340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3324,-829 3309,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211f5a0@0" ObjectIDZND0="41925@1" Pin0InfoVect0LinkObjId="SW-251168_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211f5a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3324,-829 3309,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2123a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-567 2952,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41927@1" ObjectIDZND0="41922@0" Pin0InfoVect0LinkObjId="SW-251162_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251166_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-567 2952,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2123c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-615 2952,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41922@1" ObjectIDZND0="41926@0" Pin0InfoVect0LinkObjId="SW-251165_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-251162_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-615 2952,-652 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25117" cx="2022" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25117" cx="2273" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="2577" cy="-1088" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="2274" cy="-1088" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="2913" cy="-1088" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="2913" cy="-1088" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="2274" cy="-1088" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25117" cx="2065" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25117" cx="2230" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25117" cx="2449" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41932" cx="2952" cy="-719" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41932" cx="3244" cy="-719" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25116" cx="2577" cy="-1088" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25117" cx="1908" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41932" cx="2912" cy="-719" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41932" cx="2648" cy="-719" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41932" cx="3283" cy="-719" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130475" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1682.500000 -1315.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23915" ObjectName="DYN-YR_TPL"/>
     <cge:Meas_Ref ObjectId="130475"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_230c780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2028.000000 -941.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f9d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.500000 -1409.000000) translate(0,15)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f9d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.500000 -1409.000000) translate(0,33)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f9d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.500000 -1409.000000) translate(0,51)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f9d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.500000 -1409.000000) translate(0,69)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f9d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.500000 -1409.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2224e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -370.000000) translate(0,15)">万厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_229b6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -740.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_229baa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3304.000000 -740.000000) translate(0,15)">6.3kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_228fde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3126.000000 -1110.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d98d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2550.000000 -1393.000000) translate(0,15)">猛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d98d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2550.000000 -1393.000000) translate(0,33)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d98d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2550.000000 -1393.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22d1830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2889.500000 -1395.000000) translate(0,15)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22d1830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2889.500000 -1395.000000) translate(0,33)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22d1830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2889.500000 -1395.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22a35d0" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2115.326733 -1164.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -813.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -1251.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(21,40,56)" font-family="SimHei" font-size="20" graphid="g_22c5d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1447.000000 -1392.500000) translate(0,16)">他普里变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_224c790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2821.000000 -1243.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22c1c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1817.500000 -486.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2232f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2202.000000 -371.000000) translate(0,15)">岔河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2165970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2414.000000 -370.000000) translate(0,15)">白西地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_225c680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2594.500000 -446.000000) translate(0,15)">6kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21aa400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3299.500000 -615.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21aa8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3295.500000 -679.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22715f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2968.500000 -613.000000) translate(0,12)">602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2271ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2964.500000 -677.000000) translate(0,12)">6021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2274190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3240.500000 -444.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2274760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2910.500000 -447.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2266230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3207.000000 -943.000000) translate(0,15)">6.3kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2266730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3253.000000 -794.000000) translate(0,12)">6903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d5720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2538.000000 -885.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -929.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a30e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -1246.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2284.000000 -1191.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a35a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -1137.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a37e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2312.000000 -1332.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a3b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2150.000000 -1243.000000) translate(0,12)">3839</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a3f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2587.000000 -1191.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a41b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1136.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a43f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1246.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a4630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2615.000000 -1332.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2923.000000 -1205.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a4ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2920.000000 -1137.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a4cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2952.000000 -1190.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a4f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2952.000000 -1256.000000) translate(0,12)">38160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a5170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2920.000000 -1273.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a53b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2284.000000 -990.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a55f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -1049.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a5830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2283.000000 -826.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a5a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1047.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a5cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2616.000000 -1023.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a5ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2072.000000 -761.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f62d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -615.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f6900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -549.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f6b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -679.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f6d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2239.000000 -616.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f6fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2237.000000 -680.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f7200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2237.000000 -550.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f7440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -615.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f7680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2029.000000 -549.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f78c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2029.000000 -679.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f7b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1915.000000 -681.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f8020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1805.000000 -592.000000) translate(0,12)">08417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f82a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -440.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f84e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2127.000000 -446.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f8720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2352.000000 -448.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f8960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1975.000000 -825.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f8ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2280.000000 -768.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21cdaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1404.000000 -1067.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bd120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2923.000000 -991.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2920.000000 -1050.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e2940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2655.000000 -674.000000) translate(0,12)">6041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e2b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2544.000000 -588.000000) translate(0,12)">60417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e4d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2921.000000 -827.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e51f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2919.000000 -769.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e5430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2817.000000 -929.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_21e5670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1399.000000 -777.000000) translate(0,15)">2号主变档位未上传</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_21e5670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1399.000000 -777.000000) translate(0,33)">1号主变“四遥”信号未核对</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_21ea0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1615.000000 -1376.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_21ec320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1615.000000 -1411.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_21bead0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1394.500000 -720.000000) translate(0,15)">1号主变高压侧301断路器未做过遥控试验</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_21bead0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1394.500000 -720.000000) translate(0,33)">1号主变低压侧001断路器未做过遥控试验</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2150.000000 -909.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2150.000000 -909.000000) translate(0,27)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c2af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2795.000000 -909.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c2af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2795.000000 -909.000000) translate(0,27)">S11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2964.500000 -556.000000) translate(0,12)">6026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211ecd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3295.500000 -559.000000) translate(0,12)">6016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21235a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -858.000000) translate(0,12)">69017</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-140549">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2057.000000 -731.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25137" ObjectName="SW-YR_TPL.YR_TPL_0901SW"/>
     <cge:Meas_Ref ObjectId="140549"/>
    <cge:TPSR_Ref TObjectID="25137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2266.000000 -1216.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25130" ObjectName="SW-YR_TPL.YR_TPL_3836SW"/>
     <cge:Meas_Ref ObjectId="140385"/>
    <cge:TPSR_Ref TObjectID="25130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25145" ObjectName="SW-YR_TPL.YR_TPL_0831SW"/>
     <cge:Meas_Ref ObjectId="140647"/>
    <cge:TPSR_Ref TObjectID="25145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -519.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25146" ObjectName="SW-YR_TPL.YR_TPL_0836SW"/>
     <cge:Meas_Ref ObjectId="140648"/>
    <cge:TPSR_Ref TObjectID="25146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190326">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2905.000000 -1020.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28878" ObjectName="SW-YR_TPL.YR_TPL_3011SW"/>
     <cge:Meas_Ref ObjectId="190326"/>
    <cge:TPSR_Ref TObjectID="28878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.500000 -1107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25129" ObjectName="SW-YR_TPL.YR_TPL_3831SW"/>
     <cge:Meas_Ref ObjectId="140384"/>
    <cge:TPSR_Ref TObjectID="25129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2569.000000 -1216.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25126" ObjectName="SW-YR_TPL.YR_TPL_3826SW"/>
     <cge:Meas_Ref ObjectId="140334"/>
    <cge:TPSR_Ref TObjectID="25126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2568.000000 -1106.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25125" ObjectName="SW-YR_TPL.YR_TPL_3821SW"/>
     <cge:Meas_Ref ObjectId="140333"/>
    <cge:TPSR_Ref TObjectID="25125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2905.000000 -1243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25121" ObjectName="SW-YR_TPL.YR_TPL_3816SW"/>
     <cge:Meas_Ref ObjectId="140282"/>
    <cge:TPSR_Ref TObjectID="25121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.500000 -1107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25120" ObjectName="SW-YR_TPL.YR_TPL_3811SW"/>
     <cge:Meas_Ref ObjectId="140281"/>
    <cge:TPSR_Ref TObjectID="25120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2342.000000 -1280.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25131" ObjectName="SW-YR_TPL.YR_TPL_38367SW"/>
     <cge:Meas_Ref ObjectId="140386"/>
    <cge:TPSR_Ref TObjectID="25131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140387">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2135.000000 -1213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25132" ObjectName="SW-YR_TPL.YR_TPL_3839SW"/>
     <cge:Meas_Ref ObjectId="140387"/>
    <cge:TPSR_Ref TObjectID="25132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2645.000000 -1280.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25127" ObjectName="SW-YR_TPL.YR_TPL_38267SW"/>
     <cge:Meas_Ref ObjectId="140335"/>
    <cge:TPSR_Ref TObjectID="25127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2981.000000 -1138.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25122" ObjectName="SW-YR_TPL.YR_TPL_38117SW"/>
     <cge:Meas_Ref ObjectId="140283"/>
    <cge:TPSR_Ref TObjectID="25122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2266.000000 -1019.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25133" ObjectName="SW-YR_TPL.YR_TPL_3021SW"/>
     <cge:Meas_Ref ObjectId="140435"/>
    <cge:TPSR_Ref TObjectID="25133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140440">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2265.000000 -738.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25134" ObjectName="SW-YR_TPL.YR_TPL_0021SW"/>
     <cge:Meas_Ref ObjectId="140440"/>
    <cge:TPSR_Ref TObjectID="25134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140601">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -650.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25142" ObjectName="SW-YR_TPL.YR_TPL_0821SW"/>
     <cge:Meas_Ref ObjectId="140601"/>
    <cge:TPSR_Ref TObjectID="25142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140602">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -520.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25143" ObjectName="SW-YR_TPL.YR_TPL_0826SW"/>
     <cge:Meas_Ref ObjectId="140602"/>
    <cge:TPSR_Ref TObjectID="25143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140555">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2440.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25139" ObjectName="SW-YR_TPL.YR_TPL_0811SW"/>
     <cge:Meas_Ref ObjectId="140555"/>
    <cge:TPSR_Ref TObjectID="25139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140556">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2440.000000 -519.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25140" ObjectName="SW-YR_TPL.YR_TPL_0816SW"/>
     <cge:Meas_Ref ObjectId="140556"/>
    <cge:TPSR_Ref TObjectID="25140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3274.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41928" ObjectName="SW-YR_TPL.YR_TPL_6011SW"/>
     <cge:Meas_Ref ObjectId="251163"/>
    <cge:TPSR_Ref TObjectID="41928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2943.000000 -647.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41926" ObjectName="SW-YR_TPL.YR_TPL_6021SW"/>
     <cge:Meas_Ref ObjectId="251165"/>
    <cge:TPSR_Ref TObjectID="41926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251167">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3236.000000 -766.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41924" ObjectName="SW-YR_TPL.YR_TPL_6903SW"/>
     <cge:Meas_Ref ObjectId="251167"/>
    <cge:TPSR_Ref TObjectID="41924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140547">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2569.000000 -1017.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25135" ObjectName="SW-YR_TPL.YR_TPL_3901SW"/>
     <cge:Meas_Ref ObjectId="140547"/>
    <cge:TPSR_Ref TObjectID="25135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140548">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2645.000000 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25136" ObjectName="SW-YR_TPL.YR_TPL_39017SW"/>
     <cge:Meas_Ref ObjectId="140548"/>
    <cge:TPSR_Ref TObjectID="25136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2981.000000 -1204.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25123" ObjectName="SW-YR_TPL.YR_TPL_38160SW"/>
     <cge:Meas_Ref ObjectId="140284"/>
    <cge:TPSR_Ref TObjectID="25123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190246">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28871" ObjectName="SW-YR_TPL.YR_TPL_0841SW"/>
     <cge:Meas_Ref ObjectId="190246"/>
    <cge:TPSR_Ref TObjectID="28871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1852.000000 -549.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28872" ObjectName="SW-YR_TPL.YR_TPL_08417SW"/>
     <cge:Meas_Ref ObjectId="190247"/>
    <cge:TPSR_Ref TObjectID="28872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190235">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1965.000000 -405.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28867" ObjectName="SW-YR_TPL.YR_TPL_08367SW"/>
     <cge:Meas_Ref ObjectId="190235"/>
    <cge:TPSR_Ref TObjectID="28867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190232">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2173.000000 -406.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28868" ObjectName="SW-YR_TPL.YR_TPL_08267SW"/>
     <cge:Meas_Ref ObjectId="190232"/>
    <cge:TPSR_Ref TObjectID="28868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190199">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2392.000000 -407.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28869" ObjectName="SW-YR_TPL.YR_TPL_08167SW"/>
     <cge:Meas_Ref ObjectId="190199"/>
    <cge:TPSR_Ref TObjectID="28869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190248">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2012.000000 -793.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28873" ObjectName="SW-YR_TPL.YR_TPL_09017SW"/>
     <cge:Meas_Ref ObjectId="190248"/>
    <cge:TPSR_Ref TObjectID="28873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2639.000000 -644.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28882" ObjectName="SW-YR_TPL.YR_TPL_6041SW"/>
     <cge:Meas_Ref ObjectId="190396"/>
    <cge:TPSR_Ref TObjectID="28882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2589.000000 -542.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28883" ObjectName="SW-YR_TPL.YR_TPL_60417SW"/>
     <cge:Meas_Ref ObjectId="190397"/>
    <cge:TPSR_Ref TObjectID="28883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-190321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2904.000000 -739.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28879" ObjectName="SW-YR_TPL.YR_TPL_0011SW"/>
     <cge:Meas_Ref ObjectId="190321"/>
    <cge:TPSR_Ref TObjectID="28879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251166">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2943.000000 -526.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41927" ObjectName="SW-YR_TPL.YR_TPL_6026SW"/>
     <cge:Meas_Ref ObjectId="251166"/>
    <cge:TPSR_Ref TObjectID="41927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3274.000000 -529.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41929" ObjectName="SW-YR_TPL.YR_TPL_6016SW"/>
     <cge:Meas_Ref ObjectId="251164"/>
    <cge:TPSR_Ref TObjectID="41929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-251168">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3268.000000 -824.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41925" ObjectName="SW-YR_TPL.YR_TPL_69017SW"/>
     <cge:Meas_Ref ObjectId="251168"/>
    <cge:TPSR_Ref TObjectID="41925"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_TPL.YR_TPL_083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -387.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33963" ObjectName="EC-YR_TPL.YR_TPL_083Ld"/>
    <cge:TPSR_Ref TObjectID="33963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_TPL.YR_TPL_082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2221.000000 -388.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33962" ObjectName="EC-YR_TPL.YR_TPL_082Ld"/>
    <cge:TPSR_Ref TObjectID="33962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_TPL.YR_TPL_081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2440.000000 -387.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33961" ObjectName="EC-YR_TPL.YR_TPL_081Ld"/>
    <cge:TPSR_Ref TObjectID="33961"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22a8350">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2129.000000 -1167.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22c21c0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2078.961111 -422.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21636c0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2286.961111 -423.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225a5b0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2508.961111 -422.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2274b40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2555.000000 -891.000000)" xlink:href="#lightningRod:shape134"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f0b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2043.000000 -923.000000)" xlink:href="#lightningRod:shape134"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2167a80">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 1951.961111 -556.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ce7e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3222.000000 -925.000000)" xlink:href="#lightningRod:shape134"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b4470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2684.000000 -522.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140219" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2267.000000 -1463.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140219" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25128"/>
     <cge:Term_Ref ObjectID="35417"/>
    <cge:TPSR_Ref TObjectID="25128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140220" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2267.000000 -1463.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25128"/>
     <cge:Term_Ref ObjectID="35417"/>
    <cge:TPSR_Ref TObjectID="25128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2267.000000 -1463.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25128"/>
     <cge:Term_Ref ObjectID="35417"/>
    <cge:TPSR_Ref TObjectID="25128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2570.000000 -1463.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25124"/>
     <cge:Term_Ref ObjectID="35409"/>
    <cge:TPSR_Ref TObjectID="25124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2570.000000 -1463.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25124"/>
     <cge:Term_Ref ObjectID="35409"/>
    <cge:TPSR_Ref TObjectID="25124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2570.000000 -1463.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25124"/>
     <cge:Term_Ref ObjectID="35409"/>
    <cge:TPSR_Ref TObjectID="25124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2917.000000 -1460.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25119"/>
     <cge:Term_Ref ObjectID="35399"/>
    <cge:TPSR_Ref TObjectID="25119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140207" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2917.000000 -1460.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140207" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25119"/>
     <cge:Term_Ref ObjectID="35399"/>
    <cge:TPSR_Ref TObjectID="25119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2917.000000 -1460.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25119"/>
     <cge:Term_Ref ObjectID="35399"/>
    <cge:TPSR_Ref TObjectID="25119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140226" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -1004.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140226" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25156"/>
     <cge:Term_Ref ObjectID="35479"/>
    <cge:TPSR_Ref TObjectID="25156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -1004.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25156"/>
     <cge:Term_Ref ObjectID="35479"/>
    <cge:TPSR_Ref TObjectID="25156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -1004.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25156"/>
     <cge:Term_Ref ObjectID="35479"/>
    <cge:TPSR_Ref TObjectID="25156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -838.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25157"/>
     <cge:Term_Ref ObjectID="35481"/>
    <cge:TPSR_Ref TObjectID="25157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -838.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25157"/>
     <cge:Term_Ref ObjectID="35481"/>
    <cge:TPSR_Ref TObjectID="25157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2384.000000 -838.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25157"/>
     <cge:Term_Ref ObjectID="35481"/>
    <cge:TPSR_Ref TObjectID="25157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -341.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25144"/>
     <cge:Term_Ref ObjectID="35449"/>
    <cge:TPSR_Ref TObjectID="25144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -341.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25144"/>
     <cge:Term_Ref ObjectID="35449"/>
    <cge:TPSR_Ref TObjectID="25144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -341.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25144"/>
     <cge:Term_Ref ObjectID="35449"/>
    <cge:TPSR_Ref TObjectID="25144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2222.000000 -341.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25141"/>
     <cge:Term_Ref ObjectID="35443"/>
    <cge:TPSR_Ref TObjectID="25141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2222.000000 -341.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25141"/>
     <cge:Term_Ref ObjectID="35443"/>
    <cge:TPSR_Ref TObjectID="25141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2222.000000 -341.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25141"/>
     <cge:Term_Ref ObjectID="35443"/>
    <cge:TPSR_Ref TObjectID="25141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2438.000000 -341.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25138"/>
     <cge:Term_Ref ObjectID="35437"/>
    <cge:TPSR_Ref TObjectID="25138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2438.000000 -341.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25138"/>
     <cge:Term_Ref ObjectID="35437"/>
    <cge:TPSR_Ref TObjectID="25138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2438.000000 -341.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25138"/>
     <cge:Term_Ref ObjectID="35437"/>
    <cge:TPSR_Ref TObjectID="25138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-140230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2432.000000 -915.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25155"/>
     <cge:Term_Ref ObjectID="35478"/>
    <cge:TPSR_Ref TObjectID="25155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2432.000000 -915.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25155"/>
     <cge:Term_Ref ObjectID="35478"/>
    <cge:TPSR_Ref TObjectID="25155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-140239" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 -845.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25117"/>
     <cge:Term_Ref ObjectID="35396"/>
    <cge:TPSR_Ref TObjectID="25117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-140240" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 -845.000000) translate(0,27)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25117"/>
     <cge:Term_Ref ObjectID="35396"/>
    <cge:TPSR_Ref TObjectID="25117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-140241" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 -845.000000) translate(0,42)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25117"/>
     <cge:Term_Ref ObjectID="35396"/>
    <cge:TPSR_Ref TObjectID="25117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-140245" prefix="3Uo " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 -845.000000) translate(0,57)">3Uo  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25117"/>
     <cge:Term_Ref ObjectID="35396"/>
    <cge:TPSR_Ref TObjectID="25117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-140242" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 -845.000000) translate(0,72)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25117"/>
     <cge:Term_Ref ObjectID="35396"/>
    <cge:TPSR_Ref TObjectID="25117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-140246" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 -845.000000) translate(0,87)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25117"/>
     <cge:Term_Ref ObjectID="35396"/>
    <cge:TPSR_Ref TObjectID="25117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-140231" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1965.000000 -1183.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25116"/>
     <cge:Term_Ref ObjectID="35395"/>
    <cge:TPSR_Ref TObjectID="25116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-140232" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1965.000000 -1183.000000) translate(0,27)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25116"/>
     <cge:Term_Ref ObjectID="35395"/>
    <cge:TPSR_Ref TObjectID="25116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-140233" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1965.000000 -1183.000000) translate(0,42)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25116"/>
     <cge:Term_Ref ObjectID="35395"/>
    <cge:TPSR_Ref TObjectID="25116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-140237" prefix="3Uo " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1965.000000 -1183.000000) translate(0,57)">3Uo  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25116"/>
     <cge:Term_Ref ObjectID="35395"/>
    <cge:TPSR_Ref TObjectID="25116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-140234" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1965.000000 -1183.000000) translate(0,72)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25116"/>
     <cge:Term_Ref ObjectID="35395"/>
    <cge:TPSR_Ref TObjectID="25116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="1" id="ME-140238" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1965.000000 -1183.000000) translate(0,87)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25116"/>
     <cge:Term_Ref ObjectID="35395"/>
    <cge:TPSR_Ref TObjectID="25116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3019.000000 -1003.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28875"/>
     <cge:Term_Ref ObjectID="41143"/>
    <cge:TPSR_Ref TObjectID="28875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3019.000000 -1003.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28875"/>
     <cge:Term_Ref ObjectID="41143"/>
    <cge:TPSR_Ref TObjectID="28875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3019.000000 -1003.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28875"/>
     <cge:Term_Ref ObjectID="41143"/>
    <cge:TPSR_Ref TObjectID="28875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3023.000000 -845.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28877"/>
     <cge:Term_Ref ObjectID="41145"/>
    <cge:TPSR_Ref TObjectID="28877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3023.000000 -845.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28877"/>
     <cge:Term_Ref ObjectID="41145"/>
    <cge:TPSR_Ref TObjectID="28877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3023.000000 -845.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28877"/>
     <cge:Term_Ref ObjectID="41145"/>
    <cge:TPSR_Ref TObjectID="28877"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="1412" y="-1403"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1363" y="-1420"/></g>
   <g href="35kV他普里变10kV万他线083断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2031" y="-615"/></g>
   <g href="35kV他普里变10kV岔河线082断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2239" y="-616"/></g>
   <g href="35kV他普里变10kV白西地线081断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2458" y="-615"/></g>
   <g href="35kV他普里变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="2175" y="-929"/></g>
   <g href="35kV他普里变35kV万他马红线383断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2284" y="-1191"/></g>
   <g href="35kV他普里变35kV龙他猛线382断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2587" y="-1191"/></g>
   <g href="35kV他普里变35kV中他线381断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2923" y="-1205"/></g>
   <g href="35kV他普里变公共虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="61" x="1403" y="-1067"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1604" y="-1384"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1604" y="-1419"/></g>
   <g href="35kV他普里变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2817" y="-929"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2240960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2205.000000 1463.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21adaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2194.000000 1448.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ae950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2219.000000 1433.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21af3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1945.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21af6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1934.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21af8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1959.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21afc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2357.000000 900.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b0800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2357.000000 915.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b1260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2327.000000 1001.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b1560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2316.000000 986.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b17a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2341.000000 971.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b1bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2327.000000 836.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b1e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2316.000000 821.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a1d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2341.000000 806.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e8920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2966.000000 1004.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e8bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2955.000000 989.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e8df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2980.000000 974.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e9210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2968.000000 846.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e94d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2957.000000 831.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e9710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2982.000000 816.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ecfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 1125.800000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ed6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1911.000000 1110.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21edc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1903.000000 1157.400000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21edf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1903.000000 1173.200000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ee140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1903.000000 1189.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ee380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1900.000000 1141.600000) translate(0,12)">3U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ee6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1773.000000 786.800000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bd890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1789.000000 771.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bdad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 818.400000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bdd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 834.200000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bdf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 850.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21be190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 802.600000) translate(0,12)">3U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c7010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2507.000000 1461.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c7520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2496.000000 1446.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c7760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2521.000000 1431.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c7b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2858.000000 1458.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2847.000000 1443.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c8080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2872.000000 1428.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c84a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2159.000000 343.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c8760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2148.000000 328.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c89a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2173.000000 313.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c8dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2379.000000 341.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c9080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2368.000000 326.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c92c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2393.000000 311.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2284be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2365.000000 -1300.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2296960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2668.000000 -1300.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2245a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3004.000000 -1158.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d5d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2668.000000 -991.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2239090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3004.000000 -1224.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211f5a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3320.000000 -823.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="1412" y="-1403"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="1412" y="-1403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1363" y="-1420"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1363" y="-1420"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2031" y="-615"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2031" y="-615"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2239" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2239" y="-616"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2458" y="-615"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2458" y="-615"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="2175" y="-929"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="2175" y="-929"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2284" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2284" y="-1191"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2587" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2587" y="-1191"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2923" y="-1205"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2923" y="-1205"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="61" x="1403" y="-1067"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="61" x="1403" y="-1067"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1604" y="-1384"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1604" y="-1384"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1604" y="-1419"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1604" y="-1419"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2817" y="-929"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2817" y="-929"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_TPL.YR_TPL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2003,-1088 3167,-1088 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25116" ObjectName="BS-YR_TPL.YR_TPL_3IM"/>
    <cge:TPSR_Ref TObjectID="25116"/></metadata>
   <polyline fill="none" opacity="0" points="2003,-1088 3167,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_TPL.YR_TPL_6IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2600,-719 3348,-719 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41932" ObjectName="BS-YR_TPL.YR_TPL_6IM"/>
    <cge:TPSR_Ref TObjectID="41932"/></metadata>
   <polyline fill="none" opacity="0" points="2600,-719 3348,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_TPL.YR_TPL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1865,-720 2558,-720 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25117" ObjectName="BS-YR_TPL.YR_TPL_9IM"/>
    <cge:TPSR_Ref TObjectID="25117"/></metadata>
   <polyline fill="none" opacity="0" points="1865,-720 2558,-720 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1477.000000 -1344.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217884" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -1210.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217884" ObjectName="YR_TPL:YR_TPL_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217884" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -1171.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217884" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217884" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 -1249.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217884" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_TPL"/>
</svg>