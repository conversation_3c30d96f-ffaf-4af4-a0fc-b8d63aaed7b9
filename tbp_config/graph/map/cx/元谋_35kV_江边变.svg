<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-236" aopId="3936770" id="thSvg" product="E8000V2" version="1.0" viewBox="-741 -1372 2271 1247">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="65" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="52" y2="35"/>
    <circle cx="15" cy="90" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,22 9,35 21,35 15,22 15,23 15,22 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,65 48,65 48,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="54" x2="42" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="52" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="46" x2="50" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="65" y2="70"/>
    <circle cx="15" cy="68" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="20" x2="12" y1="91" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="20" x2="12" y1="91" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="12" x2="12" y1="86" y2="97"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,35 33,53 48,53 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="22" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="65" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape24_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape83_0">
    <circle cx="65" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="84,100 90,100 27,37 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="90,100 90,93 " stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="65" x2="7" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="74" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="21" y2="21"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="53" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="53" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="65" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="65" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="65" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="65" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape83_1">
    <circle cx="65" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="65,19 58,34 73,34 65,19 65,19 65,19 "/>
   </symbol>
   <symbol id="voltageTransformer:shape130">
    <ellipse cx="16" cy="8" fillStyle="0" rx="7" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="67" x2="23" y1="9" y2="9"/>
    <rect height="14" stroke-width="0.379884" width="24" x="30" y="2"/>
   </symbol>
   <symbol id="voltageTransformer:shape131">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="68" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="31" x2="31" y1="88" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="31" y1="85" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="31" x2="29" y1="88" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="80" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="24" x2="21" y1="77" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="19" y1="80" y2="78"/>
    <ellipse cx="21" cy="80" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="22" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="84" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="46" y1="77" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="77" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="31" x2="31" y1="74" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="31" y1="71" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="31" x2="29" y1="74" y2="72"/>
    <ellipse cx="42" cy="81" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="31" cy="89" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="32" cy="76" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="69" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="69" y2="69"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="22" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="22" y2="22"/>
    <rect height="27" stroke-width="0.416667" width="14" x="25" y="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape128">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="71" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="25" x2="22" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="28" x2="25" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="25" x2="25" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="39" x2="36" y1="78" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="42" x2="39" y1="75" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="39" x2="39" y1="78" y2="81"/>
    <rect height="27" stroke-width="0.416667" width="14" x="34" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="41" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="59"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="73" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="79" y2="79"/>
    <ellipse cx="25" cy="79" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="25" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="39" cy="79" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="27" y1="80" y2="76"/>
    <circle cx="39" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="40" x2="37" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="43" x2="40" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="40" x2="40" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="25" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="58" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="58" y1="107" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="55" y1="107" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="57" y1="111" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="56" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="58" y1="78" y2="84"/>
    <rect height="13" stroke-width="1" width="5" x="56" y="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="63" y1="95" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="98" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="63" x2="63" y1="86" y2="83"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="20" y1="30" y2="76"/>
    <rect height="27" stroke-width="0.416667" width="14" x="13" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="50" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="50" x2="50" y1="76" y2="41"/>
    <rect height="27" stroke-width="0.416667" width="14" x="43" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="56" x2="44" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="50" x2="50" y1="28" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="46" x2="54" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="49" x2="52" y1="22" y2="22"/>
    <circle cx="20" cy="21" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="19" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="30" cy="17" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="19" x2="17" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="22" x2="19" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="19" x2="19" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="34" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="35" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="76" y2="92"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="19" x2="17" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="22" x2="19" y1="13" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="19" x2="19" y1="10" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bb0ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c658b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c37250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c38280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1422e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1423990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1424150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1424af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1426170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1426170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf7280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf7280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf9010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf9010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1cfa030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf2f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cf3c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cf49b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cf5100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd8370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd8ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd5120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd58e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd69c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd7340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc1be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bc25d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bc31b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bc3c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bc4da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc59c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_206f6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c27b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ce9bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ce6240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1257" width="2281" x="-746" y="-1377"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-240" x2="-240" y1="-358" y2="-241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-187" x2="-240" y1="-241" y2="-241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-188" x2="-212" y1="-350" y2="-350"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-212" x2="-212" y1="-358" y2="-350"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1343" x2="1343" y1="-849" y2="-971"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1290" x2="1343" y1="-971" y2="-971"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1291" x2="1315" y1="-856" y2="-856"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1315" x2="1315" y1="-848" y2="-856"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1521" x2="1530" y1="-737" y2="-737"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-169477">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 73.000000 -1025.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27105" ObjectName="SW-YM_JB.YM_JB_3411SW"/>
     <cge:Meas_Ref ObjectId="169477"/>
    <cge:TPSR_Ref TObjectID="27105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225628">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.000000 -1075.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37488" ObjectName="SW-YM_JB.YM_JB_34117SW"/>
     <cge:Meas_Ref ObjectId="225628"/>
    <cge:TPSR_Ref TObjectID="37488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225629">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.000000 -1132.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37489" ObjectName="SW-YM_JB.YM_JB_34160SW"/>
     <cge:Meas_Ref ObjectId="225629"/>
    <cge:TPSR_Ref TObjectID="37489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169478">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 73.000000 -1146.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27106" ObjectName="SW-YM_JB.YM_JB_3416SW"/>
     <cge:Meas_Ref ObjectId="169478"/>
    <cge:TPSR_Ref TObjectID="27106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225630">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.000000 -1195.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37490" ObjectName="SW-YM_JB.YM_JB_34167SW"/>
     <cge:Meas_Ref ObjectId="225630"/>
    <cge:TPSR_Ref TObjectID="37490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 31.000000 -1256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37487" ObjectName="SW-YM_JB.YM_JB_3419SW"/>
     <cge:Meas_Ref ObjectId="225671"/>
    <cge:TPSR_Ref TObjectID="37487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225698">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -1025.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37492" ObjectName="SW-YM_JB.YM_JB_3421SW"/>
     <cge:Meas_Ref ObjectId="225698"/>
    <cge:TPSR_Ref TObjectID="37492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225699">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 247.000000 -1075.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37494" ObjectName="SW-YM_JB.YM_JB_34217SW"/>
     <cge:Meas_Ref ObjectId="225699"/>
    <cge:TPSR_Ref TObjectID="37494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -1132.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37495" ObjectName="SW-YM_JB.YM_JB_34260SW"/>
     <cge:Meas_Ref ObjectId="225701"/>
    <cge:TPSR_Ref TObjectID="37495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -1146.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37491" ObjectName="SW-YM_JB.YM_JB_3426SW"/>
     <cge:Meas_Ref ObjectId="225700"/>
    <cge:TPSR_Ref TObjectID="37491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225702">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -1195.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37496" ObjectName="SW-YM_JB.YM_JB_34267SW"/>
     <cge:Meas_Ref ObjectId="225702"/>
    <cge:TPSR_Ref TObjectID="37496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 249.000000 -1256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37493" ObjectName="SW-YM_JB.YM_JB_3429SW"/>
     <cge:Meas_Ref ObjectId="225703"/>
    <cge:TPSR_Ref TObjectID="37493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225730">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37500" ObjectName="SW-YM_JB.YM_JB_3121SW"/>
     <cge:Meas_Ref ObjectId="225730"/>
    <cge:TPSR_Ref TObjectID="37500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225731">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -1027.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37499" ObjectName="SW-YM_JB.YM_JB_31217SW"/>
     <cge:Meas_Ref ObjectId="225731"/>
    <cge:TPSR_Ref TObjectID="37499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225732">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 723.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37502" ObjectName="SW-YM_JB.YM_JB_3122SW"/>
     <cge:Meas_Ref ObjectId="225732"/>
    <cge:TPSR_Ref TObjectID="37502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.000000 -1027.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37501" ObjectName="SW-YM_JB.YM_JB_31227SW"/>
     <cge:Meas_Ref ObjectId="225733"/>
    <cge:TPSR_Ref TObjectID="37501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225752">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 928.000000 -1044.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27101" ObjectName="SW-YM_JB.YM_JB_3902SW"/>
     <cge:Meas_Ref ObjectId="225752"/>
    <cge:TPSR_Ref TObjectID="27101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169474">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 945.000000 -1027.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27102" ObjectName="SW-YM_JB.YM_JB_39020SW"/>
     <cge:Meas_Ref ObjectId="169474"/>
    <cge:TPSR_Ref TObjectID="27102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225753">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 945.000000 -1102.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27103" ObjectName="SW-YM_JB.YM_JB_39027SW"/>
     <cge:Meas_Ref ObjectId="225753"/>
    <cge:TPSR_Ref TObjectID="27103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -122.000000 -982.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27100" ObjectName="SW-YM_JB.YM_JB_39010SW"/>
     <cge:Meas_Ref ObjectId="169424"/>
    <cge:TPSR_Ref TObjectID="27100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169473">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -78.000000 -940.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27099" ObjectName="SW-YM_JB.YM_JB_3901SW"/>
     <cge:Meas_Ref ObjectId="169473"/>
    <cge:TPSR_Ref TObjectID="27099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169475">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -122.000000 -923.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37505" ObjectName="SW-YM_JB.YM_JB_39017SW"/>
     <cge:Meas_Ref ObjectId="169475"/>
    <cge:TPSR_Ref TObjectID="37505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169320">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -958.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27092" ObjectName="SW-YM_JB.YM_JB_3011SW"/>
     <cge:Meas_Ref ObjectId="169320"/>
    <cge:TPSR_Ref TObjectID="27092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 185.000000 -940.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37504" ObjectName="SW-YM_JB.YM_JB_30117SW"/>
     <cge:Meas_Ref ObjectId="225634"/>
    <cge:TPSR_Ref TObjectID="37504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 -963.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27096" ObjectName="SW-YM_JB.YM_JB_3022SW"/>
     <cge:Meas_Ref ObjectId="169381"/>
    <cge:TPSR_Ref TObjectID="27096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.000000 -945.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27097" ObjectName="SW-YM_JB.YM_JB_30227SW"/>
     <cge:Meas_Ref ObjectId="169382"/>
    <cge:TPSR_Ref TObjectID="27097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -242.000000 -467.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37554" ObjectName="SW-YM_JB.YM_JB_04160SW"/>
     <cge:Meas_Ref ObjectId="225755"/>
    <cge:TPSR_Ref TObjectID="37554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -203.500000 -352.500000)" xlink:href="#switch2:shape24_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27118" ObjectName="SW-YM_JB.YM_JB_04167SW"/>
     <cge:Meas_Ref ObjectId="225756"/>
    <cge:TPSR_Ref TObjectID="27118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -197.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37550" ObjectName="SW-YM_JB.YM_JB_0416SW"/>
     <cge:Meas_Ref ObjectId="226135"/>
    <cge:TPSR_Ref TObjectID="37550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -88.000000 -417.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27110" ObjectName="SW-YM_JB.YM_JB_04260SW"/>
     <cge:Meas_Ref ObjectId="225777"/>
    <cge:TPSR_Ref TObjectID="27110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45.000000 -417.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37563" ObjectName="SW-YM_JB.YM_JB_04360SW"/>
     <cge:Meas_Ref ObjectId="226138"/>
    <cge:TPSR_Ref TObjectID="37563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226139">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 69.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37564" ObjectName="SW-YM_JB.YM_JB_0436SW"/>
     <cge:Meas_Ref ObjectId="226139"/>
    <cge:TPSR_Ref TObjectID="37564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225812">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 161.000000 -416.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37559" ObjectName="SW-YM_JB.YM_JB_04460SW"/>
     <cge:Meas_Ref ObjectId="225812"/>
    <cge:TPSR_Ref TObjectID="37559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 185.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37560" ObjectName="SW-YM_JB.YM_JB_0446SW"/>
     <cge:Meas_Ref ObjectId="226141"/>
    <cge:TPSR_Ref TObjectID="37560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169484">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 282.000000 -394.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27111" ObjectName="SW-YM_JB.YM_JB_04560SW"/>
     <cge:Meas_Ref ObjectId="169484"/>
    <cge:TPSR_Ref TObjectID="27111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169533">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 404.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27119" ObjectName="SW-YM_JB.YM_JB_04660SW"/>
     <cge:Meas_Ref ObjectId="169533"/>
    <cge:TPSR_Ref TObjectID="27119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37562" ObjectName="SW-YM_JB.YM_JB_04760SW"/>
     <cge:Meas_Ref ObjectId="225834"/>
    <cge:TPSR_Ref TObjectID="37562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225856">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37512" ObjectName="SW-YM_JB.YM_JB_04860SW"/>
     <cge:Meas_Ref ObjectId="225856"/>
    <cge:TPSR_Ref TObjectID="37512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225878">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.000000 -413.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37556" ObjectName="SW-YM_JB.YM_JB_04960SW"/>
     <cge:Meas_Ref ObjectId="225878"/>
    <cge:TPSR_Ref TObjectID="37556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225879">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37555" ObjectName="SW-YM_JB.YM_JB_0496SW"/>
     <cge:Meas_Ref ObjectId="225879"/>
    <cge:TPSR_Ref TObjectID="37555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225901">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.000000 -412.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37551" ObjectName="SW-YM_JB.YM_JB_05160SW"/>
     <cge:Meas_Ref ObjectId="225901"/>
    <cge:TPSR_Ref TObjectID="37551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225902">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37552" ObjectName="SW-YM_JB.YM_JB_0516SW"/>
     <cge:Meas_Ref ObjectId="225902"/>
    <cge:TPSR_Ref TObjectID="37552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225925">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1296.000000 -732.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27117" ObjectName="SW-YM_JB.YM_JB_05260SW"/>
     <cge:Meas_Ref ObjectId="225925"/>
    <cge:TPSR_Ref TObjectID="27117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225926">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1306.500000 -854.500000)" xlink:href="#switch2:shape24_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27109" ObjectName="SW-YM_JB.YM_JB_05267SW"/>
     <cge:Meas_Ref ObjectId="225926"/>
    <cge:TPSR_Ref TObjectID="27109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225921">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1300.000000 -853.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37558" ObjectName="SW-YM_JB.YM_JB_0526SW"/>
     <cge:Meas_Ref ObjectId="225921"/>
    <cge:TPSR_Ref TObjectID="37558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225923">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1281.000000 -686.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37544" ObjectName="SW-YM_JB.YM_JB_052XC1"/>
     <cge:Meas_Ref ObjectId="225923"/>
    <cge:TPSR_Ref TObjectID="37544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225923">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1281.000000 -619.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37543" ObjectName="SW-YM_JB.YM_JB_052XC"/>
     <cge:Meas_Ref ObjectId="225923"/>
    <cge:TPSR_Ref TObjectID="37543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -198.000000 -556.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37526" ObjectName="SW-YM_JB.YM_JB_041XC"/>
     <cge:Meas_Ref ObjectId="225754"/>
    <cge:TPSR_Ref TObjectID="37526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -198.000000 -489.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37527" ObjectName="SW-YM_JB.YM_JB_041XC1"/>
     <cge:Meas_Ref ObjectId="225754"/>
    <cge:TPSR_Ref TObjectID="37527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 543.000000 -706.500000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37545" ObjectName="SW-YM_JB.YM_JB_012XC"/>
     <cge:Meas_Ref ObjectId="225947"/>
    <cge:TPSR_Ref TObjectID="37545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -707.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37546" ObjectName="SW-YM_JB.YM_JB_012XC1"/>
     <cge:Meas_Ref ObjectId="225947"/>
    <cge:TPSR_Ref TObjectID="37546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -683.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37517" ObjectName="SW-YM_JB.YM_JB_001XC"/>
     <cge:Meas_Ref ObjectId="169343"/>
    <cge:TPSR_Ref TObjectID="37517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -616.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37518" ObjectName="SW-YM_JB.YM_JB_001XC1"/>
     <cge:Meas_Ref ObjectId="169343"/>
    <cge:TPSR_Ref TObjectID="37518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169423">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.000000 -684.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37519" ObjectName="SW-YM_JB.YM_JB_002XC"/>
     <cge:Meas_Ref ObjectId="169423"/>
    <cge:TPSR_Ref TObjectID="37519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169423">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.000000 -617.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37520" ObjectName="SW-YM_JB.YM_JB_002XC1"/>
     <cge:Meas_Ref ObjectId="169423"/>
    <cge:TPSR_Ref TObjectID="37520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -144.000000 -618.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27107" ObjectName="SW-YM_JB.YM_JB_0901XC"/>
     <cge:Meas_Ref ObjectId="225962"/>
    <cge:TPSR_Ref TObjectID="27107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225972">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -625.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27094" ObjectName="SW-YM_JB.YM_JB_0902SW"/>
     <cge:Meas_Ref ObjectId="225972"/>
    <cge:TPSR_Ref TObjectID="27094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.000000 -488.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37529" ObjectName="SW-YM_JB.YM_JB_042XC1"/>
     <cge:Meas_Ref ObjectId="225776"/>
    <cge:TPSR_Ref TObjectID="37529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.000000 -555.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37528" ObjectName="SW-YM_JB.YM_JB_042XC"/>
     <cge:Meas_Ref ObjectId="225776"/>
    <cge:TPSR_Ref TObjectID="37528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 68.000000 -492.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37531" ObjectName="SW-YM_JB.YM_JB_043XC1"/>
     <cge:Meas_Ref ObjectId="225790"/>
    <cge:TPSR_Ref TObjectID="37531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 68.000000 -559.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37530" ObjectName="SW-YM_JB.YM_JB_043XC"/>
     <cge:Meas_Ref ObjectId="225790"/>
    <cge:TPSR_Ref TObjectID="37530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225810">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 -491.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37533" ObjectName="SW-YM_JB.YM_JB_044XC1"/>
     <cge:Meas_Ref ObjectId="225810"/>
    <cge:TPSR_Ref TObjectID="37533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225810">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 -558.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37532" ObjectName="SW-YM_JB.YM_JB_044XC"/>
     <cge:Meas_Ref ObjectId="225810"/>
    <cge:TPSR_Ref TObjectID="37532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 -556.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37521" ObjectName="SW-YM_JB.YM_JB_045XC"/>
     <cge:Meas_Ref ObjectId="169485"/>
    <cge:TPSR_Ref TObjectID="37521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 -489.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37522" ObjectName="SW-YM_JB.YM_JB_045XC1"/>
     <cge:Meas_Ref ObjectId="169485"/>
    <cge:TPSR_Ref TObjectID="37522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -559.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37523" ObjectName="SW-YM_JB.YM_JB_046XC"/>
     <cge:Meas_Ref ObjectId="169534"/>
    <cge:TPSR_Ref TObjectID="37523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -492.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37524" ObjectName="SW-YM_JB.YM_JB_046XC1"/>
     <cge:Meas_Ref ObjectId="169534"/>
    <cge:TPSR_Ref TObjectID="37524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -492.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37535" ObjectName="SW-YM_JB.YM_JB_047XC1"/>
     <cge:Meas_Ref ObjectId="225832"/>
    <cge:TPSR_Ref TObjectID="37535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -559.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37534" ObjectName="SW-YM_JB.YM_JB_047XC"/>
     <cge:Meas_Ref ObjectId="225832"/>
    <cge:TPSR_Ref TObjectID="37534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225854">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -557.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37536" ObjectName="SW-YM_JB.YM_JB_048XC"/>
     <cge:Meas_Ref ObjectId="225854"/>
    <cge:TPSR_Ref TObjectID="37536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225854">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -490.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37537" ObjectName="SW-YM_JB.YM_JB_048XC1"/>
     <cge:Meas_Ref ObjectId="225854"/>
    <cge:TPSR_Ref TObjectID="37537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225876">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 -558.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37538" ObjectName="SW-YM_JB.YM_JB_049XC"/>
     <cge:Meas_Ref ObjectId="225876"/>
    <cge:TPSR_Ref TObjectID="37538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225876">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 -491.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37539" ObjectName="SW-YM_JB.YM_JB_049XC1"/>
     <cge:Meas_Ref ObjectId="225876"/>
    <cge:TPSR_Ref TObjectID="37539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225899">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -490.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37541" ObjectName="SW-YM_JB.YM_JB_051XC1"/>
     <cge:Meas_Ref ObjectId="225899"/>
    <cge:TPSR_Ref TObjectID="37541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225899">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -557.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37540" ObjectName="SW-YM_JB.YM_JB_051XC"/>
     <cge:Meas_Ref ObjectId="225899"/>
    <cge:TPSR_Ref TObjectID="37540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -677.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37548" ObjectName="SW-YM_JB.YM_JB_0121XC1"/>
     <cge:Meas_Ref ObjectId="225949"/>
    <cge:TPSR_Ref TObjectID="37548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -619.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37547" ObjectName="SW-YM_JB.YM_JB_0121XC"/>
     <cge:Meas_Ref ObjectId="225949"/>
    <cge:TPSR_Ref TObjectID="37547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46052" ObjectName="SW-YM_JB.YM_JB_04667SW"/>
     <cge:Meas_Ref ObjectId="169607"/>
    <cge:TPSR_Ref TObjectID="46052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169608">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 -274.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46053" ObjectName="SW-YM_JB.YM_JB_0466SW"/>
     <cge:Meas_Ref ObjectId="169608"/>
    <cge:TPSR_Ref TObjectID="46053"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_JB.YM_JB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-1016 518,-1016 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27088" ObjectName="BS-YM_JB.YM_JB_3IM"/>
    <cge:TPSR_Ref TObjectID="27088"/></metadata>
   <polyline fill="none" opacity="0" points="-192,-1016 518,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_JB.YM_JB_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-1016 1159,-1016 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37565" ObjectName="BS-YM_JB.YM_JB_3IIM"/>
    <cge:TPSR_Ref TObjectID="37565"/></metadata>
   <polyline fill="none" opacity="0" points="709,-1016 1159,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_JB.YM_JB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-227,-606 508,-606 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27089" ObjectName="BS-YM_JB.YM_JB_9IM"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   <polyline fill="none" opacity="0" points="-227,-606 508,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_JB.YM_JB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-606 1438,-606 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37566" ObjectName="BS-YM_JB.YM_JB_9IIM"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   <polyline fill="none" opacity="0" points="634,-606 1438,-606 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YM_JB.YM_JB_cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -213.000000 -241.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37567" ObjectName="CB-YM_JB.YM_JB_cb1"/>
    <cge:TPSR_Ref TObjectID="37567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-YM_JB.YM_JB_cb2">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1316.000000 -971.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37568" ObjectName="CB-YM_JB.YM_JB_cb2"/>
    <cge:TPSR_Ref TObjectID="37568"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YM_JB.YM_JB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38308"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.000000 -793.000000)" xlink:href="#transformer2:shape83_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.000000 -793.000000)" xlink:href="#transformer2:shape83_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27132" ObjectName="TF-YM_JB.YM_JB_1T"/>
    <cge:TPSR_Ref TObjectID="27132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_JB.YM_JB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38312"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 -798.000000)" xlink:href="#transformer2:shape83_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 -798.000000)" xlink:href="#transformer2:shape83_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27133" ObjectName="TF-YM_JB.YM_JB_2T"/>
    <cge:TPSR_Ref TObjectID="27133"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13fc3c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -20.000000 -1223.000000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13fdc80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -55.000000 -1060.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13ffac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -45.000000 -1175.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200cd40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 95.000000 -1212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cda8b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.000000 -1212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d02520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -780.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d032d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -715.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d04dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -731.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16af460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.000000 -784.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16b0210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.000000 -720.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16b1220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.000000 -736.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16b2e80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -173.000000 -418.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d08110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -198.000000 -412.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c6c820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.000000 -412.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c6e350">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -40.000000 -397.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c6f080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -70.000000 -260.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c73900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 68.000000 -412.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c75430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 93.000000 -397.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0e770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 90.000000 -277.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c133b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 -411.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c14ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 209.000000 -396.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c18670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 206.000000 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c1d2b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 -389.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c1e920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 -374.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c926a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -381.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c93d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 456.000000 -366.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c99b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -378.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c9b200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -364.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca04c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 821.000000 -377.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca1b30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -362.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fab110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 -408.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fac780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 -393.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb0170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -270.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb4520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -407.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb5b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -392.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb9580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 -269.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2095300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.000000 -673.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20964f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1281.000000 -744.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2052aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217875" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -591.000000 -977.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217875" ObjectName="YM_JB:YM_JB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219735" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -939.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219735" ObjectName="YM_JB:YM_JB_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217875" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -1061.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217875" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217875" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -591.000000 -1017.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217875" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-169222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -1134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27088"/>
     <cge:Term_Ref ObjectID="38220"/>
    <cge:TPSR_Ref TObjectID="27088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-169223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -1134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27088"/>
     <cge:Term_Ref ObjectID="38220"/>
    <cge:TPSR_Ref TObjectID="27088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-169224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -1134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27088"/>
     <cge:Term_Ref ObjectID="38220"/>
    <cge:TPSR_Ref TObjectID="27088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-169228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -1134.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27088"/>
     <cge:Term_Ref ObjectID="38220"/>
    <cge:TPSR_Ref TObjectID="27088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-169225" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -1134.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27088"/>
     <cge:Term_Ref ObjectID="38220"/>
    <cge:TPSR_Ref TObjectID="27088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-227083" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -1134.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227083" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27088"/>
     <cge:Term_Ref ObjectID="38220"/>
    <cge:TPSR_Ref TObjectID="27088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -1372.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37497"/>
     <cge:Term_Ref ObjectID="56080"/>
    <cge:TPSR_Ref TObjectID="37497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -1372.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37497"/>
     <cge:Term_Ref ObjectID="56080"/>
    <cge:TPSR_Ref TObjectID="37497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -1372.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37497"/>
     <cge:Term_Ref ObjectID="56080"/>
    <cge:TPSR_Ref TObjectID="37497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 297.000000 -1370.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37498"/>
     <cge:Term_Ref ObjectID="56082"/>
    <cge:TPSR_Ref TObjectID="37498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 297.000000 -1370.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37498"/>
     <cge:Term_Ref ObjectID="56082"/>
    <cge:TPSR_Ref TObjectID="37498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 297.000000 -1370.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37498"/>
     <cge:Term_Ref ObjectID="56082"/>
    <cge:TPSR_Ref TObjectID="37498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169197" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -936.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169197" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27091"/>
     <cge:Term_Ref ObjectID="38224"/>
    <cge:TPSR_Ref TObjectID="27091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -936.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27091"/>
     <cge:Term_Ref ObjectID="38224"/>
    <cge:TPSR_Ref TObjectID="27091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -936.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27091"/>
     <cge:Term_Ref ObjectID="38224"/>
    <cge:TPSR_Ref TObjectID="27091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -942.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27095"/>
     <cge:Term_Ref ObjectID="38232"/>
    <cge:TPSR_Ref TObjectID="27095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169212" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -942.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27095"/>
     <cge:Term_Ref ObjectID="38232"/>
    <cge:TPSR_Ref TObjectID="27095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -942.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27095"/>
     <cge:Term_Ref ObjectID="38232"/>
    <cge:TPSR_Ref TObjectID="27095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169217" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.000000 -687.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27098"/>
     <cge:Term_Ref ObjectID="38238"/>
    <cge:TPSR_Ref TObjectID="27098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169218" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.000000 -687.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27098"/>
     <cge:Term_Ref ObjectID="38238"/>
    <cge:TPSR_Ref TObjectID="27098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.000000 -687.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27098"/>
     <cge:Term_Ref ObjectID="38238"/>
    <cge:TPSR_Ref TObjectID="27098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-226181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -1134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37565"/>
     <cge:Term_Ref ObjectID="56216"/>
    <cge:TPSR_Ref TObjectID="37565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-226182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -1134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37565"/>
     <cge:Term_Ref ObjectID="56216"/>
    <cge:TPSR_Ref TObjectID="37565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-226183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -1134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37565"/>
     <cge:Term_Ref ObjectID="56216"/>
    <cge:TPSR_Ref TObjectID="37565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-226187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -1134.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37565"/>
     <cge:Term_Ref ObjectID="56216"/>
    <cge:TPSR_Ref TObjectID="37565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-226184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -1134.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37565"/>
     <cge:Term_Ref ObjectID="56216"/>
    <cge:TPSR_Ref TObjectID="37565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-226251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -1134.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37565"/>
     <cge:Term_Ref ObjectID="56216"/>
    <cge:TPSR_Ref TObjectID="37565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -1157.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37503"/>
     <cge:Term_Ref ObjectID="56092"/>
    <cge:TPSR_Ref TObjectID="37503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -1157.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37503"/>
     <cge:Term_Ref ObjectID="56092"/>
    <cge:TPSR_Ref TObjectID="37503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -1157.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37503"/>
     <cge:Term_Ref ObjectID="56092"/>
    <cge:TPSR_Ref TObjectID="37503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-169207" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 -837.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169207" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27132"/>
     <cge:Term_Ref ObjectID="38306"/>
    <cge:TPSR_Ref TObjectID="27132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-169206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 -837.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27132"/>
     <cge:Term_Ref ObjectID="38306"/>
    <cge:TPSR_Ref TObjectID="27132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-169221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 973.000000 -848.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27133"/>
     <cge:Term_Ref ObjectID="38310"/>
    <cge:TPSR_Ref TObjectID="27133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-169220" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 973.000000 -848.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27133"/>
     <cge:Term_Ref ObjectID="38310"/>
    <cge:TPSR_Ref TObjectID="27133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -690.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27093"/>
     <cge:Term_Ref ObjectID="38228"/>
    <cge:TPSR_Ref TObjectID="27093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -690.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27093"/>
     <cge:Term_Ref ObjectID="38228"/>
    <cge:TPSR_Ref TObjectID="27093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -690.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27093"/>
     <cge:Term_Ref ObjectID="38228"/>
    <cge:TPSR_Ref TObjectID="27093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-226188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-226189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-226190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-226194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-226191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-226192" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226192" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-226193" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226193" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-226250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -749.500000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37566"/>
     <cge:Term_Ref ObjectID="56217"/>
    <cge:TPSR_Ref TObjectID="37566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 317.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27108"/>
     <cge:Term_Ref ObjectID="38258"/>
    <cge:TPSR_Ref TObjectID="27108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 317.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27108"/>
     <cge:Term_Ref ObjectID="38258"/>
    <cge:TPSR_Ref TObjectID="27108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 317.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27108"/>
     <cge:Term_Ref ObjectID="38258"/>
    <cge:TPSR_Ref TObjectID="27108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -170.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27116"/>
     <cge:Term_Ref ObjectID="38274"/>
    <cge:TPSR_Ref TObjectID="27116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -170.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27116"/>
     <cge:Term_Ref ObjectID="38274"/>
    <cge:TPSR_Ref TObjectID="27116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -170.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27116"/>
     <cge:Term_Ref ObjectID="38274"/>
    <cge:TPSR_Ref TObjectID="27116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37508"/>
     <cge:Term_Ref ObjectID="56102"/>
    <cge:TPSR_Ref TObjectID="37508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37508"/>
     <cge:Term_Ref ObjectID="56102"/>
    <cge:TPSR_Ref TObjectID="37508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37508"/>
     <cge:Term_Ref ObjectID="56102"/>
    <cge:TPSR_Ref TObjectID="37508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37509"/>
     <cge:Term_Ref ObjectID="56104"/>
    <cge:TPSR_Ref TObjectID="37509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37509"/>
     <cge:Term_Ref ObjectID="56104"/>
    <cge:TPSR_Ref TObjectID="37509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37509"/>
     <cge:Term_Ref ObjectID="56104"/>
    <cge:TPSR_Ref TObjectID="37509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 986.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37553"/>
     <cge:Term_Ref ObjectID="56192"/>
    <cge:TPSR_Ref TObjectID="37553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 986.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37553"/>
     <cge:Term_Ref ObjectID="56192"/>
    <cge:TPSR_Ref TObjectID="37553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 986.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37553"/>
     <cge:Term_Ref ObjectID="56192"/>
    <cge:TPSR_Ref TObjectID="37553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37549"/>
     <cge:Term_Ref ObjectID="56184"/>
    <cge:TPSR_Ref TObjectID="37549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37549"/>
     <cge:Term_Ref ObjectID="56184"/>
    <cge:TPSR_Ref TObjectID="37549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37549"/>
     <cge:Term_Ref ObjectID="56184"/>
    <cge:TPSR_Ref TObjectID="37549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -212.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37511"/>
     <cge:Term_Ref ObjectID="56108"/>
    <cge:TPSR_Ref TObjectID="37511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226195" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 -212.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37511"/>
     <cge:Term_Ref ObjectID="56108"/>
    <cge:TPSR_Ref TObjectID="37511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -1020.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37542"/>
     <cge:Term_Ref ObjectID="56170"/>
    <cge:TPSR_Ref TObjectID="37542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 -1020.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37542"/>
     <cge:Term_Ref ObjectID="56170"/>
    <cge:TPSR_Ref TObjectID="37542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226232" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -795.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37506"/>
     <cge:Term_Ref ObjectID="56098"/>
    <cge:TPSR_Ref TObjectID="37506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -795.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37506"/>
     <cge:Term_Ref ObjectID="56098"/>
    <cge:TPSR_Ref TObjectID="37506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226229" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -795.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37506"/>
     <cge:Term_Ref ObjectID="56098"/>
    <cge:TPSR_Ref TObjectID="37506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-226485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-226486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-226487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-226484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-226488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-226489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-226490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-226491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -731.500000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27089"/>
     <cge:Term_Ref ObjectID="38221"/>
    <cge:TPSR_Ref TObjectID="27089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-169271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37561"/>
     <cge:Term_Ref ObjectID="56208"/>
    <cge:TPSR_Ref TObjectID="37561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-169272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37561"/>
     <cge:Term_Ref ObjectID="56208"/>
    <cge:TPSR_Ref TObjectID="37561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-169268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="169268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37561"/>
     <cge:Term_Ref ObjectID="56208"/>
    <cge:TPSR_Ref TObjectID="37561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37510"/>
     <cge:Term_Ref ObjectID="56106"/>
    <cge:TPSR_Ref TObjectID="37510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37510"/>
     <cge:Term_Ref ObjectID="56106"/>
    <cge:TPSR_Ref TObjectID="37510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226218" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37510"/>
     <cge:Term_Ref ObjectID="56106"/>
    <cge:TPSR_Ref TObjectID="37510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-226215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -217.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37557"/>
     <cge:Term_Ref ObjectID="56200"/>
    <cge:TPSR_Ref TObjectID="37557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-226216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -217.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37557"/>
     <cge:Term_Ref ObjectID="56200"/>
    <cge:TPSR_Ref TObjectID="37557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-226212" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -217.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37557"/>
     <cge:Term_Ref ObjectID="56200"/>
    <cge:TPSR_Ref TObjectID="37557"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-602" y="-1250"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-602" y="-1250"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-458" y="-1231"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-458" y="-1231"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-458" y="-1270"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-458" y="-1270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-320,-1259 -323,-1262 -323,-1209 -320,-1212 -320,-1259" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-320,-1259 -323,-1262 -263,-1262 -266,-1259 -320,-1259" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-320,-1212 -323,-1209 -263,-1209 -266,-1212 -320,-1212" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-266,-1259 -263,-1262 -263,-1209 -266,-1212 -266,-1259" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="54" x="-320" y="-1259"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(255,0,0)" width="54" x="-320" y="-1259"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="98" y="-1114"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="98" y="-1114"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="309" y="-1116"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="309" y="-1116"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="586" y="-1101"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="586" y="-1101"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="275" y="-857"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="275" y="-857"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="897" y="-869"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="897" y="-869"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="577" y="-740"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="577" y="-740"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-180" y="-545"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-180" y="-545"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-47" y="-544"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-47" y="-544"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="87" y="-548"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="87" y="-548"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="682" y="-548"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="682" y="-548"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="840" y="-546"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="840" y="-546"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1001" y="-547"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1001" y="-547"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1124" y="-546"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1124" y="-546"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1300" y="-675"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1300" y="-675"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="450" y="-548"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="450" y="-548"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="328" y="-545"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="328" y="-545"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="203" y="-547"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="203" y="-547"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="30" qtmmishow="hidden" width="120" x="-699" y="-834"/>
    </a>
   <metadata/><rect fill="white" height="30" opacity="0" stroke="white" transform="" width="120" x="-699" y="-834"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-602" y="-1250"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-458" y="-1231"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-458" y="-1270"/></g>
   <g href="AVC江边站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(255,0,0)" width="54" x="-320" y="-1259"/></g>
   <g href="35kV江边变35kV黄江线341间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="98" y="-1114"/></g>
   <g href="35kV江边变35kV边驿线342间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="309" y="-1116"/></g>
   <g href="35kV江边变35kV分段312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="586" y="-1101"/></g>
   <g href="35kV江边变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="275" y="-857"/></g>
   <g href="35kV江边变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="897" y="-869"/></g>
   <g href="35kV江边变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="577" y="-740"/></g>
   <g href="35kV江边变10kV1号电容器041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-180" y="-545"/></g>
   <g href="35kV江边变10kV2号站用变042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-47" y="-544"/></g>
   <g href="35kV江边变10kV姜驿线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="87" y="-548"/></g>
   <g href="35kV江边变10kV备用三线047间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="682" y="-548"/></g>
   <g href="35kV江边变10kV备用三线048间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="840" y="-546"/></g>
   <g href="35kV江边变10kV鱼窝线049间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1001" y="-547"/></g>
   <g href="35kV江边变10kV红江线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1124" y="-546"/></g>
   <g href="35kV江边变10kV2号电容器052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1300" y="-675"/></g>
   <g href="35kV江边变10kV江龙线046间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="450" y="-548"/></g>
   <g href="35kV江边变10kV备用一线045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="328" y="-545"/></g>
   <g href="35kV江边变10kV启宪线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="203" y="-547"/></g>
   <g href="35kV江边变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="30" qtmmishow="hidden" width="120" x="-699" y="-834"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-225670">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 73.000000 -1087.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37497" ObjectName="SW-YM_JB.YM_JB_341BK"/>
     <cge:Meas_Ref ObjectId="225670"/>
    <cge:TPSR_Ref TObjectID="37497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225697">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -1087.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37498" ObjectName="SW-YM_JB.YM_JB_342BK"/>
     <cge:Meas_Ref ObjectId="225697"/>
    <cge:TPSR_Ref TObjectID="37498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225729">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 576.000000 -1067.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37503" ObjectName="SW-YM_JB.YM_JB_312BK"/>
     <cge:Meas_Ref ObjectId="225729"/>
    <cge:TPSR_Ref TObjectID="37503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -895.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27091" ObjectName="SW-YM_JB.YM_JB_301BK"/>
     <cge:Meas_Ref ObjectId="169318"/>
    <cge:TPSR_Ref TObjectID="27091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 -900.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27095" ObjectName="SW-YM_JB.YM_JB_302BK"/>
     <cge:Meas_Ref ObjectId="169379"/>
    <cge:TPSR_Ref TObjectID="27095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225922">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 -646.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37542" ObjectName="SW-YM_JB.YM_JB_052BK"/>
     <cge:Meas_Ref ObjectId="225922"/>
    <cge:TPSR_Ref TObjectID="37542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226134">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -198.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37511" ObjectName="SW-YM_JB.YM_JB_041BK"/>
     <cge:Meas_Ref ObjectId="226134"/>
    <cge:TPSR_Ref TObjectID="37511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225946">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -706.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37506" ObjectName="SW-YM_JB.YM_JB_012BK"/>
     <cge:Meas_Ref ObjectId="225946"/>
    <cge:TPSR_Ref TObjectID="37506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -643.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27093" ObjectName="SW-YM_JB.YM_JB_001BK"/>
     <cge:Meas_Ref ObjectId="169341"/>
    <cge:TPSR_Ref TObjectID="27093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 -644.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27098" ObjectName="SW-YM_JB.YM_JB_002BK"/>
     <cge:Meas_Ref ObjectId="169421"/>
    <cge:TPSR_Ref TObjectID="27098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.000000 -515.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37510" ObjectName="SW-YM_JB.YM_JB_042BK"/>
     <cge:Meas_Ref ObjectId="226136"/>
    <cge:TPSR_Ref TObjectID="37510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 69.000000 -519.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37561" ObjectName="SW-YM_JB.YM_JB_043BK"/>
     <cge:Meas_Ref ObjectId="226137"/>
    <cge:TPSR_Ref TObjectID="37561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226140">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 185.000000 -518.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37557" ObjectName="SW-YM_JB.YM_JB_044BK"/>
     <cge:Meas_Ref ObjectId="226140"/>
    <cge:TPSR_Ref TObjectID="37557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27108" ObjectName="SW-YM_JB.YM_JB_045BK"/>
     <cge:Meas_Ref ObjectId="169480"/>
    <cge:TPSR_Ref TObjectID="27108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-169529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 -519.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27116" ObjectName="SW-YM_JB.YM_JB_046BK"/>
     <cge:Meas_Ref ObjectId="169529"/>
    <cge:TPSR_Ref TObjectID="27116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -519.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37508" ObjectName="SW-YM_JB.YM_JB_047BK"/>
     <cge:Meas_Ref ObjectId="225831"/>
    <cge:TPSR_Ref TObjectID="37508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225853">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.000000 -517.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37509" ObjectName="SW-YM_JB.YM_JB_048BK"/>
     <cge:Meas_Ref ObjectId="225853"/>
    <cge:TPSR_Ref TObjectID="37509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225875">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 -518.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37553" ObjectName="SW-YM_JB.YM_JB_049BK"/>
     <cge:Meas_Ref ObjectId="225875"/>
    <cge:TPSR_Ref TObjectID="37553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-225898">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -517.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37549" ObjectName="SW-YM_JB.YM_JB_051BK"/>
     <cge:Meas_Ref ObjectId="225898"/>
    <cge:TPSR_Ref TObjectID="37549"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HGY" endPointId="0" endStationName="YM_JB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_huanjianxian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="82,-1295 82,-1314 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37848" ObjectName="AC-35kV.LN_huanjianxian"/>
    <cge:TPSR_Ref TObjectID="37848_SS-236"/></metadata>
   <polyline fill="none" opacity="0" points="82,-1295 82,-1314 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_JB" endPointId="0" endStationName="YM_JY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_bianyi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="300,-1295 300,-1314 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38092" ObjectName="AC-35kV.LN_bianyi"/>
    <cge:TPSR_Ref TObjectID="38092_SS-236"/></metadata>
   <polyline fill="none" opacity="0" points="300,-1295 300,-1314 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2019cb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1.000000 -1074.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cad260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2.000000 -1131.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cb09b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2.000000 -1194.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2e4c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 217.000000 -1074.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c31940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 216.000000 -1131.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e3a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 216.000000 -1194.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1699e10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.000000 -1004.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb4540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 653.000000 -1004.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbbc40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 -1026.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf5370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 -1101.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bfdcd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -152.000000 -981.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bca610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -152.000000 -922.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cfbf10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -939.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16a96e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.000000 -944.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d071c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -283.000000 -466.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c6d900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -85.000000 -395.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c749e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 48.000000 -395.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c14490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 164.000000 -394.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c1ded0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 -367.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c932c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.000000 -359.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c9a7b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 639.000000 -356.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca10e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 797.000000 -355.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fabd30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 958.000000 -386.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb5140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1081.000000 -385.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2094870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1346.000000 -731.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204bf40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -334.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27089" cx="-188" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27089" cx="78" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37566" cx="673" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37566" cx="1291" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37565" cx="732" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37565" cx="937" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37565" cx="863" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27088" cx="82" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27088" cx="300" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27088" cx="496" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27088" cx="-69" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27088" cx="238" cy="-1016" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27089" cx="238" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27089" cx="-134" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37566" cx="1159" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27089" cx="194" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27089" cx="319" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37566" cx="831" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37566" cx="992" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37566" cx="1115" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37566" cx="702" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27089" cx="474" cy="-606" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_1690140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">江边变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1690e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca8300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -890.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -890.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -890.000000) translate(0,42)">5000/5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -890.000000) translate(0,57)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -890.000000) translate(0,72)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -890.000000) translate(0,87)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_16a2540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -447.000000 -1224.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_16a3440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -447.000000 -1261.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14008d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -64.856879 -1058.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200da70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4.143121 -1317.000000) translate(0,12)">35kV黄江线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdb5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 225.143121 -1315.000000) translate(0,12)">35kV边驿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb8210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.143121 -1059.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bfc220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.806094 -1250.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bfc220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.806094 -1250.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcd830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -181.856879 -876.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcd830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -181.856879 -876.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16b27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -892.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16b27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -892.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16b27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -892.000000) translate(0,42)">5000/5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16b27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -892.000000) translate(0,57)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16b27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -892.000000) translate(0,72)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16b27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -892.000000) translate(0,87)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1d15870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -245.000000 -235.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c69ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -103.856879 -805.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c69ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -103.856879 -805.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c70ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -88.000000 -251.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c96410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -251.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c96f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 428.000000 -204.000000) translate(0,12)">江龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1c9d8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 658.000000 -251.000000) translate(0,12)">备用三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_1ca3420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 816.000000 -251.000000) translate(0,12)">备用四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a4290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.143121 -863.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a4290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.143121 -863.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d18dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98.000000 -1114.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -1055.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -1176.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d196b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -1226.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d198f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -1163.000000) translate(0,12)">34160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1104.000000) translate(0,12)">34117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 38.000000 -1287.000000) translate(0,12)">3419</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -1116.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 307.000000 -1176.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 307.000000 -1055.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1aac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -1105.000000) translate(0,12)">34217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 249.000000 -1163.000000) translate(0,12)">34260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1af40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 249.000000 -1226.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 256.000000 -1287.000000) translate(0,12)">3429</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 586.000000 -1101.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -1058.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 506.000000 -1056.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ba80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -1057.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1bcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -1052.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1bf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 -1074.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 947.000000 -1058.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 947.000000 -1133.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -119.000000 -1010.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -62.000000 -970.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ca40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -119.000000 -951.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1f6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -924.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1fd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -988.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ff40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 188.000000 -971.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -929.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d203c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 870.000000 -993.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 812.000000 -976.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -192.000000 -1035.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -1035.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -857.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -869.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bef540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -673.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201e3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -740.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202feb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -672.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2030a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -673.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2030cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -545.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2030ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -600.000000) translate(0,12)">10kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2031130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -626.000000) translate(0,12)">10kVⅡ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2031370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -498.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20315b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -181.000000 -389.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20317f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -289.000000 -386.000000) translate(0,12)">04167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2031a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1248.000000 -988.000000) translate(0,12)">10kV2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2031c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1300.000000 -675.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2031eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1250.000000 -838.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20320f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -834.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20731f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -672.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2073820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -122.000000 -665.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207c640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -47.000000 -544.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207cc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -128.000000 -442.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c48b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 328.000000 -545.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c49170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 239.000000 -421.000000) translate(0,12)">04560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c493b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -548.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c495f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 360.000000 -413.000000) translate(0,12)">04660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c49830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 -548.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c49a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 590.000000 -411.000000) translate(0,12)">04760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c49cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.000000 -546.000000) translate(0,12)">048</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4a060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 749.000000 -412.000000) translate(0,12)">04860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4a540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -547.000000) translate(0,12)">049</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -443.000000) translate(0,12)">04960</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4a9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 999.000000 -369.000000) translate(0,12)">0496</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4ac00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -546.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4ae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1035.000000 -442.000000) translate(0,12)">05160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4b080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1122.000000 -368.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4b2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 203.000000 -547.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4b500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 117.000000 -446.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4b740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 201.000000 -375.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4b980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 87.000000 -548.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4bbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -376.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4be00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1.000000 -447.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="18" graphid="g_1c56810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -312.000000 -1245.000000) translate(0,15)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c57730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -762.000000) translate(0,12)">05260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1c59a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -691.000000 -832.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c614e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1093.000000 -250.000000) translate(0,12)">红江线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203b5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 970.000000 -251.000000) translate(0,12)">渔窝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203be20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 167.000000 -251.000000) translate(0,12)">启宪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203c6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 52.000000 -251.000000) translate(0,12)">姜驿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2045ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -741.000000 -265.500000) translate(0,17)">元谋巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20475c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -275.000000) translate(0,16)">18787879021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20475c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -275.000000) translate(0,36)">13908784331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2048c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -306.000000) translate(0,16)">8351070</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2049b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -703.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2049d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -718.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2049fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -734.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204a200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -289.000000 -686.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204a440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -283.000000 -626.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204a680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -303.000000 -671.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204a8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -303.000000 -655.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -303.000000 -640.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204ad40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -719.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204af80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -734.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204b1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -750.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204b400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1414.000000 -702.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204b640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1420.000000 -642.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204b880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -687.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204bac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -671.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204bd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -656.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20514c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -317.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2051af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 537.000000 -345.000000) translate(0,12)">04667</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca9ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -114.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7bc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7cb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -100.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7d4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 821.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7e080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 836.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d240a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.000000 833.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d242c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.000000 848.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4c130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -279.000000 212.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4c390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -254.000000 197.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4c6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 1021.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4c920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 1006.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.000000 -10.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5af40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -222.000000 1077.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5b870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 1093.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5bdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 1106.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5c320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -237.000000 1062.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5c5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 1124.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5c7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -220.000000 1048.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 -10.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5d530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -222.000000 1077.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5d7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 1093.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5d9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 1106.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5dc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -237.000000 1062.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5de70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -229.000000 1124.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c5e0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -220.000000 1048.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203ceb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.000000 1372.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 19.000000 1357.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 44.000000 1342.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 1370.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203da60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.000000 1355.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203dca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 256.000000 1340.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203e0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 548.000000 1157.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203e380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 537.000000 1142.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203e5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 1127.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203e9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 937.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203eca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 922.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203eee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.000000 907.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203f300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.000000 942.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203f5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 904.000000 927.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203f800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 929.000000 912.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203fc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 689.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203fee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 284.000000 674.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2040120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 659.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2040540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 518.000000 792.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2040800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 777.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2040a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.000000 762.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2040e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 921.000000 687.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.000000 672.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 657.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20420a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 133.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2042360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 122.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20425a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 147.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20429c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 265.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2042c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 254.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2042ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20432e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 170.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20435a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 381.000000 155.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20437e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 140.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2043c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2043ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2044100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2044520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20447e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2044a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2044e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2045100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2045340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2045760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2045a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1045.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2045c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1070.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YM_JB.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 69.000000 -257.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34187" ObjectName="EC-YM_JB.043Ld"/>
    <cge:TPSR_Ref TObjectID="34187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JB.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 185.000000 -257.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34186" ObjectName="EC-YM_JB.044Ld"/>
    <cge:TPSR_Ref TObjectID="34186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -257.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JB.046Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 -215.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46051" ObjectName="EC-YM_JB.046Ld"/>
    <cge:TPSR_Ref TObjectID="46051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -257.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.000000 -257.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JB.049Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 -258.500000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34185" ObjectName="EC-YM_JB.049Ld"/>
    <cge:TPSR_Ref TObjectID="34185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JB.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -257.500000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34184" ObjectName="EC-YM_JB.051Ld"/>
    <cge:TPSR_Ref TObjectID="34184"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-154233" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -418.000000 -1154.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26167" ObjectName="DYN-YM_JB"/>
     <cge:Meas_Ref ObjectId="154233"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_200e790">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -42.000000 -1252.000000)" xlink:href="#voltageTransformer:shape130"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cdc210">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 176.000000 -1252.000000)" xlink:href="#voltageTransformer:shape130"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf9480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -1118.000000)" xlink:href="#voltageTransformer:shape131"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d16770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -175.000000 -701.000000)" xlink:href="#voltageTransformer:shape128"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c95660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 481.000000 -356.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c9caf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.000000 -354.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fbb130">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -717.000000)" xlink:href="#voltageTransformer:shape128"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d1ce70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -103.000000 -827.000000)" xlink:href="#voltageTransformer:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2017520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1016 82,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27088@0" ObjectIDZND0="27105@0" Pin0InfoVect0LinkObjId="SW-169477_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfe790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1016 82,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_201a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="17,-1080 34,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2019cb0@0" ObjectIDZND0="37488@0" Pin0InfoVect0LinkObjId="SW-225628_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2019cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="17,-1080 34,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1caaad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="70,-1080 82,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="37488@1" ObjectIDZND0="27105@x" ObjectIDZND1="37497@x" Pin0InfoVect0LinkObjId="SW-169477_0" Pin0InfoVect1LinkObjId="SW-225670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225628_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="70,-1080 82,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cadcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="16,-1137 33,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cad260@0" ObjectIDZND0="37489@0" Pin0InfoVect0LinkObjId="SW-225629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cad260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="16,-1137 33,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cadf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="69,-1137 82,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="37489@1" ObjectIDZND0="37497@x" ObjectIDZND1="27106@x" Pin0InfoVect0LinkObjId="SW-225670_0" Pin0InfoVect1LinkObjId="SW-169478_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225629_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="69,-1137 82,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cb1440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="16,-1200 33,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cb09b0@0" ObjectIDZND0="37490@0" Pin0InfoVect0LinkObjId="SW-225630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cb09b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="16,-1200 33,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cb16a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="69,-1200 82,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37490@1" ObjectIDZND0="27106@x" ObjectIDZND1="g_13fc3c0@0" ObjectIDZND2="37487@x" Pin0InfoVect0LinkObjId="SW-169478_0" Pin0InfoVect1LinkObjId="g_13fc3c0_0" Pin0InfoVect2LinkObjId="SW-225671_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="69,-1200 82,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1066 82,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27105@1" ObjectIDZND0="37488@x" ObjectIDZND1="37497@x" Pin0InfoVect0LinkObjId="SW-225628_0" Pin0InfoVect1LinkObjId="SW-225670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169477_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1066 82,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1095 82,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="37497@0" ObjectIDZND0="37488@x" ObjectIDZND1="27105@x" Pin0InfoVect0LinkObjId="SW-225628_0" Pin0InfoVect1LinkObjId="SW-169477_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1095 82,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fd300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1122 82,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="37497@1" ObjectIDZND0="37489@x" ObjectIDZND1="27106@x" Pin0InfoVect0LinkObjId="SW-225629_0" Pin0InfoVect1LinkObjId="SW-169478_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1122 82,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fd560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1151 82,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27106@0" ObjectIDZND0="37489@x" ObjectIDZND1="37497@x" Pin0InfoVect0LinkObjId="SW-225629_0" Pin0InfoVect1LinkObjId="SW-225670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1151 82,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fd7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1187 82,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27106@1" ObjectIDZND0="37490@x" ObjectIDZND1="g_13fc3c0@0" ObjectIDZND2="37487@x" Pin0InfoVect0LinkObjId="SW-225630_0" Pin0InfoVect1LinkObjId="g_13fc3c0_0" Pin0InfoVect2LinkObjId="SW-225671_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169478_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1187 82,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fda20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1232 82,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_13fc3c0@0" ObjectIDND1="37487@x" ObjectIDND2="g_200cd40@0" ObjectIDZND0="37490@x" ObjectIDZND1="27106@x" Pin0InfoVect0LinkObjId="SW-225630_0" Pin0InfoVect1LinkObjId="SW-169478_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13fc3c0_0" Pin1InfoVect1LinkObjId="SW-225671_0" Pin1InfoVect2LinkObjId="g_200cd40_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1232 82,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1400670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-40,-1165 -40,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13fdc80@0" ObjectIDZND0="g_13ffac0@0" Pin0InfoVect0LinkObjId="g_13ffac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13fdc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-40,-1165 -40,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1401370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-40,-1225 -40,-1232 -15,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13ffac0@1" ObjectIDZND0="g_13fc3c0@1" Pin0InfoVect0LinkObjId="g_13fc3c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13ffac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-40,-1225 -40,-1232 -15,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1401560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="17,-1232 82,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13fc3c0@0" ObjectIDZND0="37490@x" ObjectIDZND1="27106@x" ObjectIDZND2="37487@x" Pin0InfoVect0LinkObjId="SW-225630_0" Pin0InfoVect1LinkObjId="SW-169478_0" Pin0InfoVect2LinkObjId="SW-225671_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13fc3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="17,-1232 82,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_200bf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1261 72,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_13fc3c0@0" ObjectIDND1="37490@x" ObjectIDND2="27106@x" ObjectIDZND0="37487@1" Pin0InfoVect0LinkObjId="SW-225671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13fc3c0_0" Pin1InfoVect1LinkObjId="SW-225630_0" Pin1InfoVect2LinkObjId="SW-169478_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1261 72,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_200c160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1232 82,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_13fc3c0@0" ObjectIDND1="37490@x" ObjectIDND2="27106@x" ObjectIDZND0="37487@x" ObjectIDZND1="g_200cd40@0" ObjectIDZND2="37848@1" Pin0InfoVect0LinkObjId="SW-225671_0" Pin0InfoVect1LinkObjId="g_200cd40_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13fc3c0_0" Pin1InfoVect1LinkObjId="SW-225630_0" Pin1InfoVect2LinkObjId="SW-169478_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1232 82,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_200c3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="36,-1261 24,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="37487@0" ObjectIDZND0="g_200e790@0" Pin0InfoVect0LinkObjId="g_200e790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="36,-1261 24,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_200c620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1278 102,-1278 102,-1266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37487@x" ObjectIDND1="g_13fc3c0@0" ObjectIDND2="37490@x" ObjectIDZND0="g_200cd40@0" Pin0InfoVect0LinkObjId="g_200cd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225671_0" Pin1InfoVect1LinkObjId="g_13fc3c0_0" Pin1InfoVect2LinkObjId="SW-225630_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1278 102,-1278 102,-1266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_200c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1295 82,-1278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="37848@1" ObjectIDZND0="g_200cd40@0" ObjectIDZND1="37487@x" ObjectIDZND2="g_13fc3c0@0" Pin0InfoVect0LinkObjId="g_200cd40_0" Pin0InfoVect1LinkObjId="SW-225671_0" Pin0InfoVect2LinkObjId="g_13fc3c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1295 82,-1278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_200cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1278 82,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_200cd40@0" ObjectIDND1="37848@1" ObjectIDZND0="37487@x" ObjectIDZND1="g_13fc3c0@0" ObjectIDZND2="37490@x" Pin0InfoVect0LinkObjId="SW-225671_0" Pin0InfoVect1LinkObjId="g_13fc3c0_0" Pin0InfoVect2LinkObjId="SW-225630_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_200cd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1278 82,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2bd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1016 300,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27088@0" ObjectIDZND0="37492@0" Pin0InfoVect0LinkObjId="SW-225698_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfe790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1016 300,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2ef50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="235,-1080 252,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c2e4c0@0" ObjectIDZND0="37494@0" Pin0InfoVect0LinkObjId="SW-225699_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c2e4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="235,-1080 252,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-1080 300,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="37494@1" ObjectIDZND0="37492@x" ObjectIDZND1="37498@x" Pin0InfoVect0LinkObjId="SW-225698_0" Pin0InfoVect1LinkObjId="SW-225697_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225699_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="288,-1080 300,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c323d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1137 251,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c31940@0" ObjectIDZND0="37495@0" Pin0InfoVect0LinkObjId="SW-225701_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c31940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1137 251,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c32630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-1137 300,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="37495@1" ObjectIDZND0="37498@x" ObjectIDZND1="37491@x" Pin0InfoVect0LinkObjId="SW-225697_0" Pin0InfoVect1LinkObjId="SW-225700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="287,-1137 300,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1200 251,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20e3a10@0" ObjectIDZND0="37496@0" Pin0InfoVect0LinkObjId="SW-225702_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e3a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1200 251,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-1200 300,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="37496@1" ObjectIDZND0="37491@x" ObjectIDZND1="g_1cda8b0@0" ObjectIDZND2="38092@1" Pin0InfoVect0LinkObjId="SW-225700_0" Pin0InfoVect1LinkObjId="g_1cda8b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225702_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="287,-1200 300,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e6e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1066 300,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="37492@1" ObjectIDZND0="37494@x" ObjectIDZND1="37498@x" Pin0InfoVect0LinkObjId="SW-225699_0" Pin0InfoVect1LinkObjId="SW-225697_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225698_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1066 300,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e70f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1095 300,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="37498@0" ObjectIDZND0="37494@x" ObjectIDZND1="37492@x" Pin0InfoVect0LinkObjId="SW-225699_0" Pin0InfoVect1LinkObjId="SW-225698_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1095 300,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e7350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1122 300,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="37498@1" ObjectIDZND0="37495@x" ObjectIDZND1="37491@x" Pin0InfoVect0LinkObjId="SW-225701_0" Pin0InfoVect1LinkObjId="SW-225700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1122 300,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e75b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1151 300,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="37491@0" ObjectIDZND0="37495@x" ObjectIDZND1="37498@x" Pin0InfoVect0LinkObjId="SW-225701_0" Pin0InfoVect1LinkObjId="SW-225697_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1151 300,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e7810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1187 300,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="37491@1" ObjectIDZND0="37496@x" ObjectIDZND1="g_1cda8b0@0" ObjectIDZND2="38092@1" Pin0InfoVect0LinkObjId="SW-225702_0" Pin0InfoVect1LinkObjId="g_1cda8b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1187 300,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e9fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1261 290,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1cda8b0@0" ObjectIDND1="38092@1" ObjectIDND2="37496@x" ObjectIDZND0="37493@1" Pin0InfoVect0LinkObjId="SW-225703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cda8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="SW-225702_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1261 290,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ea200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="254,-1261 242,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="37493@0" ObjectIDZND0="g_1cdc210@0" Pin0InfoVect0LinkObjId="g_1cdc210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="254,-1261 242,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ea460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1278 320,-1278 320,-1266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37493@x" ObjectIDND1="37496@x" ObjectIDND2="37491@x" ObjectIDZND0="g_1cda8b0@0" Pin0InfoVect0LinkObjId="g_1cda8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225703_0" Pin1InfoVect1LinkObjId="SW-225702_0" Pin1InfoVect2LinkObjId="SW-225700_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1278 320,-1278 320,-1266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1295 300,-1278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38092@1" ObjectIDZND0="g_1cda8b0@0" ObjectIDZND1="37493@x" ObjectIDZND2="37496@x" Pin0InfoVect0LinkObjId="g_1cda8b0_0" Pin0InfoVect1LinkObjId="SW-225703_0" Pin0InfoVect2LinkObjId="SW-225702_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1295 300,-1278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cda650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1278 300,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1cda8b0@0" ObjectIDND1="38092@1" ObjectIDZND0="37493@x" ObjectIDZND1="37496@x" ObjectIDZND2="37491@x" Pin0InfoVect0LinkObjId="SW-225703_0" Pin0InfoVect1LinkObjId="SW-225702_0" Pin0InfoVect2LinkObjId="SW-225700_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cda8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1278 300,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cdca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1200 300,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="37496@x" ObjectIDND1="37491@x" ObjectIDZND0="g_1cda8b0@0" ObjectIDZND1="38092@1" ObjectIDZND2="37493@x" Pin0InfoVect0LinkObjId="g_1cda8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-225703_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225702_0" Pin1InfoVect1LinkObjId="SW-225700_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1200 300,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1698e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-1016 496,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27088@0" ObjectIDZND0="37500@0" Pin0InfoVect0LinkObjId="SW-225730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfe790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="496,-1016 496,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16990c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-1027 732,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37502@0" ObjectIDZND0="37565@0" Pin0InfoVect0LinkObjId="g_16a9480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-1027 732,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1699bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-1063 496,-1077 555,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="37500@1" ObjectIDZND0="37499@x" ObjectIDZND1="37503@x" Pin0InfoVect0LinkObjId="SW-225731_0" Pin0InfoVect1LinkObjId="SW-225729_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="496,-1063 496,-1077 555,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-1077 558,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="37500@x" ObjectIDND1="37503@x" ObjectIDZND0="37499@1" Pin0InfoVect0LinkObjId="SW-225731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225730_0" Pin1InfoVect1LinkObjId="SW-225729_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-1077 558,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-1032 558,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37499@0" ObjectIDZND0="g_1699e10@0" Pin0InfoVect0LinkObjId="g_1699e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-1032 558,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb4f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="659,-1077 659,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="37502@x" ObjectIDND1="37503@x" ObjectIDZND0="37501@1" Pin0InfoVect0LinkObjId="SW-225733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225732_0" Pin1InfoVect1LinkObjId="SW-225729_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="659,-1077 659,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb51f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="659,-1032 659,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37501@0" ObjectIDZND0="g_1bb4540@0" Pin0InfoVect0LinkObjId="g_1bb4540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="659,-1032 659,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb5ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="659,-1077 732,-1077 732,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="37501@x" ObjectIDND1="37503@x" ObjectIDZND0="37502@1" Pin0InfoVect0LinkObjId="SW-225732_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225733_0" Pin1InfoVect1LinkObjId="SW-225729_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="659,-1077 732,-1077 732,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb7d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="555,-1077 585,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="37500@x" ObjectIDND1="37499@x" ObjectIDZND0="37503@1" Pin0InfoVect0LinkObjId="SW-225729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225730_0" Pin1InfoVect1LinkObjId="SW-225731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="555,-1077 585,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb7fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-1077 659,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="37503@0" ObjectIDZND0="37502@x" ObjectIDZND1="37501@x" Pin0InfoVect0LinkObjId="SW-225732_0" Pin0InfoVect1LinkObjId="SW-225733_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="612,-1077 659,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbb9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1016 937,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="37565@0" ObjectIDZND0="27102@x" ObjectIDZND1="27101@x" Pin0InfoVect0LinkObjId="SW-169474_0" Pin0InfoVect1LinkObjId="SW-225752_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16990c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1016 937,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf4c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1032 950,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="37565@0" ObjectIDND1="27101@x" ObjectIDZND0="27102@0" Pin0InfoVect0LinkObjId="SW-169474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16990c0_0" Pin1InfoVect1LinkObjId="SW-225752_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1032 950,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf4eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="986,-1032 1000,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27102@1" ObjectIDZND0="g_1bbbc40@0" Pin0InfoVect0LinkObjId="g_1bbbc40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="986,-1032 1000,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf5110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1032 937,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="37565@0" ObjectIDND1="27102@x" ObjectIDZND0="27101@0" Pin0InfoVect0LinkObjId="SW-225752_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16990c0_0" Pin1InfoVect1LinkObjId="SW-169474_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1032 937,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf5da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1107 950,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="27101@x" ObjectIDND1="g_1bf9480@0" ObjectIDZND0="27103@0" Pin0InfoVect0LinkObjId="SW-225753_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225752_0" Pin1InfoVect1LinkObjId="g_1bf9480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1107 950,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf6000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="986,-1107 1000,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27103@1" ObjectIDZND0="g_1bf5370@0" Pin0InfoVect0LinkObjId="g_1bf5370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225753_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="986,-1107 1000,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf8fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1085 937,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="27101@1" ObjectIDZND0="27103@x" ObjectIDZND1="g_1bf9480@0" Pin0InfoVect0LinkObjId="SW-225753_0" Pin0InfoVect1LinkObjId="g_1bf9480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225752_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1085 937,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf9220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1107 937,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="27103@x" ObjectIDND1="27101@x" ObjectIDZND0="g_1bf9480@0" Pin0InfoVect0LinkObjId="g_1bf9480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225753_0" Pin1InfoVect1LinkObjId="SW-225752_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1107 937,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfe530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-134,-987 -117,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bfdcd0@0" ObjectIDZND0="27100@0" Pin0InfoVect0LinkObjId="SW-169424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfdcd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-134,-987 -117,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfe790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-81,-987 -69,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="27100@1" ObjectIDZND0="27088@0" ObjectIDZND1="27099@x" Pin0InfoVect0LinkObjId="g_1cfbcb0_0" Pin0InfoVect1LinkObjId="SW-169473_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169424_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-81,-987 -69,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc7950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-69,-1016 -69,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27088@0" ObjectIDZND0="27100@x" ObjectIDZND1="27099@x" Pin0InfoVect0LinkObjId="SW-169424_0" Pin0InfoVect1LinkObjId="SW-169473_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfe790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-69,-1016 -69,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bca3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-69,-987 -69,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="27100@x" ObjectIDND1="27088@0" ObjectIDZND0="27099@1" Pin0InfoVect0LinkObjId="SW-169473_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-169424_0" Pin1InfoVect1LinkObjId="g_1bfe790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-69,-987 -69,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcb0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-134,-928 -117,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bca610@0" ObjectIDZND0="37505@0" Pin0InfoVect0LinkObjId="SW-169475_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bca610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-134,-928 -117,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfbcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-998 238,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27092@1" ObjectIDZND0="27088@0" Pin0InfoVect0LinkObjId="g_1bfe790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-998 238,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfc9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="173,-945 190,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1cfbf10@0" ObjectIDZND0="37504@0" Pin0InfoVect0LinkObjId="SW-225634_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cfbf10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="173,-945 190,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfcc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="226,-945 238,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="37504@1" ObjectIDZND0="27092@x" ObjectIDZND1="27091@x" Pin0InfoVect0LinkObjId="SW-169320_0" Pin0InfoVect1LinkObjId="SW-169318_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225634_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="226,-945 238,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cffc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-945 238,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="37504@x" ObjectIDND1="27091@x" ObjectIDZND0="27092@0" Pin0InfoVect0LinkObjId="SW-169320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225634_0" Pin1InfoVect1LinkObjId="SW-169318_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-945 238,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cffe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-930 238,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27091@1" ObjectIDZND0="37504@x" ObjectIDZND1="27092@x" Pin0InfoVect0LinkObjId="SW-225634_0" Pin0InfoVect1LinkObjId="SW-169320_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="238,-930 238,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d02060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-890 238,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="27132@0" ObjectIDZND0="27091@0" Pin0InfoVect0LinkObjId="SW-169318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d04910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-890 238,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d022c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-787 212,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27132@x" ObjectIDND1="g_1d04dd0@0" ObjectIDZND0="g_1d02520@0" Pin0InfoVect0LinkObjId="g_1d02520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d04910_0" Pin1InfoVect1LinkObjId="g_1d04dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-787 212,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d04910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-787 238,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1d02520@0" ObjectIDND1="g_1d04dd0@0" ObjectIDZND0="27132@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d02520_0" Pin1InfoVect1LinkObjId="g_1d04dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-787 238,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d04b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="212,-722 238,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d032d0@0" ObjectIDZND0="g_1d04dd0@0" ObjectIDZND1="37517@x" Pin0InfoVect0LinkObjId="g_1d04dd0_0" Pin0InfoVect1LinkObjId="SW-169343_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d032d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="212,-722 238,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d059f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-775 238,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_1d04dd0@1" ObjectIDZND0="g_1d02520@0" ObjectIDZND1="27132@x" Pin0InfoVect0LinkObjId="g_1d02520_0" Pin0InfoVect1LinkObjId="g_1d04910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d04dd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="238,-775 238,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d064e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-722 238,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d032d0@0" ObjectIDND1="37517@x" ObjectIDZND0="g_1d04dd0@0" Pin0InfoVect0LinkObjId="g_1d04dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d032d0_0" Pin1InfoVect1LinkObjId="SW-169343_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-722 238,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a9480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-1004 863,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27096@1" ObjectIDZND0="37565@0" Pin0InfoVect0LinkObjId="g_16990c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-1004 863,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16aa170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-950 815,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_16a96e0@0" ObjectIDZND0="27097@0" Pin0InfoVect0LinkObjId="SW-169382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a96e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="798,-950 815,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16aa3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-950 863,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27097@1" ObjectIDZND0="27095@x" ObjectIDZND1="27096@x" Pin0InfoVect0LinkObjId="SW-169379_0" Pin0InfoVect1LinkObjId="SW-169381_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="851,-950 863,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16acb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-950 863,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27095@x" ObjectIDND1="27097@x" ObjectIDZND0="27096@0" Pin0InfoVect0LinkObjId="SW-169381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-169379_0" Pin1InfoVect1LinkObjId="SW-169382_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-950 863,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16acdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-935 863,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27095@1" ObjectIDZND0="27096@x" ObjectIDZND1="27097@x" Pin0InfoVect0LinkObjId="SW-169381_0" Pin0InfoVect1LinkObjId="SW-169382_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="863,-935 863,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16aefa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-895 863,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="27133@0" ObjectIDZND0="27095@0" Pin0InfoVect0LinkObjId="SW-169379_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16b0fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-895 863,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16af200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-791 837,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_16b1220@0" ObjectIDND1="27133@x" ObjectIDZND0="g_16af460@0" Pin0InfoVect0LinkObjId="g_16af460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16b1220_0" Pin1InfoVect1LinkObjId="g_16b0fc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-791 837,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b0fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-791 863,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_16b1220@0" ObjectIDND1="g_16af460@0" ObjectIDZND0="27133@1" Pin0InfoVect0LinkObjId="g_16b1e40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16b1220_0" Pin1InfoVect1LinkObjId="g_16af460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-791 863,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b1e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-780 863,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_16b1220@1" ObjectIDZND0="27133@x" ObjectIDZND1="g_16af460@0" Pin0InfoVect0LinkObjId="g_16b0fc0_0" Pin0InfoVect1LinkObjId="g_16af460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16b1220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="863,-780 863,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b2300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-741 863,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_16b1220@0" ObjectIDZND0="g_16b0210@0" ObjectIDZND1="37519@x" Pin0InfoVect0LinkObjId="g_16b0210_0" Pin0InfoVect1LinkObjId="SW-169423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16b1220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="863,-741 863,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16b2560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-727 837,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_16b1220@0" ObjectIDND1="37519@x" ObjectIDZND0="g_16b0210@0" Pin0InfoVect0LinkObjId="g_16b0210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16b1220_0" Pin1InfoVect1LinkObjId="SW-169423_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-727 837,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d07c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-265,-472 -237,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d071c0@0" ObjectIDZND0="37554@0" Pin0InfoVect0LinkObjId="SW-225755_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d071c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-265,-472 -237,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d07eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-201,-472 -188,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37554@1" ObjectIDZND0="g_16b2e80@0" ObjectIDZND1="g_1d08110@0" ObjectIDZND2="37527@x" Pin0InfoVect0LinkObjId="g_16b2e80_0" Pin0InfoVect1LinkObjId="g_1d08110_0" Pin0InfoVect2LinkObjId="SW-225754_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225755_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-201,-472 -188,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d08d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-399 -188,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37550@1" ObjectIDZND0="g_1d08110@0" Pin0InfoVect0LinkObjId="g_1d08110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226135_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-399 -188,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d15610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-350 -188,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="37567@0" ObjectIDZND0="37550@0" Pin0InfoVect0LinkObjId="SW-226135_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_JB.YM_JB_cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-350 -188,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-55,-417 -55,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c6c820@0" ObjectIDZND0="g_1c6f080@0" Pin0InfoVect0LinkObjId="g_1c6f080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c6c820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-55,-417 -55,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-79,-422 -79,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27110@0" ObjectIDZND0="g_1c6d900@0" Pin0InfoVect0LinkObjId="g_1c6d900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-79,-422 -79,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c74520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-467 54,-467 54,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c73900@0" ObjectIDND1="g_1c75430@0" ObjectIDND2="37531@x" ObjectIDZND0="37563@1" Pin0InfoVect0LinkObjId="SW-226138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c73900_0" Pin1InfoVect1LinkObjId="g_1c75430_0" Pin1InfoVect2LinkObjId="SW-225790_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-467 54,-467 54,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c74780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="54,-422 54,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37563@0" ObjectIDZND0="g_1c749e0@0" Pin0InfoVect0LinkObjId="g_1c749e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="54,-422 54,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0e510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-417 78,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c73900@0" ObjectIDZND0="37564@1" Pin0InfoVect0LinkObjId="SW-226139_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c73900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-417 78,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0f4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-339 97,-339 97,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="37564@x" ObjectIDND1="34187@x" ObjectIDZND0="g_1c0e770@0" Pin0InfoVect0LinkObjId="g_1c0e770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226139_0" Pin1InfoVect1LinkObjId="EC-YM_JB.043Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-339 97,-339 97,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0ff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-351 78,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="37564@0" ObjectIDZND0="g_1c0e770@0" ObjectIDZND1="34187@x" Pin0InfoVect0LinkObjId="g_1c0e770_0" Pin0InfoVect1LinkObjId="EC-YM_JB.043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="78,-351 78,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c101f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-339 78,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="37564@x" ObjectIDND1="g_1c0e770@0" ObjectIDZND0="34187@0" Pin0InfoVect0LinkObjId="EC-YM_JB.043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226139_0" Pin1InfoVect1LinkObjId="g_1c0e770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-339 78,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c13fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-466 170,-466 170,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c133b0@0" ObjectIDND1="g_1c14ee0@0" ObjectIDND2="37533@x" ObjectIDZND0="37559@1" Pin0InfoVect0LinkObjId="SW-225812_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c133b0_0" Pin1InfoVect1LinkObjId="g_1c14ee0_0" Pin1InfoVect2LinkObjId="SW-225810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-466 170,-466 170,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c14230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="170,-421 170,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37559@0" ObjectIDZND0="g_1c14490@0" Pin0InfoVect0LinkObjId="g_1c14490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225812_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="170,-421 170,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c18410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-416 194,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c133b0@0" ObjectIDZND0="37560@1" Pin0InfoVect0LinkObjId="SW-226141_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c133b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-416 194,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c193a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-338 213,-338 213,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="37560@x" ObjectIDND1="34186@x" ObjectIDZND0="g_1c18670@0" Pin0InfoVect0LinkObjId="g_1c18670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226141_0" Pin1InfoVect1LinkObjId="EC-YM_JB.044Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-338 213,-338 213,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c19600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-350 194,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="37560@0" ObjectIDZND0="g_1c18670@0" ObjectIDZND1="34186@x" Pin0InfoVect0LinkObjId="g_1c18670_0" Pin0InfoVect1LinkObjId="EC-YM_JB.044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226141_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="194,-350 194,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c19860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-338 194,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="37560@x" ObjectIDND1="g_1c18670@0" ObjectIDZND0="34186@0" Pin0InfoVect0LinkObjId="EC-YM_JB.044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226141_0" Pin1InfoVect1LinkObjId="g_1c18670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-338 194,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c1f650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-394 319,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1c1d2b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_13fc3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c1d2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="319,-394 319,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c1f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="291,-399 291,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27111@0" ObjectIDZND0="g_1c1ded0@0" Pin0InfoVect0LinkObjId="g_1c1ded0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169484_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="291,-399 291,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c94a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="413,-391 413,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27119@0" ObjectIDZND0="g_1c932c0@0" Pin0InfoVect0LinkObjId="g_1c932c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169533_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="413,-391 413,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c95400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-438 413,-438 413,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_1c926a0@0" ObjectIDND1="g_1c93d10@0" ObjectIDND2="g_1c95660@0" ObjectIDZND0="27119@1" Pin0InfoVect0LinkObjId="SW-169533_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c926a0_0" Pin1InfoVect1LinkObjId="g_1c93d10_0" Pin1InfoVect2LinkObjId="g_1c95660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-438 413,-438 413,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c9bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-383 673,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1c99b90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_13fc3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c99b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-383 673,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c9c190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-388 645,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37562@0" ObjectIDZND0="g_1c9a7b0@0" Pin0InfoVect0LinkObjId="g_1c9a7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="645,-388 645,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ca2860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="831,-382 831,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1ca04c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_13fc3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ca04c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="831,-382 831,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ca2ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="803,-387 803,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37512@0" ObjectIDZND0="g_1ca10e0@0" Pin0InfoVect0LinkObjId="g_1ca10e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="803,-387 803,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fad4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-413 992,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fab110@0" ObjectIDZND0="37555@1" Pin0InfoVect0LinkObjId="SW-225879_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fab110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-413 992,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fad710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="964,-418 964,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37556@0" ObjectIDZND0="g_1fabd30@0" Pin0InfoVect0LinkObjId="g_1fabd30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="964,-418 964,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb0ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-332 1011,-332 1011,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="37555@x" ObjectIDND1="34185@x" ObjectIDZND0="g_1fb0170@0" Pin0InfoVect0LinkObjId="g_1fb0170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225879_0" Pin1InfoVect1LinkObjId="EC-YM_JB.049Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-332 1011,-332 1011,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb1100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-344 992,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="37555@0" ObjectIDZND0="g_1fb0170@0" ObjectIDZND1="34185@x" Pin0InfoVect0LinkObjId="g_1fb0170_0" Pin0InfoVect1LinkObjId="EC-YM_JB.049Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225879_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="992,-344 992,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb1360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-332 992,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1fb0170@0" ObjectIDND1="37555@x" ObjectIDZND0="34185@0" Pin0InfoVect0LinkObjId="EC-YM_JB.049Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fb0170_0" Pin1InfoVect1LinkObjId="SW-225879_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-332 992,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb68c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-412 1115,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fb4520@0" ObjectIDZND0="37552@1" Pin0InfoVect0LinkObjId="SW-225902_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb4520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-412 1115,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fb6b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1087,-417 1087,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37551@0" ObjectIDZND0="g_1fb5140@0" Pin0InfoVect0LinkObjId="g_1fb5140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1087,-417 1087,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fba2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-331 1134,-331 1134,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="37552@x" ObjectIDND1="34184@x" ObjectIDZND0="g_1fb9580@0" Pin0InfoVect0LinkObjId="g_1fb9580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225902_0" Pin1InfoVect1LinkObjId="EC-YM_JB.051Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-331 1134,-331 1134,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fba510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-343 1115,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="37552@0" ObjectIDZND0="g_1fb9580@0" ObjectIDZND1="34184@x" Pin0InfoVect0LinkObjId="g_1fb9580_0" Pin0InfoVect1LinkObjId="EC-YM_JB.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225902_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-343 1115,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fba770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-331 1115,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1fb9580@0" ObjectIDND1="37552@x" ObjectIDZND0="34184@0" Pin0InfoVect0LinkObjId="EC-YM_JB.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fb9580_0" Pin1InfoVect1LinkObjId="SW-225902_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-331 1115,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2096030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1267,-727 1267,-737 1301,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2095300@0" ObjectIDZND0="27117@0" Pin0InfoVect0LinkObjId="SW-225925_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2095300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1267,-727 1267,-737 1301,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2096290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1337,-737 1350,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27117@1" ObjectIDZND0="g_2094870@0" Pin0InfoVect0LinkObjId="g_2094870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225925_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1337,-737 1350,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2097110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-813 1291,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37558@1" ObjectIDZND0="g_20964f0@1" Pin0InfoVect0LinkObjId="g_20964f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225921_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-813 1291,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a39f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-862 1291,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="37568@0" ObjectIDZND0="37558@0" Pin0InfoVect0LinkObjId="SW-225921_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_JB.YM_JB_cb2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-862 1291,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1cc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-69,-928 -69,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1d1ce70@0" ObjectIDND1="37505@x" ObjectIDZND0="27099@0" Pin0InfoVect0LinkObjId="SW-169473_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d1ce70_0" Pin1InfoVect1LinkObjId="SW-169475_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-69,-928 -69,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d2fa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-749 1291,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20964f0@0" ObjectIDZND0="37544@0" Pin0InfoVect0LinkObjId="SW-225923_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20964f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-749 1291,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d2fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-693 1291,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37544@1" ObjectIDZND0="37542@1" Pin0InfoVect0LinkObjId="SW-225922_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225923_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-693 1291,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d2fec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-654 1291,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37542@0" ObjectIDZND0="37543@1" Pin0InfoVect0LinkObjId="SW-225923_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225922_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-654 1291,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d30120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-626 1291,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37543@0" ObjectIDZND0="37566@0" Pin0InfoVect0LinkObjId="g_1c467a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225923_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-626 1291,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be30d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-166,-472 -188,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_16b2e80@0" ObjectIDZND0="37554@x" ObjectIDZND1="g_1d08110@0" ObjectIDZND2="37527@x" Pin0InfoVect0LinkObjId="SW-225755_0" Pin0InfoVect1LinkObjId="g_1d08110_0" Pin0InfoVect2LinkObjId="SW-225754_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16b2e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-166,-472 -188,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be3330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-456 -188,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1d08110@1" ObjectIDZND0="37554@x" ObjectIDZND1="g_16b2e80@0" ObjectIDZND2="37527@x" Pin0InfoVect0LinkObjId="SW-225755_0" Pin0InfoVect1LinkObjId="g_16b2e80_0" Pin0InfoVect2LinkObjId="SW-225754_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d08110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-456 -188,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be4040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-55,-467 -79,-467 -79,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1c6c820@0" ObjectIDND1="g_1c6e350@0" ObjectIDND2="37529@x" ObjectIDZND0="27110@1" Pin0InfoVect0LinkObjId="SW-225777_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c6c820_0" Pin1InfoVect1LinkObjId="g_1c6e350_0" Pin1InfoVect2LinkObjId="SW-225776_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-55,-467 -79,-467 -79,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be42a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-55,-456 -55,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c6c820@1" ObjectIDZND0="27110@x" ObjectIDZND1="g_1c6e350@0" ObjectIDZND2="37529@x" Pin0InfoVect0LinkObjId="SW-225777_0" Pin0InfoVect1LinkObjId="g_1c6e350_0" Pin0InfoVect2LinkObjId="SW-225776_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c6c820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-55,-456 -55,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be4500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-33,-451 -33,-467 -55,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c6e350@0" ObjectIDZND0="27110@x" ObjectIDZND1="g_1c6c820@0" ObjectIDZND2="37529@x" Pin0InfoVect0LinkObjId="SW-225777_0" Pin0InfoVect1LinkObjId="g_1c6c820_0" Pin0InfoVect2LinkObjId="SW-225776_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c6e350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-33,-451 -33,-467 -55,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be5210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-467 78,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37563@x" ObjectIDND1="g_1c75430@0" ObjectIDND2="37531@x" ObjectIDZND0="g_1c73900@1" Pin0InfoVect0LinkObjId="g_1c73900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226138_0" Pin1InfoVect1LinkObjId="g_1c75430_0" Pin1InfoVect2LinkObjId="SW-225790_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-467 78,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be5f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-466 194,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37559@x" ObjectIDND1="g_1c14ee0@0" ObjectIDND2="37533@x" ObjectIDZND0="g_1c133b0@1" Pin0InfoVect0LinkObjId="g_1c133b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225812_0" Pin1InfoVect1LinkObjId="g_1c14ee0_0" Pin1InfoVect2LinkObjId="SW-225810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-466 194,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be6180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="100,-451 100,-467 78,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c75430@0" ObjectIDZND0="g_1c73900@0" ObjectIDZND1="37563@x" ObjectIDZND2="37531@x" Pin0InfoVect0LinkObjId="g_1c73900_0" Pin0InfoVect1LinkObjId="SW-226138_0" Pin0InfoVect2LinkObjId="SW-225790_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c75430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="100,-451 100,-467 78,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be63e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="216,-450 216,-466 194,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c14ee0@0" ObjectIDZND0="37559@x" ObjectIDZND1="g_1c133b0@0" ObjectIDZND2="37533@x" Pin0InfoVect0LinkObjId="SW-225812_0" Pin0InfoVect1LinkObjId="g_1c133b0_0" Pin0InfoVect2LinkObjId="SW-225810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c14ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="216,-450 216,-466 194,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be6640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="291,-435 291,-441 319,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27111@1" ObjectIDZND0="g_1c1d2b0@0" ObjectIDZND1="g_1c1e920@0" ObjectIDZND2="37522@x" Pin0InfoVect0LinkObjId="g_1c1d2b0_0" Pin0InfoVect1LinkObjId="g_1c1e920_0" Pin0InfoVect2LinkObjId="SW-169485_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169484_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="291,-435 291,-441 319,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be7350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-441 319,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="27111@x" ObjectIDND1="g_1c1e920@0" ObjectIDND2="37522@x" ObjectIDZND0="g_1c1d2b0@1" Pin0InfoVect0LinkObjId="g_1c1d2b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-169484_0" Pin1InfoVect1LinkObjId="g_1c1e920_0" Pin1InfoVect2LinkObjId="SW-169485_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="319,-441 319,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be75b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-428 341,-441 319,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c1e920@0" ObjectIDZND0="27111@x" ObjectIDZND1="g_1c1d2b0@0" ObjectIDZND2="37522@x" Pin0InfoVect0LinkObjId="SW-169484_0" Pin0InfoVect1LinkObjId="g_1c1d2b0_0" Pin0InfoVect2LinkObjId="SW-169485_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c1e920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="341,-428 341,-441 319,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be82c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-438 441,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="27119@x" ObjectIDND1="g_1c93d10@0" ObjectIDND2="g_1c95660@0" ObjectIDZND0="g_1c926a0@1" Pin0InfoVect0LinkObjId="g_1c926a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-169533_0" Pin1InfoVect1LinkObjId="g_1c93d10_0" Pin1InfoVect2LinkObjId="g_1c95660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-438 441,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be8520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-438 441,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c93d10@0" ObjectIDND1="g_1c95660@0" ObjectIDZND0="27119@x" ObjectIDZND1="g_1c926a0@0" ObjectIDZND2="37524@x" Pin0InfoVect0LinkObjId="SW-169533_0" Pin0InfoVect1LinkObjId="g_1c926a0_0" Pin0InfoVect2LinkObjId="SW-169534_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c93d10_0" Pin1InfoVect1LinkObjId="g_1c95660_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="463,-438 441,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be9010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-420 463,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c93d10@0" ObjectIDZND0="27119@x" ObjectIDZND1="g_1c926a0@0" ObjectIDZND2="37524@x" Pin0InfoVect0LinkObjId="SW-169533_0" Pin0InfoVect1LinkObjId="g_1c926a0_0" Pin0InfoVect2LinkObjId="SW-169534_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c93d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="463,-420 463,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be9270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-438 489,-438 489,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="27119@x" ObjectIDND1="g_1c926a0@0" ObjectIDND2="37524@x" ObjectIDZND0="g_1c95660@0" Pin0InfoVect0LinkObjId="g_1c95660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-169533_0" Pin1InfoVect1LinkObjId="g_1c926a0_0" Pin1InfoVect2LinkObjId="SW-169534_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,-438 489,-438 489,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be94d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-424 645,-436 673,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="37562@1" ObjectIDZND0="g_1c99b90@0" ObjectIDZND1="g_1c9caf0@0" ObjectIDZND2="g_1c9b200@0" Pin0InfoVect0LinkObjId="g_1c99b90_0" Pin0InfoVect1LinkObjId="g_1c9caf0_0" Pin0InfoVect2LinkObjId="g_1c9b200_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="645,-424 645,-436 673,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bea1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-436 673,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="37562@x" ObjectIDND1="g_1c9caf0@0" ObjectIDND2="g_1c9b200@0" ObjectIDZND0="g_1c99b90@1" Pin0InfoVect0LinkObjId="g_1c99b90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225834_0" Pin1InfoVect1LinkObjId="g_1c9caf0_0" Pin1InfoVect2LinkObjId="g_1c9b200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-436 673,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bea440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-436 673,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1c9caf0@0" ObjectIDND1="g_1c9b200@0" ObjectIDZND0="37562@x" ObjectIDZND1="g_1c99b90@0" ObjectIDZND2="37535@x" Pin0InfoVect0LinkObjId="SW-225834_0" Pin0InfoVect1LinkObjId="g_1c99b90_0" Pin0InfoVect2LinkObjId="SW-225832_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c9caf0_0" Pin1InfoVect1LinkObjId="g_1c9b200_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="695,-436 673,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beaf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-420 721,-436 695,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c9caf0@0" ObjectIDZND0="g_1c9b200@0" ObjectIDZND1="37562@x" ObjectIDZND2="g_1c99b90@0" Pin0InfoVect0LinkObjId="g_1c9b200_0" Pin0InfoVect1LinkObjId="SW-225834_0" Pin0InfoVect2LinkObjId="g_1c99b90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c9caf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="721,-420 721,-436 695,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beb190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-436 695,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c9caf0@0" ObjectIDND1="37562@x" ObjectIDND2="g_1c99b90@0" ObjectIDZND0="g_1c9b200@0" Pin0InfoVect0LinkObjId="g_1c9b200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c9caf0_0" Pin1InfoVect1LinkObjId="SW-225834_0" Pin1InfoVect2LinkObjId="g_1c99b90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,-436 695,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beb3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="803,-423 803,-437 831,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37512@1" ObjectIDZND0="g_1ca04c0@0" ObjectIDZND1="g_1ca1b30@0" ObjectIDZND2="37537@x" Pin0InfoVect0LinkObjId="g_1ca04c0_0" Pin0InfoVect1LinkObjId="g_1ca1b30_0" Pin0InfoVect2LinkObjId="SW-225854_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="803,-423 803,-437 831,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bec100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="831,-437 831,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37512@x" ObjectIDND1="g_1ca1b30@0" ObjectIDND2="37537@x" ObjectIDZND0="g_1ca04c0@1" Pin0InfoVect0LinkObjId="g_1ca04c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225856_0" Pin1InfoVect1LinkObjId="g_1ca1b30_0" Pin1InfoVect2LinkObjId="SW-225854_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="831,-437 831,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bec360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="853,-416 853,-437 831,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1ca1b30@0" ObjectIDZND0="37512@x" ObjectIDZND1="g_1ca04c0@0" ObjectIDZND2="37537@x" Pin0InfoVect0LinkObjId="SW-225856_0" Pin0InfoVect1LinkObjId="g_1ca04c0_0" Pin0InfoVect2LinkObjId="SW-225854_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ca1b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="853,-416 853,-437 831,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bec5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="964,-454 964,-468 992,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37556@1" ObjectIDZND0="g_1fab110@0" ObjectIDZND1="g_1fac780@0" ObjectIDZND2="37539@x" Pin0InfoVect0LinkObjId="g_1fab110_0" Pin0InfoVect1LinkObjId="g_1fac780_0" Pin0InfoVect2LinkObjId="SW-225876_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225878_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="964,-454 964,-468 992,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bed2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-468 992,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37556@x" ObjectIDND1="g_1fac780@0" ObjectIDND2="37539@x" ObjectIDZND0="g_1fab110@1" Pin0InfoVect0LinkObjId="g_1fab110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225878_0" Pin1InfoVect1LinkObjId="g_1fac780_0" Pin1InfoVect2LinkObjId="SW-225876_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-468 992,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bed530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1014,-447 1014,-468 992,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fac780@0" ObjectIDZND0="37556@x" ObjectIDZND1="g_1fab110@0" ObjectIDZND2="37539@x" Pin0InfoVect0LinkObjId="SW-225878_0" Pin0InfoVect1LinkObjId="g_1fab110_0" Pin0InfoVect2LinkObjId="SW-225876_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fac780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1014,-447 1014,-468 992,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bee240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-466 1115,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37551@x" ObjectIDND1="g_1fb5b90@0" ObjectIDND2="37541@x" ObjectIDZND0="g_1fb4520@1" Pin0InfoVect0LinkObjId="g_1fb4520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225901_0" Pin1InfoVect1LinkObjId="g_1fb5b90_0" Pin1InfoVect2LinkObjId="SW-225899_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-466 1115,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bee4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-466 1087,-466 1087,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1fb4520@0" ObjectIDND1="g_1fb5b90@0" ObjectIDND2="37541@x" ObjectIDZND0="37551@1" Pin0InfoVect0LinkObjId="SW-225901_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fb4520_0" Pin1InfoVect1LinkObjId="g_1fb5b90_0" Pin1InfoVect2LinkObjId="SW-225899_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-466 1087,-466 1087,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bee700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1137,-446 1137,-466 1115,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1fb5b90@0" ObjectIDZND0="g_1fb4520@0" ObjectIDZND1="37551@x" ObjectIDZND2="37541@x" Pin0InfoVect0LinkObjId="g_1fb4520_0" Pin0InfoVect1LinkObjId="SW-225901_0" Pin0InfoVect2LinkObjId="SW-225899_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb5b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1137,-446 1137,-466 1115,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bee960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-580 -188,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37526@0" ObjectIDZND0="27089@0" Pin0InfoVect0LinkObjId="g_1bef2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-580 -188,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-472 -188,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37554@x" ObjectIDND1="g_16b2e80@0" ObjectIDND2="g_1d08110@0" ObjectIDZND0="37527@0" Pin0InfoVect0LinkObjId="SW-225754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225755_0" Pin1InfoVect1LinkObjId="g_16b2e80_0" Pin1InfoVect2LinkObjId="g_1d08110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-472 -188,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-513 -188,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37527@1" ObjectIDZND0="37511@0" Pin0InfoVect0LinkObjId="SW-226134_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225754_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-513 -188,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bef080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-188,-551 -188,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37511@1" ObjectIDZND0="37526@1" Pin0InfoVect0LinkObjId="SW-225754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226134_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-188,-551 -188,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bef2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-626 474,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37547@0" ObjectIDZND0="27089@0" Pin0InfoVect0LinkObjId="g_1bee960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-626 474,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="547,-716 474,-716 474,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="37545@0" ObjectIDZND0="37548@0" Pin0InfoVect0LinkObjId="SW-225949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="547,-716 474,-716 474,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-606 702,-716 630,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="37566@0" ObjectIDZND0="37546@1" Pin0InfoVect0LinkObjId="SW-225947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d30120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-606 702,-716 630,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201df30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="613,-716 603,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37546@0" ObjectIDZND0="37506@0" Pin0InfoVect0LinkObjId="SW-225946_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="613,-716 603,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-716 564,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37506@1" ObjectIDZND0="37545@1" Pin0InfoVect0LinkObjId="SW-225947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225946_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-716 564,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202ebb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-606 863,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="37566@0" ObjectIDZND0="37520@0" Pin0InfoVect0LinkObjId="SW-169423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d30120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-606 863,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-641 863,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37520@1" ObjectIDZND0="27098@0" Pin0InfoVect0LinkObjId="SW-169421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-641 863,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202f070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-679 863,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27098@1" ObjectIDZND0="37519@1" Pin0InfoVect0LinkObjId="SW-169423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169421_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-679 863,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-708 863,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="37519@0" ObjectIDZND0="g_16b0210@0" ObjectIDZND1="g_16b1220@0" Pin0InfoVect0LinkObjId="g_16b0210_0" Pin0InfoVect1LinkObjId="g_16b1220_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="863,-708 863,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-606 238,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27089@0" ObjectIDZND0="37518@0" Pin0InfoVect0LinkObjId="SW-169343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bee960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-606 238,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202f790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-640 238,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37518@1" ObjectIDZND0="27093@0" Pin0InfoVect0LinkObjId="SW-169341_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-640 238,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-678 238,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27093@1" ObjectIDZND0="37517@1" Pin0InfoVect0LinkObjId="SW-169343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169341_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="238,-678 238,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_202fc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-707 238,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="37517@0" ObjectIDZND0="g_1d032d0@0" ObjectIDZND1="g_1d04dd0@0" Pin0InfoVect0LinkObjId="g_1d032d0_0" Pin0InfoVect1LinkObjId="g_1d04dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="238,-707 238,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2037040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-134,-606 -134,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27089@0" ObjectIDZND0="27107@0" Pin0InfoVect0LinkObjId="SW-225962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bee960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-134,-606 -134,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20372a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-134,-692 -134,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="27107@1" ObjectIDZND0="g_1d16770@0" Pin0InfoVect0LinkObjId="g_1d16770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-134,-692 -134,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2072d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,-606 1159,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="37566@0" ObjectIDZND0="27094@0" Pin0InfoVect0LinkObjId="SW-225972_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d30120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,-606 1159,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2072f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,-699 1159,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="27094@1" ObjectIDZND0="g_1fbb130@0" Pin0InfoVect0LinkObjId="g_1fbb130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225972_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,-699 1159,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_207bcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-55,-606 -55,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27089@0" ObjectIDZND0="37528@0" Pin0InfoVect0LinkObjId="SW-225776_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bee960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-55,-606 -55,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_207bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-55,-562 -55,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37528@1" ObjectIDZND0="37510@1" Pin0InfoVect0LinkObjId="SW-226136_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225776_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-55,-562 -55,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_207c180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-55,-523 -55,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37510@0" ObjectIDZND0="37529@1" Pin0InfoVect0LinkObjId="SW-225776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-55,-523 -55,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_207c3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-55,-495 -55,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="37529@0" ObjectIDZND0="27110@x" ObjectIDZND1="g_1c6c820@0" ObjectIDZND2="g_1c6e350@0" Pin0InfoVect0LinkObjId="SW-225777_0" Pin0InfoVect1LinkObjId="g_1c6c820_0" Pin0InfoVect2LinkObjId="g_1c6e350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-55,-495 -55,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2084aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-554 78,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37561@1" ObjectIDZND0="37530@1" Pin0InfoVect0LinkObjId="SW-225790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-554 78,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2084d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-583 78,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37530@0" ObjectIDZND0="27089@0" Pin0InfoVect0LinkObjId="g_1bee960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-583 78,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2084f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-467 78,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c73900@0" ObjectIDND1="37563@x" ObjectIDND2="g_1c75430@0" ObjectIDZND0="37531@0" Pin0InfoVect0LinkObjId="SW-225790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c73900_0" Pin1InfoVect1LinkObjId="SW-226138_0" Pin1InfoVect2LinkObjId="g_1c75430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-467 78,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20851c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="78,-516 78,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37531@1" ObjectIDZND0="37561@0" Pin0InfoVect0LinkObjId="SW-226137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="78,-516 78,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c43180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-553 194,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37557@1" ObjectIDZND0="37532@1" Pin0InfoVect0LinkObjId="SW-225810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-553 194,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c433e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-582 194,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37532@0" ObjectIDZND0="27089@0" Pin0InfoVect0LinkObjId="g_1bee960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-582 194,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c43640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-466 194,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37559@x" ObjectIDND1="g_1c133b0@0" ObjectIDND2="g_1c14ee0@0" ObjectIDZND0="37533@0" Pin0InfoVect0LinkObjId="SW-225810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225812_0" Pin1InfoVect1LinkObjId="g_1c133b0_0" Pin1InfoVect2LinkObjId="g_1c14ee0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-466 194,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c438a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-515 194,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37533@1" ObjectIDZND0="37557@0" Pin0InfoVect0LinkObjId="SW-226140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="194,-515 194,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c43b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-580 319,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37521@0" ObjectIDZND0="27089@0" Pin0InfoVect0LinkObjId="g_1bee960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="319,-580 319,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c43d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-441 319,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27111@x" ObjectIDND1="g_1c1d2b0@0" ObjectIDND2="g_1c1e920@0" ObjectIDZND0="37522@0" Pin0InfoVect0LinkObjId="SW-169485_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-169484_0" Pin1InfoVect1LinkObjId="g_1c1d2b0_0" Pin1InfoVect2LinkObjId="g_1c1e920_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="319,-441 319,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c43fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-513 319,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37522@1" ObjectIDZND0="27108@0" Pin0InfoVect0LinkObjId="SW-169480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169485_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="319,-513 319,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c44220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-551 319,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27108@1" ObjectIDZND0="37521@1" Pin0InfoVect0LinkObjId="SW-169485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="319,-551 319,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c44a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-499 441,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="37524@0" ObjectIDZND0="27119@x" ObjectIDZND1="g_1c926a0@0" ObjectIDZND2="g_1c93d10@0" Pin0InfoVect0LinkObjId="SW-169533_0" Pin0InfoVect1LinkObjId="g_1c926a0_0" Pin0InfoVect2LinkObjId="g_1c93d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169534_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="441,-499 441,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c44cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-527 441,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27116@0" ObjectIDZND0="37524@1" Pin0InfoVect0LinkObjId="SW-169534_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-527 441,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c44f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-606 441,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27089@0" ObjectIDZND0="37523@0" Pin0InfoVect0LinkObjId="SW-169534_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bee960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-606 441,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c45170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-566 441,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37523@1" ObjectIDZND0="27116@1" Pin0InfoVect0LinkObjId="SW-169529_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169534_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-566 441,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c46540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-554 673,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37508@1" ObjectIDZND0="37534@1" Pin0InfoVect0LinkObjId="SW-225832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-554 673,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c467a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-583 673,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37534@0" ObjectIDZND0="37566@0" Pin0InfoVect0LinkObjId="g_1d30120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225832_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-583 673,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c46a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-436 673,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="37562@x" ObjectIDND1="g_1c99b90@0" ObjectIDND2="g_1c9caf0@0" ObjectIDZND0="37535@0" Pin0InfoVect0LinkObjId="SW-225832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225834_0" Pin1InfoVect1LinkObjId="g_1c99b90_0" Pin1InfoVect2LinkObjId="g_1c9caf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-436 673,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c46c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-516 673,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37535@1" ObjectIDZND0="37508@0" Pin0InfoVect0LinkObjId="SW-225831_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225832_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-516 673,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c46ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="831,-581 831,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37536@0" ObjectIDZND0="37566@0" Pin0InfoVect0LinkObjId="g_1d30120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225854_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="831,-581 831,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c47120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="831,-437 831,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37512@x" ObjectIDND1="g_1ca04c0@0" ObjectIDND2="g_1ca1b30@0" ObjectIDZND0="37537@0" Pin0InfoVect0LinkObjId="SW-225854_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225856_0" Pin1InfoVect1LinkObjId="g_1ca04c0_0" Pin1InfoVect2LinkObjId="g_1ca1b30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="831,-437 831,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c47380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="831,-514 831,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37537@1" ObjectIDZND0="37509@0" Pin0InfoVect0LinkObjId="SW-225853_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="831,-514 831,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c475e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="831,-552 831,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37509@1" ObjectIDZND0="37536@1" Pin0InfoVect0LinkObjId="SW-225854_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225853_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="831,-552 831,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c47840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-468 992,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37556@x" ObjectIDND1="g_1fab110@0" ObjectIDND2="g_1fac780@0" ObjectIDZND0="37539@0" Pin0InfoVect0LinkObjId="SW-225876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225878_0" Pin1InfoVect1LinkObjId="g_1fab110_0" Pin1InfoVect2LinkObjId="g_1fac780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-468 992,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c47aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-515 992,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37539@1" ObjectIDZND0="37553@0" Pin0InfoVect0LinkObjId="SW-225875_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225876_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-515 992,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c47d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-553 992,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37553@1" ObjectIDZND0="37538@1" Pin0InfoVect0LinkObjId="SW-225876_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-553 992,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c47f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-582 992,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37538@0" ObjectIDZND0="37566@0" Pin0InfoVect0LinkObjId="g_1d30120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-582 992,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c481c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-606 1115,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="37566@0" ObjectIDZND0="37540@0" Pin0InfoVect0LinkObjId="SW-225899_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d30120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-606 1115,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c48420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-564 1115,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37540@1" ObjectIDZND0="37549@1" Pin0InfoVect0LinkObjId="SW-225898_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-564 1115,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c48680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-525 1115,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37549@0" ObjectIDZND0="37541@1" Pin0InfoVect0LinkObjId="SW-225899_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-525 1115,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c488e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-497 1115,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="37541@0" ObjectIDZND0="g_1fb4520@0" ObjectIDZND1="37551@x" ObjectIDZND2="g_1fb5b90@0" Pin0InfoVect0LinkObjId="g_1fb4520_0" Pin0InfoVect1LinkObjId="SW-225901_0" Pin0InfoVect2LinkObjId="g_1fb5b90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-497 1115,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c56370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-684 474,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="37548@1" ObjectIDZND0="37547@1" Pin0InfoVect0LinkObjId="SW-225949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225949_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-684 474,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5efd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-69,-920 -69,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1d1ce70@0" ObjectIDZND0="27099@x" ObjectIDZND1="37505@x" Pin0InfoVect0LinkObjId="SW-169473_0" Pin0InfoVect1LinkObjId="SW-169475_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d1ce70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-69,-920 -69,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-69,-928 -81,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="27099@x" ObjectIDND1="g_1d1ce70@0" ObjectIDZND0="37505@1" Pin0InfoVect0LinkObjId="SW-169475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-169473_0" Pin1InfoVect1LinkObjId="g_1d1ce70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-69,-928 -81,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-340 515,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46052@1" ObjectIDZND0="g_204bf40@0" Pin0InfoVect0LinkObjId="g_204bf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169607_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="502,-340 515,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2051d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="466,-340 441,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="46052@0" ObjectIDZND0="g_1c926a0@0" ObjectIDZND1="46053@x" Pin0InfoVect0LinkObjId="g_1c926a0_0" Pin0InfoVect1LinkObjId="SW-169608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="466,-340 441,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20526a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-386 441,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1c926a0@0" ObjectIDZND0="46052@x" ObjectIDZND1="46053@x" Pin0InfoVect0LinkObjId="SW-169607_0" Pin0InfoVect1LinkObjId="SW-169608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c926a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="441,-386 441,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2052890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-340 441,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="46052@x" ObjectIDND1="g_1c926a0@0" ObjectIDZND0="46053@1" Pin0InfoVect0LinkObjId="SW-169608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-169607_0" Pin1InfoVect1LinkObjId="g_1c926a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-340 441,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2053630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="469,-267 469,-274 441,-274 441,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2052aa0@0" ObjectIDZND0="46053@x" ObjectIDZND1="46051@x" Pin0InfoVect0LinkObjId="SW-169608_0" Pin0InfoVect1LinkObjId="EC-YM_JB.046Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2052aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="469,-267 469,-274 441,-274 441,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2054130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-279 441,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="46053@0" ObjectIDZND0="g_2052aa0@0" ObjectIDZND1="46051@x" Pin0InfoVect0LinkObjId="g_2052aa0_0" Pin0InfoVect1LinkObjId="EC-YM_JB.046Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="441,-279 441,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2054390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-270 441,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2052aa0@0" ObjectIDND1="46053@x" ObjectIDZND0="46051@0" Pin0InfoVect0LinkObjId="EC-YM_JB.046Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2052aa0_0" Pin1InfoVect1LinkObjId="SW-169608_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="441,-270 441,-242 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_JB"/>
</svg>