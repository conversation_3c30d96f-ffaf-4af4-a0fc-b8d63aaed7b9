<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-189" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="1324 -1442 2148 1165">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="7" stroke-width="0.153636"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="20" x2="20" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="14" y1="16" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1db69b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1db7b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1db84f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1db9190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dba3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbb060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dbbc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dbc6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_16336d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_16336d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dbfdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dbfdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc1b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc1b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1dc2b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc4770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dc5360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dc6120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dc6a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc95c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dc9d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcb7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcc2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dccc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dce0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dcec20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dcfc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dd0890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ddf060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd2190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dd3770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dd4ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1175" width="2158" x="1319" y="-1447"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2230" x2="2230" y1="-727" y2="-727"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2569" x2="2553" y1="-1358" y2="-1344"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2554" x2="2570" y1="-1358" y2="-1343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2613" x2="2597" y1="-1358" y2="-1344"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2598" x2="2614" y1="-1358" y2="-1343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2585" x2="2569" y1="-1337" y2="-1323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2570" x2="2586" y1="-1337" y2="-1322"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-141826">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 1950.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25319" ObjectName="SW-YR_LTS.YR_LTS_0901SW"/>
     <cge:Meas_Ref ObjectId="141826"/>
    <cge:TPSR_Ref TObjectID="25319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141509">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2265.000000 -1061.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25270" ObjectName="SW-YR_LTS.YR_LTS_3011SW"/>
     <cge:Meas_Ref ObjectId="141509"/>
    <cge:TPSR_Ref TObjectID="25270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2266.000000 -1248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25258" ObjectName="SW-YR_LTS.YR_LTS_3216SW"/>
     <cge:Meas_Ref ObjectId="141437"/>
    <cge:TPSR_Ref TObjectID="25258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -823.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2616.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25305" ObjectName="SW-YR_LTS.YR_LTS_0122SW"/>
     <cge:Meas_Ref ObjectId="141770"/>
    <cge:TPSR_Ref TObjectID="25305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2524.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25304" ObjectName="SW-YR_LTS.YR_LTS_0121SW"/>
     <cge:Meas_Ref ObjectId="141769"/>
    <cge:TPSR_Ref TObjectID="25304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2220.000000 -687.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25280" ObjectName="SW-YR_LTS.YR_LTS_0231SW"/>
     <cge:Meas_Ref ObjectId="141652"/>
    <cge:TPSR_Ref TObjectID="25280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2220.000000 -551.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25281" ObjectName="SW-YR_LTS.YR_LTS_0233SW"/>
     <cge:Meas_Ref ObjectId="141653"/>
    <cge:TPSR_Ref TObjectID="25281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2905.000000 -1060.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25275" ObjectName="SW-YR_LTS.YR_LTS_3021SW"/>
     <cge:Meas_Ref ObjectId="141578"/>
    <cge:TPSR_Ref TObjectID="25275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141436">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.500000 -1139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25257" ObjectName="SW-YR_LTS.YR_LTS_3211SW"/>
     <cge:Meas_Ref ObjectId="141436"/>
    <cge:TPSR_Ref TObjectID="25257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141556">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2264.000000 -762.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25272" ObjectName="SW-YR_LTS.YR_LTS_0011SW"/>
     <cge:Meas_Ref ObjectId="141556"/>
    <cge:TPSR_Ref TObjectID="25272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141557">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2264.000000 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25273" ObjectName="SW-YR_LTS.YR_LTS_0016SW"/>
     <cge:Meas_Ref ObjectId="141557"/>
    <cge:TPSR_Ref TObjectID="25273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2904.000000 -760.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25277" ObjectName="SW-YR_LTS.YR_LTS_0021SW"/>
     <cge:Meas_Ref ObjectId="141625"/>
    <cge:TPSR_Ref TObjectID="25277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2904.000000 -859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25278" ObjectName="SW-YR_LTS.YR_LTS_0026SW"/>
     <cge:Meas_Ref ObjectId="141626"/>
    <cge:TPSR_Ref TObjectID="25278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2243.000000 -1300.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25259" ObjectName="SW-YR_LTS.YR_LTS_32167SW"/>
     <cge:Meas_Ref ObjectId="141438"/>
    <cge:TPSR_Ref TObjectID="25259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2569.000000 -1248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25262" ObjectName="SW-YR_LTS.YR_LTS_3226SW"/>
     <cge:Meas_Ref ObjectId="141455"/>
    <cge:TPSR_Ref TObjectID="25262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2568.000000 -1138.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25261" ObjectName="SW-YR_LTS.YR_LTS_3221SW"/>
     <cge:Meas_Ref ObjectId="141454"/>
    <cge:TPSR_Ref TObjectID="25261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2549.000000 -1287.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25263" ObjectName="SW-YR_LTS.YR_LTS_32267SW"/>
     <cge:Meas_Ref ObjectId="141456"/>
    <cge:TPSR_Ref TObjectID="25263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141472">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2905.000000 -1251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25265" ObjectName="SW-YR_LTS.YR_LTS_3236SW"/>
     <cge:Meas_Ref ObjectId="141472"/>
    <cge:TPSR_Ref TObjectID="25265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.500000 -1141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25264" ObjectName="SW-YR_LTS.YR_LTS_3231SW"/>
     <cge:Meas_Ref ObjectId="141471"/>
    <cge:TPSR_Ref TObjectID="25264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141473">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2882.000000 -1276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25266" ObjectName="SW-YR_LTS.YR_LTS_32367SW"/>
     <cge:Meas_Ref ObjectId="141473"/>
    <cge:TPSR_Ref TObjectID="25266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2261.000000 -647.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2220.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25282" ObjectName="SW-YR_LTS.YR_LTS_0236SW"/>
     <cge:Meas_Ref ObjectId="141654"/>
    <cge:TPSR_Ref TObjectID="25282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 -688.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25284" ObjectName="SW-YR_LTS.YR_LTS_0221SW"/>
     <cge:Meas_Ref ObjectId="141670"/>
    <cge:TPSR_Ref TObjectID="25284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 -552.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25285" ObjectName="SW-YR_LTS.YR_LTS_0223SW"/>
     <cge:Meas_Ref ObjectId="141671"/>
    <cge:TPSR_Ref TObjectID="25285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2055.000000 -648.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25306" ObjectName="SW-YR_LTS.YR_LTS_02217SW"/>
     <cge:Meas_Ref ObjectId="141813"/>
    <cge:TPSR_Ref TObjectID="25306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25286" ObjectName="SW-YR_LTS.YR_LTS_0226SW"/>
     <cge:Meas_Ref ObjectId="141672"/>
    <cge:TPSR_Ref TObjectID="25286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2438.000000 -687.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25288" ObjectName="SW-YR_LTS.YR_LTS_0241SW"/>
     <cge:Meas_Ref ObjectId="141688"/>
    <cge:TPSR_Ref TObjectID="25288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141689">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2438.000000 -551.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25289" ObjectName="SW-YR_LTS.YR_LTS_0243SW"/>
     <cge:Meas_Ref ObjectId="141689"/>
    <cge:TPSR_Ref TObjectID="25289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2479.000000 -647.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141690">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2438.000000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25290" ObjectName="SW-YR_LTS.YR_LTS_0246SW"/>
     <cge:Meas_Ref ObjectId="141690"/>
    <cge:TPSR_Ref TObjectID="25290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141706">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2698.000000 -689.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25292" ObjectName="SW-YR_LTS.YR_LTS_0252SW"/>
     <cge:Meas_Ref ObjectId="141706"/>
    <cge:TPSR_Ref TObjectID="25292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141707">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2698.000000 -553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25293" ObjectName="SW-YR_LTS.YR_LTS_0253SW"/>
     <cge:Meas_Ref ObjectId="141707"/>
    <cge:TPSR_Ref TObjectID="25293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2739.000000 -649.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141708">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2698.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25294" ObjectName="SW-YR_LTS.YR_LTS_0256SW"/>
     <cge:Meas_Ref ObjectId="141708"/>
    <cge:TPSR_Ref TObjectID="25294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141724">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -688.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25296" ObjectName="SW-YR_LTS.YR_LTS_0262SW"/>
     <cge:Meas_Ref ObjectId="141724"/>
    <cge:TPSR_Ref TObjectID="25296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141725">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -552.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25297" ObjectName="SW-YR_LTS.YR_LTS_0263SW"/>
     <cge:Meas_Ref ObjectId="141725"/>
    <cge:TPSR_Ref TObjectID="25297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2979.000000 -648.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25298" ObjectName="SW-YR_LTS.YR_LTS_0266SW"/>
     <cge:Meas_Ref ObjectId="141726"/>
    <cge:TPSR_Ref TObjectID="25298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141742">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3179.000000 -687.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25300" ObjectName="SW-YR_LTS.YR_LTS_0272SW"/>
     <cge:Meas_Ref ObjectId="141742"/>
    <cge:TPSR_Ref TObjectID="25300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141743">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3179.000000 -551.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25301" ObjectName="SW-YR_LTS.YR_LTS_0273SW"/>
     <cge:Meas_Ref ObjectId="141743"/>
    <cge:TPSR_Ref TObjectID="25301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3220.000000 -647.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3179.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25302" ObjectName="SW-YR_LTS.YR_LTS_0276SW"/>
     <cge:Meas_Ref ObjectId="141744"/>
    <cge:TPSR_Ref TObjectID="25302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141828">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3246.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25321" ObjectName="SW-YR_LTS.YR_LTS_0902SW"/>
     <cge:Meas_Ref ObjectId="141828"/>
    <cge:TPSR_Ref TObjectID="25321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3285.000000 -823.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141825">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2070.000000 -1173.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25318" ObjectName="SW-YR_LTS.YR_LTS_39017SW"/>
     <cge:Meas_Ref ObjectId="141825"/>
    <cge:TPSR_Ref TObjectID="25318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141507">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2091.500000 -1141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25268" ObjectName="SW-YR_LTS.YR_LTS_3901SW"/>
     <cge:Meas_Ref ObjectId="141507"/>
    <cge:TPSR_Ref TObjectID="25268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3003.000000 -1247.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_LTS.YR_LTS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2003,-1120 3167,-1120 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25315" ObjectName="BS-YR_LTS.YR_LTS_3IM"/>
    <cge:TPSR_Ref TObjectID="25315"/></metadata>
   <polyline fill="none" opacity="0" points="2003,-1120 3167,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_LTS.YR_LTS_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2600,-751 3348,-751 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25317" ObjectName="BS-YR_LTS.YR_LTS_9IIM"/>
    <cge:TPSR_Ref TObjectID="25317"/></metadata>
   <polyline fill="none" opacity="0" points="2600,-751 3348,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_LTS.YR_LTS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1865,-752 2558,-752 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25316" ObjectName="BS-YR_LTS.YR_LTS_9IM"/>
    <cge:TPSR_Ref TObjectID="25316"/></metadata>
   <polyline fill="none" opacity="0" points="1865,-752 2558,-752 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_LTS.YR_LTS_023Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2220.000000 -357.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34001" ObjectName="EC-YR_LTS.YR_LTS_023Ld"/>
    <cge:TPSR_Ref TObjectID="34001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_LTS.YR_LTS_022Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 -358.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34000" ObjectName="EC-YR_LTS.YR_LTS_022Ld"/>
    <cge:TPSR_Ref TObjectID="34000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_LTS.YR_LTS_024Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2438.000000 -358.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34002" ObjectName="EC-YR_LTS.YR_LTS_024Ld"/>
    <cge:TPSR_Ref TObjectID="34002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_LTS.YR_LTS_025Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2698.000000 -357.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34003" ObjectName="EC-YR_LTS.YR_LTS_025Ld"/>
    <cge:TPSR_Ref TObjectID="34003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_LTS.YR_LTS_026Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -356.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34004" ObjectName="EC-YR_LTS.YR_LTS_026Ld"/>
    <cge:TPSR_Ref TObjectID="34004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_LTS.YR_LTS_027Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3179.000000 -356.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34005" ObjectName="EC-YR_LTS.YR_LTS_027Ld"/>
    <cge:TPSR_Ref TObjectID="34005"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2863830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2034.000000 -843.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28072f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2198.000000 -1320.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_279cdb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2504.000000 -1307.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2840b70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2837.000000 -1296.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2728540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -667.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2822590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2096.000000 -668.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27b9c60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2520.000000 -667.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_321b6f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2780.000000 -669.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27ca8d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3020.000000 -668.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27ac320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3261.000000 -667.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_272bf30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3330.000000 -843.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2811bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2025.000000 -1193.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_285f400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2038,-849 2016,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2863830@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2863830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2038,-849 2016,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_280c2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,-849 1959,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2826aa0@0" ObjectIDZND1="g_285b8a0@0" ObjectIDZND2="25319@x" Pin0InfoVect0LinkObjId="g_2826aa0_0" Pin0InfoVect1LinkObjId="g_285b8a0_0" Pin0InfoVect2LinkObjId="SW-141826_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1980,-849 1959,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b87c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-1022 2273,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25269@0" ObjectIDZND0="25312@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-1022 2273,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2726690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-1066 2273,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25270@0" ObjectIDZND0="25269@1" Pin0InfoVect0LinkObjId="SW-141508_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-1066 2273,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_271cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1021 2913,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25274@0" ObjectIDZND0="25313@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1021 2913,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3222850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1065 2913,-1048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25275@0" ObjectIDZND0="25274@1" Pin0InfoVect0LinkObjId="SW-141577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1065 2913,-1048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e2180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1958,-927 1958,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_285b8a0@0" ObjectIDZND0="g_2816860@0" Pin0InfoVect0LinkObjId="g_2816860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_285b8a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-927 1958,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27c9fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-1146 2100,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25268@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_27443d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-1146 2100,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27da410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1180 2274,-1202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25257@1" ObjectIDZND0="25256@0" Pin0InfoVect0LinkObjId="SW-141435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1180 2274,-1202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_281a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1229 2274,-1253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25256@1" ObjectIDZND0="25258@0" Pin0InfoVect0LinkObjId="SW-141437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1229 2274,-1253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d3c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2272,-822 2272,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25271@0" ObjectIDZND0="25272@1" Pin0InfoVect0LinkObjId="SW-141556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2272,-822 2272,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27cbcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2272,-866 2272,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25273@0" ObjectIDZND0="25271@1" Pin0InfoVect0LinkObjId="SW-141555_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2272,-866 2272,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c2ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-912 2272,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="25312@0" ObjectIDZND0="25273@1" Pin0InfoVect0LinkObjId="SW-141557_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b87c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-912 2272,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27799d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2272,-767 2272,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25272@0" ObjectIDZND0="25316@0" Pin0InfoVect0LinkObjId="g_27fa110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2272,-767 2272,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b4680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-820 2912,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25276@0" ObjectIDZND0="25277@1" Pin0InfoVect0LinkObjId="SW-141625_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-820 2912,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3215de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-864 2912,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25278@0" ObjectIDZND0="25276@1" Pin0InfoVect0LinkObjId="SW-141624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-864 2912,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d2580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-765 2912,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25277@0" ObjectIDZND0="25317@0" Pin0InfoVect0LinkObjId="g_27c8aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141625_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-765 2912,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2795610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,-1326 2216,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28072f0@0" ObjectIDZND0="25259@1" Pin0InfoVect0LinkObjId="SW-141438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28072f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,-1326 2216,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2862370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1179 2577,-1202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25261@1" ObjectIDZND0="25260@0" Pin0InfoVect0LinkObjId="SW-141453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1179 2577,-1202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3216130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1229 2577,-1253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25260@1" ObjectIDZND0="25262@0" Pin0InfoVect0LinkObjId="SW-141455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1229 2577,-1253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b34b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2499,-1313 2522,-1313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_279cdb0@0" ObjectIDZND0="25263@1" Pin0InfoVect0LinkObjId="SW-141456_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_279cdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2499,-1313 2522,-1313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2753d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2558,-1313 2577,-1313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="25263@0" ObjectIDZND0="25262@x" Pin0InfoVect0LinkObjId="SW-141455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2558,-1313 2577,-1313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27443d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1143 2577,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25261@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_27c9fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1143 2577,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_321c670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1182 2913,-1205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25264@1" ObjectIDZND0="25267@0" Pin0InfoVect0LinkObjId="SW-141476_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141471_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1182 2913,-1205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_321c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1232 2913,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25267@1" ObjectIDZND0="25265@0" Pin0InfoVect0LinkObjId="SW-141472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141476_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1232 2913,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2840910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2832,-1302 2855,-1302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2840b70@0" ObjectIDZND0="25266@1" Pin0InfoVect0LinkObjId="SW-141473_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2840b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2832,-1302 2855,-1302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273e8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1909,-901 1909,-875 1958,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2826aa0@0" ObjectIDZND0="g_285b8a0@0" ObjectIDZND1="0@x" ObjectIDZND2="25319@x" Pin0InfoVect0LinkObjId="g_285b8a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-141826_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2826aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1909,-901 1909,-875 1958,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273eaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1958,-875 1958,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2826aa0@0" ObjectIDND1="0@x" ObjectIDND2="25319@x" ObjectIDZND0="g_285b8a0@1" Pin0InfoVect0LinkObjId="g_285b8a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2826aa0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-141826_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-875 1958,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27fa110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1958,-784 1958,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25319@0" ObjectIDZND0="25316@0" Pin0InfoVect0LinkObjId="g_27799d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-784 1958,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2771730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1958,-875 1958,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2826aa0@0" ObjectIDND1="g_285b8a0@0" ObjectIDZND0="0@x" ObjectIDZND1="25319@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-141826_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2826aa0_0" Pin1InfoVect1LinkObjId="g_285b8a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-875 1958,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c3ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1958,-849 1958,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2826aa0@0" ObjectIDND2="g_285b8a0@0" ObjectIDZND0="25319@1" Pin0InfoVect0LinkObjId="SW-141826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2826aa0_0" Pin1InfoVect2LinkObjId="g_285b8a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-849 1958,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_280c990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2306,-673 2288,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2728540@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2728540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2306,-673 2288,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3213870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-619 2229,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25279@0" ObjectIDZND0="25281@1" Pin0InfoVect0LinkObjId="SW-141653_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-619 2229,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3213ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,-673 2229,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="25280@x" ObjectIDZND1="25279@x" Pin0InfoVect0LinkObjId="SW-141652_0" Pin0InfoVect1LinkObjId="SW-141651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2252,-673 2229,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27754e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-703 2229,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25280@0" ObjectIDZND0="0@x" ObjectIDZND1="25279@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-141651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-703 2229,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2775740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-673 2229,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="25280@x" ObjectIDZND0="25279@1" Pin0InfoVect0LinkObjId="SW-141651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-141652_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-673 2229,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2822330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-674 2082,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2822590@0" ObjectIDZND0="25306@1" Pin0InfoVect0LinkObjId="SW-141813_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2822590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-674 2082,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321e100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-620 2023,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25283@0" ObjectIDZND0="25285@1" Pin0InfoVect0LinkObjId="SW-141671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-620 2023,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321e360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2046,-674 2023,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25306@0" ObjectIDZND0="25284@x" ObjectIDZND1="25283@x" Pin0InfoVect0LinkObjId="SW-141670_0" Pin0InfoVect1LinkObjId="SW-141669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2046,-674 2023,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27cfcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-704 2023,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25284@0" ObjectIDZND0="25306@x" ObjectIDZND1="25283@x" Pin0InfoVect0LinkObjId="SW-141813_0" Pin0InfoVect1LinkObjId="SW-141669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-704 2023,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27cff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-674 2023,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25306@x" ObjectIDND1="25284@x" ObjectIDZND0="25283@1" Pin0InfoVect0LinkObjId="SW-141669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141813_0" Pin1InfoVect1LinkObjId="SW-141670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-674 2023,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2820ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2524,-673 2506,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27b9c60@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b9c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2524,-673 2506,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274e6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-619 2447,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25287@0" ObjectIDZND0="25289@1" Pin0InfoVect0LinkObjId="SW-141689_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141687_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-619 2447,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274e920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2470,-673 2447,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="25288@x" ObjectIDZND1="25287@x" Pin0InfoVect0LinkObjId="SW-141688_0" Pin0InfoVect1LinkObjId="SW-141687_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2470,-673 2447,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273c1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-703 2447,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25288@0" ObjectIDZND0="0@x" ObjectIDZND1="25287@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-141687_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141688_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-703 2447,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273c450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-673 2447,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="25288@x" ObjectIDZND0="25287@1" Pin0InfoVect0LinkObjId="SW-141687_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-141688_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-673 2447,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_321b490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2784,-675 2766,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_321b6f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_321b6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2784,-675 2766,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273ad90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-621 2707,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25291@0" ObjectIDZND0="25293@1" Pin0InfoVect0LinkObjId="SW-141707_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-621 2707,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273afc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2730,-675 2707,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="25292@x" ObjectIDZND1="25291@x" Pin0InfoVect0LinkObjId="SW-141706_0" Pin0InfoVect1LinkObjId="SW-141705_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2730,-675 2707,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273b220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-705 2707,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25292@0" ObjectIDZND0="0@x" ObjectIDZND1="25291@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-141705_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141706_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-705 2707,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2802270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-675 2707,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25292@x" ObjectIDND1="0@x" ObjectIDZND0="25291@1" Pin0InfoVect0LinkObjId="SW-141705_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141706_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-675 2707,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27c2b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3024,-674 3006,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27ca8d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27ca8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3024,-674 3006,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b36f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-620 2947,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25295@0" ObjectIDZND0="25297@1" Pin0InfoVect0LinkObjId="SW-141725_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-620 2947,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b3950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2970,-674 2947,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="25296@x" ObjectIDZND1="25295@x" Pin0InfoVect0LinkObjId="SW-141724_0" Pin0InfoVect1LinkObjId="SW-141723_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2970,-674 2947,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b3bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-704 2947,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25296@0" ObjectIDZND0="0@x" ObjectIDZND1="25295@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-141723_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-704 2947,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b3e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-674 2947,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="25296@x" ObjectIDZND0="25295@1" Pin0InfoVect0LinkObjId="SW-141723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-141724_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-674 2947,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27ac0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3265,-673 3247,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27ac320@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27ac320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3265,-673 3247,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3233db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-620 3188,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25299@0" ObjectIDZND0="25301@1" Pin0InfoVect0LinkObjId="SW-141743_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-620 3188,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3234010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3211,-673 3188,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="25300@x" ObjectIDZND1="25299@x" Pin0InfoVect0LinkObjId="SW-141742_0" Pin0InfoVect1LinkObjId="SW-141741_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3211,-673 3188,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3234270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-703 3188,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25300@0" ObjectIDZND0="0@x" ObjectIDZND1="25299@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-141741_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-703 3188,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32344d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-673 3188,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25300@x" ObjectIDND1="0@x" ObjectIDZND0="25299@1" Pin0InfoVect0LinkObjId="SW-141741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141742_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-673 3188,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_272ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3334,-849 3312,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_272bf30@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_272bf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3334,-849 3312,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_272bcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3276,-849 3255,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_286b080@0" ObjectIDZND1="g_2748940@0" ObjectIDZND2="25321@x" Pin0InfoVect0LinkObjId="g_286b080_0" Pin0InfoVect1LinkObjId="g_2748940_0" Pin0InfoVect2LinkObjId="SW-141828_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3276,-849 3255,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2729f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3254,-927 3254,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2748940@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2748940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3254,-927 3254,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_272a1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3205,-901 3205,-875 3254,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_286b080@0" ObjectIDZND0="g_2748940@0" ObjectIDZND1="0@x" ObjectIDZND2="25321@x" Pin0InfoVect0LinkObjId="g_2748940_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-141828_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_286b080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3205,-901 3205,-875 3254,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_272a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3254,-875 3254,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_286b080@0" ObjectIDND1="0@x" ObjectIDND2="25321@x" ObjectIDZND0="g_2748940@1" Pin0InfoVect0LinkObjId="g_2748940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_286b080_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-141828_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3254,-875 3254,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c8aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3254,-784 3254,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25321@0" ObjectIDZND0="25317@0" Pin0InfoVect0LinkObjId="g_27d2580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141828_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3254,-784 3254,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c8d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3254,-875 3254,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_286b080@0" ObjectIDND1="g_2748940@0" ObjectIDZND0="0@x" ObjectIDZND1="25321@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-141828_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_286b080_0" Pin1InfoVect1LinkObjId="g_2748940_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3254,-875 3254,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c8f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3254,-849 3254,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_286b080@0" ObjectIDND2="g_2748940@0" ObjectIDZND0="25321@1" Pin0InfoVect0LinkObjId="SW-141828_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_286b080_0" Pin1InfoVect2LinkObjId="g_2748940_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3254,-849 3254,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2811990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2020,-1199 2043,-1199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2811bf0@0" ObjectIDZND0="25318@1" Pin0InfoVect0LinkObjId="SW-141825_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2811bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2020,-1199 2043,-1199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a1540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2079,-1199 2099,-1199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25318@0" ObjectIDZND0="g_27e7130@0" ObjectIDZND1="25268@x" Pin0InfoVect0LinkObjId="g_27e7130_0" Pin0InfoVect1LinkObjId="SW-141507_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141825_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2079,-1199 2099,-1199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a17a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-1219 2100,-1199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_27e7130@0" ObjectIDZND0="25318@x" ObjectIDZND1="25268@x" Pin0InfoVect0LinkObjId="SW-141825_0" Pin0InfoVect1LinkObjId="SW-141507_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27e7130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-1219 2100,-1199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a1a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-1199 2100,-1182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25318@x" ObjectIDND1="g_27e7130@0" ObjectIDZND0="25268@1" Pin0InfoVect0LinkObjId="SW-141507_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141825_0" Pin1InfoVect1LinkObjId="g_27e7130_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-1199 2100,-1182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32280c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1144 2274,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25257@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_27c9fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1144 2274,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3228300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2273,-1102 2273,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25270@1" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_27c9fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2273,-1102 2273,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3228560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2320,-1352 2274,-1352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_2796380@0" ObjectIDZND0="37846@1" ObjectIDZND1="g_290ceb0@0" ObjectIDZND2="25259@x" Pin0InfoVect0LinkObjId="g_2855bb0_1" Pin0InfoVect1LinkObjId="g_290ceb0_0" Pin0InfoVect2LinkObjId="SW-141438_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2796380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2320,-1352 2274,-1352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2842710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,-1326 2274,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25259@0" ObjectIDZND0="25258@x" ObjectIDZND1="g_290ceb0@0" ObjectIDZND2="g_2796380@0" Pin0InfoVect0LinkObjId="SW-141437_0" Pin0InfoVect1LinkObjId="g_290ceb0_0" Pin0InfoVect2LinkObjId="g_2796380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2252,-1326 2274,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2842970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1146 2913,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25264@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_27c9fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1146 2913,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2842bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1101 2913,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25275@1" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_27c9fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1101 2913,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2842e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1319 3008,-1319 3008,-1297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2737200@0" ObjectIDND1="37841@1" ObjectIDND2="g_2858c70@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2737200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_2858c70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1319 3008,-1319 3008,-1297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2843090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1392 2992,-1392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2858c70@0" ObjectIDND1="0@x" ObjectIDND2="25266@x" ObjectIDZND0="g_2737200@0" Pin0InfoVect0LinkObjId="g_2737200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2858c70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-141473_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1392 2992,-1392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d83b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1345 2882,-1345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2737200@0" ObjectIDND1="37841@1" ObjectIDND2="0@x" ObjectIDZND0="g_2858c70@0" Pin0InfoVect0LinkObjId="g_2858c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2737200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1345 2882,-1345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d8610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2891,-1302 2912,-1302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="25266@0" ObjectIDZND0="25265@x" ObjectIDZND1="g_2737200@0" ObjectIDZND2="37841@1" Pin0InfoVect0LinkObjId="SW-141472_0" Pin0InfoVect1LinkObjId="g_2737200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2891,-1302 2912,-1302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3008,-1252 3008,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="25314@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3008,-1252 3008,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2823460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2052,-394 2023,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_29b8030@0" ObjectIDZND0="25286@x" ObjectIDZND1="34000@x" Pin0InfoVect0LinkObjId="SW-141672_0" Pin0InfoVect1LinkObjId="EC-YR_LTS.YR_LTS_022Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b8030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2052,-394 2023,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28236c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-438 2023,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25286@1" ObjectIDZND0="g_27a2d70@1" Pin0InfoVect0LinkObjId="g_27a2d70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141672_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-438 2023,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2874dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-556 3188,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25301@0" ObjectIDZND0="g_2778be0@0" ObjectIDZND1="g_27bd5b0@0" Pin0InfoVect0LinkObjId="g_2778be0_0" Pin0InfoVect1LinkObjId="g_27bd5b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-556 3188,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2875020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-535 3251,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25301@x" ObjectIDND1="g_27bd5b0@0" ObjectIDZND0="g_2778be0@0" Pin0InfoVect0LinkObjId="g_2778be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141743_0" Pin1InfoVect1LinkObjId="g_27bd5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-535 3251,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2844470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-438 2447,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25290@1" ObjectIDZND0="g_2875280@1" Pin0InfoVect0LinkObjId="g_2875280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-438 2447,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2866830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-437 2707,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25294@1" ObjectIDZND0="g_28446d0@1" Pin0InfoVect0LinkObjId="g_28446d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141708_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-437 2707,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27bd350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-436 2947,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25298@1" ObjectIDZND0="g_2866a90@1" Pin0InfoVect0LinkObjId="g_2866a90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-436 2947,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2775a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-535 3188,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25301@x" ObjectIDND1="g_2778be0@0" ObjectIDZND0="g_27bd5b0@0" Pin0InfoVect0LinkObjId="g_27bd5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141743_0" Pin1InfoVect1LinkObjId="g_2778be0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-535 3188,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2775c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-460 3188,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_27bd5b0@1" ObjectIDZND0="25302@1" Pin0InfoVect0LinkObjId="SW-141744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27bd5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-460 3188,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2775ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2258,-392 2229,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_29b8c40@0" ObjectIDZND0="25282@x" ObjectIDZND1="34001@x" Pin0InfoVect0LinkObjId="SW-141654_0" Pin0InfoVect1LinkObjId="EC-YR_LTS.YR_LTS_023Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b8c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2258,-392 2229,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2734e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-436 2229,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25282@1" ObjectIDZND0="g_2776150@1" Pin0InfoVect0LinkObjId="g_2776150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-436 2229,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27350c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-729 2023,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25284@1" ObjectIDZND0="25316@0" Pin0InfoVect0LinkObjId="g_27799d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-729 2023,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27358f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-728 2229,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25280@1" ObjectIDZND0="25316@0" Pin0InfoVect0LinkObjId="g_27799d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-728 2229,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3221610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-728 2447,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25288@1" ObjectIDZND0="25316@0" Pin0InfoVect0LinkObjId="g_27799d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141688_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-728 2447,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3221e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-730 2707,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25292@1" ObjectIDZND0="25317@0" Pin0InfoVect0LinkObjId="g_27d2580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141706_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-730 2707,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3229b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-729 2947,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25296@1" ObjectIDZND0="25317@0" Pin0InfoVect0LinkObjId="g_27d2580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-729 2947,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_322a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-728 3188,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25300@1" ObjectIDZND0="25317@0" Pin0InfoVect0LinkObjId="g_27d2580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-728 3188,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287af10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2912,-923 2912,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="25313@1" ObjectIDZND0="25278@1" Pin0InfoVect0LinkObjId="SW-141626_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_271cd80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2912,-923 2912,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28775a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2533,-784 2533,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25304@0" ObjectIDZND0="25316@0" Pin0InfoVect0LinkObjId="g_27799d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2533,-784 2533,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2877c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2625,-784 2625,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25305@0" ObjectIDZND0="25317@0" Pin0InfoVect0LinkObjId="g_27d2580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2625,-784 2625,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2878390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2593,-843 2625,-843 2625,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25303@0" ObjectIDZND0="25305@1" Pin0InfoVect0LinkObjId="SW-141770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2593,-843 2625,-843 2625,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28785f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2566,-843 2533,-843 2533,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25303@1" ObjectIDZND0="25304@1" Pin0InfoVect0LinkObjId="SW-141769_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141759_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2566,-843 2533,-843 2533,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2855bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1352 2274,-1380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2796380@0" ObjectIDND1="g_290ceb0@0" ObjectIDND2="25259@x" ObjectIDZND0="37846@1" Pin0InfoVect0LinkObjId="g_3228560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2796380_0" Pin1InfoVect1LinkObjId="g_290ceb0_0" Pin1InfoVect2LinkObjId="SW-141438_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1352 2274,-1380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2856520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1289 2274,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25258@1" ObjectIDZND0="25259@x" ObjectIDZND1="g_290ceb0@0" ObjectIDZND2="g_2796380@0" Pin0InfoVect0LinkObjId="SW-141438_0" Pin0InfoVect1LinkObjId="g_290ceb0_0" Pin0InfoVect2LinkObjId="g_2796380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1289 2274,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2856760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2540,-1351 2640,-1351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_28569c0@0" ObjectIDZND0="g_27f5ca0@0" Pin0InfoVect0LinkObjId="g_27f5ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28569c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2540,-1351 2640,-1351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2857f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1404 2913,-1392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37841@1" ObjectIDZND0="g_2737200@0" ObjectIDZND1="g_2858c70@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2737200_0" Pin0InfoVect1LinkObjId="g_2858c70_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1404 2913,-1392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2858a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1345 2913,-1392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" ObjectIDND0="g_2858c70@0" ObjectIDND1="0@x" ObjectIDND2="25266@x" ObjectIDZND0="g_2737200@0" ObjectIDZND1="37841@1" Pin0InfoVect0LinkObjId="g_2737200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2858c70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-141473_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1345 2913,-1392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_285a2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1319 2913,-1345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="25266@x" ObjectIDND2="25265@x" ObjectIDZND0="g_2737200@0" ObjectIDZND1="37841@1" ObjectIDZND2="g_2858c70@0" Pin0InfoVect0LinkObjId="g_2737200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_2858c70_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-141473_0" Pin1InfoVect2LinkObjId="SW-141472_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1319 2913,-1345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_285ada0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1292 2913,-1302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="25265@1" ObjectIDZND0="25266@x" ObjectIDZND1="g_2737200@0" ObjectIDZND2="37841@1" Pin0InfoVect0LinkObjId="SW-141473_0" Pin0InfoVect1LinkObjId="g_2737200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1292 2913,-1302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b7dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1302 2913,-1319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="25266@x" ObjectIDND1="25265@x" ObjectIDZND0="g_2737200@0" ObjectIDZND1="37841@1" ObjectIDZND2="g_2858c70@0" Pin0InfoVect0LinkObjId="g_2737200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_2858c70_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="SW-141472_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1302 2913,-1319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bd800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-402 2023,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="25286@0" ObjectIDZND0="34000@x" ObjectIDZND1="g_29b8030@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_022Ld_0" Pin0InfoVect1LinkObjId="g_29b8030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-402 2023,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bda60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-394 2023,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="25286@x" ObjectIDND1="g_29b8030@0" ObjectIDZND0="34000@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_022Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141672_0" Pin1InfoVect1LinkObjId="g_29b8030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-394 2023,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bdcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2023,-557 2023,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25285@0" ObjectIDZND0="g_27a2d70@0" Pin0InfoVect0LinkObjId="g_27a2d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2023,-557 2023,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29be7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-400 2229,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="25282@0" ObjectIDZND0="34001@x" ObjectIDZND1="g_29b8c40@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_023Ld_0" Pin0InfoVect1LinkObjId="g_29b8c40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-400 2229,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-392 2229,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="25282@x" ObjectIDND1="g_29b8c40@0" ObjectIDZND0="34001@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_023Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141654_0" Pin1InfoVect1LinkObjId="g_29b8c40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-392 2229,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2229,-506 2229,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2776150@0" ObjectIDZND0="25281@0" Pin0InfoVect0LinkObjId="SW-141653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2776150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2229,-506 2229,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29beed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-556 2447,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25289@0" ObjectIDZND0="g_2875280@0" Pin0InfoVect0LinkObjId="g_2875280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-556 2447,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bf130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-558 2707,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25293@0" ObjectIDZND0="g_28446d0@0" Pin0InfoVect0LinkObjId="g_28446d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141707_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-558 2707,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bf390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-557 2947,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25297@0" ObjectIDZND0="g_2866a90@0" Pin0InfoVect0LinkObjId="g_2866a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-557 2947,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bf5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2476,-393 2447,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_29b99b0@0" ObjectIDZND0="25290@x" ObjectIDZND1="34002@x" Pin0InfoVect0LinkObjId="SW-141690_0" Pin0InfoVect1LinkObjId="EC-YR_LTS.YR_LTS_024Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b99b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2476,-393 2447,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bf850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2736,-392 2707,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_29ba720@0" ObjectIDZND0="25294@x" ObjectIDZND1="34003@x" Pin0InfoVect0LinkObjId="SW-141708_0" Pin0InfoVect1LinkObjId="EC-YR_LTS.YR_LTS_025Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29ba720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2736,-392 2707,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29bfab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2976,-390 2947,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_29bb490@0" ObjectIDZND0="25298@x" ObjectIDZND1="34004@x" Pin0InfoVect0LinkObjId="SW-141726_0" Pin0InfoVect1LinkObjId="EC-YR_LTS.YR_LTS_026Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29bb490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2976,-390 2947,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c05a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-402 2447,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="25290@0" ObjectIDZND0="34002@x" ObjectIDZND1="g_29b99b0@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_024Ld_0" Pin0InfoVect1LinkObjId="g_29b99b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-402 2447,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c0800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2447,-393 2447,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="25290@x" ObjectIDND1="g_29b99b0@0" ObjectIDZND0="34002@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_024Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141690_0" Pin1InfoVect1LinkObjId="g_29b99b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2447,-393 2447,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2900540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-401 2707,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="25294@0" ObjectIDZND0="34003@x" ObjectIDZND1="g_29ba720@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_025Ld_0" Pin0InfoVect1LinkObjId="g_29ba720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-401 2707,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29007a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2707,-392 2707,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="25294@x" ObjectIDND1="g_29ba720@0" ObjectIDZND0="34003@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_025Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141708_0" Pin1InfoVect1LinkObjId="g_29ba720_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2707,-392 2707,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-400 2947,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="25298@0" ObjectIDZND0="34004@x" ObjectIDZND1="g_29bb490@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_026Ld_0" Pin0InfoVect1LinkObjId="g_29bb490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-400 2947,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29014d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2947,-390 2947,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="25298@x" ObjectIDND1="g_29bb490@0" ObjectIDZND0="34004@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_026Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141726_0" Pin1InfoVect1LinkObjId="g_29bb490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2947,-390 2947,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3217,-392 3188,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_29bc200@0" ObjectIDZND0="25302@x" ObjectIDZND1="34005@x" Pin0InfoVect0LinkObjId="SW-141744_0" Pin0InfoVect1LinkObjId="EC-YR_LTS.YR_LTS_027Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29bc200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3217,-392 3188,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2902170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-400 3188,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="25302@0" ObjectIDZND0="34005@x" ObjectIDZND1="g_29bc200@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_027Ld_0" Pin0InfoVect1LinkObjId="g_29bc200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-400 3188,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29023d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3188,-392 3188,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="25302@x" ObjectIDND1="g_29bc200@0" ObjectIDZND0="34005@0" Pin0InfoVect0LinkObjId="EC-YR_LTS.YR_LTS_027Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141744_0" Pin1InfoVect1LinkObjId="g_29bc200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3188,-392 3188,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_290ccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1338 2340,-1338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2796380@0" ObjectIDND1="37846@1" ObjectIDND2="25259@x" ObjectIDZND0="g_290ceb0@0" Pin0InfoVect0LinkObjId="g_290ceb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2796380_0" Pin1InfoVect1LinkObjId="g_3228560_1" Pin1InfoVect2LinkObjId="SW-141438_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1338 2340,-1338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_290ebb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1352 2274,-1338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2796380@0" ObjectIDND1="37846@1" ObjectIDZND0="g_290ceb0@0" ObjectIDZND1="25259@x" ObjectIDZND2="25258@x" Pin0InfoVect0LinkObjId="g_290ceb0_0" Pin0InfoVect1LinkObjId="SW-141438_0" Pin0InfoVect2LinkObjId="SW-141437_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2796380_0" Pin1InfoVect1LinkObjId="g_3228560_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1352 2274,-1338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_290ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-1338 2274,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_290ceb0@0" ObjectIDND1="g_2796380@0" ObjectIDND2="37846@1" ObjectIDZND0="25259@x" ObjectIDZND1="25258@x" Pin0InfoVect0LinkObjId="SW-141438_0" Pin0InfoVect1LinkObjId="SW-141437_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_290ceb0_0" Pin1InfoVect1LinkObjId="g_2796380_0" Pin1InfoVect2LinkObjId="g_3228560_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-1338 2274,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cf230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1416 2577,-1313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="25263@x" ObjectIDZND1="25262@x" Pin0InfoVect0LinkObjId="SW-141456_0" Pin0InfoVect1LinkObjId="SW-141455_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1416 2577,-1313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cf490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2577,-1313 2577,-1289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="25263@x" ObjectIDZND0="25262@1" Pin0InfoVect0LinkObjId="SW-141455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2577,-1313 2577,-1289 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25316" cx="2272" cy="-752" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25317" cx="2912" cy="-751" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25317" cx="3254" cy="-751" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25316" cx="1958" cy="-752" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25316" cx="2023" cy="-752" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25316" cx="2229" cy="-752" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25316" cx="2447" cy="-752" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25317" cx="2707" cy="-751" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25317" cx="2947" cy="-751" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25317" cx="3188" cy="-751" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="2100" cy="-1120" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="2577" cy="-1120" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="2274" cy="-1120" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="2273" cy="-1120" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="2913" cy="-1120" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25315" cx="2913" cy="-1120" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25316" cx="2533" cy="-752" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25317" cx="2625" cy="-751" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130468" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1558.500000 -1262.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23909" ObjectName="DYN-YR_LTS"/>
     <cge:Meas_Ref ObjectId="130468"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2740480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2962.000000 -1167.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_284ff00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -995.000000) translate(0,15)">10kV I段母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2852860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.500000 -1421.000000) translate(0,15)">莲</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2852860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.500000 -1421.000000) translate(0,33)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2852860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.500000 -1421.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27be790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -741.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2741500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3311.000000 -750.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27430f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1985.000000 -1113.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2197.000000 -352.000000) translate(0,15)">太平地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27744c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2550.000000 -1426.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27744c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2550.000000 -1426.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27744c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2550.000000 -1426.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2744630" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2325.326733 -1264.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2840e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2888.500000 -1442.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2840e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2888.500000 -1442.000000) translate(0,33)">猛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2840e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2888.500000 -1442.000000) translate(0,51)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2840e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2888.500000 -1442.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28170e0" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2627.326733 -1272.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2731080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1977.000000 -354.000000) translate(0,15)">龙城III回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28097f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2415.000000 -352.000000) translate(0,15)">凹利乍线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3224410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3211.000000 -998.000000) translate(0,15)">10kVⅡ段母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3212820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2664.000000 -353.000000) translate(0,15)">龙城IV回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3212d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2910.000000 -353.000000) translate(0,15)">龙城V回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3212f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -354.000000) translate(0,15)">龙城VI回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_272ef70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2053.000000 -1318.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_278a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -792.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2849780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -1230.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_27fdea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -1371.500000) translate(0,16)">龙头山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3232130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2284.000000 -1223.000000) translate(0,12)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3232be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -1169.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3232e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -1278.000000) translate(0,12)">3216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32331a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2214.000000 -1351.000000) translate(0,12)">32167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2796bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -1171.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2797110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2587.000000 -1223.000000) translate(0,12)">322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2797390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1168.000000) translate(0,12)">3221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27975d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1278.000000) translate(0,12)">3226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2797810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2507.000000 -1335.000000) translate(0,12)">32267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2797a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2923.000000 -1226.000000) translate(0,12)">323</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2745820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2920.000000 -1171.000000) translate(0,12)">3231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2745a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2920.000000 -1281.000000) translate(0,12)">3236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2745c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2852.000000 -1328.000000) translate(0,12)">32367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2745ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2284.000000 -1043.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27460e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -1091.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2746320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2923.000000 -1042.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2746560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2920.000000 -1090.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27467a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2922.000000 -841.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277dc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2919.000000 -790.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2919.000000 -889.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277e0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2282.000000 -843.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277e300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2279.000000 -891.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277e540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2279.000000 -792.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277e780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2032.000000 -641.000000) translate(0,12)">022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277e9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2030.000000 -582.000000) translate(0,12)">0223</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277ec00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2030.000000 -718.000000) translate(0,12)">0221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286e2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2044.000000 -700.000000) translate(0,12)">02217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286e520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2030.000000 -427.000000) translate(0,12)">0226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286e760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 -640.000000) translate(0,12)">023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286e9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2236.000000 -581.000000) translate(0,12)">0233</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286ebe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2236.000000 -717.000000) translate(0,12)">0231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286ee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -699.000000) translate(0,12)">02317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286f060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2236.000000 -426.000000) translate(0,12)">0236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286f2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -640.000000) translate(0,12)">024</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e09a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2454.000000 -717.000000) translate(0,12)">0241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e0db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -699.000000) translate(0,12)">02417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e0ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2454.000000 -581.000000) translate(0,12)">0243</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e1230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2454.000000 -427.000000) translate(0,12)">0246</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e1470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2567.000000 -867.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e16b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2540.000000 -809.500000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e18f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2632.000000 -809.500000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e1b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2716.000000 -642.000000) translate(0,12)">025</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 -719.000000) translate(0,12)">0252</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e9bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2728.000000 -701.000000) translate(0,12)">02527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e9e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 -583.000000) translate(0,12)">0253</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ea050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2714.000000 -426.000000) translate(0,12)">0256</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ea290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -641.000000) translate(0,12)">026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ea4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2954.000000 -718.000000) translate(0,12)">0262</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ea710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2967.000000 -700.000000) translate(0,12)">02627</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ea950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2954.000000 -582.000000) translate(0,12)">0263</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27eab90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2954.000000 -425.000000) translate(0,12)">0266</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287a3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3197.000000 -640.000000) translate(0,12)">027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287a610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -717.000000) translate(0,12)">0272</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287a850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3208.000000 -699.000000) translate(0,12)">02727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287aa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -581.000000) translate(0,12)">0273</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287acd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -425.000000) translate(0,12)">0276</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2836.000000 -966.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_284dc90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -809.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_284dee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3273.000000 -875.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_284e0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1965.000000 -809.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_284e300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1978.000000 -875.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_284e540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -1225.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_284e780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2096.000000 -699.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_28763c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2304.000000 -699.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_2876820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2522.000000 -699.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_2876a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2785.000000 -699.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_2876ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3022.000000 -699.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_2876ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -699.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_2877120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3270.000000 -898.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_2877360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -875.000000) translate(0,12)"> 现场没有采集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_2846080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2964.000000 -1091.000000) translate(0,12)">2号主变高压侧3021隔离开关与现场状态不一致需要核对</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="15" graphid="g_27a4d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -745.000000) translate(0,12)">10kV凹利乍0241隔离开关与现场状态不一致</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_27a5ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1600.000000 -1376.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_27ad710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1601.000000 -1343.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ade50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -1021.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27af690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2149.000000 -949.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27af690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2149.000000 -949.000000) translate(0,27)">SZ9 4000/37.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27b0c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2788.000000 -944.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27b0c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2788.000000 -944.000000) translate(0,27)">SZ11 5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2907970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -395.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2909010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1473.000000 -435.000000) translate(0,17)">6711213</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_290a270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -405.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_290a270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -405.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290c690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2179.000000 -975.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d0460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3386.000000 -846.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d06b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3386.000000 -861.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d08f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3386.000000 -877.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d0b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3392.000000 -829.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d0d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3398.000000 -769.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d0fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3378.000000 -814.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d11f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3378.000000 -798.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d1430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3378.000000 -783.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d1670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1782.000000 -851.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d18b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1782.000000 -866.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d1af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1782.000000 -882.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d1d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.000000 -834.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d1f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1794.000000 -774.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d21b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 -819.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d23f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 -803.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d2630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 -788.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d2870" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 3006.326733 -1346.000000) translate(0,15)">线路TV</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-141508">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2264.200000 -1014.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25269" ObjectName="SW-YR_LTS.YR_LTS_301BK"/>
     <cge:Meas_Ref ObjectId="141508"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.333333 -1194.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25256" ObjectName="SW-YR_LTS.YR_LTS_321BK"/>
     <cge:Meas_Ref ObjectId="141435"/>
    <cge:TPSR_Ref TObjectID="25256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2557.000000 -833.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25303" ObjectName="SW-YR_LTS.YR_LTS_012BK"/>
     <cge:Meas_Ref ObjectId="141759"/>
    <cge:TPSR_Ref TObjectID="25303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2220.000000 -611.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25279" ObjectName="SW-YR_LTS.YR_LTS_023BK"/>
     <cge:Meas_Ref ObjectId="141651"/>
    <cge:TPSR_Ref TObjectID="25279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.200000 -1013.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25274" ObjectName="SW-YR_LTS.YR_LTS_302BK"/>
     <cge:Meas_Ref ObjectId="141577"/>
    <cge:TPSR_Ref TObjectID="25274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141555">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2263.200000 -814.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25271" ObjectName="SW-YR_LTS.YR_LTS_001BK"/>
     <cge:Meas_Ref ObjectId="141555"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.200000 -812.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25276" ObjectName="SW-YR_LTS.YR_LTS_002BK"/>
     <cge:Meas_Ref ObjectId="141624"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141453">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2568.333333 -1194.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25260" ObjectName="SW-YR_LTS.YR_LTS_322BK"/>
     <cge:Meas_Ref ObjectId="141453"/>
    <cge:TPSR_Ref TObjectID="25260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141476">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.333333 -1197.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25267" ObjectName="SW-YR_LTS.YR_LTS_323BK"/>
     <cge:Meas_Ref ObjectId="141476"/>
    <cge:TPSR_Ref TObjectID="25267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 -612.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25283" ObjectName="SW-YR_LTS.YR_LTS_022BK"/>
     <cge:Meas_Ref ObjectId="141669"/>
    <cge:TPSR_Ref TObjectID="25283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2438.000000 -611.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25287" ObjectName="SW-YR_LTS.YR_LTS_024BK"/>
     <cge:Meas_Ref ObjectId="141687"/>
    <cge:TPSR_Ref TObjectID="25287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2698.000000 -613.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25291" ObjectName="SW-YR_LTS.YR_LTS_025BK"/>
     <cge:Meas_Ref ObjectId="141705"/>
    <cge:TPSR_Ref TObjectID="25291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141723">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 -612.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25295" ObjectName="SW-YR_LTS.YR_LTS_026BK"/>
     <cge:Meas_Ref ObjectId="141723"/>
    <cge:TPSR_Ref TObjectID="25295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3179.000000 -612.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25299" ObjectName="SW-YR_LTS.YR_LTS_027BK"/>
     <cge:Meas_Ref ObjectId="141741"/>
    <cge:TPSR_Ref TObjectID="25299"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LC" endPointId="0" endStationName="YR_LTS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_lianlongxian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2274,-1380 2274,-1418 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37846" ObjectName="AC-35kV.LN_lianlongxian"/>
    <cge:TPSR_Ref TObjectID="37846_SS-189"/></metadata>
   <polyline fill="none" opacity="0" points="2274,-1380 2274,-1418 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YR_LTS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yongmenglongTlts" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2913,-1404 2913,-1433 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37841" ObjectName="AC-35kV.LN_yongmenglongTlts"/>
    <cge:TPSR_Ref TObjectID="37841_SS-189"/></metadata>
   <polyline fill="none" opacity="0" points="2913,-1404 2913,-1433 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2816860">
    <use class="BV-10KV" transform="matrix(0.857143 -0.000000 0.000000 1.000000 1946.000000 -979.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2826aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1901.666667 -955.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27e7130">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 2131.000000 -1300.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_285b8a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1949.000000 -890.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2796380">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2374.000000 -1359.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2778be0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3257.961111 -481.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2814710">
    <use class="BV-0KV" transform="matrix(0.857143 -0.000000 0.000000 1.000000 3242.000000 -979.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_286b080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3197.666667 -955.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2748940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3245.000000 -890.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27a2d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2018.000000 -455.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2875280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2442.000000 -452.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28446d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2702.000000 -457.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2866a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2942.000000 -455.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27bd5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3183.000000 -455.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2776150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2224.000000 -449.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28569c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2483.000000 -1344.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2858c70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2825.000000 -1338.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b8030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.000000 -386.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b8c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2254.000000 -384.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b99b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2472.000000 -385.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ba720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2732.000000 -384.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bb490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2972.000000 -382.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bc200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3213.000000 -384.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2382.000000 -1237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25256"/>
     <cge:Term_Ref ObjectID="35569"/>
    <cge:TPSR_Ref TObjectID="25256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141305" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2382.000000 -1237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25256"/>
     <cge:Term_Ref ObjectID="35569"/>
    <cge:TPSR_Ref TObjectID="25256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2382.000000 -1237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25256"/>
     <cge:Term_Ref ObjectID="35569"/>
    <cge:TPSR_Ref TObjectID="25256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -1067.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141321" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -1067.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -1067.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141326" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -861.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141326" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141327" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -861.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141323" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -861.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -1237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25260"/>
     <cge:Term_Ref ObjectID="35577"/>
    <cge:TPSR_Ref TObjectID="25260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141310" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -1237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25260"/>
     <cge:Term_Ref ObjectID="35577"/>
    <cge:TPSR_Ref TObjectID="25260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -1237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25260"/>
     <cge:Term_Ref ObjectID="35577"/>
    <cge:TPSR_Ref TObjectID="25260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -1237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25267"/>
     <cge:Term_Ref ObjectID="35591"/>
    <cge:TPSR_Ref TObjectID="25267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141315" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -1237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25267"/>
     <cge:Term_Ref ObjectID="35591"/>
    <cge:TPSR_Ref TObjectID="25267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3114.000000 -1237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25267"/>
     <cge:Term_Ref ObjectID="35591"/>
    <cge:TPSR_Ref TObjectID="25267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141334" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -1059.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141334" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25274"/>
     <cge:Term_Ref ObjectID="35605"/>
    <cge:TPSR_Ref TObjectID="25274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141335" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -1059.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25274"/>
     <cge:Term_Ref ObjectID="35605"/>
    <cge:TPSR_Ref TObjectID="25274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141331" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -1059.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141331" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25274"/>
     <cge:Term_Ref ObjectID="35605"/>
    <cge:TPSR_Ref TObjectID="25274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141340" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -863.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141340" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141341" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -863.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141337" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -863.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141337" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2024.000000 -322.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25283"/>
     <cge:Term_Ref ObjectID="35623"/>
    <cge:TPSR_Ref TObjectID="25283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141386" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2024.000000 -322.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25283"/>
     <cge:Term_Ref ObjectID="35623"/>
    <cge:TPSR_Ref TObjectID="25283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141382" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2024.000000 -322.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141382" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25283"/>
     <cge:Term_Ref ObjectID="35623"/>
    <cge:TPSR_Ref TObjectID="25283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141380" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -322.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25279"/>
     <cge:Term_Ref ObjectID="35615"/>
    <cge:TPSR_Ref TObjectID="25279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -322.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25279"/>
     <cge:Term_Ref ObjectID="35615"/>
    <cge:TPSR_Ref TObjectID="25279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141377" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -322.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141377" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25279"/>
     <cge:Term_Ref ObjectID="35615"/>
    <cge:TPSR_Ref TObjectID="25279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141390" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -322.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25287"/>
     <cge:Term_Ref ObjectID="35631"/>
    <cge:TPSR_Ref TObjectID="25287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -322.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25287"/>
     <cge:Term_Ref ObjectID="35631"/>
    <cge:TPSR_Ref TObjectID="25287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -322.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25287"/>
     <cge:Term_Ref ObjectID="35631"/>
    <cge:TPSR_Ref TObjectID="25287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2708.000000 -322.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25291"/>
     <cge:Term_Ref ObjectID="35639"/>
    <cge:TPSR_Ref TObjectID="25291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141396" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2708.000000 -322.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25291"/>
     <cge:Term_Ref ObjectID="35639"/>
    <cge:TPSR_Ref TObjectID="25291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141392" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2708.000000 -322.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25291"/>
     <cge:Term_Ref ObjectID="35639"/>
    <cge:TPSR_Ref TObjectID="25291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -322.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25295"/>
     <cge:Term_Ref ObjectID="35647"/>
    <cge:TPSR_Ref TObjectID="25295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -322.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25295"/>
     <cge:Term_Ref ObjectID="35647"/>
    <cge:TPSR_Ref TObjectID="25295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -322.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25295"/>
     <cge:Term_Ref ObjectID="35647"/>
    <cge:TPSR_Ref TObjectID="25295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -322.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25299"/>
     <cge:Term_Ref ObjectID="35655"/>
    <cge:TPSR_Ref TObjectID="25299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -322.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25299"/>
     <cge:Term_Ref ObjectID="35655"/>
    <cge:TPSR_Ref TObjectID="25299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -322.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25299"/>
     <cge:Term_Ref ObjectID="35655"/>
    <cge:TPSR_Ref TObjectID="25299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-141330" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -977.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141330" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25312"/>
     <cge:Term_Ref ObjectID="35684"/>
    <cge:TPSR_Ref TObjectID="25312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-141329" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -977.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25312"/>
     <cge:Term_Ref ObjectID="35684"/>
    <cge:TPSR_Ref TObjectID="25312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-141344" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -978.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141344" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25313"/>
     <cge:Term_Ref ObjectID="35688"/>
    <cge:TPSR_Ref TObjectID="25313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-141343" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3041.000000 -978.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141343" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25313"/>
     <cge:Term_Ref ObjectID="35688"/>
    <cge:TPSR_Ref TObjectID="25313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-141410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2593.000000 -934.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25303"/>
     <cge:Term_Ref ObjectID="35663"/>
    <cge:TPSR_Ref TObjectID="25303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-141411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2593.000000 -934.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25303"/>
     <cge:Term_Ref ObjectID="35663"/>
    <cge:TPSR_Ref TObjectID="25303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-141407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2593.000000 -934.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25303"/>
     <cge:Term_Ref ObjectID="35663"/>
    <cge:TPSR_Ref TObjectID="25303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-141345" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1948.000000 -1194.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141345" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-141346" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1948.000000 -1194.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141346" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-141347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1948.000000 -1194.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-141348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1948.000000 -1194.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-141352" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1948.000000 -1194.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141352" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25269"/>
     <cge:Term_Ref ObjectID="35595"/>
    <cge:TPSR_Ref TObjectID="25269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-141353" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141353" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-141354" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141354" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-141355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-141359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-141356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-141357" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141357" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-141358" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141358" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-141360" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -880.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141360" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25271"/>
     <cge:Term_Ref ObjectID="35599"/>
    <cge:TPSR_Ref TObjectID="25271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-141369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-141370" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141370" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-141371" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141371" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-141375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-141372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-141373" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141373" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-141374" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-141376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3441.000000 -876.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="141376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25276"/>
     <cge:Term_Ref ObjectID="35609"/>
    <cge:TPSR_Ref TObjectID="25276"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="1412" y="-1382"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1363" y="-1399"/></g>
   <g href="35kV龙头山变35kV莲龙线321断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2284" y="-1223"/></g>
   <g href="35kV龙头山变35kV龙他猛线322断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2587" y="-1223"/></g>
   <g href="35kV龙头山变35kV永龙线323断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2923" y="-1226"/></g>
   <g href="35kV龙头山变10kV母联分段012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2567" y="-867"/></g>
   <g href="35kV龙头山变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="2836" y="-966"/></g>
   <g href="35kV龙头山变10kV龙城Ⅲ回线022断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2032" y="-641"/></g>
   <g href="35kV龙头山变10kV太平地线023断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2238" y="-640"/></g>
   <g href="35kV龙头山变10kV凹利乍线024断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2456" y="-640"/></g>
   <g href="35kV龙头山变10kV龙城Ⅳ回线025断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2716" y="-642"/></g>
   <g href="35kV龙头山变10kV龙城Ⅴ回线026断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2956" y="-641"/></g>
   <g href="35kV龙头山变10kV龙城Ⅵ回线027断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3197" y="-640"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1589" y="-1384"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1589" y="-1351"/></g>
   <g href="35kV龙头山变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="63" x="1370" y="-1022"/></g>
   <g href="35kV龙头山变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2179" y="-975"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287b1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1882.000000 1146.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3217b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1898.000000 1130.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2869690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1890.000000 1161.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2869bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1890.000000 1176.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2869e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1890.000000 1190.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bedd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.000000 1237.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27bf8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2314.000000 1222.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271e470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 1207.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271f230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2630.000000 1237.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271f3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2619.000000 1222.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271f590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2644.000000 1207.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283f200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3058.000000 1237.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283f450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3047.000000 1222.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283f660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3072.000000 1207.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283fa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.000000 1060.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283fce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2314.000000 1045.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_283fef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 1030.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28402e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.000000 859.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2840570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2314.000000 844.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2840780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 829.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2850ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2985.000000 1056.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2850e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2974.000000 1041.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28510a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2999.000000 1026.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28514c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2985.000000 858.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2851780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2974.000000 843.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28519c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2999.000000 828.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2851de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2538.000000 933.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2720960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2527.000000 918.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2720b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2552.000000 903.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2720fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1969.000000 322.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2721260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1958.000000 307.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27214a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1983.000000 292.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27217d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2311.000000 959.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27222f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2311.000000 974.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2971.000000 960.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277c6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2971.000000 975.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2904b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2171.000000 322.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2905070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2160.000000 307.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29052b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2185.000000 292.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29056d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2390.000000 322.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2905990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2379.000000 307.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2905bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2404.000000 292.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2905ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2651.000000 322.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29062b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2640.000000 307.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29064f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2665.000000 292.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2895.000000 322.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2884.000000 307.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2906e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2909.000000 292.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2907230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3137.000000 322.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29074f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3126.000000 307.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2907730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3151.000000 292.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YR_LTS.YR_LTS_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35691"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 0.724490 -0.869565 -0.000000 3022.000000 -1241.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 0.724490 -0.869565 -0.000000 3022.000000 -1241.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25314" ObjectName="TF-YR_LTS.YR_LTS_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_LTS.YR_LTS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35687"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2878.000000 -918.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2878.000000 -918.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25313" ObjectName="TF-YR_LTS.YR_LTS_2T"/>
    <cge:TPSR_Ref TObjectID="25313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_LTS.YR_LTS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35683"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2234.000000 -908.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2234.000000 -908.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25312" ObjectName="TF-YR_LTS.YR_LTS_1T"/>
    <cge:TPSR_Ref TObjectID="25312"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="1412" y="-1382"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="1412" y="-1382"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1363" y="-1399"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1363" y="-1399"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2284" y="-1223"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2284" y="-1223"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2587" y="-1223"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2587" y="-1223"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2923" y="-1226"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2923" y="-1226"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2567" y="-867"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2567" y="-867"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="2836" y="-966"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="2836" y="-966"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2032" y="-641"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2032" y="-641"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2238" y="-640"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2238" y="-640"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2456" y="-640"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2456" y="-640"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2716" y="-642"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2716" y="-642"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2956" y="-641"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2956" y="-641"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3197" y="-640"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3197" y="-640"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1589" y="-1384"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1589" y="-1384"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1589" y="-1351"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1589" y="-1351"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="63" x="1370" y="-1022"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="63" x="1370" y="-1022"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2179" y="-975"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2179" y="-975"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_27f5ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2618.000000 -1281.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2737200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2970.000000 -1322.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290ceb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2318.000000 -1268.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1400.000000 -1323.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217880" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1462.000000 -1146.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217880" ObjectName="YR_LTS:YR_LTS_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217880" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1460.000000 -1228.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217880" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217880" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -1189.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217880" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_LTS"/>
</svg>