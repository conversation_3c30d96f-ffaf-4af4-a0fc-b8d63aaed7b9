<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-86" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1199 1997 1154">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape86_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="54" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="39" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="72" y2="72"/>
    <polyline DF8003:Layer="PUBLIC" points="16,84 22,71 9,71 16,84 16,83 16,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="57" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="12" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="19" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape86_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,43 41,43 41,72 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="43" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="43" y2="38"/>
   </symbol>
   <symbol id="transformer2:shape87_0">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="41" x2="38" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape87_1">
    <circle cx="15" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape133">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="18" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="41" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="38" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="39" y1="46" y2="46"/>
    <circle cx="7" cy="37" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="25" r="7.5" stroke-width="1"/>
    <rect height="14" stroke-width="1" width="8" x="32" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="35" y2="44"/>
    <polyline points="42,23 30,32 30,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="21" y1="29" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="21" y1="35" y2="33"/>
    <circle cx="17" cy="31" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="37" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="36" y1="11" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape15">
    <ellipse cx="23" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="2" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="4" y="14"/>
    <ellipse cx="34" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="23" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="34" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="24,36 8,36 8,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="3" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="21" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="24" x2="24" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="35" x2="35" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="32" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="37" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="34" x2="32" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="35" x2="37" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="37" x2="32" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="23" x2="23" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="20" x2="23" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="26" y2="24"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20953d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2095da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2096720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2097400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2098600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2098f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2099a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_209a370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_209a9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_209b360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_209b360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_209c8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_209c8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_209d540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_209f1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_209fe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_20a0ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20a14c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20a2c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20a3450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20a3b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20a4560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20a5740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20a60c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20a6bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_20a7570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_20a8a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_20a95c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_20aa830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20ab4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20b9cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20acde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_20ade40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_20af390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1164" width="2007" x="3111" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4010" x2="4010" y1="-508" y2="-499"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4009" x2="4011" y1="-499" y2="-499"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4010" x2="4010" y1="-471" y2="-481"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4976" x2="4976" y1="-614" y2="-614"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-59578">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -512.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11354" ObjectName="SW-LF_YJZ.LF_YJZ_012BK"/>
     <cge:Meas_Ref ObjectId="59578"/>
    <cge:TPSR_Ref TObjectID="11354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59522">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.006522 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11319" ObjectName="SW-LF_YJZ.LF_YJZ_001BK"/>
     <cge:Meas_Ref ObjectId="59522"/>
    <cge:TPSR_Ref TObjectID="11319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11349" ObjectName="SW-LF_YJZ.LF_YJZ_064BK"/>
     <cge:Meas_Ref ObjectId="59566"/>
    <cge:TPSR_Ref TObjectID="11349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59574">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.596639 -340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11350" ObjectName="SW-LF_YJZ.LF_YJZ_061BK"/>
     <cge:Meas_Ref ObjectId="59574"/>
    <cge:TPSR_Ref TObjectID="11350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59550">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.333333 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11343" ObjectName="SW-LF_YJZ.LF_YJZ_065BK"/>
     <cge:Meas_Ref ObjectId="59550"/>
    <cge:TPSR_Ref TObjectID="11343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59558">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.215686 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11346" ObjectName="SW-LF_YJZ.LF_YJZ_062BK"/>
     <cge:Meas_Ref ObjectId="59558"/>
    <cge:TPSR_Ref TObjectID="11346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.333333 -337.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11336" ObjectName="SW-LF_YJZ.LF_YJZ_066BK"/>
     <cge:Meas_Ref ObjectId="59534"/>
    <cge:TPSR_Ref TObjectID="11336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59542">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.526611 -339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11340" ObjectName="SW-LF_YJZ.LF_YJZ_063BK"/>
     <cge:Meas_Ref ObjectId="59542"/>
    <cge:TPSR_Ref TObjectID="11340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181225">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -914.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27628" ObjectName="SW-LF_YJZ.LF_YJZ_372BK"/>
     <cge:Meas_Ref ObjectId="181225"/>
    <cge:TPSR_Ref TObjectID="27628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181513">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 -914.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27637" ObjectName="SW-LF_YJZ.LF_YJZ_371BK"/>
     <cge:Meas_Ref ObjectId="181513"/>
    <cge:TPSR_Ref TObjectID="27637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59521">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -687.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11318" ObjectName="SW-LF_YJZ.LF_YJZ_301BK"/>
     <cge:Meas_Ref ObjectId="59521"/>
    <cge:TPSR_Ref TObjectID="11318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59529">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -691.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11325" ObjectName="SW-LF_YJZ.LF_YJZ_302BK"/>
     <cge:Meas_Ref ObjectId="59529"/>
    <cge:TPSR_Ref TObjectID="11325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11326" ObjectName="SW-LF_YJZ.LF_YJZ_002BK"/>
     <cge:Meas_Ref ObjectId="59530"/>
    <cge:TPSR_Ref TObjectID="11326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.333333 -335.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27634" ObjectName="SW-LF_YJZ.LF_YJZ_067BK"/>
     <cge:Meas_Ref ObjectId="181280"/>
    <cge:TPSR_Ref TObjectID="27634"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_17c84d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -1106.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17c9be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -1105.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17612b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3568.000000 -580.000000)" xlink:href="#voltageTransformer:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1763680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -581.000000)" xlink:href="#voltageTransformer:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181e760">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -615.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HS" endPointId="0" endStationName="LF_YJZ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_HongYang" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4520,-1121 4520,-1169 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37738" ObjectName="AC-35kV.LN_HongYang"/>
    <cge:TPSR_Ref TObjectID="37738_SS-86"/></metadata>
   <polyline fill="none" opacity="0" points="4520,-1121 4520,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_YJZ" endPointId="0" endStationName="PAS_T2" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_JinYangTyjz" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3964,-1116 3964,-1156 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18141" ObjectName="AC-35kV.LN_JinYangTyjz"/>
    <cge:TPSR_Ref TObjectID="18141_SS-86"/></metadata>
   <polyline fill="none" opacity="0" points="3964,-1116 3964,-1156 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJZ.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 -152.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37680" ObjectName="EC-LF_YJZ.062Ld"/>
    <cge:TPSR_Ref TObjectID="37680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJZ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 -163.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37681" ObjectName="EC-LF_YJZ.063Ld"/>
    <cge:TPSR_Ref TObjectID="37681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJZ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -162.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37682" ObjectName="EC-LF_YJZ.064Ld"/>
    <cge:TPSR_Ref TObjectID="37682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJZ.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -167.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37683" ObjectName="EC-LF_YJZ.065Ld"/>
    <cge:TPSR_Ref TObjectID="37683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJZ.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -161.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37684" ObjectName="EC-LF_YJZ.066Ld"/>
    <cge:TPSR_Ref TObjectID="37684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJZ.067Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42902" ObjectName="EC-LF_YJZ.067Ld"/>
    <cge:TPSR_Ref TObjectID="42902"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_183a820" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3686.000000 -263.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17a35d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -1023.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f57f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -989.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f64a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 -828.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fa370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -984.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fab40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -826.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e0e90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -1026.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1766f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.000000 -728.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17679e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4876.000000 -781.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1768470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -769.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1905620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-496 4192,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11355@1" ObjectIDZND0="11354@0" Pin0InfoVect0LinkObjId="SW-59578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-496 4192,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_187cb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-446 4010,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27627@0" ObjectIDZND0="11328@0" Pin0InfoVect0LinkObjId="SW-58857_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_189c9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-446 4010,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1889850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-391 4334,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17667@0" ObjectIDZND0="11349@1" Pin0InfoVect0LinkObjId="SW-59566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-391 4334,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_187ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-223 4347,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37682@x" ObjectIDND1="17668@x" ObjectIDZND0="g_187f000@0" Pin0InfoVect0LinkObjId="g_187f000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.064Ld_0" Pin1InfoVect1LinkObjId="SW-59569_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-223 4347,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18a19d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-446 3628,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27627@0" ObjectIDZND0="11351@1" Pin0InfoVect0LinkObjId="SW-59011_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_189c9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-446 3628,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1895260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-393 3628,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11351@0" ObjectIDZND0="11350@1" Pin0InfoVect0LinkObjId="SW-59574_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59011_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-393 3628,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1896f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-348 3628,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11350@0" ObjectIDZND0="11352@1" Pin0InfoVect0LinkObjId="SW-59012_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-348 3628,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1897640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-269 3642,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="11352@x" ObjectIDND1="41829@x" ObjectIDZND0="11353@0" Pin0InfoVect0LinkObjId="SW-59013_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-59012_0" Pin1InfoVect1LinkObjId="CB-LF_YJZ.LF_YJZ_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-269 3642,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1816ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3678,-269 3690,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11353@1" ObjectIDZND0="g_183a820@0" Pin0InfoVect0LinkObjId="g_183a820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59013_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3678,-269 3690,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_185afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-391 4461,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11344@0" ObjectIDZND0="11343@1" Pin0InfoVect0LinkObjId="SW-59550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-391 4461,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_185b1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-224 4471,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37683@x" ObjectIDND1="11345@x" ObjectIDZND0="g_185b3b0@0" Pin0InfoVect0LinkObjId="g_185b3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.065Ld_0" Pin1InfoVect1LinkObjId="SW-59005_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-224 4471,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_185d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-225 3829,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37680@x" ObjectIDND1="11348@x" ObjectIDZND0="g_185d5e0@0" Pin0InfoVect0LinkObjId="g_185d5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.062Ld_0" Pin1InfoVect1LinkObjId="SW-59008_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-225 3829,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1836870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-390 4602,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11337@0" ObjectIDZND0="11336@1" Pin0InfoVect0LinkObjId="SW-59534_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58962_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-390 4602,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18603c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-223 4613,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37684@x" ObjectIDND1="11338@x" ObjectIDZND0="g_18605b0@0" Pin0InfoVect0LinkObjId="g_18605b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.066Ld_0" Pin1InfoVect1LinkObjId="SW-58963_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-223 4613,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_180c270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-392 4094,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11341@0" ObjectIDZND0="11340@1" Pin0InfoVect0LinkObjId="SW-59542_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-392 4094,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1868be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-223 4104,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37681@x" ObjectIDND1="11342@x" ObjectIDZND0="g_1868e00@0" Pin0InfoVect0LinkObjId="g_1868e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.063Ld_0" Pin1InfoVect1LinkObjId="SW-59002_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-223 4104,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17fbf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-571 4807,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_1763680@0" Pin0InfoVect0LinkObjId="g_1763680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c84d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-571 4807,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17fcd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-514 4807,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17fc180@0" ObjectIDZND0="0@x" ObjectIDZND1="17666@x" Pin0InfoVect0LinkObjId="g_17c84d0_0" Pin0InfoVect1LinkObjId="SW-228033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17fc180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-514 4807,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17fcf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-514 4807,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_17fc180@0" ObjectIDND1="17666@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_17c84d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17fc180_0" Pin1InfoVect1LinkObjId="SW-228033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-514 4807,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_189c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-467 3576,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17665@0" ObjectIDZND0="27627@0" Pin0InfoVect0LinkObjId="g_1854ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228024_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-467 3576,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17dbfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-570 3576,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_17612b0@0" Pin0InfoVect0LinkObjId="g_17612b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c84d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-570 3576,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17dcdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3608,-514 3576,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17dc1d0@0" ObjectIDZND0="17665@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-228024_0" Pin0InfoVect1LinkObjId="g_17c84d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17dc1d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3608,-514 3576,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17dcfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-503 3576,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="17665@1" ObjectIDZND0="g_17dc1d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_17dc1d0_0" Pin0InfoVect1LinkObjId="g_17c84d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-503 3576,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17dd200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-513 3576,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="17665@x" ObjectIDND1="g_17dc1d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_17c84d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228024_0" Pin1InfoVect1LinkObjId="g_17dc1d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-513 3576,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1854d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-297 3628,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="11352@0" ObjectIDZND0="11353@x" ObjectIDZND1="41829@x" Pin0InfoVect0LinkObjId="SW-59013_0" Pin0InfoVect1LinkObjId="CB-LF_YJZ.LF_YJZ_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59012_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-297 3628,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1854ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-460 4192,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11355@0" ObjectIDZND0="27627@0" Pin0InfoVect0LinkObjId="g_189c9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59015_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-460 4192,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1855250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-428 4094,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11341@1" ObjectIDZND0="27627@0" Pin0InfoVect0LinkObjId="g_189c9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-428 4094,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c22d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3853,-477 3853,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="27627@0" Pin0InfoVect0LinkObjId="g_189c9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c84d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3853,-477 3853,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1833410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-545 3854,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_17c84d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c84d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-545 3854,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c8270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-922 4520,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27628@0" ObjectIDZND0="27629@x" ObjectIDZND1="27630@x" Pin0InfoVect0LinkObjId="SW-181226_0" Pin0InfoVect1LinkObjId="SW-181227_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181225_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-922 4520,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-973 4575,-973 4575,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27628@x" ObjectIDND1="27631@x" ObjectIDZND0="27632@0" Pin0InfoVect0LinkObjId="SW-181229_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181225_0" Pin1InfoVect1LinkObjId="SW-181228_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-973 4575,-973 4575,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18c8730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-973 4520,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27632@x" ObjectIDND1="27631@x" ObjectIDZND0="27628@1" Pin0InfoVect0LinkObjId="SW-181225_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181229_0" Pin1InfoVect1LinkObjId="SW-181228_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-973 4520,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17bb6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-1015 4520,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27631@0" ObjectIDZND0="27632@x" ObjectIDZND1="27628@x" Pin0InfoVect0LinkObjId="SW-181229_0" Pin0InfoVect1LinkObjId="SW-181225_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181228_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-1015 4520,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17bb910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-1068 4491,-1068 4491,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27631@x" ObjectIDND1="g_17c9be0@0" ObjectIDND2="g_17f9110@0" ObjectIDZND0="27633@1" Pin0InfoVect0LinkObjId="SW-181230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-181228_0" Pin1InfoVect1LinkObjId="g_17c9be0_0" Pin1InfoVect2LinkObjId="g_17f9110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-1068 4491,-1068 4491,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17bbb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-1068 4520,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27633@x" ObjectIDND1="g_17c9be0@0" ObjectIDND2="g_17f9110@0" ObjectIDZND0="27631@1" Pin0InfoVect0LinkObjId="SW-181228_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-181230_0" Pin1InfoVect1LinkObjId="g_17c9be0_0" Pin1InfoVect2LinkObjId="g_17f9110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-1068 4520,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17be5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4519,-1090 4567,-1090 4567,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="27631@x" ObjectIDND1="27633@x" ObjectIDND2="g_17f9110@0" ObjectIDZND0="g_17c9be0@0" Pin0InfoVect0LinkObjId="g_17c9be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-181228_0" Pin1InfoVect1LinkObjId="SW-181230_0" Pin1InfoVect2LinkObjId="g_17f9110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4519,-1090 4567,-1090 4567,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_187ab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-1069 3935,-1069 3935,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_17f4490@0" ObjectIDND1="g_17c84d0@0" ObjectIDND2="18141@1" ObjectIDZND0="27642@1" Pin0InfoVect0LinkObjId="SW-181518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17f4490_0" Pin1InfoVect1LinkObjId="g_17c84d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-1069 3935,-1069 3935,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f3250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-1083 3964,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_17f4490@0" ObjectIDZND0="27642@x" ObjectIDZND1="27640@x" ObjectIDZND2="g_17c84d0@0" Pin0InfoVect0LinkObjId="SW-181518_0" Pin0InfoVect1LinkObjId="SW-181516_0" Pin0InfoVect2LinkObjId="g_17c84d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f4490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-1083 3964,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f3db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-1116 3964,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="18141@1" ObjectIDZND0="27642@x" ObjectIDZND1="27640@x" ObjectIDZND2="g_17f4490@0" Pin0InfoVect0LinkObjId="SW-181518_0" Pin0InfoVect1LinkObjId="SW-181516_0" Pin0InfoVect2LinkObjId="g_17f4490_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-1116 3964,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f3fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-1083 3964,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17f4490@0" ObjectIDND1="g_17c84d0@0" ObjectIDND2="18141@1" ObjectIDZND0="27642@x" ObjectIDZND1="27640@x" Pin0InfoVect0LinkObjId="SW-181518_0" Pin0InfoVect1LinkObjId="SW-181516_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17f4490_0" Pin1InfoVect1LinkObjId="g_17c84d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-1083 3964,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-1083 4011,-1083 4011,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="27642@x" ObjectIDND1="27640@x" ObjectIDND2="g_17f4490@0" ObjectIDZND0="g_17c84d0@0" Pin0InfoVect0LinkObjId="g_17c84d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-181518_0" Pin1InfoVect1LinkObjId="SW-181516_0" Pin1InfoVect2LinkObjId="g_17f4490_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-1083 4011,-1083 4011,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f6240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-1007 3935,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17f57f0@0" ObjectIDZND0="27642@0" Pin0InfoVect0LinkObjId="SW-181518_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f57f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-1007 3935,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f8b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-1126 4520,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="37738@1" ObjectIDZND0="27631@x" ObjectIDZND1="27633@x" ObjectIDZND2="g_17c9be0@0" Pin0InfoVect0LinkObjId="SW-181228_0" Pin0InfoVect1LinkObjId="SW-181230_0" Pin0InfoVect2LinkObjId="g_17c9be0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-1126 4520,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f8d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-1090 4520,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17c9be0@0" ObjectIDND1="g_17f9110@0" ObjectIDND2="37738@1" ObjectIDZND0="27631@x" ObjectIDZND1="27633@x" Pin0InfoVect0LinkObjId="SW-181228_0" Pin0InfoVect1LinkObjId="SW-181230_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17c9be0_0" Pin1InfoVect1LinkObjId="g_17f9110_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-1090 4520,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17f8f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-1090 4519,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_17f9110@0" ObjectIDZND0="27631@x" ObjectIDZND1="27633@x" ObjectIDZND2="g_17c9be0@0" Pin0InfoVect0LinkObjId="SW-181228_0" Pin0InfoVect1LinkObjId="SW-181230_0" Pin0InfoVect2LinkObjId="g_17c9be0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f9110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-1090 4519,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e1920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-1002 4491,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17fa370@0" ObjectIDZND0="27633@0" Pin0InfoVect0LinkObjId="SW-181230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17fa370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-1002 4491,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e1b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-1016 4575,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27632@1" ObjectIDZND0="g_17e0e90@0" Pin0InfoVect0LinkObjId="g_17e0e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181229_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-1016 4575,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17c74b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-898 3776,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_17c84d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c84d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-898 3776,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17c7710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-836 3776,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="27625@0" Pin0InfoVect0LinkObjId="g_17665d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c84d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-836 3776,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1776d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-524 4010,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11326@0" ObjectIDZND0="11328@1" Pin0InfoVect0LinkObjId="SW-58857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-524 4010,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17775a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-809 4010,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27625@0" ObjectIDZND0="11327@1" Pin0InfoVect0LinkObjId="SW-58856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c7710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-809 4010,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1777800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-747 4010,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11327@0" ObjectIDZND0="11325@1" Pin0InfoVect0LinkObjId="SW-59529_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-747 4010,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1779280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-699 4010,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="11325@0" ObjectIDZND0="11426@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-699 4010,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17794a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-585 4010,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="11426@0" ObjectIDZND0="11326@1" Pin0InfoVect0LinkObjId="SW-59530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1779280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-585 4010,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-809 4620,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27625@0" ObjectIDZND0="11320@1" Pin0InfoVect0LinkObjId="SW-58849_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c7710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-809 4620,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175d0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-745 4620,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="11320@0" ObjectIDZND0="11321@x" ObjectIDZND1="11318@x" Pin0InfoVect0LinkObjId="SW-58850_0" Pin0InfoVect1LinkObjId="SW-59521_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-745 4620,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175d2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-734 4595,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11320@x" ObjectIDND1="11318@x" ObjectIDZND0="11321@0" Pin0InfoVect0LinkObjId="SW-58850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58849_0" Pin1InfoVect1LinkObjId="SW-59521_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-734 4595,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175d500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-734 4544,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11321@1" ObjectIDZND0="g_1766f50@0" Pin0InfoVect0LinkObjId="g_1766f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-734 4544,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-734 4620,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11321@x" ObjectIDND1="11320@x" ObjectIDZND0="11318@1" Pin0InfoVect0LinkObjId="SW-59521_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58850_0" Pin1InfoVect1LinkObjId="SW-58849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-734 4620,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_175f060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-695 4620,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="11318@0" ObjectIDZND0="11425@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-695 4620,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_175f280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-578 4620,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="11425@0" ObjectIDZND0="11319@1" Pin0InfoVect0LinkObjId="SW-59522_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_175f060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-578 4620,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17665d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-776 4974,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="11331@1" ObjectIDZND0="27625@0" ObjectIDZND1="11333@x" Pin0InfoVect0LinkObjId="g_17c7710_0" Pin0InfoVect1LinkObjId="SW-58909_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58907_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-776 4974,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1766830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-787 4974,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="11331@x" ObjectIDND1="11333@x" ObjectIDZND0="27625@0" Pin0InfoVect0LinkObjId="g_17c7710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58907_0" Pin1InfoVect1LinkObjId="SW-58909_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-787 4974,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1766a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-787 4950,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="11331@x" ObjectIDND1="27625@0" ObjectIDZND0="11333@1" Pin0InfoVect0LinkObjId="SW-58909_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58907_0" Pin1InfoVect1LinkObjId="g_17c7710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-787 4950,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1766cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-787 4894,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11333@0" ObjectIDZND0="g_17679e0@0" Pin0InfoVect0LinkObjId="g_17679e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58909_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-787 4894,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181bfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-707 4974,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_181c940@0" ObjectIDND1="g_181d8d0@0" ObjectIDZND0="11331@x" ObjectIDZND1="11332@x" Pin0InfoVect0LinkObjId="SW-58907_0" Pin0InfoVect1LinkObjId="SW-58908_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_181c940_0" Pin1InfoVect1LinkObjId="g_181d8d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-707 4974,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181c220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-721 4974,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_181c940@0" ObjectIDND1="g_181d8d0@0" ObjectIDND2="11332@x" ObjectIDZND0="11331@0" Pin0InfoVect0LinkObjId="SW-58907_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_181c940_0" Pin1InfoVect1LinkObjId="g_181d8d0_0" Pin1InfoVect2LinkObjId="SW-58908_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-721 4974,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181c480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-721 5002,-721 5002,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_181c940@0" ObjectIDND1="g_181d8d0@0" ObjectIDND2="11331@x" ObjectIDZND0="11332@0" Pin0InfoVect0LinkObjId="SW-58908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_181c940_0" Pin1InfoVect1LinkObjId="g_181d8d0_0" Pin1InfoVect2LinkObjId="SW-58907_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-721 5002,-721 5002,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5002,-765 5002,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11332@1" ObjectIDZND0="g_1768470@0" Pin0InfoVect0LinkObjId="g_1768470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5002,-765 5002,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181d670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-696 4947,-707 4974,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_181c940@0" ObjectIDZND0="11331@x" ObjectIDZND1="11332@x" ObjectIDZND2="g_181d8d0@0" Pin0InfoVect0LinkObjId="SW-58907_0" Pin0InfoVect1LinkObjId="SW-58908_0" Pin0InfoVect2LinkObjId="g_181d8d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_181c940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-696 4947,-707 4974,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181e2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-707 4974,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="11331@x" ObjectIDND1="11332@x" ObjectIDND2="g_181c940@0" ObjectIDZND0="g_181d8d0@0" Pin0InfoVect0LinkObjId="g_181d8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-58907_0" Pin1InfoVect1LinkObjId="SW-58908_0" Pin1InfoVect2LinkObjId="g_181c940_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-707 4974,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_181e500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-667 4974,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_181d8d0@1" ObjectIDZND0="g_181e760@0" Pin0InfoVect0LinkObjId="g_181e760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_181d8d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-667 4974,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1821ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-502 4807,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17666@1" ObjectIDZND0="0@x" ObjectIDZND1="g_17fc180@0" Pin0InfoVect0LinkObjId="g_17c84d0_0" Pin0InfoVect1LinkObjId="g_17fc180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-502 4807,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1822090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-809 3964,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27625@0" ObjectIDZND0="27638@0" Pin0InfoVect0LinkObjId="SW-181514_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c7710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-809 3964,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1822280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-864 3964,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27638@1" ObjectIDZND0="27637@x" ObjectIDZND1="27639@x" Pin0InfoVect0LinkObjId="SW-181513_0" Pin0InfoVect1LinkObjId="SW-181515_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-864 3964,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1822470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-809 4520,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27625@0" ObjectIDZND0="27629@0" Pin0InfoVect0LinkObjId="SW-181226_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c7710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-809 4520,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18226a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-865 4520,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27629@1" ObjectIDZND0="27628@x" ObjectIDZND1="27630@x" Pin0InfoVect0LinkObjId="SW-181225_0" Pin0InfoVect1LinkObjId="SW-181227_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181226_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-865 4520,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18228d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4481,-844 4481,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17fab40@0" ObjectIDZND0="27630@0" Pin0InfoVect0LinkObjId="SW-181227_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17fab40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4481,-844 4481,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1822b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4481,-892 4481,-906 4520,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27630@1" ObjectIDZND0="27628@x" ObjectIDZND1="27629@x" Pin0InfoVect0LinkObjId="SW-181225_0" Pin0InfoVect1LinkObjId="SW-181226_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181227_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4481,-892 4481,-906 4520,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1822d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-907 3964,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27638@x" ObjectIDND1="27639@x" ObjectIDZND0="27637@0" Pin0InfoVect0LinkObjId="SW-181513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181514_0" Pin1InfoVect1LinkObjId="SW-181515_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-907 3964,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1822f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-949 3964,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27637@1" ObjectIDZND0="27640@x" ObjectIDZND1="27641@x" Pin0InfoVect0LinkObjId="SW-181516_0" Pin0InfoVect1LinkObjId="SW-181517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-949 3964,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1823190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3925,-846 3925,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17f64a0@0" ObjectIDZND0="27639@0" Pin0InfoVect0LinkObjId="SW-181515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f64a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3925,-846 3925,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18233f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3925,-894 3925,-906 3964,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27639@1" ObjectIDZND0="27638@x" ObjectIDZND1="27637@x" Pin0InfoVect0LinkObjId="SW-181514_0" Pin0InfoVect1LinkObjId="SW-181513_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3925,-894 3925,-906 3964,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1823650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-974 3964,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27637@x" ObjectIDND1="27641@x" ObjectIDZND0="27640@0" Pin0InfoVect0LinkObjId="SW-181516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181513_0" Pin1InfoVect1LinkObjId="SW-181517_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-974 3964,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18238b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-1050 3964,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="27640@1" ObjectIDZND0="27642@x" ObjectIDZND1="g_17f4490@0" ObjectIDZND2="g_17c84d0@0" Pin0InfoVect0LinkObjId="SW-181518_0" Pin0InfoVect1LinkObjId="g_17f4490_0" Pin0InfoVect2LinkObjId="g_17c84d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-1050 3964,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1823b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-974 4019,-974 4019,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27637@x" ObjectIDND1="27640@x" ObjectIDZND0="27641@0" Pin0InfoVect0LinkObjId="SW-181517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181513_0" Pin1InfoVect1LinkObjId="SW-181516_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-974 4019,-974 4019,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1823d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-1017 4019,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27641@1" ObjectIDZND0="g_17a35d0@0" Pin0InfoVect0LinkObjId="g_17a35d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181517_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-1017 4019,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1823fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-362 3819,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11346@1" ObjectIDZND0="11347@0" Pin0InfoVect0LinkObjId="SW-59007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-362 3819,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1824230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-418 3819,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11347@1" ObjectIDZND0="27627@0" Pin0InfoVect0LinkObjId="g_189c9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59007_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-418 3819,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1824490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-236 3628,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="41829@0" ObjectIDZND0="11352@x" ObjectIDZND1="11353@x" Pin0InfoVect0LinkObjId="SW-59012_0" Pin0InfoVect1LinkObjId="SW-59013_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-LF_YJZ.LF_YJZ_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-236 3628,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193c250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-223 4334,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_187f000@0" ObjectIDND1="17668@x" ObjectIDZND0="37682@0" Pin0InfoVect0LinkObjId="EC-LF_YJZ.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_187f000_0" Pin1InfoVect1LinkObjId="SW-59569_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-223 4334,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-224 4461,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_185b3b0@0" ObjectIDND1="11345@x" ObjectIDZND0="37683@0" Pin0InfoVect0LinkObjId="EC-LF_YJZ.065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_185b3b0_0" Pin1InfoVect1LinkObjId="SW-59005_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-224 4461,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193cf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-225 3819,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_185d5e0@0" ObjectIDND1="11348@x" ObjectIDZND0="37680@0" Pin0InfoVect0LinkObjId="EC-LF_YJZ.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_185d5e0_0" Pin1InfoVect1LinkObjId="SW-59008_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-225 3819,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-223 4602,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_18605b0@0" ObjectIDND1="11338@x" ObjectIDZND0="37684@0" Pin0InfoVect0LinkObjId="EC-LF_YJZ.066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18605b0_0" Pin1InfoVect1LinkObjId="SW-58963_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-223 4602,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193dc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-223 4094,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1868e00@0" ObjectIDND1="11342@x" ObjectIDZND0="37681@0" Pin0InfoVect0LinkObjId="EC-LF_YJZ.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1868e00_0" Pin1InfoVect1LinkObjId="SW-59002_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-223 4094,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193deb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-335 3819,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11346@0" ObjectIDZND0="11348@1" Pin0InfoVect0LinkObjId="SW-59008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-335 3819,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-244 3819,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="11348@0" ObjectIDZND0="g_185d5e0@0" ObjectIDZND1="37680@x" Pin0InfoVect0LinkObjId="g_185d5e0_0" Pin0InfoVect1LinkObjId="EC-LF_YJZ.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-244 3819,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-223 4094,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1868e00@0" ObjectIDND1="37681@x" ObjectIDZND0="11342@0" Pin0InfoVect0LinkObjId="SW-59002_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1868e00_0" Pin1InfoVect1LinkObjId="EC-LF_YJZ.063Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-223 4094,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193e5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-278 4094,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11342@1" ObjectIDZND0="11340@0" Pin0InfoVect0LinkObjId="SW-59542_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59002_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-278 4094,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-223 4334,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37682@x" ObjectIDND1="g_187f000@0" ObjectIDZND0="17668@0" Pin0InfoVect0LinkObjId="SW-59569_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.064Ld_0" Pin1InfoVect1LinkObjId="g_187f000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-223 4334,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193ea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-276 4334,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17668@1" ObjectIDZND0="11349@0" Pin0InfoVect0LinkObjId="SW-59566_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59569_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-276 4334,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-224 4461,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37683@x" ObjectIDND1="g_185b3b0@0" ObjectIDZND0="11345@0" Pin0InfoVect0LinkObjId="SW-59005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.065Ld_0" Pin1InfoVect1LinkObjId="g_185b3b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-224 4461,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193ef50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-278 4461,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11345@1" ObjectIDZND0="11343@0" Pin0InfoVect0LinkObjId="SW-59550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-278 4461,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-223 4602,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37684@x" ObjectIDND1="g_18605b0@0" ObjectIDZND0="11338@0" Pin0InfoVect0LinkObjId="SW-58963_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.066Ld_0" Pin1InfoVect1LinkObjId="g_18605b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-223 4602,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_193f410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-277 4602,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11338@1" ObjectIDZND0="11336@0" Pin0InfoVect0LinkObjId="SW-59534_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58963_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-277 4602,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1942f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-535 4620,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11319@0" ObjectIDZND0="11323@1" Pin0InfoVect0LinkObjId="SW-58852_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59522_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-535 4620,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1943660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-547 4192,-563 4303,-563 4303,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="11354@1" ObjectIDZND0="27626@0" Pin0InfoVect0LinkObjId="g_1943850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-547 4192,-563 4303,-563 4303,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1943850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-427 4334,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17667@1" ObjectIDZND0="27626@0" Pin0InfoVect0LinkObjId="g_1943660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-427 4334,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1943a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-427 4461,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11344@1" ObjectIDZND0="27626@0" Pin0InfoVect0LinkObjId="g_1943660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-427 4461,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1943c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-426 4602,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11337@1" ObjectIDZND0="27626@0" Pin0InfoVect0LinkObjId="g_1943660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-426 4602,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1943ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-474 4620,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11323@0" ObjectIDZND0="27626@0" Pin0InfoVect0LinkObjId="g_1943660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58852_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-474 4620,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1944130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-466 4807,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17666@0" ObjectIDZND0="27626@0" Pin0InfoVect0LinkObjId="g_1943660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228033_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-466 4807,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174c290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-388 4869,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27635@0" ObjectIDZND0="27634@1" Pin0InfoVect0LinkObjId="SW-181280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-388 4869,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-221 4880,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="42902@x" ObjectIDND1="27636@x" ObjectIDZND0="g_174ef50@0" Pin0InfoVect0LinkObjId="g_174ef50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_YJZ.067Ld_0" Pin1InfoVect1LinkObjId="SW-181283_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-221 4880,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1753710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-221 4869,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_174ef50@0" ObjectIDND1="27636@x" ObjectIDZND0="42902@0" Pin0InfoVect0LinkObjId="EC-LF_YJZ.067Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_174ef50_0" Pin1InfoVect1LinkObjId="SW-181283_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-221 4869,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1753900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-221 4869,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_174ef50@0" ObjectIDND1="42902@x" ObjectIDZND0="27636@0" Pin0InfoVect0LinkObjId="SW-181283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_174ef50_0" Pin1InfoVect1LinkObjId="EC-LF_YJZ.067Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-221 4869,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1753af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-275 4869,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27636@1" ObjectIDZND0="27634@0" Pin0InfoVect0LinkObjId="SW-181280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-275 4869,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_175b3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-424 4869,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27635@1" ObjectIDZND0="27626@0" Pin0InfoVect0LinkObjId="g_1943660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-424 4869,-446 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-59610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4216.000000 -598.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11354"/>
     <cge:Term_Ref ObjectID="15946"/>
    <cge:TPSR_Ref TObjectID="11354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-181044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3550.000000 -427.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27627"/>
     <cge:Term_Ref ObjectID="15845"/>
    <cge:TPSR_Ref TObjectID="27627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-181045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3550.000000 -427.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27627"/>
     <cge:Term_Ref ObjectID="15845"/>
    <cge:TPSR_Ref TObjectID="27627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-181046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3550.000000 -427.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27627"/>
     <cge:Term_Ref ObjectID="15845"/>
    <cge:TPSR_Ref TObjectID="27627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-181048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3550.000000 -427.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27627"/>
     <cge:Term_Ref ObjectID="15845"/>
    <cge:TPSR_Ref TObjectID="27627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-181137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -956.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27637"/>
     <cge:Term_Ref ObjectID="15948"/>
    <cge:TPSR_Ref TObjectID="27637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -956.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27637"/>
     <cge:Term_Ref ObjectID="15948"/>
    <cge:TPSR_Ref TObjectID="27637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-181129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -956.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27637"/>
     <cge:Term_Ref ObjectID="15948"/>
    <cge:TPSR_Ref TObjectID="27637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181023" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -956.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181023" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27628"/>
     <cge:Term_Ref ObjectID="15866"/>
    <cge:TPSR_Ref TObjectID="27628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181024" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -956.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27628"/>
     <cge:Term_Ref ObjectID="15866"/>
    <cge:TPSR_Ref TObjectID="27628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -956.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27628"/>
     <cge:Term_Ref ObjectID="15866"/>
    <cge:TPSR_Ref TObjectID="27628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58799" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -732.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11325"/>
     <cge:Term_Ref ObjectID="15858"/>
    <cge:TPSR_Ref TObjectID="11325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -732.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11325"/>
     <cge:Term_Ref ObjectID="15858"/>
    <cge:TPSR_Ref TObjectID="11325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -732.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11325"/>
     <cge:Term_Ref ObjectID="15858"/>
    <cge:TPSR_Ref TObjectID="11325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-180979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -734.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="180979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11318"/>
     <cge:Term_Ref ObjectID="15848"/>
    <cge:TPSR_Ref TObjectID="11318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-180980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -734.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="180980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11318"/>
     <cge:Term_Ref ObjectID="15848"/>
    <cge:TPSR_Ref TObjectID="11318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-180971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -734.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="180971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11318"/>
     <cge:Term_Ref ObjectID="15848"/>
    <cge:TPSR_Ref TObjectID="11318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58804" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -556.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11326"/>
     <cge:Term_Ref ObjectID="15862"/>
    <cge:TPSR_Ref TObjectID="11326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -556.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11326"/>
     <cge:Term_Ref ObjectID="15862"/>
    <cge:TPSR_Ref TObjectID="11326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -556.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11326"/>
     <cge:Term_Ref ObjectID="15862"/>
    <cge:TPSR_Ref TObjectID="11326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-180992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -573.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="180992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11319"/>
     <cge:Term_Ref ObjectID="15854"/>
    <cge:TPSR_Ref TObjectID="11319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-180993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -573.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="180993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11319"/>
     <cge:Term_Ref ObjectID="15854"/>
    <cge:TPSR_Ref TObjectID="11319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-180984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4643.000000 -573.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="180984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11319"/>
     <cge:Term_Ref ObjectID="15854"/>
    <cge:TPSR_Ref TObjectID="11319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-181030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5022.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27625"/>
     <cge:Term_Ref ObjectID="13125"/>
    <cge:TPSR_Ref TObjectID="27625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-181031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5022.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27625"/>
     <cge:Term_Ref ObjectID="13125"/>
    <cge:TPSR_Ref TObjectID="27625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-181032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5022.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27625"/>
     <cge:Term_Ref ObjectID="13125"/>
    <cge:TPSR_Ref TObjectID="27625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-181034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5022.000000 -879.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27625"/>
     <cge:Term_Ref ObjectID="13125"/>
    <cge:TPSR_Ref TObjectID="27625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-181145" prefix="Tap  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -650.000000) translate(0,12)">Tap   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11426"/>
     <cge:Term_Ref ObjectID="39092"/>
    <cge:TPSR_Ref TObjectID="11426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-181014" prefix="Tmp " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -650.000000) translate(0,27)">Tmp  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11426"/>
     <cge:Term_Ref ObjectID="39092"/>
    <cge:TPSR_Ref TObjectID="11426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-181144" prefix="Tap  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -648.000000) translate(0,12)">Tap   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11425"/>
     <cge:Term_Ref ObjectID="39088"/>
    <cge:TPSR_Ref TObjectID="11425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-180997" prefix="Tmp " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -648.000000) translate(0,27)">Tmp  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="180997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11425"/>
     <cge:Term_Ref ObjectID="39088"/>
    <cge:TPSR_Ref TObjectID="11425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-59608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -90.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11350"/>
     <cge:Term_Ref ObjectID="15934"/>
    <cge:TPSR_Ref TObjectID="11350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-59609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -90.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11350"/>
     <cge:Term_Ref ObjectID="15934"/>
    <cge:TPSR_Ref TObjectID="11350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-59604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -90.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11350"/>
     <cge:Term_Ref ObjectID="15934"/>
    <cge:TPSR_Ref TObjectID="11350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-59602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 -101.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11346"/>
     <cge:Term_Ref ObjectID="15912"/>
    <cge:TPSR_Ref TObjectID="11346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-59603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 -101.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11346"/>
     <cge:Term_Ref ObjectID="15912"/>
    <cge:TPSR_Ref TObjectID="11346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-59598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 -101.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11346"/>
     <cge:Term_Ref ObjectID="15912"/>
    <cge:TPSR_Ref TObjectID="11346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-59590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -104.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11340"/>
     <cge:Term_Ref ObjectID="15900"/>
    <cge:TPSR_Ref TObjectID="11340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-59591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -104.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11340"/>
     <cge:Term_Ref ObjectID="15900"/>
    <cge:TPSR_Ref TObjectID="11340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-59586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -104.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11340"/>
     <cge:Term_Ref ObjectID="15900"/>
    <cge:TPSR_Ref TObjectID="11340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-59620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -103.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11349"/>
     <cge:Term_Ref ObjectID="15918"/>
    <cge:TPSR_Ref TObjectID="11349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-59621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -103.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11349"/>
     <cge:Term_Ref ObjectID="15918"/>
    <cge:TPSR_Ref TObjectID="11349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-59616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -103.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11349"/>
     <cge:Term_Ref ObjectID="15918"/>
    <cge:TPSR_Ref TObjectID="11349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-59596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -100.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11343"/>
     <cge:Term_Ref ObjectID="15906"/>
    <cge:TPSR_Ref TObjectID="11343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-59597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -100.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11343"/>
     <cge:Term_Ref ObjectID="15906"/>
    <cge:TPSR_Ref TObjectID="11343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-59592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -100.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11343"/>
     <cge:Term_Ref ObjectID="15906"/>
    <cge:TPSR_Ref TObjectID="11343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-59584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -98.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11336"/>
     <cge:Term_Ref ObjectID="15894"/>
    <cge:TPSR_Ref TObjectID="11336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-59585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -98.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11336"/>
     <cge:Term_Ref ObjectID="15894"/>
    <cge:TPSR_Ref TObjectID="11336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-59580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -98.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="59580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11336"/>
     <cge:Term_Ref ObjectID="15894"/>
    <cge:TPSR_Ref TObjectID="11336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-181037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5082.000000 -435.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27626"/>
     <cge:Term_Ref ObjectID="13126"/>
    <cge:TPSR_Ref TObjectID="27626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-181038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5082.000000 -435.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27626"/>
     <cge:Term_Ref ObjectID="13126"/>
    <cge:TPSR_Ref TObjectID="27626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-181039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5082.000000 -435.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27626"/>
     <cge:Term_Ref ObjectID="13126"/>
    <cge:TPSR_Ref TObjectID="27626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-181041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5082.000000 -435.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27626"/>
     <cge:Term_Ref ObjectID="13126"/>
    <cge:TPSR_Ref TObjectID="27626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4882.000000 -98.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27634"/>
     <cge:Term_Ref ObjectID="15888"/>
    <cge:TPSR_Ref TObjectID="27634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4882.000000 -98.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27634"/>
     <cge:Term_Ref ObjectID="15888"/>
    <cge:TPSR_Ref TObjectID="27634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4882.000000 -98.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27634"/>
     <cge:Term_Ref ObjectID="15888"/>
    <cge:TPSR_Ref TObjectID="27634"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="3980" y="-946"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="3980" y="-946"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4535" y="-948"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4535" y="-948"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3451" y="-1153"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3451" y="-1153"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3451" y="-1188"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3451" y="-1188"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4612" y="-366"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4612" y="-366"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4470" y="-368"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4470" y="-368"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4343" y="-368"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4343" y="-368"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4102" y="-368"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4102" y="-368"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3827" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3827" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3635" y="-370"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3635" y="-370"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4202" y="-543"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4202" y="-543"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="92" x="3187" y="-793"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="92" x="3187" y="-793"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="4039" y="-638"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="4039" y="-638"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4526" y="-654"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4526" y="-654"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3470" y="-1043"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3470" y="-1043"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4889" y="-363"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4889" y="-363"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="35kV杨家庄变LF_YJZ_371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="3980" y="-946"/></g>
   <g href="35kV杨家庄变LF_YJZ_372间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4535" y="-948"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3451" y="-1153"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3451" y="-1188"/></g>
   <g href="35kV杨家庄变LF_YJZ_066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4612" y="-366"/></g>
   <g href="35kV杨家庄变LF_YJZ_065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4470" y="-368"/></g>
   <g href="35kV杨家庄变LF_YJZ_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4343" y="-368"/></g>
   <g href="35kV杨家庄变LF_YJZ_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4102" y="-368"/></g>
   <g href="35kV杨家庄变LF_YJZ_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3827" y="-357"/></g>
   <g href="35kV杨家庄变LF_YJZ_061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3635" y="-370"/></g>
   <g href="35kV杨家庄变LF_YJZ_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4202" y="-543"/></g>
   <g href="35kV杨家庄变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="92" x="3187" y="-793"/></g>
   <g href="35kV杨家庄变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="4039" y="-638"/></g>
   <g href="35kV杨家庄变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4526" y="-654"/></g>
   <g href="AVC杨家庄站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3470" y="-1043"/></g>
   <g href="35kV杨家庄变10kV鲁家村Ⅱ回线067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4889" y="-363"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3471" y="-1042"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_YJZ.LF_YJZ_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3550,-446 4218,-446 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27627" ObjectName="BS-LF_YJZ.LF_YJZ_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="27627"/></metadata>
   <polyline fill="none" opacity="0" points="3550,-446 4218,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_YJZ.LF_YJZ_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-446 4984,-446 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27626" ObjectName="BS-LF_YJZ.LF_YJZ_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="27626"/></metadata>
   <polyline fill="none" opacity="0" points="4266,-446 4984,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_YJZ.LF_YJZ_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-809 5026,-809 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27625" ObjectName="BS-LF_YJZ.LF_YJZ_3M"/>
    <cge:TPSR_Ref TObjectID="27625"/></metadata>
   <polyline fill="none" opacity="0" points="3560,-809 5026,-809 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -893.000000)" xlink:href="#transformer2:shape86_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -893.000000)" xlink:href="#transformer2:shape86_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -540.000000)" xlink:href="#transformer2:shape87_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -540.000000)" xlink:href="#transformer2:shape87_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_YJZ.LF_YJZ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39094"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -580.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -580.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11426" ObjectName="TF-LF_YJZ.LF_YJZ_2T"/>
    <cge:TPSR_Ref TObjectID="11426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_YJZ.LF_YJZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39090"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.000000 -573.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.000000 -573.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11425" ObjectName="TF-LF_YJZ.LF_YJZ_1T"/>
    <cge:TPSR_Ref TObjectID="11425"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80990" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -944.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80990" ObjectName="LF_YJZ:LF_YJZ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80991" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -900.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80991" ObjectName="LF_YJZ:LF_YJZ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217872" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -1027.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217872" ObjectName="LF_YJZ:LF_YJZ_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217872" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -986.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217872" ObjectName="LF_YJZ:LF_YJZ_sumP1"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52535" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3469.000000 -1074.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9245" ObjectName="DYN-LF_YJZ"/>
     <cge:Meas_Ref ObjectId="52535"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17cb430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 865.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17ccec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 880.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17cd530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 835.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17cd7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 850.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17cdeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 958.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176db80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 943.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176e3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 928.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176f830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 956.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176faf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 941.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176fd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 926.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17706e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 733.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17709a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.000000 718.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1770be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 703.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17715d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 555.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1771860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 540.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1771aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 525.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18212f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 414.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18217e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 429.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1821a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3462.000000 384.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1821c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 399.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193f8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.000000 91.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193ff00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 76.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1940140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 61.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1944f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 422.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1945160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 437.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19453a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4990.000000 392.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19455e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 407.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194e190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 104.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194e660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3766.000000 89.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194e8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3791.000000 74.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194ecc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 105.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194ef80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 90.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194f1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 75.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194f5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 105.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1745e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 90.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17460b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 75.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17464d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4416.000000 103.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1746790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 88.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17469d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 73.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1746df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4561.000000 99.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17470b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 84.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17472f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 69.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172a750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.000000 98.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172aa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4808.000000 83.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172ac50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 68.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_YJZ.LF_YJZ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -128.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41829" ObjectName="CB-LF_YJZ.LF_YJZ_Cb1"/>
    <cge:TPSR_Ref TObjectID="41829"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27627" cx="3628" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27627" cx="3576" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27627" cx="4094" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27627" cx="3819" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27627" cx="4010" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27625" cx="3776" cy="-809" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27627" cx="3853" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27625" cx="4010" cy="-809" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27625" cx="4974" cy="-809" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27627" cx="4192" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27625" cx="4620" cy="-809" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27626" cx="4303" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27626" cx="4334" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27626" cx="4461" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27626" cx="4602" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27626" cx="4620" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27626" cx="4807" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27626" cx="4869" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-59015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11355" ObjectName="SW-LF_YJZ.LF_YJZ_0122SW"/>
     <cge:Meas_Ref ObjectId="59015"/>
    <cge:TPSR_Ref TObjectID="11355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58852">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.006522 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11323" ObjectName="SW-LF_YJZ.LF_YJZ_0011SW"/>
     <cge:Meas_Ref ObjectId="58852"/>
    <cge:TPSR_Ref TObjectID="11323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59568">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17667" ObjectName="SW-LF_YJZ.LF_YJZ_0641SW"/>
     <cge:Meas_Ref ObjectId="59568"/>
    <cge:TPSR_Ref TObjectID="17667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59569">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -235.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17668" ObjectName="SW-LF_YJZ.LF_YJZ_0646SW"/>
     <cge:Meas_Ref ObjectId="59569"/>
    <cge:TPSR_Ref TObjectID="17668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59011">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3619.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11351" ObjectName="SW-LF_YJZ.LF_YJZ_0612SW"/>
     <cge:Meas_Ref ObjectId="59011"/>
    <cge:TPSR_Ref TObjectID="11351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59012">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3619.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11352" ObjectName="SW-LF_YJZ.LF_YJZ_0616SW"/>
     <cge:Meas_Ref ObjectId="59012"/>
    <cge:TPSR_Ref TObjectID="11352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4594.000000 -236.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11338" ObjectName="SW-LF_YJZ.LF_YJZ_0666SW"/>
     <cge:Meas_Ref ObjectId="58963"/>
    <cge:TPSR_Ref TObjectID="11338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11341" ObjectName="SW-LF_YJZ.LF_YJZ_0632SW"/>
     <cge:Meas_Ref ObjectId="59001"/>
    <cge:TPSR_Ref TObjectID="11341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59002">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 -237.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11342" ObjectName="SW-LF_YJZ.LF_YJZ_0636SW"/>
     <cge:Meas_Ref ObjectId="59002"/>
    <cge:TPSR_Ref TObjectID="11342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228033">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17666" ObjectName="SW-LF_YJZ.LF_YJZ_001SW"/>
     <cge:Meas_Ref ObjectId="228033"/>
    <cge:TPSR_Ref TObjectID="17666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.000000 -521.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228024">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3567.000000 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17665" ObjectName="SW-LF_YJZ.LF_YJZ_0902SW"/>
     <cge:Meas_Ref ObjectId="228024"/>
    <cge:TPSR_Ref TObjectID="17665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3571.000000 -520.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11337" ObjectName="SW-LF_YJZ.LF_YJZ_0661SW"/>
     <cge:Meas_Ref ObjectId="58962"/>
    <cge:TPSR_Ref TObjectID="11337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59004">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11344" ObjectName="SW-LF_YJZ.LF_YJZ_0651SW"/>
     <cge:Meas_Ref ObjectId="59004"/>
    <cge:TPSR_Ref TObjectID="11344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -237.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11345" ObjectName="SW-LF_YJZ.LF_YJZ_0656SW"/>
     <cge:Meas_Ref ObjectId="59005"/>
    <cge:TPSR_Ref TObjectID="11345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59007">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11347" ObjectName="SW-LF_YJZ.LF_YJZ_0622SW"/>
     <cge:Meas_Ref ObjectId="59007"/>
    <cge:TPSR_Ref TObjectID="11347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59008">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11348" ObjectName="SW-LF_YJZ.LF_YJZ_0626SW"/>
     <cge:Meas_Ref ObjectId="59008"/>
    <cge:TPSR_Ref TObjectID="11348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -472.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181229">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.785714 -0.000000 0.000000 -0.717391 4568.000000 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27632" ObjectName="SW-LF_YJZ.LF_YJZ_37260SW"/>
     <cge:Meas_Ref ObjectId="181229"/>
    <cge:TPSR_Ref TObjectID="27632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181228">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27631" ObjectName="SW-LF_YJZ.LF_YJZ_3726SW"/>
     <cge:Meas_Ref ObjectId="181228"/>
    <cge:TPSR_Ref TObjectID="27631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181230">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.760870 4483.000000 -1014.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27633" ObjectName="SW-LF_YJZ.LF_YJZ_37267SW"/>
     <cge:Meas_Ref ObjectId="181230"/>
    <cge:TPSR_Ref TObjectID="27633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181517">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.785714 -0.000000 0.000000 -0.717391 4012.000000 -988.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27641" ObjectName="SW-LF_YJZ.LF_YJZ_37160SW"/>
     <cge:Meas_Ref ObjectId="181517"/>
    <cge:TPSR_Ref TObjectID="27641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 -1009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27640" ObjectName="SW-LF_YJZ.LF_YJZ_3716SW"/>
     <cge:Meas_Ref ObjectId="181516"/>
    <cge:TPSR_Ref TObjectID="27640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.760870 3927.000000 -1015.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27642" ObjectName="SW-LF_YJZ.LF_YJZ_37167SW"/>
     <cge:Meas_Ref ObjectId="181518"/>
    <cge:TPSR_Ref TObjectID="27642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181227">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4472.000000 -851.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27630" ObjectName="SW-LF_YJZ.LF_YJZ_37217SW"/>
     <cge:Meas_Ref ObjectId="181227"/>
    <cge:TPSR_Ref TObjectID="27630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181226">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -824.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27629" ObjectName="SW-LF_YJZ.LF_YJZ_3721SW"/>
     <cge:Meas_Ref ObjectId="181226"/>
    <cge:TPSR_Ref TObjectID="27629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 -853.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27639" ObjectName="SW-LF_YJZ.LF_YJZ_37117SW"/>
     <cge:Meas_Ref ObjectId="181515"/>
    <cge:TPSR_Ref TObjectID="27639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181514">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 -823.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27638" ObjectName="SW-LF_YJZ.LF_YJZ_3711SW"/>
     <cge:Meas_Ref ObjectId="181514"/>
    <cge:TPSR_Ref TObjectID="27638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58850">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4600.000000 -725.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11321" ObjectName="SW-LF_YJZ.LF_YJZ_30117SW"/>
     <cge:Meas_Ref ObjectId="58850"/>
    <cge:TPSR_Ref TObjectID="11321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58849">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -740.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11320" ObjectName="SW-LF_YJZ.LF_YJZ_3011SW"/>
     <cge:Meas_Ref ObjectId="58849"/>
    <cge:TPSR_Ref TObjectID="11320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -742.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11327" ObjectName="SW-LF_YJZ.LF_YJZ_3021SW"/>
     <cge:Meas_Ref ObjectId="58856"/>
    <cge:TPSR_Ref TObjectID="11327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58857">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11328" ObjectName="SW-LF_YJZ.LF_YJZ_0022SW"/>
     <cge:Meas_Ref ObjectId="58857"/>
    <cge:TPSR_Ref TObjectID="11328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58909">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4909.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11333" ObjectName="SW-LF_YJZ.LF_YJZ_39010SW"/>
     <cge:Meas_Ref ObjectId="58909"/>
    <cge:TPSR_Ref TObjectID="11333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58907">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -735.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11331" ObjectName="SW-LF_YJZ.LF_YJZ_3901SW"/>
     <cge:Meas_Ref ObjectId="58907"/>
    <cge:TPSR_Ref TObjectID="11331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58908">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4993.000000 -724.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11332" ObjectName="SW-LF_YJZ.LF_YJZ_39017SW"/>
     <cge:Meas_Ref ObjectId="58908"/>
    <cge:TPSR_Ref TObjectID="11332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59013">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 -264.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11353" ObjectName="SW-LF_YJZ.LF_YJZ_06167SW"/>
     <cge:Meas_Ref ObjectId="59013"/>
    <cge:TPSR_Ref TObjectID="11353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -831.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4861.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27636" ObjectName="SW-LF_YJZ.LF_YJZ_0676SW"/>
     <cge:Meas_Ref ObjectId="181283"/>
    <cge:TPSR_Ref TObjectID="27636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27635" ObjectName="SW-LF_YJZ.LF_YJZ_0671SW"/>
     <cge:Meas_Ref ObjectId="181282"/>
    <cge:TPSR_Ref TObjectID="27635"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18d0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1685620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_15c4df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">杨家庄变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_15d9780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3452.000000 -474.000000) translate(0,18)">10kV II 母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1710a70" transform="matrix(0.945000 0.000000 -0.000000 1.058824 4928.230000 -762.647059) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1897110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.333333 -121.000000) translate(0,18)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ae480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4823.000000 -490.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_189c620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3592.000000 -489.000000) translate(0,15)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_17de120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.000000 -480.000000) translate(0,18)">10kV I 母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_17de630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -661.000000) translate(0,18)">10kVII段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_17dea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -701.000000) translate(0,18)">10kVI段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_17dea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -701.000000) translate(0,40)">母线电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_17dea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -701.000000) translate(0,62)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18184c0" transform="matrix(0.945000 0.000000 -0.000000 1.058824 5009.795000 -756.294118) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c0d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -242.000000) translate(0,17)">4720295</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c0d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -242.000000) translate(0,38)">15758580342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_18c0f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -615.000000) translate(0,18)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_18c11b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -633.000000) translate(0,18)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c13e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.000000 -350.000000) translate(0,15)">75/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c15d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 -337.000000) translate(0,15)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4108.000000 -348.000000) translate(0,15)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.000000 -349.000000) translate(0,15)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -346.000000) translate(0,15)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c1ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -349.000000) translate(0,15)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -520.000000) translate(0,15)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1833bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -666.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_18c5360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -1017.000000) translate(0,18)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_18c5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -1199.000000) translate(0,18)">35kV洪杨线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c61d0" transform="matrix(0.945000 0.000000 -0.000000 1.058824 4906.960000 -783.088235) translate(0,15)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_17a0c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3882.000000 -1198.000000) translate(0,18)">35kV金杨T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_186ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -617.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17f29e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -946.000000) translate(0,15)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17f2ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4535.000000 -948.000000) translate(0,15)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17f6ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3970.000000 -1047.000000) translate(0,15)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f7430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -1013.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f7cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -1038.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17f7f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -881.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17f9e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -856.000000) translate(0,15)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e1de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4436.000000 -1035.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e22d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4423.000000 -879.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e2510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -853.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e2750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -1011.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e2990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -1040.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_17e2ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3462.000000 -1145.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_17e4030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3462.000000 -1180.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e46e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -727.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -776.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e4ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -551.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e4de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -491.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e5020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -565.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e5260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -498.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e54a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -721.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e56e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -769.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e5920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4557.000000 -756.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e5b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3637.000000 -369.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e5da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3635.000000 -322.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e5fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3635.000000 -418.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e6220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -295.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e6460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -405.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e66a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -266.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e68e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -356.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e6b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4101.000000 -417.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e6d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -269.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17e6fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -368.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1880040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -367.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18803a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4347.000000 -267.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18807b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -416.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18809f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -416.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1880c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -268.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1880e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -367.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18810b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4609.000000 -415.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18812f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4612.000000 -269.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1881530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4612.000000 -366.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1882070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -485.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1882610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4201.000000 -541.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1882990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -792.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1774350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -832.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175c0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.000000 -638.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175f4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 -654.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1944390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -511.000000) translate(0,15)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1945820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3135.000000 -534.000000) translate(0,12)">1、2号主变档位采集现场未接线，无法采集档位数值。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1948a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -146.000000) translate(0,18)">鲁家村线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1949a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.000000 -151.000000) translate(0,18)">大红田隧道线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_194ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -148.000000) translate(0,18)">南冲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_194b4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4431.000000 -148.000000) translate(0,18)">中屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_194bd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -150.000000) translate(0,18)">铁路隧道线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_194cd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -1143.000000) translate(0,12)">35kV金杨T线杨家庄侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1747530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3127.000000 -191.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1747530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3127.000000 -191.000000) translate(0,38)">禄丰变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_17492f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -180.500000) translate(0,17)">13908784381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1749800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3487.500000 -1031.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1752c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -403.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1753290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4879.000000 -267.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17534d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -363.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1753ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -152.000000) translate(0,18)">10kV鲁家村Ⅱ回线</text>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_187f000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -217.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_185b3b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4467.000000 -218.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_185d5e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3824.882353 -218.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18605b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4609.000000 -217.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1868e00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.193277 -216.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fc180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4835.000000 -508.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17dc1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -507.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f4490">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 -1076.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17f9110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 -1083.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181c940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -642.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181d8d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -662.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_174ef50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4876.000000 -215.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_YJZ"/>
</svg>