<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-296" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-270 -1025 2029 1224">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape4_0">
    <rect height="22" stroke-width="2" width="10" x="0" y="2"/>
   </symbol>
   <symbol id="breaker2:shape4_1">
    <rect height="22" stroke-width="2" width="10" x="0" y="2"/>
    <rect height="22" stroke-width="1" width="10" x="0" y="2"/>
   </symbol>
   <symbol id="breaker2:shape4-UnNor1">
    <rect height="22" stroke-width="2" width="10" x="0" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="10" y1="20" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="3" y1="20" y2="9"/>
   </symbol>
   <symbol id="breaker2:shape4-UnNor2">
    <rect height="22" stroke-width="2" width="10" x="0" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="8" y1="20" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="2" y1="20" y2="12"/>
   </symbol>
   <symbol id="breaker2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="45" y2="67"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,25 10,18 17,27 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,20 10,13 17,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="25" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="30" y2="25"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,47 10,54 17,45 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,52 10,59 17,50 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="10,45 19,32 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="4" y2="26"/>
   </symbol>
   <symbol id="breaker2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="46" y2="68"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,26 10,19 17,28 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,21 10,14 17,23 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="26" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="31" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,48 10,55 17,46 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,53 10,60 17,51 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="10,46 16,29 " stroke-width="1"/>
   </symbol>
   <symbol id="breaker2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="45" y2="67"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,25 10,18 17,27 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,20 10,13 17,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="25" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="30" y2="25"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,47 10,54 17,45 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="0,52 10,59 17,50 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="10,45 19,32 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="4" y2="26"/>
   </symbol>
   <symbol id="breaker2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="46" y2="68"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,26 10,19 17,28 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,21 10,14 17,23 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="26" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="31" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,48 10,55 17,46 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-1,53 10,60 17,51 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="10,46 16,29 " stroke-width="1"/>
   </symbol>
   <symbol id="capacitor:shape16">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.491429" x1="33" x2="33" y1="96" y2="120"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="20" x2="33" y1="120" y2="120"/>
    <polyline points="33,133 35,133 37,132 39,132 41,131 42,130 44,129 45,127 46,125 47,124 47,122 47,120 47,118 47,116 46,115 45,113 44,112 42,110 41,109 39,108 37,108 35,107 33,107 32,107 30,108 28,108 26,109 25,110 23,112 22,113 21,115 20,116 20,118 20,120 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.460112" x1="56" x2="15" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="56" x2="56" y1="15" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="15" x2="15" y1="15" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.123418" x1="34" x2="34" y1="25" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246835" x1="2" x2="2" y1="87" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.403648" x1="56" x2="56" y1="37" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05" x1="54" x2="54" y1="75" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.403648" x1="54" x2="54" y1="96" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.385472" x1="54" x2="15" y1="96" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.988867" x1="56" x2="16" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643939" x1="15" x2="15" y1="32" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.935" x1="15" x2="15" y1="66" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.938156" x1="53" x2="15" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643939" x1="14" x2="14" y1="96" y2="89"/>
    <polyline arcFlag="1" points="13,78 13,78 12,78 11,78 11,78 10,79 9,79 9,80 8,80 8,81 8,82 7,82 7,83 7,84 7,85 8,85 8,86 8,87 9,87 9,88 10,88 11,89 11,89 12,89 13,89 13,89 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.303559" x1="65" x2="44" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.303559" x1="65" x2="44" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.303559" x1="65" x2="44" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.303559" x1="65" x2="44" y1="46" y2="46"/>
    <polyline arcFlag="1" points="13,67 13,67 12,67 11,67 11,67 10,68 9,68 9,69 8,69 8,70 8,71 7,71 7,72 7,73 7,74 8,74 8,75 8,76 9,76 9,77 10,77 11,78 11,78 12,78 13,78 13,78 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246835" x1="3" x2="3" y1="52" y2="32"/>
    <polyline arcFlag="1" points="15,43 14,43 13,43 12,43 12,43 11,44 10,44 10,45 9,45 9,46 9,47 9,47 8,48 8,49 9,50 9,50 9,51 9,52 10,52 10,53 11,53 12,54 12,54 13,54 14,54 15,54 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="15,32 14,32 13,32 12,32 12,32 11,33 10,33 10,34 9,34 9,35 9,36 9,36 8,37 8,38 9,39 9,39 9,40 9,41 10,41 10,42 11,42 12,43 12,43 13,43 14,43 15,43 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="33" x2="33" y1="133" y2="141"/>
   </symbol>
   <symbol id="capacitor:shape40">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_1376fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="currentTransformer:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="37" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="23" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="23" y1="32" y2="30"/>
    <circle cx="29" cy="7" r="7.5" stroke-width="1"/>
    <circle cx="34" cy="29" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="5" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="35" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="23" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="23" y1="20" y2="18"/>
    <circle cx="34" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="32" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="32" y1="5" y2="5"/>
    <circle cx="23" cy="29" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="3" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="35" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="41" x2="41" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="48" x2="48" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="48" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="22" x2="22" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="29" x2="29" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="29" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="7" x2="7" y1="47" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="10" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="13" y1="55" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="50" y2="50"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape68_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.335714" x1="16" x2="10" y1="81" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="66" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="35" y2="4"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="44,47 44,35 16,26 " stroke-width="1"/>
    <circle cx="15" cy="50" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,101 10,114 23,114 16,101 16,102 16,101 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="129" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="47" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="47" y2="52"/>
    <circle cx="15" cy="72" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.335714" x1="10" x2="21" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.335714" x1="16" x2="21" y1="81" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape68_1"/>
   <symbol id="transformer2:shape95_0">
    <ellipse cx="14" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape95_1">
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a491c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a49e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a91b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a92710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a93880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a94390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a94dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a95710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa3670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b67610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b68050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b62fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b645c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1234" width="2039" x="-275" y="-1030"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1309" x2="1606" y1="-518" y2="-518"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1309" x2="1298" y1="-518" y2="-536"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1301" x2="1613" y1="-531" y2="-531"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1606" x2="1617" y1="-518" y2="-536"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="871" x2="871" y1="-619" y2="-598"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="871" x2="871" y1="-561" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="260" x2="209" y1="-58" y2="-58"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="209" x2="209" y1="-58" y2="-37"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="209" x2="260" y1="0" y2="0"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="207" x2="207" y1="-37" y2="-28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="207" x2="212" y1="-37" y2="-37"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="212" x2="212" y1="-37" y2="-28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="207" x2="212" y1="-28" y2="-28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="209" x2="209" y1="-28" y2="0"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.469769" x1="260" x2="260" y1="-116" y2="-125"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.324864" x1="252" x2="261" y1="-115" y2="-98"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="260" x2="260" y1="-76" y2="-99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.375" x1="259" x2="261" y1="-116" y2="-116"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="871,-573 876,-562 865,-562 871,-573 871,-572 871,-573 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="871,-587 876,-598 865,-598 871,-587 871,-588 871,-587 " stroke="rgb(170,85,127)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-253989">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -771.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42088" ObjectName="SW-CX_JSY.CX_JSY_151BK"/>
     <cge:Meas_Ref ObjectId="253989"/>
    <cge:TPSR_Ref TObjectID="42088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 809.000000 -523.000000)" xlink:href="#breaker2:shape4_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 -549.000000)" xlink:href="#breaker2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 -558.000000)" xlink:href="#breaker2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254041">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -397.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42096" ObjectName="SW-CX_JSY.CX_JSY_301BK"/>
     <cge:Meas_Ref ObjectId="254041"/>
    <cge:TPSR_Ref TObjectID="42096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 251.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42116" ObjectName="SW-CX_JSY.CX_JSY_351BK"/>
     <cge:Meas_Ref ObjectId="254070"/>
    <cge:TPSR_Ref TObjectID="42116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 482.000000 -246.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42120" ObjectName="SW-CX_JSY.CX_JSY_352BK"/>
     <cge:Meas_Ref ObjectId="254076"/>
    <cge:TPSR_Ref TObjectID="42120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -246.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254050">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42100" ObjectName="SW-CX_JSY.CX_JSY_354BK"/>
     <cge:Meas_Ref ObjectId="254050"/>
    <cge:TPSR_Ref TObjectID="42100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42112" ObjectName="SW-CX_JSY.CX_JSY_357BK"/>
     <cge:Meas_Ref ObjectId="254065"/>
    <cge:TPSR_Ref TObjectID="42112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42104" ObjectName="SW-CX_JSY.CX_JSY_355BK"/>
     <cge:Meas_Ref ObjectId="254055"/>
    <cge:TPSR_Ref TObjectID="42104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254060">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42108" ObjectName="SW-CX_JSY.CX_JSY_356BK"/>
     <cge:Meas_Ref ObjectId="254060"/>
    <cge:TPSR_Ref TObjectID="42108"/></metadata>
   </g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1286c00">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 675.000000 -885.000000)" xlink:href="#currentTransformer:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_JSY.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.000000 10.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43331" ObjectName="SM-CX_JSY.P1"/>
    <cge:TPSR_Ref TObjectID="43331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_JSY.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 12.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43332" ObjectName="SM-CX_JSY.P2"/>
    <cge:TPSR_Ref TObjectID="43332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_JSY.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1310.000000 8.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43333" ObjectName="SM-CX_JSY.P3"/>
    <cge:TPSR_Ref TObjectID="43333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_JSY.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 9.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43334" ObjectName="SM-CX_JSY.P4"/>
    <cge:TPSR_Ref TObjectID="43334"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_TX" endPointId="0" endStationName="CX_JSY" flowDrawDirect="1" flowShape="0" id="AC-110kV.jiantian_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="768,-965 768,-996 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42218" ObjectName="AC-110kV.jiantian_line"/>
    <cge:TPSR_Ref TObjectID="42218_SS-296"/></metadata>
   <polyline fill="none" opacity="0" points="768,-965 768,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="208,-79 208,-108 256,-108 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="208,-79 208,-108 256,-108 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1562.000000 -728.000000)" xlink:href="#transformer2:shape68_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1562.000000 -728.000000)" xlink:href="#transformer2:shape68_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1333.000000 -726.000000)" xlink:href="#transformer2:shape68_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1333.000000 -726.000000)" xlink:href="#transformer2:shape68_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -10.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -10.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_JSY.CX-JSY-1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="48262"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.000000 -551.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.000000 -551.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="48921" ObjectName="TF-CX_JSY.CX-JSY-1T"/>
    <cge:TPSR_Ref TObjectID="48921"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b55670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -482.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f8330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.000000 -478.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1305590">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 854.000000 -527.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d1c6c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 -949.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1385fd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.000000 -465.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1383830">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 158.000000 -199.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13600e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 388.000000 -198.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c5040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 -197.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b01390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 809.000000 -200.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b61690">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1012.000000 -198.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aef1b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 -202.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13886c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1434.000000 -201.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b5040">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1709.000000 -665.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ac2d10">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1448.000000 -665.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1424d90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 273.000000 16.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -179.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="304" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="260" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="490" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="717" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="911" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="1114" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="1315" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42086" cx="1536" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48130" cx="768" cy="-756" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1455" cy="-491" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48130" cx="768" cy="-756" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-253872" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42075" ObjectName="DYN-CX_JSY"/>
     <cge:Meas_Ref ObjectId="253872"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a33e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 27.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a671a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -478.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a671a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -478.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112c320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 408.000000 -379.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_163c840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19a57a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19a57a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19a57a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19a57a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19a57a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19a57a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19a57a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_15cd0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 -929.500000) translate(0,16)">尖山营光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_14a4750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 7.000000) translate(0,17)">17787809293</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b28b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 45.000000) translate(0,15)">1-9号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a67db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 4.000000) translate(0,15)">预留储能回路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1425f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 449.500000 12.000000) translate(0,15)">1号站用电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ae6660" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1358.000000 -458.000000) translate(0,15)">0.4kVⅠ站用电接线示意图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13846f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1265.000000 -909.000000) translate(0,15)">110kV田心变10kV483发窝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13846f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1265.000000 -909.000000) translate(0,33)">线4号公变支线3号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a07d40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1357.000000 -795.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_132e960" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1542.500000 -880.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13f0060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 214.000000 181.000000) translate(0,15)">1号静态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b42fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 292.000000 150.000000) translate(0,15)">28MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1471f30" transform="matrix(1.000000 -0.000000 0.000000 1.000000 922.000000 -584.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -633.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -633.000000) translate(0,27)">SZ11-100000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -633.000000) translate(0,42)">115±8x1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -633.000000) translate(0,57)">YN,d0+11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -633.000000) translate(0,72)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13654b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1080.000000 28.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2cb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1080.000000 46.000000) translate(0,15)">17-18,23-24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2cb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1080.000000 46.000000) translate(0,33)">29-31号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a66f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 27.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1afbad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 45.000000) translate(0,15)">12-16,22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1afbad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 45.000000) translate(0,33)">26-28号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13cba10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 28.000000) translate(0,15)">4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1350510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 46.000000) translate(0,15)">10-11,19-21</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1350510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 46.000000) translate(0,33)">25,32号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b51790" transform="matrix(1.000000 0.000000 -0.000000 1.000000 705.000000 -1025.000000) translate(0,16)">至110kV田心变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae5a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.000000 -929.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -872.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cabb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.000000 -849.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -800.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dba930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 799.000000 -766.000000) translate(0,12)">15110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d13c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -709.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1afbf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.000000 -690.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_135d9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -644.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b26970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 776.000000 -426.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b2c370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -421.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cc540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 269.000000 -277.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ccb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -204.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ccdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -275.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -203.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -278.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 932.000000 -205.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 -278.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -203.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c65160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -280.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c653a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1336.000000 -207.000000) translate(0,12)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cac320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -280.000000) translate(0,12)">357</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cac560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 -206.000000) translate(0,12)">35767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cac7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -273.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cac9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -203.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1330950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -954.000000) translate(0,12)">尖</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1330950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -954.000000) translate(0,27)">田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1330950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -954.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f35820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -818.000000) translate(0,12)">AGC/AVC</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="260,-156 255,-166 265,-166 260,-156 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="260,-145 255,-135 265,-135 260,-145 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="490,-127 485,-137 495,-137 490,-127 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="490,-116 485,-106 495,-106 490,-116 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="717,-126 712,-136 722,-136 717,-126 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="717,-115 712,-105 722,-105 717,-115 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="911,-129 906,-139 916,-139 911,-129 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="911,-118 906,-108 916,-108 911,-118 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1114,-127 1109,-137 1119,-137 1114,-127 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1114,-116 1109,-106 1119,-106 1114,-116 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1315,-131 1310,-141 1320,-141 1315,-131 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1315,-120 1310,-110 1320,-110 1315,-120 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1536,-130 1531,-140 1541,-140 1536,-130 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1536,-119 1531,-109 1541,-109 1536,-119 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1458,-524 1448,-532 1468,-532 1458,-524 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-254049">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42099" ObjectName="SW-CX_JSY.CX_JSY_3901SW"/>
     <cge:Meas_Ref ObjectId="254049"/>
    <cge:TPSR_Ref TObjectID="42099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254040">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 913.000000 -554.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42095" ObjectName="SW-CX_JSY.CX_JSY_1010SW"/>
     <cge:Meas_Ref ObjectId="254040"/>
    <cge:TPSR_Ref TObjectID="42095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-253995">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -659.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42094" ObjectName="SW-CX_JSY.CX_JSY_15117SW"/>
     <cge:Meas_Ref ObjectId="253995"/>
    <cge:TPSR_Ref TObjectID="42094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-253994">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.000000 -735.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42093" ObjectName="SW-CX_JSY.CX_JSY_15110SW"/>
     <cge:Meas_Ref ObjectId="253994"/>
    <cge:TPSR_Ref TObjectID="42093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-253993">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -818.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42092" ObjectName="SW-CX_JSY.CX_JSY_15160SW"/>
     <cge:Meas_Ref ObjectId="253993"/>
    <cge:TPSR_Ref TObjectID="42092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-253990">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -842.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42089" ObjectName="SW-CX_JSY.CX_JSY_1516SW"/>
     <cge:Meas_Ref ObjectId="253990"/>
    <cge:TPSR_Ref TObjectID="42089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-253992">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 -898.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42091" ObjectName="SW-CX_JSY.CX_JSY_15167SW"/>
     <cge:Meas_Ref ObjectId="253992"/>
    <cge:TPSR_Ref TObjectID="42091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254072">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 278.000000 -173.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42119" ObjectName="SW-CX_JSY.CX_JSY_35167SW"/>
     <cge:Meas_Ref ObjectId="254072"/>
    <cge:TPSR_Ref TObjectID="42119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-253991">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -679.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42090" ObjectName="SW-CX_JSY.CX_JSY_1511SW"/>
     <cge:Meas_Ref ObjectId="253991"/>
    <cge:TPSR_Ref TObjectID="42090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254078">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 -172.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42123" ObjectName="SW-CX_JSY.CX_JSY_35267SW"/>
     <cge:Meas_Ref ObjectId="254078"/>
    <cge:TPSR_Ref TObjectID="42123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -171.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254052">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 929.000000 -174.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42103" ObjectName="SW-CX_JSY.CX_JSY_35467SW"/>
     <cge:Meas_Ref ObjectId="254052"/>
    <cge:TPSR_Ref TObjectID="42103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 -172.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42107" ObjectName="SW-CX_JSY.CX_JSY_35567SW"/>
     <cge:Meas_Ref ObjectId="254057"/>
    <cge:TPSR_Ref TObjectID="42107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254062">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1333.000000 -176.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42111" ObjectName="SW-CX_JSY.CX_JSY_35667SW"/>
     <cge:Meas_Ref ObjectId="254062"/>
    <cge:TPSR_Ref TObjectID="42111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1554.000000 -175.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42115" ObjectName="SW-CX_JSY.CX_JSY_35767SW"/>
     <cge:Meas_Ref ObjectId="254067"/>
    <cge:TPSR_Ref TObjectID="42115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1594.000000 -667.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1333.000000 -667.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254042">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -368.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42097" ObjectName="SW-CX_JSY.CX_JSY_301XC"/>
     <cge:Meas_Ref ObjectId="254042"/>
    <cge:TPSR_Ref TObjectID="42097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254042">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -443.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42098" ObjectName="SW-CX_JSY.CX_JSY_301XC1"/>
     <cge:Meas_Ref ObjectId="254042"/>
    <cge:TPSR_Ref TObjectID="42098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254071">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 250.000000 -219.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42118" ObjectName="SW-CX_JSY.CX_JSY_351XC1"/>
     <cge:Meas_Ref ObjectId="254071"/>
    <cge:TPSR_Ref TObjectID="42118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254071">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 250.000000 -294.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42117" ObjectName="SW-CX_JSY.CX_JSY_351XC"/>
     <cge:Meas_Ref ObjectId="254071"/>
    <cge:TPSR_Ref TObjectID="42117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 481.000000 -292.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42121" ObjectName="SW-CX_JSY.CX_JSY_352XC"/>
     <cge:Meas_Ref ObjectId="254077"/>
    <cge:TPSR_Ref TObjectID="42121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 481.000000 -217.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42122" ObjectName="SW-CX_JSY.CX_JSY_352XC1"/>
     <cge:Meas_Ref ObjectId="254077"/>
    <cge:TPSR_Ref TObjectID="42122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 708.000000 -292.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 708.000000 -217.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254051">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.000000 -295.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42101" ObjectName="SW-CX_JSY.CX_JSY_354XC"/>
     <cge:Meas_Ref ObjectId="254051"/>
    <cge:TPSR_Ref TObjectID="42101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254051">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.000000 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42102" ObjectName="SW-CX_JSY.CX_JSY_354XC1"/>
     <cge:Meas_Ref ObjectId="254051"/>
    <cge:TPSR_Ref TObjectID="42102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.000000 -297.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42113" ObjectName="SW-CX_JSY.CX_JSY_357XC"/>
     <cge:Meas_Ref ObjectId="254066"/>
    <cge:TPSR_Ref TObjectID="42113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.000000 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42114" ObjectName="SW-CX_JSY.CX_JSY_357XC1"/>
     <cge:Meas_Ref ObjectId="254066"/>
    <cge:TPSR_Ref TObjectID="42114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.000000 -295.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42105" ObjectName="SW-CX_JSY.CX_JSY_355XC"/>
     <cge:Meas_Ref ObjectId="254056"/>
    <cge:TPSR_Ref TObjectID="42105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.000000 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42106" ObjectName="SW-CX_JSY.CX_JSY_355XC1"/>
     <cge:Meas_Ref ObjectId="254056"/>
    <cge:TPSR_Ref TObjectID="42106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254061">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1306.000000 -297.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42109" ObjectName="SW-CX_JSY.CX_JSY_356XC"/>
     <cge:Meas_Ref ObjectId="254061"/>
    <cge:TPSR_Ref TObjectID="42109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-254061">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1306.000000 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42110" ObjectName="SW-CX_JSY.CX_JSY_356XC1"/>
     <cge:Meas_Ref ObjectId="254061"/>
    <cge:TPSR_Ref TObjectID="42110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 251.000000 -4.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 -64.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_JSY.CX_JSY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-354 1676,-354 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42086" ObjectName="BS-CX_JSY.CX_JSY_3IM"/>
    <cge:TPSR_Ref TObjectID="42086"/></metadata>
   <polyline fill="none" opacity="0" points="171,-354 1676,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1302,-491 1604,-491 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1302,-491 1604,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_JSY.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-756 773,-756 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48130" ObjectName="BS-CX_JSY.XM"/>
    <cge:TPSR_Ref TObjectID="48130"/></metadata>
   <polyline fill="none" opacity="0" points="762,-756 773,-756 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1305760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1636.000000 -766.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa0550" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 910.000000 -523.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acc280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -897.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f2640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 -172.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a2cd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -817.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1615650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -658.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b59490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 -734.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c75a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -171.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ae0dd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 -165.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12aa430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -173.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140f4b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1187.000000 -171.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1644120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1388.000000 -175.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa3df0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1609.000000 -174.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b599f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1234.000000 -767.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d13f60" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 877.000000 -523.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ce6db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 187.000000 -35.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="173" x="-175" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="173" x="-175" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="31" qtmmishow="hidden" width="86" x="-25" y="-825"/>
    </a>
   <metadata/><rect fill="white" height="31" opacity="0" stroke="white" transform="" width="86" x="-25" y="-825"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-224" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-224" y="-957"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c36e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 448.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d0f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.000000 433.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa3200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 418.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12baa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 110.000000 388.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 105.000000 375.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f23e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 430.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeaa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 402.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1350ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 416.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12cb2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 823.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1297880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 808.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b41370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 839.000000 793.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 160.000000 -58.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c64420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 149.000000 -73.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19df400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 -88.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b215b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 -536.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_19d49e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-518 304,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1b55670@1" ObjectIDZND0="g_1b215b0@0" Pin0InfoVect0LinkObjId="g_1b215b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b55670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-518 304,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a18480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-396 304,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42099@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1aa2730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254049_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-396 304,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4ec60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-469 304,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1385fd0@0" ObjectIDZND0="g_1b55670@0" ObjectIDZND1="42099@x" Pin0InfoVect0LinkObjId="g_1b55670_0" Pin0InfoVect1LinkObjId="SW-254049_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1385fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-469 304,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16327c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-474 304,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42099@x" ObjectIDND1="g_1385fd0@0" ObjectIDZND0="g_1b55670@0" Pin0InfoVect0LinkObjId="g_1b55670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-254049_0" Pin1InfoVect1LinkObjId="g_1385fd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-474 304,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_163cfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-772 1640,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1305760@0" Pin0InfoVect0LinkObjId="g_1305760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-772 1640,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13545a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-664 698,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42094@0" ObjectIDZND0="g_1615650@0" Pin0InfoVect0LinkObjId="g_1615650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-664 698,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_165fc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-664 755,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="42090@x" ObjectIDND1="48921@x" ObjectIDZND0="42094@1" Pin0InfoVect0LinkObjId="SW-253995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-253991_0" Pin1InfoVect1LinkObjId="g_1cc4350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-664 755,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b220d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-639 768,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48921@1" ObjectIDZND0="42094@x" ObjectIDZND1="42090@x" Pin0InfoVect0LinkObjId="SW-253995_0" Pin0InfoVect1LinkObjId="SW-253991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc4350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="768,-639 768,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12c06e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-823 698,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42092@0" ObjectIDZND0="g_12a2cd0@0" Pin0InfoVect0LinkObjId="g_12a2cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253993_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-823 698,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b22350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-823 755,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="42088@x" ObjectIDND1="42089@x" ObjectIDZND0="42092@1" Pin0InfoVect0LinkObjId="SW-253993_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-253989_0" Pin1InfoVect1LinkObjId="SW-253990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-823 755,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15edc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-664 768,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="42094@x" ObjectIDND1="48921@x" ObjectIDZND0="42090@0" Pin0InfoVect0LinkObjId="SW-253991_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-253995_0" Pin1InfoVect1LinkObjId="g_1cc4350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-664 768,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1288760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-823 768,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42092@x" ObjectIDND1="42088@x" ObjectIDZND0="42089@0" Pin0InfoVect0LinkObjId="SW-253990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-253993_0" Pin1InfoVect1LinkObjId="SW-253989_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-823 768,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1634aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-806 768,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42088@1" ObjectIDZND0="42092@x" ObjectIDZND1="42089@x" Pin0InfoVect0LinkObjId="SW-253993_0" Pin0InfoVect1LinkObjId="SW-253990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="768,-806 768,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_130b640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-903 835,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42091@1" ObjectIDZND0="g_1acc280@0" Pin0InfoVect0LinkObjId="g_1acc280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253992_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="818,-903 835,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a40c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="337,-178 319,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_13f2640@0" ObjectIDZND0="42119@1" Pin0InfoVect0LinkObjId="SW-254072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f2640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="337,-178 319,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_15cb650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-619 847,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_1305590@0" ObjectIDZND1="42095@x" Pin0InfoVect0LinkObjId="g_1305590_0" Pin0InfoVect1LinkObjId="SW-254040_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="767,-619 847,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12b5b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,-585 846,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1305590@0" ObjectIDZND0="42095@x" Pin0InfoVect0LinkObjId="SW-254040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1305590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,-585 846,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_134fe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,-619 904,-619 904,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1305590@0" ObjectIDZND0="42095@1" Pin0InfoVect0LinkObjId="SW-254040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1305590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,-619 904,-619 904,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_162db40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-740 853,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42093@1" ObjectIDZND0="g_1b59490@0" Pin0InfoVect0LinkObjId="g_1b59490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253994_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-740 853,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a8a9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-779 768,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="42088@0" ObjectIDZND0="48130@0" Pin0InfoVect0LinkObjId="g_133f3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-779 768,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-485 767,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_14f8330@0" ObjectIDZND0="42098@x" ObjectIDZND1="48921@x" Pin0InfoVect0LinkObjId="SW-254042_0" Pin0InfoVect1LinkObjId="g_1cc4350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f8330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="746,-485 767,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cc4350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-485 767,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_14f8330@0" ObjectIDND1="42098@x" ObjectIDZND0="48921@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14f8330_0" Pin1InfoVect1LinkObjId="SW-254042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-485 767,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca34a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-474 304,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b55670@0" ObjectIDND1="g_1385fd0@0" ObjectIDZND0="42099@1" Pin0InfoVect0LinkObjId="SW-254049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b55670_0" Pin1InfoVect1LinkObjId="g_1385fd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-474 304,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b0c970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-575 814,-575 814,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-575 814,-575 814,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_130d790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="814,-525 814,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="814,-525 814,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a886c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="215,-206 260,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1383830@0" ObjectIDZND0="42118@x" ObjectIDZND1="42119@x" Pin0InfoVect0LinkObjId="SW-254071_0" Pin0InfoVect1LinkObjId="SW-254072_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1383830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="215,-206 260,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1666a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-227 260,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42118@0" ObjectIDZND0="g_1383830@0" ObjectIDZND1="42119@x" Pin0InfoVect0LinkObjId="g_1383830_0" Pin0InfoVect1LinkObjId="SW-254072_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254071_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="260,-227 260,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_140b610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="283,-179 260,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42119@0" ObjectIDZND0="g_1383830@0" ObjectIDZND1="42118@x" Pin0InfoVect0LinkObjId="g_1383830_0" Pin0InfoVect1LinkObjId="SW-254071_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="283,-179 260,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f2a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-177 549,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_15c75a0@0" ObjectIDZND0="42123@1" Pin0InfoVect0LinkObjId="SW-254078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c75a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,-177 549,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12cdc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="445,-205 490,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="g_13600e0@0" ObjectIDZND0="42123@x" ObjectIDZND1="0@x" ObjectIDZND2="42122@x" Pin0InfoVect0LinkObjId="SW-254078_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-254077_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13600e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="445,-205 490,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12cb090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-226 490,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="42122@0" ObjectIDZND0="g_13600e0@0" ObjectIDZND1="42123@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_13600e0_0" Pin0InfoVect1LinkObjId="SW-254078_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="490,-226 490,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f2cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-177 490,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="42123@0" ObjectIDZND0="g_13600e0@0" ObjectIDZND1="42122@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_13600e0_0" Pin0InfoVect1LinkObjId="SW-254077_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="513,-177 490,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144ec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-205 490,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_13600e0@0" ObjectIDND1="42122@x" ObjectIDZND0="42123@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-254078_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13600e0_0" Pin1InfoVect1LinkObjId="SW-254077_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="490,-205 490,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14b5700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-176 776,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ae0dd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ae0dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-176 776,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ac42d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,-204 717,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_13c5040@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13c5040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="672,-204 717,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1350250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-225 717,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_13c5040@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_13c5040_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="717,-225 717,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15c9410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="740,-176 717,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_13c5040@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_13c5040_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="740,-176 717,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a17340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-204 717,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_13c5040@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13c5040_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="717,-204 717,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1635bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-176 717,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_13c5040@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13c5040_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="717,-176 717,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a95350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-179 970,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12aa430@0" ObjectIDZND0="42103@1" Pin0InfoVect0LinkObjId="SW-254052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12aa430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-179 970,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a95060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-207 911,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="g_1b01390@0" ObjectIDZND0="42103@x" ObjectIDZND1="43331@x" ObjectIDZND2="42102@x" Pin0InfoVect0LinkObjId="SW-254052_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P1_0" Pin0InfoVect2LinkObjId="SW-254051_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b01390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="866,-207 911,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a96050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-228 911,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42102@0" ObjectIDZND0="g_1b01390@0" ObjectIDZND1="42103@x" ObjectIDZND2="43331@x" Pin0InfoVect0LinkObjId="g_1b01390_0" Pin0InfoVect1LinkObjId="SW-254052_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254051_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="911,-228 911,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_136c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,-179 911,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42103@0" ObjectIDZND0="g_1b01390@0" ObjectIDZND1="42102@x" ObjectIDZND2="43331@x" Pin0InfoVect0LinkObjId="g_1b01390_0" Pin0InfoVect1LinkObjId="SW-254051_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="934,-179 911,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b28f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-207 911,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="g_1b01390@0" ObjectIDND1="42102@x" ObjectIDZND0="42103@x" ObjectIDZND1="43331@x" Pin0InfoVect0LinkObjId="SW-254052_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b01390_0" Pin1InfoVect1LinkObjId="SW-254051_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="911,-207 911,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b683c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-179 911,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_1b01390@0" ObjectIDND1="42102@x" ObjectIDND2="42103@x" ObjectIDZND0="43331@0" Pin0InfoVect0LinkObjId="SM-CX_JSY.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b01390_0" Pin1InfoVect1LinkObjId="SW-254051_0" Pin1InfoVect2LinkObjId="SW-254052_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-179 911,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1191,-177 1173,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_140f4b0@0" ObjectIDZND0="42107@1" Pin0InfoVect0LinkObjId="SW-254057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_140f4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1191,-177 1173,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a176f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1069,-205 1114,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="g_1b61690@0" ObjectIDZND0="42107@x" ObjectIDZND1="43332@x" ObjectIDZND2="42106@x" Pin0InfoVect0LinkObjId="SW-254057_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P2_0" Pin0InfoVect2LinkObjId="SW-254056_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b61690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1069,-205 1114,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1285830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-226 1114,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42106@0" ObjectIDZND0="g_1b61690@0" ObjectIDZND1="42107@x" ObjectIDZND2="43332@x" Pin0InfoVect0LinkObjId="g_1b61690_0" Pin0InfoVect1LinkObjId="SW-254057_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-226 1114,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12860c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1137,-177 1114,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42107@0" ObjectIDZND0="g_1b61690@0" ObjectIDZND1="42106@x" ObjectIDZND2="43332@x" Pin0InfoVect0LinkObjId="g_1b61690_0" Pin0InfoVect1LinkObjId="SW-254056_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1137,-177 1114,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a17f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-205 1114,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="g_1b61690@0" ObjectIDND1="42106@x" ObjectIDZND0="42107@x" ObjectIDZND1="43332@x" Pin0InfoVect0LinkObjId="SW-254057_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b61690_0" Pin1InfoVect1LinkObjId="SW-254056_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-205 1114,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1286950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-177 1114,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_1b61690@0" ObjectIDND1="42106@x" ObjectIDND2="42107@x" ObjectIDZND0="43332@0" Pin0InfoVect0LinkObjId="SM-CX_JSY.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b61690_0" Pin1InfoVect1LinkObjId="SW-254056_0" Pin1InfoVect2LinkObjId="SW-254057_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-177 1114,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dc8f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1392,-181 1374,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1644120@0" ObjectIDZND0="42111@1" Pin0InfoVect0LinkObjId="SW-254062_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1644120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1392,-181 1374,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a727a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-209 1315,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="g_1aef1b0@0" ObjectIDZND0="42111@x" ObjectIDZND1="43333@x" ObjectIDZND2="42110@x" Pin0InfoVect0LinkObjId="SW-254062_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P3_0" Pin0InfoVect2LinkObjId="SW-254061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aef1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-209 1315,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a70300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1315,-230 1315,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42110@0" ObjectIDZND0="g_1aef1b0@0" ObjectIDZND1="42111@x" ObjectIDZND2="43333@x" Pin0InfoVect0LinkObjId="g_1aef1b0_0" Pin0InfoVect1LinkObjId="SW-254062_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1315,-230 1315,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a4ff40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-181 1315,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42111@0" ObjectIDZND0="g_1aef1b0@0" ObjectIDZND1="42110@x" ObjectIDZND2="43333@x" Pin0InfoVect0LinkObjId="g_1aef1b0_0" Pin0InfoVect1LinkObjId="SW-254061_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254062_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-181 1315,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1653090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1315,-209 1315,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="g_1aef1b0@0" ObjectIDND1="42110@x" ObjectIDZND0="42111@x" ObjectIDZND1="43333@x" Pin0InfoVect0LinkObjId="SW-254062_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aef1b0_0" Pin1InfoVect1LinkObjId="SW-254061_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1315,-209 1315,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dbbad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1315,-181 1315,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_1aef1b0@0" ObjectIDND1="42110@x" ObjectIDND2="42111@x" ObjectIDZND0="43333@0" Pin0InfoVect0LinkObjId="SM-CX_JSY.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1aef1b0_0" Pin1InfoVect1LinkObjId="SW-254061_0" Pin1InfoVect2LinkObjId="SW-254062_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1315,-181 1315,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d0440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1613,-180 1595,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1aa3df0@0" ObjectIDZND0="42115@1" Pin0InfoVect0LinkObjId="SW-254067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa3df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1613,-180 1595,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a31a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1491,-208 1536,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="g_13886c0@0" ObjectIDZND0="42115@x" ObjectIDZND1="43334@x" ObjectIDZND2="42114@x" Pin0InfoVect0LinkObjId="SW-254067_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P4_0" Pin0InfoVect2LinkObjId="SW-254066_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13886c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1491,-208 1536,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1635530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-229 1536,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42114@0" ObjectIDZND0="g_13886c0@0" ObjectIDZND1="42115@x" ObjectIDZND2="43334@x" Pin0InfoVect0LinkObjId="g_13886c0_0" Pin0InfoVect1LinkObjId="SW-254067_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-229 1536,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c48660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-180 1536,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="42115@0" ObjectIDZND0="g_13886c0@0" ObjectIDZND1="42114@x" ObjectIDZND2="43334@x" Pin0InfoVect0LinkObjId="g_13886c0_0" Pin0InfoVect1LinkObjId="SW-254066_0" Pin0InfoVect2LinkObjId="SM-CX_JSY.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-180 1536,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a5c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-208 1536,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="g_13886c0@0" ObjectIDND1="42114@x" ObjectIDZND0="42115@x" ObjectIDZND1="43334@x" Pin0InfoVect0LinkObjId="SW-254067_0" Pin0InfoVect1LinkObjId="SM-CX_JSY.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13886c0_0" Pin1InfoVect1LinkObjId="SW-254066_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-208 1536,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1645320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-180 1536,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_13886c0@0" ObjectIDND1="42114@x" ObjectIDND2="42115@x" ObjectIDZND0="43334@0" Pin0InfoVect0LinkObjId="SM-CX_JSY.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13886c0_0" Pin1InfoVect1LinkObjId="SW-254066_0" Pin1InfoVect2LinkObjId="SW-254067_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-180 1536,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a9f290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-177 490,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_13600e0@0" ObjectIDND1="42122@x" ObjectIDND2="42123@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13600e0_0" Pin1InfoVect1LinkObjId="SW-254077_0" Pin1InfoVect2LinkObjId="SW-254078_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="490,-177 490,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa2730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-319 260,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42117@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1a18480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254071_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="260,-319 260,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d7be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-318 490,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42121@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1a18480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="490,-318 490,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca2590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-317 717,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1a18480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="717,-317 717,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d8fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-320 911,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42101@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1a18480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254051_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-320 911,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acc980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-318 1114,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42105@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1a18480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-318 1114,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d8310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1315,-322 1315,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42109@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1a18480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1315,-322 1315,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c3ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-321 1536,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42113@0" ObjectIDZND0="42086@0" Pin0InfoVect0LinkObjId="g_1a18480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-321 1536,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12c0f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-773 1267,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_1b599f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b599f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1252,-773 1267,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b4e9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1455,-518 1455,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1455,-518 1455,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1af2310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1651,-672 1635,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12b5040@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b5040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1651,-672 1635,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b3ec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1599,-672 1578,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1599,-672 1578,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1410240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-672 1578,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-672 1578,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1634d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1390,-672 1374,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ac2d10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac2d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1390,-672 1374,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b2f430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-672 1317,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-672 1317,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1aa8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-672 1317,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-672 1317,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1aaeb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-672 1317,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-672 1317,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1369500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-563 1317,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-563 1317,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a03780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-530 1578,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-530 1578,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12b10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-617 1578,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-617 1578,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1aa4bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1565,-530 1565,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1565,-530 1565,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a89980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1327,-551 1327,-531 1323,-531 1323,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1327,-551 1327,-531 1323,-531 1323,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d1c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-935 768,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1286c00@0" ObjectIDZND0="42091@x" ObjectIDZND1="42089@x" ObjectIDZND2="g_1d1c6c0@0" Pin0InfoVect0LinkObjId="SW-253992_0" Pin0InfoVect1LinkObjId="SW-253990_0" Pin0InfoVect2LinkObjId="g_1d1c6c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1286c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="734,-935 768,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1aa1a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="782,-903 768,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="42091@0" ObjectIDZND0="42089@x" ObjectIDZND1="g_1286c00@0" ObjectIDZND2="g_1d1c6c0@0" Pin0InfoVect0LinkObjId="SW-253990_0" Pin0InfoVect1LinkObjId="g_1286c00_0" Pin0InfoVect2LinkObjId="g_1d1c6c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253992_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="782,-903 768,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a0cd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-883 768,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="42089@1" ObjectIDZND0="42091@x" ObjectIDZND1="g_1286c00@0" ObjectIDZND2="g_1d1c6c0@0" Pin0InfoVect0LinkObjId="SW-253992_0" Pin0InfoVect1LinkObjId="g_1286c00_0" Pin0InfoVect2LinkObjId="g_1d1c6c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="768,-883 768,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_165d300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-903 768,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="42091@x" ObjectIDND1="42089@x" ObjectIDZND0="g_1286c00@0" ObjectIDZND1="g_1d1c6c0@0" ObjectIDZND2="42218@1" Pin0InfoVect0LinkObjId="g_1286c00_0" Pin0InfoVect1LinkObjId="g_1d1c6c0_0" Pin0InfoVect2LinkObjId="g_1374fc0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-253992_0" Pin1InfoVect1LinkObjId="SW-253990_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="768,-903 768,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cbf0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-541 904,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1aa0550@0" ObjectIDZND0="42095@0" Pin0InfoVect0LinkObjId="SW-254040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa0550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-541 904,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b47170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-432 767,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42096@1" ObjectIDZND0="42098@1" Pin0InfoVect0LinkObjId="SW-254042_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254041_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-432 767,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b2dc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-467 767,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="42098@0" ObjectIDZND0="g_14f8330@0" ObjectIDZND1="48921@x" Pin0InfoVect0LinkObjId="g_14f8330_0" Pin0InfoVect1LinkObjId="g_1cc4350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254042_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="767,-467 767,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b24170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-356 767,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="42086@0" ObjectIDZND0="42097@0" Pin0InfoVect0LinkObjId="SW-254042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a18480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-356 767,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_130ad90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-392 767,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42097@1" ObjectIDZND0="42096@0" Pin0InfoVect0LinkObjId="SW-254041_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="767,-392 767,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aef4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-283 260,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42116@1" ObjectIDZND0="42117@1" Pin0InfoVect0LinkObjId="SW-254071_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="260,-283 260,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c1090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-243 260,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42118@1" ObjectIDZND0="42116@0" Pin0InfoVect0LinkObjId="SW-254070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254071_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="260,-243 260,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a16880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="491,-281 491,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42120@1" ObjectIDZND0="42121@1" Pin0InfoVect0LinkObjId="SW-254077_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="491,-281 491,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1355050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="491,-241 491,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42122@1" ObjectIDZND0="42120@0" Pin0InfoVect0LinkObjId="SW-254076_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="491,-241 491,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1347f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="718,-281 718,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="718,-281 718,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1af9cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="718,-241 718,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="718,-241 718,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12a5870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-284 911,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42100@1" ObjectIDZND0="42101@1" Pin0InfoVect0LinkObjId="SW-254051_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-284 911,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1426180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="911,-244 911,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42102@1" ObjectIDZND0="42100@0" Pin0InfoVect0LinkObjId="SW-254050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-244 911,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ae5280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-286 1537,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42112@1" ObjectIDZND0="42113@1" Pin0InfoVect0LinkObjId="SW-254066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-286 1537,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ae54e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-246 1537,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42114@1" ObjectIDZND0="42112@0" Pin0InfoVect0LinkObjId="SW-254065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-246 1537,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b53ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-284 1114,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42104@1" ObjectIDZND0="42105@1" Pin0InfoVect0LinkObjId="SW-254056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-284 1114,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b53d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-244 1114,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42106@1" ObjectIDZND0="42104@0" Pin0InfoVect0LinkObjId="SW-254055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-244 1114,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16300c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-286 1316,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42108@1" ObjectIDZND0="42109@1" Pin0InfoVect0LinkObjId="SW-254061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-286 1316,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d5b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-246 1316,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42110@1" ObjectIDZND0="42108@0" Pin0InfoVect0LinkObjId="SW-254060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-254061_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-246 1316,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13767c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-206 260,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1383830@0" ObjectIDND1="42118@x" ObjectIDZND0="42119@x" Pin0InfoVect0LinkObjId="SW-254072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1383830_0" Pin1InfoVect1LinkObjId="SW-254071_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="260,-206 260,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1376a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-125 260,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="42119@x" ObjectIDZND1="g_1383830@0" ObjectIDZND2="42118@x" Pin0InfoVect0LinkObjId="SW-254072_0" Pin0InfoVect1LinkObjId="g_1383830_0" Pin0InfoVect2LinkObjId="SW-254071_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="260,-125 260,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1424ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,135 260,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="260,135 260,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14258b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="277,8 260,8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="g_1424d90@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1424d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="277,8 260,8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13743b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-9 260,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="g_1424d90@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1424d90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="260,-9 260,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1374610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,7 260,15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="g_1424d90@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1424d90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="260,7 260,15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1374fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-957 768,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="currentTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1d1c6c0@0" ObjectIDND1="g_1286c00@0" ObjectIDND2="42091@x" ObjectIDZND0="42218@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d1c6c0_0" Pin1InfoVect1LinkObjId="g_1286c00_0" Pin1InfoVect2LinkObjId="SW-253992_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-957 768,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_132fea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="787,-957 768,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d1c6c0@0" ObjectIDZND0="g_1286c00@0" ObjectIDZND1="42091@x" ObjectIDZND2="42089@x" Pin0InfoVect0LinkObjId="g_1286c00_0" Pin0InfoVect1LinkObjId="SW-253992_0" Pin0InfoVect2LinkObjId="SW-253990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d1c6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="787,-957 768,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1330100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-957 768,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d1c6c0@0" ObjectIDND1="42218@1" ObjectIDZND0="g_1286c00@0" ObjectIDZND1="42091@x" ObjectIDZND2="42089@x" Pin0InfoVect0LinkObjId="g_1286c00_0" Pin0InfoVect1LinkObjId="SW-253992_0" Pin0InfoVect2LinkObjId="SW-253990_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d1c6c0_0" Pin1InfoVect1LinkObjId="g_1374fc0_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="768,-957 768,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce7790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-69 193,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1ce6db0@0" Pin0InfoVect0LinkObjId="g_1ce6db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-69 193,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce79f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="229,-69 260,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="229,-69 260,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f35360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-75 260,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="260,-75 260,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f355c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-69 260,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="260,-69 260,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_133f3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-740 768,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="42093@x" ObjectIDND1="42090@x" ObjectIDZND0="48130@0" Pin0InfoVect0LinkObjId="g_1a8a9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-253994_0" Pin1InfoVect1LinkObjId="SW-253991_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-740 768,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_133fe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="800,-740 768,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="42093@0" ObjectIDZND0="48130@0" ObjectIDZND1="42090@x" Pin0InfoVect0LinkObjId="g_1a8a9f0_0" Pin0InfoVect1LinkObjId="SW-253991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-253994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="800,-740 768,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13400e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-740 768,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="48130@0" ObjectIDND1="42093@x" ObjectIDZND0="42090@1" Pin0InfoVect0LinkObjId="SW-253991_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a8a9f0_0" Pin1InfoVect1LinkObjId="SW-253994_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-740 768,-720 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="173" x="-175" y="-940"/></g>
   <g href="AVC尖山营.svg" style="fill-opacity:0"><rect height="31" qtmmishow="hidden" width="86" x="-25" y="-825"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-224" y="-957"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="494" lineStyle="1" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="561" x="1197" y="-915"/>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -823.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42088"/>
     <cge:Term_Ref ObjectID="17427"/>
    <cge:TPSR_Ref TObjectID="42088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -823.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42088"/>
     <cge:Term_Ref ObjectID="17427"/>
    <cge:TPSR_Ref TObjectID="42088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -823.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42088"/>
     <cge:Term_Ref ObjectID="17427"/>
    <cge:TPSR_Ref TObjectID="42088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 894.000000 -448.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42096"/>
     <cge:Term_Ref ObjectID="17443"/>
    <cge:TPSR_Ref TObjectID="42096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 894.000000 -448.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42096"/>
     <cge:Term_Ref ObjectID="17443"/>
    <cge:TPSR_Ref TObjectID="42096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 894.000000 -448.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42096"/>
     <cge:Term_Ref ObjectID="17443"/>
    <cge:TPSR_Ref TObjectID="42096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 60.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42116"/>
     <cge:Term_Ref ObjectID="17487"/>
    <cge:TPSR_Ref TObjectID="42116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 60.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42116"/>
     <cge:Term_Ref ObjectID="17487"/>
    <cge:TPSR_Ref TObjectID="42116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 60.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42116"/>
     <cge:Term_Ref ObjectID="17487"/>
    <cge:TPSR_Ref TObjectID="42116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 464.000000 61.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42120"/>
     <cge:Term_Ref ObjectID="17495"/>
    <cge:TPSR_Ref TObjectID="42120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 464.000000 61.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42120"/>
     <cge:Term_Ref ObjectID="17495"/>
    <cge:TPSR_Ref TObjectID="42120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 464.000000 61.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42120"/>
     <cge:Term_Ref ObjectID="17495"/>
    <cge:TPSR_Ref TObjectID="42120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42100"/>
     <cge:Term_Ref ObjectID="17451"/>
    <cge:TPSR_Ref TObjectID="42100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 87.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42100"/>
     <cge:Term_Ref ObjectID="17451"/>
    <cge:TPSR_Ref TObjectID="42100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 87.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42100"/>
     <cge:Term_Ref ObjectID="17451"/>
    <cge:TPSR_Ref TObjectID="42100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42104"/>
     <cge:Term_Ref ObjectID="17459"/>
    <cge:TPSR_Ref TObjectID="42104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 87.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42104"/>
     <cge:Term_Ref ObjectID="17459"/>
    <cge:TPSR_Ref TObjectID="42104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 87.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42104"/>
     <cge:Term_Ref ObjectID="17459"/>
    <cge:TPSR_Ref TObjectID="42104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42108"/>
     <cge:Term_Ref ObjectID="17467"/>
    <cge:TPSR_Ref TObjectID="42108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 87.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42108"/>
     <cge:Term_Ref ObjectID="17467"/>
    <cge:TPSR_Ref TObjectID="42108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 87.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42108"/>
     <cge:Term_Ref ObjectID="17467"/>
    <cge:TPSR_Ref TObjectID="42108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-253970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42112"/>
     <cge:Term_Ref ObjectID="17475"/>
    <cge:TPSR_Ref TObjectID="42112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-253971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 87.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42112"/>
     <cge:Term_Ref ObjectID="17475"/>
    <cge:TPSR_Ref TObjectID="42112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-253972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 87.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42112"/>
     <cge:Term_Ref ObjectID="17475"/>
    <cge:TPSR_Ref TObjectID="42112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-253944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 177.000000 -432.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42086"/>
     <cge:Term_Ref ObjectID="17424"/>
    <cge:TPSR_Ref TObjectID="42086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-253945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 177.000000 -432.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42086"/>
     <cge:Term_Ref ObjectID="17424"/>
    <cge:TPSR_Ref TObjectID="42086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-253946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 177.000000 -432.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42086"/>
     <cge:Term_Ref ObjectID="17424"/>
    <cge:TPSR_Ref TObjectID="42086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-253950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 177.000000 -432.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42086"/>
     <cge:Term_Ref ObjectID="17424"/>
    <cge:TPSR_Ref TObjectID="42086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-253947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 177.000000 -432.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="253947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42086"/>
     <cge:Term_Ref ObjectID="17424"/>
    <cge:TPSR_Ref TObjectID="42086"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.753846 -0.000000 0.000000 -0.808219 235.000000 129.000000)" xlink:href="#capacitor:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 243.000000 165.000000)" xlink:href="#capacitor:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_JSY"/>
</svg>