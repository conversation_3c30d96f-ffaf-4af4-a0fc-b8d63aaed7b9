<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" viewBox="-2 -2971 4209 2973">
 
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    
   </symbol>
   <symbol id="Tag:shape27">
    
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="2983" width="4219" x="-7" y="-2976"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="0" fill="none" points="3392,-2631 3393,-2631 3394,-2631 3395,-2632 3396,-2632 3397,-2633 3398,-2634 3399,-2635 3399,-2636 3400,-2637 3400,-2638 3400,-2639 3400,-2640 3400,-2641 3400,-2642 3400,-2643 3399,-2644 3399,-2645 3398,-2646 3397,-2647 3396,-2648 3395,-2648 3394,-2649 3393,-2649 3392,-2649 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3392,-2613 3393,-2613 3394,-2613 3395,-2614 3396,-2614 3397,-2615 3398,-2616 3399,-2617 3399,-2618 3400,-2619 3400,-2620 3400,-2621 3400,-2622 3400,-2623 3400,-2624 3400,-2625 3399,-2626 3399,-2627 3398,-2628 3397,-2629 3396,-2630 3395,-2630 3394,-2631 3393,-2631 3392,-2631 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3392,-2544 3393,-2544 3394,-2544 3395,-2544 3397,-2545 3398,-2545 3398,-2546 3399,-2547 3400,-2548 3401,-2549 3401,-2550 3402,-2551 3402,-2552 3402,-2553 3402,-2554 3402,-2555 3401,-2556 3401,-2557 3400,-2558 3399,-2559 3398,-2560 3398,-2561 3397,-2561 3395,-2562 3394,-2562 3393,-2562 3392,-2562 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3392,-2526 3393,-2526 3394,-2526 3395,-2526 3397,-2527 3398,-2527 3398,-2528 3399,-2529 3400,-2530 3401,-2531 3401,-2532 3402,-2533 3402,-2534 3402,-2535 3402,-2536 3402,-2537 3401,-2538 3401,-2539 3400,-2540 3399,-2541 3398,-2542 3398,-2543 3397,-2543 3395,-2544 3394,-2544 3393,-2544 3392,-2544 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3392,-2435 3393,-2435 3394,-2435 3395,-2435 3397,-2436 3398,-2436 3398,-2437 3399,-2438 3400,-2439 3401,-2440 3401,-2441 3402,-2442 3402,-2443 3402,-2444 3402,-2445 3402,-2446 3401,-2447 3401,-2448 3400,-2449 3399,-2450 3398,-2451 3398,-2452 3397,-2452 3395,-2453 3394,-2453 3393,-2453 3392,-2453 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3392,-2417 3393,-2417 3394,-2417 3395,-2418 3396,-2418 3397,-2419 3398,-2420 3399,-2421 3399,-2422 3400,-2423 3400,-2424 3400,-2425 3400,-2426 3400,-2427 3400,-2428 3400,-2429 3399,-2430 3399,-2431 3398,-2432 3397,-2433 3396,-2434 3395,-2434 3394,-2435 3393,-2435 3392,-2435 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2087,-953 2087,-952 2087,-951 2087,-950 2088,-949 2089,-948 2089,-947 2090,-946 2091,-945 2092,-945 2093,-944 2094,-944 2095,-944 2096,-944 2097,-944 2098,-944 2099,-944 2100,-945 2101,-945 2102,-946 2103,-947 2104,-948 2104,-949 2105,-950 2105,-951 2105,-952 2105,-953 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2105,-953 2105,-952 2105,-951 2105,-950 2106,-949 2107,-948 2107,-947 2108,-946 2109,-945 2110,-945 2111,-944 2112,-944 2113,-944 2114,-944 2115,-944 2116,-944 2117,-944 2118,-945 2119,-945 2120,-946 2121,-947 2122,-948 2122,-949 2123,-950 2123,-951 2123,-952 2123,-953 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2087,-608 2087,-607 2087,-606 2087,-605 2088,-604 2089,-603 2089,-602 2090,-601 2091,-600 2092,-600 2093,-599 2094,-599 2095,-599 2096,-599 2097,-599 2098,-599 2099,-599 2100,-600 2101,-600 2102,-601 2103,-602 2104,-603 2104,-604 2105,-605 2105,-606 2105,-607 2105,-608 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2105,-608 2105,-607 2105,-606 2105,-605 2106,-604 2107,-603 2107,-602 2108,-601 2109,-600 2110,-600 2111,-599 2112,-599 2113,-599 2114,-599 2115,-599 2116,-599 2117,-599 2118,-600 2119,-600 2120,-601 2121,-602 2122,-603 2122,-604 2123,-605 2123,-606 2123,-607 2123,-608 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1332,-895 1332,-894 1332,-893 1333,-892 1333,-891 1334,-890 1335,-889 1336,-888 1337,-888 1338,-887 1339,-887 1340,-887 1341,-887 1342,-887 1343,-887 1344,-887 1345,-888 1346,-888 1347,-889 1348,-890 1349,-891 1349,-892 1350,-893 1350,-894 1350,-895 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1350,-895 1350,-894 1350,-893 1351,-892 1351,-891 1352,-890 1353,-889 1354,-888 1355,-888 1356,-887 1357,-887 1358,-887 1359,-887 1360,-887 1361,-887 1362,-887 1363,-888 1364,-888 1365,-889 1366,-890 1367,-891 1367,-892 1368,-893 1368,-894 1368,-895 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1328,-973 1329,-973 1330,-973 1331,-974 1332,-974 1333,-975 1334,-976 1335,-977 1335,-978 1336,-979 1336,-980 1336,-981 1336,-982 1336,-983 1336,-984 1336,-985 1335,-986 1335,-987 1334,-988 1333,-989 1332,-990 1331,-990 1330,-991 1329,-991 1328,-991 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1328,-955 1329,-955 1330,-955 1331,-956 1332,-956 1333,-957 1334,-958 1335,-959 1335,-960 1336,-961 1336,-962 1336,-963 1336,-964 1336,-965 1336,-966 1336,-967 1335,-968 1335,-969 1334,-970 1333,-971 1332,-972 1331,-972 1330,-973 1329,-973 1328,-973 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="844,-653 844,-652 844,-651 844,-650 845,-649 846,-648 846,-647 847,-646 848,-645 849,-645 850,-644 851,-644 852,-644 853,-644 854,-644 855,-644 856,-644 857,-645 858,-645 859,-646 860,-647 861,-648 861,-649 862,-650 862,-651 862,-652 862,-653 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="862,-653 862,-652 862,-651 862,-650 863,-649 864,-648 864,-647 865,-646 866,-645 867,-645 868,-644 869,-644 870,-644 871,-644 872,-644 873,-644 874,-644 875,-645 876,-645 877,-646 878,-647 879,-648 879,-649 880,-650 880,-651 880,-652 880,-653 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="264,-1140 264,-1139 264,-1138 265,-1137 265,-1136 266,-1135 267,-1134 268,-1133 269,-1133 270,-1132 271,-1132 272,-1132 273,-1132 274,-1132 275,-1132 276,-1132 277,-1133 278,-1133 279,-1134 280,-1135 281,-1136 281,-1137 282,-1138 282,-1139 282,-1140 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="282,-1140 282,-1139 282,-1138 283,-1137 283,-1136 284,-1135 285,-1134 286,-1133 287,-1133 288,-1132 289,-1132 290,-1132 291,-1132 292,-1132 293,-1132 294,-1132 295,-1133 296,-1133 297,-1134 298,-1135 299,-1136 299,-1137 300,-1138 300,-1139 300,-1140 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3423,-27 3423,-26 3423,-25 3424,-24 3424,-23 3425,-22 3426,-21 3427,-20 3428,-20 3429,-19 3430,-19 3431,-19 3432,-19 3433,-19 3434,-19 3435,-19 3436,-20 3437,-20 3438,-21 3439,-22 3440,-23 3440,-24 3441,-25 3441,-26 3441,-27 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3441,-27 3441,-26 3441,-25 3442,-24 3442,-23 3443,-22 3444,-21 3445,-20 3446,-20 3447,-19 3448,-19 3449,-19 3450,-19 3451,-19 3452,-19 3453,-19 3454,-20 3455,-20 3456,-21 3457,-22 3458,-23 3458,-24 3459,-25 3459,-26 3459,-27 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="213,-736 214,-736 215,-736 216,-737 217,-737 218,-738 219,-739 220,-740 220,-741 221,-742 221,-743 221,-744 221,-745 221,-746 221,-747 221,-748 220,-749 220,-750 219,-751 218,-752 217,-753 216,-753 215,-754 214,-754 213,-754 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="213,-718 214,-718 215,-718 216,-719 217,-719 218,-720 219,-721 220,-722 220,-723 221,-724 221,-725 221,-726 221,-727 221,-728 221,-729 221,-730 220,-731 220,-732 219,-733 218,-734 217,-735 216,-735 215,-736 214,-736 213,-736 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2021,-1455 2021,-1454 2021,-1453 2021,-1452 2022,-1451 2023,-1450 2023,-1449 2024,-1448 2025,-1447 2026,-1447 2027,-1446 2028,-1446 2029,-1446 2030,-1446 2031,-1446 2032,-1446 2033,-1446 2034,-1447 2035,-1447 2036,-1448 2037,-1449 2038,-1450 2038,-1451 2039,-1452 2039,-1453 2039,-1454 2039,-1455 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2039,-1455 2039,-1454 2039,-1453 2039,-1452 2040,-1451 2041,-1450 2041,-1449 2042,-1448 2043,-1447 2044,-1447 2045,-1446 2046,-1446 2047,-1446 2048,-1446 2049,-1446 2050,-1446 2051,-1446 2052,-1447 2053,-1447 2054,-1448 2055,-1449 2056,-1450 2056,-1451 2057,-1452 2057,-1453 2057,-1454 2057,-1455 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2503,-933 2504,-933 2505,-933 2506,-934 2507,-934 2508,-935 2509,-936 2510,-937 2510,-938 2511,-939 2511,-940 2511,-941 2511,-942 2511,-943 2511,-944 2511,-945 2510,-946 2510,-947 2509,-948 2508,-949 2507,-950 2506,-950 2505,-951 2504,-951 2503,-951 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2503,-914 2504,-914 2505,-914 2506,-914 2508,-915 2509,-915 2509,-916 2510,-917 2511,-918 2512,-919 2512,-920 2513,-921 2513,-922 2513,-923 2513,-924 2513,-925 2512,-926 2512,-927 2511,-928 2510,-929 2509,-930 2509,-931 2508,-931 2506,-932 2505,-932 2504,-932 2503,-932 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="923,-1253 923,-1252 923,-1251 924,-1250 924,-1249 925,-1248 926,-1247 927,-1246 928,-1246 929,-1245 930,-1245 931,-1245 932,-1245 933,-1245 934,-1245 935,-1245 936,-1246 937,-1246 938,-1247 939,-1248 940,-1249 940,-1250 941,-1251 941,-1252 941,-1253 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="941,-1253 941,-1252 941,-1251 942,-1250 942,-1249 943,-1248 944,-1247 945,-1246 946,-1246 947,-1245 948,-1245 949,-1245 950,-1245 951,-1245 952,-1245 953,-1245 954,-1246 955,-1246 956,-1247 957,-1248 958,-1249 958,-1250 959,-1251 959,-1252 959,-1253 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="221,-1001 221,-1000 221,-999 222,-998 222,-997 223,-996 224,-995 225,-994 226,-994 227,-993 228,-993 229,-993 230,-993 231,-993 232,-993 233,-993 234,-994 235,-994 236,-995 237,-996 238,-997 238,-998 239,-999 239,-1000 239,-1001 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="239,-1001 239,-1000 239,-999 239,-998 240,-996 240,-995 241,-995 242,-994 243,-993 244,-992 245,-992 246,-991 247,-991 248,-991 249,-991 250,-991 251,-992 252,-992 253,-993 254,-994 255,-995 256,-995 256,-996 257,-998 257,-999 257,-1000 257,-1001 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1343,-1340 1343,-1339 1343,-1338 1343,-1336 1343,-1335 1344,-1334 1344,-1333 1345,-1332 1346,-1331 1347,-1331 1348,-1330 1349,-1330 1350,-1329 1351,-1329 1353,-1329 1354,-1329 1355,-1330 1356,-1330 1357,-1331 1358,-1331 1359,-1332 1360,-1333 1360,-1334 1361,-1335 1361,-1336 1361,-1338 1361,-1339 1361,-1340 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1361,-1340 1361,-1339 1361,-1338 1361,-1336 1361,-1335 1362,-1334 1362,-1333 1363,-1332 1364,-1331 1365,-1331 1366,-1330 1367,-1330 1368,-1329 1369,-1329 1371,-1329 1372,-1329 1373,-1330 1374,-1330 1375,-1331 1376,-1331 1377,-1332 1378,-1333 1378,-1334 1379,-1335 1379,-1336 1379,-1338 1379,-1339 1379,-1340 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1337,-1516 1337,-1515 1337,-1514 1338,-1513 1338,-1512 1339,-1511 1340,-1510 1341,-1509 1342,-1509 1343,-1508 1344,-1508 1345,-1508 1346,-1508 1347,-1508 1348,-1508 1349,-1508 1350,-1509 1351,-1509 1352,-1510 1353,-1511 1354,-1512 1354,-1513 1355,-1514 1355,-1515 1355,-1516 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1355,-1516 1355,-1515 1355,-1514 1356,-1513 1356,-1512 1357,-1511 1358,-1510 1359,-1509 1360,-1509 1361,-1508 1362,-1508 1363,-1508 1364,-1508 1365,-1508 1366,-1508 1367,-1508 1368,-1509 1369,-1509 1370,-1510 1371,-1511 1372,-1512 1372,-1513 1373,-1514 1373,-1515 1373,-1516 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1962,-2050 1962,-2049 1962,-2048 1962,-2047 1963,-2046 1964,-2045 1964,-2044 1965,-2043 1966,-2042 1967,-2042 1968,-2041 1969,-2041 1970,-2041 1971,-2041 1972,-2041 1973,-2041 1974,-2041 1975,-2042 1976,-2042 1977,-2043 1978,-2044 1979,-2045 1979,-2046 1980,-2047 1980,-2048 1980,-2049 1980,-2050 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1980,-2050 1980,-2049 1980,-2048 1980,-2047 1981,-2046 1982,-2045 1982,-2044 1983,-2043 1984,-2042 1985,-2042 1986,-2041 1987,-2041 1988,-2041 1989,-2041 1990,-2041 1991,-2041 1992,-2041 1993,-2042 1994,-2042 1995,-2043 1996,-2044 1997,-2045 1997,-2046 1998,-2047 1998,-2048 1998,-2049 1998,-2050 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3394,-1803 3394,-1802 3394,-1801 3394,-1800 3395,-1799 3396,-1798 3396,-1797 3397,-1796 3398,-1795 3399,-1795 3400,-1794 3401,-1794 3402,-1794 3403,-1794 3404,-1794 3405,-1794 3406,-1794 3407,-1795 3408,-1795 3409,-1796 3410,-1797 3411,-1798 3411,-1799 3412,-1800 3412,-1801 3412,-1802 3412,-1803 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3412,-1803 3412,-1802 3412,-1801 3412,-1800 3413,-1799 3414,-1798 3414,-1797 3415,-1796 3416,-1795 3417,-1795 3418,-1794 3419,-1794 3420,-1794 3421,-1794 3422,-1794 3423,-1794 3424,-1794 3425,-1795 3426,-1795 3427,-1796 3428,-1797 3429,-1798 3429,-1799 3430,-1800 3430,-1801 3430,-1802 3430,-1803 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3352,-2120 3352,-2119 3352,-2118 3353,-2117 3353,-2116 3354,-2115 3355,-2114 3356,-2113 3357,-2113 3358,-2112 3359,-2112 3360,-2112 3361,-2112 3362,-2112 3363,-2112 3364,-2112 3365,-2113 3366,-2113 3367,-2114 3368,-2115 3369,-2116 3369,-2117 3370,-2118 3370,-2119 3370,-2120 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3370,-2120 3370,-2119 3370,-2118 3371,-2117 3371,-2116 3372,-2115 3373,-2114 3374,-2113 3375,-2113 3376,-2112 3377,-2112 3378,-2112 3379,-2112 3380,-2112 3381,-2112 3382,-2112 3383,-2113 3384,-2113 3385,-2114 3386,-2115 3387,-2116 3387,-2117 3388,-2118 3388,-2119 3388,-2120 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="706,-860 707,-860 708,-860 709,-860 710,-861 711,-862 712,-862 713,-863 714,-864 714,-865 715,-866 715,-867 715,-868 715,-869 715,-870 715,-871 715,-872 714,-873 714,-874 713,-875 712,-876 711,-877 710,-877 709,-878 708,-878 707,-878 706,-878 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="706,-842 707,-842 708,-842 709,-842 710,-843 711,-844 712,-844 713,-845 714,-846 714,-847 715,-848 715,-849 715,-850 715,-851 715,-852 715,-853 715,-854 714,-855 714,-856 713,-857 712,-858 711,-859 710,-859 709,-860 708,-860 707,-860 706,-860 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1718,-1770 1718,-1769 1718,-1768 1719,-1767 1719,-1766 1720,-1765 1721,-1764 1722,-1763 1723,-1763 1724,-1762 1725,-1762 1726,-1762 1727,-1762 1728,-1762 1729,-1762 1730,-1762 1731,-1763 1732,-1763 1733,-1764 1734,-1765 1735,-1766 1735,-1767 1736,-1768 1736,-1769 1736,-1770 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1736,-1770 1736,-1769 1736,-1768 1737,-1767 1737,-1766 1738,-1765 1739,-1764 1740,-1763 1741,-1763 1742,-1762 1743,-1762 1744,-1762 1745,-1762 1746,-1762 1747,-1762 1748,-1762 1749,-1763 1750,-1763 1751,-1764 1752,-1765 1753,-1766 1753,-1767 1754,-1768 1754,-1769 1754,-1770 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1717,-1669 1717,-1668 1717,-1667 1717,-1666 1718,-1665 1719,-1664 1719,-1663 1720,-1662 1721,-1661 1722,-1661 1723,-1660 1724,-1660 1725,-1660 1726,-1660 1727,-1660 1728,-1660 1729,-1660 1730,-1661 1731,-1661 1732,-1662 1733,-1663 1734,-1664 1734,-1665 1735,-1666 1735,-1667 1735,-1668 1735,-1669 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1735,-1669 1735,-1668 1735,-1667 1735,-1666 1736,-1665 1737,-1664 1737,-1663 1738,-1662 1739,-1661 1740,-1661 1741,-1660 1742,-1660 1743,-1660 1744,-1660 1745,-1660 1746,-1660 1747,-1660 1748,-1661 1749,-1661 1750,-1662 1751,-1663 1752,-1664 1752,-1665 1753,-1666 1753,-1667 1753,-1668 1753,-1669 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1617,-1563 1617,-1562 1617,-1561 1618,-1560 1618,-1559 1619,-1558 1620,-1557 1621,-1556 1622,-1556 1623,-1555 1624,-1555 1625,-1555 1626,-1555 1627,-1555 1628,-1555 1629,-1555 1630,-1556 1631,-1556 1632,-1557 1633,-1558 1634,-1559 1634,-1560 1635,-1561 1635,-1562 1635,-1563 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1635,-1563 1635,-1562 1635,-1561 1636,-1560 1636,-1559 1637,-1558 1638,-1557 1639,-1556 1640,-1556 1641,-1555 1642,-1555 1643,-1555 1644,-1555 1645,-1555 1646,-1555 1647,-1555 1648,-1556 1649,-1556 1650,-1557 1651,-1558 1652,-1559 1652,-1560 1653,-1561 1653,-1562 1653,-1563 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1716,-1182 1716,-1181 1716,-1180 1717,-1179 1717,-1178 1718,-1177 1719,-1176 1720,-1175 1721,-1175 1722,-1174 1723,-1174 1724,-1174 1725,-1174 1726,-1174 1727,-1174 1728,-1174 1729,-1175 1730,-1175 1731,-1176 1732,-1177 1733,-1178 1733,-1179 1734,-1180 1734,-1181 1734,-1182 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1734,-1182 1734,-1181 1734,-1180 1735,-1179 1735,-1178 1736,-1177 1737,-1176 1738,-1175 1739,-1175 1740,-1174 1741,-1174 1742,-1174 1743,-1174 1744,-1174 1745,-1174 1746,-1174 1747,-1175 1748,-1175 1749,-1176 1750,-1177 1751,-1178 1751,-1179 1752,-1180 1752,-1181 1752,-1182 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1716,-839 1716,-838 1716,-837 1716,-836 1717,-834 1717,-833 1718,-833 1719,-832 1720,-831 1721,-830 1722,-830 1723,-829 1724,-829 1725,-829 1726,-829 1727,-829 1728,-830 1729,-830 1730,-831 1731,-832 1732,-833 1733,-833 1733,-834 1734,-836 1734,-837 1734,-838 1734,-839 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1734,-839 1734,-838 1734,-837 1734,-836 1735,-834 1735,-833 1736,-833 1737,-832 1738,-831 1739,-830 1740,-830 1741,-829 1742,-829 1743,-829 1744,-829 1745,-829 1746,-830 1747,-830 1748,-831 1749,-832 1750,-833 1751,-833 1751,-834 1752,-836 1752,-837 1752,-838 1752,-839 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1715,-655 1715,-654 1715,-653 1716,-652 1716,-651 1717,-650 1718,-649 1719,-648 1720,-648 1721,-647 1722,-647 1723,-647 1724,-647 1725,-647 1726,-647 1727,-647 1728,-648 1729,-648 1730,-649 1731,-650 1732,-651 1732,-652 1733,-653 1733,-654 1733,-655 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1733,-655 1733,-654 1733,-653 1734,-652 1734,-651 1735,-650 1736,-649 1737,-648 1738,-648 1739,-647 1740,-647 1741,-647 1742,-647 1743,-647 1744,-647 1745,-647 1746,-648 1747,-648 1748,-649 1749,-650 1750,-651 1750,-652 1751,-653 1751,-654 1751,-655 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1355,-1598 1355,-1597 1355,-1596 1356,-1595 1356,-1594 1357,-1593 1358,-1592 1359,-1591 1360,-1591 1361,-1590 1362,-1590 1363,-1590 1364,-1590 1365,-1590 1366,-1590 1367,-1590 1368,-1591 1369,-1591 1370,-1592 1371,-1593 1372,-1594 1372,-1595 1373,-1596 1373,-1597 1373,-1598 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1373,-1598 1373,-1597 1373,-1596 1374,-1595 1374,-1594 1375,-1593 1376,-1592 1377,-1591 1378,-1591 1379,-1590 1380,-1590 1381,-1590 1382,-1590 1383,-1590 1384,-1590 1385,-1590 1386,-1591 1387,-1591 1388,-1592 1389,-1593 1390,-1594 1390,-1595 1391,-1596 1391,-1597 1391,-1598 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1356,-1422 1356,-1421 1356,-1420 1356,-1419 1357,-1418 1358,-1417 1358,-1416 1359,-1415 1360,-1414 1361,-1414 1362,-1413 1363,-1413 1364,-1413 1365,-1413 1366,-1413 1367,-1413 1368,-1413 1369,-1414 1370,-1414 1371,-1415 1372,-1416 1373,-1417 1373,-1418 1374,-1419 1374,-1420 1374,-1421 1374,-1422 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1374,-1422 1374,-1421 1374,-1420 1374,-1418 1374,-1417 1375,-1416 1375,-1415 1376,-1414 1377,-1413 1378,-1413 1379,-1412 1380,-1412 1381,-1411 1382,-1411 1384,-1411 1385,-1411 1386,-1412 1387,-1412 1388,-1413 1389,-1413 1390,-1414 1391,-1415 1391,-1416 1392,-1417 1392,-1418 1392,-1420 1392,-1421 1392,-1422 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1355,-1247 1355,-1246 1355,-1245 1355,-1244 1356,-1242 1356,-1241 1357,-1241 1358,-1240 1359,-1239 1360,-1238 1361,-1238 1362,-1237 1363,-1237 1364,-1237 1365,-1237 1366,-1237 1367,-1238 1368,-1238 1369,-1239 1370,-1240 1371,-1241 1372,-1241 1372,-1242 1373,-1244 1373,-1245 1373,-1246 1373,-1247 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1374,-1247 1374,-1246 1374,-1245 1375,-1244 1375,-1243 1376,-1242 1377,-1241 1378,-1240 1379,-1240 1380,-1239 1381,-1239 1382,-1239 1383,-1239 1384,-1239 1385,-1239 1386,-1239 1387,-1240 1388,-1240 1389,-1241 1390,-1242 1391,-1243 1391,-1244 1392,-1245 1392,-1246 1392,-1247 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1353,-1140 1353,-1139 1353,-1138 1353,-1136 1353,-1135 1354,-1134 1354,-1133 1355,-1132 1356,-1131 1357,-1131 1358,-1130 1359,-1130 1360,-1129 1361,-1129 1363,-1129 1364,-1129 1365,-1130 1366,-1130 1367,-1131 1368,-1131 1369,-1132 1370,-1133 1370,-1134 1371,-1135 1371,-1136 1371,-1138 1371,-1139 1371,-1140 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1372,-1140 1372,-1139 1372,-1138 1372,-1137 1373,-1136 1374,-1135 1374,-1134 1375,-1133 1376,-1132 1377,-1132 1378,-1131 1379,-1131 1380,-1131 1381,-1131 1382,-1131 1383,-1131 1384,-1131 1385,-1132 1386,-1132 1387,-1133 1388,-1134 1389,-1135 1389,-1136 1390,-1137 1390,-1138 1390,-1139 1390,-1140 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1352,-1033 1352,-1032 1352,-1031 1353,-1030 1353,-1029 1354,-1028 1355,-1027 1356,-1026 1357,-1026 1358,-1025 1359,-1025 1360,-1025 1361,-1025 1362,-1025 1363,-1025 1364,-1025 1365,-1026 1366,-1026 1367,-1027 1368,-1028 1369,-1029 1369,-1030 1370,-1031 1370,-1032 1370,-1033 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1370,-1033 1370,-1032 1370,-1031 1370,-1030 1371,-1028 1371,-1027 1372,-1027 1373,-1026 1374,-1025 1375,-1024 1376,-1024 1377,-1023 1378,-1023 1379,-1023 1380,-1023 1381,-1023 1382,-1024 1383,-1024 1384,-1025 1385,-1026 1386,-1027 1387,-1027 1387,-1028 1388,-1030 1388,-1031 1388,-1032 1388,-1033 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1409,-1340 1409,-1339 1409,-1338 1409,-1337 1410,-1336 1411,-1335 1411,-1334 1412,-1333 1413,-1332 1414,-1332 1415,-1331 1416,-1331 1417,-1331 1418,-1331 1419,-1331 1420,-1331 1421,-1331 1422,-1332 1423,-1332 1424,-1333 1425,-1334 1426,-1335 1426,-1336 1427,-1337 1427,-1338 1427,-1339 1427,-1340 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1427,-1340 1427,-1339 1427,-1338 1427,-1337 1428,-1336 1429,-1335 1429,-1334 1430,-1333 1431,-1332 1432,-1332 1433,-1331 1434,-1331 1435,-1331 1436,-1331 1437,-1331 1438,-1331 1439,-1331 1440,-1332 1441,-1332 1442,-1333 1443,-1334 1444,-1335 1444,-1336 1445,-1337 1445,-1338 1445,-1339 1445,-1340 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1882,-2050 1882,-2049 1882,-2048 1882,-2047 1883,-2046 1884,-2045 1884,-2044 1885,-2043 1886,-2042 1887,-2042 1888,-2041 1889,-2041 1890,-2041 1891,-2041 1892,-2041 1893,-2041 1894,-2041 1895,-2042 1896,-2042 1897,-2043 1898,-2044 1899,-2045 1899,-2046 1900,-2047 1900,-2048 1900,-2049 1900,-2050 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1900,-2050 1900,-2049 1900,-2048 1900,-2047 1901,-2046 1902,-2045 1902,-2044 1903,-2043 1904,-2042 1905,-2042 1906,-2041 1907,-2041 1908,-2041 1909,-2041 1910,-2041 1911,-2041 1912,-2041 1913,-2042 1914,-2042 1915,-2043 1916,-2044 1917,-2045 1917,-2046 1918,-2047 1918,-2048 1918,-2049 1918,-2050 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2020,-1671 2020,-1670 2020,-1669 2021,-1668 2021,-1667 2022,-1666 2023,-1665 2024,-1664 2025,-1664 2026,-1663 2027,-1663 2028,-1663 2029,-1663 2030,-1663 2031,-1663 2032,-1663 2033,-1664 2034,-1664 2035,-1665 2036,-1666 2037,-1667 2037,-1668 2038,-1669 2038,-1670 2038,-1671 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2038,-1671 2038,-1670 2038,-1669 2039,-1668 2039,-1667 2040,-1666 2041,-1665 2042,-1664 2043,-1664 2044,-1663 2045,-1663 2046,-1663 2047,-1663 2048,-1663 2049,-1663 2050,-1663 2051,-1664 2052,-1664 2053,-1665 2054,-1666 2055,-1667 2055,-1668 2056,-1669 2056,-1670 2056,-1671 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2022,-1616 2022,-1615 2022,-1614 2023,-1613 2023,-1612 2024,-1611 2025,-1610 2026,-1609 2027,-1609 2028,-1608 2029,-1608 2030,-1608 2031,-1608 2032,-1608 2033,-1608 2034,-1608 2035,-1609 2036,-1609 2037,-1610 2038,-1611 2039,-1612 2039,-1613 2040,-1614 2040,-1615 2040,-1616 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2040,-1616 2040,-1615 2040,-1614 2041,-1613 2041,-1612 2042,-1611 2043,-1610 2044,-1609 2045,-1609 2046,-1608 2047,-1608 2048,-1608 2049,-1608 2050,-1608 2051,-1608 2052,-1608 2053,-1609 2054,-1609 2055,-1610 2056,-1611 2057,-1612 2057,-1613 2058,-1614 2058,-1615 2058,-1616 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2516,-1548 2516,-1547 2516,-1546 2517,-1545 2517,-1544 2518,-1543 2519,-1542 2520,-1541 2521,-1541 2522,-1540 2523,-1540 2524,-1540 2525,-1540 2526,-1540 2527,-1540 2528,-1540 2529,-1541 2530,-1541 2531,-1542 2532,-1543 2533,-1544 2533,-1545 2534,-1546 2534,-1547 2534,-1548 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2534,-1548 2534,-1547 2534,-1546 2535,-1545 2535,-1544 2536,-1543 2537,-1542 2538,-1541 2539,-1541 2540,-1540 2541,-1540 2542,-1540 2543,-1540 2544,-1540 2545,-1540 2546,-1540 2547,-1541 2548,-1541 2549,-1542 2550,-1543 2551,-1544 2551,-1545 2552,-1546 2552,-1547 2552,-1548 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2272,-1454 2272,-1453 2272,-1452 2273,-1451 2273,-1450 2274,-1449 2275,-1448 2276,-1447 2277,-1447 2278,-1446 2279,-1446 2280,-1446 2281,-1446 2282,-1446 2283,-1446 2284,-1446 2285,-1447 2286,-1447 2287,-1448 2288,-1449 2289,-1450 2289,-1451 2290,-1452 2290,-1453 2290,-1454 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2290,-1454 2290,-1453 2290,-1452 2291,-1451 2291,-1450 2292,-1449 2293,-1448 2294,-1447 2295,-1447 2296,-1446 2297,-1446 2298,-1446 2299,-1446 2300,-1446 2301,-1446 2302,-1446 2303,-1447 2304,-1447 2305,-1448 2306,-1449 2307,-1450 2307,-1451 2308,-1452 2308,-1453 2308,-1454 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3514,-1914 3514,-1913 3514,-1912 3515,-1911 3515,-1910 3516,-1909 3517,-1908 3518,-1907 3519,-1907 3520,-1906 3521,-1906 3522,-1906 3523,-1906 3524,-1906 3525,-1906 3526,-1906 3527,-1907 3528,-1907 3529,-1908 3530,-1909 3531,-1910 3531,-1911 3532,-1912 3532,-1913 3532,-1914 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3532,-1914 3532,-1913 3532,-1912 3533,-1911 3533,-1910 3534,-1909 3535,-1908 3536,-1907 3537,-1907 3538,-1906 3539,-1906 3540,-1906 3541,-1906 3542,-1906 3543,-1906 3544,-1906 3545,-1907 3546,-1907 3547,-1908 3548,-1909 3549,-1910 3549,-1911 3550,-1912 3550,-1913 3550,-1914 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3514,-1803 3514,-1802 3514,-1801 3515,-1800 3515,-1799 3516,-1798 3517,-1797 3518,-1796 3519,-1796 3520,-1795 3521,-1795 3522,-1795 3523,-1795 3524,-1795 3525,-1795 3526,-1795 3527,-1796 3528,-1796 3529,-1797 3530,-1798 3531,-1799 3531,-1800 3532,-1801 3532,-1802 3532,-1803 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3532,-1803 3532,-1802 3532,-1801 3533,-1800 3533,-1799 3534,-1798 3535,-1797 3536,-1796 3537,-1796 3538,-1795 3539,-1795 3540,-1795 3541,-1795 3542,-1795 3543,-1795 3544,-1795 3545,-1796 3546,-1796 3547,-1797 3548,-1798 3549,-1799 3549,-1800 3550,-1801 3550,-1802 3550,-1803 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3512,-2666 3512,-2665 3512,-2664 3513,-2663 3513,-2662 3514,-2661 3515,-2660 3516,-2659 3517,-2659 3518,-2658 3519,-2658 3520,-2658 3521,-2658 3522,-2658 3523,-2658 3524,-2658 3525,-2659 3526,-2659 3527,-2660 3528,-2661 3529,-2662 3529,-2663 3530,-2664 3530,-2665 3530,-2666 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3530,-2666 3530,-2665 3530,-2664 3531,-2663 3531,-2662 3532,-2661 3533,-2660 3534,-2659 3535,-2659 3536,-2658 3537,-2658 3538,-2658 3539,-2658 3540,-2658 3541,-2658 3542,-2658 3543,-2659 3544,-2659 3545,-2660 3546,-2661 3547,-2662 3547,-2663 3548,-2664 3548,-2665 3548,-2666 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3515,-2586 3515,-2585 3515,-2584 3516,-2583 3516,-2582 3517,-2581 3518,-2580 3519,-2579 3520,-2579 3521,-2578 3522,-2578 3523,-2578 3524,-2578 3525,-2578 3526,-2578 3527,-2578 3528,-2579 3529,-2579 3530,-2580 3531,-2581 3532,-2582 3532,-2583 3533,-2584 3533,-2585 3533,-2586 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3533,-2586 3533,-2585 3533,-2584 3534,-2583 3534,-2582 3535,-2581 3536,-2580 3537,-2579 3538,-2579 3539,-2578 3540,-2578 3541,-2578 3542,-2578 3543,-2578 3544,-2578 3545,-2578 3546,-2579 3547,-2579 3548,-2580 3549,-2581 3550,-2582 3550,-2583 3551,-2584 3551,-2585 3551,-2586 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3513,-2499 3513,-2498 3513,-2497 3513,-2495 3513,-2494 3514,-2493 3514,-2492 3515,-2491 3516,-2490 3517,-2490 3518,-2489 3519,-2489 3520,-2488 3521,-2488 3523,-2488 3524,-2488 3525,-2489 3526,-2489 3527,-2490 3528,-2490 3529,-2491 3530,-2492 3530,-2493 3531,-2494 3531,-2495 3531,-2497 3531,-2498 3531,-2499 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3532,-2499 3532,-2498 3532,-2497 3532,-2496 3533,-2495 3534,-2494 3534,-2493 3535,-2492 3536,-2491 3537,-2491 3538,-2490 3539,-2490 3540,-2490 3541,-2490 3542,-2490 3543,-2490 3544,-2490 3545,-2491 3546,-2491 3547,-2492 3548,-2493 3549,-2494 3549,-2495 3550,-2496 3550,-2497 3550,-2498 3550,-2499 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3513,-2365 3513,-2364 3513,-2363 3514,-2362 3514,-2361 3515,-2360 3516,-2359 3517,-2358 3518,-2358 3519,-2357 3520,-2357 3521,-2357 3522,-2357 3523,-2357 3524,-2357 3525,-2357 3526,-2358 3527,-2358 3528,-2359 3529,-2360 3530,-2361 3530,-2362 3531,-2363 3531,-2364 3531,-2365 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3531,-2365 3531,-2364 3531,-2363 3532,-2362 3532,-2361 3533,-2360 3534,-2359 3535,-2358 3536,-2358 3537,-2357 3538,-2357 3539,-2357 3540,-2357 3541,-2357 3542,-2357 3543,-2357 3544,-2358 3545,-2358 3546,-2359 3547,-2360 3548,-2361 3548,-2362 3549,-2363 3549,-2364 3549,-2365 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3514,-2244 3514,-2243 3514,-2242 3515,-2241 3515,-2240 3516,-2239 3517,-2238 3518,-2237 3519,-2237 3520,-2236 3521,-2236 3522,-2236 3523,-2236 3524,-2236 3525,-2236 3526,-2236 3527,-2237 3528,-2237 3529,-2238 3530,-2239 3531,-2240 3531,-2241 3532,-2242 3532,-2243 3532,-2244 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3532,-2244 3532,-2243 3532,-2242 3533,-2241 3533,-2240 3534,-2239 3535,-2238 3536,-2237 3537,-2237 3538,-2236 3539,-2236 3540,-2236 3541,-2236 3542,-2236 3543,-2236 3544,-2236 3545,-2237 3546,-2237 3547,-2238 3548,-2239 3549,-2240 3549,-2241 3550,-2242 3550,-2243 3550,-2244 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3504,-2121 3504,-2120 3504,-2119 3505,-2118 3505,-2117 3506,-2116 3507,-2115 3508,-2114 3509,-2114 3510,-2113 3511,-2113 3512,-2113 3513,-2113 3514,-2113 3515,-2113 3516,-2113 3517,-2114 3518,-2114 3519,-2115 3520,-2116 3521,-2117 3521,-2118 3522,-2119 3522,-2120 3522,-2121 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3522,-2121 3522,-2120 3522,-2119 3523,-2118 3523,-2117 3524,-2116 3525,-2115 3526,-2114 3527,-2114 3528,-2113 3529,-2113 3530,-2113 3531,-2113 3532,-2113 3533,-2113 3534,-2113 3535,-2114 3536,-2114 3537,-2115 3538,-2116 3539,-2117 3539,-2118 3540,-2119 3540,-2120 3540,-2121 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3236,-2121 3236,-2120 3236,-2119 3237,-2118 3237,-2117 3238,-2116 3239,-2115 3240,-2114 3241,-2114 3242,-2113 3243,-2113 3244,-2113 3245,-2113 3246,-2113 3247,-2113 3248,-2113 3249,-2114 3250,-2114 3251,-2115 3252,-2116 3253,-2117 3253,-2118 3254,-2119 3254,-2120 3254,-2121 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3254,-2121 3254,-2120 3254,-2119 3255,-2118 3255,-2117 3256,-2116 3257,-2115 3258,-2114 3259,-2114 3260,-2113 3261,-2113 3262,-2113 3263,-2113 3264,-2113 3265,-2113 3266,-2113 3267,-2114 3268,-2114 3269,-2115 3270,-2116 3271,-2117 3271,-2118 3272,-2119 3272,-2120 3272,-2121 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3496,-1673 3496,-1672 3496,-1671 3497,-1670 3497,-1669 3498,-1668 3499,-1667 3500,-1666 3501,-1666 3502,-1665 3503,-1665 3504,-1665 3505,-1665 3506,-1665 3507,-1665 3508,-1665 3509,-1666 3510,-1666 3511,-1667 3512,-1668 3513,-1669 3513,-1670 3514,-1671 3514,-1672 3514,-1673 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3514,-1673 3514,-1672 3514,-1671 3514,-1670 3515,-1668 3515,-1667 3516,-1667 3517,-1666 3518,-1665 3519,-1664 3520,-1664 3521,-1663 3522,-1663 3523,-1663 3524,-1663 3525,-1663 3526,-1664 3527,-1664 3528,-1665 3529,-1666 3530,-1667 3531,-1667 3531,-1668 3532,-1670 3532,-1671 3532,-1672 3532,-1673 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3499,-1563 3499,-1562 3499,-1561 3500,-1560 3500,-1559 3501,-1558 3502,-1557 3503,-1556 3504,-1556 3505,-1555 3506,-1555 3507,-1555 3508,-1555 3509,-1555 3510,-1555 3511,-1555 3512,-1556 3513,-1556 3514,-1557 3515,-1558 3516,-1559 3516,-1560 3517,-1561 3517,-1562 3517,-1563 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3517,-1563 3517,-1562 3517,-1561 3518,-1560 3518,-1559 3519,-1558 3520,-1557 3521,-1556 3522,-1556 3523,-1555 3524,-1555 3525,-1555 3526,-1555 3527,-1555 3528,-1555 3529,-1555 3530,-1556 3531,-1556 3532,-1557 3533,-1558 3534,-1559 3534,-1560 3535,-1561 3535,-1562 3535,-1563 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3504,-1430 3504,-1429 3504,-1428 3504,-1427 3505,-1426 3506,-1425 3506,-1424 3507,-1423 3508,-1422 3509,-1422 3510,-1421 3511,-1421 3512,-1421 3513,-1421 3514,-1421 3515,-1421 3516,-1421 3517,-1422 3518,-1422 3519,-1423 3520,-1424 3521,-1425 3521,-1426 3522,-1427 3522,-1428 3522,-1429 3522,-1430 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3522,-1430 3522,-1429 3522,-1428 3522,-1426 3522,-1425 3523,-1424 3523,-1423 3524,-1422 3525,-1421 3526,-1421 3527,-1420 3528,-1420 3529,-1419 3530,-1419 3532,-1419 3533,-1419 3534,-1420 3535,-1420 3536,-1421 3537,-1421 3538,-1422 3539,-1423 3539,-1424 3540,-1425 3540,-1426 3540,-1428 3540,-1429 3540,-1430 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2136,-1191 2136,-1190 2136,-1189 2136,-1187 2136,-1186 2137,-1185 2137,-1184 2138,-1183 2139,-1182 2140,-1182 2141,-1181 2142,-1181 2143,-1180 2144,-1180 2146,-1180 2147,-1180 2148,-1181 2149,-1181 2150,-1182 2151,-1182 2152,-1183 2153,-1184 2153,-1185 2154,-1186 2154,-1187 2154,-1189 2154,-1190 2154,-1191 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2155,-1191 2155,-1190 2155,-1189 2155,-1188 2156,-1187 2157,-1186 2157,-1185 2158,-1184 2159,-1183 2160,-1183 2161,-1182 2162,-1182 2163,-1182 2164,-1182 2165,-1182 2166,-1182 2167,-1182 2168,-1183 2169,-1183 2170,-1184 2171,-1185 2172,-1186 2172,-1187 2173,-1188 2173,-1189 2173,-1190 2173,-1191 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2131,-1127 2131,-1126 2131,-1125 2132,-1124 2132,-1123 2133,-1122 2134,-1121 2135,-1120 2136,-1120 2137,-1119 2138,-1119 2139,-1119 2140,-1119 2141,-1119 2142,-1119 2143,-1119 2144,-1120 2145,-1120 2146,-1121 2147,-1122 2148,-1123 2148,-1124 2149,-1125 2149,-1126 2149,-1127 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2149,-1127 2149,-1126 2149,-1125 2150,-1124 2150,-1123 2151,-1122 2152,-1121 2153,-1120 2154,-1120 2155,-1119 2156,-1119 2157,-1119 2158,-1119 2159,-1119 2160,-1119 2161,-1119 2162,-1120 2163,-1120 2164,-1121 2165,-1122 2166,-1123 2166,-1124 2167,-1125 2167,-1126 2167,-1127 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2120,-1039 2120,-1038 2120,-1037 2121,-1036 2121,-1035 2122,-1034 2123,-1033 2124,-1032 2125,-1032 2126,-1031 2127,-1031 2128,-1031 2129,-1031 2130,-1031 2131,-1031 2132,-1031 2133,-1032 2134,-1032 2135,-1033 2136,-1034 2137,-1035 2137,-1036 2138,-1037 2138,-1038 2138,-1039 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2138,-1039 2138,-1038 2138,-1037 2138,-1036 2139,-1034 2139,-1033 2140,-1033 2141,-1032 2142,-1031 2143,-1030 2144,-1030 2145,-1029 2146,-1029 2147,-1029 2148,-1029 2149,-1029 2150,-1030 2151,-1030 2152,-1031 2153,-1032 2154,-1033 2155,-1033 2155,-1034 2156,-1036 2156,-1037 2156,-1038 2156,-1039 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3323,-1122 3323,-1121 3323,-1120 3323,-1119 3324,-1118 3325,-1117 3325,-1116 3326,-1115 3327,-1114 3328,-1114 3329,-1113 3330,-1113 3331,-1113 3332,-1113 3333,-1113 3334,-1113 3335,-1113 3336,-1114 3337,-1114 3338,-1115 3339,-1116 3340,-1117 3340,-1118 3341,-1119 3341,-1120 3341,-1121 3341,-1122 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3341,-1122 3341,-1121 3341,-1120 3341,-1119 3342,-1118 3343,-1117 3343,-1116 3344,-1115 3345,-1114 3346,-1114 3347,-1113 3348,-1113 3349,-1113 3350,-1113 3351,-1113 3352,-1113 3353,-1113 3354,-1114 3355,-1114 3356,-1115 3357,-1116 3358,-1117 3358,-1118 3359,-1119 3359,-1120 3359,-1121 3359,-1122 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3320,-991 3320,-990 3320,-989 3320,-988 3321,-986 3321,-985 3322,-985 3323,-984 3324,-983 3325,-982 3326,-982 3327,-981 3328,-981 3329,-981 3330,-981 3331,-981 3332,-982 3333,-982 3334,-983 3335,-984 3336,-985 3337,-985 3337,-986 3338,-988 3338,-989 3338,-990 3338,-991 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3339,-991 3339,-990 3339,-989 3340,-988 3340,-987 3341,-986 3342,-985 3343,-984 3344,-984 3345,-983 3346,-983 3347,-983 3348,-983 3349,-983 3350,-983 3351,-983 3352,-984 3353,-984 3354,-985 3355,-986 3356,-987 3356,-988 3357,-989 3357,-990 3357,-991 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3321,-842 3321,-841 3321,-840 3321,-839 3322,-837 3322,-836 3323,-836 3324,-835 3325,-834 3326,-833 3327,-833 3328,-832 3329,-832 3330,-832 3331,-832 3332,-832 3333,-833 3334,-833 3335,-834 3336,-835 3337,-836 3338,-836 3338,-837 3339,-839 3339,-840 3339,-841 3339,-842 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3340,-842 3340,-841 3340,-840 3341,-839 3341,-838 3342,-837 3343,-836 3344,-835 3345,-835 3346,-834 3347,-834 3348,-834 3349,-834 3350,-834 3351,-834 3352,-834 3353,-835 3354,-835 3355,-836 3356,-837 3357,-838 3357,-839 3358,-840 3358,-841 3358,-842 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3058,-941 3058,-940 3058,-939 3058,-938 3059,-937 3060,-936 3060,-935 3061,-934 3062,-933 3063,-933 3064,-932 3065,-932 3066,-932 3067,-932 3068,-932 3069,-932 3070,-932 3071,-933 3072,-933 3073,-934 3074,-935 3075,-936 3075,-937 3076,-938 3076,-939 3076,-940 3076,-941 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3076,-941 3076,-940 3076,-939 3076,-937 3076,-936 3077,-935 3077,-934 3078,-933 3079,-932 3080,-932 3081,-931 3082,-931 3083,-930 3084,-930 3086,-930 3087,-930 3088,-931 3089,-931 3090,-932 3091,-932 3092,-933 3093,-934 3093,-935 3094,-936 3094,-937 3094,-939 3094,-940 3094,-941 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3063,-841 3063,-840 3063,-839 3063,-837 3063,-836 3064,-835 3064,-834 3065,-833 3066,-832 3067,-832 3068,-831 3069,-831 3070,-830 3071,-830 3073,-830 3074,-830 3075,-831 3076,-831 3077,-832 3078,-832 3079,-833 3080,-834 3080,-835 3081,-836 3081,-837 3081,-839 3081,-840 3081,-841 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3081,-841 3081,-840 3081,-839 3081,-837 3081,-836 3082,-835 3082,-834 3083,-833 3084,-832 3085,-832 3086,-831 3087,-831 3088,-830 3089,-830 3091,-830 3092,-830 3093,-831 3094,-831 3095,-832 3096,-832 3097,-833 3098,-834 3098,-835 3099,-836 3099,-837 3099,-839 3099,-840 3099,-841 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2134,-721 2134,-720 2134,-719 2134,-718 2135,-717 2136,-716 2136,-715 2137,-714 2138,-713 2139,-713 2140,-712 2141,-712 2142,-712 2143,-712 2144,-712 2145,-712 2146,-712 2147,-713 2148,-713 2149,-714 2150,-715 2151,-716 2151,-717 2152,-718 2152,-719 2152,-720 2152,-721 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2152,-721 2152,-720 2152,-719 2152,-718 2153,-717 2154,-716 2154,-715 2155,-714 2156,-713 2157,-713 2158,-712 2159,-712 2160,-712 2161,-712 2162,-712 2163,-712 2164,-712 2165,-713 2166,-713 2167,-714 2168,-715 2169,-716 2169,-717 2170,-718 2170,-719 2170,-720 2170,-721 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2131,-384 2131,-383 2131,-382 2131,-380 2131,-379 2132,-378 2132,-377 2133,-376 2134,-375 2135,-375 2136,-374 2137,-374 2138,-373 2139,-373 2141,-373 2142,-373 2143,-374 2144,-374 2145,-375 2146,-375 2147,-376 2148,-377 2148,-378 2149,-379 2149,-380 2149,-382 2149,-383 2149,-384 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2150,-384 2150,-383 2150,-382 2150,-381 2151,-380 2152,-379 2152,-378 2153,-377 2154,-376 2155,-376 2156,-375 2157,-375 2158,-375 2159,-375 2160,-375 2161,-375 2162,-375 2163,-376 2164,-376 2165,-377 2166,-378 2167,-379 2167,-380 2168,-381 2168,-382 2168,-383 2168,-384 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="942,-361 942,-360 942,-359 943,-358 943,-357 944,-356 945,-355 946,-354 947,-354 948,-353 949,-353 950,-353 951,-353 952,-353 953,-353 954,-353 955,-354 956,-354 957,-355 958,-356 959,-357 959,-358 960,-359 960,-360 960,-361 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="960,-361 960,-360 960,-359 961,-358 961,-357 962,-356 963,-355 964,-354 965,-354 966,-353 967,-353 968,-353 969,-353 970,-353 971,-353 972,-353 973,-354 974,-354 975,-355 976,-356 977,-357 977,-358 978,-359 978,-360 978,-361 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="943,-488 943,-487 943,-486 943,-485 944,-484 945,-483 945,-482 946,-481 947,-480 948,-480 949,-479 950,-479 951,-479 952,-479 953,-479 954,-479 955,-479 956,-480 957,-480 958,-481 959,-482 960,-483 960,-484 961,-485 961,-486 961,-487 961,-488 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="961,-488 961,-487 961,-486 961,-485 962,-484 963,-483 963,-482 964,-481 965,-480 966,-480 967,-479 968,-479 969,-479 970,-479 971,-479 972,-479 973,-479 974,-480 975,-480 976,-481 977,-482 978,-483 978,-484 979,-485 979,-486 979,-487 979,-488 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="980,-733 980,-732 980,-731 981,-730 981,-729 982,-728 983,-727 984,-726 985,-726 986,-725 987,-725 988,-725 989,-725 990,-725 991,-725 992,-725 993,-726 994,-726 995,-727 996,-728 997,-729 997,-730 998,-731 998,-732 998,-733 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="998,-733 998,-732 998,-731 999,-730 999,-729 1000,-728 1001,-727 1002,-726 1003,-726 1004,-725 1005,-725 1006,-725 1007,-725 1008,-725 1009,-725 1010,-725 1011,-726 1012,-726 1013,-727 1014,-728 1015,-729 1015,-730 1016,-731 1016,-732 1016,-733 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1096,-951 1096,-950 1096,-949 1097,-948 1097,-947 1098,-946 1099,-945 1100,-944 1101,-944 1102,-943 1103,-943 1104,-943 1105,-943 1106,-943 1107,-943 1108,-943 1109,-944 1110,-944 1111,-945 1112,-946 1113,-947 1113,-948 1114,-949 1114,-950 1114,-951 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1114,-951 1114,-950 1114,-949 1115,-948 1115,-947 1116,-946 1117,-945 1118,-944 1119,-944 1120,-943 1121,-943 1122,-943 1123,-943 1124,-943 1125,-943 1126,-943 1127,-944 1128,-944 1129,-945 1130,-946 1131,-947 1131,-948 1132,-949 1132,-950 1132,-951 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="939,-1060 939,-1059 939,-1058 940,-1057 940,-1056 941,-1055 942,-1054 943,-1053 944,-1053 945,-1052 946,-1052 947,-1052 948,-1052 949,-1052 950,-1052 951,-1052 952,-1053 953,-1053 954,-1054 955,-1055 956,-1056 956,-1057 957,-1058 957,-1059 957,-1060 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="957,-1060 957,-1059 957,-1058 958,-1057 958,-1056 959,-1055 960,-1054 961,-1053 962,-1053 963,-1052 964,-1052 965,-1052 966,-1052 967,-1052 968,-1052 969,-1052 970,-1053 971,-1053 972,-1054 973,-1055 974,-1056 974,-1057 975,-1058 975,-1059 975,-1060 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="942,-1168 942,-1167 942,-1166 942,-1165 943,-1164 944,-1163 944,-1162 945,-1161 946,-1160 947,-1160 948,-1159 949,-1159 950,-1159 951,-1159 952,-1159 953,-1159 954,-1159 955,-1160 956,-1160 957,-1161 958,-1162 959,-1163 959,-1164 960,-1165 960,-1166 960,-1167 960,-1168 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="960,-1168 960,-1167 960,-1166 960,-1165 961,-1164 962,-1163 962,-1162 963,-1161 964,-1160 965,-1160 966,-1159 967,-1159 968,-1159 969,-1159 970,-1159 971,-1159 972,-1159 973,-1160 974,-1160 975,-1161 976,-1162 977,-1163 977,-1164 978,-1165 978,-1166 978,-1167 978,-1168 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1004,-1252 1004,-1251 1004,-1250 1005,-1249 1005,-1248 1006,-1247 1007,-1246 1008,-1245 1009,-1245 1010,-1244 1011,-1244 1012,-1244 1013,-1244 1014,-1244 1015,-1244 1016,-1244 1017,-1245 1018,-1245 1019,-1246 1020,-1247 1021,-1248 1021,-1249 1022,-1250 1022,-1251 1022,-1252 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1022,-1252 1022,-1251 1022,-1250 1023,-1249 1023,-1248 1024,-1247 1025,-1246 1026,-1245 1027,-1245 1028,-1244 1029,-1244 1030,-1244 1031,-1244 1032,-1244 1033,-1244 1034,-1244 1035,-1245 1036,-1245 1037,-1246 1038,-1247 1039,-1248 1039,-1249 1040,-1250 1040,-1251 1040,-1252 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="940,-1345 940,-1344 940,-1343 941,-1342 941,-1341 942,-1340 943,-1339 944,-1338 945,-1338 946,-1337 947,-1337 948,-1337 949,-1337 950,-1337 951,-1337 952,-1337 953,-1338 954,-1338 955,-1339 956,-1340 957,-1341 957,-1342 958,-1343 958,-1344 958,-1345 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="958,-1345 958,-1344 958,-1343 959,-1342 959,-1341 960,-1340 961,-1339 962,-1338 963,-1338 964,-1337 965,-1337 966,-1337 967,-1337 968,-1337 969,-1337 970,-1337 971,-1338 972,-1338 973,-1339 974,-1340 975,-1341 975,-1342 976,-1343 976,-1344 976,-1345 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="940,-1420 940,-1419 940,-1418 940,-1416 940,-1415 941,-1414 941,-1413 942,-1412 943,-1411 944,-1411 945,-1410 946,-1410 947,-1409 948,-1409 950,-1409 951,-1409 952,-1410 953,-1410 954,-1411 955,-1411 956,-1412 957,-1413 957,-1414 958,-1415 958,-1416 958,-1418 958,-1419 958,-1420 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="959,-1420 959,-1419 959,-1418 959,-1417 960,-1416 961,-1415 961,-1414 962,-1413 963,-1412 964,-1412 965,-1411 966,-1411 967,-1411 968,-1411 969,-1411 970,-1411 971,-1411 972,-1412 973,-1412 974,-1413 975,-1414 976,-1415 976,-1416 977,-1417 977,-1418 977,-1419 977,-1420 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1422,-1516 1422,-1515 1422,-1514 1422,-1513 1423,-1511 1423,-1510 1424,-1510 1425,-1509 1426,-1508 1427,-1507 1428,-1507 1429,-1506 1430,-1506 1431,-1506 1432,-1506 1433,-1506 1434,-1507 1435,-1507 1436,-1508 1437,-1509 1438,-1510 1439,-1510 1439,-1511 1440,-1513 1440,-1514 1440,-1515 1440,-1516 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1440,-1516 1440,-1515 1440,-1514 1440,-1513 1441,-1511 1441,-1510 1442,-1510 1443,-1509 1444,-1508 1445,-1507 1446,-1507 1447,-1506 1448,-1506 1449,-1506 1450,-1506 1451,-1506 1452,-1507 1453,-1507 1454,-1508 1455,-1509 1456,-1510 1457,-1510 1457,-1511 1458,-1513 1458,-1514 1458,-1515 1458,-1516 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="312,-1001 312,-1000 312,-999 312,-998 313,-996 313,-995 314,-995 315,-994 316,-993 317,-992 318,-992 319,-991 320,-991 321,-991 322,-991 323,-991 324,-992 325,-992 326,-993 327,-994 328,-995 329,-995 329,-996 330,-998 330,-999 330,-1000 330,-1001 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="331,-1001 331,-1000 331,-999 332,-998 332,-997 333,-996 334,-995 335,-994 336,-994 337,-993 338,-993 339,-993 340,-993 341,-993 342,-993 343,-993 344,-994 345,-994 346,-995 347,-996 348,-997 348,-998 349,-999 349,-1000 349,-1001 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="267,-812 267,-811 267,-810 267,-809 268,-808 269,-807 269,-806 270,-805 271,-804 272,-804 273,-803 274,-803 275,-803 276,-803 277,-803 278,-803 279,-803 280,-804 281,-804 282,-805 283,-806 284,-807 284,-808 285,-809 285,-810 285,-811 285,-812 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="285,-812 285,-811 285,-810 285,-809 286,-808 287,-807 287,-806 288,-805 289,-804 290,-804 291,-803 292,-803 293,-803 294,-803 295,-803 296,-803 297,-803 298,-804 299,-804 300,-805 301,-806 302,-807 302,-808 303,-809 303,-810 303,-811 303,-812 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="247,-642 247,-641 247,-640 247,-639 248,-638 249,-637 249,-636 250,-635 251,-634 252,-634 253,-633 254,-633 255,-633 256,-633 257,-633 258,-633 259,-633 260,-634 261,-634 262,-635 263,-636 264,-637 264,-638 265,-639 265,-640 265,-641 265,-642 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="265,-642 265,-641 265,-640 265,-638 265,-637 266,-636 266,-635 267,-634 268,-633 269,-633 270,-632 271,-632 272,-631 273,-631 275,-631 276,-631 277,-632 278,-632 279,-633 280,-633 281,-634 282,-635 282,-636 283,-637 283,-638 283,-640 283,-641 283,-642 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="238,-513 238,-512 238,-511 239,-510 239,-509 240,-508 241,-507 242,-506 243,-506 244,-505 245,-505 246,-505 247,-505 248,-505 249,-505 250,-505 251,-506 252,-506 253,-507 254,-508 255,-509 255,-510 256,-511 256,-512 256,-513 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="256,-513 256,-512 256,-511 257,-510 257,-509 258,-508 259,-507 260,-506 261,-506 262,-505 263,-505 264,-505 265,-505 266,-505 267,-505 268,-505 269,-506 270,-506 271,-507 272,-508 273,-509 273,-510 274,-511 274,-512 274,-513 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="240,-357 240,-356 240,-355 240,-354 241,-353 242,-352 242,-351 243,-350 244,-349 245,-349 246,-348 247,-348 248,-348 249,-348 250,-348 251,-348 252,-348 253,-349 254,-349 255,-350 256,-351 257,-352 257,-353 258,-354 258,-355 258,-356 258,-357 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="258,-357 258,-356 258,-355 258,-354 259,-353 260,-352 260,-351 261,-350 262,-349 263,-349 264,-348 265,-348 266,-348 267,-348 268,-348 269,-348 270,-348 271,-349 272,-349 273,-350 274,-351 275,-352 275,-353 276,-354 276,-355 276,-356 276,-357 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="237,-207 237,-206 237,-205 237,-204 238,-203 239,-202 239,-201 240,-200 241,-199 242,-199 243,-198 244,-198 245,-198 246,-198 247,-198 248,-198 249,-198 250,-199 251,-199 252,-200 253,-201 254,-202 254,-203 255,-204 255,-205 255,-206 255,-207 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="255,-207 255,-206 255,-205 255,-204 256,-203 257,-202 257,-201 258,-200 259,-199 260,-199 261,-198 262,-198 263,-198 264,-198 265,-198 266,-198 267,-198 268,-199 269,-199 270,-200 271,-201 272,-202 272,-203 273,-204 273,-205 273,-206 273,-207 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="240,-1409 240,-1408 240,-1407 241,-1406 241,-1405 242,-1404 243,-1403 244,-1402 245,-1402 246,-1401 247,-1401 248,-1401 249,-1401 250,-1401 251,-1401 252,-1401 253,-1402 254,-1402 255,-1403 256,-1404 257,-1405 257,-1406 258,-1407 258,-1408 258,-1409 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="258,-1409 258,-1408 258,-1407 259,-1406 259,-1405 260,-1404 261,-1403 262,-1402 263,-1402 264,-1401 265,-1401 266,-1401 267,-1401 268,-1401 269,-1401 270,-1401 271,-1402 272,-1402 273,-1403 274,-1404 275,-1405 275,-1406 276,-1407 276,-1408 276,-1409 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="240,-1524 240,-1523 240,-1522 240,-1521 241,-1520 242,-1519 242,-1518 243,-1517 244,-1516 245,-1516 246,-1515 247,-1515 248,-1515 249,-1515 250,-1515 251,-1515 252,-1515 253,-1516 254,-1516 255,-1517 256,-1518 257,-1519 257,-1520 258,-1521 258,-1522 258,-1523 258,-1524 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="258,-1524 258,-1523 258,-1522 258,-1521 259,-1520 260,-1519 260,-1518 261,-1517 262,-1516 263,-1516 264,-1515 265,-1515 266,-1515 267,-1515 268,-1515 269,-1515 270,-1515 271,-1516 272,-1516 273,-1517 274,-1518 275,-1519 275,-1520 276,-1521 276,-1522 276,-1523 276,-1524 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="241,-1671 241,-1670 241,-1669 242,-1668 242,-1667 243,-1666 244,-1665 245,-1664 246,-1664 247,-1663 248,-1663 249,-1663 250,-1663 251,-1663 252,-1663 253,-1663 254,-1664 255,-1664 256,-1665 257,-1666 258,-1667 258,-1668 259,-1669 259,-1670 259,-1671 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="259,-1671 259,-1670 259,-1669 260,-1668 260,-1667 261,-1666 262,-1665 263,-1664 264,-1664 265,-1663 266,-1663 267,-1663 268,-1663 269,-1663 270,-1663 271,-1663 272,-1664 273,-1664 274,-1665 275,-1666 276,-1667 276,-1668 277,-1669 277,-1670 277,-1671 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="804,-1741 804,-1740 804,-1739 805,-1738 805,-1737 806,-1736 807,-1735 808,-1734 809,-1734 810,-1733 811,-1733 812,-1733 813,-1733 814,-1733 815,-1733 816,-1733 817,-1734 818,-1734 819,-1735 820,-1736 821,-1737 821,-1738 822,-1739 822,-1740 822,-1741 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="822,-1741 822,-1740 822,-1739 823,-1738 823,-1737 824,-1736 825,-1735 826,-1734 827,-1734 828,-1733 829,-1733 830,-1733 831,-1733 832,-1733 833,-1733 834,-1733 835,-1734 836,-1734 837,-1735 838,-1736 839,-1737 839,-1738 840,-1739 840,-1740 840,-1741 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2546,-1787 2547,-1787 2548,-1787 2549,-1788 2550,-1788 2551,-1789 2552,-1790 2553,-1791 2553,-1792 2554,-1793 2554,-1794 2554,-1795 2554,-1796 2554,-1797 2554,-1798 2554,-1799 2553,-1800 2553,-1801 2552,-1802 2551,-1803 2550,-1804 2549,-1804 2548,-1805 2547,-1805 2546,-1805 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2546,-1769 2547,-1769 2548,-1769 2549,-1770 2550,-1770 2551,-1771 2552,-1772 2553,-1773 2553,-1774 2554,-1775 2554,-1776 2554,-1777 2554,-1778 2554,-1779 2554,-1780 2554,-1781 2553,-1782 2553,-1783 2552,-1784 2551,-1785 2550,-1786 2549,-1786 2548,-1787 2547,-1787 2546,-1787 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2447,-1787 2448,-1787 2449,-1787 2450,-1787 2451,-1788 2452,-1789 2453,-1789 2454,-1790 2455,-1791 2455,-1792 2456,-1793 2456,-1794 2456,-1795 2456,-1796 2456,-1797 2456,-1798 2456,-1799 2455,-1800 2455,-1801 2454,-1802 2453,-1803 2452,-1804 2451,-1804 2450,-1805 2449,-1805 2448,-1805 2447,-1805 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2447,-1769 2448,-1769 2449,-1769 2450,-1769 2451,-1770 2452,-1771 2453,-1771 2454,-1772 2455,-1773 2455,-1774 2456,-1775 2456,-1776 2456,-1777 2456,-1778 2456,-1779 2456,-1780 2456,-1781 2455,-1782 2455,-1783 2454,-1784 2453,-1785 2452,-1786 2451,-1786 2450,-1787 2449,-1787 2448,-1787 2447,-1787 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2350,-1785 2351,-1785 2352,-1785 2353,-1785 2354,-1786 2355,-1787 2356,-1787 2357,-1788 2358,-1789 2358,-1790 2359,-1791 2359,-1792 2359,-1793 2359,-1794 2359,-1795 2359,-1796 2359,-1797 2358,-1798 2358,-1799 2357,-1800 2356,-1801 2355,-1802 2354,-1802 2353,-1803 2352,-1803 2351,-1803 2350,-1803 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2350,-1767 2351,-1767 2352,-1767 2353,-1767 2354,-1768 2355,-1769 2356,-1769 2357,-1770 2358,-1771 2358,-1772 2359,-1773 2359,-1774 2359,-1775 2359,-1776 2359,-1777 2359,-1778 2359,-1779 2358,-1780 2358,-1781 2357,-1782 2356,-1783 2355,-1784 2354,-1784 2353,-1785 2352,-1785 2351,-1785 2350,-1785 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2191,-916 2192,-916 2193,-916 2194,-917 2195,-917 2196,-918 2197,-919 2198,-920 2198,-921 2199,-922 2199,-923 2199,-924 2199,-925 2199,-926 2199,-927 2199,-928 2198,-929 2198,-930 2197,-931 2196,-932 2195,-933 2194,-933 2193,-934 2192,-934 2191,-934 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2191,-898 2192,-898 2193,-898 2194,-899 2195,-899 2196,-900 2197,-901 2198,-902 2198,-903 2199,-904 2199,-905 2199,-906 2199,-907 2199,-908 2199,-909 2199,-910 2198,-911 2198,-912 2197,-913 2196,-914 2195,-915 2194,-915 2193,-916 2192,-916 2191,-916 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2348,-916 2349,-916 2350,-916 2351,-917 2352,-917 2353,-918 2354,-919 2355,-920 2355,-921 2356,-922 2356,-923 2356,-924 2356,-925 2356,-926 2356,-927 2356,-928 2355,-929 2355,-930 2354,-931 2353,-932 2352,-933 2351,-933 2350,-934 2349,-934 2348,-934 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2348,-898 2349,-898 2350,-898 2351,-899 2352,-899 2353,-900 2354,-901 2355,-902 2355,-903 2356,-904 2356,-905 2356,-906 2356,-907 2356,-908 2356,-909 2356,-910 2355,-911 2355,-912 2354,-913 2353,-914 2352,-915 2351,-915 2350,-916 2349,-916 2348,-916 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2503,-875 2504,-875 2505,-875 2506,-875 2507,-876 2508,-877 2509,-877 2510,-878 2511,-879 2511,-880 2512,-881 2512,-882 2512,-883 2512,-884 2512,-885 2512,-886 2512,-887 2511,-888 2511,-889 2510,-890 2509,-891 2508,-892 2507,-892 2506,-893 2505,-893 2504,-893 2503,-893 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2503,-857 2504,-857 2505,-857 2506,-857 2507,-858 2508,-859 2509,-859 2510,-860 2511,-861 2511,-862 2512,-863 2512,-864 2512,-865 2512,-866 2512,-867 2512,-868 2512,-869 2511,-870 2511,-871 2510,-872 2509,-873 2508,-874 2507,-874 2506,-875 2505,-875 2504,-875 2503,-875 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2684,-871 2685,-871 2686,-871 2687,-872 2688,-872 2689,-873 2690,-874 2691,-875 2691,-876 2692,-877 2692,-878 2692,-879 2692,-880 2692,-881 2692,-882 2692,-883 2691,-884 2691,-885 2690,-886 2689,-887 2688,-888 2687,-888 2686,-889 2685,-889 2684,-889 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2684,-853 2685,-853 2686,-853 2687,-854 2688,-854 2689,-855 2690,-856 2691,-857 2691,-858 2692,-859 2692,-860 2692,-861 2692,-862 2692,-863 2692,-864 2692,-865 2691,-866 2691,-867 2690,-868 2689,-869 2688,-870 2687,-870 2686,-871 2685,-871 2684,-871 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2836,-914 2837,-914 2838,-914 2839,-915 2840,-915 2841,-916 2842,-917 2843,-918 2843,-919 2844,-920 2844,-921 2844,-922 2844,-923 2844,-924 2844,-925 2844,-926 2843,-927 2843,-928 2842,-929 2841,-930 2840,-931 2839,-931 2838,-932 2837,-932 2836,-932 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2836,-896 2837,-896 2838,-896 2839,-897 2840,-897 2841,-898 2842,-899 2843,-900 2843,-901 2844,-902 2844,-903 2844,-904 2844,-905 2844,-906 2844,-907 2844,-908 2843,-909 2843,-910 2842,-911 2841,-912 2840,-913 2839,-913 2838,-914 2837,-914 2836,-914 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="4057,-568 4058,-568 4059,-568 4061,-568 4062,-568 4063,-569 4064,-569 4065,-570 4066,-571 4066,-572 4067,-573 4067,-574 4068,-575 4068,-576 4068,-578 4068,-579 4067,-580 4067,-581 4066,-582 4066,-583 4065,-584 4064,-585 4063,-585 4062,-586 4061,-586 4059,-586 4058,-586 4057,-586 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="4057,-550 4058,-550 4059,-550 4061,-550 4062,-550 4063,-551 4064,-551 4065,-552 4066,-553 4066,-554 4067,-555 4067,-556 4068,-557 4068,-558 4068,-560 4068,-561 4067,-562 4067,-563 4066,-564 4066,-565 4065,-566 4064,-567 4063,-567 4062,-568 4061,-568 4059,-568 4058,-568 4057,-568 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3932,-569 3933,-569 3934,-569 3936,-569 3937,-569 3938,-570 3939,-570 3940,-571 3941,-572 3941,-573 3942,-574 3942,-575 3943,-576 3943,-577 3943,-579 3943,-580 3942,-581 3942,-582 3941,-583 3941,-584 3940,-585 3939,-586 3938,-586 3937,-587 3936,-587 3934,-587 3933,-587 3932,-587 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3932,-551 3933,-551 3934,-551 3935,-551 3936,-552 3937,-553 3938,-553 3939,-554 3940,-555 3940,-556 3941,-557 3941,-558 3941,-559 3941,-560 3941,-561 3941,-562 3941,-563 3940,-564 3940,-565 3939,-566 3938,-567 3937,-568 3936,-568 3935,-569 3934,-569 3933,-569 3932,-569 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3797,-568 3798,-568 3799,-568 3800,-569 3801,-569 3802,-570 3803,-571 3804,-572 3804,-573 3805,-574 3805,-575 3805,-576 3805,-577 3805,-578 3805,-579 3805,-580 3804,-581 3804,-582 3803,-583 3802,-584 3801,-585 3800,-585 3799,-586 3798,-586 3797,-586 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3797,-550 3798,-550 3799,-550 3800,-551 3801,-551 3802,-552 3803,-553 3804,-554 3804,-555 3805,-556 3805,-557 3805,-558 3805,-559 3805,-560 3805,-561 3805,-562 3804,-563 3804,-564 3803,-565 3802,-566 3801,-567 3800,-567 3799,-568 3798,-568 3797,-568 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3631,-568 3632,-568 3633,-568 3635,-568 3636,-568 3637,-569 3638,-569 3639,-570 3640,-571 3640,-572 3641,-573 3641,-574 3642,-575 3642,-576 3642,-578 3642,-579 3641,-580 3641,-581 3640,-582 3640,-583 3639,-584 3638,-585 3637,-585 3636,-586 3635,-586 3633,-586 3632,-586 3631,-586 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3631,-550 3632,-550 3633,-550 3635,-550 3636,-550 3637,-551 3638,-551 3639,-552 3640,-553 3640,-554 3641,-555 3641,-556 3642,-557 3642,-558 3642,-560 3642,-561 3641,-562 3641,-563 3640,-564 3640,-565 3639,-566 3638,-567 3637,-567 3636,-568 3635,-568 3633,-568 3632,-568 3631,-568 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="761,-1927 762,-1927 763,-1927 764,-1927 765,-1928 766,-1929 767,-1929 768,-1930 769,-1931 769,-1932 770,-1933 770,-1934 770,-1935 770,-1936 770,-1937 770,-1938 770,-1939 769,-1940 769,-1941 768,-1942 767,-1943 766,-1944 765,-1944 764,-1945 763,-1945 762,-1945 761,-1945 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="761,-1909 762,-1909 763,-1909 764,-1909 765,-1910 766,-1911 767,-1911 768,-1912 769,-1913 769,-1914 770,-1915 770,-1916 770,-1917 770,-1918 770,-1919 770,-1920 770,-1921 769,-1922 769,-1923 768,-1924 767,-1925 766,-1926 765,-1926 764,-1927 763,-1927 762,-1927 761,-1927 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="604,-1927 605,-1927 606,-1927 607,-1927 608,-1928 609,-1929 610,-1929 611,-1930 612,-1931 612,-1932 613,-1933 613,-1934 613,-1935 613,-1936 613,-1937 613,-1938 613,-1939 612,-1940 612,-1941 611,-1942 610,-1943 609,-1944 608,-1944 607,-1945 606,-1945 605,-1945 604,-1945 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="604,-1909 605,-1909 606,-1909 607,-1909 608,-1910 609,-1911 610,-1911 611,-1912 612,-1913 612,-1914 613,-1915 613,-1916 613,-1917 613,-1918 613,-1919 613,-1920 613,-1921 612,-1922 612,-1923 611,-1924 610,-1925 609,-1926 608,-1926 607,-1927 606,-1927 605,-1927 604,-1927 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="454,-1925 455,-1925 456,-1925 458,-1925 459,-1925 460,-1926 461,-1926 462,-1927 463,-1928 463,-1929 464,-1930 464,-1931 465,-1932 465,-1933 465,-1935 465,-1936 464,-1937 464,-1938 463,-1939 463,-1940 462,-1941 461,-1942 460,-1942 459,-1943 458,-1943 456,-1943 455,-1943 454,-1943 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="454,-1907 455,-1907 456,-1907 457,-1907 458,-1908 459,-1909 460,-1909 461,-1910 462,-1911 462,-1912 463,-1913 463,-1914 463,-1915 463,-1916 463,-1917 463,-1918 463,-1919 462,-1920 462,-1921 461,-1922 460,-1923 459,-1924 458,-1924 457,-1925 456,-1925 455,-1925 454,-1925 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="297,-1925 298,-1925 299,-1925 300,-1925 302,-1926 303,-1926 303,-1927 304,-1928 305,-1929 306,-1930 306,-1931 307,-1932 307,-1933 307,-1934 307,-1935 307,-1936 306,-1937 306,-1938 305,-1939 304,-1940 303,-1941 303,-1942 302,-1942 300,-1943 299,-1943 298,-1943 297,-1943 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="297,-1907 298,-1907 299,-1907 300,-1908 301,-1908 302,-1909 303,-1910 304,-1911 304,-1912 305,-1913 305,-1914 305,-1915 305,-1916 305,-1917 305,-1918 305,-1919 304,-1920 304,-1921 303,-1922 302,-1923 301,-1924 300,-1924 299,-1925 298,-1925 297,-1925 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1121,-1800 1122,-1800 1123,-1800 1124,-1800 1126,-1801 1127,-1801 1127,-1802 1128,-1803 1129,-1804 1130,-1805 1130,-1806 1131,-1807 1131,-1808 1131,-1809 1131,-1810 1131,-1811 1130,-1812 1130,-1813 1129,-1814 1128,-1815 1127,-1816 1127,-1817 1126,-1817 1124,-1818 1123,-1818 1122,-1818 1121,-1818 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1121,-1782 1122,-1782 1123,-1782 1124,-1783 1125,-1783 1126,-1784 1127,-1785 1128,-1786 1128,-1787 1129,-1788 1129,-1789 1129,-1790 1129,-1791 1129,-1792 1129,-1793 1129,-1794 1128,-1795 1128,-1796 1127,-1797 1126,-1798 1125,-1799 1124,-1799 1123,-1800 1122,-1800 1121,-1800 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1550,-1798 1551,-1798 1552,-1798 1554,-1798 1555,-1798 1556,-1799 1557,-1799 1558,-1800 1559,-1801 1559,-1802 1560,-1803 1560,-1804 1561,-1805 1561,-1806 1561,-1808 1561,-1809 1560,-1810 1560,-1811 1559,-1812 1559,-1813 1558,-1814 1557,-1815 1556,-1815 1555,-1816 1554,-1816 1552,-1816 1551,-1816 1550,-1816 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1550,-1780 1551,-1780 1552,-1780 1553,-1780 1554,-1781 1555,-1782 1556,-1782 1557,-1783 1558,-1784 1558,-1785 1559,-1786 1559,-1787 1559,-1788 1559,-1789 1559,-1790 1559,-1791 1559,-1792 1558,-1793 1558,-1794 1557,-1795 1556,-1796 1555,-1797 1554,-1797 1553,-1798 1552,-1798 1551,-1798 1550,-1798 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="660,-1583 661,-1583 662,-1583 663,-1583 664,-1584 665,-1585 666,-1585 667,-1586 668,-1587 668,-1588 669,-1589 669,-1590 669,-1591 669,-1592 669,-1593 669,-1594 669,-1595 668,-1596 668,-1597 667,-1598 666,-1599 665,-1600 664,-1600 663,-1601 662,-1601 661,-1601 660,-1601 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="660,-1564 661,-1564 662,-1564 664,-1564 665,-1564 666,-1565 667,-1565 668,-1566 669,-1567 669,-1568 670,-1569 670,-1570 671,-1571 671,-1572 671,-1574 671,-1575 670,-1576 670,-1577 669,-1578 669,-1579 668,-1580 667,-1581 666,-1581 665,-1582 664,-1582 662,-1582 661,-1582 660,-1582 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="805,-1583 806,-1583 807,-1583 808,-1584 809,-1584 810,-1585 811,-1586 812,-1587 812,-1588 813,-1589 813,-1590 813,-1591 813,-1592 813,-1593 813,-1594 813,-1595 812,-1596 812,-1597 811,-1598 810,-1599 809,-1600 808,-1600 807,-1601 806,-1601 805,-1601 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="805,-1564 806,-1564 807,-1564 808,-1564 810,-1565 811,-1565 811,-1566 812,-1567 813,-1568 814,-1569 814,-1570 815,-1571 815,-1572 815,-1573 815,-1574 815,-1575 814,-1576 814,-1577 813,-1578 812,-1579 811,-1580 811,-1581 810,-1581 808,-1582 807,-1582 806,-1582 805,-1582 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="997,-1583 998,-1583 999,-1583 1000,-1584 1001,-1584 1002,-1585 1003,-1586 1004,-1587 1004,-1588 1005,-1589 1005,-1590 1005,-1591 1005,-1592 1005,-1593 1005,-1594 1005,-1595 1004,-1596 1004,-1597 1003,-1598 1002,-1599 1001,-1600 1000,-1600 999,-1601 998,-1601 997,-1601 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="997,-1564 998,-1564 999,-1564 1000,-1564 1002,-1565 1003,-1565 1003,-1566 1004,-1567 1005,-1568 1006,-1569 1006,-1570 1007,-1571 1007,-1572 1007,-1573 1007,-1574 1007,-1575 1006,-1576 1006,-1577 1005,-1578 1004,-1579 1003,-1580 1003,-1581 1002,-1581 1000,-1582 999,-1582 998,-1582 997,-1582 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1164,-1587 1165,-1587 1166,-1587 1167,-1587 1168,-1588 1169,-1589 1170,-1589 1171,-1590 1172,-1591 1172,-1592 1173,-1593 1173,-1594 1173,-1595 1173,-1596 1173,-1597 1173,-1598 1173,-1599 1172,-1600 1172,-1601 1171,-1602 1170,-1603 1169,-1604 1168,-1604 1167,-1605 1166,-1605 1165,-1605 1164,-1605 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1164,-1569 1165,-1569 1166,-1569 1167,-1569 1168,-1570 1169,-1571 1170,-1571 1171,-1572 1172,-1573 1172,-1574 1173,-1575 1173,-1576 1173,-1577 1173,-1578 1173,-1579 1173,-1580 1173,-1581 1172,-1582 1172,-1583 1171,-1584 1170,-1585 1169,-1586 1168,-1586 1167,-1587 1166,-1587 1165,-1587 1164,-1587 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1067,-899 1068,-899 1069,-899 1070,-899 1072,-900 1073,-900 1073,-901 1074,-902 1075,-903 1076,-904 1076,-905 1077,-906 1077,-907 1077,-908 1077,-909 1077,-910 1076,-911 1076,-912 1075,-913 1074,-914 1073,-915 1073,-916 1072,-916 1070,-917 1069,-917 1068,-917 1067,-917 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1067,-881 1068,-881 1069,-881 1070,-881 1072,-882 1073,-882 1073,-883 1074,-884 1075,-885 1076,-886 1076,-887 1077,-888 1077,-889 1077,-890 1077,-891 1077,-892 1076,-893 1076,-894 1075,-895 1074,-896 1073,-897 1073,-898 1072,-898 1070,-899 1069,-899 1068,-899 1067,-899 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="760,-601 761,-601 762,-601 763,-601 764,-602 765,-603 766,-603 767,-604 768,-605 768,-606 769,-607 769,-608 769,-609 769,-610 769,-611 769,-612 769,-613 768,-614 768,-615 767,-616 766,-617 765,-618 764,-618 763,-619 762,-619 761,-619 760,-619 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="760,-583 761,-583 762,-583 763,-583 764,-584 765,-585 766,-585 767,-586 768,-587 768,-588 769,-589 769,-590 769,-591 769,-592 769,-593 769,-594 769,-595 768,-596 768,-597 767,-598 766,-599 765,-600 764,-600 763,-601 762,-601 761,-601 760,-601 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="829,-817 830,-817 831,-817 832,-817 833,-818 834,-819 835,-819 836,-820 837,-821 837,-822 838,-823 838,-824 838,-825 838,-826 838,-827 838,-828 838,-829 837,-830 837,-831 836,-832 835,-833 834,-834 833,-834 832,-835 831,-835 830,-835 829,-835 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="829,-799 830,-799 831,-799 832,-799 833,-800 834,-801 835,-801 836,-802 837,-803 837,-804 838,-805 838,-806 838,-807 838,-808 838,-809 838,-810 838,-811 837,-812 837,-813 836,-814 835,-815 834,-816 833,-816 832,-817 831,-817 830,-817 829,-817 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="707,-816 708,-816 709,-816 710,-817 711,-817 712,-818 713,-819 714,-820 714,-821 715,-822 715,-823 715,-824 715,-825 715,-826 715,-827 715,-828 714,-829 714,-830 713,-831 712,-832 711,-833 710,-833 709,-834 708,-834 707,-834 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="707,-797 708,-797 709,-797 710,-797 712,-798 713,-798 713,-799 714,-800 715,-801 716,-802 716,-803 717,-804 717,-805 717,-806 717,-807 717,-808 716,-809 716,-810 715,-811 714,-812 713,-813 713,-814 712,-814 710,-815 709,-815 708,-815 707,-815 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="599,-816 600,-816 601,-816 602,-817 603,-817 604,-818 605,-819 606,-820 606,-821 607,-822 607,-823 607,-824 607,-825 607,-826 607,-827 607,-828 606,-829 606,-830 605,-831 604,-832 603,-833 602,-833 601,-834 600,-834 599,-834 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="599,-797 600,-797 601,-797 602,-797 604,-798 605,-798 605,-799 606,-800 607,-801 608,-802 608,-803 609,-804 609,-805 609,-806 609,-807 609,-808 608,-809 608,-810 607,-811 606,-812 605,-813 605,-814 604,-814 602,-815 601,-815 600,-815 599,-815 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="645,-602 646,-602 647,-602 648,-602 649,-603 650,-604 651,-604 652,-605 653,-606 653,-607 654,-608 654,-609 654,-610 654,-611 654,-612 654,-613 654,-614 653,-615 653,-616 652,-617 651,-618 650,-619 649,-619 648,-620 647,-620 646,-620 645,-620 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="645,-584 646,-584 647,-584 648,-584 649,-585 650,-586 651,-586 652,-587 653,-588 653,-589 654,-590 654,-591 654,-592 654,-593 654,-594 654,-595 654,-596 653,-597 653,-598 652,-599 651,-600 650,-601 649,-601 648,-602 647,-602 646,-602 645,-602 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1328,-808 1329,-808 1330,-808 1331,-808 1333,-809 1334,-809 1334,-810 1335,-811 1336,-812 1337,-813 1337,-814 1338,-815 1338,-816 1338,-817 1338,-818 1338,-819 1337,-820 1337,-821 1336,-822 1335,-823 1334,-824 1334,-825 1333,-825 1331,-826 1330,-826 1329,-826 1328,-826 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1328,-790 1329,-790 1330,-790 1331,-791 1332,-791 1333,-792 1334,-793 1335,-794 1335,-795 1336,-796 1336,-797 1336,-798 1336,-799 1336,-800 1336,-801 1336,-802 1335,-803 1335,-804 1334,-805 1333,-806 1332,-807 1331,-807 1330,-808 1329,-808 1328,-808 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1451,-895 1451,-894 1451,-893 1451,-892 1452,-891 1453,-890 1453,-889 1454,-888 1455,-887 1456,-887 1457,-886 1458,-886 1459,-886 1460,-886 1461,-886 1462,-886 1463,-886 1464,-887 1465,-887 1466,-888 1467,-889 1468,-890 1468,-891 1469,-892 1469,-893 1469,-894 1469,-895 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1469,-895 1469,-894 1469,-893 1469,-892 1470,-891 1471,-890 1471,-889 1472,-888 1473,-887 1474,-887 1475,-886 1476,-886 1477,-886 1478,-886 1479,-886 1480,-886 1481,-886 1482,-887 1483,-887 1484,-888 1485,-889 1486,-890 1486,-891 1487,-892 1487,-893 1487,-894 1487,-895 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2800,-570 2801,-570 2802,-570 2803,-571 2804,-571 2805,-572 2806,-573 2807,-574 2807,-575 2808,-576 2808,-577 2808,-578 2808,-579 2808,-580 2808,-581 2808,-582 2807,-583 2807,-584 2806,-585 2805,-586 2804,-587 2803,-587 2802,-588 2801,-588 2800,-588 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2800,-552 2801,-552 2802,-552 2803,-553 2804,-553 2805,-554 2806,-555 2807,-556 2807,-557 2808,-558 2808,-559 2808,-560 2808,-561 2808,-562 2808,-563 2808,-564 2807,-565 2807,-566 2806,-567 2805,-568 2804,-569 2803,-569 2802,-570 2801,-570 2800,-570 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3123,-569 3124,-569 3125,-569 3126,-570 3127,-570 3128,-571 3129,-572 3130,-573 3130,-574 3131,-575 3131,-576 3131,-577 3131,-578 3131,-579 3131,-580 3131,-581 3130,-582 3130,-583 3129,-584 3128,-585 3127,-586 3126,-586 3125,-587 3124,-587 3123,-587 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3123,-551 3124,-551 3125,-551 3126,-552 3127,-552 3128,-553 3129,-554 3130,-555 3130,-556 3131,-557 3131,-558 3131,-559 3131,-560 3131,-561 3131,-562 3131,-563 3130,-564 3130,-565 3129,-566 3128,-567 3127,-568 3126,-568 3125,-569 3124,-569 3123,-569 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3260,-569 3261,-569 3262,-569 3263,-570 3264,-570 3265,-571 3266,-572 3267,-573 3267,-574 3268,-575 3268,-576 3268,-577 3268,-578 3268,-579 3268,-580 3268,-581 3267,-582 3267,-583 3266,-584 3265,-585 3264,-586 3263,-586 3262,-587 3261,-587 3260,-587 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3260,-551 3261,-551 3262,-551 3263,-552 3264,-552 3265,-553 3266,-554 3267,-555 3267,-556 3268,-557 3268,-558 3268,-559 3268,-560 3268,-561 3268,-562 3268,-563 3267,-564 3267,-565 3266,-566 3265,-567 3264,-568 3263,-568 3262,-569 3261,-569 3260,-569 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="366,-1194 367,-1194 368,-1194 369,-1195 370,-1195 371,-1196 372,-1197 373,-1198 373,-1199 374,-1200 374,-1201 374,-1202 374,-1203 374,-1204 374,-1205 374,-1206 373,-1207 373,-1208 372,-1209 371,-1210 370,-1211 369,-1211 368,-1212 367,-1212 366,-1212 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="366,-1176 367,-1176 368,-1176 369,-1177 370,-1177 371,-1178 372,-1179 373,-1180 373,-1181 374,-1182 374,-1183 374,-1184 374,-1185 374,-1186 374,-1187 374,-1188 373,-1189 373,-1190 372,-1191 371,-1192 370,-1193 369,-1193 368,-1194 367,-1194 366,-1194 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="465,-1193 466,-1193 467,-1193 468,-1193 469,-1194 470,-1195 471,-1195 472,-1196 473,-1197 473,-1198 474,-1199 474,-1200 474,-1201 474,-1202 474,-1203 474,-1204 474,-1205 473,-1206 473,-1207 472,-1208 471,-1209 470,-1210 469,-1210 468,-1211 467,-1211 466,-1211 465,-1211 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="465,-1175 466,-1175 467,-1175 468,-1175 469,-1176 470,-1177 471,-1177 472,-1178 473,-1179 473,-1180 474,-1181 474,-1182 474,-1183 474,-1184 474,-1185 474,-1186 474,-1187 473,-1188 473,-1189 472,-1190 471,-1191 470,-1192 469,-1192 468,-1193 467,-1193 466,-1193 465,-1193 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="561,-1194 562,-1194 563,-1194 564,-1195 565,-1195 566,-1196 567,-1197 568,-1198 568,-1199 569,-1200 569,-1201 569,-1202 569,-1203 569,-1204 569,-1205 569,-1206 568,-1207 568,-1208 567,-1209 566,-1210 565,-1211 564,-1211 563,-1212 562,-1212 561,-1212 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="561,-1176 562,-1176 563,-1176 564,-1177 565,-1177 566,-1178 567,-1179 568,-1180 568,-1181 569,-1182 569,-1183 569,-1184 569,-1185 569,-1186 569,-1187 569,-1188 568,-1189 568,-1190 567,-1191 566,-1192 565,-1193 564,-1193 563,-1194 562,-1194 561,-1194 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="723,-1090 724,-1090 725,-1090 726,-1090 727,-1091 728,-1092 729,-1092 730,-1093 731,-1094 731,-1095 732,-1096 732,-1097 732,-1098 732,-1099 732,-1100 732,-1101 732,-1102 731,-1103 731,-1104 730,-1105 729,-1106 728,-1107 727,-1107 726,-1108 725,-1108 724,-1108 723,-1108 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="723,-1072 724,-1072 725,-1072 726,-1072 727,-1073 728,-1074 729,-1074 730,-1075 731,-1076 731,-1077 732,-1078 732,-1079 732,-1080 732,-1081 732,-1082 732,-1083 732,-1084 731,-1085 731,-1086 730,-1087 729,-1088 728,-1089 727,-1089 726,-1090 725,-1090 724,-1090 723,-1090 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3231,-2471 3231,-2470 3231,-2469 3231,-2467 3231,-2466 3232,-2465 3232,-2464 3233,-2463 3234,-2462 3235,-2462 3236,-2461 3237,-2461 3238,-2460 3239,-2460 3241,-2460 3242,-2460 3243,-2461 3244,-2461 3245,-2462 3246,-2462 3247,-2463 3248,-2464 3248,-2465 3249,-2466 3249,-2467 3249,-2469 3249,-2470 3249,-2471 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3249,-2471 3249,-2470 3249,-2469 3249,-2467 3249,-2466 3250,-2465 3250,-2464 3251,-2463 3252,-2462 3253,-2462 3254,-2461 3255,-2461 3256,-2460 3257,-2460 3259,-2460 3260,-2460 3261,-2461 3262,-2461 3263,-2462 3264,-2462 3265,-2463 3266,-2464 3266,-2465 3267,-2466 3267,-2467 3267,-2469 3267,-2470 3267,-2471 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="790,-1893 790,-1892 790,-1891 790,-1890 791,-1889 792,-1888 792,-1887 793,-1886 794,-1885 795,-1885 796,-1884 797,-1884 798,-1884 799,-1884 800,-1884 801,-1884 802,-1884 803,-1885 804,-1885 805,-1886 806,-1887 807,-1888 807,-1889 808,-1890 808,-1891 808,-1892 808,-1893 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="808,-1893 808,-1892 808,-1891 808,-1890 809,-1889 810,-1888 810,-1887 811,-1886 812,-1885 813,-1885 814,-1884 815,-1884 816,-1884 817,-1884 818,-1884 819,-1884 820,-1884 821,-1885 822,-1885 823,-1886 824,-1887 825,-1888 825,-1889 826,-1890 826,-1891 826,-1892 826,-1893 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2144,-1835 2144,-1834 2144,-1833 2145,-1832 2145,-1831 2146,-1830 2147,-1829 2148,-1828 2149,-1828 2150,-1827 2151,-1827 2152,-1827 2153,-1827 2154,-1827 2155,-1827 2156,-1827 2157,-1828 2158,-1828 2159,-1829 2160,-1830 2161,-1831 2161,-1832 2162,-1833 2162,-1834 2162,-1835 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2125,-1835 2125,-1834 2125,-1833 2125,-1832 2126,-1830 2126,-1829 2127,-1829 2128,-1828 2129,-1827 2130,-1826 2131,-1826 2132,-1825 2133,-1825 2134,-1825 2135,-1825 2136,-1825 2137,-1826 2138,-1826 2139,-1827 2140,-1828 2141,-1829 2142,-1829 2142,-1830 2143,-1832 2143,-1833 2143,-1834 2143,-1835 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2047,-723 2047,-722 2047,-721 2047,-720 2048,-719 2049,-718 2049,-717 2050,-716 2051,-715 2052,-715 2053,-714 2054,-714 2055,-714 2056,-714 2057,-714 2058,-714 2059,-714 2060,-715 2061,-715 2062,-716 2063,-717 2064,-718 2064,-719 2065,-720 2065,-721 2065,-722 2065,-723 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2028,-723 2028,-722 2028,-721 2028,-719 2028,-718 2029,-717 2029,-716 2030,-715 2031,-714 2032,-714 2033,-713 2034,-713 2035,-712 2036,-712 2038,-712 2039,-712 2040,-713 2041,-713 2042,-714 2043,-714 2044,-715 2045,-716 2045,-717 2046,-718 2046,-719 2046,-721 2046,-722 2046,-723 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1603,-1105 1603,-1104 1603,-1103 1603,-1102 1604,-1101 1605,-1100 1605,-1099 1606,-1098 1607,-1097 1608,-1097 1609,-1096 1610,-1096 1611,-1096 1612,-1096 1613,-1096 1614,-1096 1615,-1096 1616,-1097 1617,-1097 1618,-1098 1619,-1099 1620,-1100 1620,-1101 1621,-1102 1621,-1103 1621,-1104 1621,-1105 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1621,-1105 1621,-1104 1621,-1103 1621,-1102 1622,-1101 1623,-1100 1623,-1099 1624,-1098 1625,-1097 1626,-1097 1627,-1096 1628,-1096 1629,-1096 1630,-1096 1631,-1096 1632,-1096 1633,-1096 1634,-1097 1635,-1097 1636,-1098 1637,-1099 1638,-1100 1638,-1101 1639,-1102 1639,-1103 1639,-1104 1639,-1105 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2144,-1384 2144,-1383 2144,-1382 2144,-1381 2145,-1380 2146,-1379 2146,-1378 2147,-1377 2148,-1376 2149,-1376 2150,-1375 2151,-1375 2152,-1375 2153,-1375 2154,-1375 2155,-1375 2156,-1375 2157,-1376 2158,-1376 2159,-1377 2160,-1378 2161,-1379 2161,-1380 2162,-1381 2162,-1382 2162,-1383 2162,-1384 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2162,-1384 2162,-1383 2162,-1382 2162,-1381 2163,-1380 2164,-1379 2164,-1378 2165,-1377 2166,-1376 2167,-1376 2168,-1375 2169,-1375 2170,-1375 2171,-1375 2172,-1375 2173,-1375 2174,-1375 2175,-1376 2176,-1376 2177,-1377 2178,-1378 2179,-1379 2179,-1380 2180,-1381 2180,-1382 2180,-1383 2180,-1384 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3499,-1334 3499,-1333 3499,-1332 3500,-1331 3500,-1330 3501,-1329 3502,-1328 3503,-1327 3504,-1327 3505,-1326 3506,-1326 3507,-1326 3508,-1326 3509,-1326 3510,-1326 3511,-1326 3512,-1327 3513,-1327 3514,-1328 3515,-1329 3516,-1330 3516,-1331 3517,-1332 3517,-1333 3517,-1334 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3517,-1334 3517,-1333 3517,-1332 3518,-1331 3518,-1330 3519,-1329 3520,-1328 3521,-1327 3522,-1327 3523,-1326 3524,-1326 3525,-1326 3526,-1326 3527,-1326 3528,-1326 3529,-1326 3530,-1327 3531,-1327 3532,-1328 3533,-1329 3534,-1330 3534,-1331 3535,-1332 3535,-1333 3535,-1334 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1660,-733 1660,-732 1660,-731 1660,-729 1660,-728 1661,-727 1661,-726 1662,-725 1663,-724 1664,-724 1665,-723 1666,-723 1667,-722 1668,-722 1670,-722 1671,-722 1672,-723 1673,-723 1674,-724 1675,-724 1676,-725 1677,-726 1677,-727 1678,-728 1678,-729 1678,-731 1678,-732 1678,-733 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1678,-733 1678,-732 1678,-731 1678,-729 1678,-728 1679,-727 1679,-726 1680,-725 1681,-724 1682,-724 1683,-723 1684,-723 1685,-722 1686,-722 1688,-722 1689,-722 1690,-723 1691,-723 1692,-724 1693,-724 1694,-725 1695,-726 1695,-727 1696,-728 1696,-729 1696,-731 1696,-732 1696,-733 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1660,-996 1660,-995 1660,-994 1660,-993 1661,-991 1661,-990 1662,-990 1663,-989 1664,-988 1665,-987 1666,-987 1667,-986 1668,-986 1669,-986 1670,-986 1671,-986 1672,-987 1673,-987 1674,-988 1675,-989 1676,-990 1677,-990 1677,-991 1678,-993 1678,-994 1678,-995 1678,-996 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1678,-996 1678,-995 1678,-994 1678,-993 1679,-991 1679,-990 1680,-990 1681,-989 1682,-988 1683,-987 1684,-987 1685,-986 1686,-986 1687,-986 1688,-986 1689,-986 1690,-987 1691,-987 1692,-988 1693,-989 1694,-990 1695,-990 1695,-991 1696,-993 1696,-994 1696,-995 1696,-996 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1660,-1282 1660,-1281 1660,-1280 1660,-1279 1661,-1277 1661,-1276 1662,-1276 1663,-1275 1664,-1274 1665,-1273 1666,-1273 1667,-1272 1668,-1272 1669,-1272 1670,-1272 1671,-1272 1672,-1273 1673,-1273 1674,-1274 1675,-1275 1676,-1276 1677,-1276 1677,-1277 1678,-1279 1678,-1280 1678,-1281 1678,-1282 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1678,-1282 1678,-1281 1678,-1280 1678,-1279 1679,-1277 1679,-1276 1680,-1276 1681,-1275 1682,-1274 1683,-1273 1684,-1273 1685,-1272 1686,-1272 1687,-1272 1688,-1272 1689,-1272 1690,-1273 1691,-1273 1692,-1274 1693,-1275 1694,-1276 1695,-1276 1695,-1277 1696,-1279 1696,-1280 1696,-1281 1696,-1282 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1607,-1462 1607,-1461 1607,-1460 1608,-1459 1608,-1458 1609,-1457 1610,-1456 1611,-1455 1612,-1455 1613,-1454 1614,-1454 1615,-1454 1616,-1454 1617,-1454 1618,-1454 1619,-1454 1620,-1455 1621,-1455 1622,-1456 1623,-1457 1624,-1458 1624,-1459 1625,-1460 1625,-1461 1625,-1462 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1625,-1462 1625,-1461 1625,-1460 1625,-1459 1626,-1457 1626,-1456 1627,-1456 1628,-1455 1629,-1454 1630,-1453 1631,-1453 1632,-1452 1633,-1452 1634,-1452 1635,-1452 1636,-1452 1637,-1453 1638,-1453 1639,-1454 1640,-1455 1641,-1456 1642,-1456 1642,-1457 1643,-1459 1643,-1460 1643,-1461 1643,-1462 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2818,-1341 2819,-1341 2820,-1341 2821,-1341 2822,-1342 2823,-1343 2824,-1343 2825,-1344 2826,-1345 2826,-1346 2827,-1347 2827,-1348 2827,-1349 2827,-1350 2827,-1351 2827,-1352 2827,-1353 2826,-1354 2826,-1355 2825,-1356 2824,-1357 2823,-1358 2822,-1358 2821,-1359 2820,-1359 2819,-1359 2818,-1359 " stroke="rgb(255,0,0)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2818,-1359 2819,-1359 2820,-1359 2821,-1359 2822,-1360 2823,-1361 2824,-1361 2825,-1362 2826,-1363 2826,-1364 2827,-1365 2827,-1366 2827,-1367 2827,-1368 2827,-1369 2827,-1370 2827,-1371 2826,-1372 2826,-1373 2825,-1374 2824,-1375 2823,-1376 2822,-1376 2821,-1377 2820,-1377 2819,-1377 2818,-1377 " stroke="rgb(255,0,0)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2942,-1405 2943,-1405 2944,-1405 2945,-1405 2947,-1406 2948,-1406 2948,-1407 2949,-1408 2950,-1409 2951,-1410 2951,-1411 2952,-1412 2952,-1413 2952,-1414 2952,-1415 2952,-1416 2951,-1417 2951,-1418 2950,-1419 2949,-1420 2948,-1421 2948,-1422 2947,-1422 2945,-1423 2944,-1423 2943,-1423 2942,-1423 " stroke="rgb(255,0,0)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2942,-1424 2943,-1424 2944,-1424 2945,-1425 2946,-1425 2947,-1426 2948,-1427 2949,-1428 2949,-1429 2950,-1430 2950,-1431 2950,-1432 2950,-1433 2950,-1434 2950,-1435 2950,-1436 2949,-1437 2949,-1438 2948,-1439 2947,-1440 2946,-1441 2945,-1441 2944,-1442 2943,-1442 2942,-1442 " stroke="rgb(255,0,0)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3092,-1406 3093,-1406 3094,-1406 3095,-1406 3096,-1407 3097,-1408 3098,-1408 3099,-1409 3100,-1410 3100,-1411 3101,-1412 3101,-1413 3101,-1414 3101,-1415 3101,-1416 3101,-1417 3101,-1418 3100,-1419 3100,-1420 3099,-1421 3098,-1422 3097,-1423 3096,-1423 3095,-1424 3094,-1424 3093,-1424 3092,-1424 " stroke="rgb(255,0,0)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3092,-1424 3093,-1424 3094,-1424 3095,-1424 3096,-1425 3097,-1426 3098,-1426 3099,-1427 3100,-1428 3100,-1429 3101,-1430 3101,-1431 3101,-1432 3101,-1433 3101,-1434 3101,-1435 3101,-1436 3100,-1437 3100,-1438 3099,-1439 3098,-1440 3097,-1441 3096,-1441 3095,-1442 3094,-1442 3093,-1442 3092,-1442 " stroke="rgb(255,0,0)" stroke-width="0.03"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="2006,-2279 2024,-2279 2024,-2249 2006,-2249 2006,-2279 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2005,-1329 2023,-1329 2023,-1299 2005,-1299 2005,-1329 " stroke="rgb(255,255,255)"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2003" x2="2027" y1="-2309" y2="-2291"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2003" x2="2027" y1="-2237" y2="-2219"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2015" x2="2015" y1="-2326" y2="-2279"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1544" x2="2598" y1="-2325" y2="-2325"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3605" x2="4205" y1="0" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4017" x2="4017" y1="-38" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-38" y2="-38"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-210" y2="-210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-149" y2="-150"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3464" x2="3431" y1="-77" y2="-77"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3464" x2="3431" y1="-62" y2="-62"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3855" x2="3855" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3605" x2="3605" y1="-210" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3680" x2="3680" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3854" x2="3605" y1="-75" y2="-75"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3855" x2="3605" y1="-114" y2="-114"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="915" y1="-1861" y2="-361"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="213" x2="213" y1="-1862" y2="-741"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1940" x2="1964" y1="-1871" y2="-1853"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1787" y1="-1862" y2="-552"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1328" x2="1328" y1="-1583" y2="-1382"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1328" x2="1328" y1="-1814" y2="-1613"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1328" x2="1328" y1="-1862" y2="-1116"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1328" x2="1328" y1="-1862" y2="-787"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1328" x2="1328" y1="-1814" y2="-1613"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1328" x2="1328" y1="-1583" y2="-1382"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="915" y1="-1583" y2="-1382"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="915" y1="-1583" y2="-1382"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="661" y1="-1634" y2="-1634"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="1120" y1="-1634" y2="-1634"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="213" x2="213" y1="-1302" y2="-206"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1931" x2="1901" y1="-1852" y2="-1852"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1901" x2="1901" y1="-1852" y2="-1872"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1931" x2="1931" y1="-1872" y2="-1852"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1901" x2="1931" y1="-1872" y2="-1872"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1931" x2="2015" y1="-1862" y2="-1862"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2070" x2="2015" y1="-1673" y2="-1673"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2015" x2="2015" y1="-2249" y2="-1329"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2003" x2="2027" y1="-2193" y2="-2175"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2079" x2="2015" y1="-1616" y2="-1616"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2200" x2="2020" y1="-1744" y2="-1744"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2351" x2="2351" y1="-1807" y2="-1744"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2447" x2="2447" y1="-1807" y2="-1744"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2546" x2="2546" y1="-1807" y2="-1744"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="3392" y1="-1234" y2="-1234"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3392" y1="-2667" y2="-1234"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-2667" y2="-2667"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-2587" y2="-2587"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-2498" y2="-2498"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-2365" y2="-2365"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-2244" y2="-2244"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-2121" y2="-2121"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-1914" y2="-1914"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-1803" y2="-1803"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-1673" y2="-1673"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-1562" y2="-1562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-1430" y2="-1430"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="2178" y1="-1190" y2="-1190"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="2178" y1="-1127" y2="-1127"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="2178" y1="-1038" y2="-1038"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2003" x2="2027" y1="-1515" y2="-1497"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="2836" y1="-953" y2="-953"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2836" x2="2836" y1="-895" y2="-953"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2192" x2="2192" y1="-895" y2="-953"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2347" x2="2347" y1="-895" y2="-953"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2503" x2="2503" y1="-855" y2="-953"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2684" y1="-853" y2="-911"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2503" y1="-911" y2="-911"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="2178" y1="-721" y2="-721"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="4058" y1="-608" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2800" x2="2800" y1="-550" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3000" x2="3000" y1="-942" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3000" x2="3109" y1="-942" y2="-942"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3000" x2="3109" y1="-840" y2="-840"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3122" x2="3122" y1="-550" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3260" x2="3260" y1="-550" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3447" x2="3447" y1="-1122" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3631" x2="3631" y1="-550" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3797" x2="3797" y1="-550" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3447" x2="3320" y1="-1122" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3447" x2="3320" y1="-992" y2="-992"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3447" x2="3320" y1="-842" y2="-842"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="2178" y1="-384" y2="-384"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2003" x2="2027" y1="-220" y2="-202"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1475" x2="1328" y1="-1516" y2="-1516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1393" x2="1328" y1="-1422" y2="-1422"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1448" x2="1328" y1="-1340" y2="-1340"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1393" x2="1329" y1="-1247" y2="-1247"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1393" x2="1328" y1="-1141" y2="-1141"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1393" x2="1328" y1="-1035" y2="-1035"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1489" x2="1328" y1="-895" y2="-895"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="915" y1="-1420" y2="-1420"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="915" y1="-1345" y2="-1345"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1045" x2="915" y1="-1252" y2="-1252"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="915" y1="-1168" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="915" y1="-1060" y2="-1060"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1136" x2="915" y1="-951" y2="-951"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1019" x2="915" y1="-733" y2="-733"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="915" y1="-489" y2="-489"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="915" y1="-361" y2="-361"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="278" x2="213" y1="-1671" y2="-1671"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="278" x2="213" y1="-1522" y2="-1522"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="278" x2="213" y1="-1409" y2="-1409"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="724" x2="213" y1="-1140" y2="-1140"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="357" x2="213" y1="-1001" y2="-1001"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="306" x2="213" y1="-812" y2="-812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="290" x2="213" y1="-641" y2="-641"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="278" x2="213" y1="-513" y2="-513"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="278" x2="213" y1="-357" y2="-357"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="278" x2="213" y1="-206" y2="-206"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3598" x2="3374" y1="-209" y2="-210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1901" y1="-1862" y2="-1862"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="915" y1="-1862" y2="-1861"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="213" y1="-1861" y2="-1862"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1164" x2="1120" y1="-1634" y2="-1634"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3933" x2="3933" y1="-550" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4058" x2="4058" y1="-550" y2="-608"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2200" x2="2546" y1="-1744" y2="-1744"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2035" x2="2059" y1="-1752" y2="-1734"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2016" x2="2311" y1="-1454" y2="-1454"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="2566" y1="-1549" y2="-1549"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1392" x2="1328" y1="-1598" y2="-1598"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3229" x2="3392" y1="-2472" y2="-2472"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2014" x2="2167" y1="-1834" y2="-1834"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2053" x2="2053" y1="-1819" y2="-1819"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2022" x2="2046" y1="-1846" y2="-1828"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1762" x2="1673" y1="-1105" y2="-1105"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1762" y1="-1183" y2="-1105"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2002" x2="2026" y1="-1287" y2="-1269"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2015" x2="2015" y1="-1299" y2="-135"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2186" x2="2015" y1="-1384" y2="-1384"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3555" y1="-1333" y2="-1333"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1734" x2="1734" y1="-1462" y2="-1563"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2004" x2="2028" y1="-681" y2="-663"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="2819" x2="2819" y1="-1234" y2="-1397"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="2942" x2="2942" y1="-1234" y2="-1462"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3093" x2="3093" y1="-1299" y2="-1463"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3093" x2="2942" y1="-1299" y2="-1299"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3402" x2="3494" y1="-70" y2="-70"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3598" x2="3598" y1="-10" y2="-210"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3374" x2="3598" y1="-10" y2="-10"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3374" x2="3374" y1="-210" y2="-10"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3402" x2="3494" y1="-159" y2="-159"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3438" x2="3458" y1="-149" y2="-169"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3402" x2="3439" y1="-124" y2="-124"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3469" x2="3469" y1="-134" y2="-114"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3439" x2="3439" y1="-114" y2="-134"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3469" x2="3439" y1="-114" y2="-114"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3439" x2="3469" y1="-134" y2="-134"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3469" x2="3494" y1="-124" y2="-124"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3394" x2="3488" y1="-27" y2="-27"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="903" x2="927" y1="-1830" y2="-1812"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1775" x2="1799" y1="-1824" y2="-1806"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1612" x2="1636" y1="-1871" y2="-1853"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1775" x2="1799" y1="-609" y2="-591"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1316" x2="1340" y1="-1787" y2="-1769"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3379" x2="3403" y1="-1978" y2="-1960"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2649" x2="2673" y1="-1244" y2="-1226"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2058" x2="2082" y1="-1136" y2="-1118"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2988" x2="3012" y1="-638" y2="-620"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3436" x2="3460" y1="-745" y2="-727"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3331" x2="3355" y1="-616" y2="-598"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="903" x2="927" y1="-1589" y2="-1571"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="952" x2="976" y1="-961" y2="-943"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="870" x2="894" y1="-888" y2="-870"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="201" x2="225" y1="-1787" y2="-1769"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2582" x2="2606" y1="-920" y2="-902"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3697" x2="3721" y1="-617" y2="-599"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3411" x2="3435" y1="-1571" y2="-1553"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="658" x2="682" y1="-1185" y2="-1167"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3312" x2="3336" y1="-2482" y2="-2464"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1725" x2="1749" y1="-1115" y2="-1097"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3411" x2="3435" y1="-1342" y2="-1324"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1746" x2="1770" y1="-741" y2="-723"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1737" x2="1761" y1="-1005" y2="-987"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1737" x2="1761" y1="-1291" y2="-1273"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1684" x2="1708" y1="-1471" y2="-1453"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="0.5" x1="2808" x2="2832" y1="-1261" y2="-1243"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="0.5" x1="2931" x2="2955" y1="-1261" y2="-1243"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3082" x2="3106" y1="-1326" y2="-1308"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2015" x2="1879" y1="-2050" y2="-2050"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1756" x2="1713" y1="-1770" y2="-1770"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1717" y1="-1770" y2="-1770"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1550" x2="1550" y1="-1862" y2="-1779"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1164" x2="1164" y1="-1603" y2="-1561"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1164" x2="1164" y1="-1634" y2="-1561"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1717" y1="-1668" y2="-1668"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1616" y1="-1563" y2="-1563"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1717" y1="-1183" y2="-1183"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1717" y1="-655" y2="-655"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1717" y1="-840" y2="-840"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="297" x2="297" y1="-1862" y2="-1945"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="914" x2="872" y1="-1741" y2="-1741"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="802" y1="-1741" y2="-1741"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="998" x2="998" y1="-1603" y2="-1561"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="998" x2="998" y1="-1634" y2="-1561"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="804" x2="804" y1="-1603" y2="-1561"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="804" x2="804" y1="-1634" y2="-1561"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="661" x2="661" y1="-1634" y2="-1561"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1067" x2="1067" y1="-951" y2="-908"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1067" x2="1067" y1="-951" y2="-881"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="598" y1="-880" y2="-880"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="707" x2="707" y1="-880" y2="-838"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="707" x2="707" y1="-880" y2="-798"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="598" x2="598" y1="-880" y2="-838"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="598" x2="598" y1="-880" y2="-798"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="829" x2="829" y1="-880" y2="-838"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="829" x2="829" y1="-880" y2="-798"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="915" x2="645" y1="-653" y2="-653"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="645" x2="645" y1="-653" y2="-610"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="645" x2="645" y1="-653" y2="-582"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="759" x2="759" y1="-653" y2="-610"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="759" x2="759" y1="-653" y2="-582"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="453" x2="453" y1="-1862" y2="-1945"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="605" x2="605" y1="-1862" y2="-1945"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="761" x2="761" y1="-1862" y2="-1945"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1122" x2="1122" y1="-1862" y2="-1779"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="724" x2="724" y1="-1140" y2="-1067"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="367" x2="367" y1="-1140" y2="-1214"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="466" x2="466" y1="-1140" y2="-1214"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="561" x2="561" y1="-1140" y2="-1214"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3226" y1="-2121" y2="-2121"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="669" x2="669" y1="-1140" y2="-1272"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="826" x2="761" y1="-1892" y2="-1892"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1604" y1="-1105" y2="-1105"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1663" y1="-733" y2="-733"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1663" y1="-996" y2="-996"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1787" x2="1663" y1="-1282" y2="-1282"/>
   <line DF8003:Layer="公变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1734" x2="1610" y1="-1462" y2="-1462"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="4205" y1="-2970" y2="-2969"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="4205" y1="-2969" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="0" y1="0" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="0" y1="0" y2="-2970"/>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="0" cx="1853" cy="-2051" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1866" cy="-2051" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2082" cy="-1672" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2094" cy="-1672" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2091" cy="-1616" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2103" cy="-1616" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2350" cy="-1819" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2350" cy="-1832" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2447" cy="-1819" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2447" cy="-1832" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2545" cy="-1819" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2545" cy="-1832" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-2666" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-2666" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-2586" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-2586" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-2498" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-2498" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-2365" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-2365" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-2243" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-2243" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-2121" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-2121" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-1914" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-1914" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-1802" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-1802" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-1673" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-1673" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-1562" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-1562" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-1430" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-1430" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2190" cy="-1190" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2203" cy="-1190" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2190" cy="-1127" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2203" cy="-1127" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2190" cy="-1038" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2203" cy="-1038" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2837" cy="-869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2837" cy="-882" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2193" cy="-869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2193" cy="-882" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2348" cy="-869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2348" cy="-882" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2504" cy="-829" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2504" cy="-842" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2685" cy="-827" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2685" cy="-840" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2190" cy="-720" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2203" cy="-720" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2801" cy="-524" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2801" cy="-537" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3121" cy="-941" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3134" cy="-941" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3121" cy="-840" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3134" cy="-840" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3123" cy="-524" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3123" cy="-537" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3261" cy="-524" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3261" cy="-537" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3632" cy="-524" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3632" cy="-537" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3797" cy="-524" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3797" cy="-537" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3295" cy="-1121" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3307" cy="-1121" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3295" cy="-991" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3307" cy="-991" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3295" cy="-841" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3307" cy="-841" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2190" cy="-384" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2203" cy="-384" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1692" cy="-1769" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1704" cy="-1769" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1692" cy="-1668" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1704" cy="-1668" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1591" cy="-1563" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1603" cy="-1563" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1692" cy="-1182" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1704" cy="-1182" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1692" cy="-655" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1704" cy="-655" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1692" cy="-840" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1704" cy="-840" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1549" cy="-1753" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1549" cy="-1766" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1404" cy="-1598" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1416" cy="-1598" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1487" cy="-1517" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1500" cy="-1517" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1405" cy="-1422" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1418" cy="-1422" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1460" cy="-1340" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1473" cy="-1340" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1405" cy="-1246" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1418" cy="-1246" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1405" cy="-1140" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1418" cy="-1140" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1405" cy="-1034" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1418" cy="-1034" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1501" cy="-896" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1514" cy="-896" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1327" cy="-762" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1327" cy="-774" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="297" cy="-1969" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="297" cy="-1957" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="777" cy="-1741" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="789" cy="-1741" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="992" cy="-1419" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1005" cy="-1419" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1164" cy="-1535" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1164" cy="-1548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="997" cy="-1535" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="997" cy="-1548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="803" cy="-1535" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="803" cy="-1548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="660" cy="-1535" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="660" cy="-1548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="992" cy="-1344" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1005" cy="-1344" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1057" cy="-1252" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1070" cy="-1252" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="992" cy="-1168" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1005" cy="-1168" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="992" cy="-1060" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1005" cy="-1060" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1148" cy="-950" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1160" cy="-950" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1066" cy="-856" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1066" cy="-869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="706" cy="-772" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="706" cy="-785" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1031" cy="-733" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1043" cy="-733" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="598" cy="-772" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="598" cy="-785" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="828" cy="-772" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="828" cy="-785" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="643" cy="-556" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="643" cy="-569" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="757" cy="-556" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="757" cy="-569" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="992" cy="-488" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1005" cy="-488" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="992" cy="-361" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1005" cy="-361" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="453" cy="-1969" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="453" cy="-1957" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="605" cy="-1969" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="605" cy="-1957" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="761" cy="-1969" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="761" cy="-1957" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1121" cy="-1753" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1121" cy="-1766" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="290" cy="-1671" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="303" cy="-1671" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="290" cy="-1522" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="303" cy="-1522" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="290" cy="-1408" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="303" cy="-1408" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="369" cy="-1001" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="382" cy="-1001" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="318" cy="-812" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="331" cy="-812" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="302" cy="-640" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="315" cy="-640" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="290" cy="-512" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="303" cy="-512" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="290" cy="-356" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="303" cy="-356" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="290" cy="-206" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="303" cy="-206" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="723" cy="-1042" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="723" cy="-1054" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="367" cy="-1238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="367" cy="-1226" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="465" cy="-1238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="465" cy="-1226" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="561" cy="-1238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="561" cy="-1226" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3933" cy="-524" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3933" cy="-537" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4058" cy="-524" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4058" cy="-537" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3201" cy="-2121" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3213" cy="-2121" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2323" cy="-1454" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2336" cy="-1454" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2578" cy="-1548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2590" cy="-1548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="669" cy="-1296" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="669" cy="-1284" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3203" cy="-2473" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3216" cy="-2473" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="838" cy="-1891" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="850" cy="-1891" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2179" cy="-1834" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2195" cy="-1833" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1578" cy="-1105" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1591" cy="-1105" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2198" cy="-1383" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2210" cy="-1383" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3567" cy="-1333" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3580" cy="-1333" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1637" cy="-732" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1650" cy="-732" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1637" cy="-996" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1650" cy="-996" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1637" cy="-1282" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1650" cy="-1282" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1584" cy="-1462" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1597" cy="-1462" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2818" cy="-1409" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2818" cy="-1422" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2942" cy="-1474" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2942" cy="-1487" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3092" cy="-1475" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3092" cy="-1487" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3448" cy="-77" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3448" cy="-77" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3448" cy="-62" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3448" cy="-62" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1846.000000 -1900.000000) translate(0,12)">小黑阱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1879.000000 -1847.000000) translate(0,20)">Z061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -65.000000) translate(0,10)">谢丛晖</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3714.000000 -27.000000) translate(0,10)">谢爱生</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 4046.000000 -30.000000) translate(0,10)">2012年12月11日</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2121.000000 -1890.000000) translate(0,10)">冯荣兴魔芋加工厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2121.000000 -1890.000000) translate(0,22)">   S11-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 267.000000 -1441.000000) translate(0,10)">#3杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 583.000000 -1211.000000) translate(0,10)">#6杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 483.000000 -1214.000000) translate(0,10)">#8杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 795.000000 -1884.000000) translate(0,10)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1042.000000 -1282.000000) translate(0,10)">#6杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2029.000000 -752.000000) translate(0,10)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2036.000000 -712.000000) translate(0,10)">10kV么榨拉支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1355.000000 -1596.000000) translate(0,10)">#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1016.000000 -1555.000000) translate(0,10)">#6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1472.000000 -909.000000) translate(0,10)">#6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2150.000000 -1214.000000) translate(0,10)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2147.000000 -743.000000) translate(0,10)">#5杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1969.000000 -1842.000000) translate(0,10)">#17杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2136.000000 -1826.000000) translate(0,10)">#2杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 632.000000 -1272.000000) translate(0,10)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2042.000000 -2311.000000) translate(0,20)">0561</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2046.000000 -2233.000000) translate(0,20)">0566</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1958.000000 -2222.000000) translate(0,20)">大过口线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2045.000000 -2271.000000) translate(0,20)">056</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2131.000000 -1604.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2123.000000 -1627.000000) translate(0,12)">联通机站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1439.000000 -1062.000000) translate(0,12)">碧鸡水田</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1425.000000 -1396.000000) translate(0,12)">下村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1451.000000 -1270.000000) translate(0,12)">旧村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1383.000000 -1476.000000) translate(0,12)">中村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1285.000000 -1824.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1285.000000 -1824.000000) translate(0,27)">碧</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1285.000000 -1824.000000) translate(0,42)">鸡</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1285.000000 -1824.000000) translate(0,57)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1285.000000 -1824.000000) translate(0,72)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1285.000000 -1824.000000) translate(0,87)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1295.000000 -745.000000) translate(0,12)">碧鸡上下排</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1459.000000 -882.000000) translate(0,12)">四角田</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1452.000000 -1165.000000) translate(0,12)">核桃箐</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1460.000000 -1736.000000) translate(0,12)">小黑阱加工厂私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -1706.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1442.000000 -1570.000000) translate(0,12)">坟地山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1033.000000 -1366.000000) translate(0,12)">蕨菜地</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1197.000000 -975.000000) translate(0,12)">刘家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1040.000000 -843.000000) translate(0,12)">瓦窑箐</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 756.000000 -1523.000000) translate(0,12)">小箐头私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 642.000000 -1519.000000) translate(0,12)">山腰</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1122.000000 -1522.000000) translate(0,12)">山背后私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1524.000000) translate(0,12)">爱可背私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1109.000000 -1284.000000) translate(0,12)">陈家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 617.000000 -543.000000) translate(0,12)">咪的么</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1048.000000 -512.000000) translate(0,12)">瓦房</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1035.000000 -1087.000000) translate(0,12)">九台山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 571.000000 -759.000000) translate(0,12)">拔刀岭</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 680.000000 -759.000000) translate(0,12)">蚕豆田</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1839.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1839.000000) translate(0,27)">蚕</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1839.000000) translate(0,42)">豆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1839.000000) translate(0,57)">田</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1839.000000) translate(0,72)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1839.000000) translate(0,87)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 946.000000 -1839.000000) translate(0,102)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 720.000000 -1724.000000) translate(0,12)">大益鸡上下村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1037.000000 -1198.000000) translate(0,12)">磨盘山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1031.000000 -1443.000000) translate(0,12)">冲脑私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 579.000000 -2037.000000) translate(0,12)"> 陶家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 345.000000 -837.000000) translate(0,12)">西康郎村委会</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 398.000000 -1025.000000) translate(0,12)">西康郎大村私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 347.000000 -665.000000) translate(0,12)">五松力</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -376.000000) translate(0,12)">干田</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 331.000000 -228.000000) translate(0,12)">仓房</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1677.000000 -632.000000) translate(0,12)">洼子</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1571.000000 -1549.000000) translate(0,12)">黑奶依</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1667.000000 -1747.000000) translate(0,12)">小海子</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1605.000000 -1203.000000) translate(0,12)">大龙潭</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -1648.000000) translate(0,12)">肖井</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,45)">中</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,70)">洋</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,95)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,120)">六</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,145)">街</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,170)">联</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,195)">络</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1742.000000) translate(0,220)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 988.000000 -1943.000000) translate(0,20)">10kV    西       康       郎       支       线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1033.000000 -383.000000) translate(0,12)">新旧村变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2021.000000 -2166.000000) translate(0,20)">F0611</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2021.000000 -2060.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2124.000000 -1665.000000) translate(0,12)">S11-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2123.000000 -1688.000000) translate(0,12)">自来水厂私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2305.000000 -1890.000000) translate(0,12)">磨刀箐</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2302.000000 -1867.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2426.000000 -1888.000000) translate(0,12)">母掌</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2414.000000 -1865.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2520.000000 -1890.000000) translate(0,12)">郭溪租</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2513.000000 -1868.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1962.000000 -1757.000000) translate(0,12)">#39杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2519.000000 -1738.000000) translate(0,12)">#42杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -2676.000000) translate(0,12)">#99杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1878.000000 -2003.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -2591.000000) translate(0,12)">#91杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -2509.000000) translate(0,12)">#83杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -2374.000000) translate(0,12)">#69杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -2253.000000) translate(0,12)">#54杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3399.000000 -2148.000000) translate(0,12)">#36杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -1923.000000) translate(0,12)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -1812.000000) translate(0,12)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -1682.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -1571.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -1439.000000) translate(0,12)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -1984.000000) translate(0,12)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -1984.000000) translate(0,27)">Z0812</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -2561.000000) translate(0,12)">#84杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -2561.000000) translate(0,27)">BZ0821</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -2655.000000) translate(0,12)">#92杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -2655.000000) translate(0,27)">BZ0822</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -2453.000000) translate(0,12)">#70杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -2453.000000) translate(0,27)">BZ082</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2428.000000 -1738.000000) translate(0,12)">#32杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2325.000000 -1738.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -1624.000000) translate(0,12)">#41杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -1686.000000) translate(0,12)">#40杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2027.000000 -1799.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2027.000000 -1799.000000) translate(0,27)">Z0681</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2650.000000 -1265.000000) translate(0,12)">Z0811</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -1141.000000) translate(0,12)">#61杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -1202.000000) translate(0,12)">#46杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -1045.000000) translate(0,12)">#64杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -1245.000000) translate(0,12)">#46杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1920.000000 -1509.000000) translate(0,20)">F0631</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1179.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1179.000000) translate(0,27)">Z0826</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -963.000000) translate(0,12)">#67杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2094.000000 -943.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2094.000000 -943.000000) translate(0,27)">BZ083</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2816.000000 -849.000000) translate(0,12)">维依亨</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2809.000000 -827.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2179.000000 -849.000000) translate(0,12)">胡家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2165.000000 -827.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2320.000000 -849.000000) translate(0,12)">法古么</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2315.000000 -827.000000) translate(0,12)">D9-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2481.000000 -810.000000) translate(0,12)">闪片房</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2476.000000 -787.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2672.000000 -807.000000) translate(0,12)">四西</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2657.000000 -785.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -728.000000) translate(0,12)">#72杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2171.000000 -974.000000) translate(0,12)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2333.000000 -974.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2490.000000 -974.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2815.000000 -974.000000) translate(0,12)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2084.000000 -621.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2084.000000 -621.000000) translate(0,27)">BZ084</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2779.000000 -504.000000) translate(0,12)">箐边</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2773.000000 -482.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2778.000000 -629.000000) translate(0,12)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2942.000000 -950.000000) translate(0,12)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2959.000000 -855.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3081.000000 -504.000000) translate(0,12)">野猪塘山背后</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3095.000000 -482.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3101.000000 -629.000000) translate(0,12)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3239.000000 -504.000000) translate(0,12)">何家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3233.000000 -482.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3238.000000 -629.000000) translate(0,12)">#25杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3611.000000 -504.000000) translate(0,12)">马扎山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3604.000000 -482.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3609.000000 -629.000000) translate(0,12)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -504.000000) translate(0,12)">大舍腰</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3770.000000 -482.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3775.000000 -629.000000) translate(0,12)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3455.000000 -1132.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3455.000000 -1001.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3402.000000 -868.000000) translate(0,12)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -391.000000) translate(0,12)">#82杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -618.000000) translate(0,12)">#80杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3388.000000 -765.000000) translate(0,12)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3388.000000 -765.000000) translate(0,27)">Z0844</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3329.000000 -641.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3326.000000 -596.000000) translate(0,12)">Z0843</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2038.000000 -225.000000) translate(0,20)">F0651</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -242.000000) translate(0,12)">#87杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1918.000000 -120.000000) translate(0,20)">至密者河一级电站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2021.000000 -1872.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1922.000000 -1905.000000) translate(0,20)">Z0611</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2111.000000 -1263.000000) translate(0,12)">10kV   杞  叉  拉   支    线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2111.000000 -644.000000) translate(0,12)">10kV  野  猪  塘  支   线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2111.000000 -1001.000000) translate(0,12)">10kV  维  依  亨   支    线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1659.000000 -1724.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1801.000000 -1778.000000) translate(0,12)">139杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1801.000000 -1680.000000) translate(0,12)">138杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1659.000000 -1624.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1559.000000 -1523.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1603.000000 -1179.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1659.000000 -609.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1792.000000 -1573.000000) translate(0,12)">136杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1795.000000 -1194.000000) translate(0,12)">131杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1648.000000 -824.000000) translate(0,12)">磨刀箐新村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1659.000000 -802.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1801.000000 -847.000000) translate(0,12)">119杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1801.000000 -661.000000) translate(0,12)">114杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1804.000000 -1842.000000) translate(0,12)">150杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1804.000000 -1842.000000) translate(0,27)">L0291</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1804.000000 -602.000000) translate(0,12)">113杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1804.000000 -602.000000) translate(0,27)">L0271</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1727.000000 -525.000000) translate(0,12)">至六街四方架</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1617.000000 -1852.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1617.000000 -1852.000000) translate(0,27)">Z0612</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1525.000000 -1889.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1357.000000 -1627.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1373.000000 -1650.000000) translate(0,12)">麦架山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1435.000000 -1548.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1374.000000 -1453.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1413.000000 -1373.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1440.000000 -1246.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1440.000000 -1140.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1446.000000 -1034.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1451.000000 -860.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1307.000000 -720.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -1257.000000) translate(0,12)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -1150.000000) translate(0,12)">#25杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -1048.000000) translate(0,12)">#29杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -908.000000) translate(0,12)">#32杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -805.000000) translate(0,12)">#35杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1330.000000 -883.000000) translate(0,12)">BZ064</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -990.000000) translate(0,12)">#30杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -990.000000) translate(0,27)">BZ062</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1344.000000 -1796.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1344.000000 -1796.000000) translate(0,27)">Z0621</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1097.000000 -1706.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1078.000000 -1736.000000) translate(0,12)">茶厂私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1094.000000 -1889.000000) translate(0,12)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 888.000000 -1889.000000) translate(0,12)">#21杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 733.000000 -1695.000000) translate(0,12)">S7-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 853.000000 -1850.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 853.000000 -1850.000000) translate(0,27)">Z0631</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1027.000000 -1419.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 963.000000 -1499.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1134.000000 -1499.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 625.000000 -1495.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1178.000000 -1574.000000) translate(0,12)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 984.000000 -1659.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 785.000000 -1657.000000) translate(0,12)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 648.000000 -1656.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 778.000000 -1496.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1073.000000 -1622.000000) translate(0,12)">10kV山背</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1073.000000 -1622.000000) translate(0,27)">后分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 609.000000 -1675.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 609.000000 -1675.000000) translate(0,27)">山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 609.000000 -1675.000000) translate(0,42)">腰</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 609.000000 -1675.000000) translate(0,57)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 609.000000 -1675.000000) translate(0,72)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 609.000000 -1675.000000) translate(0,87)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1027.000000 -1344.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1092.000000 -1260.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1027.000000 -1167.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1027.000000 -1059.000000) translate(0,12)">S8-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -1431.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 859.000000 -1658.000000) translate(0,12)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 923.000000 -1597.000000) translate(0,12)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 923.000000 -1597.000000) translate(0,27)">Z0632</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -1355.000000) translate(0,12)">#25杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -1265.000000) translate(0,12)">#30杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -1178.000000) translate(0,12)">#33杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -1071.000000) translate(0,12)">#36杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -961.000000) translate(0,12)">#38杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1182.000000 -950.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1037.000000 -820.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 677.000000 -736.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1072.000000 -733.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 568.000000 -736.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 799.000000 -736.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 978.000000 -973.000000) translate(0,12)">Z0641</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 918.000000 -941.000000) translate(0,12)">10kV刘家分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1048.000000 -972.000000) translate(0,12)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1121.000000 -985.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 813.000000 -901.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 693.000000 -901.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 585.000000 -901.000000) translate(0,12)">#15杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 785.000000 -758.000000) translate(0,12)">蚕豆田水田</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 614.000000 -520.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 727.000000 -520.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 743.000000 -674.000000) translate(0,12)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 630.000000 -674.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 724.000000 -930.000000) translate(0,12)">10kV拔刀岭分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 715.000000 -541.000000) translate(0,12)">蚕豆田大小村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1034.000000 -488.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1034.000000 -361.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 922.000000 -890.000000) translate(0,12)">#41杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 918.000000 -762.000000) translate(0,12)">#48杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 922.000000 -663.000000) translate(0,12)">#49杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -497.000000) translate(0,12)">#55杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 857.000000 -368.000000) translate(0,12)">#67杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 859.000000 -866.000000) translate(0,12)">Z0642</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 841.000000 -641.000000) translate(0,12)">BZ065</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 585.000000 -1858.000000) translate(0,12)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 738.000000 -1858.000000) translate(0,12)">#23杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 436.000000 -1858.000000) translate(0,12)">#33杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 273.000000 -1858.000000) translate(0,12)">#39杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 730.000000 -2010.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 579.000000 -2010.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 413.000000 -2010.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 260.000000 -2010.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 746.000000 -2038.000000) translate(0,12)">道班</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 229.000000 -1796.000000) translate(0,12)">#40杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 229.000000 -1796.000000) translate(0,27)">Z0613</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 344.000000 -1671.000000) translate(0,12)">S8-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -1682.000000) translate(0,12)">#41杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 325.000000 -1521.000000) translate(0,12)">S8-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -1532.000000) translate(0,12)">#50杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 325.000000 -1408.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -1419.000000) translate(0,12)">#57杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 427.000000 -1000.000000) translate(0,12)">S8-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -1011.000000) translate(0,12)">#60杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 353.000000 -811.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 220.000000 -843.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -640.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -651.000000) translate(0,12)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 325.000000 -512.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -523.000000) translate(0,12)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 325.000000 -356.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -367.000000) translate(0,12)">#28杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 325.000000 -205.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -217.000000) translate(0,12)">#36杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 343.000000 -1134.000000) translate(0,12)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 446.000000 -1134.000000) translate(0,12)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 545.000000 -1134.000000) translate(0,12)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 729.000000 -1150.000000) translate(0,12)">#27杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 691.000000 -1000.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -1273.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 426.000000 -1273.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 335.000000 -1273.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 319.000000 -1693.000000) translate(0,12)">西康朗咪自掌私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 717.000000 -701.000000) translate(0,12)">10kV咪的么分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 156.000000 -1153.000000) translate(0,12)">#58杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 673.000000 -1025.000000) translate(0,12)">新龙街私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 333.000000 -533.000000) translate(0,12)">支锅山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 256.000000 -1201.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 256.000000 -1201.000000) translate(0,27)">BZ066</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,27)">仓</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,42)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,57)">房</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,72)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,87)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,102)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,117)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,132)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 167.000000 -963.000000) translate(0,147)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1310.000000 -1889.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -1608.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -1528.000000) translate(0,12)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -1433.000000) translate(0,12)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1272.000000 -1356.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 867.000000 -1768.000000) translate(0,12)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 224.000000 -754.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 224.000000 -754.000000) translate(0,27)">BZ067</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2647.000000 -1226.000000) translate(0,12)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2444.000000 -916.000000) translate(0,12)">#23杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2577.000000 -927.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2577.000000 -927.000000) translate(0,27)">Z0835</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2976.000000 -605.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3432.000000 -605.000000) translate(0,12)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1336.000000 -929.000000) translate(0,12)">10kV四角田分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2050.000000 -1111.000000) translate(0,12)">10kV中野猪塘支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2525.000000 -938.000000) translate(0,12)">10kV四西分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3906.000000 -632.000000) translate(0,12)">#30杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4024.000000 -635.000000) translate(0,12)">#39杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2088.000000 -1769.000000) translate(0,12)">10kV磨刀箐支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2354.000000 -1476.000000) translate(0,12)">大过口集镇</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2358.000000 -1453.000000) translate(0,12)">S9-315kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2609.000000 -1571.000000) translate(0,12)">魔芋厂私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2612.000000 -1548.000000) translate(0,12)">S11-100kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2061.000000 -1479.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2061.000000 -1479.000000) translate(0,27)">BZ069</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1959.000000 -1466.000000) translate(0,12)">#42杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1958.000000 -1560.000000) translate(0,12)">#42杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2114.000000 -1490.000000) translate(0,12)">10kV大过口集镇支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 679.000000 -866.000000) translate(0,12)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2036.000000 -2189.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 652.000000 -1135.000000) translate(0,12)">#26杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 634.000000 -1332.000000) translate(0,12)">S9-250kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 619.000000 -1187.000000) translate(0,12)">#0杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 675.000000 -1217.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 675.000000 -1217.000000) translate(0,27)">Z0665</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2036.000000 -1513.000000) translate(0,12)">#42杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 936.000000 -1281.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1944.000000 -2078.000000) translate(0,12)">BZ061</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1347.000000 -1504.000000) translate(0,12)">BZ063</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1347.000000 -1329.000000) translate(0,12)">BZ072</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 846.000000 -1849.000000) translate(0,12)">#21杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 923.000000 -1241.000000) translate(0,12)">BZ073</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 720.000000 -866.000000) translate(0,12)">BZ074</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 224.000000 -991.000000) translate(0,12)">BZ075</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3423.000000 -1592.000000) translate(0,12)">Z0814</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3400.000000 -1793.000000) translate(0,12)">BZ076</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -2149.000000) translate(0,12)">BZ077</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2438.000000 -944.000000) translate(0,12)">BZ078</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3398.000000 -2481.000000) translate(0,12)">#80杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3305.000000 -2465.000000) translate(0,12)">K0818</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3279.000000 -2498.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 801.000000 -1929.000000) translate(0,12)">唐协贵专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 712.000000 -1900.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3682.000000 -637.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3673.000000 -594.000000) translate(0,12)">Z0845</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3014.000000 -666.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3014.000000 -666.000000) translate(0,27)">Z0842</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 811.000000 -1951.000000) translate(0,12)">S11-125kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1705.000000 -1120.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1705.000000 -1120.000000) translate(0,27)">Z0293</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1735.000000 -1121.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1555.000000 -1091.000000) translate(0,12)">大龙潭1号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1600.000000 -1133.000000) translate(0,8)">10kV大龙潭1号变支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1553.000000 -1066.000000) translate(0,12)">SHB15-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1623.000000 -1119.000000) translate(0,8)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1930.000000 -1325.000000) translate(0,20)">F064</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1915.000000 -1295.000000) translate(0,20)">F0641</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1933.000000 -1391.000000) translate(0,12)">#43+1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2020.000000 -1299.000000) translate(0,12)">#45杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2020.000000 -1416.000000) translate(0,12)">10kV集镇2号支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2228.000000 -1403.000000) translate(0,12)">集镇2号公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2225.000000 -1380.000000) translate(0,12)">S13-M-125kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3334.000000 -1342.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3423.000000 -1352.000000) translate(0,12)">Z0815</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3599.000000 -1332.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1727.000000 -747.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1727.000000 -747.000000) translate(0,27)">Z0295</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1792.000000 -744.000000) translate(0,12)">#116</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1623.000000 -719.000000) translate(0,12)">大平掌变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1688.000000 -756.000000) translate(0,8)">10kV大平掌支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1623.000000 -698.000000) translate(0,12)">SHB15-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1731.000000 -1011.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1731.000000 -1011.000000) translate(0,27)">Z0294</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1792.000000 -1008.000000) translate(0,12)">#127</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1608.000000 -982.000000) translate(0,12)">大龙潭2号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1665.000000 -1022.000000) translate(0,8)">10kV大龙潭2号支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1608.000000 -961.000000) translate(0,12)">SHB15-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1752.000000 -1009.000000) translate(0,8)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1664.000000 -995.000000) translate(0,8)">#2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1665.000000 -731.000000) translate(0,8)">#8</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1731.000000 -1296.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1731.000000 -1296.000000) translate(0,27)">Z0291</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1792.000000 -1293.000000) translate(0,12)">#132</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1608.000000 -1268.000000) translate(0,12)">茨菇塘1号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1665.000000 -1308.000000) translate(0,8)">10kV茨菇塘1号支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1608.000000 -1247.000000) translate(0,12)">SHB15-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1664.000000 -1281.000000) translate(0,8)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1657.000000 -1581.000000) translate(0,8)">10kV黑奶依分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1677.000000 -1476.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1677.000000 -1476.000000) translate(0,27)">Z0292</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1555.000000 -1448.000000) translate(0,12)">茨菇塘2号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1612.000000 -1488.000000) translate(0,8)">10kV茨菇塘2号支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1555.000000 -1427.000000) translate(0,12)">SHB15-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1611.000000 -1461.000000) translate(0,8)">#5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1737.000000 -1561.000000) translate(0,8)">#4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3523.000000 -1352.000000) translate(0,12)">#5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1921.000000 -674.000000) translate(0,20)">F0642</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2037.000000 -679.000000) translate(0,12)">#73杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2803.000000 -1230.000000) translate(0,12)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2755.000000 -1264.000000) translate(0,12)">Z0813</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2720.000000 -1456.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2927.000000 -1230.000000) translate(0,12)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2878.000000 -1264.000000) translate(0,12)">Z0816</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2820.000000 -1520.000000) translate(0,12)">SHB15-M-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3029.000000 -1329.000000) translate(0,12)">Z0817</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2944.000000 -1296.000000) translate(0,12)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2975.000000 -1520.000000) translate(0,12)">SHB15-M-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3523.000000 -92.000000) translate(0,10)">高压计量箱</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1870.000000 -2024.000000) translate(0,10)">三保山私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 247.000000 -2039.000000) translate(0,10)">大益鸡咪自掌</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 336.000000 -1544.000000) translate(0,12)">领岗私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 334.000000 -1431.000000) translate(0,12)">新开田</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 347.000000 -1295.000000) translate(0,12)">路瓦享</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 440.000000 -1328.000000) translate(0,12)">西康郎</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 440.000000 -1328.000000) translate(0,27)"> 新村</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 537.000000 -1298.000000) translate(0,12)">小龙坝</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -2677.000000) translate(0,12)">丫胡S11-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -2597.000000) translate(0,12)">丫罗S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -2509.000000) translate(0,12)">杞叉拉大村S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -2376.000000) translate(0,12)">杞叉拉村委会S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -2254.000000) translate(0,12)">塔地本私变S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3080.000000 -2136.000000) translate(0,12)">洒利亨私变S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -2132.000000) translate(0,12)">小中村S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -1925.000000) translate(0,12)">依齐么村委会S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -1813.000000) translate(0,12)">斗干利S8-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -1684.000000) translate(0,12)">周家私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -1684.000000) translate(0,27)">S8-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -1573.000000) translate(0,12)">移动基站S11-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -1441.000000) translate(0,12)">木克山S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2224.000000 -1201.000000) translate(0,12)">李家S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2224.000000 -1138.000000) translate(0,12)">中野猪塘S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2224.000000 -1049.000000) translate(0,12)">上野猪S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2224.000000 -731.000000) translate(0,12)">么榨拉S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3088.000000 -915.000000) translate(0,12)">小水田</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3088.000000 -915.000000) translate(0,27)">S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3102.000000 -815.000000) translate(0,12)">大路边</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3102.000000 -815.000000) translate(0,27)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3277.000000 -1095.000000) translate(0,12)">地桌子</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3277.000000 -1095.000000) translate(0,27)">S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3273.000000 -965.000000) translate(0,12)">下野猪</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3273.000000 -965.000000) translate(0,27)">S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -823.000000) translate(0,12)">杨家</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -823.000000) translate(0,27)">S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4023.000000 -505.000000) translate(0,12)">石头岩</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4023.000000 -505.000000) translate(0,27)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3908.000000 -505.000000) translate(0,12)">马鞍山</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3908.000000 -505.000000) translate(0,27)">S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2224.000000 -395.000000) translate(0,12)">十街公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2224.000000 -395.000000) translate(0,27)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1062.000000 -756.000000) translate(0,12)">蚕豆田村委会</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 437.000000 -2035.000000) translate(0,12)">田冲</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 233.000000 -1113.000000) translate(0,12)">10kV小龙坝分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3297.000000 -575.000000) translate(0,12)">10kV  石  头  岩  分  支  线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -801.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -801.000000) translate(0,27)">小</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -801.000000) translate(0,42)">水</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -801.000000) translate(0,57)">田</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -801.000000) translate(0,72)">分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -801.000000) translate(0,87)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -801.000000) translate(0,102)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3468.000000 -809.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3468.000000 -809.000000) translate(0,27)">地</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3468.000000 -809.000000) translate(0,42)">桌</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3468.000000 -809.000000) translate(0,57)">子</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3468.000000 -809.000000) translate(0,72)">分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3468.000000 -809.000000) translate(0,87)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3468.000000 -809.000000) translate(0,102)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 644.000000 -1357.000000) translate(0,12)">益民砖厂</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 674.000000 -1272.000000) translate(0,12)">T益民</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 674.000000 -1272.000000) translate(0,27)">砖厂</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 674.000000 -1272.000000) translate(0,42)">分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -2534.000000) translate(0,12)">移动基站专变D-5kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3601.000000 -1354.000000) translate(0,12)">落水洞公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -1324.000000) translate(0,12)">10kV落水洞支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2757.000000 -1437.000000) translate(0,12)">木克山1号公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2760.000000 -1358.000000) translate(0,12)">10kV木克山1号支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2881.000000 -1502.000000) translate(0,12)">木克山2号公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2883.000000 -1356.000000) translate(0,12)">10kV木克山2号支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3031.000000 -1503.000000) translate(0,12)">木克山3号公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -1423.000000) translate(0,12)">10kV木克山3号支线</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -170.000000) translate(0,10)">隔离开关</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3523.000000 -131.000000) translate(0,10)">断路器</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3518.000000 -37.000000) translate(0,10)">跌落保险</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3864.000000 -25.000000) translate(0,10)">更新日期</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -107.000000) translate(0,10)">大过口供电所辖区电网接线图</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1796.000000 -2870.000000) translate(0,40)">大过口供电所电网接线图</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1875.000000 -2543.000000) translate(0,40)">35kV中山变</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1875.000000 -2543.000000) translate(0,90)">10kV母线</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -192.000000) translate(0,18)">楚 雄 市 供 电 有 限 公 司</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3458.000000 -206.000000) translate(0,10)"> 图  例</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3613.000000 -142.000000) translate(0,10)">审　定</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3614.000000 -68.000000) translate(0,10)">校　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3614.000000 -27.000000) translate(0,10)">制  图</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3613.000000 -99.000000) translate(0,10)">审　核</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="标注、文字:0.000000 0.000000" layer11="图框（粗实线）:0.000000 0.000000" layer12="35KV母线:0.000000 0.000000" layer13="10KV线路:0.000000 0.000000" layer14="10KV母线:0.000000 0.000000" layer15="35KV线路:0.000000 0.000000" layer16="虚线:0.000000 0.000000" layer17="Defpoints:0.000000 0.000000" layer18="0:0.000000 0.000000" layer19="主干线:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="次干线:0.000000 0.000000" layer21="支线:0.000000 0.000000" layer22="公变:0.000000 0.000000" layer23="自变:0.000000 0.000000" layer24="图框（细实线）:0.000000 0.000000" layer25="标注、文字:0.000000 0.000000" layer26="图框（粗实线）:0.000000 0.000000" layer27="35KV母线:0.000000 0.000000" layer28="10KV线路:0.000000 0.000000" layer29="10KV母线:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer30="35KV线路:0.000000 0.000000" layer31="虚线:0.000000 0.000000" layer32="Defpoints:0.000000 0.000000" layer4="主干线:0.000000 0.000000" layer5="次干线:0.000000 0.000000" layer6="支线:0.000000 0.000000" layer7="公变:0.000000 0.000000" layer8="自变:0.000000 0.000000" layer9="图框（细实线）:0.000000 0.000000" layerN="33"/>
</svg>