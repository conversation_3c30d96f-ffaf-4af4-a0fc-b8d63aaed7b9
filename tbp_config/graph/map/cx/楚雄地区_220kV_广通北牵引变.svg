<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-269" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-54 -983 1213 988">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="transformer2:shape30_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="8,21 14,10 19,21 " stroke-width="1"/>
   </symbol>
   <symbol id="transformer2:shape30_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="7,43 13,32 18,43 " stroke-width="1"/>
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="voltageTransformer:shape52">
    <circle cx="27" cy="15" r="7.5" stroke-width="0.804311"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,28 13,28 13,1 40,1 40,8 " stroke-width="0.964286"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="24" x2="27" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="27" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="30" x2="27" y1="17" y2="15"/>
    <circle cx="27" cy="27" r="7.5" stroke-width="0.804311"/>
    <circle cx="39" cy="27" r="7.5" stroke-width="0.804311"/>
    <polyline points="27,15 6,15 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="4" x2="4" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="2" x2="2" y1="13" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="6" x2="6" y1="10" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="24" x2="27" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="27" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="30" x2="27" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="40" x2="40" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="40" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="40" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="37" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="41" x2="43" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="37" y1="26" y2="26"/>
    <circle cx="39" cy="15" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2941940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2942390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2942c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2943e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2945010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2945b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29466f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29471a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2947bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2948550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29490b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2949990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_294b540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_294c1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_294c9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_294d3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_294e8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_294fa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2512550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2950fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29521a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2952b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2953610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_248a810" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2955e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29567c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2966410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2957e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_295b6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2955400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="998" width="1223" x="-59" y="-988"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-53" y="-862"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-52" y="-982"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-217358">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 489.000000 -539.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33582" ObjectName="SW-CX_GTBQ.CX_GTBQ_2619SW"/>
     <cge:Meas_Ref ObjectId="217358"/>
    <cge:TPSR_Ref TObjectID="33582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217359">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 454.000000 -587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33583" ObjectName="SW-CX_GTBQ.CX_GTBQ_26197SW"/>
     <cge:Meas_Ref ObjectId="217359"/>
    <cge:TPSR_Ref TObjectID="33583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217357">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 -475.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33581" ObjectName="SW-CX_GTBQ.CX_GTBQ_26167SW"/>
     <cge:Meas_Ref ObjectId="217357"/>
    <cge:TPSR_Ref TObjectID="33581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217356">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33580" ObjectName="SW-CX_GTBQ.CX_GTBQ_2616SW"/>
     <cge:Meas_Ref ObjectId="217356"/>
    <cge:TPSR_Ref TObjectID="33580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217363">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 929.000000 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33586" ObjectName="SW-CX_GTBQ.CX_GTBQ_2626SW"/>
     <cge:Meas_Ref ObjectId="217363"/>
    <cge:TPSR_Ref TObjectID="33586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217364">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -475.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33587" ObjectName="SW-CX_GTBQ.CX_GTBQ_26267SW"/>
     <cge:Meas_Ref ObjectId="217364"/>
    <cge:TPSR_Ref TObjectID="33587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217365">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 -541.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33588" ObjectName="SW-CX_GTBQ.CX_GTBQ_2629SW"/>
     <cge:Meas_Ref ObjectId="217365"/>
    <cge:TPSR_Ref TObjectID="33588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217366">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.000000 -585.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33589" ObjectName="SW-CX_GTBQ.CX_GTBQ_26297SW"/>
     <cge:Meas_Ref ObjectId="217366"/>
    <cge:TPSR_Ref TObjectID="33589"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_GTBQ.CX_GTBQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49168"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(2.615385 -0.000000 0.000000 -2.692308 542.000000 -149.000000)" xlink:href="#transformer2:shape30_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(2.615385 -0.000000 0.000000 -2.692308 542.000000 -149.000000)" xlink:href="#transformer2:shape30_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="33578" ObjectName="TF-CX_GTBQ.CX_GTBQ_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_GTBQ.CX_GTBQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49172"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(2.615385 -0.000000 0.000000 -2.692308 910.000000 -150.000000)" xlink:href="#transformer2:shape30_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(2.615385 -0.000000 0.000000 -2.692308 910.000000 -150.000000)" xlink:href="#transformer2:shape30_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="33579" ObjectName="TF-CX_GTBQ.CX_GTBQ_2T"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21d7d70">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 -478.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24924e0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1145.000000 -479.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 53.000000 -902.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217372" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -374.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217372" ObjectName="CX_GTBQ:CX_GTBQ_111BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217373" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -359.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217373" ObjectName="CX_GTBQ:CX_GTBQ_111BK_Ubc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217374" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -344.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217374" ObjectName="CX_GTBQ:CX_GTBQ_111BK_Uca"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217375" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -329.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217375" ObjectName="CX_GTBQ:CX_GTBQ_111BK_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217382" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -374.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217382" ObjectName="CX_GTBQ:CX_GTBQ_112BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217383" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -359.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217383" ObjectName="CX_GTBQ:CX_GTBQ_112BK_Ubc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217384" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -344.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217384" ObjectName="CX_GTBQ:CX_GTBQ_112BK_Uca"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217385" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -329.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217385" ObjectName="CX_GTBQ:CX_GTBQ_112BK_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217389" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 76.000000 -774.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217389" ObjectName="CX_GTBQ:CX_GTBQ_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -372.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33584"/>
     <cge:Term_Ref ObjectID="49182"/>
    <cge:TPSR_Ref TObjectID="33584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217377" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -372.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217377" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33584"/>
     <cge:Term_Ref ObjectID="49182"/>
    <cge:TPSR_Ref TObjectID="33584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -372.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33584"/>
     <cge:Term_Ref ObjectID="49182"/>
    <cge:TPSR_Ref TObjectID="33584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-217386" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -373.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33585"/>
     <cge:Term_Ref ObjectID="49184"/>
    <cge:TPSR_Ref TObjectID="33585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-217387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -373.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33585"/>
     <cge:Term_Ref ObjectID="49184"/>
    <cge:TPSR_Ref TObjectID="33585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-217379" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -373.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="33585"/>
     <cge:Term_Ref ObjectID="49184"/>
    <cge:TPSR_Ref TObjectID="33585"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="65" y="-961"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="65" y="-961"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="16" y="-978"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="16" y="-978"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="65" y="-961"/></g>
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="16" y="-978"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-217355">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33584" ObjectName="SW-CX_GTBQ.CX_GTBQ_111BK"/>
     <cge:Meas_Ref ObjectId="217355"/>
    <cge:TPSR_Ref TObjectID="33584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-217362">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33585" ObjectName="SW-CX_GTBQ.CX_GTBQ_112BK"/>
     <cge:Meas_Ref ObjectId="217362"/>
    <cge:TPSR_Ref TObjectID="33585"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2263890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.000000 -474.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2258690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 -474.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b5520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 502.000000 -586.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2525910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1126.000000 -584.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer"/><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2569600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2569600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2569600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2569600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2569600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2569600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2569600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -816.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_221c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -373.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="18" graphid="g_2316d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -950.500000) translate(0,15)">广通北牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2492cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -850.000000) translate(0,17)">220kV苍岭变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246bfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -759.000000) translate(0,17)">苍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246bfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -759.000000) translate(0,38)">广</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246bfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -759.000000) translate(0,59)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246bfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -759.000000) translate(0,80)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246bfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -759.000000) translate(0,101)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_246bfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -759.000000) translate(0,122)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_226ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -765.000000) translate(0,17)">苍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_226ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -765.000000) translate(0,38)">广</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_226ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -765.000000) translate(0,59)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_226ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -765.000000) translate(0,80)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_226ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -765.000000) translate(0,101)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_226ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -765.000000) translate(0,122)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24a2c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 -850.000000) translate(0,17)">220kV苍岭变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2511b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -137.000000) translate(0,20)">220kV广通北牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250c2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 612.000000 -226.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250c620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.000000 -227.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -356.000000) translate(0,12)">111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -356.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 -506.000000) translate(0,12)">26167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 583.000000 -432.000000) translate(0,12)">2616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -618.000000) translate(0,12)">26197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 496.000000 -570.000000) translate(0,12)">2619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25137d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 -572.000000) translate(0,12)">2629</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1080.000000 -616.000000) translate(0,12)">26297</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2513b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -432.000000) translate(0,12)">2626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2494930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -506.000000) translate(0,12)">26267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -337.000000) translate(0,17)">现场C相没有接入，所以Ubc和Uca</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -337.000000) translate(0,38)">显示的是相电压的值</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e7600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 77.000000 -21.000000) translate(0,15)">0871-66122953</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2494aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 373.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2494c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 343.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2494f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 621.000000 358.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2495170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 373.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2495320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 358.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2495510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 343.000000) translate(0,12)">UcA(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2495700" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 441.000000 329.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2496620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 373.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24967d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 358.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2496980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 343.000000) translate(0,12)">UcA(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2496b30" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 832.000000 329.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2497810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1006.000000 373.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24979c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 343.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2497b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 358.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_250d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-275 576,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="33578@1" ObjectIDZND0="33584@0" Pin0InfoVect0LinkObjId="SW-217355_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-275 576,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_250d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-362 576,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33584@1" ObjectIDZND0="33580@0" Pin0InfoVect0LinkObjId="SW-217356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217355_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-362 576,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f8e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-544 576,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="33582@x" ObjectIDND1="33580@x" ObjectIDND2="33581@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217358_0" Pin1InfoVect1LinkObjId="SW-217356_0" Pin1InfoVect2LinkObjId="SW-217357_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="576,-544 576,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f9060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-544 529,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="33580@x" ObjectIDND1="33581@x" ObjectIDZND0="33582@1" Pin0InfoVect0LinkObjId="SW-217358_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217356_0" Pin1InfoVect1LinkObjId="SW-217357_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-544 529,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f9250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="495,-592 506,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="33583@1" ObjectIDZND0="g_24b5520@0" Pin0InfoVect0LinkObjId="g_24b5520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="495,-592 506,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f9b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-443 576,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="33580@1" ObjectIDZND0="33582@x" ObjectIDZND1="33581@x" Pin0InfoVect0LinkObjId="SW-217358_0" Pin0InfoVect1LinkObjId="SW-217357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="576,-443 576,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f9d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-480 576,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="33580@x" ObjectIDND1="33581@x" ObjectIDZND0="33582@x" Pin0InfoVect0LinkObjId="SW-217358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217356_0" Pin1InfoVect1LinkObjId="SW-217357_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-480 576,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f9f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-480 518,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2263890@0" ObjectIDZND0="33581@0" Pin0InfoVect0LinkObjId="SW-217357_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2263890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="496,-480 518,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24fa100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-480 576,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="33581@1" ObjectIDZND0="33580@x" ObjectIDZND1="33582@x" Pin0InfoVect0LinkObjId="SW-217356_0" Pin0InfoVect1LinkObjId="SW-217358_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="554,-480 576,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2490e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-546 944,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="33588@x" ObjectIDND1="33587@x" ObjectIDND2="33586@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217365_0" Pin1InfoVect1LinkObjId="SW-217364_0" Pin1InfoVect2LinkObjId="SW-217363_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="944,-546 944,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2491060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-546 991,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="33587@x" ObjectIDND1="33586@x" ObjectIDZND0="33588@0" Pin0InfoVect0LinkObjId="SW-217365_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217364_0" Pin1InfoVect1LinkObjId="SW-217363_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-546 991,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2491250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,-590 1119,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2525910@0" ObjectIDZND0="33589@1" Pin0InfoVect0LinkObjId="SW-217366_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2525910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-590 1119,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2491440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-546 1072,-590 1083,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2466d20@0" ObjectIDND1="g_24924e0@0" ObjectIDND2="33588@x" ObjectIDZND0="33589@0" Pin0InfoVect0LinkObjId="SW-217366_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2466d20_0" Pin1InfoVect1LinkObjId="g_24924e0_0" Pin1InfoVect2LinkObjId="SW-217365_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-546 1072,-590 1083,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2491630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1024,-480 1003,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2258690@0" ObjectIDZND0="33587@1" Pin0InfoVect0LinkObjId="SW-217364_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2258690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1024,-480 1003,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2491820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-480 944,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="33587@0" ObjectIDZND0="33588@x" ObjectIDZND1="33586@x" Pin0InfoVect0LinkObjId="SW-217365_0" Pin0InfoVect1LinkObjId="SW-217363_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217364_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="967,-480 944,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2491a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-475 1120,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2466d20@0" ObjectIDZND0="g_24924e0@0" ObjectIDZND1="33588@x" ObjectIDZND2="33589@x" Pin0InfoVect0LinkObjId="g_24924e0_0" Pin0InfoVect1LinkObjId="SW-217365_0" Pin0InfoVect2LinkObjId="SW-217366_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2466d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-475 1120,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24922f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1152,-537 1152,-546 1120,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24924e0@0" ObjectIDZND0="g_2466d20@0" ObjectIDZND1="33588@x" ObjectIDZND2="33589@x" Pin0InfoVect0LinkObjId="g_2466d20_0" Pin0InfoVect1LinkObjId="SW-217365_0" Pin0InfoVect2LinkObjId="SW-217366_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24924e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1152,-537 1152,-546 1120,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f8030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,-475 400,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_21ea970@0" ObjectIDZND0="g_21d7d70@0" ObjectIDZND1="33583@x" ObjectIDZND2="33582@x" Pin0InfoVect0LinkObjId="g_21d7d70_0" Pin0InfoVect1LinkObjId="SW-217359_0" Pin0InfoVect2LinkObjId="SW-217358_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ea970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="400,-475 400,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,-544 368,-544 368,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_21ea970@0" ObjectIDND1="33583@x" ObjectIDND2="33582@x" ObjectIDZND0="g_21d7d70@0" Pin0InfoVect0LinkObjId="g_21d7d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21ea970_0" Pin1InfoVect1LinkObjId="SW-217359_0" Pin1InfoVect2LinkObjId="SW-217358_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="400,-544 368,-544 368,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24f8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="448,-544 448,-592 459,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_21ea970@0" ObjectIDND1="g_21d7d70@0" ObjectIDND2="33582@x" ObjectIDZND0="33583@0" Pin0InfoVect0LinkObjId="SW-217359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21ea970_0" Pin1InfoVect1LinkObjId="g_21d7d70_0" Pin1InfoVect2LinkObjId="SW-217358_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="448,-544 448,-592 459,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248b1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-546 944,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="33588@x" ObjectIDZND0="33587@x" ObjectIDZND1="33586@x" Pin0InfoVect0LinkObjId="SW-217364_0" Pin0InfoVect1LinkObjId="SW-217363_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="944,-546 944,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248b3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-276 944,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="33579@1" ObjectIDZND0="33585@0" Pin0InfoVect0LinkObjId="SW-217362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-276 944,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-362 944,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="33585@1" ObjectIDZND0="33586@0" Pin0InfoVect0LinkObjId="SW-217363_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-362 944,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248b7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-443 944,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="33586@1" ObjectIDZND0="33587@x" ObjectIDZND1="33588@x" Pin0InfoVect0LinkObjId="SW-217364_0" Pin0InfoVect1LinkObjId="SW-217365_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="944,-443 944,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248c080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-546 1120,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="33588@x" ObjectIDND1="33589@x" ObjectIDZND0="g_2466d20@0" ObjectIDZND1="g_24924e0@0" Pin0InfoVect0LinkObjId="g_2466d20_0" Pin0InfoVect1LinkObjId="g_24924e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-217365_0" Pin1InfoVect1LinkObjId="SW-217366_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-546 1120,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248c270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1027,-546 1072,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="33588@1" ObjectIDZND0="g_2466d20@0" ObjectIDZND1="g_24924e0@0" ObjectIDZND2="33589@x" Pin0InfoVect0LinkObjId="g_2466d20_0" Pin0InfoVect1LinkObjId="g_24924e0_0" Pin0InfoVect2LinkObjId="SW-217366_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-217365_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1027,-546 1072,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24847a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,-544 448,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_21ea970@0" ObjectIDND1="g_21d7d70@0" ObjectIDZND0="33583@x" ObjectIDZND1="33582@x" Pin0InfoVect0LinkObjId="SW-217359_0" Pin0InfoVect1LinkObjId="SW-217358_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21ea970_0" Pin1InfoVect1LinkObjId="g_21d7d70_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="400,-544 448,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2484990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="448,-544 494,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="33583@x" ObjectIDND1="g_21ea970@0" ObjectIDND2="g_21d7d70@0" ObjectIDZND0="33582@0" Pin0InfoVect0LinkObjId="SW-217358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-217359_0" Pin1InfoVect1LinkObjId="g_21ea970_0" Pin1InfoVect2LinkObjId="g_21d7d70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="448,-544 494,-544 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21ea970">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -440.000000)" xlink:href="#voltageTransformer:shape52"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2466d20">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1093.000000 -440.000000)" xlink:href="#voltageTransformer:shape52"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-217335" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 244.000000 -872.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33573" ObjectName="DYN-CX_GTBQ"/>
     <cge:Meas_Ref ObjectId="217335"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_GTBQ"/>
</svg>